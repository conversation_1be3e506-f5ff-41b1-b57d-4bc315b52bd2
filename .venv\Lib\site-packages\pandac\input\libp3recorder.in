1730992849
3 3
13 libp3recorder 4 ayCl 12 panda3d.core 
51
46 13 ~RecorderBase 0 518 97 27 RecorderBase::~RecorderBase 0 0 10
/**
 *
 */
42
virtual RecorderBase::~RecorderBase(void);

47 12 is_recording 0 4 97 26 RecorderBase::is_recording 0 1 1 187
/**
 * Returns true if this recorder is presently recording data for saving to a
 * session file, false otherwise.  If this is true, record_data() will be
 * called from time to time.
 */
51
inline bool RecorderBase::is_recording(void) const;

48 10 is_playing 0 4 97 24 RecorderBase::is_playing 0 1 2 177
/**
 * Returns true if this recorder is presently playing back data from session
 * file, false otherwise.  If this is true, play_data() will be called from
 * time to time.
 */
49
inline bool RecorderBase::is_playing(void) const;

49 14 get_class_type 0 4 97 28 RecorderBase::get_class_type 0 1 3 0
53
static TypeHandle RecorderBase::get_class_type(void);

50 18 upcast_to_DataNode 0 12 98 33 MouseRecorder::upcast_to_DataNode 0 1 6 37
upcast from MouseRecorder to DataNode
50
DataNode *MouseRecorder::upcast_to_DataNode(void);

51 25 downcast_to_MouseRecorder 0 12 99 35 DataNode::downcast_to_MouseRecorder 0 0 39
downcast from DataNode to MouseRecorder
57
MouseRecorder *DataNode::downcast_to_MouseRecorder(void);

52 22 upcast_to_RecorderBase 0 12 98 37 MouseRecorder::upcast_to_RecorderBase 0 1 7 41
upcast from MouseRecorder to RecorderBase
58
RecorderBase *MouseRecorder::upcast_to_RecorderBase(void);

53 25 downcast_to_MouseRecorder 0 12 97 39 RecorderBase::downcast_to_MouseRecorder 0 0 43
downcast from RecorderBase to MouseRecorder
61
MouseRecorder *RecorderBase::downcast_to_MouseRecorder(void);

54 13 MouseRecorder 0 260 98 28 MouseRecorder::MouseRecorder 0 1 4 10
/**
 *
 */
63
explicit MouseRecorder::MouseRecorder(std::string const &name);

55 14 ~MouseRecorder 0 518 98 29 MouseRecorder::~MouseRecorder 0 0 10
/**
 *
 */
44
virtual MouseRecorder::~MouseRecorder(void);

56 14 get_class_type 0 4 98 29 MouseRecorder::get_class_type 0 1 5 0
54
static TypeHandle MouseRecorder::get_class_type(void);

57 18 RecorderController 0 260 100 38 RecorderController::RecorderController 0 1 8 10
/**
 *
 */
45
RecorderController::RecorderController(void);

58 12 begin_record 0 4 100 32 RecorderController::begin_record 0 1 9 123
/**
 * Begins recording data to the indicated filename.  All of the recorders in
 * use should already have been added.
 */
64
bool RecorderController::begin_record(Filename const &filename);

59 14 begin_playback 0 4 100 34 RecorderController::begin_playback 0 1 10 349
/**
 * Begins playing back data from the indicated filename.  All of the recorders
 * in use should already have been added, although this may define additional
 * recorders if they are present in the file (these new recorders will not be
 * used).  This may also undefine recorders that were previously added but are
 * not present in the file.
 */
66
bool RecorderController::begin_playback(Filename const &filename);

60 5 close 0 4 100 25 RecorderController::close 0 1 11 61
/**
 * Finishes recording data to the indicated filename.
 */
37
void RecorderController::close(void);

61 14 get_start_time 0 4 100 34 RecorderController::get_start_time 0 1 12 167
/**
 * Returns the time (and date) at which the current session was originally
 * recorded (or, in recording mode, the time at which the current session
 * began).
 */
61
inline time_t RecorderController::get_start_time(void) const;

62 15 set_random_seed 0 4 100 35 RecorderController::set_random_seed 0 1 13 203
/**
 * Indicates an arbitrary number to be recorded in the session file as a
 * random seed, should the application wish to take advantage of it.  This
 * must be set before begin_record() is called.
 */
65
inline void RecorderController::set_random_seed(int random_seed);

63 15 get_random_seed 0 4 100 35 RecorderController::get_random_seed 0 1 14 175
/**
 * Returns the random seed that was set by a previous call to
 * set_random_seed(), or the number read from the session file after
 * begin_playback() has been called.
 */
59
inline int RecorderController::get_random_seed(void) const;

64 12 is_recording 0 4 100 32 RecorderController::is_recording 0 1 15 86
/**
 * Returns true if the controller has been opened for output, false otherwise.
 */
57
inline bool RecorderController::is_recording(void) const;

65 10 is_playing 0 4 100 30 RecorderController::is_playing 0 1 16 85
/**
 * Returns true if the controller has been opened for input, false otherwise.
 */
55
inline bool RecorderController::is_playing(void) const;

66 7 is_open 0 4 100 27 RecorderController::is_open 0 1 17 105
/**
 * Returns true if the controller has been opened for either input or output,
 * false otherwise.
 */
52
inline bool RecorderController::is_open(void) const;

67 12 get_filename 0 4 100 32 RecorderController::get_filename 0 1 18 113
/**
 * Returns the filename that was passed to the most recent call to
 * begin_record() or begin_playback().
 */
68
inline Filename const &RecorderController::get_filename(void) const;

68 8 is_error 0 4 100 28 RecorderController::is_error 0 1 19 191
/**
 * Returns true if the controller has been opened for input or output output
 * and there is an error on the stream, or false if the controller is closed
 * or if there is no problem.
 */
47
inline bool RecorderController::is_error(void);

69 16 get_clock_offset 0 4 100 36 RecorderController::get_clock_offset 0 1 20 186
/**
 * Returns the delta offset between the actual frame time and the frame time
 * written to the log.  This is essentially the time at which the recording
 * (or playback) started.
 */
63
inline double RecorderController::get_clock_offset(void) const;

70 16 get_frame_offset 0 4 100 36 RecorderController::get_frame_offset 0 1 21 196
/**
 * Returns the delta offset between the actual frame count and the frame count
 * written to the log.  This is essentially the frame number at which the
 * recording (or playback) started.
 */
60
inline int RecorderController::get_frame_offset(void) const;

71 12 add_recorder 0 4 100 32 RecorderController::add_recorder 0 1 22 343
/**
 * Adds the named recorder to the set of recorders that are in use.
 *
 * If the controller is in recording mode, the named recorder will begin
 * recording its status to the session file.  If the controller is in playback
 * mode and the name and type matches a recorder in the session file, the
 * recorder will begin receiving data.
 */
94
inline void RecorderController::add_recorder(std::string const &name, RecorderBase *recorder);

72 12 has_recorder 0 4 100 32 RecorderController::has_recorder 0 1 23 331
/**
 * Returns true if the named recorder has been added to the table by a
 * previous call to add_recorder(), false otherwise.
 *
 * If the controller is in playback mode, this will also return false for a
 * recorder that was found in the session file but was never explicitly added
 * via add_recorder(); see get_recorder().
 */
76
inline bool RecorderController::has_recorder(std::string const &name) const;

73 12 get_recorder 0 4 100 32 RecorderController::get_recorder 0 1 24 389
/**
 * Returns the recorder with the indicated name, or NULL if there is no such
 * recorder.
 *
 * If the controller is in playback mode, this may return the recorder
 * matching the indicated name as read from the session file, even if it was
 * never added to the table by the user.  In this case, has_recorder() may
 * return false, but get_recorder() will return a non-NULL value.
 */
85
inline RecorderBase *RecorderController::get_recorder(std::string const &name) const;

74 15 remove_recorder 0 4 100 35 RecorderController::remove_recorder 0 1 25 463
/**
 * Removes the named recorder from the table.  Returns true if successful,
 * false if there was no such recorder.
 *
 * If the controller is in recording mode, the named recorder will stop
 * recording.  If the controller is in playback mode, the named recorder will
 * disassociate itself from the session file (but if the session file still
 * has data for this name, a default recorder will take its place to decode
 * the data from the session file).
 */
73
inline bool RecorderController::remove_recorder(std::string const &name);

75 13 set_frame_tie 0 4 100 33 RecorderController::set_frame_tie 0 1 26 528
/**
 * Sets the frame_tie flag.
 *
 * When this is true, sessions are played back frame-for-frame, based on the
 * frame count of the recorded session.  This gives the most accurate
 * playback, but the playback rate will vary according to the frame rate of
 * the playback machine.
 *
 * When this is false, sessions are played back at real time, based on the
 * clock of the recorded session.  This may introduce playback discrepencies
 * if the frames do not fall at exactly the same times as they did in the
 * original.
 */
62
inline void RecorderController::set_frame_tie(bool frame_tie);

76 13 get_frame_tie 0 4 100 33 RecorderController::get_frame_tie 0 1 27 31
/**
 * See set_frame_tie().
 */
58
inline bool RecorderController::get_frame_tie(void) const;

77 12 record_frame 0 4 100 32 RecorderController::record_frame 0 1 28 106
/**
 * Gets the next frame of data from all of the active recorders and adds it to
 * the output file.
 */
44
void RecorderController::record_frame(void);

78 10 play_frame 0 4 100 30 RecorderController::play_frame 0 1 29 106
/**
 * Gets the next frame of data from all of the active recorders and adds it to
 * the output file.
 */
42
void RecorderController::play_frame(void);

79 14 get_class_type 0 4 100 34 RecorderController::get_class_type 0 1 30 0
59
static TypeHandle RecorderController::get_class_type(void);

80 22 upcast_to_RecorderBase 0 12 102 44 SocketStreamRecorder::upcast_to_RecorderBase 0 1 44 48
upcast from SocketStreamRecorder to RecorderBase
65
RecorderBase *SocketStreamRecorder::upcast_to_RecorderBase(void);

81 32 downcast_to_SocketStreamRecorder 0 12 97 46 RecorderBase::downcast_to_SocketStreamRecorder 0 0 50
downcast from RecorderBase to SocketStreamRecorder
75
SocketStreamRecorder *RecorderBase::downcast_to_SocketStreamRecorder(void);

82 24 upcast_to_ReferenceCount 0 12 102 46 SocketStreamRecorder::upcast_to_ReferenceCount 0 1 45 50
upcast from SocketStreamRecorder to ReferenceCount
69
ReferenceCount *SocketStreamRecorder::upcast_to_ReferenceCount(void);

83 32 downcast_to_SocketStreamRecorder 0 12 103 48 ReferenceCount::downcast_to_SocketStreamRecorder 0 0 52
downcast from ReferenceCount to SocketStreamRecorder
77
SocketStreamRecorder *ReferenceCount::downcast_to_SocketStreamRecorder(void);

84 20 SocketStreamRecorder 0 260 102 42 SocketStreamRecorder::SocketStreamRecorder 0 2 31 32 22
/**
 *
 */

/**
 *
 */
156
inline SocketStreamRecorder::SocketStreamRecorder(void);
inline explicit SocketStreamRecorder::SocketStreamRecorder(SocketStream *stream, bool owns_stream);

85 21 ~SocketStreamRecorder 0 518 102 43 SocketStreamRecorder::~SocketStreamRecorder 0 0 10
/**
 *
 */
65
virtual inline SocketStreamRecorder::~SocketStreamRecorder(void);

86 16 receive_datagram 0 4 102 38 SocketStreamRecorder::receive_datagram 0 1 33 253
/**
 * Receives a datagram over the socket by expecting a little-endian 16-bit
 * byte count as a prefix.  If the socket stream is non-blocking, may return
 * false if the data is not available; otherwise, returns false only if the
 * socket closes.
 */
58
bool SocketStreamRecorder::receive_datagram(Datagram &dg);

87 13 send_datagram 0 4 102 35 SocketStreamRecorder::send_datagram 0 1 34 45
/**
 * See SocketStream::send_datagram().
 */
68
inline bool SocketStreamRecorder::send_datagram(Datagram const &dg);

88 9 is_closed 0 4 102 31 SocketStreamRecorder::is_closed 0 1 35 41
/**
 * See SocketStream::is_closed().
 */
50
inline bool SocketStreamRecorder::is_closed(void);

89 5 close 0 4 102 27 SocketStreamRecorder::close 0 1 36 37
/**
 * See SocketStream::close().
 */
46
inline void SocketStreamRecorder::close(void);

90 15 set_collect_tcp 0 4 102 37 SocketStreamRecorder::set_collect_tcp 0 1 37 47
/**
 * See SocketStream::set_collect_tcp().
 */
68
inline void SocketStreamRecorder::set_collect_tcp(bool collect_tcp);

91 15 get_collect_tcp 0 4 102 37 SocketStreamRecorder::get_collect_tcp 0 1 38 47
/**
 * See SocketStream::get_collect_tcp().
 */
62
inline bool SocketStreamRecorder::get_collect_tcp(void) const;

92 24 set_collect_tcp_interval 0 4 102 46 SocketStreamRecorder::set_collect_tcp_interval 0 1 39 56
/**
 * See SocketStream::set_collect_tcp_interval().
 */
76
inline void SocketStreamRecorder::set_collect_tcp_interval(double interval);

93 24 get_collect_tcp_interval 0 4 102 46 SocketStreamRecorder::get_collect_tcp_interval 0 1 40 56
/**
 * See SocketStream::get_collect_tcp_interval().
 */
73
inline double SocketStreamRecorder::get_collect_tcp_interval(void) const;

94 14 consider_flush 0 4 102 36 SocketStreamRecorder::consider_flush 0 1 41 45
/**
 * See SocketStream::consider_flush()
 */
55
inline bool SocketStreamRecorder::consider_flush(void);

95 5 flush 0 4 102 27 SocketStreamRecorder::flush 0 1 42 36
/**
 * See SocketStream::flush()
 */
46
inline bool SocketStreamRecorder::flush(void);

96 14 get_class_type 0 4 102 36 SocketStreamRecorder::get_class_type 0 1 43 0
61
static TypeHandle SocketStreamRecorder::get_class_type(void);

45
1 0 0 6 3 106 0 0 187 /**
 * Returns true if this recorder is presently recording data for saving to a
 * session file, false otherwise.  If this is true, record_data() will be
 * called from time to time.
 */ 1 4 this 3 104  
2 0 0 6 4 106 0 0 177 /**
 * Returns true if this recorder is presently playing back data from session
 * file, false otherwise.  If this is true, play_data() will be called from
 * time to time.
 */ 1 4 this 3 104  
3 0 0 7 5 108 0 0 0 0 
4 0 0 7 12 110 55 0 10 /**
 *
 */ 1 4 name 1 109  
5 0 0 7 14 108 0 0 0 0 
6 0 0 7 8 111 0 0 0 1 4 this 3 110  
7 0 0 7 10 112 46 0 0 1 4 this 3 110  
8 0 0 7 17 113 0 0 10 /**
 *
 */ 0 
9 0 0 6 18 106 0 0 123 /**
 * Begins recording data to the indicated filename.  All of the recorders in
 * use should already have been added.
 */ 2 4 this 3 113  8 filename 1 114  
10 0 0 6 19 106 0 0 349 /**
 * Begins playing back data from the indicated filename.  All of the recorders
 * in use should already have been added, although this may define additional
 * recorders if they are present in the file (these new recorders will not be
 * used).  This may also undefine recorders that were previously added but are
 * not present in the file.
 */ 2 4 this 3 113  8 filename 1 114  
11 0 0 4 20 117 0 0 61 /**
 * Finishes recording data to the indicated filename.
 */ 1 4 this 3 113  
12 0 0 6 21 120 0 0 167 /**
 * Returns the time (and date) at which the current session was originally
 * recorded (or, in recording mode, the time at which the current session
 * began).
 */ 1 4 this 3 118  
13 0 0 4 22 117 0 0 203 /**
 * Indicates an arbitrary number to be recorded in the session file as a
 * random seed, should the application wish to take advantage of it.  This
 * must be set before begin_record() is called.
 */ 2 4 this 3 113  11 random_seed 1 122  
14 0 0 6 23 122 0 0 175 /**
 * Returns the random seed that was set by a previous call to
 * set_random_seed(), or the number read from the session file after
 * begin_playback() has been called.
 */ 1 4 this 3 118  
15 0 0 6 24 106 0 0 86 /**
 * Returns true if the controller has been opened for output, false otherwise.
 */ 1 4 this 3 118  
16 0 0 6 25 106 0 0 85 /**
 * Returns true if the controller has been opened for input, false otherwise.
 */ 1 4 this 3 118  
17 0 0 6 26 106 0 0 105 /**
 * Returns true if the controller has been opened for either input or output,
 * false otherwise.
 */ 1 4 this 3 118  
18 0 0 6 27 114 0 0 113 /**
 * Returns the filename that was passed to the most recent call to
 * begin_record() or begin_playback().
 */ 1 4 this 3 118  
19 0 0 6 28 106 0 0 191 /**
 * Returns true if the controller has been opened for input or output output
 * and there is an error on the stream, or false if the controller is closed
 * or if there is no problem.
 */ 1 4 this 3 113  
20 0 0 6 29 123 0 0 186 /**
 * Returns the delta offset between the actual frame time and the frame time
 * written to the log.  This is essentially the time at which the recording
 * (or playback) started.
 */ 1 4 this 3 118  
21 0 0 6 30 122 0 0 196 /**
 * Returns the delta offset between the actual frame count and the frame count
 * written to the log.  This is essentially the frame number at which the
 * recording (or playback) started.
 */ 1 4 this 3 118  
22 0 0 4 31 117 0 0 343 /**
 * Adds the named recorder to the set of recorders that are in use.
 *
 * If the controller is in recording mode, the named recorder will begin
 * recording its status to the session file.  If the controller is in playback
 * mode and the name and type matches a recorder in the session file, the
 * recorder will begin receiving data.
 */ 3 4 this 3 113  4 name 1 109  8 recorder 1 112  
23 0 0 6 32 106 0 0 331 /**
 * Returns true if the named recorder has been added to the table by a
 * previous call to add_recorder(), false otherwise.
 *
 * If the controller is in playback mode, this will also return false for a
 * recorder that was found in the session file but was never explicitly added
 * via add_recorder(); see get_recorder().
 */ 2 4 this 3 118  4 name 1 109  
24 0 0 7 33 112 46 0 389 /**
 * Returns the recorder with the indicated name, or NULL if there is no such
 * recorder.
 *
 * If the controller is in playback mode, this may return the recorder
 * matching the indicated name as read from the session file, even if it was
 * never added to the table by the user.  In this case, has_recorder() may
 * return false, but get_recorder() will return a non-NULL value.
 */ 2 4 this 3 118  4 name 1 109  
25 0 0 6 34 106 0 0 463 /**
 * Removes the named recorder from the table.  Returns true if successful,
 * false if there was no such recorder.
 *
 * If the controller is in recording mode, the named recorder will stop
 * recording.  If the controller is in playback mode, the named recorder will
 * disassociate itself from the session file (but if the session file still
 * has data for this name, a default recorder will take its place to decode
 * the data from the session file).
 */ 2 4 this 3 113  4 name 1 109  
26 0 0 4 35 117 0 0 528 /**
 * Sets the frame_tie flag.
 *
 * When this is true, sessions are played back frame-for-frame, based on the
 * frame count of the recorded session.  This gives the most accurate
 * playback, but the playback rate will vary according to the frame rate of
 * the playback machine.
 *
 * When this is false, sessions are played back at real time, based on the
 * clock of the recorded session.  This may introduce playback discrepencies
 * if the frames do not fall at exactly the same times as they did in the
 * original.
 */ 2 4 this 3 113  9 frame_tie 1 106  
27 0 0 6 36 106 0 0 31 /**
 * See set_frame_tie().
 */ 1 4 this 3 118  
28 0 0 4 37 117 0 0 106 /**
 * Gets the next frame of data from all of the active recorders and adds it to
 * the output file.
 */ 1 4 this 3 113  
29 0 0 4 38 117 0 0 106 /**
 * Gets the next frame of data from all of the active recorders and adds it to
 * the output file.
 */ 1 4 this 3 113  
30 0 0 7 39 108 0 0 0 0 
31 0 0 7 46 124 85 0 10 /**
 *
 */ 0 
32 0 0 7 46 124 85 0 10 /**
 *
 */ 2 6 stream 1 125  11 owns_stream 1 106  
33 0 0 6 48 106 0 0 253 /**
 * Receives a datagram over the socket by expecting a little-endian 16-bit
 * byte count as a prefix.  If the socket stream is non-blocking, may return
 * false if the data is not available; otherwise, returns false only if the
 * socket closes.
 */ 2 4 this 3 124  2 dg 1 127  
34 0 0 6 49 106 0 0 45 /**
 * See SocketStream::send_datagram().
 */ 2 4 this 3 124  2 dg 1 129  
35 0 0 6 50 106 0 0 41 /**
 * See SocketStream::is_closed().
 */ 1 4 this 3 124  
36 0 0 4 51 117 0 0 37 /**
 * See SocketStream::close().
 */ 1 4 this 3 124  
37 0 0 4 52 117 0 0 47 /**
 * See SocketStream::set_collect_tcp().
 */ 2 4 this 3 124  11 collect_tcp 1 106  
38 0 0 6 53 106 0 0 47 /**
 * See SocketStream::get_collect_tcp().
 */ 1 4 this 3 131  
39 0 0 4 54 117 0 0 56 /**
 * See SocketStream::set_collect_tcp_interval().
 */ 2 4 this 3 124  8 interval 1 123  
40 0 0 6 55 123 0 0 56 /**
 * See SocketStream::get_collect_tcp_interval().
 */ 1 4 this 3 131  
41 0 0 6 56 106 0 0 45 /**
 * See SocketStream::consider_flush()
 */ 1 4 this 3 124  
42 0 0 6 57 106 0 0 36 /**
 * See SocketStream::flush()
 */ 1 4 this 3 124  
43 0 0 7 58 108 0 0 0 0 
44 0 0 7 41 112 46 0 0 1 4 this 3 124  
45 0 0 7 44 133 0 0 0 1 4 this 3 124  
37
97 12 RecorderBase 0 26625 12 RecorderBase 12 RecorderBase 0 0 0 0 46 0 3 47 48 49 0 0 0 0 0 1028
/**
 * This is the base class to a number of objects that record particular kinds
 * of user input (like a MouseRecorder) to use in conjunction with a
 * RecorderController to record the user's inputs for a session.
 *
 * Note that RecorderBase does not actually inherit from TypedObject, even
 * though it defines get_type().  The assumption is that the classes that
 * derive from RecorderBase might also inherit independently from TypedObject.
 *
 * It also does not inherit from TypedWritable, but it defines a method called
 * write_recorder() which is very similar to a TypedWritable's
 * write_datagram(). Classes that derive from RecorderBase and also inherit
 * from TypedWritable may choose to remap write_recorder() to do exactly the
 * same thing as write_datagram(), or they may choose to write something
 * slightly different.
 *
 * Most types of recorders should derive from Recorder, as it derives from
 * ReferenceCount, except for MouseRecorder, which would otherwise doubly
 * inherit from ReferenceCount.
 */

98 13 MouseRecorder 0 26625 13 MouseRecorder 13 MouseRecorder 0 0 0 1 54 55 0 1 56 0 0 2 3 99 50 51 3 97 52 53 0 0 362
/**
 * This object records any data generated by a particular MouseAndKeyboard
 * node on the datagraph for a session for eventual playback via a
 * DataGraphPlayback (and a PlaybackController).  To use it, make it a child
 * of the node you wish to record.  It also serves as a pass-through, so that
 * additional child nodes may be parented directly to it.
 */

99 8 DataNode 0 2048 8 DataNode 8 DataNode 0 0 0 0 0 0 0 0 0 0 0 0 422
/**
 * The fundamental type of node for the data graph.  The DataNode class is
 * itself primarily intended as an abstract class; it defines no inputs and no
 * outputs.  Most kinds of data nodes will derive from this to specify the
 * inputs and outputs in the constructor.
 *
 * DataNode does not attempt to cycle its data with a PipelineCycler.  The
 * data graph is intended to be used only within a single thread.
 */

100 18 RecorderController 0 75777 18 RecorderController 18 RecorderController 0 0 0 1 57 0 0 22 58 59 60 61 62 63 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 0 0 1 0 101 0 0 0 0 142
/**
 * This object manages the process of recording the user's runtime inputs to a
 * bam file so that the session can be recreated later.
 */

101 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

102 20 SocketStreamRecorder 0 26625 20 SocketStreamRecorder 20 SocketStreamRecorder 0 0 0 1 84 85 0 11 86 87 88 89 90 91 92 93 94 95 96 0 0 2 3 97 80 81 3 103 82 83 0 0 350
/**
 * Records any data received from the indicated socket stream.  On playback,
 * it will act as if the incoming data is coming over the wire again even if
 * an actual connection is not available.
 *
 * Outbound data will not be recorded, but will be sent straight through to
 * the socket if it is connected, or silently ignored if it is not.
 */

103 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

104 20 RecorderBase const * 0 8576 20 RecorderBase const * 20 RecorderBase const * 0 0 105 0 0 0 0 0 0 0 0 0 0

105 18 RecorderBase const 0 8832 18 RecorderBase const 18 RecorderBase const 0 0 97 0 0 0 0 0 0 0 0 0 0

106 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

107 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

108 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 107 0 0 0 0 0 0 0 0 0 0

109 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

110 15 MouseRecorder * 0 8576 15 MouseRecorder * 15 MouseRecorder * 0 0 98 0 0 0 0 0 0 0 0 0 0

111 10 DataNode * 0 8576 10 DataNode * 10 DataNode * 0 0 99 0 0 0 0 0 0 0 0 0 0

112 14 RecorderBase * 0 8576 14 RecorderBase * 14 RecorderBase * 0 0 97 0 0 0 0 0 0 0 0 0 0

113 20 RecorderController * 0 8576 20 RecorderController * 20 RecorderController * 0 0 100 0 0 0 0 0 0 0 0 0 0

114 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 115 0 0 0 0 0 0 0 0 0 0

115 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 116 0 0 0 0 0 0 0 0 0 0

116 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

117 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

118 26 RecorderController const * 0 8576 26 RecorderController const * 26 RecorderController const * 0 0 119 0 0 0 0 0 0 0 0 0 0

119 24 RecorderController const 0 8832 24 RecorderController const 24 RecorderController const 0 0 100 0 0 0 0 0 0 0 0 0 0

120 6 time_t 0 2105344 6 time_t 6 time_t 0 0 121 0 0 0 0 0 0 0 0 0 0

121 8 long int 0 8210 8 long int 8 long int 0 1 0 0 0 0 0 0 0 0 0 0 0

122 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

123 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

124 22 SocketStreamRecorder * 0 8576 22 SocketStreamRecorder * 22 SocketStreamRecorder * 0 0 102 0 0 0 0 0 0 0 0 0 0

125 14 SocketStream * 0 8576 14 SocketStream * 14 SocketStream * 0 0 126 0 0 0 0 0 0 0 0 0 0

126 12 SocketStream 0 2048 12 SocketStream 12 SocketStream 0 0 0 0 0 0 0 0 0 0 0 0 97
/**
 * A base class for iostreams that read and write to a (possibly non-blocking)
 * socket.
 */

127 10 Datagram * 0 8576 10 Datagram * 10 Datagram * 0 0 128 0 0 0 0 0 0 0 0 0 0

128 8 Datagram 0 2048 8 Datagram 8 Datagram 0 0 0 0 0 0 0 0 0 0 0 0 565
/**
 * An ordered list of data elements, formatted in memory for transmission over
 * a socket or writing to a data file.
 *
 * Data elements should be added one at a time, in order, to the Datagram.
 * The nature and contents of the data elements are totally up to the user.
 * When a Datagram has been transmitted and received, its data elements may be
 * extracted using a DatagramIterator; it is up to the caller to know the
 * correct type of each data element in order.
 *
 * A Datagram is itself headerless; it is simply a collection of data
 * elements.
 */

129 16 Datagram const * 0 8576 16 Datagram const * 16 Datagram const * 0 0 130 0 0 0 0 0 0 0 0 0 0

130 14 Datagram const 0 8832 14 Datagram const 14 Datagram const 0 0 128 0 0 0 0 0 0 0 0 0 0

131 28 SocketStreamRecorder const * 0 8576 28 SocketStreamRecorder const * 28 SocketStreamRecorder const * 0 0 132 0 0 0 0 0 0 0 0 0 0

132 26 SocketStreamRecorder const 0 8832 26 SocketStreamRecorder const 26 SocketStreamRecorder const 0 0 102 0 0 0 0 0 0 0 0 0 0

133 16 ReferenceCount * 0 8576 16 ReferenceCount * 16 ReferenceCount * 0 0 103 0 0 0 0 0 0 0 0 0 0

0
0
0
