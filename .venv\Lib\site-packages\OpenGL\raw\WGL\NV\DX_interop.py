'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_NV_DX_interop'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_NV_DX_interop',error_checker=_errors._error_checker)
WGL_ACCESS_READ_ONLY_NV=_C('WGL_ACCESS_READ_ONLY_NV',0x00000000)
WGL_ACCESS_READ_WRITE_NV=_C('WGL_ACCESS_READ_WRITE_NV',0x00000001)
WGL_ACCESS_WRITE_DISCARD_NV=_C('WGL_ACCESS_WRITE_DISCARD_NV',0x00000002)
@_f
@_p.types(_cs.BOOL,_cs.HANDLE)
def wglDXCloseDeviceNV(hDevice):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.GLint,ctypes.POINTER(_cs.HANDLE))
def wglDXLockObjectsNV(hDevice,count,hObjects):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.GLenum)
def wglDXObjectAccessNV(hObject,access):pass
@_f
@_p.types(_cs.HANDLE,ctypes.c_void_p)
def wglDXOpenDeviceNV(dxDevice):pass
@_f
@_p.types(_cs.HANDLE,_cs.HANDLE,ctypes.c_void_p,_cs.GLuint,_cs.GLenum,_cs.GLenum)
def wglDXRegisterObjectNV(hDevice,dxObject,name,type,access):pass
@_f
@_p.types(_cs.BOOL,ctypes.c_void_p,_cs.HANDLE)
def wglDXSetResourceShareHandleNV(dxObject,shareHandle):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.GLint,ctypes.POINTER(_cs.HANDLE))
def wglDXUnlockObjectsNV(hDevice,count,hObjects):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.HANDLE)
def wglDXUnregisterObjectNV(hDevice,hObject):pass
