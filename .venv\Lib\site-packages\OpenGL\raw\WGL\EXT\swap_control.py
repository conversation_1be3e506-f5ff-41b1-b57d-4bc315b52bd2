'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_EXT_swap_control'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_EXT_swap_control',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.c_int,)
def wglGetSwapIntervalEXT():pass
@_f
@_p.types(_cs.BOOL,_cs.c_int)
def wglSwapIntervalEXT(interval):pass
