[console_scripts]
eggcacher = direct.directscripts.eggcacher:main
pfreeze = direct.dist.pfreeze:main
apply_patch = panda3d_tools:apply_patch
bam-info = panda3d_tools:bam_info
bam2egg = panda3d_tools:bam2egg
build_patch = panda3d_tools:build_patch
cginfo = panda3d_tools:cginfo
check_adler = panda3d_tools:check_adler
check_crc = panda3d_tools:check_crc
check_md5 = panda3d_tools:check_md5
dae2egg = panda3d_tools:dae2egg
deploy-stub = panda3d_tools:deploy_stub
deploy-stubw = panda3d_tools:deploy_stubw
dxf-points = panda3d_tools:dxf_points
dxf2egg = panda3d_tools:dxf2egg
egg-crop = panda3d_tools:egg_crop
egg-list-textures = panda3d_tools:egg_list_textures
egg-make-tube = panda3d_tools:egg_make_tube
egg-mkfont = panda3d_tools:egg_mkfont
egg-optchar = panda3d_tools:egg_optchar
egg-palettize = panda3d_tools:egg_palettize
egg-qtess = panda3d_tools:egg_qtess
egg-rename = panda3d_tools:egg_rename
egg-retarget-anim = panda3d_tools:egg_retarget_anim
egg-texture-cards = panda3d_tools:egg_texture_cards
egg-topstrip = panda3d_tools:egg_topstrip
egg-trans = panda3d_tools:egg_trans
egg2bam = panda3d_tools:egg2bam
egg2c = panda3d_tools:egg2c
egg2dxf = panda3d_tools:egg2dxf
egg2flt = panda3d_tools:egg2flt
egg2maya2008 = panda3d_tools:egg2maya2008
egg2maya2009 = panda3d_tools:egg2maya2009
egg2maya2011 = panda3d_tools:egg2maya2011
egg2maya2012 = panda3d_tools:egg2maya2012
egg2maya2013 = panda3d_tools:egg2maya2013
egg2maya2014 = panda3d_tools:egg2maya2014
egg2maya2015 = panda3d_tools:egg2maya2015
egg2maya2016 = panda3d_tools:egg2maya2016
egg2maya20165 = panda3d_tools:egg2maya20165
egg2maya2017 = panda3d_tools:egg2maya2017
egg2maya2018 = panda3d_tools:egg2maya2018
egg2maya2019 = panda3d_tools:egg2maya2019
egg2maya2020 = panda3d_tools:egg2maya2020
egg2maya2022 = panda3d_tools:egg2maya2022
egg2maya85 = panda3d_tools:egg2maya85
egg2obj = panda3d_tools:egg2obj
egg2x = panda3d_tools:egg2x
ffmpeg = panda3d_tools:ffmpeg
ffplay = panda3d_tools:ffplay
ffprobe = panda3d_tools:ffprobe
flt-info = panda3d_tools:flt_info
flt-trans = panda3d_tools:flt_trans
flt2egg = panda3d_tools:flt2egg
fltcopy = panda3d_tools:fltcopy
image-info = panda3d_tools:image_info
image-resize = panda3d_tools:image_resize
image-trans = panda3d_tools:image_trans
interrogate = panda3d_tools:interrogate
interrogate_module = panda3d_tools:interrogate_module
lwo-scan = panda3d_tools:lwo_scan
lwo2egg = panda3d_tools:lwo2egg
make-prc-key = panda3d_tools:make_prc_key
maya2egg2008 = panda3d_tools:maya2egg2008
maya2egg2009 = panda3d_tools:maya2egg2009
maya2egg2011 = panda3d_tools:maya2egg2011
maya2egg2012 = panda3d_tools:maya2egg2012
maya2egg2013 = panda3d_tools:maya2egg2013
maya2egg2014 = panda3d_tools:maya2egg2014
maya2egg2015 = panda3d_tools:maya2egg2015
maya2egg2016 = panda3d_tools:maya2egg2016
maya2egg20165 = panda3d_tools:maya2egg20165
maya2egg2017 = panda3d_tools:maya2egg2017
maya2egg2018 = panda3d_tools:maya2egg2018
maya2egg2019 = panda3d_tools:maya2egg2019
maya2egg2020 = panda3d_tools:maya2egg2020
maya2egg2022 = panda3d_tools:maya2egg2022
maya2egg85 = panda3d_tools:maya2egg85
mayacopy2008 = panda3d_tools:mayacopy2008
mayacopy2009 = panda3d_tools:mayacopy2009
mayacopy2011 = panda3d_tools:mayacopy2011
mayacopy2012 = panda3d_tools:mayacopy2012
mayacopy2013 = panda3d_tools:mayacopy2013
mayacopy2014 = panda3d_tools:mayacopy2014
mayacopy2015 = panda3d_tools:mayacopy2015
mayacopy2016 = panda3d_tools:mayacopy2016
mayacopy20165 = panda3d_tools:mayacopy20165
mayacopy2017 = panda3d_tools:mayacopy2017
mayacopy2018 = panda3d_tools:mayacopy2018
mayacopy2019 = panda3d_tools:mayacopy2019
mayacopy2020 = panda3d_tools:mayacopy2020
mayacopy2022 = panda3d_tools:mayacopy2022
mayacopy85 = panda3d_tools:mayacopy85
multify = panda3d_tools:multify
obj2egg = panda3d_tools:obj2egg
p3dcparse = panda3d_tools:p3dcparse
parse_file = panda3d_tools:parse_file
pdecrypt = panda3d_tools:pdecrypt
pencrypt = panda3d_tools:pencrypt
pfm-bba = panda3d_tools:pfm_bba
pfm-trans = panda3d_tools:pfm_trans
pstats = panda3d_tools:pstats
punzip = panda3d_tools:punzip
pview = panda3d_tools:pview
pzip = panda3d_tools:pzip
show_ddb = panda3d_tools:show_ddb
test_interrogate = panda3d_tools:test_interrogate
text-stats = panda3d_tools:text_stats
vrml-trans = panda3d_tools:vrml_trans
vrml2egg = panda3d_tools:vrml2egg
x-trans = panda3d_tools:x_trans
x2egg = panda3d_tools:x2egg
[distutils.commands]
build_apps = direct.dist.commands:build_apps
bdist_apps = direct.dist.commands:bdist_apps
[setuptools.finalize_distribution_options]
build_apps = direct.dist._dist_hooks:finalize_distribution_options
