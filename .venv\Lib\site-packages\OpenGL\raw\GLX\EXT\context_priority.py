'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_EXT_context_priority'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_EXT_context_priority',error_checker=_errors._error_checker)
GLX_CONTEXT_PRIORITY_HIGH_EXT=_C('GLX_CONTEXT_PRIORITY_HIGH_EXT',0x3101)
GLX_CONTEXT_PRIORITY_LEVEL_EXT=_C('GLX_CONTEXT_PRIORITY_LEVEL_EXT',0x3100)
GLX_CONTEXT_PRIORITY_LOW_EXT=_C('GLX_CONTEXT_PRIORITY_LOW_EXT',0x3103)
GLX_CONTEXT_PRIORITY_MEDIUM_EXT=_C('GLX_CONTEXT_PRIORITY_MEDIUM_EXT',0x3102)

