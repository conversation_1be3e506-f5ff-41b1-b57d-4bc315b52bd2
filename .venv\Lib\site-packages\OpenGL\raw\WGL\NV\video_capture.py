'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_NV_video_capture'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_NV_video_capture',error_checker=_errors._error_checker)
WGL_NUM_VIDEO_CAPTURE_SLOTS_NV=_C('WGL_NUM_VIDEO_CAPTURE_SLOTS_NV',0x20CF)
WGL_UNIQUE_ID_NV=_C('WGL_UNIQUE_ID_NV',0x20CE)
@_f
@_p.types(_cs.BOOL,_cs.UINT,_cs.HVIDEOINPUTDEVICENV)
def wglBindVideoCaptureDeviceNV(uVideoSlot,hDevice):pass
@_f
@_p.types(_cs.UINT,_cs.HDC,ctypes.POINTER(_cs.HVIDEOINPUTDEVICENV))
def wglEnumerateVideoCaptureDevicesNV(hDc,phDeviceList):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.HVIDEOINPUTDEVICENV)
def wglLockVideoCaptureDeviceNV(hDc,hDevice):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.HVIDEOINPUTDEVICENV,_cs.c_int,ctypes.POINTER(_cs.c_int))
def wglQueryVideoCaptureDeviceNV(hDc,hDevice,iAttribute,piValue):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.HVIDEOINPUTDEVICENV)
def wglReleaseVideoCaptureDeviceNV(hDc,hDevice):pass
