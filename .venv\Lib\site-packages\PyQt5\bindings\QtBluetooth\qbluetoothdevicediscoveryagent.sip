// qbluetoothdevicediscoveryagent.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothDeviceDiscoveryAgent : public QObject
{
%TypeHeaderCode
#include <qbluetoothdevicediscoveryagent.h>
%End

public:
    enum Error
    {
        NoError,
        InputOutputError,
        PoweredOffError,
%If (Qt_5_3_0 -)
        InvalidBluetoothAdapterError,
%End
%If (Qt_5_5_0 -)
        UnsupportedPlatformError,
%End
%If (Qt_5_8_0 -)
        UnsupportedDiscoveryMethod,
%End
        UnknownError,
    };

    enum InquiryType
    {
        GeneralUnlimitedInquiry,
        LimitedInquiry,
    };

%If (Qt_5_6_1 -)
    explicit QBluetoothDeviceDiscoveryAgent(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QBluetoothDeviceDiscoveryAgent(QObject *parent /TransferThis/ = 0);
%End
    QBluetoothDeviceDiscoveryAgent(const QBluetoothAddress &deviceAdapter, QObject *parent /TransferThis/ = 0);
    virtual ~QBluetoothDeviceDiscoveryAgent();
    QBluetoothDeviceDiscoveryAgent::InquiryType inquiryType() const;
    void setInquiryType(QBluetoothDeviceDiscoveryAgent::InquiryType type);
    bool isActive() const;
    QBluetoothDeviceDiscoveryAgent::Error error() const;
    QString errorString() const;
    QList<QBluetoothDeviceInfo> discoveredDevices() const;

public slots:
    void start();
%If (Qt_5_8_0 -)
    void start(QBluetoothDeviceDiscoveryAgent::DiscoveryMethods method);
%End
    void stop();

signals:
    void deviceDiscovered(const QBluetoothDeviceInfo &info);
    void finished();
    void error(QBluetoothDeviceDiscoveryAgent::Error error);
    void canceled();
%If (Qt_5_12_0 -)
    void deviceUpdated(const QBluetoothDeviceInfo &info, QBluetoothDeviceInfo::Fields updatedFields);
%End

public:
%If (Qt_5_8_0 -)

    enum DiscoveryMethod
    {
        NoMethod,
        ClassicMethod,
        LowEnergyMethod,
    };

%End
%If (Qt_5_8_0 -)
    typedef QFlags<QBluetoothDeviceDiscoveryAgent::DiscoveryMethod> DiscoveryMethods;
%End
%If (Qt_5_8_0 -)
    void setLowEnergyDiscoveryTimeout(int msTimeout);
%End
%If (Qt_5_8_0 -)
    int lowEnergyDiscoveryTimeout() const;
%End
%If (Qt_5_8_0 -)
    static QBluetoothDeviceDiscoveryAgent::DiscoveryMethods supportedDiscoveryMethods();
%End
};

%End
%If (Qt_5_8_0 -)
QFlags<QBluetoothDeviceDiscoveryAgent::DiscoveryMethod> operator|(QBluetoothDeviceDiscoveryAgent::DiscoveryMethod f1, QFlags<QBluetoothDeviceDiscoveryAgent::DiscoveryMethod> f2);
%End
