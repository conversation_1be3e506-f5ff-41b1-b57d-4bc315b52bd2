'''OpenGL extension VERSION.WGL_1_0

This module customises the behaviour of the 
OpenGL.raw.WGL.VERSION.WGL_1_0 to provide a more 
Python-friendly API

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/VERSION/WGL_1_0.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.VERSION.WGL_1_0 import *
from OpenGL.raw.WGL.VERSION.WGL_1_0 import _EXTENSION_NAME

def glInitWgl10VERSION():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION

wglGetCurrentDC.restyle = ctypes.HDC
