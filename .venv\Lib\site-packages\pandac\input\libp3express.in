1730992849
3 3
12 libp3express 4 vDgf 12 panda3d.core 
655
666 7 is_null 0 4 1325 22 PointerToVoid::is_null 0 1 20 130
/**
 * Returns true if the PointerTo is a NULL pointer, false otherwise.  (Direct
 * comparison to a NULL pointer also works.)
 */
50
constexpr bool PointerToVoid::is_null(void) const;

667 8 get_hash 0 4 1325 23 PointerToVoid::get_hash 0 1 21 10
/**
 *
 */
55
inline std::size_t PointerToVoid::get_hash(void) const;

668 14 ~PointerToVoid 0 516 1325 29 PointerToVoid::~PointerToVoid 0 0 0
36
PointerToVoid::~PointerToVoid(void);

669 5 clear 0 4 1324 56 PointerToBase< ReferenceCountedVector< double > >::clear 0 1 18 0
75
inline void PointerToBase< ReferenceCountedVector< double > >::clear(void);

670 6 output 0 4 1324 57 PointerToBase< ReferenceCountedVector< double > >::output 0 1 19 0
88
void PointerToBase< ReferenceCountedVector< double > >::output(std::ostream &out) const;

671 19 ~PointerToArrayBase 0 516 1323 49 PointerToArrayBase< double >::~PointerToArrayBase 0 0 0
63
inline PointerToArrayBase< double >::~PointerToArrayBase(void);

672 11 operator == 0 4 1323 41 PointerToArrayBase< double >::operator == 0 1 16 64
// These are implemented in PointerToVoid, but expose them here.
103
inline bool PointerToArrayBase< double >::operator ==(PointerToArrayBase< double > const &other) const;

673 11 operator != 0 4 1323 41 PointerToArrayBase< double >::operator != 0 1 17 0
103
inline bool PointerToArrayBase< double >::operator !=(PointerToArrayBase< double > const &other) const;

674 19 ConstPointerToArray 0 260 1322 50 ConstPointerToArray< double >::ConstPointerToArray 0 2 1 2 0
198
inline ConstPointerToArray< double >::ConstPointerToArray(PointerToArray< double > const &copy);
inline ConstPointerToArray< double >::ConstPointerToArray(ConstPointerToArray< double > const &copy);

675 5 clear 0 4 1322 36 ConstPointerToArray< double >::clear 0 1 3 0
55
inline void ConstPointerToArray< double >::clear(void);

676 4 size 0 4 1322 35 ConstPointerToArray< double >::size 0 1 4 0
78
inline unsigned long long int ConstPointerToArray< double >::size(void) const;

677 11 get_element 0 4 1322 42 ConstPointerToArray< double >::get_element 0 1 5 0
96
inline double const &ConstPointerToArray< double >::get_element(unsigned long long int n) const;

678 11 __getitem__ 0 4 1322 42 ConstPointerToArray< double >::__getitem__ 0 1 6 0
89
double const &ConstPointerToArray< double >::__getitem__(unsigned long long int n) const;

679 8 get_data 0 4 1322 39 ConstPointerToArray< double >::get_data 0 1 7 0
62
PyObject *ConstPointerToArray< double >::get_data(void) const;

680 11 get_subdata 0 4 1322 42 ConstPointerToArray< double >::get_subdata 0 1 8 0
115
PyObject *ConstPointerToArray< double >::get_subdata(unsigned long long int n, unsigned long long int count) const;

681 13 get_ref_count 0 4 1322 44 ConstPointerToArray< double >::get_ref_count 0 1 9 0
68
inline int ConstPointerToArray< double >::get_ref_count(void) const;

682 18 get_node_ref_count 0 4 1322 49 ConstPointerToArray< double >::get_node_ref_count 0 1 10 0
73
inline int ConstPointerToArray< double >::get_node_ref_count(void) const;

683 5 count 0 4 1322 36 ConstPointerToArray< double >::count 0 1 11 0
89
inline unsigned long long int ConstPointerToArray< double >::count(double const &) const;

684 10 __reduce__ 0 4 1322 41 ConstPointerToArray< double >::__reduce__ 0 1 12 0
74
PyObject *ConstPointerToArray< double >::__reduce__(PyObject *self) const;

685 13 __getbuffer__ 0 4 1322 44 ConstPointerToArray< double >::__getbuffer__ 0 1 13 0
99
int ConstPointerToArray< double >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

686 17 __releasebuffer__ 0 4 1322 48 ConstPointerToArray< double >::__releasebuffer__ 0 1 14 0
93
void ConstPointerToArray< double >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

687 12 __deepcopy__ 0 4 1322 43 ConstPointerToArray< double >::__deepcopy__ 0 1 15 0
96
ConstPointerToArray< double > ConstPointerToArray< double >::__deepcopy__(PyObject *memo) const;

688 20 ~ConstPointerToArray 0 516 1322 51 ConstPointerToArray< double >::~ConstPointerToArray 0 0 0
58
ConstPointerToArray< double >::~ConstPointerToArray(void);

689 5 clear 0 4 1330 55 PointerToBase< ReferenceCountedVector< float > >::clear 0 1 39 0
74
inline void PointerToBase< ReferenceCountedVector< float > >::clear(void);

690 6 output 0 4 1330 56 PointerToBase< ReferenceCountedVector< float > >::output 0 1 40 0
87
void PointerToBase< ReferenceCountedVector< float > >::output(std::ostream &out) const;

691 19 ~PointerToArrayBase 0 516 1329 48 PointerToArrayBase< float >::~PointerToArrayBase 0 0 0
62
inline PointerToArrayBase< float >::~PointerToArrayBase(void);

692 11 operator == 0 4 1329 40 PointerToArrayBase< float >::operator == 0 1 37 64
// These are implemented in PointerToVoid, but expose them here.
101
inline bool PointerToArrayBase< float >::operator ==(PointerToArrayBase< float > const &other) const;

693 11 operator != 0 4 1329 40 PointerToArrayBase< float >::operator != 0 1 38 0
101
inline bool PointerToArrayBase< float >::operator !=(PointerToArrayBase< float > const &other) const;

694 19 ConstPointerToArray 0 260 1328 49 ConstPointerToArray< float >::ConstPointerToArray 0 2 22 23 0
194
inline ConstPointerToArray< float >::ConstPointerToArray(PointerToArray< float > const &copy);
inline ConstPointerToArray< float >::ConstPointerToArray(ConstPointerToArray< float > const &copy);

695 5 clear 0 4 1328 35 ConstPointerToArray< float >::clear 0 1 24 0
54
inline void ConstPointerToArray< float >::clear(void);

696 4 size 0 4 1328 34 ConstPointerToArray< float >::size 0 1 25 0
77
inline unsigned long long int ConstPointerToArray< float >::size(void) const;

697 11 get_element 0 4 1328 41 ConstPointerToArray< float >::get_element 0 1 26 0
94
inline float const &ConstPointerToArray< float >::get_element(unsigned long long int n) const;

698 11 __getitem__ 0 4 1328 41 ConstPointerToArray< float >::__getitem__ 0 1 27 0
87
float const &ConstPointerToArray< float >::__getitem__(unsigned long long int n) const;

699 8 get_data 0 4 1328 38 ConstPointerToArray< float >::get_data 0 1 28 0
61
PyObject *ConstPointerToArray< float >::get_data(void) const;

700 11 get_subdata 0 4 1328 41 ConstPointerToArray< float >::get_subdata 0 1 29 0
114
PyObject *ConstPointerToArray< float >::get_subdata(unsigned long long int n, unsigned long long int count) const;

701 13 get_ref_count 0 4 1328 43 ConstPointerToArray< float >::get_ref_count 0 1 30 0
67
inline int ConstPointerToArray< float >::get_ref_count(void) const;

702 18 get_node_ref_count 0 4 1328 48 ConstPointerToArray< float >::get_node_ref_count 0 1 31 0
72
inline int ConstPointerToArray< float >::get_node_ref_count(void) const;

703 5 count 0 4 1328 35 ConstPointerToArray< float >::count 0 1 32 0
87
inline unsigned long long int ConstPointerToArray< float >::count(float const &) const;

704 10 __reduce__ 0 4 1328 40 ConstPointerToArray< float >::__reduce__ 0 1 33 0
73
PyObject *ConstPointerToArray< float >::__reduce__(PyObject *self) const;

705 13 __getbuffer__ 0 4 1328 43 ConstPointerToArray< float >::__getbuffer__ 0 1 34 0
98
int ConstPointerToArray< float >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

706 17 __releasebuffer__ 0 4 1328 47 ConstPointerToArray< float >::__releasebuffer__ 0 1 35 0
92
void ConstPointerToArray< float >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

707 12 __deepcopy__ 0 4 1328 42 ConstPointerToArray< float >::__deepcopy__ 0 1 36 0
94
ConstPointerToArray< float > ConstPointerToArray< float >::__deepcopy__(PyObject *memo) const;

708 20 ~ConstPointerToArray 0 516 1328 50 ConstPointerToArray< float >::~ConstPointerToArray 0 0 0
57
ConstPointerToArray< float >::~ConstPointerToArray(void);

709 5 clear 0 4 1334 53 PointerToBase< ReferenceCountedVector< int > >::clear 0 1 58 0
72
inline void PointerToBase< ReferenceCountedVector< int > >::clear(void);

710 6 output 0 4 1334 54 PointerToBase< ReferenceCountedVector< int > >::output 0 1 59 0
85
void PointerToBase< ReferenceCountedVector< int > >::output(std::ostream &out) const;

711 19 ~PointerToArrayBase 0 516 1333 46 PointerToArrayBase< int >::~PointerToArrayBase 0 0 0
60
inline PointerToArrayBase< int >::~PointerToArrayBase(void);

712 11 operator == 0 4 1333 38 PointerToArrayBase< int >::operator == 0 1 56 64
// These are implemented in PointerToVoid, but expose them here.
97
inline bool PointerToArrayBase< int >::operator ==(PointerToArrayBase< int > const &other) const;

713 11 operator != 0 4 1333 38 PointerToArrayBase< int >::operator != 0 1 57 0
97
inline bool PointerToArrayBase< int >::operator !=(PointerToArrayBase< int > const &other) const;

714 19 ConstPointerToArray 0 260 1332 47 ConstPointerToArray< int >::ConstPointerToArray 0 2 41 42 0
186
inline ConstPointerToArray< int >::ConstPointerToArray(PointerToArray< int > const &copy);
inline ConstPointerToArray< int >::ConstPointerToArray(ConstPointerToArray< int > const &copy);

715 5 clear 0 4 1332 33 ConstPointerToArray< int >::clear 0 1 43 0
52
inline void ConstPointerToArray< int >::clear(void);

716 4 size 0 4 1332 32 ConstPointerToArray< int >::size 0 1 44 0
75
inline unsigned long long int ConstPointerToArray< int >::size(void) const;

717 11 get_element 0 4 1332 39 ConstPointerToArray< int >::get_element 0 1 45 0
90
inline int const &ConstPointerToArray< int >::get_element(unsigned long long int n) const;

718 11 __getitem__ 0 4 1332 39 ConstPointerToArray< int >::__getitem__ 0 1 46 0
83
int const &ConstPointerToArray< int >::__getitem__(unsigned long long int n) const;

719 8 get_data 0 4 1332 36 ConstPointerToArray< int >::get_data 0 1 47 0
59
PyObject *ConstPointerToArray< int >::get_data(void) const;

720 11 get_subdata 0 4 1332 39 ConstPointerToArray< int >::get_subdata 0 1 48 0
112
PyObject *ConstPointerToArray< int >::get_subdata(unsigned long long int n, unsigned long long int count) const;

721 13 get_ref_count 0 4 1332 41 ConstPointerToArray< int >::get_ref_count 0 1 49 0
65
inline int ConstPointerToArray< int >::get_ref_count(void) const;

722 18 get_node_ref_count 0 4 1332 46 ConstPointerToArray< int >::get_node_ref_count 0 1 50 0
70
inline int ConstPointerToArray< int >::get_node_ref_count(void) const;

723 5 count 0 4 1332 33 ConstPointerToArray< int >::count 0 1 51 0
83
inline unsigned long long int ConstPointerToArray< int >::count(int const &) const;

724 10 __reduce__ 0 4 1332 38 ConstPointerToArray< int >::__reduce__ 0 1 52 0
71
PyObject *ConstPointerToArray< int >::__reduce__(PyObject *self) const;

725 13 __getbuffer__ 0 4 1332 41 ConstPointerToArray< int >::__getbuffer__ 0 1 53 0
96
int ConstPointerToArray< int >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

726 17 __releasebuffer__ 0 4 1332 45 ConstPointerToArray< int >::__releasebuffer__ 0 1 54 0
90
void ConstPointerToArray< int >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

727 12 __deepcopy__ 0 4 1332 40 ConstPointerToArray< int >::__deepcopy__ 0 1 55 0
90
ConstPointerToArray< int > ConstPointerToArray< int >::__deepcopy__(PyObject *memo) const;

728 20 ~ConstPointerToArray 0 516 1332 48 ConstPointerToArray< int >::~ConstPointerToArray 0 0 0
55
ConstPointerToArray< int >::~ConstPointerToArray(void);

729 5 clear 0 4 1338 63 PointerToBase< ReferenceCountedVector< unsigned char > >::clear 0 1 77 0
82
inline void PointerToBase< ReferenceCountedVector< unsigned char > >::clear(void);

730 6 output 0 4 1338 64 PointerToBase< ReferenceCountedVector< unsigned char > >::output 0 1 78 0
95
void PointerToBase< ReferenceCountedVector< unsigned char > >::output(std::ostream &out) const;

731 19 ~PointerToArrayBase 0 516 1337 56 PointerToArrayBase< unsigned char >::~PointerToArrayBase 0 0 0
70
inline PointerToArrayBase< unsigned char >::~PointerToArrayBase(void);

732 11 operator == 0 4 1337 48 PointerToArrayBase< unsigned char >::operator == 0 1 75 64
// These are implemented in PointerToVoid, but expose them here.
117
inline bool PointerToArrayBase< unsigned char >::operator ==(PointerToArrayBase< unsigned char > const &other) const;

733 11 operator != 0 4 1337 48 PointerToArrayBase< unsigned char >::operator != 0 1 76 0
117
inline bool PointerToArrayBase< unsigned char >::operator !=(PointerToArrayBase< unsigned char > const &other) const;

734 19 ConstPointerToArray 0 260 1336 57 ConstPointerToArray< unsigned char >::ConstPointerToArray 0 2 60 61 0
226
inline ConstPointerToArray< unsigned char >::ConstPointerToArray(PointerToArray< unsigned char > const &copy);
inline ConstPointerToArray< unsigned char >::ConstPointerToArray(ConstPointerToArray< unsigned char > const &copy);

735 5 clear 0 4 1336 43 ConstPointerToArray< unsigned char >::clear 0 1 62 0
62
inline void ConstPointerToArray< unsigned char >::clear(void);

736 4 size 0 4 1336 42 ConstPointerToArray< unsigned char >::size 0 1 63 0
85
inline unsigned long long int ConstPointerToArray< unsigned char >::size(void) const;

737 11 get_element 0 4 1336 49 ConstPointerToArray< unsigned char >::get_element 0 1 64 0
110
inline unsigned char const &ConstPointerToArray< unsigned char >::get_element(unsigned long long int n) const;

738 11 __getitem__ 0 4 1336 49 ConstPointerToArray< unsigned char >::__getitem__ 0 1 65 0
103
unsigned char const &ConstPointerToArray< unsigned char >::__getitem__(unsigned long long int n) const;

739 8 get_data 0 4 1336 46 ConstPointerToArray< unsigned char >::get_data 0 1 66 0
69
PyObject *ConstPointerToArray< unsigned char >::get_data(void) const;

740 11 get_subdata 0 4 1336 49 ConstPointerToArray< unsigned char >::get_subdata 0 1 67 0
122
PyObject *ConstPointerToArray< unsigned char >::get_subdata(unsigned long long int n, unsigned long long int count) const;

741 13 get_ref_count 0 4 1336 51 ConstPointerToArray< unsigned char >::get_ref_count 0 1 68 0
75
inline int ConstPointerToArray< unsigned char >::get_ref_count(void) const;

742 18 get_node_ref_count 0 4 1336 56 ConstPointerToArray< unsigned char >::get_node_ref_count 0 1 69 0
80
inline int ConstPointerToArray< unsigned char >::get_node_ref_count(void) const;

743 5 count 0 4 1336 43 ConstPointerToArray< unsigned char >::count 0 1 70 0
103
inline unsigned long long int ConstPointerToArray< unsigned char >::count(unsigned char const &) const;

744 10 __reduce__ 0 4 1336 48 ConstPointerToArray< unsigned char >::__reduce__ 0 1 71 0
81
PyObject *ConstPointerToArray< unsigned char >::__reduce__(PyObject *self) const;

745 13 __getbuffer__ 0 4 1336 51 ConstPointerToArray< unsigned char >::__getbuffer__ 0 1 72 0
106
int ConstPointerToArray< unsigned char >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

746 17 __releasebuffer__ 0 4 1336 55 ConstPointerToArray< unsigned char >::__releasebuffer__ 0 1 73 0
100
void ConstPointerToArray< unsigned char >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

747 12 __deepcopy__ 0 4 1336 50 ConstPointerToArray< unsigned char >::__deepcopy__ 0 1 74 0
110
ConstPointerToArray< unsigned char > ConstPointerToArray< unsigned char >::__deepcopy__(PyObject *memo) const;

748 20 ~ConstPointerToArray 0 516 1336 58 ConstPointerToArray< unsigned char >::~ConstPointerToArray 0 0 0
65
ConstPointerToArray< unsigned char >::~ConstPointerToArray(void);

749 14 PointerToArray 0 260 1340 40 PointerToArray< double >::PointerToArray 0 3 79 80 81 0
279
inline PointerToArray< double >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((double const *)(0))));
inline PointerToArray< double >::PointerToArray(PointerToArray< double > const &copy);
PointerToArray< double >::PointerToArray(PyObject *self, PyObject *source);

750 11 empty_array 0 4 1340 37 PointerToArray< double >::empty_array 0 1 82 0
171
static inline PointerToArray< double > PointerToArray< double >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((double const *)(0))));

751 5 clear 0 4 1340 31 PointerToArray< double >::clear 0 1 83 0
50
inline void PointerToArray< double >::clear(void);

752 4 size 0 4 1340 30 PointerToArray< double >::size 0 1 84 0
73
inline unsigned long long int PointerToArray< double >::size(void) const;

753 9 push_back 0 4 1340 35 PointerToArray< double >::push_back 0 1 85 0
65
inline void PointerToArray< double >::push_back(double const &x);

754 8 pop_back 0 4 1340 34 PointerToArray< double >::pop_back 0 1 86 0
53
inline void PointerToArray< double >::pop_back(void);

755 11 get_element 0 4 1340 37 PointerToArray< double >::get_element 0 1 87 0
91
inline double const &PointerToArray< double >::get_element(unsigned long long int n) const;

756 11 set_element 0 4 1340 37 PointerToArray< double >::set_element 0 1 88 0
97
inline void PointerToArray< double >::set_element(unsigned long long int n, double const &value);

757 11 __getitem__ 0 4 1340 37 PointerToArray< double >::__getitem__ 0 1 89 0
84
double const &PointerToArray< double >::__getitem__(unsigned long long int n) const;

758 11 __setitem__ 0 4 1340 37 PointerToArray< double >::__setitem__ 0 1 90 0
90
void PointerToArray< double >::__setitem__(unsigned long long int n, double const &value);

759 8 get_data 0 4 1340 34 PointerToArray< double >::get_data 0 1 91 0
57
PyObject *PointerToArray< double >::get_data(void) const;

760 8 set_data 0 4 1340 34 PointerToArray< double >::set_data 0 1 92 0
56
void PointerToArray< double >::set_data(PyObject *data);

761 11 get_subdata 0 4 1340 37 PointerToArray< double >::get_subdata 0 1 93 0
110
PyObject *PointerToArray< double >::get_subdata(unsigned long long int n, unsigned long long int count) const;

762 11 set_subdata 0 4 1340 37 PointerToArray< double >::set_subdata 0 1 94 0
145
inline void PointerToArray< double >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

763 13 get_ref_count 0 4 1340 39 PointerToArray< double >::get_ref_count 0 1 95 0
63
inline int PointerToArray< double >::get_ref_count(void) const;

764 18 get_node_ref_count 0 4 1340 44 PointerToArray< double >::get_node_ref_count 0 1 96 0
68
inline int PointerToArray< double >::get_node_ref_count(void) const;

765 5 count 0 4 1340 31 PointerToArray< double >::count 0 1 97 0
84
inline unsigned long long int PointerToArray< double >::count(double const &) const;

766 10 __reduce__ 0 4 1340 36 PointerToArray< double >::__reduce__ 0 1 98 0
69
PyObject *PointerToArray< double >::__reduce__(PyObject *self) const;

767 13 __getbuffer__ 0 4 1340 39 PointerToArray< double >::__getbuffer__ 0 1 99 0
88
int PointerToArray< double >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

768 17 __releasebuffer__ 0 4 1340 43 PointerToArray< double >::__releasebuffer__ 0 1 100 0
88
void PointerToArray< double >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

769 12 __deepcopy__ 0 4 1340 38 PointerToArray< double >::__deepcopy__ 0 1 101 0
86
PointerToArray< double > PointerToArray< double >::__deepcopy__(PyObject *memo) const;

770 15 ~PointerToArray 0 516 1340 41 PointerToArray< double >::~PointerToArray 0 0 0
48
PointerToArray< double >::~PointerToArray(void);

771 14 PointerToArray 0 260 1342 39 PointerToArray< float >::PointerToArray 0 3 102 103 104 0
274
inline PointerToArray< float >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((float const *)(0))));
inline PointerToArray< float >::PointerToArray(PointerToArray< float > const &copy);
PointerToArray< float >::PointerToArray(PyObject *self, PyObject *source);

772 11 empty_array 0 4 1342 36 PointerToArray< float >::empty_array 0 1 105 0
168
static inline PointerToArray< float > PointerToArray< float >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((float const *)(0))));

773 5 clear 0 4 1342 30 PointerToArray< float >::clear 0 1 106 0
49
inline void PointerToArray< float >::clear(void);

774 4 size 0 4 1342 29 PointerToArray< float >::size 0 1 107 0
72
inline unsigned long long int PointerToArray< float >::size(void) const;

775 9 push_back 0 4 1342 34 PointerToArray< float >::push_back 0 1 108 0
63
inline void PointerToArray< float >::push_back(float const &x);

776 8 pop_back 0 4 1342 33 PointerToArray< float >::pop_back 0 1 109 0
52
inline void PointerToArray< float >::pop_back(void);

777 11 get_element 0 4 1342 36 PointerToArray< float >::get_element 0 1 110 0
89
inline float const &PointerToArray< float >::get_element(unsigned long long int n) const;

778 11 set_element 0 4 1342 36 PointerToArray< float >::set_element 0 1 111 0
95
inline void PointerToArray< float >::set_element(unsigned long long int n, float const &value);

779 11 __getitem__ 0 4 1342 36 PointerToArray< float >::__getitem__ 0 1 112 0
82
float const &PointerToArray< float >::__getitem__(unsigned long long int n) const;

780 11 __setitem__ 0 4 1342 36 PointerToArray< float >::__setitem__ 0 1 113 0
88
void PointerToArray< float >::__setitem__(unsigned long long int n, float const &value);

781 8 get_data 0 4 1342 33 PointerToArray< float >::get_data 0 1 114 0
56
PyObject *PointerToArray< float >::get_data(void) const;

782 8 set_data 0 4 1342 33 PointerToArray< float >::set_data 0 1 115 0
55
void PointerToArray< float >::set_data(PyObject *data);

783 11 get_subdata 0 4 1342 36 PointerToArray< float >::get_subdata 0 1 116 0
109
PyObject *PointerToArray< float >::get_subdata(unsigned long long int n, unsigned long long int count) const;

784 11 set_subdata 0 4 1342 36 PointerToArray< float >::set_subdata 0 1 117 0
144
inline void PointerToArray< float >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

785 13 get_ref_count 0 4 1342 38 PointerToArray< float >::get_ref_count 0 1 118 0
62
inline int PointerToArray< float >::get_ref_count(void) const;

786 18 get_node_ref_count 0 4 1342 43 PointerToArray< float >::get_node_ref_count 0 1 119 0
67
inline int PointerToArray< float >::get_node_ref_count(void) const;

787 5 count 0 4 1342 30 PointerToArray< float >::count 0 1 120 0
82
inline unsigned long long int PointerToArray< float >::count(float const &) const;

788 10 __reduce__ 0 4 1342 35 PointerToArray< float >::__reduce__ 0 1 121 0
68
PyObject *PointerToArray< float >::__reduce__(PyObject *self) const;

789 13 __getbuffer__ 0 4 1342 38 PointerToArray< float >::__getbuffer__ 0 1 122 0
87
int PointerToArray< float >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

790 17 __releasebuffer__ 0 4 1342 42 PointerToArray< float >::__releasebuffer__ 0 1 123 0
87
void PointerToArray< float >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

791 12 __deepcopy__ 0 4 1342 37 PointerToArray< float >::__deepcopy__ 0 1 124 0
84
PointerToArray< float > PointerToArray< float >::__deepcopy__(PyObject *memo) const;

792 15 ~PointerToArray 0 516 1342 40 PointerToArray< float >::~PointerToArray 0 0 0
47
PointerToArray< float >::~PointerToArray(void);

793 14 PointerToArray 0 260 1344 37 PointerToArray< int >::PointerToArray 0 3 125 126 127 0
264
inline PointerToArray< int >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((int const *)(0))));
inline PointerToArray< int >::PointerToArray(PointerToArray< int > const &copy);
PointerToArray< int >::PointerToArray(PyObject *self, PyObject *source);

794 11 empty_array 0 4 1344 34 PointerToArray< int >::empty_array 0 1 128 0
162
static inline PointerToArray< int > PointerToArray< int >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((int const *)(0))));

795 5 clear 0 4 1344 28 PointerToArray< int >::clear 0 1 129 0
47
inline void PointerToArray< int >::clear(void);

796 4 size 0 4 1344 27 PointerToArray< int >::size 0 1 130 0
70
inline unsigned long long int PointerToArray< int >::size(void) const;

797 9 push_back 0 4 1344 32 PointerToArray< int >::push_back 0 1 131 0
59
inline void PointerToArray< int >::push_back(int const &x);

798 8 pop_back 0 4 1344 31 PointerToArray< int >::pop_back 0 1 132 0
50
inline void PointerToArray< int >::pop_back(void);

799 11 get_element 0 4 1344 34 PointerToArray< int >::get_element 0 1 133 0
85
inline int const &PointerToArray< int >::get_element(unsigned long long int n) const;

800 11 set_element 0 4 1344 34 PointerToArray< int >::set_element 0 1 134 0
91
inline void PointerToArray< int >::set_element(unsigned long long int n, int const &value);

801 11 __getitem__ 0 4 1344 34 PointerToArray< int >::__getitem__ 0 1 135 0
78
int const &PointerToArray< int >::__getitem__(unsigned long long int n) const;

802 11 __setitem__ 0 4 1344 34 PointerToArray< int >::__setitem__ 0 1 136 0
84
void PointerToArray< int >::__setitem__(unsigned long long int n, int const &value);

803 8 get_data 0 4 1344 31 PointerToArray< int >::get_data 0 1 137 0
54
PyObject *PointerToArray< int >::get_data(void) const;

804 8 set_data 0 4 1344 31 PointerToArray< int >::set_data 0 1 138 0
53
void PointerToArray< int >::set_data(PyObject *data);

805 11 get_subdata 0 4 1344 34 PointerToArray< int >::get_subdata 0 1 139 0
107
PyObject *PointerToArray< int >::get_subdata(unsigned long long int n, unsigned long long int count) const;

806 11 set_subdata 0 4 1344 34 PointerToArray< int >::set_subdata 0 1 140 0
142
inline void PointerToArray< int >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

807 13 get_ref_count 0 4 1344 36 PointerToArray< int >::get_ref_count 0 1 141 0
60
inline int PointerToArray< int >::get_ref_count(void) const;

808 18 get_node_ref_count 0 4 1344 41 PointerToArray< int >::get_node_ref_count 0 1 142 0
65
inline int PointerToArray< int >::get_node_ref_count(void) const;

809 5 count 0 4 1344 28 PointerToArray< int >::count 0 1 143 0
78
inline unsigned long long int PointerToArray< int >::count(int const &) const;

810 10 __reduce__ 0 4 1344 33 PointerToArray< int >::__reduce__ 0 1 144 0
66
PyObject *PointerToArray< int >::__reduce__(PyObject *self) const;

811 13 __getbuffer__ 0 4 1344 36 PointerToArray< int >::__getbuffer__ 0 1 145 0
85
int PointerToArray< int >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

812 17 __releasebuffer__ 0 4 1344 40 PointerToArray< int >::__releasebuffer__ 0 1 146 0
85
void PointerToArray< int >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

813 12 __deepcopy__ 0 4 1344 35 PointerToArray< int >::__deepcopy__ 0 1 147 0
80
PointerToArray< int > PointerToArray< int >::__deepcopy__(PyObject *memo) const;

814 15 ~PointerToArray 0 516 1344 38 PointerToArray< int >::~PointerToArray 0 0 0
45
PointerToArray< int >::~PointerToArray(void);

815 14 PointerToArray 0 260 1346 47 PointerToArray< unsigned char >::PointerToArray 0 3 148 149 150 0
314
inline PointerToArray< unsigned char >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((unsigned char const *)(0))));
inline PointerToArray< unsigned char >::PointerToArray(PointerToArray< unsigned char > const &copy);
PointerToArray< unsigned char >::PointerToArray(PyObject *self, PyObject *source);

816 11 empty_array 0 4 1346 44 PointerToArray< unsigned char >::empty_array 0 1 151 0
192
static inline PointerToArray< unsigned char > PointerToArray< unsigned char >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((unsigned char const *)(0))));

817 5 clear 0 4 1346 38 PointerToArray< unsigned char >::clear 0 1 152 0
57
inline void PointerToArray< unsigned char >::clear(void);

818 4 size 0 4 1346 37 PointerToArray< unsigned char >::size 0 1 153 0
80
inline unsigned long long int PointerToArray< unsigned char >::size(void) const;

819 9 push_back 0 4 1346 42 PointerToArray< unsigned char >::push_back 0 1 154 0
79
inline void PointerToArray< unsigned char >::push_back(unsigned char const &x);

820 8 pop_back 0 4 1346 41 PointerToArray< unsigned char >::pop_back 0 1 155 0
60
inline void PointerToArray< unsigned char >::pop_back(void);

821 11 get_element 0 4 1346 44 PointerToArray< unsigned char >::get_element 0 1 156 0
105
inline unsigned char const &PointerToArray< unsigned char >::get_element(unsigned long long int n) const;

822 11 set_element 0 4 1346 44 PointerToArray< unsigned char >::set_element 0 1 157 0
111
inline void PointerToArray< unsigned char >::set_element(unsigned long long int n, unsigned char const &value);

823 11 __getitem__ 0 4 1346 44 PointerToArray< unsigned char >::__getitem__ 0 1 158 0
98
unsigned char const &PointerToArray< unsigned char >::__getitem__(unsigned long long int n) const;

824 11 __setitem__ 0 4 1346 44 PointerToArray< unsigned char >::__setitem__ 0 1 159 0
104
void PointerToArray< unsigned char >::__setitem__(unsigned long long int n, unsigned char const &value);

825 8 get_data 0 4 1346 41 PointerToArray< unsigned char >::get_data 0 1 160 0
64
PyObject *PointerToArray< unsigned char >::get_data(void) const;

826 8 set_data 0 4 1346 41 PointerToArray< unsigned char >::set_data 0 1 161 0
63
void PointerToArray< unsigned char >::set_data(PyObject *data);

827 11 get_subdata 0 4 1346 44 PointerToArray< unsigned char >::get_subdata 0 1 162 0
117
PyObject *PointerToArray< unsigned char >::get_subdata(unsigned long long int n, unsigned long long int count) const;

828 11 set_subdata 0 4 1346 44 PointerToArray< unsigned char >::set_subdata 0 1 163 0
152
inline void PointerToArray< unsigned char >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

829 13 get_ref_count 0 4 1346 46 PointerToArray< unsigned char >::get_ref_count 0 1 164 0
70
inline int PointerToArray< unsigned char >::get_ref_count(void) const;

830 18 get_node_ref_count 0 4 1346 51 PointerToArray< unsigned char >::get_node_ref_count 0 1 165 0
75
inline int PointerToArray< unsigned char >::get_node_ref_count(void) const;

831 5 count 0 4 1346 38 PointerToArray< unsigned char >::count 0 1 166 0
98
inline unsigned long long int PointerToArray< unsigned char >::count(unsigned char const &) const;

832 10 __reduce__ 0 4 1346 43 PointerToArray< unsigned char >::__reduce__ 0 1 167 0
76
PyObject *PointerToArray< unsigned char >::__reduce__(PyObject *self) const;

833 13 __getbuffer__ 0 4 1346 46 PointerToArray< unsigned char >::__getbuffer__ 0 1 168 0
95
int PointerToArray< unsigned char >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

834 17 __releasebuffer__ 0 4 1346 50 PointerToArray< unsigned char >::__releasebuffer__ 0 1 169 0
95
void PointerToArray< unsigned char >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

835 12 __deepcopy__ 0 4 1346 45 PointerToArray< unsigned char >::__deepcopy__ 0 1 170 0
100
PointerToArray< unsigned char > PointerToArray< unsigned char >::__deepcopy__(PyObject *memo) const;

836 15 ~PointerToArray 0 516 1346 48 PointerToArray< unsigned char >::~PointerToArray 0 0 0
55
PointerToArray< unsigned char >::~PointerToArray(void);

837 11 is_tracking 0 4 1347 24 MemoryUsage::is_tracking 0 1 172 125
/**
 * Returns true if the MemoryUsage object is currently tracking memory (e.g.
 * track-memory-usage is configured #t).
 */
50
static inline bool MemoryUsage::is_tracking(void);

838 11 is_counting 0 4 1347 24 MemoryUsage::is_counting 0 1 173 168
/**
 * Returns true if the MemoryUsage object is currently at least counting
 * memory (e.g.  this is a Windows debug build), even if it's not fully
 * tracking it.
 */
50
static inline bool MemoryUsage::is_counting(void);

839 20 get_current_cpp_size 0 4 1347 33 MemoryUsage::get_current_cpp_size 0 1 174 136
/**
 * Returns the total number of bytes of allocated memory consumed by C++
 * objects, not including the memory previously frozen.
 */
66
static inline std::size_t MemoryUsage::get_current_cpp_size(void);

840 18 get_total_cpp_size 0 4 1347 31 MemoryUsage::get_total_cpp_size 0 1 175 132
/**
 * Returns the total number of bytes of allocated memory consumed by C++
 * objects, including the memory previously frozen.
 */
64
static inline std::size_t MemoryUsage::get_total_cpp_size(void);

841 26 get_panda_heap_single_size 0 4 1347 39 MemoryUsage::get_panda_heap_single_size 0 1 176 119
/**
 * Returns the total number of bytes allocated from the heap from code within
 * Panda, for individual objects.
 */
72
static inline std::size_t MemoryUsage::get_panda_heap_single_size(void);

842 25 get_panda_heap_array_size 0 4 1347 38 MemoryUsage::get_panda_heap_array_size 0 1 177 107
/**
 * Returns the total number of bytes allocated from the heap from code within
 * Panda, for arrays.
 */
71
static inline std::size_t MemoryUsage::get_panda_heap_array_size(void);

843 23 get_panda_heap_overhead 0 4 1347 36 MemoryUsage::get_panda_heap_overhead 0 1 178 190
/**
 * Returns the extra bytes allocated from the system that are not immediately
 * used for holding allocated objects.  This can only be determined if
 * ALTERNATIVE_MALLOC is enabled.
 */
69
static inline std::size_t MemoryUsage::get_panda_heap_overhead(void);

844 19 get_panda_mmap_size 0 4 1347 32 MemoryUsage::get_panda_mmap_size 0 1 179 110
/**
 * Returns the total number of bytes allocated from the virtual memory pool
 * from code within Panda.
 */
65
static inline std::size_t MemoryUsage::get_panda_mmap_size(void);

845 17 get_external_size 0 4 1347 30 MemoryUsage::get_external_size 0 1 180 496
/**
 * Returns the total number of bytes of allocated memory in the heap that
 * Panda didn't seem to be responsible for.  This includes a few bytes for
 * very low-level objects (like ConfigVariables) that cannot use Panda memory
 * tracking because they are so very low-level.
 *
 * This also includes all of the memory that might have been allocated by a
 * high-level interpreter, like Python.
 *
 * This number is only available if Panda is able to hook into the actual heap
 * callback.
 */
63
static inline std::size_t MemoryUsage::get_external_size(void);

846 14 get_total_size 0 4 1347 27 MemoryUsage::get_total_size 0 1 181 113
/**
 * Returns the total size of allocated memory consumed by the process, as
 * nearly as can be determined.
 */
60
static inline std::size_t MemoryUsage::get_total_size(void);

847 16 get_num_pointers 0 4 1347 29 MemoryUsage::get_num_pointers 0 1 182 59
/**
 * Returns the number of pointers currently active.
 */
54
static inline int MemoryUsage::get_num_pointers(void);

848 12 get_pointers 0 4 1347 25 MemoryUsage::get_pointers 0 1 183 100
/**
 * Fills the indicated MemoryUsagePointers with the set of all pointers
 * currently active.
 */
74
static inline void MemoryUsage::get_pointers(MemoryUsagePointers &result);

849 20 get_pointers_of_type 0 4 1347 33 MemoryUsage::get_pointers_of_type 0 1 184 122
/**
 * Fills the indicated MemoryUsagePointers with the set of all pointers of the
 * indicated type currently active.
 */
99
static inline void MemoryUsage::get_pointers_of_type(MemoryUsagePointers &result, TypeHandle type);

850 19 get_pointers_of_age 0 4 1347 32 MemoryUsage::get_pointers_of_age 0 1 185 159
/**
 * Fills the indicated MemoryUsagePointers with the set of all pointers that
 * were allocated within the range of the indicated number of seconds ago.
 */
105
static inline void MemoryUsage::get_pointers_of_age(MemoryUsagePointers &result, double from, double to);

851 28 get_pointers_with_zero_count 0 4 1347 41 MemoryUsage::get_pointers_with_zero_count 0 1 186 852
/**
 * Fills the indicated MemoryUsagePointers with the set of all currently
 * active pointers (that is, pointers allocated since the last call to
 * freeze(), and not yet freed) that have a zero reference count.
 *
 * Generally, an undeleted pointer with a zero reference count means its
 * reference count has never been incremented beyond zero (since once it has
 * been incremented, the only way it can return to zero would free the
 * pointer).  This may include objects that are allocated statically or on the
 * stack, which are never intended to be deleted.  Or, it might represent a
 * programmer or compiler error.
 *
 * This function has the side-effect of incrementing each of their reference
 * counts by one, thus preventing them from ever being freed--but since they
 * hadn't been freed anyway, probably no additional harm is done.
 */
90
static inline void MemoryUsage::get_pointers_with_zero_count(MemoryUsagePointers &result);

852 6 freeze 0 4 1347 19 MemoryUsage::freeze 0 1 187 285
/**
 * 'Freezes' all pointers currently stored so that they are no longer
 * reported; only newly allocate pointers from this point on will appear in
 * future information requests.  This makes it easier to differentiate between
 * continuous leaks and one-time memory allocations.
 */
45
static inline void MemoryUsage::freeze(void);

853 18 show_current_types 0 4 1347 31 MemoryUsage::show_current_types 0 1 188 70
/**
 * Shows the breakdown of types of all of the active pointers.
 */
57
static inline void MemoryUsage::show_current_types(void);

854 16 show_trend_types 0 4 1347 29 MemoryUsage::show_trend_types 0 1 189 118
/**
 * Shows the breakdown of types of all of the pointers allocated and freed
 * since the last call to freeze().
 */
55
static inline void MemoryUsage::show_trend_types(void);

855 17 show_current_ages 0 4 1347 30 MemoryUsage::show_current_ages 0 1 190 69
/**
 * Shows the breakdown of ages of all of the active pointers.
 */
56
static inline void MemoryUsage::show_current_ages(void);

856 15 show_trend_ages 0 4 1347 28 MemoryUsage::show_trend_ages 0 1 191 117
/**
 * Shows the breakdown of ages of all of the pointers allocated and freed
 * since the last call to freeze().
 */
54
static inline void MemoryUsage::show_trend_ages(void);

857 11 MemoryUsage 0 260 1347 24 MemoryUsage::MemoryUsage 0 1 171 10
/**
 *
 */
63
inline MemoryUsage::MemoryUsage(MemoryUsage const &) = default;

858 12 ~MemoryUsage 0 516 1347 25 MemoryUsage::~MemoryUsage 0 0 0
32
MemoryUsage::~MemoryUsage(void);

859 20 upcast_to_MemoryBase 0 12 1352 36 ReferenceCount::upcast_to_MemoryBase 0 1 198 40
upcast from ReferenceCount to MemoryBase
55
MemoryBase *ReferenceCount::upcast_to_MemoryBase(void);

860 26 downcast_to_ReferenceCount 0 12 1326 38 MemoryBase::downcast_to_ReferenceCount 0 0 42
downcast from MemoryBase to ReferenceCount
61
ReferenceCount *MemoryBase::downcast_to_ReferenceCount(void);

861 15 ~ReferenceCount 0 518 1352 31 ReferenceCount::~ReferenceCount 0 0 10
/**
 *
 */
53
virtual inline ReferenceCount::~ReferenceCount(void);

862 13 get_ref_count 0 4 1352 29 ReferenceCount::get_ref_count 0 1 192 47
/**
 * Returns the current reference count.
 */
53
inline int ReferenceCount::get_ref_count(void) const;

863 3 ref 0 4 1352 19 ReferenceCount::ref 0 1 193 538
/**
 * Explicitly increments the reference count.  User code should avoid using
 * ref() and unref() directly, which can result in missed reference counts.
 * Instead, let a PointerTo object manage the reference counting
 * automatically.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 */
44
inline void ReferenceCount::ref(void) const;

864 5 unref 0 6 1352 21 ReferenceCount::unref 0 1 194 865
/**
 * Explicitly decrements the reference count.  Note that the object will not
 * be implicitly deleted by unref() simply because the reference count drops
 * to zero.  (Having a member function delete itself is problematic.) However,
 * see the helper function unref_delete().
 *
 * User code should avoid using ref() and unref() directly, which can result
 * in missed reference counts.  Instead, let a PointerTo object manage the
 * reference counting automatically.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */
54
virtual inline bool ReferenceCount::unref(void) const;

865 24 test_ref_count_integrity 0 4 1352 40 ReferenceCount::test_ref_count_integrity 0 1 195 135
/**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.  Returns true if ok, false otherwise.
 */
65
inline bool ReferenceCount::test_ref_count_integrity(void) const;

866 22 test_ref_count_nonzero 0 4 1352 38 ReferenceCount::test_ref_count_nonzero 0 1 196 144
/**
 * Does some easy checks to make sure that the reference count isn't zero, or
 * completely bogus.  Returns true if ok, false otherwise.
 */
63
inline bool ReferenceCount::test_ref_count_nonzero(void) const;

867 14 get_class_type 0 4 1352 30 ReferenceCount::get_class_type 0 1 197 0
55
static TypeHandle ReferenceCount::get_class_type(void);

868 10 get_length 0 4 1354 18 Buffer::get_length 0 1 200 10
/**
 *
 */
42
inline int Buffer::get_length(void) const;

869 6 Buffer 0 260 1354 14 Buffer::Buffer 0 1 199 10
/**
 *
 */
48
inline Buffer::Buffer(Buffer const &) = default;

870 15 compress_string 0 1 0 15 compress_string 0 1 648 0
78
std::string compress_string(std::string const &source, int compression_level);

871 17 decompress_string 0 1 0 17 decompress_string 0 1 649 0
57
std::string decompress_string(std::string const &source);

872 13 compress_file 0 1 0 13 compress_file 0 1 650 0
88
bool compress_file(Filename const &source, Filename const &dest, int compression_level);

873 15 decompress_file 0 1 0 15 decompress_file 0 1 651 0
67
bool decompress_file(Filename const &source, Filename const &dest);

874 15 compress_stream 0 1 0 15 compress_stream 0 1 652 0
86
bool compress_stream(std::istream &source, std::ostream &dest, int compression_level);

875 17 decompress_stream 0 1 0 17 decompress_stream 0 1 653 0
65
bool decompress_stream(std::istream &source, std::ostream &dest);

876 11 copy_stream 0 1 0 11 copy_stream 0 1 654 0
59
bool copy_stream(std::istream &source, std::ostream &dest);

877 9 add_level 0 6 1355 36 PStatCollectorForwardBase::add_level 0 1 201 0
68
virtual void PStatCollectorForwardBase::add_level(double level) = 0;

878 18 get_node_ref_count 0 4 1356 38 NodeReferenceCount::get_node_ref_count 0 1 202 47
/**
 * Returns the current reference count.
 */
62
inline int NodeReferenceCount::get_node_ref_count(void) const;

879 8 node_ref 0 4 1356 28 NodeReferenceCount::node_ref 0 1 203 107
/**
 * Explicitly increments the node reference count and the normal reference
 * count simultaneously.
 */
53
inline void NodeReferenceCount::node_ref(void) const;

880 10 node_unref 0 4 1356 30 NodeReferenceCount::node_unref 0 1 204 201
/**
 * Explicitly decrements the node reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */
55
inline bool NodeReferenceCount::node_unref(void) const;

881 24 test_ref_count_integrity 0 4 1356 44 NodeReferenceCount::test_ref_count_integrity 0 1 205 97
/**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */
69
inline bool NodeReferenceCount::test_ref_count_integrity(void) const;

882 15 node_unref_only 0 4 1356 35 NodeReferenceCount::node_unref_only 0 1 206 186
/**
 * Decrements the node reference count without affecting the normal reference
 * count.  Intended to be called by derived classes only, presumably to
 * reimplement node_unref().
 */
60
inline void NodeReferenceCount::node_unref_only(void) const;

883 14 get_class_type 0 4 1356 34 NodeReferenceCount::get_class_type 0 1 207 0
59
static TypeHandle NodeReferenceCount::get_class_type(void);

884 8 Datagram 0 260 1357 18 Datagram::Datagram 0 3 208 209 210 130
/**
 * Constructs a datagram from an existing block of data.
 */

/**
 * Constructs a datagram from an existing block of data.
 */
212
inline Datagram::Datagram(void) = default;
inline Datagram::Datagram(void const *data, std::size_t size);
inline explicit Datagram::Datagram(vector_uchar data);
Datagram::Datagram(Datagram const &copy) = default;

885 10 operator = 0 4 1357 20 Datagram::operator = 0 1 211 0
58
void Datagram::operator =(Datagram const &copy) = default;

886 5 clear 0 6 1357 15 Datagram::clear 0 1 212 90
/**
 * Resets the datagram to empty, in preparation for building up a new
 * datagram.
 */
35
virtual void Datagram::clear(void);

887 8 dump_hex 0 4 1357 18 Datagram::dump_hex 0 1 213 111
/**
 * Writes a representation of the entire datagram contents, as a sequence of
 * hex (and ASCII) values.
 */
74
void Datagram::dump_hex(std::ostream &out, unsigned int indent = 0) const;

888 8 add_bool 0 4 1357 18 Datagram::add_bool 0 1 214 48
/**
 * Adds a boolean value to the datagram.
 */
43
inline void Datagram::add_bool(bool value);

889 8 add_int8 0 4 1357 18 Datagram::add_int8 0 1 215 55
/**
 * Adds a signed 8-bit integer to the datagram.
 */
45
inline void Datagram::add_int8(int8_t value);

890 9 add_uint8 0 4 1357 19 Datagram::add_uint8 0 1 216 58
/**
 * Adds an unsigned 8-bit integer to the datagram.
 */
47
inline void Datagram::add_uint8(uint8_t value);

891 9 add_int16 0 4 1357 19 Datagram::add_int16 0 1 217 106
// The default numeric packing is little-endian.

/**
 * Adds a signed 16-bit integer to the datagram.
 */
47
inline void Datagram::add_int16(int16_t value);

892 9 add_int32 0 4 1357 19 Datagram::add_int32 0 1 218 56
/**
 * Adds a signed 32-bit integer to the datagram.
 */
47
inline void Datagram::add_int32(int32_t value);

893 9 add_int64 0 4 1357 19 Datagram::add_int64 0 1 219 56
/**
 * Adds a signed 64-bit integer to the datagram.
 */
47
inline void Datagram::add_int64(int64_t value);

894 10 add_uint16 0 4 1357 20 Datagram::add_uint16 0 1 220 59
/**
 * Adds an unsigned 16-bit integer to the datagram.
 */
49
inline void Datagram::add_uint16(uint16_t value);

895 10 add_uint32 0 4 1357 20 Datagram::add_uint32 0 1 221 59
/**
 * Adds an unsigned 32-bit integer to the datagram.
 */
49
inline void Datagram::add_uint32(uint32_t value);

896 10 add_uint64 0 4 1357 20 Datagram::add_uint64 0 1 222 59
/**
 * Adds an unsigned 64-bit integer to the datagram.
 */
49
inline void Datagram::add_uint64(uint64_t value);

897 11 add_float32 0 4 1357 21 Datagram::add_float32 0 1 223 197
/**
 * Adds a 32-bit single-precision floating-point number to the datagram.
 * Since this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */
52
inline void Datagram::add_float32(PN_float32 value);

898 11 add_float64 0 4 1357 21 Datagram::add_float64 0 1 224 63
/**
 * Adds a 64-bit floating-point number to the datagram.
 */
52
inline void Datagram::add_float64(PN_float64 value);

899 12 add_stdfloat 0 4 1357 22 Datagram::add_stdfloat 0 1 225 105
/**
 * Adds either a 32-bit or a 64-bit floating-point number, according to
 * set_stdfloat_double().
 */
54
inline void Datagram::add_stdfloat(PN_stdfloat value);

900 12 add_be_int16 0 4 1357 22 Datagram::add_be_int16 0 1 226 136
// These functions pack numbers big-endian, in case that's desired.

/**
 * Adds a signed 16-bit big-endian integer to the datagram.
 */
50
inline void Datagram::add_be_int16(int16_t value);

901 12 add_be_int32 0 4 1357 22 Datagram::add_be_int32 0 1 227 67
/**
 * Adds a signed 32-bit big-endian integer to the datagram.
 */
50
inline void Datagram::add_be_int32(int32_t value);

902 12 add_be_int64 0 4 1357 22 Datagram::add_be_int64 0 1 228 67
/**
 * Adds a signed 64-bit big-endian integer to the datagram.
 */
50
inline void Datagram::add_be_int64(int64_t value);

903 13 add_be_uint16 0 4 1357 23 Datagram::add_be_uint16 0 1 229 70
/**
 * Adds an unsigned 16-bit big-endian integer to the datagram.
 */
52
inline void Datagram::add_be_uint16(uint16_t value);

904 13 add_be_uint32 0 4 1357 23 Datagram::add_be_uint32 0 1 230 70
/**
 * Adds an unsigned 32-bit big-endian integer to the datagram.
 */
52
inline void Datagram::add_be_uint32(uint32_t value);

905 13 add_be_uint64 0 4 1357 23 Datagram::add_be_uint64 0 1 231 70
/**
 * Adds an unsigned 64-bit big-endian integer to the datagram.
 */
52
inline void Datagram::add_be_uint64(uint64_t value);

906 14 add_be_float32 0 4 1357 24 Datagram::add_be_float32 0 1 232 94
/**
 * Adds a 32-bit single-precision big-endian floating-point number to the
 * datagram.
 */
55
inline void Datagram::add_be_float32(PN_float32 value);

907 14 add_be_float64 0 4 1357 24 Datagram::add_be_float64 0 1 233 74
/**
 * Adds a 64-bit big-endian floating-point number to the datagram.
 */
55
inline void Datagram::add_be_float64(PN_float64 value);

908 10 add_string 0 4 1357 20 Datagram::add_string 0 1 234 109
/**
 * Adds a variable-length string to the datagram.  This actually adds a count
 * followed by n bytes.
 */
57
inline void Datagram::add_string(std::string const &str);

909 12 add_string32 0 4 1357 22 Datagram::add_string32 0 1 235 116
/**
 * Adds a variable-length string to the datagram, using a 32-bit length field
 * to allow very long strings.
 */
59
inline void Datagram::add_string32(std::string const &str);

910 12 add_z_string 0 4 1357 22 Datagram::add_z_string 0 1 236 86
/**
 * Adds a variable-length string to the datagram, as a NULL-terminated string.
 */
59
inline void Datagram::add_z_string(std::string const &str);

911 16 add_fixed_string 0 4 1357 26 Datagram::add_fixed_string 0 1 237 238
/**
 * Adds a fixed-length string to the datagram.  If the string given is less
 * than the requested size, this will pad the string out with zeroes; if it is
 * greater than the requested size, this will silently truncate the string.
 */
81
inline void Datagram::add_fixed_string(std::string const &str, std::size_t size);

912 11 add_wstring 0 4 1357 21 Datagram::add_wstring 0 1 238 58
/**
 * Adds a variable-length wstring to the datagram.
 */
52
void Datagram::add_wstring(std::wstring const &str);

913 8 add_blob 0 4 1357 18 Datagram::add_blob 0 1 239 114
/**
 * Adds a variable-length binary blob to the datagram.  This actually adds a
 * count followed by n bytes.
 */
53
inline void Datagram::add_blob(vector_uchar const &);

914 10 add_blob32 0 4 1357 20 Datagram::add_blob32 0 1 240 119
/**
 * Adds a variable-length binary blob to the datagram, using a 32-bit length
 * field to allow very long blobs.
 */
55
inline void Datagram::add_blob32(vector_uchar const &);

915 9 pad_bytes 0 4 1357 19 Datagram::pad_bytes 0 1 241 67
/**
 * Adds the indicated number of zero bytes to the datagram.
 */
43
void Datagram::pad_bytes(std::size_t size);

916 11 append_data 0 4 1357 21 Datagram::append_data 0 1 242 132
/**
 * Appends some more raw data to the end of the datagram.
 */

/**
 * Appends some more raw data to the end of the datagram.
 */
124
void Datagram::append_data(void const *data, std::size_t size);
inline void Datagram::append_data(vector_uchar const &data);

917 11 get_message 0 4 1357 21 Datagram::get_message 0 1 243 51
/**
 * Returns the datagram's data as a string.
 */
51
inline PyObject *Datagram::get_message(void) const;

918 9 __bytes__ 0 4 1357 19 Datagram::__bytes__ 0 1 244 0
49
inline PyObject *Datagram::__bytes__(void) const;

919 10 __reduce__ 0 4 1357 20 Datagram::__reduce__ 0 1 245 0
43
PyObject *Datagram::__reduce__(void) const;

920 10 get_length 0 4 1357 20 Datagram::get_length 0 1 246 55
/**
 * Returns the number of bytes in the datagram.
 */
52
inline std::size_t Datagram::get_length(void) const;

921 9 set_array 0 4 1357 19 Datagram::set_array 0 1 247 199
/**
 * Replaces the data in the Datagram with the data in the indicated PTA_uchar.
 * This is assignment by reference: subsequent changes to the Datagram will
 * also change the source PTA_uchar.
 */
48
inline void Datagram::set_array(PTA_uchar data);

922 10 copy_array 0 4 1357 20 Datagram::copy_array 0 1 248 237
/**
 * Replaces the data in the Datagram with a copy of the data in the indicated
 * CPTA_uchar.  Unlike set_array(), a complete copy is made of the data;
 * subsequent changes to the Datagram will *not* change the source CPTA_uchar.
 */
50
inline void Datagram::copy_array(CPTA_uchar data);

923 9 get_array 0 4 1357 19 Datagram::get_array 0 1 249 70
/**
 * Returns a const pointer to the actual data in the Datagram.
 */
50
inline CPTA_uchar Datagram::get_array(void) const;

924 12 modify_array 0 4 1357 22 Datagram::modify_array 0 1 250 75
/**
 * Returns a modifiable pointer to the actual data in the Datagram.
 */
46
inline PTA_uchar Datagram::modify_array(void);

925 19 set_stdfloat_double 0 4 1357 29 Datagram::set_stdfloat_double 0 1 251 343
/**
 * Changes the stdfloat_double flag, which defines the operation performed by
 * add_stdfloat() and DatagramIterator::get_stdfloat().  When this is true,
 * add_stdfloat() adds a 64-bit floating-point number; when it is false, it
 * adds a 32-bit floating-point number.  The default is based on the
 * STDFLOAT_DOUBLE compilation flag.
 */
64
inline void Datagram::set_stdfloat_double(bool stdfloat_double);

926 19 get_stdfloat_double 0 4 1357 29 Datagram::get_stdfloat_double 0 1 252 72
/**
 * Returns the stdfloat_double flag.  See set_stdfloat_double().
 */
54
inline bool Datagram::get_stdfloat_double(void) const;

927 11 operator == 0 4 1357 21 Datagram::operator == 0 1 253 0
63
inline bool Datagram::operator ==(Datagram const &other) const;

928 11 operator != 0 4 1357 21 Datagram::operator != 0 1 254 0
63
inline bool Datagram::operator !=(Datagram const &other) const;

929 10 operator < 0 4 1357 20 Datagram::operator < 0 1 255 0
62
inline bool Datagram::operator <(Datagram const &other) const;

930 6 output 0 4 1357 16 Datagram::output 0 1 256 67
/**
 * Write a string representation of this instance to <out>.
 */
47
void Datagram::output(std::ostream &out) const;

931 5 write 0 4 1357 15 Datagram::write 0 1 257 67
/**
 * Write a string representation of this instance to <out>.
 */
71
void Datagram::write(std::ostream &out, unsigned int indent = 0) const;

932 14 get_class_type 0 4 1357 24 Datagram::get_class_type 0 1 258 0
49
static TypeHandle Datagram::get_class_type(void);

933 18 ~DatagramGenerator 0 518 1359 37 DatagramGenerator::~DatagramGenerator 0 0 82
/**
 * Does nothing since this is class is just the definition of an interface
 */
52
virtual DatagramGenerator::~DatagramGenerator(void);

934 12 get_datagram 0 6 1359 31 DatagramGenerator::get_datagram 0 1 259 0
65
virtual bool DatagramGenerator::get_datagram(Datagram &data) = 0;

935 13 save_datagram 0 6 1359 32 DatagramGenerator::save_datagram 0 1 260 391
/**
 * Skips over the next datagram without extracting it, but saves the relevant
 * file information in the SubfileInfo object so that its data may be read
 * later.  For non-file-based datagram generators, this may mean creating a
 * temporary file and copying the contents of the datagram to disk.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.
 */
65
virtual bool DatagramGenerator::save_datagram(SubfileInfo &info);

936 6 is_eof 0 6 1359 25 DatagramGenerator::is_eof 0 1 261 0
49
virtual bool DatagramGenerator::is_eof(void) = 0;

937 8 is_error 0 6 1359 27 DatagramGenerator::is_error 0 1 262 0
51
virtual bool DatagramGenerator::is_error(void) = 0;

938 12 get_filename 0 6 1359 31 DatagramGenerator::get_filename 0 1 263 159
/**
 * Returns the filename that provides the source for these datagrams, if any,
 * or empty string if the datagrams do not originate from a file on disk.
 */
62
virtual Filename const &DatagramGenerator::get_filename(void);

939 13 get_timestamp 0 6 1359 32 DatagramGenerator::get_timestamp 0 1 264 138
/**
 * Returns the on-disk timestamp of the file that was read, at the time it was
 * opened, if that is available, or 0 if it is not.
 */
60
virtual time_t DatagramGenerator::get_timestamp(void) const;

940 8 get_file 0 6 1359 27 DatagramGenerator::get_file 0 1 265 156
/**
 * Returns the FileReference that provides the source for these datagrams, if
 * any, or NULL if the datagrams do not originate from a file on disk.
 */
63
virtual FileReference const *DatagramGenerator::get_file(void);

941 9 get_vfile 0 6 1359 28 DatagramGenerator::get_vfile 0 1 266 153
/**
 * Returns the VirtualFile that provides the source for these datagrams, if
 * any, or NULL if the datagrams do not originate from a VirtualFile.
 */
56
virtual VirtualFile *DatagramGenerator::get_vfile(void);

942 12 get_file_pos 0 6 1359 31 DatagramGenerator::get_file_pos 0 1 267 327
/**
 * Returns the current file position within the data stream, if any, or 0 if
 * the file position is not meaningful or cannot be determined.
 *
 * For DatagramGenerators that return a meaningful file position, this will be
 * pointing to the first byte following the datagram returned after a call to
 * get_datagram().
 */
61
virtual std::streamoff DatagramGenerator::get_file_pos(void);

943 16 DatagramIterator 0 260 1360 34 DatagramIterator::DatagramIterator 0 3 268 269 270 22
/**
 *
 */

/**
 *
 */
220
inline DatagramIterator::DatagramIterator(void);
inline DatagramIterator::DatagramIterator(Datagram const &datagram, std::size_t offset = 0);
inline DatagramIterator::DatagramIterator(DatagramIterator const &) = default;

944 8 get_bool 0 4 1360 26 DatagramIterator::get_bool 0 1 271 36
/**
 * Extracts a boolean value.
 */
45
inline bool DatagramIterator::get_bool(void);

945 8 get_int8 0 4 1360 26 DatagramIterator::get_int8 0 1 272 43
/**
 * Extracts a signed 8-bit integer.
 */
47
inline int8_t DatagramIterator::get_int8(void);

946 9 get_uint8 0 4 1360 27 DatagramIterator::get_uint8 0 1 273 46
/**
 * Extracts an unsigned 8-bit integer.
 */
49
inline uint8_t DatagramIterator::get_uint8(void);

947 9 get_int16 0 4 1360 27 DatagramIterator::get_int16 0 1 274 44
/**
 * Extracts a signed 16-bit integer.
 */
49
inline int16_t DatagramIterator::get_int16(void);

948 9 get_int32 0 4 1360 27 DatagramIterator::get_int32 0 1 275 44
/**
 * Extracts a signed 32-bit integer.
 */
49
inline int32_t DatagramIterator::get_int32(void);

949 9 get_int64 0 4 1360 27 DatagramIterator::get_int64 0 1 276 44
/**
 * Extracts a signed 64-bit integer.
 */
49
inline int64_t DatagramIterator::get_int64(void);

950 10 get_uint16 0 4 1360 28 DatagramIterator::get_uint16 0 1 277 47
/**
 * Extracts an unsigned 16-bit integer.
 */
51
inline uint16_t DatagramIterator::get_uint16(void);

951 10 get_uint32 0 4 1360 28 DatagramIterator::get_uint32 0 1 278 47
/**
 * Extracts an unsigned 32-bit integer.
 */
51
inline uint32_t DatagramIterator::get_uint32(void);

952 10 get_uint64 0 4 1360 28 DatagramIterator::get_uint64 0 1 279 47
/**
 * Extracts an unsigned 64-bit integer.
 */
51
inline uint64_t DatagramIterator::get_uint64(void);

953 11 get_float32 0 4 1360 29 DatagramIterator::get_float32 0 1 280 68
/**
 * Extracts a 32-bit single-precision floating-point number.
 */
54
inline PN_float32 DatagramIterator::get_float32(void);

954 11 get_float64 0 4 1360 29 DatagramIterator::get_float64 0 1 281 51
/**
 * Extracts a 64-bit floating-point number.
 */
54
inline PN_float64 DatagramIterator::get_float64(void);

955 12 get_stdfloat 0 4 1360 30 DatagramIterator::get_stdfloat 0 1 282 119
/**
 * Extracts either a 32-bit or a 64-bit floating-point number, according to
 * Datagram::set_stdfloat_double().
 */
56
inline PN_stdfloat DatagramIterator::get_stdfloat(void);

956 12 get_be_int16 0 4 1360 30 DatagramIterator::get_be_int16 0 1 283 55
/**
 * Extracts a signed 16-bit big-endian integer.
 */
52
inline int16_t DatagramIterator::get_be_int16(void);

957 12 get_be_int32 0 4 1360 30 DatagramIterator::get_be_int32 0 1 284 55
/**
 * Extracts a signed 32-bit big-endian integer.
 */
52
inline int32_t DatagramIterator::get_be_int32(void);

958 12 get_be_int64 0 4 1360 30 DatagramIterator::get_be_int64 0 1 285 55
/**
 * Extracts a signed 64-bit big-endian integer.
 */
52
inline int64_t DatagramIterator::get_be_int64(void);

959 13 get_be_uint16 0 4 1360 31 DatagramIterator::get_be_uint16 0 1 286 58
/**
 * Extracts an unsigned 16-bit big-endian integer.
 */
54
inline uint16_t DatagramIterator::get_be_uint16(void);

960 13 get_be_uint32 0 4 1360 31 DatagramIterator::get_be_uint32 0 1 287 58
/**
 * Extracts an unsigned 32-bit big-endian integer.
 */
54
inline uint32_t DatagramIterator::get_be_uint32(void);

961 13 get_be_uint64 0 4 1360 31 DatagramIterator::get_be_uint64 0 1 288 58
/**
 * Extracts an unsigned 64-bit big-endian integer.
 */
54
inline uint64_t DatagramIterator::get_be_uint64(void);

962 14 get_be_float32 0 4 1360 32 DatagramIterator::get_be_float32 0 1 289 79
/**
 * Extracts a 32-bit big-endian single-precision floating-point number.
 */
57
inline PN_float32 DatagramIterator::get_be_float32(void);

963 14 get_be_float64 0 4 1360 32 DatagramIterator::get_be_float64 0 1 290 62
/**
 * Extracts a 64-bit big-endian floating-point number.
 */
57
inline PN_float64 DatagramIterator::get_be_float64(void);

964 10 get_string 0 4 1360 28 DatagramIterator::get_string 0 1 291 45
/**
 * Extracts a variable-length string.
 */
47
std::string DatagramIterator::get_string(void);

965 12 get_string32 0 4 1360 30 DatagramIterator::get_string32 0 1 292 72
/**
 * Extracts a variable-length string with a 32-bit length field.
 */
49
std::string DatagramIterator::get_string32(void);

966 12 get_z_string 0 4 1360 30 DatagramIterator::get_z_string 0 1 293 74
/**
 * Extracts a variable-length string, as a NULL-terminated string.
 */
49
std::string DatagramIterator::get_z_string(void);

967 16 get_fixed_string 0 4 1360 34 DatagramIterator::get_fixed_string 0 1 294 128
/**
 * Extracts a fixed-length string.  However, if a zero byte occurs within the
 * string, it marks the end of the string.
 */
65
std::string DatagramIterator::get_fixed_string(std::size_t size);

968 11 get_wstring 0 4 1360 29 DatagramIterator::get_wstring 0 1 295 75
/**
 * Extracts a variable-length wstring (with a 32-bit length field).
 */
49
std::wstring DatagramIterator::get_wstring(void);

969 8 get_blob 0 4 1360 26 DatagramIterator::get_blob 0 1 296 50
/**
 * Extracts a variable-length binary blob.
 */
53
inline vector_uchar DatagramIterator::get_blob(void);

970 10 get_blob32 0 4 1360 28 DatagramIterator::get_blob32 0 1 297 75
/**
 * Extracts a variable-length binary blob with a 32-bit size field.
 */
55
inline vector_uchar DatagramIterator::get_blob32(void);

971 10 skip_bytes 0 4 1360 28 DatagramIterator::skip_bytes 0 1 298 68
/**
 * Skips over the indicated number of bytes in the datagram.
 */
59
inline void DatagramIterator::skip_bytes(std::size_t size);

972 13 extract_bytes 0 4 1360 31 DatagramIterator::extract_bytes 0 1 299 346
/**
 * Extracts the indicated number of bytes in the datagram and returns them as
 * a string.
 */

/**
 * Extracts the indicated number of bytes in the datagram into the given
 * character buffer.  Assumes that the buffer is big enough to hold the
 * requested number of bytes.  Returns the number of bytes that were
 * successfully written.
 */
147
vector_uchar DatagramIterator::extract_bytes(std::size_t size);
std::size_t DatagramIterator::extract_bytes(unsigned char *into, std::size_t size);

973 19 get_remaining_bytes 0 4 1360 37 DatagramIterator::get_remaining_bytes 0 1 300 115
/**
 * Returns the remaining bytes in the datagram as a string, but does not
 * extract them from the iterator.
 */
70
inline vector_uchar DatagramIterator::get_remaining_bytes(void) const;

974 18 get_remaining_size 0 4 1360 36 DatagramIterator::get_remaining_size 0 1 301 49
/**
 * Return the bytes left in the datagram.
 */
68
inline std::size_t DatagramIterator::get_remaining_size(void) const;

975 12 get_datagram 0 4 1360 30 DatagramIterator::get_datagram 0 1 302 48
/**
 * Return the datagram of this iterator.
 */
66
inline Datagram const &DatagramIterator::get_datagram(void) const;

976 17 get_current_index 0 4 1360 35 DatagramIterator::get_current_index 0 1 303 100
/**
 * Returns the current position within the datagram of the next piece of data
 * to extract.
 */
67
inline std::size_t DatagramIterator::get_current_index(void) const;

977 6 output 0 4 1360 24 DatagramIterator::output 0 1 304 67
/**
 * Write a string representation of this instance to <out>.
 */
55
void DatagramIterator::output(std::ostream &out) const;

978 5 write 0 4 1360 23 DatagramIterator::write 0 1 305 67
/**
 * Write a string representation of this instance to <out>.
 */
79
void DatagramIterator::write(std::ostream &out, unsigned int indent = 0) const;

979 14 get_class_type 0 4 1360 32 DatagramIterator::get_class_type 0 1 306 0
57
static TypeHandle DatagramIterator::get_class_type(void);

980 17 ~DatagramIterator 0 516 1360 35 DatagramIterator::~DatagramIterator 0 0 0
42
DatagramIterator::~DatagramIterator(void);

981 13 ~DatagramSink 0 518 1361 27 DatagramSink::~DatagramSink 0 0 82
/**
 * Does nothing since this is class is just the definition of an interface
 */
42
virtual DatagramSink::~DatagramSink(void);

982 12 put_datagram 0 6 1361 26 DatagramSink::put_datagram 0 1 307 0
66
virtual bool DatagramSink::put_datagram(Datagram const &data) = 0;

983 13 copy_datagram 0 6 1361 27 DatagramSink::copy_datagram 0 2 308 309 713
/**
 * Copies the file data from the entire indicated file (via the vfs) as the
 * next datagram.  This is intended to support potentially very large
 * datagrams.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.  On true, fills "result" with the information that
 * references the copied file, if possible.
 */

/**
 * Copies the file data from the range of the indicated file (outside of the
 * vfs) as the next datagram.  This is intended to support potentially very
 * large datagrams.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.  On true, fills "result" with the information that
 * references the copied file, if possible.
 */
178
virtual bool DatagramSink::copy_datagram(SubfileInfo &result, Filename const &filename);
virtual bool DatagramSink::copy_datagram(SubfileInfo &result, SubfileInfo const &source);

984 8 is_error 0 6 1361 22 DatagramSink::is_error 0 1 310 0
46
virtual bool DatagramSink::is_error(void) = 0;

985 5 flush 0 6 1361 19 DatagramSink::flush 0 1 311 0
43
virtual void DatagramSink::flush(void) = 0;

986 12 get_filename 0 6 1361 26 DatagramSink::get_filename 0 1 312 159
/**
 * Returns the filename that provides the target for these datagrams, if any,
 * or empty string if the datagrams do not get written to a file on disk.
 */
57
virtual Filename const &DatagramSink::get_filename(void);

987 8 get_file 0 6 1361 22 DatagramSink::get_file 0 1 313 152
/**
 * Returns the FileReference that provides the target for these datagrams, if
 * any, or NULL if the datagrams do not written to a file on disk.
 */
58
virtual FileReference const *DatagramSink::get_file(void);

988 12 get_file_pos 0 6 1361 26 DatagramSink::get_file_pos 0 1 314 322
/**
 * Returns the current file position within the data stream, if any, or 0 if
 * the file position is not meaningful or cannot be determined.
 *
 * For DatagramSinks that return a meaningful file position, this will be
 * pointing to the first byte following the datagram returned after a call to
 * put_datagram().
 */
56
virtual std::streamoff DatagramSink::get_file_pos(void);

989 21 upcast_to_TypedObject 0 12 1367 42 TypedReferenceCount::upcast_to_TypedObject 0 1 320 46
upcast from TypedReferenceCount to TypedObject
62
TypedObject *TypedReferenceCount::upcast_to_TypedObject(void);

990 31 downcast_to_TypedReferenceCount 0 12 1358 44 TypedObject::downcast_to_TypedReferenceCount 0 0 48
downcast from TypedObject to TypedReferenceCount
72
TypedReferenceCount *TypedObject::downcast_to_TypedReferenceCount(void);

991 24 upcast_to_ReferenceCount 0 12 1367 45 TypedReferenceCount::upcast_to_ReferenceCount 0 1 321 49
upcast from TypedReferenceCount to ReferenceCount
68
ReferenceCount *TypedReferenceCount::upcast_to_ReferenceCount(void);

992 31 downcast_to_TypedReferenceCount 0 12 1352 47 ReferenceCount::downcast_to_TypedReferenceCount 0 0 51
downcast from ReferenceCount to TypedReferenceCount
75
TypedReferenceCount *ReferenceCount::downcast_to_TypedReferenceCount(void);

993 14 get_class_type 0 4 1367 35 TypedReferenceCount::get_class_type 0 1 319 0
60
static TypeHandle TypedReferenceCount::get_class_type(void);

994 20 ~TypedReferenceCount 0 516 1367 41 TypedReferenceCount::~TypedReferenceCount 0 0 0
48
TypedReferenceCount::~TypedReferenceCount(void);

995 13 FileReference 0 260 1366 28 FileReference::FileReference 0 2 315 316 10
/**
 *
 */
132
inline FileReference::FileReference(Filename const &filename);
inline FileReference::FileReference(FileReference const &) = default;

996 12 get_filename 0 4 1366 27 FileReference::get_filename 0 1 317 49
/**
 * Returns the filename of the reference.
 */
63
inline Filename const &FileReference::get_filename(void) const;

997 14 get_class_type 0 4 1366 29 FileReference::get_class_type 0 1 318 0
54
static TypeHandle FileReference::get_class_type(void);

998 14 ~FileReference 0 516 1366 29 FileReference::~FileReference 0 0 0
36
FileReference::~FileReference(void);

999 14 encrypt_string 0 1 0 14 encrypt_string 0 1 655 0
171
std::string encrypt_string(std::string const &source, std::string const &password, std::string const &algorithm = string(), int key_length = -1, int iteration_count = -1);

1000 14 decrypt_string 0 1 0 14 decrypt_string 0 1 656 0
83
std::string decrypt_string(std::string const &source, std::string const &password);

1001 12 encrypt_file 0 1 0 12 encrypt_file 0 1 657 0
181
bool encrypt_file(Filename const &source, Filename const &dest, std::string const &password, std::string const &algorithm = string(), int key_length = -1, int iteration_count = -1);

1002 12 decrypt_file 0 1 0 12 decrypt_file 0 1 658 0
93
bool decrypt_file(Filename const &source, Filename const &dest, std::string const &password);

1003 14 encrypt_stream 0 1 0 14 encrypt_stream 0 1 659 0
179
bool encrypt_stream(std::istream &source, std::ostream &dest, std::string const &password, std::string const &algorithm = string(), int key_length = -1, int iteration_count = -1);

1004 14 decrypt_stream 0 1 0 14 decrypt_stream 0 1 660 0
91
bool decrypt_stream(std::istream &source, std::ostream &dest, std::string const &password);

1005 13 error_to_text 0 1 0 13 error_to_text 0 1 661 10
/**
 *
 */
45
std::string error_to_text(ErrorUtilCode err);

1006 15 get_write_error 0 1 0 15 get_write_error 0 1 662 0
26
int get_write_error(void);

1007 19 handle_socket_error 0 1 0 19 handle_socket_error 0 1 663 10
/**
 *
 */
38
std::string handle_socket_error(void);

1008 17 get_network_error 0 1 0 17 get_network_error 0 1 664 0
28
int get_network_error(void);

1009 7 Ramfile 0 260 1371 16 Ramfile::Ramfile 0 2 322 323 10
/**
 *
 */
82
inline Ramfile::Ramfile(void);
inline Ramfile::Ramfile(Ramfile const &) = default;

1010 4 seek 0 4 1371 13 Ramfile::seek 0 1 324 130
/**
 * Moves the data pointer to the indicated byte position.  It is not an error
 * to move the pointer past the end of data.
 */
43
inline void Ramfile::seek(std::size_t pos);

1011 4 tell 0 4 1371 13 Ramfile::tell 0 1 325 106
/**
 * Returns the current data pointer position as a byte offset from the
 * beginning of the stream.
 */
45
inline std::size_t Ramfile::tell(void) const;

1012 4 read 0 4 1371 13 Ramfile::read 0 1 326 319
/**
 * Extracts and returns the indicated number of characters from the current
 * data pointer, and advances the data pointer.  If the data pointer exceeds
 * the end of the buffer, returns empty string.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's file.read() function.
 */
44
PyObject *Ramfile::read(std::size_t length);

1013 8 readline 0 4 1371 17 Ramfile::readline 0 1 327 303
/**
 * Assumes the stream represents a text file, and extracts one line up to and
 * including the trailing newline character.  Returns empty string when the
 * end of file is reached.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's file.readline() function.
 */
34
PyObject *Ramfile::readline(void);

1014 9 readlines 0 4 1371 18 Ramfile::readlines 0 1 328 0
35
PyObject *Ramfile::readlines(void);

1015 8 get_data 0 4 1371 17 Ramfile::get_data 0 1 329 101
/**
 * Returns the entire buffer contents as a string, regardless of the current
 * data pointer.
 */
40
PyObject *Ramfile::get_data(void) const;

1016 13 get_data_size 0 4 1371 22 Ramfile::get_data_size 0 1 330 58
/**
 * Returns the size of the entire buffer contents.
 */
54
inline std::size_t Ramfile::get_data_size(void) const;

1017 5 clear 0 4 1371 14 Ramfile::clear 0 1 331 47
/**
 * Empties the current buffer contents.
 */
33
inline void Ramfile::clear(void);

1018 8 ~Ramfile 0 516 1371 17 Ramfile::~Ramfile 0 0 0
24
Ramfile::~Ramfile(void);

1019 7 HashVal 0 260 1372 16 HashVal::HashVal 0 2 332 333 22
/**
 *
 */

/**
 *
 */
76
inline HashVal::HashVal(void);
inline HashVal::HashVal(HashVal const &copy);

1020 10 operator = 0 4 1372 19 HashVal::operator = 0 1 334 0
53
inline void HashVal::operator =(HashVal const &copy);

1021 11 operator == 0 4 1372 20 HashVal::operator == 0 1 335 0
61
inline bool HashVal::operator ==(HashVal const &other) const;

1022 11 operator != 0 4 1372 20 HashVal::operator != 0 1 336 0
61
inline bool HashVal::operator !=(HashVal const &other) const;

1023 10 operator < 0 4 1372 19 HashVal::operator < 0 1 337 0
60
inline bool HashVal::operator <(HashVal const &other) const;

1024 10 compare_to 0 4 1372 19 HashVal::compare_to 0 1 338 10
/**
 *
 */
59
inline int HashVal::compare_to(HashVal const &other) const;

1025 10 merge_with 0 4 1372 19 HashVal::merge_with 0 1 339 86
/**
 * Generates a new HashVal representing the xor of this one and the other one.
 */
54
inline void HashVal::merge_with(HashVal const &other);

1026 10 output_dec 0 4 1372 19 HashVal::output_dec 0 1 340 65
/**
 * Outputs the HashVal as four unsigned decimal integers.
 */
57
inline void HashVal::output_dec(std::ostream &out) const;

1027 9 input_dec 0 4 1372 18 HashVal::input_dec 0 1 341 64
/**
 * Inputs the HashVal as four unsigned decimal integers.
 */
49
inline void HashVal::input_dec(std::istream &in);

1028 10 output_hex 0 4 1372 19 HashVal::output_hex 0 1 342 64
/**
 * Outputs the HashVal as a 32-digit hexadecimal number.
 */
50
void HashVal::output_hex(std::ostream &out) const;

1029 9 input_hex 0 4 1372 18 HashVal::input_hex 0 1 343 63
/**
 * Inputs the HashVal as a 32-digit hexadecimal number.
 */
42
void HashVal::input_hex(std::istream &in);

1030 13 output_binary 0 4 1372 22 HashVal::output_binary 0 1 344 128
/**
 * Outputs the HashVal as a binary stream of bytes in order.  This is not the
 * same order generated by write_stream().
 */
53
void HashVal::output_binary(std::ostream &out) const;

1031 12 input_binary 0 4 1372 21 HashVal::input_binary 0 1 345 125
/**
 * Inputs the HashVal as a binary stream of bytes in order.  This is not the
 * same order expected by read_stream().
 */
45
void HashVal::input_binary(std::istream &in);

1032 6 output 0 4 1372 15 HashVal::output 0 1 346 10
/**
 *
 */
53
inline void HashVal::output(std::ostream &out) const;

1033 6 as_dec 0 4 1372 15 HashVal::as_dec 0 1 347 69
/**
 * Returns the HashVal as a string with four decimal numbers.
 */
40
std::string HashVal::as_dec(void) const;

1034 12 set_from_dec 0 4 1372 21 HashVal::set_from_dec 0 1 348 112
/**
 * Sets the HashVal from a string with four decimal numbers.  Returns true if
 * valid, false otherwise.
 */
52
bool HashVal::set_from_dec(std::string const &text);

1035 6 as_hex 0 4 1372 15 HashVal::as_hex 0 1 349 63
/**
 * Returns the HashVal as a 32-byte hexadecimal string.
 */
40
std::string HashVal::as_hex(void) const;

1036 12 set_from_hex 0 4 1372 21 HashVal::set_from_hex 0 1 350 111
/**
 * Sets the HashVal from a 32-byte hexademical string.  Returns true if
 * successful, false otherwise.
 */
52
bool HashVal::set_from_hex(std::string const &text);

1037 6 as_bin 0 4 1372 15 HashVal::as_bin 0 1 351 58
/**
 * Returns the HashVal as a 16-byte binary string.
 */
41
vector_uchar HashVal::as_bin(void) const;

1038 12 set_from_bin 0 4 1372 21 HashVal::set_from_bin 0 1 352 106
/**
 * Sets the HashVal from a 16-byte binary string.  Returns true if successful,
 * false otherwise.
 */
53
bool HashVal::set_from_bin(vector_uchar const &text);

1039 14 write_datagram 0 4 1372 23 HashVal::write_datagram 0 1 353 10
/**
 *
 */
65
inline void HashVal::write_datagram(Datagram &destination) const;

1040 13 read_datagram 0 4 1372 22 HashVal::read_datagram 0 1 354 10
/**
 *
 */
61
inline void HashVal::read_datagram(DatagramIterator &source);

1041 12 write_stream 0 4 1372 21 HashVal::write_stream 0 1 355 10
/**
 *
 */
67
inline void HashVal::write_stream(StreamWriter &destination) const;

1042 11 read_stream 0 4 1372 20 HashVal::read_stream 0 1 356 10
/**
 *
 */
55
inline void HashVal::read_stream(StreamReader &source);

1043 9 hash_file 0 4 1372 18 HashVal::hash_file 0 1 357 232
/**
 * Generates the hash value from the indicated file.  Returns true on success,
 * false if the file cannot be read.  This method is only defined if we have
 * the OpenSSL library (which provides md5 functionality) available.
 */
50
bool HashVal::hash_file(Filename const &filename);

1044 11 hash_stream 0 4 1372 20 HashVal::hash_stream 0 1 358 232
/**
 * Generates the hash value from the indicated file.  Returns true on success,
 * false if the file cannot be read.  This method is only defined if we have
 * the OpenSSL library (which provides md5 functionality) available.
 */
48
bool HashVal::hash_stream(std::istream &stream);

1045 12 hash_ramfile 0 4 1372 21 HashVal::hash_ramfile 0 1 359 178
/**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */
58
inline void HashVal::hash_ramfile(Ramfile const &ramfile);

1046 11 hash_string 0 4 1372 20 HashVal::hash_string 0 1 360 178
/**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */
58
inline void HashVal::hash_string(std::string const &data);

1047 10 hash_bytes 0 4 1372 19 HashVal::hash_bytes 0 1 361 178
/**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */
58
inline void HashVal::hash_bytes(vector_uchar const &data);

1048 11 hash_buffer 0 4 1372 20 HashVal::hash_buffer 0 1 362 178
/**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */
58
void HashVal::hash_buffer(char const *buffer, int length);

1049 8 ~HashVal 0 516 1372 17 HashVal::~HashVal 0 0 0
24
HashVal::~HashVal(void);

1050 19 MemoryUsagePointers 0 260 1373 40 MemoryUsagePointers::MemoryUsagePointers 0 2 363 364 10
/**
 *
 */
135
MemoryUsagePointers::MemoryUsagePointers(void);
inline MemoryUsagePointers::MemoryUsagePointers(MemoryUsagePointers const &) = default;

1051 20 ~MemoryUsagePointers 0 516 1373 41 MemoryUsagePointers::~MemoryUsagePointers 0 0 10
/**
 *
 */
48
MemoryUsagePointers::~MemoryUsagePointers(void);

1052 16 get_num_pointers 0 4 1373 37 MemoryUsagePointers::get_num_pointers 0 1 365 53
/**
 * Returns the number of pointers in the set.
 */
62
std::size_t MemoryUsagePointers::get_num_pointers(void) const;

1053 11 get_pointer 0 4 1373 32 MemoryUsagePointers::get_pointer 0 1 366 46
/**
 * Returns the nth pointer of the set.
 */
70
ReferenceCount *MemoryUsagePointers::get_pointer(std::size_t n) const;

1054 17 get_typed_pointer 0 4 1373 38 MemoryUsagePointers::get_typed_pointer 0 1 367 175
/**
 * Returns the nth pointer of the set, typecast to a TypedObject if possible.
 * If the pointer is not a TypedObject or if the cast cannot be made, returns
 * nullptr.
 */
73
TypedObject *MemoryUsagePointers::get_typed_pointer(std::size_t n) const;

1055 8 get_type 0 4 1373 29 MemoryUsagePointers::get_type 0 1 368 70
/**
 * Returns the actual type of the nth pointer, if it is known.
 */
62
TypeHandle MemoryUsagePointers::get_type(std::size_t n) const;

1056 13 get_type_name 0 4 1373 34 MemoryUsagePointers::get_type_name 0 1 369 68
/**
 * Returns the type name of the nth pointer, if it is known.
 */
68
std::string MemoryUsagePointers::get_type_name(std::size_t n) const;

1057 7 get_age 0 4 1373 28 MemoryUsagePointers::get_age 0 1 370 197
/**
 * Returns the age of the nth pointer: the number of seconds elapsed between
 * the time it was allocated and the time it was added to this set via a call
 * to MemoryUsage::get_pointers().
 */
57
double MemoryUsagePointers::get_age(std::size_t n) const;

1058 18 get_python_pointer 0 4 1373 39 MemoryUsagePointers::get_python_pointer 0 1 371 0
71
PyObject *MemoryUsagePointers::get_python_pointer(std::size_t n) const;

1059 5 clear 0 4 1373 26 MemoryUsagePointers::clear 0 1 372 39
/**
 * Empties the set of pointers.
 */
38
void MemoryUsagePointers::clear(void);

1060 6 output 0 4 1373 27 MemoryUsagePointers::output 0 1 373 10
/**
 *
 */
58
void MemoryUsagePointers::output(std::ostream &out) const;

1061 10 ISubStream 0 260 1374 22 ISubStream::ISubStream 0 2 374 375 22
/**
 *
 */

/**
 *
 */
142
inline ISubStream::ISubStream(void);
inline explicit ISubStream::ISubStream(IStreamWrapper *source, std::streamoff start, std::streamoff end);

1062 4 open 0 4 1374 16 ISubStream::open 0 1 376 391
/**
 * Starts the SubStream reading from the indicated source, with the first
 * character being the character at position "start" within the source, for
 * end - start total characters.  The character at "end" within the source
 * will never be read; this will appear to be EOF.
 *
 * If end is zero, it indicates that the ISubStream will continue until the
 * end of the source stream.
 */
102
inline ISubStream &ISubStream::open(IStreamWrapper *source, std::streamoff start, std::streamoff end);

1063 5 close 0 4 1374 17 ISubStream::close 0 1 377 92
/**
 * Resets the SubStream to empty, but does not actually close the source
 * istream.
 */
43
inline ISubStream &ISubStream::close(void);

1064 11 ~ISubStream 0 516 1374 23 ISubStream::~ISubStream 0 0 0
30
ISubStream::~ISubStream(void);

1065 10 OSubStream 0 260 1376 22 OSubStream::OSubStream 0 2 378 379 22
/**
 *
 */

/**
 *
 */
161
inline OSubStream::OSubStream(void);
inline explicit OSubStream::OSubStream(OStreamWrapper *dest, std::streamoff start, std::streamoff end, bool append = false);

1066 4 open 0 4 1376 16 OSubStream::open 0 1 380 383
/**
 * Starts the SubStream reading from the indicated dest, with the first
 * character being the character at position "start" within the dest, for end
 * - start total characters.  The character at "end" within the dest will
 * never be read; this will appear to be EOF.
 *
 * If end is zero, it indicates that the OSubStream will continue until the
 * end of the dest stream.
 */
121
inline OSubStream &OSubStream::open(OStreamWrapper *dest, std::streamoff start, std::streamoff end, bool append = false);

1067 5 close 0 4 1376 17 OSubStream::close 0 1 381 90
/**
 * Resets the SubStream to empty, but does not actually close the dest
 * ostream.
 */
43
inline OSubStream &OSubStream::close(void);

1068 11 ~OSubStream 0 516 1376 23 OSubStream::~OSubStream 0 0 0
30
OSubStream::~OSubStream(void);

1069 9 SubStream 0 260 1378 20 SubStream::SubStream 0 2 382 383 22
/**
 *
 */

/**
 *
 */
158
inline SubStream::SubStream(void);
inline explicit SubStream::SubStream(StreamWrapper *nested, std::streamoff start, std::streamoff end, bool append = false);

1070 4 open 0 4 1378 15 SubStream::open 0 1 384 278
/**
 * Starts the SubStream reading and writing from the indicated nested stream,
 * within the indicated range.  "end" is the first character outside of the
 * range.
 *
 * If end is zero, it indicates that the SubStream will continue until the end
 * of the nested stream.
 */
120
inline SubStream &SubStream::open(StreamWrapper *nested, std::streamoff start, std::streamoff end, bool append = false);

1071 5 close 0 4 1378 16 SubStream::close 0 1 385 92
/**
 * Resets the SubStream to empty, but does not actually close the nested
 * ostream.
 */
41
inline SubStream &SubStream::close(void);

1072 10 ~SubStream 0 516 1378 21 SubStream::~SubStream 0 0 0
28
SubStream::~SubStream(void);

1073 9 Multifile 0 260 1380 20 Multifile::Multifile 0 1 386 10
/**
 *
 */
27
Multifile::Multifile(void);

1074 9 open_read 0 4 1380 20 Multifile::open_read 0 2 387 388 687
/**
 * Opens the named Multifile on disk for reading.  The Multifile index is read
 * in, and the list of subfiles becomes available; individual subfiles may
 * then be extracted or read, but the list of subfiles may not be modified.
 *
 * Also see the version of open_read() which accepts an istream.  Returns true
 * on success, false on failure.
 */

/**
 * Opens an anonymous Multifile for reading using an istream.  There must be
 * seek functionality via seekg() and tellg() on the istream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */
214
bool Multifile::open_read(Filename const &multifile_name, std::streamoff const &offset = 0);
bool Multifile::open_read(IStreamWrapper *multifile_stream, bool owns_pointer = false, std::streamoff const &offset = 0);

1075 10 open_write 0 4 1380 21 Multifile::open_write 0 2 389 390 755
/**
 * Opens the named Multifile on disk for writing.  If there already exists a
 * file by that name, it is truncated.  The Multifile is then prepared for
 * accepting a brand new set of subfiles, which will be written to the
 * indicated filename.  Individual subfiles may not be extracted or read.
 *
 * Also see the version of open_write() which accepts an ostream.  Returns
 * true on success, false on failure.
 */

/**
 * Opens an anonymous Multifile for writing using an ostream.  There must be
 * seek functionality via seekp() and tellp() on the pstream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */
146
bool Multifile::open_write(Filename const &multifile_name);
bool Multifile::open_write(std::ostream *multifile_stream, bool owns_pointer = false);

1076 15 open_read_write 0 4 1380 26 Multifile::open_read_write 0 2 391 392 727
/**
 * Opens the named Multifile on disk for reading and writing.  If there
 * already exists a file by that name, its index is read.  Subfiles may be
 * added or removed, and the resulting changes will be written to the named
 * file.
 *
 * Also see the version of open_read_write() which accepts an iostream.
 * Returns true on success, false on failure.
 */

/**
 * Opens an anonymous Multifile for reading and writing using an iostream.
 * There must be seek functionality via seekg()/seekp() and tellg()/tellp() on
 * the iostream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */
157
bool Multifile::open_read_write(Filename const &multifile_name);
bool Multifile::open_read_write(std::iostream *multifile_stream, bool owns_pointer = false);

1077 5 close 0 4 1380 16 Multifile::close 0 1 393 169
/**
 * Closes the Multifile if it is open.  All changes are flushed to disk, and
 * the file becomes invalid for further operations until the next call to
 * open().
 */
28
void Multifile::close(void);

1078 18 get_multifile_name 0 4 1380 29 Multifile::get_multifile_name 0 1 394 69
/**
 * Returns the filename of the Multifile, if it is available.
 */
65
inline Filename const &Multifile::get_multifile_name(void) const;

1079 18 set_multifile_name 0 4 1380 29 Multifile::set_multifile_name 0 1 395 209
/**
 * Replaces the filename of the Multifile.  This is primarily used for
 * documentation purposes only; changing this name does not open the indicated
 * file.  See open_read() or open_write() for that.
 */
74
inline void Multifile::set_multifile_name(Filename const &multifile_name);

1080 13 is_read_valid 0 4 1380 24 Multifile::is_read_valid 0 1 396 154
/**
 * Returns true if the Multifile has been opened for read mode and there have
 * been no errors, and individual Subfile contents may be extracted.
 */
49
inline bool Multifile::is_read_valid(void) const;

1081 14 is_write_valid 0 4 1380 25 Multifile::is_write_valid 0 1 397 162
/**
 * Returns true if the Multifile has been opened for write mode and there have
 * been no errors, and Subfiles may be added or removed from the Multifile.
 */
50
inline bool Multifile::is_write_valid(void) const;

1082 12 needs_repack 0 4 1380 23 Multifile::needs_repack 0 1 398 118
/**
 * Returns true if the Multifile index is suboptimal and should be repacked.
 * Call repack() to achieve this.
 */
48
inline bool Multifile::needs_repack(void) const;

1083 13 get_timestamp 0 4 1380 24 Multifile::get_timestamp 0 1 399 299
/**
 * Returns the modification timestamp of the overall Multifile.  This
 * indicates the most recent date at which subfiles were added or removed from
 * the Multifile.  Note that it is logically possible for an individual
 * subfile to have a more recent timestamp than the overall timestamp.
 */
51
inline time_t Multifile::get_timestamp(void) const;

1084 13 set_timestamp 0 4 1380 24 Multifile::set_timestamp 0 1 400 206
/**
 * Changes the overall modification timestamp of the multifile.  Note that this
 * will be reset to the current time every time you modify a subfile.
 * Only set this if you know what you are doing!
 */
55
inline void Multifile::set_timestamp(time_t timestamp);

1085 20 set_record_timestamp 0 4 1380 31 Multifile::set_record_timestamp 0 1 401 529
/**
 * Sets the flag indicating whether timestamps should be recorded within the
 * Multifile or not.  The default is true, indicating the Multifile will
 * record timestamps for the overall file and also for each subfile.
 *
 * If this is false, the Multifile will not record timestamps internally.  In
 * this case, the return value from get_timestamp() or get_subfile_timestamp()
 * will be estimations.
 *
 * You may want to set this false to minimize the bitwise difference between
 * independently-generated Multifiles.
 */
67
inline void Multifile::set_record_timestamp(bool record_timestamp);

1086 20 get_record_timestamp 0 4 1380 31 Multifile::get_record_timestamp 0 1 402 137
/**
 * Returns the flag indicating whether timestamps should be recorded within
 * the Multifile or not.  See set_record_timestamp().
 */
56
inline bool Multifile::get_record_timestamp(void) const;

1087 16 set_scale_factor 0 4 1380 27 Multifile::set_scale_factor 0 1 403 782
/**
 * Changes the internal scale factor for this Multifile.
 *
 * This is normally 1, but it may be set to any arbitrary value (greater than
 * zero) to support Multifile archives that exceed 4GB, if necessary.
 * (Individual subfiles may still not exceed 4GB.)
 *
 * All addresses within the file are rounded up to the next multiple of
 * _scale_factor, and zeros are written to the file to fill the resulting
 * gaps.  Then the address is divided by _scale_factor and written out as a
 * 32-bit integer.  Thus, setting a scale factor of 2 supports up to 8GB
 * files, 3 supports 12GB files, etc.
 *
 * Calling this function on an already-existing Multifile will have no
 * immediate effect until a future call to repack() or close() (or until the
 * Multifile is destructed).
 */
59
void Multifile::set_scale_factor(std::size_t scale_factor);

1088 16 get_scale_factor 0 4 1380 27 Multifile::get_scale_factor 0 1 404 92
/**
 * Returns the internal scale factor for this Multifile.  See
 * set_scale_factor().
 */
59
inline std::size_t Multifile::get_scale_factor(void) const;

1089 19 set_encryption_flag 0 4 1380 30 Multifile::set_encryption_flag 0 1 405 461
/**
 * Sets the flag indicating whether subsequently-added subfiles should be
 * encrypted before writing them to the multifile.  If true, subfiles will be
 * encrypted; if false (the default), they will be written without encryption.
 *
 * When true, subfiles will be encrypted with the password specified by
 * set_encryption_password().  It is possible to apply a different password to
 * different files, but the resulting file can't be mounted via VFS.
 */
54
inline void Multifile::set_encryption_flag(bool flag);

1090 19 get_encryption_flag 0 4 1380 30 Multifile::get_encryption_flag 0 1 406 163
/**
 * Returns the flag indicating whether subsequently-added subfiles should be
 * encrypted before writing them to the multifile.  See set_encryption_flag().
 */
55
inline bool Multifile::get_encryption_flag(void) const;

1091 23 set_encryption_password 0 4 1380 34 Multifile::set_encryption_password 0 1 407 369
/**
 * Specifies the password that will be used to encrypt subfiles subsequently
 * added to the multifile, if the encryption flag is also set true (see
 * set_encryption_flag()).
 *
 * It is possible to apply a different password to different files, but the
 * resulting file can't be mounted via VFS.  Changing this value may cause an
 * implicit call to flush().
 */
87
inline void Multifile::set_encryption_password(std::string const &encryption_password);

1092 23 get_encryption_password 0 4 1380 34 Multifile::get_encryption_password 0 1 408 141
/**
 * Returns the password that will be used to encrypt subfiles subsequently
 * added to the multifile.  See set_encryption_password().
 */
73
inline std::string const &Multifile::get_encryption_password(void) const;

1093 24 set_encryption_algorithm 0 4 1380 35 Multifile::set_encryption_algorithm 0 1 409 659
/**
 * Specifies the encryption algorithm that should be used for future calls to
 * add_subfile().  The default is whatever is specified by the encryption-
 * algorithm config variable.  The complete set of available algorithms is
 * defined by the current version of OpenSSL.
 *
 * If an invalid algorithm is specified, there is no immediate error return
 * code, but flush() will fail and the file will be invalid.
 *
 * It is possible to apply a different encryption algorithm to different
 * files, and unlike the password, this does not interfere with mounting the
 * multifile via VFS.  Changing this value may cause an implicit call to
 * flush().
 */
89
inline void Multifile::set_encryption_algorithm(std::string const &encryption_algorithm);

1094 24 get_encryption_algorithm 0 4 1380 35 Multifile::get_encryption_algorithm 0 1 410 96
/**
 * Returns the encryption algorithm that was specified by
 * set_encryption_algorithm().
 */
74
inline std::string const &Multifile::get_encryption_algorithm(void) const;

1095 25 set_encryption_key_length 0 4 1380 36 Multifile::set_encryption_key_length 0 1 411 611
/**
 * Specifies the length of the key, in bits, that should be used to encrypt
 * the stream in future calls to add_subfile().  The default is whatever is
 * specified by the encryption-key-length config variable.
 *
 * If an invalid key_length for the chosen algorithm is specified, there is no
 * immediate error return code, but flush() will fail and the file will be
 * invalid.
 *
 * It is possible to apply a different key length to different files, and
 * unlike the password, this does not interfere with mounting the multifile
 * via VFS. Changing this value may cause an implicit call to flush().
 */
76
inline void Multifile::set_encryption_key_length(int encryption_key_length);

1096 25 get_encryption_key_length 0 4 1380 36 Multifile::get_encryption_key_length 0 1 412 108
/**
 * Returns the encryption key length, in bits, that was specified by
 * set_encryption_key_length().
 */
60
inline int Multifile::get_encryption_key_length(void) const;

1097 30 set_encryption_iteration_count 0 4 1380 41 Multifile::set_encryption_iteration_count 0 1 413 740
/**
 * Specifies the number of times to repeatedly hash the key before writing it
 * to the stream in future calls to add_subfile().  Its purpose is to make it
 * computationally more expensive for an attacker to search the key space
 * exhaustively.  This should be a multiple of 1,000 and should not exceed
 * about 65 million; the value 0 indicates just one application of the hashing
 * algorithm.
 *
 * The default is whatever is specified by the multifile-encryption-iteration-
 * count config variable.
 *
 * It is possible to apply a different iteration count to different files, and
 * unlike the password, this does not interfere with mounting the multifile
 * via VFS.  Changing this value causes an implicit call to flush().
 */
86
inline void Multifile::set_encryption_iteration_count(int encryption_iteration_count);

1098 30 get_encryption_iteration_count 0 4 1380 41 Multifile::get_encryption_iteration_count 0 1 414 84
/**
 * Returns the value that was specified by set_encryption_iteration_count().
 */
65
inline int Multifile::get_encryption_iteration_count(void) const;

1099 11 add_subfile 0 4 1380 22 Multifile::add_subfile 0 2 415 416 1397
/**
 * Adds a file on disk as a subfile to the Multifile.  The file named by
 * filename will be read and added to the Multifile at the next call to
 * flush().  If there already exists a subfile with the indicated name, it is
 * replaced without examining its contents (but see also update_subfile).
 *
 * Either Filename:::set_binary() or set_text() must have been called
 * previously to specify the nature of the source file.  If set_text() was
 * called, the text flag will be set on the subfile.
 *
 * Returns the subfile name on success (it might have been modified slightly),
 * or empty string on failure.
 */

/**
 * Adds a file from a stream as a subfile to the Multifile.  The indicated
 * istream will be read and its contents added to the Multifile at the next
 * call to flush(). The file will be added as a binary subfile.
 *
 * Note that the istream must remain untouched and unused by any other code
 * until flush() is called.  At that time, the Multifile will read the entire
 * contents of the istream from the current file position to the end of the
 * file.  Subsequently, the Multifile will *not* close or delete the istream.
 * It is the caller's responsibility to ensure that the istream pointer does
 * not destruct during the lifetime of the Multifile.
 *
 * Returns the subfile name on success (it might have been modified slightly),
 * or empty string on failure.
 */
237
std::string Multifile::add_subfile(std::string const &subfile_name, Filename const &filename, int compression_level);
std::string Multifile::add_subfile(std::string const &subfile_name, std::istream *subfile_data, int compression_level);

1100 14 update_subfile 0 4 1380 25 Multifile::update_subfile 0 1 417 451
/**
 * Adds a file on disk to the subfile.  If a subfile already exists with the
 * same name, its contents are compared byte-for-byte to the disk file, and it
 * is replaced only if it is different; otherwise, the multifile is left
 * unchanged.
 *
 * Either Filename:::set_binary() or set_text() must have been called
 * previously to specify the nature of the source file.  If set_text() was
 * called, the text flag will be set on the subfile.
 */
120
std::string Multifile::update_subfile(std::string const &subfile_name, Filename const &filename, int compression_level);

1101 13 add_signature 0 4 1380 24 Multifile::add_signature 0 2 418 419 3062
/**
 * Adds a new signature to the Multifile.  This signature associates the
 * indicated certificate with the current contents of the Multifile.  When the
 * Multifile is read later, the signature will still be present only if the
 * Multifile is unchanged; any subsequent changes to the Multifile will
 * automatically invalidate and remove the signature.
 *
 * The chain filename may be empty if the certificate does not require an
 * authenticating certificate chain (e.g.  because it is self-signed).
 *
 * The specified private key must match the certificate, and the Multifile
 * must be open in read-write mode.  The private key is only used for
 * generating the signature; it is not written to the Multifile and cannot be
 * retrieved from the Multifile later.  (However, the certificate *can* be
 * retrieved from the Multifile later, to identify the entity that created the
 * signature.)
 *
 * This implicitly causes a repack() operation if one is needed.  Returns true
 * on success, false on failure.
 *
 * This flavor of add_signature() reads the certificate and private key from a
 * PEM-formatted file, for instance as generated by the openssl command.  If
 * the private key file is password-encrypted, the third parameter will be
 * used as the password to decrypt it.
 */

/**
 * Adds a new signature to the Multifile.  This signature associates the
 * indicated certificate with the current contents of the Multifile.  When the
 * Multifile is read later, the signature will still be present only if the
 * Multifile is unchanged; any subsequent changes to the Multifile will
 * automatically invalidate and remove the signature.
 *
 * This flavor of add_signature() reads the certificate, private key, and
 * certificate chain from the same PEM-formatted file.  It takes the first
 * private key found as the intended key, and then uses the first certificate
 * found that matches that key as the signing certificate.  Any other
 * certificates in the file are taken to be part of the chain.
 */

/**
 * Adds a new signature to the Multifile.  This signature associates the
 * indicated certificate with the current contents of the Multifile.  When the
 * Multifile is read later, the signature will still be present only if the
 * Multifile is unchanged; any subsequent changes to the Multifile will
 * automatically invalidate and remove the signature.
 *
 * The signature certificate is the first certificate on the CertChain object.
 * Any remaining certificates are support certificates to authenticate the
 * first one.
 *
 * The specified private key must match the certificate, and the Multifile
 * must be open in read-write mode.  The private key is only used for
 * generating the signature; it is not written to the Multifile and cannot be
 * retrieved from the Multifile later.  (However, the certificate *can* be
 * retrieved from the Multifile later, to identify the entity that created the
 * signature.)
 *
 * This implicitly causes a repack() operation if one is needed.  Returns true
 * on success, false on failure.
 */
230
bool Multifile::add_signature(Filename const &certificate, Filename const &chain, Filename const &pkey, std::string const &password = "");
bool Multifile::add_signature(Filename const &composite, std::string const &password = "");

1102 18 get_num_signatures 0 4 1380 29 Multifile::get_num_signatures 0 1 420 555
/**
 * Returns the number of matching signatures found on the Multifile.  These
 * signatures may be iterated via get_signature() and related methods.
 *
 * A signature on this list is guaranteed to match the Multifile contents,
 * proving that the Multifile has been unmodified since the signature was
 * applied.  However, this does not guarantee that the certificate itself is
 * actually from who it says it is from; only that it matches the Multifile
 * contents.  See validate_signature_certificate() to authenticate a
 * particular certificate.
 */
46
int Multifile::get_num_signatures(void) const;

1103 26 get_signature_subject_name 0 4 1380 37 Multifile::get_signature_subject_name 0 1 421 352
/**
 * Returns the "subject name" for the nth signature found on the Multifile.
 * This is a string formatted according to RFC2253 that should more-or-less
 * identify a particular certificate; when paired with the public key (see
 * get_signature_public_key()), it can uniquely identify a certificate.  See
 * the comments in get_num_signatures().
 */
63
std::string Multifile::get_signature_subject_name(int n) const;

1104 27 get_signature_friendly_name 0 4 1380 38 Multifile::get_signature_friendly_name 0 1 422 299
/**
 * Returns a "friendly name" for the nth signature found on the Multifile.
 * This attempts to extract out the most meaningful part of the subject name.
 * It returns the emailAddress, if it is defined; otherwise, it returns the
 * commonName.
 *
 * See the comments in get_num_signatures().
 */
64
std::string Multifile::get_signature_friendly_name(int n) const;

1105 24 get_signature_public_key 0 4 1380 35 Multifile::get_signature_public_key 0 1 423 379
/**
 * Returns the public key used for the nth signature found on the Multifile.
 * This is encoded in DER form and returned as a string of hex digits.
 *
 * This can be used, in conjunction with the subject name (see
 * get_signature_subject_name()), to uniquely identify a particular
 * certificate and its subsequent reissues.  See the comments in
 * get_num_signatures().
 */
61
std::string Multifile::get_signature_public_key(int n) const;

1106 27 print_signature_certificate 0 4 1380 38 Multifile::print_signature_certificate 0 1 424 158
/**
 * Writes the certificate for the nth signature, in user-readable verbose
 * form, to the indicated stream.  See the comments in get_num_signatures().
 */
76
void Multifile::print_signature_certificate(int n, std::ostream &out) const;

1107 27 write_signature_certificate 0 4 1380 38 Multifile::write_signature_certificate 0 1 425 140
/**
 * Writes the certificate for the nth signature, in PEM form, to the indicated
 * stream.  See the comments in get_num_signatures().
 */
76
void Multifile::write_signature_certificate(int n, std::ostream &out) const;

1108 30 validate_signature_certificate 0 4 1380 41 Multifile::validate_signature_certificate 0 1 426 289
/**
 * Checks that the certificate used for the nth signature is a valid,
 * authorized certificate with some known certificate authority.  Returns 0 if
 * it is valid, -1 if there is some error, or the corresponding OpenSSL error
 * code if it is invalid, out-of-date, or self-signed.
 */
59
int Multifile::validate_signature_certificate(int n) const;

1109 5 flush 0 4 1380 16 Multifile::flush 0 1 427 691
/**
 * Writes all contents of the Multifile to disk.  Until flush() is called,
 * add_subfile() and remove_subfile() do not actually do anything to disk.  At
 * this point, all of the recently-added subfiles are read and their contents
 * are added to the end of the Multifile, and the recently-removed subfiles
 * are marked gone from the Multifile.
 *
 * This may result in a suboptimal index.  To guarantee that the index is
 * written at the beginning of the file, call repack() instead of flush().
 *
 * It is not necessary to call flush() explicitly unless you are concerned
 * about reading the recently-added subfiles immediately.
 *
 * Returns true on success, false on failure.
 */
28
bool Multifile::flush(void);

1110 6 repack 0 4 1380 17 Multifile::repack 0 1 428 587
/**
 * Forces a complete rewrite of the Multifile and all of its contents, so that
 * its index will appear at the beginning of the file with all of the subfiles
 * listed in alphabetical order.  This is considered optimal for reading, and
 * is the standard configuration; but it is not essential to do this.
 *
 * It is only valid to call this if the Multifile was opened using
 * open_read_write() and an explicit filename, rather than an iostream.  Also,
 * we must have write permission to the directory containing the Multifile.
 *
 * Returns true on success, false on failure.
 */
29
bool Multifile::repack(void);

1111 16 get_num_subfiles 0 4 1380 27 Multifile::get_num_subfiles 0 1 429 169
/**
 * Returns the number of subfiles within the Multifile.  The subfiles may be
 * accessed in alphabetical order by iterating through [0 ..
 * get_num_subfiles()).
 */
44
int Multifile::get_num_subfiles(void) const;

1112 12 find_subfile 0 4 1380 23 Multifile::find_subfile 0 1 430 127
/**
 * Returns the index of the subfile with the indicated name, or -1 if the
 * named subfile is not within the Multifile.
 */
67
int Multifile::find_subfile(std::string const &subfile_name) const;

1113 13 has_directory 0 4 1380 24 Multifile::has_directory 0 1 431 203
/**
 * Returns true if the indicated subfile name is the directory prefix to one
 * or more files within the Multifile.  That is, the Multifile contains at
 * least one file named "subfile_name/...".
 */
69
bool Multifile::has_directory(std::string const &subfile_name) const;

1114 14 scan_directory 0 4 1380 25 Multifile::scan_directory 0 1 432 420
/**
 * Considers subfile_name to be the name of a subdirectory within the
 * Multifile, but not a file itself; fills the given vector up with the sorted
 * list of subdirectories or files within the named directory.
 *
 * Note that directories do not exist explicitly within a Multifile; this just
 * checks for the existence of files with the given initial prefix.
 *
 * Returns true if successful, false otherwise.
 */
95
bool Multifile::scan_directory(vector_string &contents, std::string const &subfile_name) const;

1115 14 remove_subfile 0 4 1380 25 Multifile::remove_subfile 0 2 433 434 912
/**
 * Removes the named subfile from the Multifile, if it exists; returns true if
 * successfully removed, or false if it did not exist in the first place.  The
 * file will not actually be removed from the disk until the next call to
 * flush().
 *
 * Note that this does not actually remove the data from the indicated
 * subfile; it simply removes it from the index.  The Multifile will not be
 * reduced in size after this operation, until the next call to repack().
 */

/**
 * Removes the nth subfile from the Multifile.  This will cause all subsequent
 * index numbers to decrease by one.  The file will not actually be removed
 * from the disk until the next call to flush().
 *
 * Note that this does not actually remove the data from the indicated
 * subfile; it simply removes it from the index.  The Multifile will not be
 * reduced in size after this operation, until the next call to repack().
 */
114
void Multifile::remove_subfile(int index);
inline bool Multifile::remove_subfile(std::string const &subfile_name);

1116 16 get_subfile_name 0 4 1380 27 Multifile::get_subfile_name 0 1 435 47
/**
 * Returns the name of the nth subfile.
 */
64
std::string const &Multifile::get_subfile_name(int index) const;

1117 18 get_subfile_length 0 4 1380 29 Multifile::get_subfile_length 0 1 436 170
/**
 * Returns the uncompressed data length of the nth subfile.  This might return
 * 0 if the subfile has recently been added and flush() has not yet been
 * called.
 */
59
std::size_t Multifile::get_subfile_length(int index) const;

1118 21 get_subfile_timestamp 0 4 1380 32 Multifile::get_subfile_timestamp 0 1 437 270
/**
 * Returns the modification time of the nth subfile.  If this is called on an
 * older .mf file, which did not store individual timestamps in the file (or
 * if get_record_timestamp() is false), this will return the modification time
 * of the overall multifile.
 */
57
time_t Multifile::get_subfile_timestamp(int index) const;

1119 21 is_subfile_compressed 0 4 1380 32 Multifile::is_subfile_compressed 0 1 438 120
/**
 * Returns true if the indicated subfile has been compressed when stored
 * within the archive, false otherwise.
 */
55
bool Multifile::is_subfile_compressed(int index) const;

1120 20 is_subfile_encrypted 0 4 1380 31 Multifile::is_subfile_encrypted 0 1 439 119
/**
 * Returns true if the indicated subfile has been encrypted when stored within
 * the archive, false otherwise.
 */
54
bool Multifile::is_subfile_encrypted(int index) const;

1121 15 is_subfile_text 0 4 1380 26 Multifile::is_subfile_text 0 1 440 343
/**
 * Returns true if the indicated subfile represents text data, or false if it
 * represents binary data.  If the file is text data, it may have been
 * processed by end-of-line conversion when it was added.  (But the actual
 * bits in the multifile will represent the standard Unix end-of-line
 * convention, e.g.  \n instead of \r\n.)
 */
49
bool Multifile::is_subfile_text(int index) const;

1122 13 get_index_end 0 4 1380 24 Multifile::get_index_end 0 1 441 371
/**
 * Returns the first byte that is guaranteed to follow any index byte already
 * written to disk in the Multifile.
 *
 * This number is largely meaningless in many cases, but if needs_repack() is
 * false, and the file is flushed, this will indicate the number of bytes in
 * the header + index.  Everything at this byte position and later will be
 * actual data.
 */
52
std::streamoff Multifile::get_index_end(void) const;

1123 26 get_subfile_internal_start 0 4 1380 37 Multifile::get_subfile_internal_start 0 1 442 338
/**
 * Returns the starting byte position within the Multifile at which the
 * indicated subfile begins.  This may be used, with
 * get_subfile_internal_length(), for low-level access to the subfile, but
 * usually it is better to use open_read_subfile() instead (which
 * automatically decrypts and/or uncompresses the subfile data).
 */
70
std::streamoff Multifile::get_subfile_internal_start(int index) const;

1124 27 get_subfile_internal_length 0 4 1380 38 Multifile::get_subfile_internal_length 0 1 443 322
/**
 * Returns the number of bytes the indicated subfile consumes within the
 * archive.  For compressed subfiles, this will generally be smaller than
 * get_subfile_length(); for encrypted (but noncompressed) subfiles, it may be
 * slightly different, for noncompressed and nonencrypted subfiles, it will be
 * equal.
 */
68
std::size_t Multifile::get_subfile_internal_length(int index) const;

1125 12 read_subfile 0 4 1380 23 Multifile::read_subfile 0 1 444 253
/**
 * Returns a vector_uchar that contains the entire contents of the indicated
 * subfile.
 */

/**
 * Fills a string with the entire contents of the indicated subfile.
 */

/**
 * Fills a pvector with the entire contents of the indicated subfile.
 */
55
inline vector_uchar Multifile::read_subfile(int index);

1126 17 open_read_subfile 0 4 1380 28 Multifile::open_read_subfile 0 1 445 937
/**
 * Returns an istream that may be used to read the indicated subfile.  You may
 * seek() within this istream to your heart's content; even though it will be
 * a reference to the already-opened pfstream of the Multifile itself, byte 0
 * appears to be the beginning of the subfile and EOF appears to be the end of
 * the subfile.
 *
 * The returned istream will have been allocated via new; you should pass the
 * pointer to close_read_subfile() when you are finished with it to delete it
 * and release its resources.
 *
 * Any future calls to repack() or close() (or the Multifile destructor) will
 * invalidate all currently open subfile pointers.
 *
 * The return value will be NULL if the stream cannot be opened for some
 * reason.
 */

/**
 * This variant of open_read_subfile() is used internally only, and accepts a
 * pointer to the internal Subfile object, which is assumed to be valid and
 * written to the multifile.
 */
54
std::istream *Multifile::open_read_subfile(int index);

1127 18 close_read_subfile 0 4 1380 29 Multifile::close_read_subfile 0 1 446 246
/**
 * Closes a file opened by a previous call to open_read_subfile().  This
 * really just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
64
static void Multifile::close_read_subfile(std::istream *stream);

1128 15 extract_subfile 0 4 1380 26 Multifile::extract_subfile 0 1 447 68
/**
 * Extracts the nth subfile into a file with the given name.
 */
69
bool Multifile::extract_subfile(int index, Filename const &filename);

1129 18 extract_subfile_to 0 4 1380 29 Multifile::extract_subfile_to 0 1 448 61
/**
 * Extracts the nth subfile to the indicated ostream.
 */
65
bool Multifile::extract_subfile_to(int index, std::ostream &out);

1130 15 compare_subfile 0 4 1380 26 Multifile::compare_subfile 0 1 449 509
/**
 * Performs a byte-for-byte comparison of the indicated file on disk with the
 * nth subfile.  Returns true if the files are equivalent, or false if they
 * are different (or the file is missing).
 *
 * If Filename::set_binary() or set_text() has already been called, it
 * specifies the nature of the source file.  If this is different from the
 * text flag of the subfile, the comparison will always return false.  If this
 * has not been specified, it will be set from the text flag of the subfile.
 */
69
bool Multifile::compare_subfile(int index, Filename const &filename);

1131 6 output 0 4 1380 17 Multifile::output 0 1 450 10
/**
 *
 */
48
void Multifile::output(std::ostream &out) const;

1132 2 ls 0 4 1380 13 Multifile::ls 0 1 451 61
/**
 * Shows a list of all subfiles within the Multifile.
 */
58
void Multifile::ls(std::ostream &out = ::std::cout) const;

1133 16 get_magic_number 0 4 1380 27 Multifile::get_magic_number 0 1 452 108
/**
 * Returns a string with the first n bytes written to a Multifile, to identify
 * it as a Multifile.
 */
60
static inline std::string Multifile::get_magic_number(void);

1134 17 set_header_prefix 0 4 1380 28 Multifile::set_header_prefix 0 1 453 644
/**
 * Sets the string which is written to the Multifile before the Multifile
 * header.  This string must begin with a hash mark and end with a newline
 * character; and if it includes embedded newline characters, each one must be
 * followed by a hash mark.  If these conditions are not initially true, the
 * string will be modified as necessary to make it so.
 *
 * This is primarily useful as a simple hack to allow p3d applications to be
 * run directly from the command line on Unix-like systems.
 *
 * The return value is true if successful, or false on failure (for instance,
 * because the header prefix violates the above rules).
 */
68
void Multifile::set_header_prefix(std::string const &header_prefix);

1135 17 get_header_prefix 0 4 1380 28 Multifile::get_header_prefix 0 1 454 113
/**
 * Returns the string that preceded the Multifile header on the file, if any.
 * See set_header_prefix().
 */
67
inline std::string const &Multifile::get_header_prefix(void) const;

1136 7 Namable 0 260 1383 16 Namable::Namable 0 2 455 456 10
/**
 *
 */
123
inline explicit Namable::Namable(std::string const &initial_name = "");
inline Namable::Namable(Namable const &) = default;

1137 8 set_name 0 4 1383 17 Namable::set_name 0 1 457 10
/**
 *
 */
55
inline void Namable::set_name(std::string const &name);

1138 10 clear_name 0 4 1383 19 Namable::clear_name 0 1 458 46
/**
 * Resets the Namable's name to empty.
 */
38
inline void Namable::clear_name(void);

1139 8 has_name 0 4 1383 17 Namable::has_name 0 1 459 94
/**
 * Returns true if the Namable has a nonempty name set, false if the name is
 * empty.
 */
42
inline bool Namable::has_name(void) const;

1140 8 get_name 0 4 1383 17 Namable::get_name 0 1 460 10
/**
 *
 */
56
inline std::string const &Namable::get_name(void) const;

1141 6 output 0 4 1383 15 Namable::output 0 1 461 251
// In the absence of any definition to the contrary, outputting a Namable
// will write out its name.

/**
 * Outputs the Namable.  This function simply writes the name to the output
 * stream; most Namable derivatives will probably redefine this.
 */
53
inline void Namable::output(std::ostream &out) const;

1142 14 get_class_type 0 4 1383 23 Namable::get_class_type 0 1 462 0
48
static TypeHandle Namable::get_class_type(void);

1143 8 ~Namable 0 516 1383 17 Namable::~Namable 0 0 0
24
Namable::~Namable(void);

1144 18 clear_certificates 0 4 1385 34 OpenSSLWrapper::clear_certificates 0 1 463 200
/**
 * Removes all the certificates from the global store, including the compiled-
 * in certificates loaded from ca_bundle_data.c.  You can add new certificates
 * by calling load_certificates().
 */
46
void OpenSSLWrapper::clear_certificates(void);

1145 17 load_certificates 0 4 1385 33 OpenSSLWrapper::load_certificates 0 1 464 436
/**
 * Reads the PEM-formatted certificate(s) (delimited by -----BEGIN
 * CERTIFICATE----- and -----END CERTIFICATE-----) from the indicated file and
 * adds them to the global store object, retrieved via get_x509_store().
 *
 * Returns the number of certificates read on success, or 0 on failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */
64
int OpenSSLWrapper::load_certificates(Filename const &filename);

1146 30 load_certificates_from_pem_ram 0 4 1385 46 OpenSSLWrapper::load_certificates_from_pem_ram 0 2 465 466 754
/**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be PEM-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */

/**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be PEM-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */
176
int OpenSSLWrapper::load_certificates_from_pem_ram(char const *data, std::size_t data_size);
inline int OpenSSLWrapper::load_certificates_from_pem_ram(std::string const &data);

1147 30 load_certificates_from_der_ram 0 4 1385 46 OpenSSLWrapper::load_certificates_from_der_ram 0 2 467 468 754
/**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be DER-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */

/**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be DER-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */
176
int OpenSSLWrapper::load_certificates_from_der_ram(char const *data, std::size_t data_size);
inline int OpenSSLWrapper::load_certificates_from_der_ram(std::string const &data);

1148 14 get_x509_store 0 4 1385 30 OpenSSLWrapper::get_x509_store 0 1 469 378
/**
 * Returns the global X509_STORE object.
 *
 * It has to be a global object, because OpenSSL seems to store some global
 * pointers associated with this object whether you want it to or not, and
 * keeping independent copies of a local X509_STORE object doesn't seem to
 * work that well.  So, we have one store that keeps all certificates the
 * application might need.
 */
49
X509_STORE *OpenSSLWrapper::get_x509_store(void);

1149 17 notify_ssl_errors 0 4 1385 33 OpenSSLWrapper::notify_ssl_errors 0 1 470 277
/**
 * A convenience function that is itself a wrapper around the OpenSSL
 * convenience function to output the recent OpenSSL errors.  This function
 * sends the error string to express_cat.warning().  If REPORT_OPENSSL_ERRORS
 * is not defined, the function does nothing.
 */
45
void OpenSSLWrapper::notify_ssl_errors(void);

1150 23 notify_debug_ssl_errors 0 4 1385 39 OpenSSLWrapper::notify_debug_ssl_errors 0 1 471 84
/**
 * As notify_ssl_errors(), but sends the output to debug instead of warning.
 */
51
void OpenSSLWrapper::notify_debug_ssl_errors(void);

1151 14 get_global_ptr 0 4 1385 30 OpenSSLWrapper::get_global_ptr 0 1 472 10
/**
 *
 */
60
static OpenSSLWrapper *OpenSSLWrapper::get_global_ptr(void);

1152 11 SubfileInfo 0 260 1386 24 SubfileInfo::SubfileInfo 0 4 473 474 475 476 46
/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
321
inline SubfileInfo::SubfileInfo(void);
inline explicit SubfileInfo::SubfileInfo(FileReference const *file, std::streamoff start, std::streamsize size);
inline explicit SubfileInfo::SubfileInfo(Filename const &filename, std::streamoff start, std::streamsize size);
inline SubfileInfo::SubfileInfo(SubfileInfo const &copy);

1153 10 operator = 0 4 1386 23 SubfileInfo::operator = 0 1 477 0
61
inline void SubfileInfo::operator =(SubfileInfo const &copy);

1154 8 is_empty 0 4 1386 21 SubfileInfo::is_empty 0 1 478 98
/**
 * Returns true if this SubfileInfo doesn't define any file, false if it has
 * real data.
 */
46
inline bool SubfileInfo::is_empty(void) const;

1155 8 get_file 0 4 1386 21 SubfileInfo::get_file 0 1 479 63
/**
 * Returns the FileReference that represents this file.
 */
62
inline FileReference const *SubfileInfo::get_file(void) const;

1156 12 get_filename 0 4 1386 25 SubfileInfo::get_filename 0 1 480 38
/**
 * A shortcut to the filename.
 */
61
inline Filename const &SubfileInfo::get_filename(void) const;

1157 9 get_start 0 4 1386 22 SubfileInfo::get_start 0 1 481 77
/**
 * Returns the offset within the file at which this file data begins.
 */
57
inline std::streamoff SubfileInfo::get_start(void) const;

1158 8 get_size 0 4 1386 21 SubfileInfo::get_size 0 1 482 115
/**
 * Returns the number of consecutive bytes, beginning at get_start(), that
 * correspond to this file data.
 */
57
inline std::streamsize SubfileInfo::get_size(void) const;

1159 6 output 0 4 1386 19 SubfileInfo::output 0 1 483 10
/**
 *
 */
50
void SubfileInfo::output(std::ostream &out) const;

1160 12 ~SubfileInfo 0 516 1386 25 SubfileInfo::~SubfileInfo 0 0 0
32
SubfileInfo::~SubfileInfo(void);

1161 15 get_file_system 0 6 1387 28 VirtualFile::get_file_system 0 1 484 0
72
virtual VirtualFileSystem *VirtualFile::get_file_system(void) const = 0;

1162 12 get_filename 0 6 1387 25 VirtualFile::get_filename 0 1 485 0
59
virtual Filename VirtualFile::get_filename(void) const = 0;

1163 21 get_original_filename 0 4 1387 34 VirtualFile::get_original_filename 0 1 486 166
/**
 * Returns the original filename as it was used to locate this VirtualFile.
 * This is usually, but not always, the same string returned by
 * get_filename().
 */
70
inline Filename const &VirtualFile::get_original_filename(void) const;

1164 8 has_file 0 6 1387 21 VirtualFile::has_file 0 1 487 61
/**
 * Returns true if this file exists, false otherwise.
 */
47
virtual bool VirtualFile::has_file(void) const;

1165 12 is_directory 0 6 1387 25 VirtualFile::is_directory 0 1 488 117
/**
 * Returns true if this file represents a directory (and scan_directory() may
 * be called), false otherwise.
 */
51
virtual bool VirtualFile::is_directory(void) const;

1166 15 is_regular_file 0 6 1387 28 VirtualFile::is_regular_file 0 1 489 115
/**
 * Returns true if this file represents a regular file (and read_file() may be
 * called), false otherwise.
 */
54
virtual bool VirtualFile::is_regular_file(void) const;

1167 11 is_writable 0 6 1387 24 VirtualFile::is_writable 0 1 490 153
/**
 * Returns true if this file may be written to, which implies write_file() may
 * be called (unless it is a directory instead of a regular file).
 */
50
virtual bool VirtualFile::is_writable(void) const;

1168 11 delete_file 0 6 1387 24 VirtualFile::delete_file 0 1 491 199
/**
 * Attempts to delete this file or directory.  This can remove a single file
 * or an empty directory.  It will not remove a nonempty directory.  Returns
 * true on success, false on failure.
 */
44
virtual bool VirtualFile::delete_file(void);

1169 11 rename_file 0 6 1387 24 VirtualFile::rename_file 0 1 492 589
/**
 * Attempts to move or rename this file or directory.  If the original file is
 * an ordinary file, it will quietly replace any already-existing file in the
 * new filename (but not a directory).  If the original file is a directory,
 * the new filename must not already exist.
 *
 * If the file is a directory, the new filename must be within the same mount
 * point.  If the file is an ordinary file, the new filename may be anywhere;
 * but if it is not within the same mount point then the rename operation is
 * automatically performed as a two-step copy-and-delete operation.
 */
61
virtual bool VirtualFile::rename_file(VirtualFile *new_file);

1170 9 copy_file 0 6 1387 22 VirtualFile::copy_file 0 1 493 123
/**
 * Attempts to copy the contents of this file to the indicated file.  Returns
 * true on success, false on failure.
 */
59
virtual bool VirtualFile::copy_file(VirtualFile *new_file);

1171 14 scan_directory 0 4 1387 27 VirtualFile::scan_directory 0 1 494 247
/**
 * If the file represents a directory (that is, is_directory() returns true),
 * this returns the list of files within the directory at the current time.
 * Returns NULL if the file is not a directory or if the directory cannot be
 * read.
 */
69
PointerTo< VirtualFileList > VirtualFile::scan_directory(void) const;

1172 6 output 0 4 1387 19 VirtualFile::output 0 1 495 10
/**
 *
 */
50
void VirtualFile::output(std::ostream &out) const;

1173 2 ls 0 4 1387 15 VirtualFile::ls 0 1 496 66
/**
 * If the file represents a directory, lists its contents.
 */
60
void VirtualFile::ls(std::ostream &out = ::std::cout) const;

1174 6 ls_all 0 4 1387 19 VirtualFile::ls_all 0 1 497 113
/**
 * If the file represents a directory, recursively lists its contents and
 * those of all subdirectories.
 */
64
void VirtualFile::ls_all(std::ostream &out = ::std::cout) const;

1175 9 read_file 0 4 1387 22 VirtualFile::read_file 0 1 498 352
/**
 * Returns the entire contents of the file as a string.
 */

/**
 * Fills up the indicated string with the contents of the file, if it is a
 * regular file.  Returns true on success, false otherwise.
 */

/**
 * Fills up the indicated pvector with the contents of the file, if it is a
 * regular file.  Returns true on success, false otherwise.
 */
57
PyObject *VirtualFile::read_file(bool auto_unwrap) const;

1176 14 open_read_file 0 6 1387 27 VirtualFile::open_read_file 0 1 499 179
/**
 * Opens the file for reading.  Returns a newly allocated istream on success
 * (which you should eventually delete when you are done reading). Returns
 * NULL on failure.
 */
74
virtual std::istream *VirtualFile::open_read_file(bool auto_unwrap) const;

1177 15 close_read_file 0 6 1387 28 VirtualFile::close_read_file 0 1 500 243
/**
 * Closes a file opened by a previous call to open_read_file().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
70
virtual void VirtualFile::close_read_file(std::istream *stream) const;

1178 19 was_read_successful 0 6 1387 32 VirtualFile::was_read_successful 0 1 501 324
/**
 * Call this method after a reading the istream returned by open_read_file()
 * to completion.  If it returns true, the file was read completely and
 * without error; if it returns false, there may have been some errors or a
 * truncated file read.  This is particularly likely if the stream is a
 * VirtualFileHTTP.
 */
58
virtual bool VirtualFile::was_read_successful(void) const;

1179 10 write_file 0 4 1387 23 VirtualFile::write_file 0 1 502 197
/**
 * Writes the entire contents of the file as a string, if it is writable.
 */

/**
 * Writes the indicated data to the file, if it is writable.  Returns true on
 * success, false otherwise.
 */
66
PyObject *VirtualFile::write_file(PyObject *data, bool auto_wrap);

1180 15 open_write_file 0 6 1387 28 VirtualFile::open_write_file 0 1 503 179
/**
 * Opens the file for writing.  Returns a newly allocated ostream on success
 * (which you should eventually delete when you are done writing). Returns
 * NULL on failure.
 */
82
virtual std::ostream *VirtualFile::open_write_file(bool auto_wrap, bool truncate);

1181 16 open_append_file 0 6 1387 29 VirtualFile::open_append_file 0 1 504 180
/**
 * Works like open_write_file(), but the file is opened in append mode.  Like
 * open_write_file, the returned pointer should eventually be passed to
 * close_write_file().
 */
58
virtual std::ostream *VirtualFile::open_append_file(void);

1182 16 close_write_file 0 6 1387 29 VirtualFile::close_write_file 0 1 505 244
/**
 * Closes a file opened by a previous call to open_write_file().  This really
 * just deletes the ostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
65
virtual void VirtualFile::close_write_file(std::ostream *stream);

1183 20 open_read_write_file 0 6 1387 33 VirtualFile::open_read_write_file 0 1 506 180
/**
 * Opens the file for writing.  Returns a newly allocated iostream on success
 * (which you should eventually delete when you are done writing). Returns
 * NULL on failure.
 */
72
virtual std::iostream *VirtualFile::open_read_write_file(bool truncate);

1184 21 open_read_append_file 0 6 1387 34 VirtualFile::open_read_append_file 0 1 507 194
/**
 * Works like open_read_write_file(), but the file is opened in append mode.
 * Like open_read_write_file, the returned pointer should eventually be passed
 * to close_read_write_file().
 */
64
virtual std::iostream *VirtualFile::open_read_append_file(void);

1185 21 close_read_write_file 0 6 1387 34 VirtualFile::close_read_write_file 0 1 508 250
/**
 * Closes a file opened by a previous call to open_read_write_file().  This
 * really just deletes the iostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
71
virtual void VirtualFile::close_read_write_file(std::iostream *stream);

1186 13 get_file_size 0 6 1387 26 VirtualFile::get_file_size 0 2 509 510 329
/**
 * Returns the current size on disk (or wherever it is) of the already-open
 * file.  Pass in the stream that was returned by open_read_file(); some
 * implementations may require this stream to determine the size.
 */

/**
 * Returns the current size on disk (or wherever it is) of the file before it
 * has been opened.
 */
143
virtual std::streamsize VirtualFile::get_file_size(std::istream *stream) const;
virtual std::streamsize VirtualFile::get_file_size(void) const;

1187 13 get_timestamp 0 6 1387 26 VirtualFile::get_timestamp 0 1 511 434
/**
 * Returns a time_t value that represents the time the file was last modified,
 * to within whatever precision the operating system records this information
 * (on a Windows95 system, for instance, this may only be accurate to within 2
 * seconds).
 *
 * If the timestamp cannot be determined, either because it is not supported
 * by the operating system or because there is some error (such as file not
 * found), returns 0.
 */
54
virtual time_t VirtualFile::get_timestamp(void) const;

1188 15 get_system_info 0 6 1387 28 VirtualFile::get_system_info 0 1 512 330
/**
 * Populates the SubfileInfo structure with the data representing where the
 * file actually resides on disk, if this is knowable.  Returns true if the
 * file might reside on disk, and the info is populated, or false if it does
 * not (or it is not known where the file resides), in which case the info is
 * meaningless.
 */
61
virtual bool VirtualFile::get_system_info(SubfileInfo &info);

1189 14 get_class_type 0 4 1387 27 VirtualFile::get_class_type 0 1 513 0
52
static TypeHandle VirtualFile::get_class_type(void);

1190 12 ~VirtualFile 0 516 1387 25 VirtualFile::~VirtualFile 0 0 0
32
VirtualFile::~VirtualFile(void);

1191 14 get_class_type 0 4 1388 36 VirtualFileComposite::get_class_type 0 1 514 0
61
static TypeHandle VirtualFileComposite::get_class_type(void);

1192 21 ~VirtualFileComposite 0 516 1388 43 VirtualFileComposite::~VirtualFileComposite 0 0 0
50
VirtualFileComposite::~VirtualFileComposite(void);

1193 15 get_file_system 0 4 1389 33 VirtualFileMount::get_file_system 0 1 515 68
/**
 * Returns the file system this mount object is attached to.
 */
72
inline VirtualFileSystem *VirtualFileMount::get_file_system(void) const;

1194 15 get_mount_point 0 4 1389 33 VirtualFileMount::get_mount_point 0 1 516 161
/**
 * Returns the name of the directory within the virtual file system that this
 * mount object is attached to.  This directory name will end with a slash.
 */
69
inline Filename const &VirtualFileMount::get_mount_point(void) const;

1195 15 get_mount_flags 0 4 1389 33 VirtualFileMount::get_mount_flags 0 1 517 100
/**
 * Returns the set of flags passed by the user to the
 * VirtualFileSystem::mount() command.
 */
57
inline int VirtualFileMount::get_mount_flags(void) const;

1196 6 output 0 6 1389 24 VirtualFileMount::output 0 1 518 10
/**
 *
 */
63
virtual void VirtualFileMount::output(std::ostream &out) const;

1197 5 write 0 6 1389 23 VirtualFileMount::write 0 1 519 10
/**
 *
 */
62
virtual void VirtualFileMount::write(std::ostream &out) const;

1198 14 get_class_type 0 4 1389 32 VirtualFileMount::get_class_type 0 1 520 0
57
static TypeHandle VirtualFileMount::get_class_type(void);

1199 25 VirtualFileMountMultifile 0 260 1390 52 VirtualFileMountMultifile::VirtualFileMountMultifile 0 1 521 10
/**
 *
 */
82
inline VirtualFileMountMultifile::VirtualFileMountMultifile(Multifile *multifile);

1200 13 get_multifile 0 4 1390 40 VirtualFileMountMultifile::get_multifile 0 1 522 76
/**
 * Returns the Multifile pointer that this mount object is based on.
 */
71
inline Multifile *VirtualFileMountMultifile::get_multifile(void) const;

1201 14 get_class_type 0 4 1390 41 VirtualFileMountMultifile::get_class_type 0 1 523 0
66
static TypeHandle VirtualFileMountMultifile::get_class_type(void);

1202 23 VirtualFileMountRamdisk 0 260 1391 48 VirtualFileMountRamdisk::VirtualFileMountRamdisk 0 1 524 10
/**
 *
 */
55
VirtualFileMountRamdisk::VirtualFileMountRamdisk(void);

1203 14 get_class_type 0 4 1391 39 VirtualFileMountRamdisk::get_class_type 0 1 525 0
64
static TypeHandle VirtualFileMountRamdisk::get_class_type(void);

1204 24 ~VirtualFileMountRamdisk 0 516 1391 49 VirtualFileMountRamdisk::~VirtualFileMountRamdisk 0 0 0
56
VirtualFileMountRamdisk::~VirtualFileMountRamdisk(void);

1205 22 VirtualFileMountSystem 0 260 1392 46 VirtualFileMountSystem::VirtualFileMountSystem 0 1 526 10
/**
 *
 */
89
inline VirtualFileMountSystem::VirtualFileMountSystem(Filename const &physical_filename);

1206 21 get_physical_filename 0 4 1392 45 VirtualFileMountSystem::get_physical_filename 0 1 527 112
/**
 * Returns the name of the source file on the OS filesystem of the directory
 * or file that is mounted.
 */
81
inline Filename const &VirtualFileMountSystem::get_physical_filename(void) const;

1207 14 get_class_type 0 4 1392 38 VirtualFileMountSystem::get_class_type 0 1 528 0
63
static TypeHandle VirtualFileMountSystem::get_class_type(void);

1208 23 ~VirtualFileMountSystem 0 516 1392 47 VirtualFileMountSystem::~VirtualFileMountSystem 0 0 0
54
VirtualFileMountSystem::~VirtualFileMountSystem(void);

1209 9 get_mount 0 4 1393 28 VirtualFileSimple::get_mount 0 1 529 69
/**
 * Returns the VirtualFileMount this file is associated with.
 */
66
inline VirtualFileMount *VirtualFileSimple::get_mount(void) const;

1210 19 is_implicit_pz_file 0 4 1393 38 VirtualFileSimple::is_implicit_pz_file 0 1 530 174
/**
 * Returns true if this file is a .pz file that should be implicitly
 * decompressed on load, or false if it is not a .pz file or if it should not
 * be decompressed.
 */
63
inline bool VirtualFileSimple::is_implicit_pz_file(void) const;

1211 14 get_class_type 0 4 1393 33 VirtualFileSimple::get_class_type 0 1 531 0
58
static TypeHandle VirtualFileSimple::get_class_type(void);

1212 18 ~VirtualFileSimple 0 516 1393 37 VirtualFileSimple::~VirtualFileSimple 0 0 0
44
VirtualFileSimple::~VirtualFileSimple(void);

1213 13 TemporaryFile 0 260 1394 28 TemporaryFile::TemporaryFile 0 2 532 533 10
/**
 *
 */
141
inline explicit TemporaryFile::TemporaryFile(Filename const &filename);
inline TemporaryFile::TemporaryFile(TemporaryFile const &) = default;

1214 14 get_class_type 0 4 1394 29 TemporaryFile::get_class_type 0 1 534 0
54
static TypeHandle TemporaryFile::get_class_type(void);

1215 17 IDecompressStream 0 260 1395 36 IDecompressStream::IDecompressStream 0 2 535 536 22
/**
 *
 */

/**
 *
 */
144
inline IDecompressStream::IDecompressStream(void);
inline explicit IDecompressStream::IDecompressStream(std::istream *source, bool owns_source);

1216 4 open 0 4 1395 23 IDecompressStream::open 0 1 537 10
/**
 *
 */
90
inline IDecompressStream &IDecompressStream::open(std::istream *source, bool owns_source);

1217 5 close 0 4 1395 24 IDecompressStream::close 0 1 538 118
/**
 * Resets the ZStream to empty, but does not actually close the source istream
 * unless owns_source was true.
 */
57
inline IDecompressStream &IDecompressStream::close(void);

1218 18 ~IDecompressStream 0 516 1395 37 IDecompressStream::~IDecompressStream 0 0 0
44
IDecompressStream::~IDecompressStream(void);

1219 15 OCompressStream 0 260 1396 32 OCompressStream::OCompressStream 0 2 539 540 22
/**
 *
 */

/**
 *
 */
159
inline OCompressStream::OCompressStream(void);
inline explicit OCompressStream::OCompressStream(std::ostream *dest, bool owns_dest, int compression_level = 6);

1220 4 open 0 4 1396 21 OCompressStream::open 0 1 541 10
/**
 *
 */
109
inline OCompressStream &OCompressStream::open(std::ostream *dest, bool owns_dest, int compression_level = 6);

1221 5 close 0 4 1396 22 OCompressStream::close 0 1 542 114
/**
 * Resets the ZStream to empty, but does not actually close the dest ostream
 * unless owns_dest was true.
 */
53
inline OCompressStream &OCompressStream::close(void);

1222 16 ~OCompressStream 0 516 1396 33 OCompressStream::~OCompressStream 0 0 0
40
OCompressStream::~OCompressStream(void);

1223 13 get_num_files 0 4 1397 30 VirtualFileList::get_num_files 0 1 544 51
/**
 * Returns the number of files in the list.
 */
62
inline std::size_t VirtualFileList::get_num_files(void) const;

1224 8 get_file 0 4 1397 25 VirtualFileList::get_file 0 1 545 44
/**
 * Returns the nth file in the list.
 */
67
inline VirtualFile *VirtualFileList::get_file(std::size_t n) const;

1225 11 operator [] 0 4 1397 28 VirtualFileList::operator [] 0 1 546 44
/**
 * Returns the nth file in the list.
 */
70
inline VirtualFile *VirtualFileList::operator [](std::size_t n) const;

1226 4 size 0 4 1397 21 VirtualFileList::size 0 1 547 51
/**
 * Returns the number of files in the list.
 */
53
inline std::size_t VirtualFileList::size(void) const;

1227 11 operator += 0 4 1397 28 VirtualFileList::operator += 0 1 548 0
71
inline void VirtualFileList::operator +=(VirtualFileList const &other);

1228 10 operator + 0 4 1397 27 VirtualFileList::operator + 0 1 549 0
87
inline VirtualFileList VirtualFileList::operator +(VirtualFileList const &other) const;

1229 15 VirtualFileList 0 260 1397 32 VirtualFileList::VirtualFileList 0 1 543 10
/**
 *
 */
75
inline VirtualFileList::VirtualFileList(VirtualFileList const &) = default;

1230 17 VirtualFileSystem 0 260 1398 36 VirtualFileSystem::VirtualFileSystem 0 1 550 10
/**
 *
 */
43
VirtualFileSystem::VirtualFileSystem(void);

1231 18 ~VirtualFileSystem 0 516 1398 37 VirtualFileSystem::~VirtualFileSystem 0 0 10
/**
 *
 */
44
VirtualFileSystem::~VirtualFileSystem(void);

1232 5 mount 0 4 1398 24 VirtualFileSystem::mount 0 3 551 552 553 1288
/**
 * Mounts the indicated Multifile at the given mount point.
 */

/**
 * Mounts the indicated system file or directory at the given mount point.  If
 * the named file is a directory, mounts the directory.  If the named file is
 * a Multifile, mounts it as a Multifile.  Returns true on success, false on
 * failure.
 *
 * A given system directory may be mounted to multiple different mount point,
 * and the same mount point may share multiple system directories.  In the
 * case of ambiguities (that is, two different files with exactly the same
 * full pathname), the most-recently mounted system wins.
 *
 * The filename specified as the first parameter must refer to a real,
 * physical filename on disk; it cannot be a virtual file already appearing
 * within the vfs filespace.  However, it is possible to mount such a file;
 * see mount_loop() for this.
 *
 * Note that a mounted VirtualFileSystem directory is fully case-sensitive,
 * unlike the native Windows file system, so you must refer to files within
 * the virtual file system with exactly the right case.
 */

/**
 * Adds the given VirtualFileMount object to the mount list.  This is a lower-
 * level function than the other flavors of mount(); it requires you to create
 * a VirtualFileMount object specifically.
 */
328
bool VirtualFileSystem::mount(Multifile *multifile, Filename const &mount_point, int flags);
bool VirtualFileSystem::mount(Filename const &physical_filename, Filename const &mount_point, int flags, std::string const &password = "");
bool VirtualFileSystem::mount(VirtualFileMount *mount, Filename const &mount_point, int flags);

1233 10 mount_loop 0 4 1398 29 VirtualFileSystem::mount_loop 0 1 554 587
/**
 * This is similar to mount(), but it receives the name of a Multifile that
 * already appears within the virtual file system.  It can be used to mount a
 * Multifile that is itself hosted within a virtually-mounted Multifile.
 *
 * This interface can also be used to mount physical files (that appear within
 * the virtual filespace), but it cannot be used to mount directories.  Use
 * mount() if you need to mount a directory.
 *
 * Note that there is additional overhead, in the form of additional buffer
 * copies of the data, for recursively mounting a multifile like this.
 */
143
bool VirtualFileSystem::mount_loop(Filename const &virtual_filename, Filename const &mount_point, int flags, std::string const &password = "");

1234 7 unmount 0 4 1398 26 VirtualFileSystem::unmount 0 3 555 556 557 419
/**
 * Unmounts all appearances of the indicated Multifile from the file system.
 * Returns the number of appearances unmounted.
 */

/**
 * Unmounts all appearances of the indicated directory name or multifile name
 * from the file system.  Returns the number of appearances unmounted.
 */

/**
 * Unmounts the indicated VirtualFileMount object from the file system.
 * Returns the number of appearances unmounted.
 */
177
int VirtualFileSystem::unmount(Multifile *multifile);
int VirtualFileSystem::unmount(Filename const &physical_filename);
int VirtualFileSystem::unmount(VirtualFileMount *mount);

1235 13 unmount_point 0 4 1398 32 VirtualFileSystem::unmount_point 0 1 558 136
/**
 * Unmounts all systems attached to the given mount point from the file
 * system.  Returns the number of appearances unmounted.
 */
66
int VirtualFileSystem::unmount_point(Filename const &mount_point);

1236 11 unmount_all 0 4 1398 30 VirtualFileSystem::unmount_all 0 1 559 96
/**
 * Unmounts all files from the file system.  Returns the number of systems
 * unmounted.
 */
41
int VirtualFileSystem::unmount_all(void);

1237 14 get_num_mounts 0 4 1398 33 VirtualFileSystem::get_num_mounts 0 1 560 65
/**
 * Returns the number of individual mounts in the system.
 */
50
int VirtualFileSystem::get_num_mounts(void) const;

1238 9 get_mount 0 4 1398 28 VirtualFileSystem::get_mount 0 1 561 47
/**
 * Returns the nth mount in the system.
 */
72
PointerTo< VirtualFileMount > VirtualFileSystem::get_mount(int n) const;

1239 5 clear 0 4 1401 40 PointerToBase< VirtualFileMount >::clear 0 1 601 0
59
inline void PointerToBase< VirtualFileMount >::clear(void);

1240 6 output 0 4 1401 41 PointerToBase< VirtualFileMount >::output 0 1 602 0
72
void PointerToBase< VirtualFileMount >::output(std::ostream &out) const;

1241 9 PointerTo 0 260 1400 40 PointerTo< VirtualFileMount >::PointerTo 0 4 592 593 594 595 0
353
inline constexpr PointerTo< VirtualFileMount >::PointerTo(void) noexcept = default;
inline explicit constexpr PointerTo< VirtualFileMount >::PointerTo(decltype(nullptr) ) noexcept;
inline PointerTo< VirtualFileMount >::PointerTo(VirtualFileMount *ptr) noexcept;
inline PointerTo< VirtualFileMount >::PointerTo(PointerTo< VirtualFileMount > const &copy);

1242 1 p 0 4 1400 32 PointerTo< VirtualFileMount >::p 0 1 596 289
// If your base class is a derivative of TypedObject, you might want to use
// the DCAST macro defined in typedObject.h instead, e.g.  DCAST(MyType,
// ptr).  This provides a clean downcast that doesn't require .p() or any
// double-casting, and it can be run-time checked for correctness.
82
constexpr VirtualFileMount *PointerTo< VirtualFileMount >::p(void) const noexcept;

1243 10 operator = 0 4 1400 41 PointerTo< VirtualFileMount >::operator = 0 2 597 598 0
175
inline void PointerTo< VirtualFileMount >::operator =(VirtualFileMount *ptr);
inline void PointerTo< VirtualFileMount >::operator =(PointerTo< VirtualFileMount > const &copy);

1244 7 is_null 0 4 1400 38 PointerTo< VirtualFileMount >::is_null 0 1 599 0
63
inline bool PointerTo< VirtualFileMount >::is_null(void) const;

1245 5 clear 0 4 1400 36 PointerTo< VirtualFileMount >::clear 0 1 600 0
55
inline void PointerTo< VirtualFileMount >::clear(void);

1246 10 ~PointerTo 0 516 1400 41 PointerTo< VirtualFileMount >::~PointerTo 0 0 0
48
PointerTo< VirtualFileMount >::~PointerTo(void);

1247 5 chdir 0 4 1398 24 VirtualFileSystem::chdir 0 1 562 171
/**
 * Changes the current directory.  This is used to resolve relative pathnames
 * in get_file() and/or find_file().  Returns true if successful, false
 * otherwise.
 */
61
bool VirtualFileSystem::chdir(Filename const &new_directory);

1248 7 get_cwd 0 4 1398 26 VirtualFileSystem::get_cwd 0 1 563 60
/**
 * Returns the current directory name.  See chdir().
 */
48
Filename VirtualFileSystem::get_cwd(void) const;

1249 14 make_directory 0 4 1398 33 VirtualFileSystem::make_directory 0 1 564 263
/**
 * Attempts to create a directory within the file system.  Returns true on
 * success, false on failure (for instance, because the parent directory does
 * not exist, or is read-only).  If the directory already existed prior to
 * this call, returns true.
 */
65
bool VirtualFileSystem::make_directory(Filename const &filename);

1250 19 make_directory_full 0 4 1398 38 VirtualFileSystem::make_directory_full 0 1 565 169
/**
 * Attempts to create a directory within the file system.  Will also create
 * any intervening directories needed.  Returns true on success, false on
 * failure.
 */
70
bool VirtualFileSystem::make_directory_full(Filename const &filename);

1251 8 get_file 0 4 1398 27 VirtualFileSystem::get_file 0 1 566 537
/**
 * Looks up the file by the indicated name in the file system.  Returns a
 * VirtualFile pointer representing the file if it is found, or NULL if it is
 * not.
 *
 * If status_only is true, the file will be checked for existence and length
 * and so on, but the returned file's contents cannot be read.  This is an
 * optimization which is especially important for certain mount types, for
 * instance HTTP, for which opening a file to determine its status is
 * substantially less expensive than opening it to read its contents.
 */
111
PointerTo< VirtualFile > VirtualFileSystem::get_file(Filename const &filename, bool status_only = false) const;

1252 11 create_file 0 4 1398 30 VirtualFileSystem::create_file 0 1 567 298
/**
 * Attempts to create a file by the indicated name in the filesystem, if
 * possible, and returns it.  If a file by this name already exists, returns
 * the same thing as get_file().  If the filename is located within a read-
 * only directory, or the directory doesn't exist, returns NULL.
 */
82
PointerTo< VirtualFile > VirtualFileSystem::create_file(Filename const &filename);

1253 9 find_file 0 4 1398 28 VirtualFileSystem::find_file 0 1 568 173
/**
 * Uses the indicated search path to find the file within the file system.
 * Returns the first occurrence of the file found, or NULL if the file cannot
 * be found.
 */
143
PointerTo< VirtualFile > VirtualFileSystem::find_file(Filename const &filename, DSearchPath const &searchpath, bool status_only = false) const;

1254 11 delete_file 0 4 1398 30 VirtualFileSystem::delete_file 0 1 569 208
/**
 * Attempts to delete the indicated file or directory.  This can remove a
 * single file or an empty directory.  It will not remove a nonempty
 * directory.  Returns true on success, false on failure.
 */
62
bool VirtualFileSystem::delete_file(Filename const &filename);

1255 11 rename_file 0 4 1398 30 VirtualFileSystem::rename_file 0 1 570 599
/**
 * Attempts to move or rename the indicated file or directory.  If the
 * original file is an ordinary file, it will quietly replace any already-
 * existing file in the new filename (but not a directory).  If the original
 * file is a directory, the new filename must not already exist.
 *
 * If the file is a directory, the new filename must be within the same mount
 * point.  If the file is an ordinary file, the new filename may be anywhere;
 * but if it is not within the same mount point then the rename operation is
 * automatically performed as a two-step copy-and-delete operation.
 */
97
bool VirtualFileSystem::rename_file(Filename const &orig_filename, Filename const &new_filename);

1256 9 copy_file 0 4 1398 28 VirtualFileSystem::copy_file 0 1 571 131
/**
 * Attempts to copy the contents of the indicated file to the indicated file.
 * Returns true on success, false on failure.
 */
95
bool VirtualFileSystem::copy_file(Filename const &orig_filename, Filename const &new_filename);

1257 16 resolve_filename 0 4 1398 35 VirtualFileSystem::resolve_filename 0 1 572 174
/**
 * Searches the given search path for the filename.  If it is found, updates
 * the filename to the full pathname found and returns true; otherwise,
 * returns false.
 */
147
bool VirtualFileSystem::resolve_filename(Filename &filename, DSearchPath const &searchpath, std::string const &default_extension = string()) const;

1258 14 find_all_files 0 4 1398 33 VirtualFileSystem::find_all_files 0 1 573 363
/**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Fills up the results list with *all* of the matching filenames
 * found, if any.  Returns the number of matches found.
 *
 * It is the responsibility of the the caller to clear the results list first;
 * otherwise, the newly-found files will be appended to the list.
 */
132
int VirtualFileSystem::find_all_files(Filename const &filename, DSearchPath const &searchpath, DSearchPath::Results &results) const;

1259 6 exists 0 4 1398 25 VirtualFileSystem::exists 0 1 574 111
/**
 * Convenience function; returns true if the named file exists in the virtual
 * file system hierarchy.
 */
70
inline bool VirtualFileSystem::exists(Filename const &filename) const;

1260 12 is_directory 0 4 1398 31 VirtualFileSystem::is_directory 0 1 575 126
/**
 * Convenience function; returns true if the named file exists as a directory in
 * the virtual file system hierarchy.
 */
76
inline bool VirtualFileSystem::is_directory(Filename const &filename) const;

1261 15 is_regular_file 0 4 1398 34 VirtualFileSystem::is_regular_file 0 1 576 129
/**
 * Convenience function; returns true if the named file exists as a regular file
 * in the virtual file system hierarchy.
 */
79
inline bool VirtualFileSystem::is_regular_file(Filename const &filename) const;

1262 14 scan_directory 0 4 1398 33 VirtualFileSystem::scan_directory 0 1 577 247
/**
 * If the file represents a directory (that is, is_directory() returns true),
 * this returns the list of files within the directory at the current time.
 * Returns NULL if the file is not a directory or if the directory cannot be
 * read.
 */
102
inline PointerTo< VirtualFileList > VirtualFileSystem::scan_directory(Filename const &filename) const;

1263 2 ls 0 4 1398 21 VirtualFileSystem::ls 0 1 578 80
/**
 * Convenience function; lists the files within the indicated directory.
 */
66
inline void VirtualFileSystem::ls(Filename const &filename) const;

1264 6 ls_all 0 4 1398 25 VirtualFileSystem::ls_all 0 1 579 117
/**
 * Convenience function; lists the files within the indicated directory, and
 * all files below, recursively.
 */
70
inline void VirtualFileSystem::ls_all(Filename const &filename) const;

1265 5 write 0 4 1398 24 VirtualFileSystem::write 0 1 580 75
/**
 * Print debugging information.  (e.g.  from Python or gdb prompt).
 */
55
void VirtualFileSystem::write(std::ostream &out) const;

1266 14 get_global_ptr 0 4 1398 33 VirtualFileSystem::get_global_ptr 0 1 581 423
/**
 * Returns the default global VirtualFileSystem.  You may create your own
 * personal VirtualFileSystem objects and use them for whatever you like, but
 * Panda will attempt to load models and stuff from this default object.
 *
 * Initially, the global VirtualFileSystem is set up to mount the OS
 * filesystem to root; i.e.  it is equivalent to the OS filesystem.  This may
 * be subsequently adjusted by the user.
 */
66
static VirtualFileSystem *VirtualFileSystem::get_global_ptr(void);

1267 9 read_file 0 4 1398 28 VirtualFileSystem::read_file 0 1 582 1244
/**
 * Convenience function; returns the entire contents of the indicated file as
 * a string.
 *
 * If auto_unwrap is true, an explicitly-named .pz/.gz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */

/**
 * Convenience function; fills the string up with the data from the indicated
 * file, if it exists and can be read.  Returns true on success, false
 * otherwise.
 *
 * If auto_unwrap is true, an explicitly-named .pz/.gz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */

/**
 * Convenience function; fills the pvector up with the data from the indicated
 * file, if it exists and can be read.  Returns true on success, false
 * otherwise.
 *
 * If auto_unwrap is true, an explicitly-named .pz/.gz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */
89
PyObject *VirtualFileSystem::read_file(Filename const &filename, bool auto_unwrap) const;

1268 14 open_read_file 0 4 1398 33 VirtualFileSystem::open_read_file 0 1 583 424
/**
 * Convenience function; returns a newly allocated istream if the file exists
 * and can be read, or NULL otherwise.  Does not return an invalid istream.
 *
 * If auto_unwrap is true, an explicitly-named .pz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */
98
std::istream *VirtualFileSystem::open_read_file(Filename const &filename, bool auto_unwrap) const;

1269 15 close_read_file 0 4 1398 34 VirtualFileSystem::close_read_file 0 1 584 243
/**
 * Closes a file opened by a previous call to open_read_file().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
69
static void VirtualFileSystem::close_read_file(std::istream *stream);

1270 10 write_file 0 4 1398 29 VirtualFileSystem::write_file 0 1 585 409
/**
 * Convenience function; writes the entire contents of the indicated file as a
 * string.
 *
 * If auto_wrap is true, an explicitly-named .pz file is automatically
 * compressed while writing.
 */

/**
 * Convenience function; writes the entire contents of the indicated file as a
 * block of data.
 *
 * If auto_wrap is true, an explicitly-named .pz file is automatically
 * compressed while writing.
 */
98
PyObject *VirtualFileSystem::write_file(Filename const &filename, PyObject *data, bool auto_wrap);

1271 15 open_write_file 0 4 1398 34 VirtualFileSystem::open_write_file 0 1 586 345
/**
 * Convenience function; returns a newly allocated ostream if the file exists
 * and can be written, or NULL otherwise.  Does not return an invalid ostream.
 *
 * If auto_wrap is true, an explicitly-named .pz file is automatically
 * compressed while writing.  If truncate is true, the file is truncated to
 * zero length before writing.
 */
106
std::ostream *VirtualFileSystem::open_write_file(Filename const &filename, bool auto_wrap, bool truncate);

1272 16 open_append_file 0 4 1398 35 VirtualFileSystem::open_append_file 0 1 587 180
/**
 * Works like open_write_file(), but the file is opened in append mode.  Like
 * open_write_file, the returned pointer should eventually be passed to
 * close_write_file().
 */
76
std::ostream *VirtualFileSystem::open_append_file(Filename const &filename);

1273 16 close_write_file 0 4 1398 35 VirtualFileSystem::close_write_file 0 1 588 244
/**
 * Closes a file opened by a previous call to open_write_file().  This really
 * just deletes the ostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
70
static void VirtualFileSystem::close_write_file(std::ostream *stream);

1274 20 open_read_write_file 0 4 1398 39 VirtualFileSystem::open_read_write_file 0 1 589 169
/**
 * Convenience function; returns a newly allocated iostream if the file exists
 * and can be written, or NULL otherwise.  Does not return an invalid
 * iostream.
 */
96
std::iostream *VirtualFileSystem::open_read_write_file(Filename const &filename, bool truncate);

1275 21 open_read_append_file 0 4 1398 40 VirtualFileSystem::open_read_append_file 0 1 590 194
/**
 * Works like open_read_write_file(), but the file is opened in append mode.
 * Like open_read_write_file, the returned pointer should eventually be passed
 * to close_read_write_file().
 */
82
std::iostream *VirtualFileSystem::open_read_append_file(Filename const &filename);

1276 21 close_read_write_file 0 4 1398 40 VirtualFileSystem::close_read_write_file 0 1 591 250
/**
 * Closes a file opened by a previous call to open_read_write_file().  This
 * really just deletes the iostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
76
static void VirtualFileSystem::close_read_write_file(std::iostream *stream);

1277 13 get_long_time 0 4 1402 24 TrueClock::get_long_time 0 1 603 420
// get_long_time() returns the most accurate timer we have over a long
// interval.  It may not be very precise for measuring short intervals, but
// it should not drift substantially over the long haul.

// get_long_time() returns the most accurate timer we have over a long
// interval.  It may not be very precise for measuring short intervals, but
// it should not drift substantially over the long haul.

/**
 *
 */
38
double TrueClock::get_long_time(void);

1278 14 get_short_time 0 4 1402 25 TrueClock::get_short_time 0 1 604 426
// get_short_time() returns the most precise timer we have over a short
// interval.  It may tend to drift over the long haul, but it should have
// lots of digits to measure short intervals very precisely.

// get_short_time() returns the most precise timer we have over a short
// interval.  It may tend to drift over the long haul, but it should have
// lots of digits to measure short intervals very precisely.

/**
 *
 */
46
inline double TrueClock::get_short_time(void);

1279 18 get_short_raw_time 0 4 1402 29 TrueClock::get_short_raw_time 0 1 605 302
// get_short_raw_time() is like get_short_time(), but does not apply any
// corrections (e.g.  paranoid-clock) to the result returned by the OS.

// get_short_raw_time() is like get_short_time(), but does not apply any
// corrections (e.g.  paranoid-clock) to the result returned by the OS.

/**
 *
 */
43
double TrueClock::get_short_raw_time(void);

1280 15 get_error_count 0 4 1402 26 TrueClock::get_error_count 0 1 606 369
/**
 * Returns the number of clock errors that have been detected.  Each time a
 * clock error is detected, in which the value returned by either of the above
 * methods is suspect, the value returned by this method will be incremented.
 * Applications can monitor this value and react, for instance, by
 * resynchronizing their clocks each time this value changes.
 */
50
inline int TrueClock::get_error_count(void) const;

1281 14 get_global_ptr 0 4 1402 25 TrueClock::get_global_ptr 0 1 607 70
/**
 * Returns a pointer to the one TrueClock object in the world.
 */
57
static inline TrueClock *TrueClock::get_global_ptr(void);

1282 16 set_cpu_affinity 0 4 1402 27 TrueClock::set_cpu_affinity 0 1 608 0
54
bool TrueClock::set_cpu_affinity(uint32_t mask) const;

1283 9 Patchfile 0 260 1404 20 Patchfile::Patchfile 0 2 609 610 111
/**
 * Create a patch file and initializes internal data
 */

/**
 * Create patch file with buffer to patch
 */
86
Patchfile::Patchfile(void);
explicit Patchfile::Patchfile(PointerTo< Buffer > buffer);

1284 10 ~Patchfile 0 516 1404 21 Patchfile::~Patchfile 0 0 10
/**
 *
 */
28
Patchfile::~Patchfile(void);

1285 5 build 0 4 1404 16 Patchfile::build 0 1 611 359
/**
 *
 * This implementation uses the "greedy differencing algorithm" described in
 * the masters thesis "Differential Compression: A Generalized Solution for
 * Binary Files" by Randal C. Burns (p.13). For an original file of size M and
 * a new file of size N, this algorithm is O(M) in space and O(M*N) (worst-
 * case) in time.  return false on error
 */
82
bool Patchfile::build(Filename file_orig, Filename file_new, Filename patch_name);

1286 11 read_header 0 4 1404 22 Patchfile::read_header 0 1 612 193
/**
 * Opens the patch file for reading, and gets the header information from the
 * file but does not begin to do any real work.  This can be used to query the
 * data stored in the patch.
 */
55
int Patchfile::read_header(Filename const &patch_file);

1287 8 initiate 0 4 1404 19 Patchfile::initiate 0 2 613 614 236
/**
 * Set up to apply the patch to the file (original file and patch are
 * destroyed in the process).
 */

/**
 * Set up to apply the patch to the file.  In this form, neither the original
 * file nor the patch file are destroyed.
 */
183
int Patchfile::initiate(Filename const &patch_file, Filename const &file);
int Patchfile::initiate(Filename const &patch_file, Filename const &orig_file, Filename const &target_file);

1288 3 run 0 4 1404 14 Patchfile::run 0 1 615 390
/**
 * Perform one buffer's worth of patching.
 * Returns one of the following values:
 * @li @c EU_ok : while patching
 * @li @c EU_success : when done
 * @li @c EU_error_abort : Patching has not been initiated
 * @li @c EU_error_file_invalid : file is corrupted
 * @li @c EU_error_invalid_checksum : incompatible patch file
 * @li @c EU_error_write_file_rename : could not rename file
 */
25
int Patchfile::run(void);

1289 5 apply 0 4 1404 16 Patchfile::apply 0 2 616 617 311
/**
 * Patches the entire file in one call returns true on success and false on
 * error
 *
 * This version will delete the patch file and overwrite the original file.
 */

/**
 * Patches the entire file in one call returns true on success and false on
 * error
 *
 * This version will not delete any files.
 */
155
bool Patchfile::apply(Filename &patch_file, Filename &file);
bool Patchfile::apply(Filename &patch_file, Filename &orig_file, Filename const &target_file);

1290 12 get_progress 0 4 1404 23 Patchfile::get_progress 0 1 618 125
/**
 * Returns a value in the range 0..1, representing the amount of progress
 * through the patchfile, during a session.
 */
55
inline PN_stdfloat Patchfile::get_progress(void) const;

1291 19 set_allow_multifile 0 4 1404 30 Patchfile::set_allow_multifile 0 1 619 362
/**
 * If this flag is set true, the Patchfile will make a special case for
 * patching Panda Multifiles, if detected, and attempt to patch them on a
 * subfile-by-subfile basis.  If this flag is false, the Patchfile will always
 * patch the file on a full-file basis.
 *
 * This has effect only when building patches; it is not used for applying
 * patches.
 */
65
inline void Patchfile::set_allow_multifile(bool allow_multifile);

1292 19 get_allow_multifile 0 4 1404 30 Patchfile::get_allow_multifile 0 1 620 37
/**
 * See set_allow_multifile().
 */
49
inline bool Patchfile::get_allow_multifile(void);

1293 20 set_footprint_length 0 4 1404 31 Patchfile::set_footprint_length 0 1 621 10
/**
 *
 */
56
inline void Patchfile::set_footprint_length(int length);

1294 20 get_footprint_length 0 4 1404 31 Patchfile::get_footprint_length 0 1 622 10
/**
 *
 */
49
inline int Patchfile::get_footprint_length(void);

1295 22 reset_footprint_length 0 4 1404 33 Patchfile::reset_footprint_length 0 1 623 10
/**
 *
 */
52
inline void Patchfile::reset_footprint_length(void);

1296 15 has_source_hash 0 4 1404 26 Patchfile::has_source_hash 0 1 624 145
/**
 * Returns true if the MD5 hash for the source file is known.  (Some early
 * versions of the patch file did not store this information.)
 */
51
inline bool Patchfile::has_source_hash(void) const;

1297 15 get_source_hash 0 4 1404 26 Patchfile::get_source_hash 0 1 625 52
/**
 * Returns the MD5 hash for the source file.
 */
61
inline HashVal const &Patchfile::get_source_hash(void) const;

1298 15 get_result_hash 0 4 1404 26 Patchfile::get_result_hash 0 1 626 78
/**
 * Returns the MD5 hash for the file after the patch has been applied.
 */
61
inline HashVal const &Patchfile::get_result_hash(void) const;

1299 13 password_hash 0 1 0 13 password_hash 0 1 665 0
103
std::string password_hash(std::string const &password, std::string const &salt, int iters, int keylen);

1300 12 ProfileTimer 0 260 1408 26 ProfileTimer::ProfileTimer 0 2 627 628 0
136
explicit ProfileTimer::ProfileTimer(char const *name = 0, int maxEntries = 4096);
ProfileTimer::ProfileTimer(ProfileTimer const &other);

1301 13 ~ProfileTimer 0 516 1408 27 ProfileTimer::~ProfileTimer 0 0 0
34
ProfileTimer::~ProfileTimer(void);

1302 4 init 0 4 1408 18 ProfileTimer::init 0 1 629 0
65
void ProfileTimer::init(char const *name, int maxEntries = 4096);

1303 2 on 0 4 1408 16 ProfileTimer::on 0 1 630 0
28
void ProfileTimer::on(void);

1304 4 mark 0 4 1408 18 ProfileTimer::mark 0 1 631 0
41
void ProfileTimer::mark(char const *tag);

1305 3 off 0 4 1408 17 ProfileTimer::off 0 2 632 633 0
70
void ProfileTimer::off(void);
void ProfileTimer::off(char const *tag);

1306 12 getTotalTime 0 4 1408 26 ProfileTimer::getTotalTime 0 1 634 110
// Don't call any of the following during timing: (Because they are slow,
// not because anything will break).
46
double ProfileTimer::getTotalTime(void) const;

1307 16 consolidateAllTo 0 4 1408 30 ProfileTimer::consolidateAllTo 0 1 635 0
76
static void ProfileTimer::consolidateAllTo(std::ostream &out = ::std::cout);

1308 13 consolidateTo 0 4 1408 27 ProfileTimer::consolidateTo 0 1 636 0
72
void ProfileTimer::consolidateTo(std::ostream &out = ::std::cout) const;

1309 10 printAllTo 0 4 1408 24 ProfileTimer::printAllTo 0 1 637 0
70
static void ProfileTimer::printAllTo(std::ostream &out = ::std::cout);

1310 7 printTo 0 4 1408 21 ProfileTimer::printTo 0 1 638 0
66
void ProfileTimer::printTo(std::ostream &out = ::std::cout) const;

1311 11 was_deleted 0 4 1409 30 WeakPointerToVoid::was_deleted 0 1 639 384
/**
 * Returns true if the object we are pointing to has been deleted, false
 * otherwise.  If this returns true, it means that the pointer can not yet be
 * reused, but it does not guarantee that it can be safely accessed.  See the
 * lock() method for a safe way to access the underlying pointer.
 *
 * This will always return true for a null pointer, unlike is_valid_pointer().
 */
55
inline bool WeakPointerToVoid::was_deleted(void) const;

1312 16 is_valid_pointer 0 4 1409 35 WeakPointerToVoid::is_valid_pointer 0 1 640 122
/**
 * Returns true if the pointer is not null and the object has not been
 * deleted.  See was_deleted() for caveats.
 */
60
inline bool WeakPointerToVoid::is_valid_pointer(void) const;

1313 18 ~WeakPointerToVoid 0 516 1409 37 WeakPointerToVoid::~WeakPointerToVoid 0 0 0
44
WeakPointerToVoid::~WeakPointerToVoid(void);

1314 16 set_string_value 0 4 1410 33 WindowsRegistry::set_string_value 0 1 643 302
/**
 * Sets the registry key to the indicated value as a string.  The supplied
 * string value is automatically converted from whatever encoding is set by
 * TextEncoder::set_default_encoding() and written as a Unicode string.  The
 * registry key must already exist prior to calling this function.
 */
183
static bool WindowsRegistry::set_string_value(std::string const &key, std::string const &name, std::string const &value, WindowsRegistry::RegLevel rl = ::WindowsRegistry::rl_machine);

1315 13 set_int_value 0 4 1410 30 WindowsRegistry::set_int_value 0 1 644 142
/**
 * Sets the registry key to the indicated value as an integer.  The registry
 * key must already exist prior to calling this function.
 */
165
static bool WindowsRegistry::set_int_value(std::string const &key, std::string const &name, int value, WindowsRegistry::RegLevel rl = ::WindowsRegistry::rl_machine);

1316 12 get_key_type 0 4 1410 29 WindowsRegistry::get_key_type 0 1 645 115
/**
 * Returns the type of the indicated key, or T_none if the key is not known or
 * is some unsupported type.
 */
170
static WindowsRegistry::Type WindowsRegistry::get_key_type(std::string const &key, std::string const &name, WindowsRegistry::RegLevel rl = ::WindowsRegistry::rl_machine);

1317 16 get_string_value 0 4 1410 33 WindowsRegistry::get_string_value 0 1 646 291
/**
 * Returns the value associated with the indicated registry key, assuming it
 * is a string value.  The string value is automatically encoded using
 * TextEncoder::get_default_encoding().  If the key is not defined or is not a
 * string type value, default_value is returned instead.
 */
198
static std::string WindowsRegistry::get_string_value(std::string const &key, std::string const &name, std::string const &default_value, WindowsRegistry::RegLevel rl = ::WindowsRegistry::rl_machine);

1318 13 get_int_value 0 4 1410 30 WindowsRegistry::get_int_value 0 1 647 206
/**
 * Returns the value associated with the indicated registry key, assuming it
 * is an integer value.  If the key is not defined or is not an integer type
 * value, default_value is returned instead.
 */
172
static int WindowsRegistry::get_int_value(std::string const &key, std::string const &name, int default_value, WindowsRegistry::RegLevel rl = ::WindowsRegistry::rl_machine);

1319 15 WindowsRegistry 0 260 1410 32 WindowsRegistry::WindowsRegistry 0 2 641 642 0
132
inline WindowsRegistry::WindowsRegistry(void) = default;
inline WindowsRegistry::WindowsRegistry(WindowsRegistry const &) = default;

1320 16 ~WindowsRegistry 0 516 1410 33 WindowsRegistry::~WindowsRegistry 0 0 0
40
WindowsRegistry::~WindowsRegistry(void);

665
1 0 0 15 15 1417 688 0 0 1 4 copy 1 1415  
2 0 0 23 15 1417 688 0 0 1 4 copy 1 1418  
3 0 0 4 16 1420 0 0 0 1 4 this 3 1417  
4 0 0 6 17 1351 0 0 0 1 4 this 3 1415  
5 0 0 6 18 1403 0 0 0 2 4 this 3 1415  1 n 1 1351  
6 0 0 38 19 1403 0 0 0 2 4 this 3 1415  1 n 1 1351  
7 0 0 38 20 1421 0 0 0 1 4 this 3 1415  
8 0 0 38 21 1421 0 0 0 3 4 this 3 1415  1 n 1 1351  5 count 1 1351  
9 0 0 6 22 1353 0 0 0 1 4 this 3 1415  
10 0 0 6 23 1353 0 0 0 1 4 this 3 1415  
11 0 0 6 24 1351 0 0 0 2 4 this 3 1415  6 param0 0 1403  
12 0 0 38 25 1421 0 0 0 1 4 this 3 1415  
13 0 0 38 26 1353 0 0 0 3 4 this 3 1415  4 view 1 1424  5 flags 1 1353  
14 0 0 36 27 1420 0 0 0 2 4 this 3 1415  4 view 1 1424  
15 0 0 39 28 1417 688 0 0 2 4 this 3 1415  4 memo 1 1421  
16 0 0 6 13 1349 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1427  5 other 1 1427  
17 0 0 6 14 1349 0 0 0 2 4 this 3 1427  5 other 1 1427  
18 0 0 4 10 1420 0 0 0 1 4 this 3 1429  
19 0 0 4 11 1420 0 0 0 2 4 this 3 1430  3 out 1 1432  
20 0 0 6 7 1349 0 0 130 /**
 * Returns true if the PointerTo is a NULL pointer, false otherwise.  (Direct
 * comparison to a NULL pointer also works.)
 */ 1 4 this 3 1433  
21 0 0 6 8 1350 0 0 10 /**
 *
 */ 1 4 this 3 1433  
22 0 0 15 39 1437 708 0 0 1 4 copy 1 1435  
23 0 0 23 39 1437 708 0 0 1 4 copy 1 1438  
24 0 0 4 40 1420 0 0 0 1 4 this 3 1437  
25 0 0 6 41 1351 0 0 0 1 4 this 3 1435  
26 0 0 6 42 1406 0 0 0 2 4 this 3 1435  1 n 1 1351  
27 0 0 38 43 1406 0 0 0 2 4 this 3 1435  1 n 1 1351  
28 0 0 38 44 1421 0 0 0 1 4 this 3 1435  
29 0 0 38 45 1421 0 0 0 3 4 this 3 1435  1 n 1 1351  5 count 1 1351  
30 0 0 6 46 1353 0 0 0 1 4 this 3 1435  
31 0 0 6 47 1353 0 0 0 1 4 this 3 1435  
32 0 0 6 48 1351 0 0 0 2 4 this 3 1435  6 param0 0 1406  
33 0 0 38 49 1421 0 0 0 1 4 this 3 1435  
34 0 0 38 50 1353 0 0 0 3 4 this 3 1435  4 view 1 1424  5 flags 1 1353  
35 0 0 36 51 1420 0 0 0 2 4 this 3 1435  4 view 1 1424  
36 0 0 39 52 1437 708 0 0 2 4 this 3 1435  4 memo 1 1421  
37 0 0 6 37 1349 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1440  5 other 1 1440  
38 0 0 6 38 1349 0 0 0 2 4 this 3 1440  5 other 1 1440  
39 0 0 4 34 1420 0 0 0 1 4 this 3 1442  
40 0 0 4 35 1420 0 0 0 2 4 this 3 1443  3 out 1 1432  
41 0 0 15 63 1447 728 0 0 1 4 copy 1 1445  
42 0 0 23 63 1447 728 0 0 1 4 copy 1 1448  
43 0 0 4 64 1420 0 0 0 1 4 this 3 1447  
44 0 0 6 65 1351 0 0 0 1 4 this 3 1445  
45 0 0 6 66 1353 0 0 0 2 4 this 3 1445  1 n 1 1351  
46 0 0 38 67 1353 0 0 0 2 4 this 3 1445  1 n 1 1351  
47 0 0 38 68 1421 0 0 0 1 4 this 3 1445  
48 0 0 38 69 1421 0 0 0 3 4 this 3 1445  1 n 1 1351  5 count 1 1351  
49 0 0 6 70 1353 0 0 0 1 4 this 3 1445  
50 0 0 6 71 1353 0 0 0 1 4 this 3 1445  
51 0 0 6 72 1351 0 0 0 2 4 this 3 1445  6 param0 0 1353  
52 0 0 38 73 1421 0 0 0 1 4 this 3 1445  
53 0 0 38 74 1353 0 0 0 3 4 this 3 1445  4 view 1 1424  5 flags 1 1353  
54 0 0 36 75 1420 0 0 0 2 4 this 3 1445  4 view 1 1424  
55 0 0 39 76 1447 728 0 0 2 4 this 3 1445  4 memo 1 1421  
56 0 0 6 61 1349 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1450  5 other 1 1450  
57 0 0 6 62 1349 0 0 0 2 4 this 3 1450  5 other 1 1450  
58 0 0 4 58 1420 0 0 0 1 4 this 3 1452  
59 0 0 4 59 1420 0 0 0 2 4 this 3 1453  3 out 1 1432  
60 0 0 15 87 1457 748 0 0 1 4 copy 1 1455  
61 0 0 23 87 1457 748 0 0 1 4 copy 1 1458  
62 0 0 4 88 1420 0 0 0 1 4 this 3 1457  
63 0 0 6 89 1351 0 0 0 1 4 this 3 1455  
64 0 0 6 90 1460 0 0 0 2 4 this 3 1455  1 n 1 1351  
65 0 0 38 91 1460 0 0 0 2 4 this 3 1455  1 n 1 1351  
66 0 0 38 92 1421 0 0 0 1 4 this 3 1455  
67 0 0 38 93 1421 0 0 0 3 4 this 3 1455  1 n 1 1351  5 count 1 1351  
68 0 0 6 94 1353 0 0 0 1 4 this 3 1455  
69 0 0 6 95 1353 0 0 0 1 4 this 3 1455  
70 0 0 6 96 1351 0 0 0 2 4 this 3 1455  6 param0 0 1460  
71 0 0 38 97 1421 0 0 0 1 4 this 3 1455  
72 0 0 38 98 1353 0 0 0 3 4 this 3 1455  4 view 1 1424  5 flags 1 1353  
73 0 0 36 99 1420 0 0 0 2 4 this 3 1455  4 view 1 1424  
74 0 0 39 100 1457 748 0 0 2 4 this 3 1455  4 memo 1 1421  
75 0 0 6 85 1349 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1461  5 other 1 1461  
76 0 0 6 86 1349 0 0 0 2 4 this 3 1461  5 other 1 1461  
77 0 0 4 82 1420 0 0 0 1 4 this 3 1463  
78 0 0 4 83 1420 0 0 0 2 4 this 3 1464  3 out 1 1432  
79 0 0 15 104 1466 770 0 0 1 4 copy 1 1418  
80 0 0 55 104 1466 770 0 0 1 6 source 1 1421  
81 0 0 23 104 1466 770 0 0 1 11 type_handle 5 1467  
82 0 0 7 105 1466 770 0 0 2 1 n 1 1351  11 type_handle 5 1467  
83 0 0 4 106 1420 0 0 0 1 4 this 3 1466  
84 0 0 6 107 1351 0 0 0 1 4 this 3 1418  
85 0 0 4 108 1420 0 0 0 2 4 this 3 1466  1 x 1 1403  
86 0 0 4 109 1420 0 0 0 1 4 this 3 1466  
87 0 0 6 110 1403 0 0 0 2 4 this 3 1418  1 n 1 1351  
88 0 0 4 111 1420 0 0 0 3 4 this 3 1466  1 n 1 1351  5 value 1 1403  
89 0 0 38 112 1403 0 0 0 2 4 this 3 1418  1 n 1 1351  
90 0 0 36 113 1420 0 0 0 3 4 this 3 1466  1 n 1 1351  5 value 1 1403  
91 0 0 38 114 1421 0 0 0 1 4 this 3 1418  
92 0 0 36 115 1420 0 0 0 2 4 this 3 1466  4 data 1 1421  
93 0 0 38 116 1421 0 0 0 3 4 this 3 1418  1 n 1 1351  5 count 1 1351  
94 0 0 4 117 1420 0 0 0 4 4 this 3 1466  1 n 1 1351  5 count 1 1351  4 data 1 1469  
95 0 0 6 118 1353 0 0 0 1 4 this 3 1418  
96 0 0 6 119 1353 0 0 0 1 4 this 3 1418  
97 0 0 6 120 1351 0 0 0 2 4 this 3 1418  6 param0 0 1403  
98 0 0 38 121 1421 0 0 0 1 4 this 3 1418  
99 0 0 38 122 1353 0 0 0 3 4 this 3 1466  4 view 1 1424  5 flags 1 1353  
100 0 0 36 123 1420 0 0 0 2 4 this 3 1418  4 view 1 1424  
101 0 0 39 124 1466 770 0 0 2 4 this 3 1418  4 memo 1 1421  
102 0 0 15 128 1470 792 0 0 1 4 copy 1 1438  
103 0 0 55 128 1470 792 0 0 1 6 source 1 1421  
104 0 0 23 128 1470 792 0 0 1 11 type_handle 5 1467  
105 0 0 7 129 1470 792 0 0 2 1 n 1 1351  11 type_handle 5 1467  
106 0 0 4 130 1420 0 0 0 1 4 this 3 1470  
107 0 0 6 131 1351 0 0 0 1 4 this 3 1438  
108 0 0 4 132 1420 0 0 0 2 4 this 3 1470  1 x 1 1406  
109 0 0 4 133 1420 0 0 0 1 4 this 3 1470  
110 0 0 6 134 1406 0 0 0 2 4 this 3 1438  1 n 1 1351  
111 0 0 4 135 1420 0 0 0 3 4 this 3 1470  1 n 1 1351  5 value 1 1406  
112 0 0 38 136 1406 0 0 0 2 4 this 3 1438  1 n 1 1351  
113 0 0 36 137 1420 0 0 0 3 4 this 3 1470  1 n 1 1351  5 value 1 1406  
114 0 0 38 138 1421 0 0 0 1 4 this 3 1438  
115 0 0 36 139 1420 0 0 0 2 4 this 3 1470  4 data 1 1421  
116 0 0 38 140 1421 0 0 0 3 4 this 3 1438  1 n 1 1351  5 count 1 1351  
117 0 0 4 141 1420 0 0 0 4 4 this 3 1470  1 n 1 1351  5 count 1 1351  4 data 1 1469  
118 0 0 6 142 1353 0 0 0 1 4 this 3 1438  
119 0 0 6 143 1353 0 0 0 1 4 this 3 1438  
120 0 0 6 144 1351 0 0 0 2 4 this 3 1438  6 param0 0 1406  
121 0 0 38 145 1421 0 0 0 1 4 this 3 1438  
122 0 0 38 146 1353 0 0 0 3 4 this 3 1470  4 view 1 1424  5 flags 1 1353  
123 0 0 36 147 1420 0 0 0 2 4 this 3 1438  4 view 1 1424  
124 0 0 39 148 1470 792 0 0 2 4 this 3 1438  4 memo 1 1421  
125 0 0 15 152 1471 814 0 0 1 4 copy 1 1448  
126 0 0 55 152 1471 814 0 0 1 6 source 1 1421  
127 0 0 23 152 1471 814 0 0 1 11 type_handle 5 1467  
128 0 0 7 153 1471 814 0 0 2 1 n 1 1351  11 type_handle 5 1467  
129 0 0 4 154 1420 0 0 0 1 4 this 3 1471  
130 0 0 6 155 1351 0 0 0 1 4 this 3 1448  
131 0 0 4 156 1420 0 0 0 2 4 this 3 1471  1 x 1 1353  
132 0 0 4 157 1420 0 0 0 1 4 this 3 1471  
133 0 0 6 158 1353 0 0 0 2 4 this 3 1448  1 n 1 1351  
134 0 0 4 159 1420 0 0 0 3 4 this 3 1471  1 n 1 1351  5 value 1 1353  
135 0 0 38 160 1353 0 0 0 2 4 this 3 1448  1 n 1 1351  
136 0 0 36 161 1420 0 0 0 3 4 this 3 1471  1 n 1 1351  5 value 1 1353  
137 0 0 38 162 1421 0 0 0 1 4 this 3 1448  
138 0 0 36 163 1420 0 0 0 2 4 this 3 1471  4 data 1 1421  
139 0 0 38 164 1421 0 0 0 3 4 this 3 1448  1 n 1 1351  5 count 1 1351  
140 0 0 4 165 1420 0 0 0 4 4 this 3 1471  1 n 1 1351  5 count 1 1351  4 data 1 1469  
141 0 0 6 166 1353 0 0 0 1 4 this 3 1448  
142 0 0 6 167 1353 0 0 0 1 4 this 3 1448  
143 0 0 6 168 1351 0 0 0 2 4 this 3 1448  6 param0 0 1353  
144 0 0 38 169 1421 0 0 0 1 4 this 3 1448  
145 0 0 38 170 1353 0 0 0 3 4 this 3 1471  4 view 1 1424  5 flags 1 1353  
146 0 0 36 171 1420 0 0 0 2 4 this 3 1448  4 view 1 1424  
147 0 0 39 172 1471 814 0 0 2 4 this 3 1448  4 memo 1 1421  
148 0 0 15 176 1472 836 0 0 1 4 copy 1 1458  
149 0 0 55 176 1472 836 0 0 1 6 source 1 1421  
150 0 0 23 176 1472 836 0 0 1 11 type_handle 5 1467  
151 0 0 7 177 1472 836 0 0 2 1 n 1 1351  11 type_handle 5 1467  
152 0 0 4 178 1420 0 0 0 1 4 this 3 1472  
153 0 0 6 179 1351 0 0 0 1 4 this 3 1458  
154 0 0 4 180 1420 0 0 0 2 4 this 3 1472  1 x 1 1460  
155 0 0 4 181 1420 0 0 0 1 4 this 3 1472  
156 0 0 6 182 1460 0 0 0 2 4 this 3 1458  1 n 1 1351  
157 0 0 4 183 1420 0 0 0 3 4 this 3 1472  1 n 1 1351  5 value 1 1460  
158 0 0 38 184 1460 0 0 0 2 4 this 3 1458  1 n 1 1351  
159 0 0 36 185 1420 0 0 0 3 4 this 3 1472  1 n 1 1351  5 value 1 1460  
160 0 0 38 186 1421 0 0 0 1 4 this 3 1458  
161 0 0 36 187 1420 0 0 0 2 4 this 3 1472  4 data 1 1421  
162 0 0 38 188 1421 0 0 0 3 4 this 3 1458  1 n 1 1351  5 count 1 1351  
163 0 0 4 189 1420 0 0 0 4 4 this 3 1472  1 n 1 1351  5 count 1 1351  4 data 1 1469  
164 0 0 6 190 1353 0 0 0 1 4 this 3 1458  
165 0 0 6 191 1353 0 0 0 1 4 this 3 1458  
166 0 0 6 192 1351 0 0 0 2 4 this 3 1458  6 param0 0 1460  
167 0 0 38 193 1421 0 0 0 1 4 this 3 1458  
168 0 0 38 194 1353 0 0 0 3 4 this 3 1472  4 view 1 1424  5 flags 1 1353  
169 0 0 36 195 1420 0 0 0 2 4 this 3 1458  4 view 1 1424  
170 0 0 39 196 1472 836 0 0 2 4 this 3 1458  4 memo 1 1421  
171 0 0 15 233 1475 858 0 0 1 6 param0 0 1473  
172 0 0 6 200 1349 0 0 125 /**
 * Returns true if the MemoryUsage object is currently tracking memory (e.g.
 * track-memory-usage is configured #t).
 */ 0 
173 0 0 6 201 1349 0 0 168 /**
 * Returns true if the MemoryUsage object is currently at least counting
 * memory (e.g.  this is a Windows debug build), even if it's not fully
 * tracking it.
 */ 0 
174 0 0 6 202 1350 0 0 136 /**
 * Returns the total number of bytes of allocated memory consumed by C++
 * objects, not including the memory previously frozen.
 */ 0 
175 0 0 6 203 1350 0 0 132 /**
 * Returns the total number of bytes of allocated memory consumed by C++
 * objects, including the memory previously frozen.
 */ 0 
176 0 0 6 204 1350 0 0 119 /**
 * Returns the total number of bytes allocated from the heap from code within
 * Panda, for individual objects.
 */ 0 
177 0 0 6 205 1350 0 0 107 /**
 * Returns the total number of bytes allocated from the heap from code within
 * Panda, for arrays.
 */ 0 
178 0 0 6 206 1350 0 0 190 /**
 * Returns the extra bytes allocated from the system that are not immediately
 * used for holding allocated objects.  This can only be determined if
 * ALTERNATIVE_MALLOC is enabled.
 */ 0 
179 0 0 6 207 1350 0 0 110 /**
 * Returns the total number of bytes allocated from the virtual memory pool
 * from code within Panda.
 */ 0 
180 0 0 6 208 1350 0 0 496 /**
 * Returns the total number of bytes of allocated memory in the heap that
 * Panda didn't seem to be responsible for.  This includes a few bytes for
 * very low-level objects (like ConfigVariables) that cannot use Panda memory
 * tracking because they are so very low-level.
 *
 * This also includes all of the memory that might have been allocated by a
 * high-level interpreter, like Python.
 *
 * This number is only available if Panda is able to hook into the actual heap
 * callback.
 */ 0 
181 0 0 6 209 1350 0 0 113 /**
 * Returns the total size of allocated memory consumed by the process, as
 * nearly as can be determined.
 */ 0 
182 0 0 6 210 1353 0 0 59 /**
 * Returns the number of pointers currently active.
 */ 0 
183 0 0 4 211 1420 0 0 100 /**
 * Fills the indicated MemoryUsagePointers with the set of all pointers
 * currently active.
 */ 1 6 result 1 1476  
184 0 0 4 212 1420 0 0 122 /**
 * Fills the indicated MemoryUsagePointers with the set of all pointers of the
 * indicated type currently active.
 */ 2 6 result 1 1476  4 type 1 1467  
185 0 0 4 213 1420 0 0 159 /**
 * Fills the indicated MemoryUsagePointers with the set of all pointers that
 * were allocated within the range of the indicated number of seconds ago.
 */ 3 6 result 1 1476  4 from 1 1403  2 to 1 1403  
186 0 0 4 214 1420 0 0 852 /**
 * Fills the indicated MemoryUsagePointers with the set of all currently
 * active pointers (that is, pointers allocated since the last call to
 * freeze(), and not yet freed) that have a zero reference count.
 *
 * Generally, an undeleted pointer with a zero reference count means its
 * reference count has never been incremented beyond zero (since once it has
 * been incremented, the only way it can return to zero would free the
 * pointer).  This may include objects that are allocated statically or on the
 * stack, which are never intended to be deleted.  Or, it might represent a
 * programmer or compiler error.
 *
 * This function has the side-effect of incrementing each of their reference
 * counts by one, thus preventing them from ever being freed--but since they
 * hadn't been freed anyway, probably no additional harm is done.
 */ 1 6 result 1 1476  
187 0 0 4 215 1420 0 0 285 /**
 * 'Freezes' all pointers currently stored so that they are no longer
 * reported; only newly allocate pointers from this point on will appear in
 * future information requests.  This makes it easier to differentiate between
 * continuous leaks and one-time memory allocations.
 */ 0 
188 0 0 4 216 1420 0 0 70 /**
 * Shows the breakdown of types of all of the active pointers.
 */ 0 
189 0 0 4 217 1420 0 0 118 /**
 * Shows the breakdown of types of all of the pointers allocated and freed
 * since the last call to freeze().
 */ 0 
190 0 0 4 218 1420 0 0 69 /**
 * Shows the breakdown of ages of all of the active pointers.
 */ 0 
191 0 0 4 219 1420 0 0 117 /**
 * Shows the breakdown of ages of all of the pointers allocated and freed
 * since the last call to freeze().
 */ 0 
192 0 0 6 239 1353 0 0 47 /**
 * Returns the current reference count.
 */ 1 4 this 3 1477  
193 0 0 4 240 1420 0 0 538 /**
 * Explicitly increments the reference count.  User code should avoid using
 * ref() and unref() directly, which can result in missed reference counts.
 * Instead, let a PointerTo object manage the reference counting
 * automatically.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 */ 1 4 this 3 1477  
194 0 0 6 241 1349 0 0 865 /**
 * Explicitly decrements the reference count.  Note that the object will not
 * be implicitly deleted by unref() simply because the reference count drops
 * to zero.  (Having a member function delete itself is problematic.) However,
 * see the helper function unref_delete().
 *
 * User code should avoid using ref() and unref() directly, which can result
 * in missed reference counts.  Instead, let a PointerTo object manage the
 * reference counting automatically.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */ 1 4 this 3 1477  
195 0 0 6 244 1349 0 0 135 /**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.  Returns true if ok, false otherwise.
 */ 1 4 this 3 1477  
196 0 0 6 245 1349 0 0 144 /**
 * Does some easy checks to make sure that the reference count isn't zero, or
 * completely bogus.  Returns true if ok, false otherwise.
 */ 1 4 this 3 1477  
197 0 0 7 246 1467 0 0 0 0 
198 0 0 6 236 1480 0 0 0 1 4 this 3 1479  
199 0 0 15 249 1483 861 0 0 1 6 param0 0 1481  
200 0 0 6 248 1353 0 0 10 /**
 *
 */ 1 4 this 3 1481  
201 0 0 4 258 1420 0 0 0 2 4 this 3 1484  5 level 1 1403  
202 0 0 6 260 1353 0 0 47 /**
 * Returns the current reference count.
 */ 1 4 this 3 1485  
203 0 0 4 261 1420 0 0 107 /**
 * Explicitly increments the node reference count and the normal reference
 * count simultaneously.
 */ 1 4 this 3 1485  
204 0 0 6 262 1349 0 0 201 /**
 * Explicitly decrements the node reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */ 1 4 this 3 1485  
205 0 0 6 263 1349 0 0 97 /**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */ 1 4 this 3 1485  
206 0 0 4 264 1420 0 0 186 /**
 * Decrements the node reference count without affecting the normal reference
 * count.  Intended to be called by derived classes only, presumably to
 * reimplement node_unref().
 */ 1 4 this 3 1485  
207 0 0 7 265 1467 0 0 0 0 
208 0 0 7 268 1487 0 0 0 0 
209 0 0 15 268 1487 0 0 0 1 4 copy 1 1488  
210 0 0 7 268 1487 0 0 64 /**
 * Constructs a datagram from an existing block of data.
 */ 1 4 data 1 1490  
211 0 0 6 269 1487 0 0 0 2 4 this 3 1487  4 copy 1 1488  
212 0 0 4 270 1420 0 0 90 /**
 * Resets the datagram to empty, in preparation for building up a new
 * datagram.
 */ 1 4 this 3 1487  
213 0 0 4 271 1420 0 0 111 /**
 * Writes a representation of the entire datagram contents, as a sequence of
 * hex (and ASCII) values.
 */ 3 4 this 3 1488  3 out 1 1432  6 indent 5 1492  
214 0 0 4 272 1420 0 0 48 /**
 * Adds a boolean value to the datagram.
 */ 2 4 this 3 1487  5 value 1 1349  
215 0 0 4 273 1420 0 0 55 /**
 * Adds a signed 8-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1493  
216 0 0 4 274 1420 0 0 58 /**
 * Adds an unsigned 8-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1495  
217 0 0 4 275 1420 0 0 56 /**
 * Adds a signed 16-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1496  
218 0 0 4 276 1420 0 0 56 /**
 * Adds a signed 32-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1498  
219 0 0 4 277 1420 0 0 56 /**
 * Adds a signed 64-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1499  
220 0 0 4 278 1420 0 0 59 /**
 * Adds an unsigned 16-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1500  
221 0 0 4 279 1420 0 0 59 /**
 * Adds an unsigned 32-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1502  
222 0 0 4 280 1420 0 0 59 /**
 * Adds an unsigned 64-bit integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1503  
223 0 0 4 281 1420 0 0 197 /**
 * Adds a 32-bit single-precision floating-point number to the datagram.
 * Since this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */ 2 4 this 3 1487  5 value 1 1504  
224 0 0 4 282 1420 0 0 63 /**
 * Adds a 64-bit floating-point number to the datagram.
 */ 2 4 this 3 1487  5 value 1 1505  
225 0 0 4 283 1420 0 0 105 /**
 * Adds either a 32-bit or a 64-bit floating-point number, according to
 * set_stdfloat_double().
 */ 2 4 this 3 1487  5 value 1 1405  
226 0 0 4 284 1420 0 0 67 // These functions pack numbers big-endian, in case that's desired. 2 4 this 3 1487  5 value 1 1496  
227 0 0 4 285 1420 0 0 67 /**
 * Adds a signed 32-bit big-endian integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1498  
228 0 0 4 286 1420 0 0 67 /**
 * Adds a signed 64-bit big-endian integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1499  
229 0 0 4 287 1420 0 0 70 /**
 * Adds an unsigned 16-bit big-endian integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1500  
230 0 0 4 288 1420 0 0 70 /**
 * Adds an unsigned 32-bit big-endian integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1502  
231 0 0 4 289 1420 0 0 70 /**
 * Adds an unsigned 64-bit big-endian integer to the datagram.
 */ 2 4 this 3 1487  5 value 1 1503  
232 0 0 4 290 1420 0 0 94 /**
 * Adds a 32-bit single-precision big-endian floating-point number to the
 * datagram.
 */ 2 4 this 3 1487  5 value 1 1504  
233 0 0 4 291 1420 0 0 74 /**
 * Adds a 64-bit big-endian floating-point number to the datagram.
 */ 2 4 this 3 1487  5 value 1 1505  
234 0 0 4 292 1420 0 0 109 /**
 * Adds a variable-length string to the datagram.  This actually adds a count
 * followed by n bytes.
 */ 2 4 this 3 1487  3 str 1 1469  
235 0 0 4 293 1420 0 0 116 /**
 * Adds a variable-length string to the datagram, using a 32-bit length field
 * to allow very long strings.
 */ 2 4 this 3 1487  3 str 1 1469  
236 0 0 4 294 1420 0 0 86 /**
 * Adds a variable-length string to the datagram, as a NULL-terminated string.
 */ 2 4 this 3 1487  3 str 1 1469  
237 0 0 4 295 1420 0 0 238 /**
 * Adds a fixed-length string to the datagram.  If the string given is less
 * than the requested size, this will pad the string out with zeroes; if it is
 * greater than the requested size, this will silently truncate the string.
 */ 3 4 this 3 1487  3 str 1 1469  4 size 1 1350  
238 0 0 4 296 1420 0 0 58 /**
 * Adds a variable-length wstring to the datagram.
 */ 2 4 this 3 1487  3 str 1 1469  
239 0 0 4 297 1420 0 0 114 /**
 * Adds a variable-length binary blob to the datagram.  This actually adds a
 * count followed by n bytes.
 */ 2 4 this 3 1487  6 param0 0 1490  
240 0 0 4 298 1420 0 0 119 /**
 * Adds a variable-length binary blob to the datagram, using a 32-bit length
 * field to allow very long blobs.
 */ 2 4 this 3 1487  6 param0 0 1490  
241 0 0 4 299 1420 0 0 67 /**
 * Adds the indicated number of zero bytes to the datagram.
 */ 2 4 this 3 1487  4 size 1 1350  
242 0 0 4 300 1420 0 0 65 /**
 * Appends some more raw data to the end of the datagram.
 */ 2 4 this 3 1487  4 data 1 1490  
243 0 0 38 301 1421 0 0 51 /**
 * Returns the datagram's data as a string.
 */ 1 4 this 3 1488  
244 0 0 38 302 1421 0 0 0 1 4 this 3 1488  
245 0 0 38 303 1421 0 0 0 1 4 this 3 1488  
246 0 0 6 304 1350 0 0 55 /**
 * Returns the number of bytes in the datagram.
 */ 1 4 this 3 1488  
247 0 0 4 305 1420 0 0 199 /**
 * Replaces the data in the Datagram with the data in the indicated PTA_uchar.
 * This is assignment by reference: subsequent changes to the Datagram will
 * also change the source PTA_uchar.
 */ 2 4 this 3 1487  4 data 1 1506  
248 0 0 4 306 1420 0 0 237 /**
 * Replaces the data in the Datagram with a copy of the data in the indicated
 * CPTA_uchar.  Unlike set_array(), a complete copy is made of the data;
 * subsequent changes to the Datagram will *not* change the source CPTA_uchar.
 */ 2 4 this 3 1487  4 data 1 1507  
249 0 0 7 307 1507 0 0 70 /**
 * Returns a const pointer to the actual data in the Datagram.
 */ 1 4 this 3 1488  
250 0 0 7 308 1506 0 0 75 /**
 * Returns a modifiable pointer to the actual data in the Datagram.
 */ 1 4 this 3 1487  
251 0 0 4 309 1420 0 0 343 /**
 * Changes the stdfloat_double flag, which defines the operation performed by
 * add_stdfloat() and DatagramIterator::get_stdfloat().  When this is true,
 * add_stdfloat() adds a 64-bit floating-point number; when it is false, it
 * adds a 32-bit floating-point number.  The default is based on the
 * STDFLOAT_DOUBLE compilation flag.
 */ 2 4 this 3 1487  15 stdfloat_double 1 1349  
252 0 0 6 310 1349 0 0 72 /**
 * Returns the stdfloat_double flag.  See set_stdfloat_double().
 */ 1 4 this 3 1488  
253 0 0 6 311 1349 0 0 0 2 4 this 3 1488  5 other 1 1488  
254 0 0 6 312 1349 0 0 0 2 4 this 3 1488  5 other 1 1488  
255 0 0 6 313 1349 0 0 0 2 4 this 3 1488  5 other 1 1488  
256 0 0 4 314 1420 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 1488  3 out 1 1432  
257 0 0 4 315 1420 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 1488  3 out 1 1432  6 indent 5 1492  
258 0 0 7 316 1467 0 0 0 0 
259 0 0 6 319 1349 0 0 0 2 4 this 3 1508  4 data 1 1487  
260 0 0 6 320 1349 0 0 391 /**
 * Skips over the next datagram without extracting it, but saves the relevant
 * file information in the SubfileInfo object so that its data may be read
 * later.  For non-file-based datagram generators, this may mean creating a
 * temporary file and copying the contents of the datagram to disk.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.
 */ 2 4 this 3 1508  4 info 1 1509  
261 0 0 6 321 1349 0 0 0 1 4 this 3 1508  
262 0 0 6 322 1349 0 0 0 1 4 this 3 1508  
263 0 0 6 323 1510 0 0 159 /**
 * Returns the filename that provides the source for these datagrams, if any,
 * or empty string if the datagrams do not originate from a file on disk.
 */ 1 4 this 3 1508  
264 0 0 6 324 1513 0 0 138 /**
 * Returns the on-disk timestamp of the file that was read, at the time it was
 * opened, if that is available, or 0 if it is not.
 */ 1 4 this 3 1511  
265 0 0 7 325 1364 0 0 156 /**
 * Returns the FileReference that provides the source for these datagrams, if
 * any, or NULL if the datagrams do not originate from a file on disk.
 */ 1 4 this 3 1508  
266 0 0 7 326 1515 1190 0 153 /**
 * Returns the VirtualFile that provides the source for these datagrams, if
 * any, or NULL if the datagrams do not originate from a VirtualFile.
 */ 1 4 this 3 1508  
267 0 0 6 327 1368 0 0 327 /**
 * Returns the current file position within the data stream, if any, or 0 if
 * the file position is not meaningful or cannot be determined.
 *
 * For DatagramGenerators that return a meaningful file position, this will be
 * pointing to the first byte following the datagram returned after a call to
 * get_datagram().
 */ 1 4 this 3 1508  
268 0 0 7 329 1516 980 0 10 /**
 *
 */ 0 
269 0 0 23 329 1516 980 0 10 /**
 *
 */ 2 8 datagram 1 1488  6 offset 5 1350  
270 0 0 15 329 1516 980 0 0 1 6 param0 0 1517  
271 0 0 6 330 1349 0 0 36 /**
 * Extracts a boolean value.
 */ 1 4 this 3 1516  
272 0 0 6 331 1493 0 0 43 /**
 * Extracts a signed 8-bit integer.
 */ 1 4 this 3 1516  
273 0 0 6 332 1495 0 0 46 /**
 * Extracts an unsigned 8-bit integer.
 */ 1 4 this 3 1516  
274 0 0 6 333 1496 0 0 44 /**
 * Extracts a signed 16-bit integer.
 */ 1 4 this 3 1516  
275 0 0 6 334 1498 0 0 44 /**
 * Extracts a signed 32-bit integer.
 */ 1 4 this 3 1516  
276 0 0 6 335 1499 0 0 44 /**
 * Extracts a signed 64-bit integer.
 */ 1 4 this 3 1516  
277 0 0 6 336 1500 0 0 47 /**
 * Extracts an unsigned 16-bit integer.
 */ 1 4 this 3 1516  
278 0 0 6 337 1502 0 0 47 /**
 * Extracts an unsigned 32-bit integer.
 */ 1 4 this 3 1516  
279 0 0 6 338 1503 0 0 47 /**
 * Extracts an unsigned 64-bit integer.
 */ 1 4 this 3 1516  
280 0 0 6 339 1504 0 0 68 /**
 * Extracts a 32-bit single-precision floating-point number.
 */ 1 4 this 3 1516  
281 0 0 6 340 1505 0 0 51 /**
 * Extracts a 64-bit floating-point number.
 */ 1 4 this 3 1516  
282 0 0 6 341 1405 0 0 119 /**
 * Extracts either a 32-bit or a 64-bit floating-point number, according to
 * Datagram::set_stdfloat_double().
 */ 1 4 this 3 1516  
283 0 0 6 342 1496 0 0 55 /**
 * Extracts a signed 16-bit big-endian integer.
 */ 1 4 this 3 1516  
284 0 0 6 343 1498 0 0 55 /**
 * Extracts a signed 32-bit big-endian integer.
 */ 1 4 this 3 1516  
285 0 0 6 344 1499 0 0 55 /**
 * Extracts a signed 64-bit big-endian integer.
 */ 1 4 this 3 1516  
286 0 0 6 345 1500 0 0 58 /**
 * Extracts an unsigned 16-bit big-endian integer.
 */ 1 4 this 3 1516  
287 0 0 6 346 1502 0 0 58 /**
 * Extracts an unsigned 32-bit big-endian integer.
 */ 1 4 this 3 1516  
288 0 0 6 347 1503 0 0 58 /**
 * Extracts an unsigned 64-bit big-endian integer.
 */ 1 4 this 3 1516  
289 0 0 6 348 1504 0 0 79 /**
 * Extracts a 32-bit big-endian single-precision floating-point number.
 */ 1 4 this 3 1516  
290 0 0 6 349 1505 0 0 62 /**
 * Extracts a 64-bit big-endian floating-point number.
 */ 1 4 this 3 1516  
291 0 0 6 350 1469 0 0 45 /**
 * Extracts a variable-length string.
 */ 1 4 this 3 1516  
292 0 0 6 351 1469 0 0 72 /**
 * Extracts a variable-length string with a 32-bit length field.
 */ 1 4 this 3 1516  
293 0 0 6 352 1469 0 0 74 /**
 * Extracts a variable-length string, as a NULL-terminated string.
 */ 1 4 this 3 1516  
294 0 0 6 353 1469 0 0 128 /**
 * Extracts a fixed-length string.  However, if a zero byte occurs within the
 * string, it marks the end of the string.
 */ 2 4 this 3 1516  4 size 1 1350  
295 0 0 6 354 1469 0 0 75 /**
 * Extracts a variable-length wstring (with a 32-bit length field).
 */ 1 4 this 3 1516  
296 0 0 6 355 1490 0 0 50 /**
 * Extracts a variable-length binary blob.
 */ 1 4 this 3 1516  
297 0 0 6 356 1490 0 0 75 /**
 * Extracts a variable-length binary blob with a 32-bit size field.
 */ 1 4 this 3 1516  
298 0 0 4 357 1420 0 0 68 /**
 * Skips over the indicated number of bytes in the datagram.
 */ 2 4 this 3 1516  4 size 1 1350  
299 0 0 6 358 1490 0 0 98 /**
 * Extracts the indicated number of bytes in the datagram and returns them as
 * a string.
 */ 2 4 this 3 1516  4 size 1 1350  
300 0 0 6 359 1490 0 0 115 /**
 * Returns the remaining bytes in the datagram as a string, but does not
 * extract them from the iterator.
 */ 1 4 this 3 1517  
301 0 0 6 360 1350 0 0 49 /**
 * Return the bytes left in the datagram.
 */ 1 4 this 3 1517  
302 0 0 6 361 1488 0 0 48 /**
 * Return the datagram of this iterator.
 */ 1 4 this 3 1517  
303 0 0 6 362 1350 0 0 100 /**
 * Returns the current position within the datagram of the next piece of data
 * to extract.
 */ 1 4 this 3 1517  
304 0 0 4 363 1420 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 1517  3 out 1 1432  
305 0 0 4 364 1420 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 1517  3 out 1 1432  6 indent 5 1492  
306 0 0 7 365 1467 0 0 0 0 
307 0 0 6 369 1349 0 0 0 2 4 this 3 1519  4 data 1 1488  
308 0 0 6 370 1349 0 0 349 /**
 * Copies the file data from the entire indicated file (via the vfs) as the
 * next datagram.  This is intended to support potentially very large
 * datagrams.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.  On true, fills "result" with the information that
 * references the copied file, if possible.
 */ 3 4 this 3 1519  6 result 1 1509  8 filename 1 1510  
309 0 0 6 370 1349 0 0 362 /**
 * Copies the file data from the range of the indicated file (outside of the
 * vfs) as the next datagram.  This is intended to support potentially very
 * large datagrams.
 *
 * Returns true on success, false on failure or if this method is
 * unimplemented.  On true, fills "result" with the information that
 * references the copied file, if possible.
 */ 3 4 this 3 1519  6 result 1 1509  6 source 1 1520  
310 0 0 6 371 1349 0 0 0 1 4 this 3 1519  
311 0 0 4 372 1420 0 0 0 1 4 this 3 1519  
312 0 0 6 373 1510 0 0 159 /**
 * Returns the filename that provides the target for these datagrams, if any,
 * or empty string if the datagrams do not get written to a file on disk.
 */ 1 4 this 3 1519  
313 0 0 7 374 1364 0 0 152 /**
 * Returns the FileReference that provides the target for these datagrams, if
 * any, or NULL if the datagrams do not written to a file on disk.
 */ 1 4 this 3 1519  
314 0 0 6 375 1368 0 0 322 /**
 * Returns the current file position within the data stream, if any, or 0 if
 * the file position is not meaningful or cannot be determined.
 *
 * For DatagramSinks that return a meaningful file position, this will be
 * pointing to the first byte following the datagram returned after a call to
 * put_datagram().
 */ 1 4 this 3 1519  
315 0 0 15 390 1522 998 0 0 1 6 param0 0 1364  
316 0 0 23 390 1522 998 0 10 /**
 *
 */ 1 8 filename 1 1510  
317 0 0 6 391 1510 0 0 49 /**
 * Returns the filename of the reference.
 */ 1 4 this 3 1364  
318 0 0 7 392 1467 0 0 0 0 
319 0 0 7 388 1467 0 0 0 0 
320 0 0 6 384 1524 0 0 0 1 4 this 3 1523  
321 0 0 7 386 1479 861 0 0 1 4 this 3 1523  
322 0 0 7 409 1525 1018 0 10 /**
 *
 */ 0 
323 0 0 15 409 1525 1018 0 0 1 6 param0 0 1526  
324 0 0 4 410 1420 0 0 130 /**
 * Moves the data pointer to the indicated byte position.  It is not an error
 * to move the pointer past the end of data.
 */ 2 4 this 3 1525  3 pos 1 1350  
325 0 0 6 411 1350 0 0 106 /**
 * Returns the current data pointer position as a byte offset from the
 * beginning of the stream.
 */ 1 4 this 3 1526  
326 0 0 38 412 1421 0 0 319 /**
 * Extracts and returns the indicated number of characters from the current
 * data pointer, and advances the data pointer.  If the data pointer exceeds
 * the end of the buffer, returns empty string.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's file.read() function.
 */ 2 4 this 3 1525  6 length 1 1350  
327 0 0 38 413 1421 0 0 303 /**
 * Assumes the stream represents a text file, and extracts one line up to and
 * including the trailing newline character.  Returns empty string when the
 * end of file is reached.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's file.readline() function.
 */ 1 4 this 3 1525  
328 0 0 38 414 1421 0 0 0 1 4 this 3 1525  
329 0 0 38 415 1421 0 0 101 /**
 * Returns the entire buffer contents as a string, regardless of the current
 * data pointer.
 */ 1 4 this 3 1526  
330 0 0 6 416 1350 0 0 58 /**
 * Returns the size of the entire buffer contents.
 */ 1 4 this 3 1526  
331 0 0 4 417 1420 0 0 47 /**
 * Empties the current buffer contents.
 */ 1 4 this 3 1525  
332 0 0 7 420 1528 1049 0 10 /**
 *
 */ 0 
333 0 0 15 420 1528 1049 0 10 /**
 *
 */ 1 4 copy 1 1529  
334 0 0 6 421 1528 0 0 0 2 4 this 3 1528  4 copy 1 1529  
335 0 0 6 422 1349 0 0 0 2 4 this 3 1529  5 other 1 1529  
336 0 0 6 423 1349 0 0 0 2 4 this 3 1529  5 other 1 1529  
337 0 0 6 424 1349 0 0 0 2 4 this 3 1529  5 other 1 1529  
338 0 0 6 425 1353 0 0 10 /**
 *
 */ 2 4 this 3 1529  5 other 1 1529  
339 0 0 4 426 1420 0 0 86 /**
 * Generates a new HashVal representing the xor of this one and the other one.
 */ 2 4 this 3 1528  5 other 1 1529  
340 0 0 4 427 1420 0 0 65 /**
 * Outputs the HashVal as four unsigned decimal integers.
 */ 2 4 this 3 1529  3 out 1 1432  
341 0 0 4 428 1420 0 0 64 /**
 * Inputs the HashVal as four unsigned decimal integers.
 */ 2 4 this 3 1528  2 in 1 1530  
342 0 0 4 429 1420 0 0 64 /**
 * Outputs the HashVal as a 32-digit hexadecimal number.
 */ 2 4 this 3 1529  3 out 1 1432  
343 0 0 4 430 1420 0 0 63 /**
 * Inputs the HashVal as a 32-digit hexadecimal number.
 */ 2 4 this 3 1528  2 in 1 1530  
344 0 0 4 431 1420 0 0 128 /**
 * Outputs the HashVal as a binary stream of bytes in order.  This is not the
 * same order generated by write_stream().
 */ 2 4 this 3 1529  3 out 1 1432  
345 0 0 4 432 1420 0 0 125 /**
 * Inputs the HashVal as a binary stream of bytes in order.  This is not the
 * same order expected by read_stream().
 */ 2 4 this 3 1528  2 in 1 1530  
346 0 0 4 433 1420 0 0 10 /**
 *
 */ 2 4 this 3 1529  3 out 1 1432  
347 0 0 6 434 1469 0 0 69 /**
 * Returns the HashVal as a string with four decimal numbers.
 */ 1 4 this 3 1529  
348 0 0 6 435 1349 0 0 112 /**
 * Sets the HashVal from a string with four decimal numbers.  Returns true if
 * valid, false otherwise.
 */ 2 4 this 3 1528  4 text 1 1469  
349 0 0 6 436 1469 0 0 63 /**
 * Returns the HashVal as a 32-byte hexadecimal string.
 */ 1 4 this 3 1529  
350 0 0 6 437 1349 0 0 111 /**
 * Sets the HashVal from a 32-byte hexademical string.  Returns true if
 * successful, false otherwise.
 */ 2 4 this 3 1528  4 text 1 1469  
351 0 0 6 438 1490 0 0 58 /**
 * Returns the HashVal as a 16-byte binary string.
 */ 1 4 this 3 1529  
352 0 0 6 439 1349 0 0 106 /**
 * Sets the HashVal from a 16-byte binary string.  Returns true if successful,
 * false otherwise.
 */ 2 4 this 3 1528  4 text 1 1490  
353 0 0 4 440 1420 0 0 10 /**
 *
 */ 2 4 this 3 1529  11 destination 1 1487  
354 0 0 4 441 1420 0 0 10 /**
 *
 */ 2 4 this 3 1528  6 source 1 1516  
355 0 0 4 442 1420 0 0 10 /**
 *
 */ 2 4 this 3 1529  11 destination 1 1531  
356 0 0 4 443 1420 0 0 10 /**
 *
 */ 2 4 this 3 1528  6 source 1 1533  
357 0 0 6 444 1349 0 0 232 /**
 * Generates the hash value from the indicated file.  Returns true on success,
 * false if the file cannot be read.  This method is only defined if we have
 * the OpenSSL library (which provides md5 functionality) available.
 */ 2 4 this 3 1528  8 filename 1 1510  
358 0 0 6 445 1349 0 0 232 /**
 * Generates the hash value from the indicated file.  Returns true on success,
 * false if the file cannot be read.  This method is only defined if we have
 * the OpenSSL library (which provides md5 functionality) available.
 */ 2 4 this 3 1528  6 stream 1 1530  
359 0 0 4 446 1420 0 0 178 /**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */ 2 4 this 3 1528  7 ramfile 1 1526  
360 0 0 4 447 1420 0 0 178 /**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */ 2 4 this 3 1528  4 data 1 1469  
361 0 0 4 448 1420 0 0 178 /**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */ 2 4 this 3 1528  4 data 1 1490  
362 0 0 4 449 1420 0 0 178 /**
 * Generates the hash value by hashing the indicated data.  This method is
 * only defined if we have the OpenSSL library (which provides md5
 * functionality) available.
 */ 3 4 this 3 1528  6 buffer 1 1469  6 length 1 1353  
363 0 0 7 452 1476 1051 0 10 /**
 *
 */ 0 
364 0 0 15 452 1476 1051 0 0 1 6 param0 0 1535  
365 0 0 6 454 1350 0 0 53 /**
 * Returns the number of pointers in the set.
 */ 1 4 this 3 1535  
366 0 0 7 455 1479 861 0 46 /**
 * Returns the nth pointer of the set.
 */ 2 4 this 3 1535  1 n 1 1350  
367 0 0 6 457 1524 0 0 175 /**
 * Returns the nth pointer of the set, typecast to a TypedObject if possible.
 * If the pointer is not a TypedObject or if the cast cannot be made, returns
 * nullptr.
 */ 2 4 this 3 1535  1 n 1 1350  
368 0 0 7 459 1467 0 0 70 /**
 * Returns the actual type of the nth pointer, if it is known.
 */ 2 4 this 3 1535  1 n 1 1350  
369 0 0 6 460 1469 0 0 68 /**
 * Returns the type name of the nth pointer, if it is known.
 */ 2 4 this 3 1535  1 n 1 1350  
370 0 0 6 461 1403 0 0 197 /**
 * Returns the age of the nth pointer: the number of seconds elapsed between
 * the time it was allocated and the time it was added to this set via a call
 * to MemoryUsage::get_pointers().
 */ 2 4 this 3 1535  1 n 1 1350  
371 0 0 38 462 1421 0 0 0 2 4 this 3 1535  1 n 1 1350  
372 0 0 4 463 1420 0 0 39 /**
 * Empties the set of pointers.
 */ 1 4 this 3 1476  
373 0 0 4 464 1420 0 0 10 /**
 *
 */ 2 4 this 3 1535  3 out 1 1432  
374 0 0 7 467 1537 1064 0 10 /**
 *
 */ 0 
375 0 0 7 467 1537 1064 0 10 /**
 *
 */ 3 6 source 1 1538  5 start 1 1368  3 end 1 1368  
376 0 0 6 468 1537 0 0 391 /**
 * Starts the SubStream reading from the indicated source, with the first
 * character being the character at position "start" within the source, for
 * end - start total characters.  The character at "end" within the source
 * will never be read; this will appear to be EOF.
 *
 * If end is zero, it indicates that the ISubStream will continue until the
 * end of the source stream.
 */ 4 4 this 3 1537  6 source 1 1538  5 start 1 1368  3 end 1 1368  
377 0 0 6 469 1537 0 0 92 /**
 * Resets the SubStream to empty, but does not actually close the source
 * istream.
 */ 1 4 this 3 1537  
378 0 0 7 473 1540 1068 0 10 /**
 *
 */ 0 
379 0 0 7 473 1540 1068 0 10 /**
 *
 */ 4 4 dest 1 1541  5 start 1 1368  3 end 1 1368  6 append 5 1349  
380 0 0 6 474 1540 0 0 383 /**
 * Starts the SubStream reading from the indicated dest, with the first
 * character being the character at position "start" within the dest, for end
 * - start total characters.  The character at "end" within the dest will
 * never be read; this will appear to be EOF.
 *
 * If end is zero, it indicates that the OSubStream will continue until the
 * end of the dest stream.
 */ 5 4 this 3 1540  4 dest 1 1541  5 start 1 1368  3 end 1 1368  6 append 5 1349  
381 0 0 6 475 1540 0 0 90 /**
 * Resets the SubStream to empty, but does not actually close the dest
 * ostream.
 */ 1 4 this 3 1540  
382 0 0 7 479 1543 1072 0 10 /**
 *
 */ 0 
383 0 0 7 479 1543 1072 0 10 /**
 *
 */ 4 6 nested 1 1544  5 start 1 1368  3 end 1 1368  6 append 5 1349  
384 0 0 6 480 1543 0 0 278 /**
 * Starts the SubStream reading and writing from the indicated nested stream,
 * within the indicated range.  "end" is the first character outside of the
 * range.
 *
 * If end is zero, it indicates that the SubStream will continue until the end
 * of the nested stream.
 */ 5 4 this 3 1543  6 nested 1 1544  5 start 1 1368  3 end 1 1368  6 append 5 1349  
385 0 0 6 481 1543 0 0 92 /**
 * Resets the SubStream to empty, but does not actually close the nested
 * ostream.
 */ 1 4 this 3 1543  
386 0 0 7 484 1546 861 0 10 /**
 *
 */ 0 
387 0 0 6 485 1349 0 0 352 /**
 * Opens the named Multifile on disk for reading.  The Multifile index is read
 * in, and the list of subfiles becomes available; individual subfiles may
 * then be extracted or read, but the list of subfiles may not be modified.
 *
 * Also see the version of open_read() which accepts an istream.  Returns true
 * on success, false on failure.
 */ 3 4 this 3 1546  14 multifile_name 1 1510  6 offset 5 1368  
388 0 0 6 485 1349 0 0 333 /**
 * Opens an anonymous Multifile for reading using an istream.  There must be
 * seek functionality via seekg() and tellg() on the istream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */ 4 4 this 3 1546  16 multifile_stream 1 1538  12 owns_pointer 5 1349  6 offset 5 1368  
389 0 0 6 486 1349 0 0 420 /**
 * Opens the named Multifile on disk for writing.  If there already exists a
 * file by that name, it is truncated.  The Multifile is then prepared for
 * accepting a brand new set of subfiles, which will be written to the
 * indicated filename.  Individual subfiles may not be extracted or read.
 *
 * Also see the version of open_write() which accepts an ostream.  Returns
 * true on success, false on failure.
 */ 2 4 this 3 1546  14 multifile_name 1 1510  
390 0 0 6 486 1349 0 0 333 /**
 * Opens an anonymous Multifile for writing using an ostream.  There must be
 * seek functionality via seekp() and tellp() on the pstream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */ 3 4 this 3 1546  16 multifile_stream 1 1432  12 owns_pointer 5 1349  
391 0 0 6 487 1349 0 0 360 /**
 * Opens the named Multifile on disk for reading and writing.  If there
 * already exists a file by that name, its index is read.  Subfiles may be
 * added or removed, and the resulting changes will be written to the named
 * file.
 *
 * Also see the version of open_read_write() which accepts an iostream.
 * Returns true on success, false on failure.
 */ 2 4 this 3 1546  14 multifile_name 1 1510  
392 0 0 6 487 1349 0 0 365 /**
 * Opens an anonymous Multifile for reading and writing using an iostream.
 * There must be seek functionality via seekg()/seekp() and tellg()/tellp() on
 * the iostream.
 *
 * If owns_pointer is true, then the Multifile assumes ownership of the stream
 * pointer and will delete it when the multifile is closed, including if this
 * function returns false.
 */ 3 4 this 3 1546  16 multifile_stream 1 1547  12 owns_pointer 5 1349  
393 0 0 4 488 1420 0 0 169 /**
 * Closes the Multifile if it is open.  All changes are flushed to disk, and
 * the file becomes invalid for further operations until the next call to
 * open().
 */ 1 4 this 3 1546  
394 0 0 6 489 1510 0 0 69 /**
 * Returns the filename of the Multifile, if it is available.
 */ 1 4 this 3 1548  
395 0 0 4 490 1420 0 0 209 /**
 * Replaces the filename of the Multifile.  This is primarily used for
 * documentation purposes only; changing this name does not open the indicated
 * file.  See open_read() or open_write() for that.
 */ 2 4 this 3 1546  14 multifile_name 1 1510  
396 0 0 6 491 1349 0 0 154 /**
 * Returns true if the Multifile has been opened for read mode and there have
 * been no errors, and individual Subfile contents may be extracted.
 */ 1 4 this 3 1548  
397 0 0 6 492 1349 0 0 162 /**
 * Returns true if the Multifile has been opened for write mode and there have
 * been no errors, and Subfiles may be added or removed from the Multifile.
 */ 1 4 this 3 1548  
398 0 0 6 493 1349 0 0 118 /**
 * Returns true if the Multifile index is suboptimal and should be repacked.
 * Call repack() to achieve this.
 */ 1 4 this 3 1548  
399 0 0 6 494 1513 0 0 299 /**
 * Returns the modification timestamp of the overall Multifile.  This
 * indicates the most recent date at which subfiles were added or removed from
 * the Multifile.  Note that it is logically possible for an individual
 * subfile to have a more recent timestamp than the overall timestamp.
 */ 1 4 this 3 1548  
400 0 0 4 495 1420 0 0 206 /**
 * Changes the overall modification timestamp of the multifile.  Note that this
 * will be reset to the current time every time you modify a subfile.
 * Only set this if you know what you are doing!
 */ 2 4 this 3 1546  9 timestamp 1 1513  
401 0 0 4 496 1420 0 0 529 /**
 * Sets the flag indicating whether timestamps should be recorded within the
 * Multifile or not.  The default is true, indicating the Multifile will
 * record timestamps for the overall file and also for each subfile.
 *
 * If this is false, the Multifile will not record timestamps internally.  In
 * this case, the return value from get_timestamp() or get_subfile_timestamp()
 * will be estimations.
 *
 * You may want to set this false to minimize the bitwise difference between
 * independently-generated Multifiles.
 */ 2 4 this 3 1546  16 record_timestamp 1 1349  
402 0 0 6 497 1349 0 0 137 /**
 * Returns the flag indicating whether timestamps should be recorded within
 * the Multifile or not.  See set_record_timestamp().
 */ 1 4 this 3 1548  
403 0 0 4 498 1420 0 0 782 /**
 * Changes the internal scale factor for this Multifile.
 *
 * This is normally 1, but it may be set to any arbitrary value (greater than
 * zero) to support Multifile archives that exceed 4GB, if necessary.
 * (Individual subfiles may still not exceed 4GB.)
 *
 * All addresses within the file are rounded up to the next multiple of
 * _scale_factor, and zeros are written to the file to fill the resulting
 * gaps.  Then the address is divided by _scale_factor and written out as a
 * 32-bit integer.  Thus, setting a scale factor of 2 supports up to 8GB
 * files, 3 supports 12GB files, etc.
 *
 * Calling this function on an already-existing Multifile will have no
 * immediate effect until a future call to repack() or close() (or until the
 * Multifile is destructed).
 */ 2 4 this 3 1546  12 scale_factor 1 1350  
404 0 0 6 499 1350 0 0 92 /**
 * Returns the internal scale factor for this Multifile.  See
 * set_scale_factor().
 */ 1 4 this 3 1548  
405 0 0 4 500 1420 0 0 461 /**
 * Sets the flag indicating whether subsequently-added subfiles should be
 * encrypted before writing them to the multifile.  If true, subfiles will be
 * encrypted; if false (the default), they will be written without encryption.
 *
 * When true, subfiles will be encrypted with the password specified by
 * set_encryption_password().  It is possible to apply a different password to
 * different files, but the resulting file can't be mounted via VFS.
 */ 2 4 this 3 1546  4 flag 1 1349  
406 0 0 6 501 1349 0 0 163 /**
 * Returns the flag indicating whether subsequently-added subfiles should be
 * encrypted before writing them to the multifile.  See set_encryption_flag().
 */ 1 4 this 3 1548  
407 0 0 4 502 1420 0 0 369 /**
 * Specifies the password that will be used to encrypt subfiles subsequently
 * added to the multifile, if the encryption flag is also set true (see
 * set_encryption_flag()).
 *
 * It is possible to apply a different password to different files, but the
 * resulting file can't be mounted via VFS.  Changing this value may cause an
 * implicit call to flush().
 */ 2 4 this 3 1546  19 encryption_password 1 1469  
408 0 0 6 503 1469 0 0 141 /**
 * Returns the password that will be used to encrypt subfiles subsequently
 * added to the multifile.  See set_encryption_password().
 */ 1 4 this 3 1548  
409 0 0 4 504 1420 0 0 659 /**
 * Specifies the encryption algorithm that should be used for future calls to
 * add_subfile().  The default is whatever is specified by the encryption-
 * algorithm config variable.  The complete set of available algorithms is
 * defined by the current version of OpenSSL.
 *
 * If an invalid algorithm is specified, there is no immediate error return
 * code, but flush() will fail and the file will be invalid.
 *
 * It is possible to apply a different encryption algorithm to different
 * files, and unlike the password, this does not interfere with mounting the
 * multifile via VFS.  Changing this value may cause an implicit call to
 * flush().
 */ 2 4 this 3 1546  20 encryption_algorithm 1 1469  
410 0 0 6 505 1469 0 0 96 /**
 * Returns the encryption algorithm that was specified by
 * set_encryption_algorithm().
 */ 1 4 this 3 1548  
411 0 0 4 506 1420 0 0 611 /**
 * Specifies the length of the key, in bits, that should be used to encrypt
 * the stream in future calls to add_subfile().  The default is whatever is
 * specified by the encryption-key-length config variable.
 *
 * If an invalid key_length for the chosen algorithm is specified, there is no
 * immediate error return code, but flush() will fail and the file will be
 * invalid.
 *
 * It is possible to apply a different key length to different files, and
 * unlike the password, this does not interfere with mounting the multifile
 * via VFS. Changing this value may cause an implicit call to flush().
 */ 2 4 this 3 1546  21 encryption_key_length 1 1353  
412 0 0 6 507 1353 0 0 108 /**
 * Returns the encryption key length, in bits, that was specified by
 * set_encryption_key_length().
 */ 1 4 this 3 1548  
413 0 0 4 508 1420 0 0 740 /**
 * Specifies the number of times to repeatedly hash the key before writing it
 * to the stream in future calls to add_subfile().  Its purpose is to make it
 * computationally more expensive for an attacker to search the key space
 * exhaustively.  This should be a multiple of 1,000 and should not exceed
 * about 65 million; the value 0 indicates just one application of the hashing
 * algorithm.
 *
 * The default is whatever is specified by the multifile-encryption-iteration-
 * count config variable.
 *
 * It is possible to apply a different iteration count to different files, and
 * unlike the password, this does not interfere with mounting the multifile
 * via VFS.  Changing this value causes an implicit call to flush().
 */ 2 4 this 3 1546  26 encryption_iteration_count 1 1353  
414 0 0 6 509 1353 0 0 84 /**
 * Returns the value that was specified by set_encryption_iteration_count().
 */ 1 4 this 3 1548  
415 0 0 6 510 1469 0 0 618 /**
 * Adds a file on disk as a subfile to the Multifile.  The file named by
 * filename will be read and added to the Multifile at the next call to
 * flush().  If there already exists a subfile with the indicated name, it is
 * replaced without examining its contents (but see also update_subfile).
 *
 * Either Filename:::set_binary() or set_text() must have been called
 * previously to specify the nature of the source file.  If set_text() was
 * called, the text flag will be set on the subfile.
 *
 * Returns the subfile name on success (it might have been modified slightly),
 * or empty string on failure.
 */ 4 4 this 3 1546  12 subfile_name 1 1469  8 filename 1 1510  17 compression_level 1 1353  
416 0 0 6 510 1469 0 0 777 /**
 * Adds a file from a stream as a subfile to the Multifile.  The indicated
 * istream will be read and its contents added to the Multifile at the next
 * call to flush(). The file will be added as a binary subfile.
 *
 * Note that the istream must remain untouched and unused by any other code
 * until flush() is called.  At that time, the Multifile will read the entire
 * contents of the istream from the current file position to the end of the
 * file.  Subsequently, the Multifile will *not* close or delete the istream.
 * It is the caller's responsibility to ensure that the istream pointer does
 * not destruct during the lifetime of the Multifile.
 *
 * Returns the subfile name on success (it might have been modified slightly),
 * or empty string on failure.
 */ 4 4 this 3 1546  12 subfile_name 1 1469  12 subfile_data 1 1530  17 compression_level 1 1353  
417 0 0 6 511 1469 0 0 451 /**
 * Adds a file on disk to the subfile.  If a subfile already exists with the
 * same name, its contents are compared byte-for-byte to the disk file, and it
 * is replaced only if it is different; otherwise, the multifile is left
 * unchanged.
 *
 * Either Filename:::set_binary() or set_text() must have been called
 * previously to specify the nature of the source file.  If set_text() was
 * called, the text flag will be set on the subfile.
 */ 4 4 this 3 1546  12 subfile_name 1 1469  8 filename 1 1510  17 compression_level 1 1353  
418 0 0 6 512 1349 0 0 1292 /**
 * Adds a new signature to the Multifile.  This signature associates the
 * indicated certificate with the current contents of the Multifile.  When the
 * Multifile is read later, the signature will still be present only if the
 * Multifile is unchanged; any subsequent changes to the Multifile will
 * automatically invalidate and remove the signature.
 *
 * The chain filename may be empty if the certificate does not require an
 * authenticating certificate chain (e.g.  because it is self-signed).
 *
 * The specified private key must match the certificate, and the Multifile
 * must be open in read-write mode.  The private key is only used for
 * generating the signature; it is not written to the Multifile and cannot be
 * retrieved from the Multifile later.  (However, the certificate *can* be
 * retrieved from the Multifile later, to identify the entity that created the
 * signature.)
 *
 * This implicitly causes a repack() operation if one is needed.  Returns true
 * on success, false on failure.
 *
 * This flavor of add_signature() reads the certificate and private key from a
 * PEM-formatted file, for instance as generated by the openssl command.  If
 * the private key file is password-encrypted, the third parameter will be
 * used as the password to decrypt it.
 */ 5 4 this 3 1546  11 certificate 1 1510  5 chain 1 1510  4 pkey 1 1510  8 password 5 1469  
419 0 0 6 512 1349 0 0 724 /**
 * Adds a new signature to the Multifile.  This signature associates the
 * indicated certificate with the current contents of the Multifile.  When the
 * Multifile is read later, the signature will still be present only if the
 * Multifile is unchanged; any subsequent changes to the Multifile will
 * automatically invalidate and remove the signature.
 *
 * This flavor of add_signature() reads the certificate, private key, and
 * certificate chain from the same PEM-formatted file.  It takes the first
 * private key found as the intended key, and then uses the first certificate
 * found that matches that key as the signing certificate.  Any other
 * certificates in the file are taken to be part of the chain.
 */ 3 4 this 3 1546  9 composite 1 1510  8 password 5 1469  
420 0 0 6 513 1353 0 0 555 /**
 * Returns the number of matching signatures found on the Multifile.  These
 * signatures may be iterated via get_signature() and related methods.
 *
 * A signature on this list is guaranteed to match the Multifile contents,
 * proving that the Multifile has been unmodified since the signature was
 * applied.  However, this does not guarantee that the certificate itself is
 * actually from who it says it is from; only that it matches the Multifile
 * contents.  See validate_signature_certificate() to authenticate a
 * particular certificate.
 */ 1 4 this 3 1548  
421 0 0 6 514 1469 0 0 352 /**
 * Returns the "subject name" for the nth signature found on the Multifile.
 * This is a string formatted according to RFC2253 that should more-or-less
 * identify a particular certificate; when paired with the public key (see
 * get_signature_public_key()), it can uniquely identify a certificate.  See
 * the comments in get_num_signatures().
 */ 2 4 this 3 1548  1 n 1 1353  
422 0 0 6 515 1469 0 0 299 /**
 * Returns a "friendly name" for the nth signature found on the Multifile.
 * This attempts to extract out the most meaningful part of the subject name.
 * It returns the emailAddress, if it is defined; otherwise, it returns the
 * commonName.
 *
 * See the comments in get_num_signatures().
 */ 2 4 this 3 1548  1 n 1 1353  
423 0 0 6 516 1469 0 0 379 /**
 * Returns the public key used for the nth signature found on the Multifile.
 * This is encoded in DER form and returned as a string of hex digits.
 *
 * This can be used, in conjunction with the subject name (see
 * get_signature_subject_name()), to uniquely identify a particular
 * certificate and its subsequent reissues.  See the comments in
 * get_num_signatures().
 */ 2 4 this 3 1548  1 n 1 1353  
424 0 0 4 517 1420 0 0 158 /**
 * Writes the certificate for the nth signature, in user-readable verbose
 * form, to the indicated stream.  See the comments in get_num_signatures().
 */ 3 4 this 3 1548  1 n 1 1353  3 out 1 1432  
425 0 0 4 518 1420 0 0 140 /**
 * Writes the certificate for the nth signature, in PEM form, to the indicated
 * stream.  See the comments in get_num_signatures().
 */ 3 4 this 3 1548  1 n 1 1353  3 out 1 1432  
426 0 0 6 519 1353 0 0 289 /**
 * Checks that the certificate used for the nth signature is a valid,
 * authorized certificate with some known certificate authority.  Returns 0 if
 * it is valid, -1 if there is some error, or the corresponding OpenSSL error
 * code if it is invalid, out-of-date, or self-signed.
 */ 2 4 this 3 1548  1 n 1 1353  
427 0 0 6 520 1349 0 0 691 /**
 * Writes all contents of the Multifile to disk.  Until flush() is called,
 * add_subfile() and remove_subfile() do not actually do anything to disk.  At
 * this point, all of the recently-added subfiles are read and their contents
 * are added to the end of the Multifile, and the recently-removed subfiles
 * are marked gone from the Multifile.
 *
 * This may result in a suboptimal index.  To guarantee that the index is
 * written at the beginning of the file, call repack() instead of flush().
 *
 * It is not necessary to call flush() explicitly unless you are concerned
 * about reading the recently-added subfiles immediately.
 *
 * Returns true on success, false on failure.
 */ 1 4 this 3 1546  
428 0 0 6 521 1349 0 0 587 /**
 * Forces a complete rewrite of the Multifile and all of its contents, so that
 * its index will appear at the beginning of the file with all of the subfiles
 * listed in alphabetical order.  This is considered optimal for reading, and
 * is the standard configuration; but it is not essential to do this.
 *
 * It is only valid to call this if the Multifile was opened using
 * open_read_write() and an explicit filename, rather than an iostream.  Also,
 * we must have write permission to the directory containing the Multifile.
 *
 * Returns true on success, false on failure.
 */ 1 4 this 3 1546  
429 0 0 6 522 1353 0 0 169 /**
 * Returns the number of subfiles within the Multifile.  The subfiles may be
 * accessed in alphabetical order by iterating through [0 ..
 * get_num_subfiles()).
 */ 1 4 this 3 1548  
430 0 0 6 523 1353 0 0 127 /**
 * Returns the index of the subfile with the indicated name, or -1 if the
 * named subfile is not within the Multifile.
 */ 2 4 this 3 1548  12 subfile_name 1 1469  
431 0 0 6 524 1349 0 0 203 /**
 * Returns true if the indicated subfile name is the directory prefix to one
 * or more files within the Multifile.  That is, the Multifile contains at
 * least one file named "subfile_name/...".
 */ 2 4 this 3 1548  12 subfile_name 1 1469  
432 0 0 6 525 1349 0 0 420 /**
 * Considers subfile_name to be the name of a subdirectory within the
 * Multifile, but not a file itself; fills the given vector up with the sorted
 * list of subdirectories or files within the named directory.
 *
 * Note that directories do not exist explicitly within a Multifile; this just
 * checks for the existence of files with the given initial prefix.
 *
 * Returns true if successful, false otherwise.
 */ 3 4 this 3 1548  8 contents 1 1550  12 subfile_name 1 1469  
433 0 0 4 526 1420 0 0 435 /**
 * Removes the nth subfile from the Multifile.  This will cause all subsequent
 * index numbers to decrease by one.  The file will not actually be removed
 * from the disk until the next call to flush().
 *
 * Note that this does not actually remove the data from the indicated
 * subfile; it simply removes it from the index.  The Multifile will not be
 * reduced in size after this operation, until the next call to repack().
 */ 2 4 this 3 1546  5 index 1 1353  
434 0 0 6 526 1349 0 0 475 /**
 * Removes the named subfile from the Multifile, if it exists; returns true if
 * successfully removed, or false if it did not exist in the first place.  The
 * file will not actually be removed from the disk until the next call to
 * flush().
 *
 * Note that this does not actually remove the data from the indicated
 * subfile; it simply removes it from the index.  The Multifile will not be
 * reduced in size after this operation, until the next call to repack().
 */ 2 4 this 3 1546  12 subfile_name 1 1469  
435 0 0 6 527 1469 0 0 47 /**
 * Returns the name of the nth subfile.
 */ 2 4 this 3 1548  5 index 1 1353  
436 0 0 6 529 1350 0 0 170 /**
 * Returns the uncompressed data length of the nth subfile.  This might return
 * 0 if the subfile has recently been added and flush() has not yet been
 * called.
 */ 2 4 this 3 1548  5 index 1 1353  
437 0 0 6 530 1513 0 0 270 /**
 * Returns the modification time of the nth subfile.  If this is called on an
 * older .mf file, which did not store individual timestamps in the file (or
 * if get_record_timestamp() is false), this will return the modification time
 * of the overall multifile.
 */ 2 4 this 3 1548  5 index 1 1353  
438 0 0 6 531 1349 0 0 120 /**
 * Returns true if the indicated subfile has been compressed when stored
 * within the archive, false otherwise.
 */ 2 4 this 3 1548  5 index 1 1353  
439 0 0 6 532 1349 0 0 119 /**
 * Returns true if the indicated subfile has been encrypted when stored within
 * the archive, false otherwise.
 */ 2 4 this 3 1548  5 index 1 1353  
440 0 0 6 533 1349 0 0 343 /**
 * Returns true if the indicated subfile represents text data, or false if it
 * represents binary data.  If the file is text data, it may have been
 * processed by end-of-line conversion when it was added.  (But the actual
 * bits in the multifile will represent the standard Unix end-of-line
 * convention, e.g.  \n instead of \r\n.)
 */ 2 4 this 3 1548  5 index 1 1353  
441 0 0 6 534 1368 0 0 371 /**
 * Returns the first byte that is guaranteed to follow any index byte already
 * written to disk in the Multifile.
 *
 * This number is largely meaningless in many cases, but if needs_repack() is
 * false, and the file is flushed, this will indicate the number of bytes in
 * the header + index.  Everything at this byte position and later will be
 * actual data.
 */ 1 4 this 3 1548  
442 0 0 6 535 1368 0 0 338 /**
 * Returns the starting byte position within the Multifile at which the
 * indicated subfile begins.  This may be used, with
 * get_subfile_internal_length(), for low-level access to the subfile, but
 * usually it is better to use open_read_subfile() instead (which
 * automatically decrypts and/or uncompresses the subfile data).
 */ 2 4 this 3 1548  5 index 1 1353  
443 0 0 6 536 1350 0 0 322 /**
 * Returns the number of bytes the indicated subfile consumes within the
 * archive.  For compressed subfiles, this will generally be smaller than
 * get_subfile_length(); for encrypted (but noncompressed) subfiles, it may be
 * slightly different, for noncompressed and nonencrypted subfiles, it will be
 * equal.
 */ 2 4 this 3 1548  5 index 1 1353  
444 0 0 6 537 1490 0 0 96 /**
 * Returns a vector_uchar that contains the entire contents of the indicated
 * subfile.
 */ 2 4 this 3 1546  5 index 1 1353  
445 0 0 6 538 1530 0 0 745 /**
 * Returns an istream that may be used to read the indicated subfile.  You may
 * seek() within this istream to your heart's content; even though it will be
 * a reference to the already-opened pfstream of the Multifile itself, byte 0
 * appears to be the beginning of the subfile and EOF appears to be the end of
 * the subfile.
 *
 * The returned istream will have been allocated via new; you should pass the
 * pointer to close_read_subfile() when you are finished with it to delete it
 * and release its resources.
 *
 * Any future calls to repack() or close() (or the Multifile destructor) will
 * invalidate all currently open subfile pointers.
 *
 * The return value will be NULL if the stream cannot be opened for some
 * reason.
 */ 2 4 this 3 1546  5 index 1 1353  
446 0 0 4 539 1420 0 0 246 /**
 * Closes a file opened by a previous call to open_read_subfile().  This
 * really just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 1 6 stream 1 1530  
447 0 0 6 540 1349 0 0 68 /**
 * Extracts the nth subfile into a file with the given name.
 */ 3 4 this 3 1546  5 index 1 1353  8 filename 1 1510  
448 0 0 6 541 1349 0 0 61 /**
 * Extracts the nth subfile to the indicated ostream.
 */ 3 4 this 3 1546  5 index 1 1353  3 out 1 1432  
449 0 0 6 542 1349 0 0 509 /**
 * Performs a byte-for-byte comparison of the indicated file on disk with the
 * nth subfile.  Returns true if the files are equivalent, or false if they
 * are different (or the file is missing).
 *
 * If Filename::set_binary() or set_text() has already been called, it
 * specifies the nature of the source file.  If this is different from the
 * text flag of the subfile, the comparison will always return false.  If this
 * has not been specified, it will be set from the text flag of the subfile.
 */ 3 4 this 3 1546  5 index 1 1353  8 filename 1 1510  
450 0 0 4 543 1420 0 0 10 /**
 *
 */ 2 4 this 3 1548  3 out 1 1432  
451 0 0 4 544 1420 0 0 61 /**
 * Shows a list of all subfiles within the Multifile.
 */ 2 4 this 3 1548  3 out 5 1432  
452 0 0 6 545 1469 0 0 108 /**
 * Returns a string with the first n bytes written to a Multifile, to identify
 * it as a Multifile.
 */ 0 
453 0 0 4 549 1420 0 0 644 /**
 * Sets the string which is written to the Multifile before the Multifile
 * header.  This string must begin with a hash mark and end with a newline
 * character; and if it includes embedded newline characters, each one must be
 * followed by a hash mark.  If these conditions are not initially true, the
 * string will be modified as necessary to make it so.
 *
 * This is primarily useful as a simple hack to allow p3d applications to be
 * run directly from the command line on Unix-like systems.
 *
 * The return value is true if successful, or false on failure (for instance,
 * because the header prefix violates the above rules).
 */ 2 4 this 3 1546  13 header_prefix 1 1469  
454 0 0 6 550 1469 0 0 113 /**
 * Returns the string that preceded the Multifile header on the file, if any.
 * See set_header_prefix().
 */ 1 4 this 3 1548  
455 0 0 15 552 1555 1143 0 0 1 6 param0 0 1553  
456 0 0 7 552 1555 1143 0 10 /**
 *
 */ 1 12 initial_name 5 1469  
457 0 0 4 553 1420 0 0 10 /**
 *
 */ 2 4 this 3 1555  4 name 1 1469  
458 0 0 4 554 1420 0 0 46 /**
 * Resets the Namable's name to empty.
 */ 1 4 this 3 1555  
459 0 0 6 555 1349 0 0 94 /**
 * Returns true if the Namable has a nonempty name set, false if the name is
 * empty.
 */ 1 4 this 3 1553  
460 0 0 6 556 1469 0 0 10 /**
 *
 */ 1 4 this 3 1553  
461 0 0 4 559 1420 0 0 148 /**
 * Outputs the Namable.  This function simply writes the name to the output
 * stream; most Namable derivatives will probably redefine this.
 */ 2 4 this 3 1553  3 out 1 1432  
462 0 0 7 560 1467 0 0 0 0 
463 0 0 4 563 1420 0 0 200 /**
 * Removes all the certificates from the global store, including the compiled-
 * in certificates loaded from ca_bundle_data.c.  You can add new certificates
 * by calling load_certificates().
 */ 1 4 this 3 1556  
464 0 0 6 564 1353 0 0 436 /**
 * Reads the PEM-formatted certificate(s) (delimited by -----BEGIN
 * CERTIFICATE----- and -----END CERTIFICATE-----) from the indicated file and
 * adds them to the global store object, retrieved via get_x509_store().
 *
 * Returns the number of certificates read on success, or 0 on failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */ 2 4 this 3 1556  8 filename 1 1510  
465 0 0 6 565 1353 0 0 376 /**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be PEM-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */ 3 4 this 3 1556  4 data 1 1469  9 data_size 1 1350  
466 0 0 6 565 1353 0 0 376 /**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be PEM-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */ 2 4 this 3 1556  4 data 1 1469  
467 0 0 6 566 1353 0 0 376 /**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be DER-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */ 3 4 this 3 1556  4 data 1 1469  9 data_size 1 1350  
468 0 0 6 566 1353 0 0 376 /**
 * Reads a chain of trusted certificates from the indicated data buffer and
 * adds them to the X509_STORE object.  The data buffer should be DER-
 * formatted.  Returns the number of certificates read on success, or 0 on
 * failure.
 *
 * You should call this only with trusted, locally-stored certificates; not
 * with certificates received from an untrusted source.
 */ 2 4 this 3 1556  4 data 1 1469  
469 0 0 6 567 1557 0 0 378 /**
 * Returns the global X509_STORE object.
 *
 * It has to be a global object, because OpenSSL seems to store some global
 * pointers associated with this object whether you want it to or not, and
 * keeping independent copies of a local X509_STORE object doesn't seem to
 * work that well.  So, we have one store that keeps all certificates the
 * application might need.
 */ 1 4 this 3 1556  
470 0 0 4 568 1420 0 0 277 /**
 * A convenience function that is itself a wrapper around the OpenSSL
 * convenience function to output the recent OpenSSL errors.  This function
 * sends the error string to express_cat.warning().  If REPORT_OPENSSL_ERRORS
 * is not defined, the function does nothing.
 */ 1 4 this 3 1556  
471 0 0 4 569 1420 0 0 84 /**
 * As notify_ssl_errors(), but sends the output to debug instead of warning.
 */ 1 4 this 3 1556  
472 0 0 6 570 1556 0 0 10 /**
 *
 */ 0 
473 0 0 7 572 1509 1160 0 10 /**
 *
 */ 0 
474 0 0 7 572 1509 1160 0 10 /**
 *
 */ 3 4 file 1 1364  5 start 1 1368  4 size 1 1560  
475 0 0 7 572 1509 1160 0 10 /**
 *
 */ 3 8 filename 1 1510  5 start 1 1368  4 size 1 1560  
476 0 0 15 572 1509 1160 0 10 /**
 *
 */ 1 4 copy 1 1520  
477 0 0 6 573 1509 0 0 0 2 4 this 3 1509  4 copy 1 1520  
478 0 0 6 574 1349 0 0 98 /**
 * Returns true if this SubfileInfo doesn't define any file, false if it has
 * real data.
 */ 1 4 this 3 1520  
479 0 0 7 575 1364 0 0 63 /**
 * Returns the FileReference that represents this file.
 */ 1 4 this 3 1520  
480 0 0 6 576 1510 0 0 38 /**
 * A shortcut to the filename.
 */ 1 4 this 3 1520  
481 0 0 6 577 1368 0 0 77 /**
 * Returns the offset within the file at which this file data begins.
 */ 1 4 this 3 1520  
482 0 0 6 578 1560 0 0 115 /**
 * Returns the number of consecutive bytes, beginning at get_start(), that
 * correspond to this file data.
 */ 1 4 this 3 1520  
483 0 0 4 579 1420 0 0 10 /**
 *
 */ 2 4 this 3 1520  3 out 1 1432  
484 0 0 6 582 1563 0 0 0 1 4 this 3 1561  
485 0 0 7 583 1564 0 0 0 1 4 this 3 1561  
486 0 0 6 584 1510 0 0 166 /**
 * Returns the original filename as it was used to locate this VirtualFile.
 * This is usually, but not always, the same string returned by
 * get_filename().
 */ 1 4 this 3 1561  
487 0 0 6 585 1349 0 0 61 /**
 * Returns true if this file exists, false otherwise.
 */ 1 4 this 3 1561  
488 0 0 6 586 1349 0 0 117 /**
 * Returns true if this file represents a directory (and scan_directory() may
 * be called), false otherwise.
 */ 1 4 this 3 1561  
489 0 0 6 587 1349 0 0 115 /**
 * Returns true if this file represents a regular file (and read_file() may be
 * called), false otherwise.
 */ 1 4 this 3 1561  
490 0 0 6 588 1349 0 0 153 /**
 * Returns true if this file may be written to, which implies write_file() may
 * be called (unless it is a directory instead of a regular file).
 */ 1 4 this 3 1561  
491 0 0 6 589 1349 0 0 199 /**
 * Attempts to delete this file or directory.  This can remove a single file
 * or an empty directory.  It will not remove a nonempty directory.  Returns
 * true on success, false on failure.
 */ 1 4 this 3 1515  
492 0 0 6 590 1349 0 0 589 /**
 * Attempts to move or rename this file or directory.  If the original file is
 * an ordinary file, it will quietly replace any already-existing file in the
 * new filename (but not a directory).  If the original file is a directory,
 * the new filename must not already exist.
 *
 * If the file is a directory, the new filename must be within the same mount
 * point.  If the file is an ordinary file, the new filename may be anywhere;
 * but if it is not within the same mount point then the rename operation is
 * automatically performed as a two-step copy-and-delete operation.
 */ 2 4 this 3 1515  8 new_file 1 1515  
493 0 0 6 591 1349 0 0 123 /**
 * Attempts to copy the contents of this file to the indicated file.  Returns
 * true on success, false on failure.
 */ 2 4 this 3 1515  8 new_file 1 1515  
494 0 0 7 592 1565 861 0 247 /**
 * If the file represents a directory (that is, is_directory() returns true),
 * this returns the list of files within the directory at the current time.
 * Returns NULL if the file is not a directory or if the directory cannot be
 * read.
 */ 1 4 this 3 1561  
495 0 0 4 593 1420 0 0 10 /**
 *
 */ 2 4 this 3 1561  3 out 1 1432  
496 0 0 4 594 1420 0 0 66 /**
 * If the file represents a directory, lists its contents.
 */ 2 4 this 3 1561  3 out 5 1432  
497 0 0 4 595 1420 0 0 113 /**
 * If the file represents a directory, recursively lists its contents and
 * those of all subdirectories.
 */ 2 4 this 3 1561  3 out 5 1432  
498 0 0 38 596 1421 0 0 63 /**
 * Returns the entire contents of the file as a string.
 */ 2 4 this 3 1561  11 auto_unwrap 1 1349  
499 0 0 6 597 1530 0 0 179 /**
 * Opens the file for reading.  Returns a newly allocated istream on success
 * (which you should eventually delete when you are done reading). Returns
 * NULL on failure.
 */ 2 4 this 3 1561  11 auto_unwrap 1 1349  
500 0 0 4 598 1420 0 0 243 /**
 * Closes a file opened by a previous call to open_read_file().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 2 4 this 3 1561  6 stream 1 1530  
501 0 0 6 599 1349 0 0 324 /**
 * Call this method after a reading the istream returned by open_read_file()
 * to completion.  If it returns true, the file was read completely and
 * without error; if it returns false, there may have been some errors or a
 * truncated file read.  This is particularly likely if the stream is a
 * VirtualFileHTTP.
 */ 1 4 this 3 1561  
502 0 0 38 600 1421 0 0 0 3 4 this 3 1515  4 data 1 1421  9 auto_wrap 1 1349  
503 0 0 6 601 1432 0 0 179 /**
 * Opens the file for writing.  Returns a newly allocated ostream on success
 * (which you should eventually delete when you are done writing). Returns
 * NULL on failure.
 */ 3 4 this 3 1515  9 auto_wrap 1 1349  8 truncate 1 1349  
504 0 0 6 602 1432 0 0 180 /**
 * Works like open_write_file(), but the file is opened in append mode.  Like
 * open_write_file, the returned pointer should eventually be passed to
 * close_write_file().
 */ 1 4 this 3 1515  
505 0 0 4 603 1420 0 0 244 /**
 * Closes a file opened by a previous call to open_write_file().  This really
 * just deletes the ostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 2 4 this 3 1515  6 stream 1 1432  
506 0 0 6 604 1547 0 0 180 /**
 * Opens the file for writing.  Returns a newly allocated iostream on success
 * (which you should eventually delete when you are done writing). Returns
 * NULL on failure.
 */ 2 4 this 3 1515  8 truncate 1 1349  
507 0 0 6 605 1547 0 0 194 /**
 * Works like open_read_write_file(), but the file is opened in append mode.
 * Like open_read_write_file, the returned pointer should eventually be passed
 * to close_read_write_file().
 */ 1 4 this 3 1515  
508 0 0 4 606 1420 0 0 250 /**
 * Closes a file opened by a previous call to open_read_write_file().  This
 * really just deletes the iostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 2 4 this 3 1515  6 stream 1 1547  
509 0 0 6 607 1560 0 0 105 /**
 * Returns the current size on disk (or wherever it is) of the file before it
 * has been opened.
 */ 1 4 this 3 1561  
510 0 0 6 607 1560 0 0 222 /**
 * Returns the current size on disk (or wherever it is) of the already-open
 * file.  Pass in the stream that was returned by open_read_file(); some
 * implementations may require this stream to determine the size.
 */ 2 4 this 3 1561  6 stream 1 1530  
511 0 0 6 608 1513 0 0 434 /**
 * Returns a time_t value that represents the time the file was last modified,
 * to within whatever precision the operating system records this information
 * (on a Windows95 system, for instance, this may only be accurate to within 2
 * seconds).
 *
 * If the timestamp cannot be determined, either because it is not supported
 * by the operating system or because there is some error (such as file not
 * found), returns 0.
 */ 1 4 this 3 1561  
512 0 0 6 609 1349 0 0 330 /**
 * Populates the SubfileInfo structure with the data representing where the
 * file actually resides on disk, if this is knowable.  Returns true if the
 * file might reside on disk, and the info is populated, or false if it does
 * not (or it is not known where the file resides), in which case the info is
 * meaningless.
 */ 2 4 this 3 1515  4 info 1 1509  
513 0 0 7 610 1467 0 0 0 0 
514 0 0 7 613 1467 0 0 0 0 
515 0 0 6 616 1563 0 0 68 /**
 * Returns the file system this mount object is attached to.
 */ 1 4 this 3 1566  
516 0 0 6 617 1510 0 0 161 /**
 * Returns the name of the directory within the virtual file system that this
 * mount object is attached to.  This directory name will end with a slash.
 */ 1 4 this 3 1566  
517 0 0 6 618 1353 0 0 100 /**
 * Returns the set of flags passed by the user to the
 * VirtualFileSystem::mount() command.
 */ 1 4 this 3 1566  
518 0 0 4 619 1420 0 0 10 /**
 *
 */ 2 4 this 3 1566  3 out 1 1432  
519 0 0 4 620 1420 0 0 10 /**
 *
 */ 2 4 this 3 1566  3 out 1 1432  
520 0 0 7 621 1467 0 0 0 0 
521 0 0 23 623 1568 994 0 10 /**
 *
 */ 1 9 multifile 1 1546  
522 0 0 7 624 1546 861 0 76 /**
 * Returns the Multifile pointer that this mount object is based on.
 */ 1 4 this 3 1569  
523 0 0 7 625 1467 0 0 0 0 
524 0 0 7 627 1571 1204 0 10 /**
 *
 */ 0 
525 0 0 7 628 1467 0 0 0 0 
526 0 0 23 631 1572 1208 0 10 /**
 *
 */ 1 17 physical_filename 1 1510  
527 0 0 6 632 1510 0 0 112 /**
 * Returns the name of the source file on the OS filesystem of the directory
 * or file that is mounted.
 */ 1 4 this 3 1573  
528 0 0 7 633 1467 0 0 0 0 
529 0 0 7 636 1577 994 0 69 /**
 * Returns the VirtualFileMount this file is associated with.
 */ 1 4 this 3 1575  
530 0 0 6 637 1349 0 0 174 /**
 * Returns true if this file is a .pz file that should be implicitly
 * decompressed on load, or false if it is not a .pz file or if it should not
 * be decompressed.
 */ 1 4 this 3 1575  
531 0 0 7 638 1467 0 0 0 0 
532 0 0 7 641 1578 998 0 10 /**
 *
 */ 1 8 filename 1 1510  
533 0 0 15 641 1578 998 0 0 1 6 param0 0 1579  
534 0 0 7 642 1467 0 0 0 0 
535 0 0 7 644 1581 1218 0 10 /**
 *
 */ 0 
536 0 0 7 644 1581 1218 0 10 /**
 *
 */ 2 6 source 1 1530  11 owns_source 1 1349  
537 0 0 6 645 1581 0 0 10 /**
 *
 */ 3 4 this 3 1581  6 source 1 1530  11 owns_source 1 1349  
538 0 0 6 646 1581 0 0 118 /**
 * Resets the ZStream to empty, but does not actually close the source istream
 * unless owns_source was true.
 */ 1 4 this 3 1581  
539 0 0 7 649 1582 1222 0 10 /**
 *
 */ 0 
540 0 0 7 649 1582 1222 0 10 /**
 *
 */ 3 4 dest 1 1432  9 owns_dest 1 1349  17 compression_level 5 1353  
541 0 0 6 650 1582 0 0 10 /**
 *
 */ 4 4 this 3 1582  4 dest 1 1432  9 owns_dest 1 1349  17 compression_level 5 1353  
542 0 0 6 651 1582 0 0 114 /**
 * Resets the ZStream to empty, but does not actually close the dest ostream
 * unless owns_dest was true.
 */ 1 4 this 3 1582  
543 0 0 15 661 1565 861 0 0 1 6 param0 0 1583  
544 0 0 6 654 1350 0 0 51 /**
 * Returns the number of files in the list.
 */ 1 4 this 3 1583  
545 0 0 7 655 1515 1190 0 44 /**
 * Returns the nth file in the list.
 */ 2 4 this 3 1583  1 n 1 1350  
546 0 0 7 657 1515 1190 0 44 /**
 * Returns the nth file in the list.
 */ 2 4 this 3 1583  1 n 1 1350  
547 0 0 6 658 1350 0 0 51 /**
 * Returns the number of files in the list.
 */ 1 4 this 3 1583  
548 0 0 7 659 1565 861 0 0 2 4 this 3 1565  5 other 1 1583  
549 0 0 7 660 1565 861 0 0 2 4 this 3 1583  5 other 1 1583  
550 0 0 7 663 1563 1231 0 10 /**
 *
 */ 0 
551 0 0 6 666 1349 0 0 1009 /**
 * Mounts the indicated system file or directory at the given mount point.  If
 * the named file is a directory, mounts the directory.  If the named file is
 * a Multifile, mounts it as a Multifile.  Returns true on success, false on
 * failure.
 *
 * A given system directory may be mounted to multiple different mount point,
 * and the same mount point may share multiple system directories.  In the
 * case of ambiguities (that is, two different files with exactly the same
 * full pathname), the most-recently mounted system wins.
 *
 * The filename specified as the first parameter must refer to a real,
 * physical filename on disk; it cannot be a virtual file already appearing
 * within the vfs filespace.  However, it is possible to mount such a file;
 * see mount_loop() for this.
 *
 * Note that a mounted VirtualFileSystem directory is fully case-sensitive,
 * unlike the native Windows file system, so you must refer to files within
 * the virtual file system with exactly the right case.
 */ 5 4 this 3 1563  17 physical_filename 1 1510  11 mount_point 1 1510  5 flags 1 1353  8 password 5 1469  
552 0 0 6 666 1349 0 0 67 /**
 * Mounts the indicated Multifile at the given mount point.
 */ 4 4 this 3 1563  9 multifile 1 1546  11 mount_point 1 1510  5 flags 1 1353  
553 0 0 6 666 1349 0 0 208 /**
 * Adds the given VirtualFileMount object to the mount list.  This is a lower-
 * level function than the other flavors of mount(); it requires you to create
 * a VirtualFileMount object specifically.
 */ 4 4 this 3 1563  5 mount 1 1577  11 mount_point 1 1510  5 flags 1 1353  
554 0 0 6 667 1349 0 0 587 /**
 * This is similar to mount(), but it receives the name of a Multifile that
 * already appears within the virtual file system.  It can be used to mount a
 * Multifile that is itself hosted within a virtually-mounted Multifile.
 *
 * This interface can also be used to mount physical files (that appear within
 * the virtual filespace), but it cannot be used to mount directories.  Use
 * mount() if you need to mount a directory.
 *
 * Note that there is additional overhead, in the form of additional buffer
 * copies of the data, for recursively mounting a multifile like this.
 */ 5 4 this 3 1563  16 virtual_filename 1 1510  11 mount_point 1 1510  5 flags 1 1353  8 password 5 1469  
555 0 0 6 668 1353 0 0 156 /**
 * Unmounts all appearances of the indicated directory name or multifile name
 * from the file system.  Returns the number of appearances unmounted.
 */ 2 4 this 3 1563  17 physical_filename 1 1510  
556 0 0 6 668 1353 0 0 132 /**
 * Unmounts all appearances of the indicated Multifile from the file system.
 * Returns the number of appearances unmounted.
 */ 2 4 this 3 1563  9 multifile 1 1546  
557 0 0 6 668 1353 0 0 127 /**
 * Unmounts the indicated VirtualFileMount object from the file system.
 * Returns the number of appearances unmounted.
 */ 2 4 this 3 1563  5 mount 1 1577  
558 0 0 6 669 1353 0 0 136 /**
 * Unmounts all systems attached to the given mount point from the file
 * system.  Returns the number of appearances unmounted.
 */ 2 4 this 3 1563  11 mount_point 1 1510  
559 0 0 6 670 1353 0 0 96 /**
 * Unmounts all files from the file system.  Returns the number of systems
 * unmounted.
 */ 1 4 this 3 1563  
560 0 0 6 671 1353 0 0 65 /**
 * Returns the number of individual mounts in the system.
 */ 1 4 this 3 1585  
561 0 0 7 672 1577 994 0 47 /**
 * Returns the nth mount in the system.
 */ 2 4 this 3 1585  1 n 1 1353  
562 0 0 6 685 1349 0 0 171 /**
 * Changes the current directory.  This is used to resolve relative pathnames
 * in get_file() and/or find_file().  Returns true if successful, false
 * otherwise.
 */ 2 4 this 3 1563  13 new_directory 1 1510  
563 0 0 7 686 1564 0 0 60 /**
 * Returns the current directory name.  See chdir().
 */ 1 4 this 3 1585  
564 0 0 6 687 1349 0 0 263 /**
 * Attempts to create a directory within the file system.  Returns true on
 * success, false on failure (for instance, because the parent directory does
 * not exist, or is read-only).  If the directory already existed prior to
 * this call, returns true.
 */ 2 4 this 3 1563  8 filename 1 1510  
565 0 0 6 688 1349 0 0 169 /**
 * Attempts to create a directory within the file system.  Will also create
 * any intervening directories needed.  Returns true on success, false on
 * failure.
 */ 2 4 this 3 1563  8 filename 1 1510  
566 0 0 7 689 1515 1190 0 537 /**
 * Looks up the file by the indicated name in the file system.  Returns a
 * VirtualFile pointer representing the file if it is found, or NULL if it is
 * not.
 *
 * If status_only is true, the file will be checked for existence and length
 * and so on, but the returned file's contents cannot be read.  This is an
 * optimization which is especially important for certain mount types, for
 * instance HTTP, for which opening a file to determine its status is
 * substantially less expensive than opening it to read its contents.
 */ 3 4 this 3 1585  8 filename 1 1510  11 status_only 5 1349  
567 0 0 7 690 1515 1190 0 298 /**
 * Attempts to create a file by the indicated name in the filesystem, if
 * possible, and returns it.  If a file by this name already exists, returns
 * the same thing as get_file().  If the filename is located within a read-
 * only directory, or the directory doesn't exist, returns NULL.
 */ 2 4 this 3 1563  8 filename 1 1510  
568 0 0 7 691 1515 1190 0 173 /**
 * Uses the indicated search path to find the file within the file system.
 * Returns the first occurrence of the file found, or NULL if the file cannot
 * be found.
 */ 4 4 this 3 1585  8 filename 1 1510  10 searchpath 1 1587  11 status_only 5 1349  
569 0 0 6 692 1349 0 0 208 /**
 * Attempts to delete the indicated file or directory.  This can remove a
 * single file or an empty directory.  It will not remove a nonempty
 * directory.  Returns true on success, false on failure.
 */ 2 4 this 3 1563  8 filename 1 1510  
570 0 0 6 693 1349 0 0 599 /**
 * Attempts to move or rename the indicated file or directory.  If the
 * original file is an ordinary file, it will quietly replace any already-
 * existing file in the new filename (but not a directory).  If the original
 * file is a directory, the new filename must not already exist.
 *
 * If the file is a directory, the new filename must be within the same mount
 * point.  If the file is an ordinary file, the new filename may be anywhere;
 * but if it is not within the same mount point then the rename operation is
 * automatically performed as a two-step copy-and-delete operation.
 */ 3 4 this 3 1563  13 orig_filename 1 1510  12 new_filename 1 1510  
571 0 0 6 694 1349 0 0 131 /**
 * Attempts to copy the contents of the indicated file to the indicated file.
 * Returns true on success, false on failure.
 */ 3 4 this 3 1563  13 orig_filename 1 1510  12 new_filename 1 1510  
572 0 0 6 695 1349 0 0 174 /**
 * Searches the given search path for the filename.  If it is found, updates
 * the filename to the full pathname found and returns true; otherwise,
 * returns false.
 */ 4 4 this 3 1585  8 filename 1 1564  10 searchpath 1 1587  17 default_extension 5 1469  
573 0 0 6 696 1353 0 0 363 /**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Fills up the results list with *all* of the matching filenames
 * found, if any.  Returns the number of matches found.
 *
 * It is the responsibility of the the caller to clear the results list first;
 * otherwise, the newly-found files will be appended to the list.
 */ 4 4 this 3 1585  8 filename 1 1510  10 searchpath 1 1587  7 results 1 1590  
574 0 0 6 697 1349 0 0 111 /**
 * Convenience function; returns true if the named file exists in the virtual
 * file system hierarchy.
 */ 2 4 this 3 1585  8 filename 1 1510  
575 0 0 6 698 1349 0 0 126 /**
 * Convenience function; returns true if the named file exists as a directory in
 * the virtual file system hierarchy.
 */ 2 4 this 3 1585  8 filename 1 1510  
576 0 0 6 699 1349 0 0 129 /**
 * Convenience function; returns true if the named file exists as a regular file
 * in the virtual file system hierarchy.
 */ 2 4 this 3 1585  8 filename 1 1510  
577 0 0 7 700 1565 861 0 247 /**
 * If the file represents a directory (that is, is_directory() returns true),
 * this returns the list of files within the directory at the current time.
 * Returns NULL if the file is not a directory or if the directory cannot be
 * read.
 */ 2 4 this 3 1585  8 filename 1 1510  
578 0 0 4 701 1420 0 0 80 /**
 * Convenience function; lists the files within the indicated directory.
 */ 2 4 this 3 1585  8 filename 1 1510  
579 0 0 4 702 1420 0 0 117 /**
 * Convenience function; lists the files within the indicated directory, and
 * all files below, recursively.
 */ 2 4 this 3 1585  8 filename 1 1510  
580 0 0 4 703 1420 0 0 75 /**
 * Print debugging information.  (e.g.  from Python or gdb prompt).
 */ 2 4 this 3 1585  3 out 1 1432  
581 0 0 6 704 1563 0 0 423 /**
 * Returns the default global VirtualFileSystem.  You may create your own
 * personal VirtualFileSystem objects and use them for whatever you like, but
 * Panda will attempt to load models and stuff from this default object.
 *
 * Initially, the global VirtualFileSystem is set up to mount the OS
 * filesystem to root; i.e.  it is equivalent to the OS filesystem.  This may
 * be subsequently adjusted by the user.
 */ 0 
582 0 0 38 705 1421 0 0 365 /**
 * Convenience function; returns the entire contents of the indicated file as
 * a string.
 *
 * If auto_unwrap is true, an explicitly-named .pz/.gz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */ 3 4 this 3 1585  8 filename 1 1510  11 auto_unwrap 1 1349  
583 0 0 6 706 1530 0 0 424 /**
 * Convenience function; returns a newly allocated istream if the file exists
 * and can be read, or NULL otherwise.  Does not return an invalid istream.
 *
 * If auto_unwrap is true, an explicitly-named .pz file is automatically
 * decompressed and the decompressed contents are returned.  This is different
 * than vfs-implicit-pz, which will automatically decompress a file if the
 * extension .pz is *not* given.
 */ 3 4 this 3 1585  8 filename 1 1510  11 auto_unwrap 1 1349  
584 0 0 4 707 1420 0 0 243 /**
 * Closes a file opened by a previous call to open_read_file().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 1 6 stream 1 1530  
585 0 0 38 708 1421 0 0 0 4 4 this 3 1563  8 filename 1 1510  4 data 1 1421  9 auto_wrap 1 1349  
586 0 0 6 709 1432 0 0 345 /**
 * Convenience function; returns a newly allocated ostream if the file exists
 * and can be written, or NULL otherwise.  Does not return an invalid ostream.
 *
 * If auto_wrap is true, an explicitly-named .pz file is automatically
 * compressed while writing.  If truncate is true, the file is truncated to
 * zero length before writing.
 */ 4 4 this 3 1563  8 filename 1 1510  9 auto_wrap 1 1349  8 truncate 1 1349  
587 0 0 6 710 1432 0 0 180 /**
 * Works like open_write_file(), but the file is opened in append mode.  Like
 * open_write_file, the returned pointer should eventually be passed to
 * close_write_file().
 */ 2 4 this 3 1563  8 filename 1 1510  
588 0 0 4 711 1420 0 0 244 /**
 * Closes a file opened by a previous call to open_write_file().  This really
 * just deletes the ostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 1 6 stream 1 1432  
589 0 0 6 712 1547 0 0 169 /**
 * Convenience function; returns a newly allocated iostream if the file exists
 * and can be written, or NULL otherwise.  Does not return an invalid
 * iostream.
 */ 3 4 this 3 1563  8 filename 1 1510  8 truncate 1 1349  
590 0 0 6 713 1547 0 0 194 /**
 * Works like open_read_write_file(), but the file is opened in append mode.
 * Like open_read_write_file, the returned pointer should eventually be passed
 * to close_read_write_file().
 */ 2 4 this 3 1563  8 filename 1 1510  
591 0 0 4 714 1420 0 0 250 /**
 * Closes a file opened by a previous call to open_read_write_file().  This
 * really just deletes the iostream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 1 6 stream 1 1547  
592 0 0 7 679 1592 1246 0 0 0 
593 0 0 15 679 1592 1246 0 0 1 4 copy 1 1577  
594 0 0 23 679 1592 1246 0 0 1 3 ptr 1 1577  
595 0 0 7 679 1592 1246 0 0 1 6 param0 0 1593  
596 0 0 7 680 1577 994 0 289 // If your base class is a derivative of TypedObject, you might want to use
// the DCAST macro defined in typedObject.h instead, e.g.  DCAST(MyType,
// ptr).  This provides a clean downcast that doesn't require .p() or any
// double-casting, and it can be run-time checked for correctness. 1 4 this 3 1594  
597 0 0 6 681 1592 0 0 0 2 4 this 3 1592  4 copy 1 1577  
598 0 0 6 681 1592 0 0 0 2 4 this 3 1592  3 ptr 1 1577  
599 0 0 6 682 1349 0 0 0 1 4 this 3 1594  
600 0 0 4 683 1420 0 0 0 1 4 this 3 1592  
601 0 0 4 677 1420 0 0 0 1 4 this 3 1596  
602 0 0 4 678 1420 0 0 0 2 4 this 3 1597  3 out 1 1432  
603 0 0 6 716 1403 0 0 203 // get_long_time() returns the most accurate timer we have over a long
// interval.  It may not be very precise for measuring short intervals, but
// it should not drift substantially over the long haul. 1 4 this 3 1599  
604 0 0 6 719 1403 0 0 206 // get_short_time() returns the most precise timer we have over a short
// interval.  It may tend to drift over the long haul, but it should have
// lots of digits to measure short intervals very precisely. 1 4 this 3 1599  
605 0 0 6 721 1403 0 0 144 // get_short_raw_time() is like get_short_time(), but does not apply any
// corrections (e.g.  paranoid-clock) to the result returned by the OS. 1 4 this 3 1599  
606 0 0 6 723 1353 0 0 369 /**
 * Returns the number of clock errors that have been detected.  Each time a
 * clock error is detected, in which the value returned by either of the above
 * methods is suspect, the value returned by this method will be incremented.
 * Applications can monitor this value and react, for instance, by
 * resynchronizing their clocks each time this value changes.
 */ 1 4 this 3 1600  
607 0 0 6 725 1599 0 0 70 /**
 * Returns a pointer to the one TrueClock object in the world.
 */ 0 
608 0 0 6 726 1349 0 0 0 2 4 this 3 1600  4 mask 1 1502  
609 0 0 7 728 1602 1284 0 60 /**
 * Create a patch file and initializes internal data
 */ 0 
610 0 0 7 728 1602 1284 0 49 /**
 * Create patch file with buffer to patch
 */ 1 6 buffer 1 1483  
611 0 0 6 730 1349 0 0 359 /**
 *
 * This implementation uses the "greedy differencing algorithm" described in
 * the masters thesis "Differential Compression: A Generalized Solution for
 * Binary Files" by Randal C. Burns (p.13). For an original file of size M and
 * a new file of size N, this algorithm is O(M) in space and O(M*N) (worst-
 * case) in time.  return false on error
 */ 4 4 this 3 1602  9 file_orig 1 1564  8 file_new 1 1564  10 patch_name 1 1564  
612 0 0 6 731 1353 0 0 193 /**
 * Opens the patch file for reading, and gets the header information from the
 * file but does not begin to do any real work.  This can be used to query the
 * data stored in the patch.
 */ 2 4 this 3 1602  10 patch_file 1 1510  
613 0 0 6 732 1353 0 0 107 /**
 * Set up to apply the patch to the file (original file and patch are
 * destroyed in the process).
 */ 3 4 this 3 1602  10 patch_file 1 1510  4 file 1 1510  
614 0 0 6 732 1353 0 0 127 /**
 * Set up to apply the patch to the file.  In this form, neither the original
 * file nor the patch file are destroyed.
 */ 4 4 this 3 1602  10 patch_file 1 1510  9 orig_file 1 1510  11 target_file 1 1510  
615 0 0 6 733 1353 0 0 390 /**
 * Perform one buffer's worth of patching.
 * Returns one of the following values:
 * @li @c EU_ok : while patching
 * @li @c EU_success : when done
 * @li @c EU_error_abort : Patching has not been initiated
 * @li @c EU_error_file_invalid : file is corrupted
 * @li @c EU_error_invalid_checksum : incompatible patch file
 * @li @c EU_error_write_file_rename : could not rename file
 */ 1 4 this 3 1602  
616 0 0 6 734 1349 0 0 171 /**
 * Patches the entire file in one call returns true on success and false on
 * error
 *
 * This version will delete the patch file and overwrite the original file.
 */ 3 4 this 3 1602  10 patch_file 1 1564  4 file 1 1564  
617 0 0 6 734 1349 0 0 138 /**
 * Patches the entire file in one call returns true on success and false on
 * error
 *
 * This version will not delete any files.
 */ 4 4 this 3 1602  10 patch_file 1 1564  9 orig_file 1 1564  11 target_file 1 1510  
618 0 0 6 735 1405 0 0 125 /**
 * Returns a value in the range 0..1, representing the amount of progress
 * through the patchfile, during a session.
 */ 1 4 this 3 1603  
619 0 0 4 739 1420 0 0 362 /**
 * If this flag is set true, the Patchfile will make a special case for
 * patching Panda Multifiles, if detected, and attempt to patch them on a
 * subfile-by-subfile basis.  If this flag is false, the Patchfile will always
 * patch the file on a full-file basis.
 *
 * This has effect only when building patches; it is not used for applying
 * patches.
 */ 2 4 this 3 1602  15 allow_multifile 1 1349  
620 0 0 6 740 1349 0 0 37 /**
 * See set_allow_multifile().
 */ 1 4 this 3 1602  
621 0 0 4 742 1420 0 0 10 /**
 *
 */ 2 4 this 3 1602  6 length 1 1353  
622 0 0 6 743 1353 0 0 10 /**
 *
 */ 1 4 this 3 1602  
623 0 0 4 744 1420 0 0 10 /**
 *
 */ 1 4 this 3 1602  
624 0 0 6 746 1349 0 0 145 /**
 * Returns true if the MD5 hash for the source file is known.  (Some early
 * versions of the patch file did not store this information.)
 */ 1 4 this 3 1603  
625 0 0 6 747 1529 0 0 52 /**
 * Returns the MD5 hash for the source file.
 */ 1 4 this 3 1603  
626 0 0 6 748 1529 0 0 78 /**
 * Returns the MD5 hash for the file after the patch has been applied.
 */ 1 4 this 3 1603  
627 0 0 15 754 1607 1301 0 0 1 5 other 1 1605  
628 0 0 7 754 1607 1301 0 0 2 4 name 5 1469  10 maxEntries 5 1353  
629 0 0 4 756 1420 0 0 0 3 4 this 3 1607  4 name 1 1469  10 maxEntries 5 1353  
630 0 0 4 757 1420 0 0 0 1 4 this 3 1607  
631 0 0 4 758 1420 0 0 0 2 4 this 3 1607  3 tag 1 1469  
632 0 0 4 759 1420 0 0 0 1 4 this 3 1607  
633 0 0 4 759 1420 0 0 0 2 4 this 3 1607  3 tag 1 1469  
634 0 0 6 760 1403 0 0 110 // Don't call any of the following during timing: (Because they are slow,
// not because anything will break). 1 4 this 3 1605  
635 0 0 4 761 1420 0 0 0 1 3 out 5 1432  
636 0 0 4 762 1420 0 0 0 2 4 this 3 1605  3 out 5 1432  
637 0 0 4 763 1420 0 0 0 1 3 out 5 1432  
638 0 0 4 764 1420 0 0 0 2 4 this 3 1605  3 out 5 1432  
639 0 0 6 766 1349 0 0 384 /**
 * Returns true if the object we are pointing to has been deleted, false
 * otherwise.  If this returns true, it means that the pointer can not yet be
 * reused, but it does not guarantee that it can be safely accessed.  See the
 * lock() method for a safe way to access the underlying pointer.
 *
 * This will always return true for a null pointer, unlike is_valid_pointer().
 */ 1 4 this 3 1608  
640 0 0 6 767 1349 0 0 122 /**
 * Returns true if the pointer is not null and the object has not been
 * deleted.  See was_deleted() for caveats.
 */ 1 4 this 3 1608  
641 0 0 7 777 1610 1320 0 0 0 
642 0 0 15 777 1610 1320 0 0 1 6 param0 0 1611  
643 0 0 6 771 1349 0 0 302 /**
 * Sets the registry key to the indicated value as a string.  The supplied
 * string value is automatically converted from whatever encoding is set by
 * TextEncoder::set_default_encoding() and written as a Unicode string.  The
 * registry key must already exist prior to calling this function.
 */ 4 3 key 1 1469  4 name 1 1469  5 value 1 1469  2 rl 5 1411  
644 0 0 6 772 1349 0 0 142 /**
 * Sets the registry key to the indicated value as an integer.  The registry
 * key must already exist prior to calling this function.
 */ 4 3 key 1 1469  4 name 1 1469  5 value 1 1353  2 rl 5 1411  
645 0 0 6 774 1412 0 0 115 /**
 * Returns the type of the indicated key, or T_none if the key is not known or
 * is some unsupported type.
 */ 3 3 key 1 1469  4 name 1 1469  2 rl 5 1411  
646 0 0 6 775 1469 0 0 291 /**
 * Returns the value associated with the indicated registry key, assuming it
 * is a string value.  The string value is automatically encoded using
 * TextEncoder::get_default_encoding().  If the key is not defined or is not a
 * string type value, default_value is returned instead.
 */ 4 3 key 1 1469  4 name 1 1469  13 default_value 1 1469  2 rl 5 1411  
647 0 0 6 776 1353 0 0 206 /**
 * Returns the value associated with the indicated registry key, assuming it
 * is an integer value.  If the key is not defined or is not an integer type
 * value, default_value is returned instead.
 */ 4 3 key 1 1469  4 name 1 1469  13 default_value 1 1353  2 rl 5 1411  
648 0 0 6 250 1469 0 0 0 2 6 source 1 1469  17 compression_level 1 1353  
649 0 0 6 251 1469 0 0 0 1 6 source 1 1469  
650 0 0 6 252 1349 0 0 0 3 6 source 1 1510  4 dest 1 1510  17 compression_level 1 1353  
651 0 0 6 253 1349 0 0 0 2 6 source 1 1510  4 dest 1 1510  
652 0 0 6 254 1349 0 0 0 3 6 source 1 1530  4 dest 1 1432  17 compression_level 1 1353  
653 0 0 6 255 1349 0 0 0 2 6 source 1 1530  4 dest 1 1432  
654 0 0 6 256 1349 0 0 0 2 6 source 1 1530  4 dest 1 1432  
655 0 0 6 397 1469 0 0 0 5 6 source 1 1469  8 password 1 1469  9 algorithm 5 1469  10 key_length 5 1353  15 iteration_count 5 1353  
656 0 0 6 398 1469 0 0 0 2 6 source 1 1469  8 password 1 1469  
657 0 0 6 399 1349 0 0 0 6 6 source 1 1510  4 dest 1 1510  8 password 1 1469  9 algorithm 5 1469  10 key_length 5 1353  15 iteration_count 5 1353  
658 0 0 6 400 1349 0 0 0 3 6 source 1 1510  4 dest 1 1510  8 password 1 1469  
659 0 0 6 401 1349 0 0 0 6 6 source 1 1530  4 dest 1 1432  8 password 1 1469  9 algorithm 5 1469  10 key_length 5 1353  15 iteration_count 5 1353  
660 0 0 6 402 1349 0 0 0 3 6 source 1 1530  4 dest 1 1432  8 password 1 1469  
661 0 0 6 404 1469 0 0 10 /**
 *
 */ 1 3 err 1 1370  
662 0 0 6 405 1353 0 0 0 0 
663 0 0 6 406 1469 0 0 10 /**
 *
 */ 0 
664 0 0 6 407 1353 0 0 0 0 
665 0 0 6 752 1469 0 0 0 4 8 password 1 1469  4 salt 1 1469  5 iters 1 1353  6 keylen 1 1353  
292
1321 11 CPTA_double 0 2105345 11 CPTA_double 11 CPTA_double 0 0 1322 0 0 0 0 0 0 0 0 0 0

1322 29 ConstPointerToArray< double > 0 141313 29 ConstPointerToArray< double > 29 ConstPointerToArray< double > 0 0 0 1 674 688 0 13 675 676 677 678 679 680 681 682 683 684 685 686 687 0 0 1 0 1323 0 0 0 0 0

1323 28 PointerToArrayBase< double > 0 26625 28 PointerToArrayBase< double > 28 PointerToArrayBase< double > 0 0 0 0 671 0 2 672 673 0 0 1 0 1324 0 0 0 0 0

1324 49 PointerToBase< ReferenceCountedVector< double > > 0 43009 49 PointerToBase< ReferenceCountedVector< double > > 49 PointerToBase< ReferenceCountedVector< double > > 0 0 0 0 0 0 2 669 670 0 0 1 0 1325 0 0 0 0 0

1325 13 PointerToVoid 0 141313 13 PointerToVoid 13 PointerToVoid 0 0 0 0 668 0 2 666 667 0 0 1 0 1326 0 0 0 0 373
/**
 * This is the non-template part of the base class for PointerTo and
 * ConstPointerTo.  It is necessary so we can keep a pointer to a non-template
 * class within the ReferenceCount object, to implement weak reference
 * pointers--we need to have something to clean up when the ReferenceCount
 * object destructs.
 *
 * This is the base class for PointerToBase<T>.
 */

1326 10 MemoryBase 0 2048 10 MemoryBase 10 MemoryBase 0 0 0 0 0 0 0 0 0 0 0 0 428
/**
 * This class is intended to be the base class of all objects in Panda that
 * might be allocated and deleted via the new and delete operators.  It
 * redefines these operators to provide some memory tracking support.
 *
 * We used to try to override the global operator new and delete methods, but
 * that seems to cause problems when including header files for C++-based
 * system libraries (such as are found on OSX).
 */

1327 10 CPTA_float 0 2105345 10 CPTA_float 10 CPTA_float 0 0 1328 0 0 0 0 0 0 0 0 0 0

1328 28 ConstPointerToArray< float > 0 141313 28 ConstPointerToArray< float > 28 ConstPointerToArray< float > 0 0 0 1 694 708 0 13 695 696 697 698 699 700 701 702 703 704 705 706 707 0 0 1 0 1329 0 0 0 0 0

1329 27 PointerToArrayBase< float > 0 26625 27 PointerToArrayBase< float > 27 PointerToArrayBase< float > 0 0 0 0 691 0 2 692 693 0 0 1 0 1330 0 0 0 0 0

1330 48 PointerToBase< ReferenceCountedVector< float > > 0 43009 48 PointerToBase< ReferenceCountedVector< float > > 48 PointerToBase< ReferenceCountedVector< float > > 0 0 0 0 0 0 2 689 690 0 0 1 0 1325 0 0 0 0 0

1331 8 CPTA_int 0 2105345 8 CPTA_int 8 CPTA_int 0 0 1332 0 0 0 0 0 0 0 0 0 0

1332 26 ConstPointerToArray< int > 0 141313 26 ConstPointerToArray< int > 26 ConstPointerToArray< int > 0 0 0 1 714 728 0 13 715 716 717 718 719 720 721 722 723 724 725 726 727 0 0 1 0 1333 0 0 0 0 0

1333 25 PointerToArrayBase< int > 0 26625 25 PointerToArrayBase< int > 25 PointerToArrayBase< int > 0 0 0 0 711 0 2 712 713 0 0 1 0 1334 0 0 0 0 0

1334 46 PointerToBase< ReferenceCountedVector< int > > 0 43009 46 PointerToBase< ReferenceCountedVector< int > > 46 PointerToBase< ReferenceCountedVector< int > > 0 0 0 0 0 0 2 709 710 0 0 1 0 1325 0 0 0 0 0

1335 10 CPTA_uchar 0 2105345 10 CPTA_uchar 10 CPTA_uchar 0 0 1336 0 0 0 0 0 0 0 0 0 0

1336 36 ConstPointerToArray< unsigned char > 0 141313 36 ConstPointerToArray< unsigned char > 36 ConstPointerToArray< unsigned char > 0 0 0 1 734 748 0 13 735 736 737 738 739 740 741 742 743 744 745 746 747 0 0 1 0 1337 0 0 0 0 0

1337 35 PointerToArrayBase< unsigned char > 0 26625 35 PointerToArrayBase< unsigned char > 35 PointerToArrayBase< unsigned char > 0 0 0 0 731 0 2 732 733 0 0 1 0 1338 0 0 0 0 0

1338 56 PointerToBase< ReferenceCountedVector< unsigned char > > 0 43009 56 PointerToBase< ReferenceCountedVector< unsigned char > > 56 PointerToBase< ReferenceCountedVector< unsigned char > > 0 0 0 0 0 0 2 729 730 0 0 1 0 1325 0 0 0 0 0

1339 10 PTA_double 0 2105345 10 PTA_double 10 PTA_double 0 0 1340 0 0 0 0 0 0 0 0 0 0

1340 24 PointerToArray< double > 0 141313 24 PointerToArray< double > 24 PointerToArray< double > 0 0 0 1 749 770 0 20 750 751 752 753 754 755 756 757 758 759 760 761 762 763 764 765 766 767 768 769 0 0 1 0 1323 0 0 0 0 0

1341 9 PTA_float 0 2105345 9 PTA_float 9 PTA_float 0 0 1342 0 0 0 0 0 0 0 0 0 0

1342 23 PointerToArray< float > 0 141313 23 PointerToArray< float > 23 PointerToArray< float > 0 0 0 1 771 792 0 20 772 773 774 775 776 777 778 779 780 781 782 783 784 785 786 787 788 789 790 791 0 0 1 0 1329 0 0 0 0 0

1343 7 PTA_int 0 2105345 7 PTA_int 7 PTA_int 0 0 1344 0 0 0 0 0 0 0 0 0 0

1344 21 PointerToArray< int > 0 141313 21 PointerToArray< int > 21 PointerToArray< int > 0 0 0 1 793 814 0 20 794 795 796 797 798 799 800 801 802 803 804 805 806 807 808 809 810 811 812 813 0 0 1 0 1333 0 0 0 0 0

1345 9 PTA_uchar 0 2105345 9 PTA_uchar 9 PTA_uchar 0 0 1346 0 0 0 0 0 0 0 0 0 0

1346 31 PointerToArray< unsigned char > 0 141313 31 PointerToArray< unsigned char > 31 PointerToArray< unsigned char > 0 0 0 1 815 836 0 20 816 817 818 819 820 821 822 823 824 825 826 827 828 829 830 831 832 833 834 835 0 0 1 0 1337 0 0 0 0 0

1347 11 MemoryUsage 0 141313 11 MemoryUsage 11 MemoryUsage 0 0 0 1 857 858 10 1613 1614 1615 1616 1617 1618 1619 1620 1621 1622 20 837 838 839 840 841 842 843 844 845 846 847 848 849 850 851 852 853 854 855 856 0 0 1 0 1348 0 0 0 0 292
/**
 * This class is used strictly for debugging purposes, specifically for
 * tracking memory leaks of reference-counted objects: it keeps a record of
 * every such object currently allocated.
 *
 * When compiled with NDEBUG set, this entire class does nothing and compiles
 * to a stub.
 */

1348 10 MemoryHook 0 2048 10 MemoryHook 10 MemoryHook 0 0 0 0 0 0 0 0 0 0 0 0 641
/**
 * This class provides a wrapper around the various possible malloc schemes
 * Panda might employ.  It also exists to allow the MemoryUsage class in Panda
 * to insert callback hooks to track the size of allocated pointers.
 *
 * The PANDA_MALLOC_* and PANDA_FREE_* macros are defined to vector through
 * through this class (except in production builds) to facilitate that.  Every
 * memory allocation call in Panda should therefore use these macros instead
 * of direct calls to malloc or free.  (C++ new and delete operators may be
 * employed for classes which inherit from MemoryBase; otherwise, use the
 * PANDA_MALLOC macros.)
 */

1349 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

1350 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 1351 0 0 0 0 0 0 0 0 0 0

1351 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

1352 14 ReferenceCount 0 26625 14 ReferenceCount 14 ReferenceCount 0 0 0 0 861 1 1623 6 862 863 864 865 866 867 0 0 1 3 1326 859 860 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

1353 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

1354 6 Buffer 0 75777 6 Buffer 6 Buffer 0 0 0 1 869 861 0 1 868 0 0 1 0 1352 0 0 0 0 10
/**
 *
 */

1355 25 PStatCollectorForwardBase 0 75777 25 PStatCollectorForwardBase 25 PStatCollectorForwardBase 0 0 0 0 861 0 1 877 0 0 1 0 1352 0 0 0 0 282
/**
 * This class serves as a cheap forward reference to a PStatCollector, which
 * is defined in the pstatclient module (and is not directly accessible here
 * in the express module).
 *
 * This is subclassed as PStatCollectorForward, which defines the actual
 * functionality.
 */

1356 18 NodeReferenceCount 0 43009 18 NodeReferenceCount 18 NodeReferenceCount 0 0 0 0 0 0 6 878 879 880 881 882 883 0 0 1 0 1352 0 0 0 0 603
/**
 * This class specializes ReferenceCount to add an additional counter, called
 * node_ref_count, for the purposes of counting the number of times the object
 * is referenced by a "node", whatever that may mean in context.
 *
 * The new methods node_ref() and node_unref() automatically increment and
 * decrement the primary reference count as well.  There also exists a
 * NodePointerTo<> class to maintain the node_ref counters automatically.
 *
 * See also CachedTypedWritableReferenceCount, which is similar in principle,
 * as well as NodeCachedReferenceCount, which combines both of these.
 */

1357 8 Datagram 0 75777 8 Datagram 8 Datagram 0 0 0 1 884 0 0 48 885 886 887 888 889 890 891 892 893 894 895 896 897 898 899 900 901 902 903 904 905 906 907 908 909 910 911 912 913 914 915 916 917 918 919 920 921 922 923 924 925 926 927 928 929 930 931 932 0 0 1 0 1358 0 0 0 0 565
/**
 * An ordered list of data elements, formatted in memory for transmission over
 * a socket or writing to a data file.
 *
 * Data elements should be added one at a time, in order, to the Datagram.
 * The nature and contents of the data elements are totally up to the user.
 * When a Datagram has been transmitted and received, its data elements may be
 * extracted using a DatagramIterator; it is up to the caller to know the
 * correct type of each data element in order.
 *
 * A Datagram is itself headerless; it is simply a collection of data
 * elements.
 */

1358 11 TypedObject 0 2048 11 TypedObject 11 TypedObject 0 0 0 0 0 0 0 0 0 0 0 0 2508
/**
 * This is an abstract class that all classes which use TypeHandle, and also
 * provide virtual functions to support polymorphism, should inherit from.
 * Each derived class should define get_type(), which should return the
 * specific type of the derived class.  Inheriting from this automatically
 * provides support for is_of_type() and is_exact_type().
 *
 * All classes that inherit directly or indirectly from TypedObject should
 * redefine get_type() and force_init_type(), as shown below.  Some classes
 * that do not inherit from TypedObject may still declare TypeHandles for
 * themselves by defining methods called get_class_type() and init_type().
 * Classes such as these may serve as base classes, but the dynamic type
 * identification system will be limited.  Classes that do not inherit from
 * TypedObject need not define the virtual functions get_type() and
 * force_init_type() (or any other virtual functions).
 *
 * There is a specific layout for defining the overrides from this class.
 * Keeping the definitions formatted just like these examples will allow
 * someone in the future to use a sed (or similar) script to make global
 * changes, if necessary.  Avoid rearranging the braces or the order of the
 * functions unless you're ready to change them in every file all at once.
 *
 * What follows are some examples that can be used in new classes that you
 * create.
 *
 * @par In the class definition (.h file):
 * @code
 * public:
 *   static TypeHandle get_class_type() {
 *     return _type_handle;
 *   }
 *   static void init_type() {
 *     <<<BaseClassOne>>>::init_type();
 *     <<<BaseClassTwo>>>::init_type();
 *     <<<BaseClassN>>>::init_type();
 *     register_type(_type_handle, "<<<ThisClassStringName>>>",
 *                   <<<BaseClassOne>>>::get_class_type(),
 *                   <<<BaseClassTwo>>>::get_class_type(),
 *                   <<<BaseClassN>>>::get_class_type());
 *   }
 *   virtual TypeHandle get_type() const {
 *     return get_class_type();
 *   }
 *   virtual TypeHandle force_init_type() {init_type(); return get_class_type();}
 *
 * private:
 *   static TypeHandle _type_handle;
 * @endcode
 *
 * @par In the class .cxx file:
 * @code
 * TypeHandle <<<ThisClassStringName>>>::_type_handle;
 * @endcode
 *
 * @par In the class config_<<<PackageName>>>.cxx file:
 * @code
 * ConfigureFn(config_<<<PackageName>>>) {
 *   <<<ClassOne>>>::init_type();
 *   <<<ClassTwo>>>::init_type();
 *   <<<ClassN>>>::init_type();
 * }
 * @endcode
 */

1359 17 DatagramGenerator 0 26625 17 DatagramGenerator 17 DatagramGenerator 0 0 0 0 933 0 9 934 935 936 937 938 939 940 941 942 0 0 0 0 0 125
/**
 * This class defines the abstract interace to any source of datagrams,
 * whether it be from a file or from the net.
 */

1360 16 DatagramIterator 0 141313 16 DatagramIterator 16 DatagramIterator 0 0 0 1 943 980 0 36 944 945 946 947 948 949 950 951 952 953 954 955 956 957 958 959 960 961 962 963 964 965 966 967 968 969 970 971 972 973 974 975 976 977 978 979 0 0 0 0 0 365
/**
 * A class to retrieve the individual data elements previously stored in a
 * Datagram.  Elements may be retrieved one at a time; it is up to the caller
 * to know the correct type and order of each element.
 *
 * Note that it is the responsibility of the caller to ensure that the datagram
 * object is not destructed while this DatagramIterator is in use.
 */

1361 12 DatagramSink 0 26625 12 DatagramSink 12 DatagramSink 0 0 0 0 981 3 1624 1625 1626 7 982 983 984 985 986 987 988 0 0 0 0 0 135
/**
 * This class defines the abstract interface to sending datagrams to any
 * target, whether it be into a file or across the net
 */

1362 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 1363 0 0 0 0 0 0 0 0 0 0

1363 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

1364 21 FileReference const * 0 8576 21 FileReference const * 21 FileReference const * 0 0 1365 0 0 0 0 0 0 0 0 0 0

1365 19 FileReference const 0 8832 19 FileReference const 19 FileReference const 0 0 1366 0 0 0 0 0 0 0 0 0 0

1366 13 FileReference 0 141313 13 FileReference 13 FileReference 0 0 0 1 995 998 0 2 996 997 0 0 1 0 1367 0 0 0 0 149
/**
 * Keeps a reference-counted pointer to a file on disk.  As long as the
 * FileReference is held, someone presumably has a use for this file.
 */

1367 19 TypedReferenceCount 0 141313 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 994 0 1 993 0 0 2 3 1358 989 990 3 1352 991 992 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1368 9 streamoff 0 2105344 14 std::streamoff 14 std::streamoff 0 0 1369 0 0 0 0 0 0 0 0 0 0

1369 13 long long int 0 8226 13 long long int 13 long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

1370 13 ErrorUtilCode 0 532481 13 ErrorUtilCode 13 ErrorUtilCode 0 0 0 0 0 0 0 0 0 0 37 16 EU_http_redirect 16 EU_http_redirect 0
7 6 EU_eof 6 EU_eof 0
6 18 EU_network_no_data 18 EU_network_no_data 0
5 12 EU_write_ram 12 EU_write_ram 0
4 8 EU_write 8 EU_write 0
3 5 EU_ok 5 EU_ok 0
2 10 EU_success 10 EU_success 0
1 14 EU_error_abort 14 EU_error_abort 17
// General errors
-1 19 EU_error_file_empty 19 EU_error_file_empty 0
-2 21 EU_error_file_invalid 21 EU_error_file_invalid 0
-3 25 EU_error_invalid_checksum 25 EU_error_invalid_checksum 0
-4 21 EU_error_network_dead 21 EU_error_network_dead 25
// General network errors
-30 28 EU_error_network_unreachable 28 EU_error_network_unreachable 0
-31 29 EU_error_network_disconnected 29 EU_error_network_disconnected 0
-32 24 EU_error_network_timeout 24 EU_error_network_timeout 0
-33 24 EU_error_network_no_data 24 EU_error_network_no_data 0
-34 37 EU_error_network_disconnected_locally 37 EU_error_network_disconnected_locally 23
// Local network errors
-40 32 EU_error_network_buffer_overflow 32 EU_error_network_buffer_overflow 0
-41 36 EU_error_network_disk_quota_exceeded 36 EU_error_network_disk_quota_exceeded 0
-42 41 EU_error_network_remote_host_disconnected 41 EU_error_network_remote_host_disconnected 29
// Remote host network errors
-50 33 EU_error_network_remote_host_down 33 EU_error_network_remote_host_down 0
-51 40 EU_error_network_remote_host_unreachable 40 EU_error_network_remote_host_unreachable 0
-52 38 EU_error_network_remote_host_not_found 38 EU_error_network_remote_host_not_found 0
-53 40 EU_error_network_remote_host_no_response 40 EU_error_network_remote_host_no_response 0
-54 27 EU_error_write_out_of_files 27 EU_error_write_out_of_files 23
// General local errors
-60 28 EU_error_write_out_of_memory 28 EU_error_write_out_of_memory 0
-61 32 EU_error_write_sharing_violation 32 EU_error_write_sharing_violation 0
-62 24 EU_error_write_disk_full 24 EU_error_write_disk_full 0
-63 29 EU_error_write_disk_not_found 29 EU_error_write_disk_not_found 0
-64 36 EU_error_write_disk_sector_not_found 36 EU_error_write_disk_sector_not_found 0
-65 25 EU_error_write_disk_fault 25 EU_error_write_disk_fault 0
-66 26 EU_error_write_file_rename 26 EU_error_write_file_rename 0
-67 28 EU_error_http_server_timeout 28 EU_error_http_server_timeout 14
// HTTP errors
-70 29 EU_error_http_gateway_timeout 29 EU_error_http_gateway_timeout 0
-71 33 EU_error_http_service_unavailable 33 EU_error_http_service_unavailable 0
-72 34 EU_error_http_proxy_authentication 34 EU_error_http_proxy_authentication 0
-73 13 EU_error_zlib 13 EU_error_zlib 14
// Zlib errors
-80 0 0

1371 7 Ramfile 0 141313 7 Ramfile 7 Ramfile 0 0 0 1 1009 1018 0 8 1010 1011 1012 1013 1014 1015 1016 1017 0 0 0 0 0 85
/**
 * An in-memory buffer specifically designed for downloading files to memory.
 */

1372 7 HashVal 0 141313 7 HashVal 7 HashVal 0 0 0 1 1019 1049 0 29 1020 1021 1022 1023 1024 1025 1026 1027 1028 1029 1030 1031 1032 1033 1034 1035 1036 1037 1038 1039 1040 1041 1042 1043 1044 1045 1046 1047 1048 0 0 0 0 0 109
/**
 * Stores a 128-bit value that represents the hashed contents (typically MD5)
 * of a file or buffer.
 */

1373 19 MemoryUsagePointers 0 26625 19 MemoryUsagePointers 19 MemoryUsagePointers 0 0 0 1 1050 1051 0 9 1052 1053 1054 1055 1056 1057 1058 1059 1060 2 1639 1640 0 0 0 0 785
/**
 * This is a list of pointers returned by a MemoryUsage object in response to
 * some query.
 *
 * Warning: once pointers are stored in a MemoryUsagePointers object, they are
 * reference-counted, and will not be freed until the MemoryUsagePointers
 * object is freed (or clear() is called on the object).  However, they may
 * not even be freed then; pointers may leak once they have been added to this
 * structure.  This is because we don't store enough information in this
 * structure to correctly free the pointers that have been added.  Since this
 * is intended primarily as a debugging tool, this is not a major issue.
 *
 * This class is just a user interface to talk about pointers stored in a
 * MemoryUsage object.  It doesn't even exist when compiled with NDEBUG.
 */

1374 10 ISubStream 0 141313 10 ISubStream 10 ISubStream 0 0 0 1 1061 1064 0 2 1062 1063 0 0 1 0 1375 0 0 0 0 392
/**
 * An istream object that presents a subwindow into another istream.  The
 * first character read from this stream will be the "start" character from
 * the source istream; just before the file pointer reaches the "end"
 * character, eof is returned.
 *
 * The source stream must be one that we can randomly seek within.  The
 * resulting ISubStream will also support arbitrary seeks.
 */

1375 7 istream 0 2048 12 std::istream 12 std::istream 0 0 0 0 0 0 0 0 0 0 0 0 0

1376 10 OSubStream 0 141313 10 OSubStream 10 OSubStream 0 0 0 1 1065 1068 0 2 1066 1067 0 0 1 0 1377 0 0 0 0 391
/**
 * An ostream object that presents a subwindow into another ostream.  The
 * first character written to this stream will be the "start" character in the
 * dest istream; no characters may be written to character "end" or later
 * (unless end is zero).
 *
 * The dest stream must be one that we can randomly seek within.  The
 * resulting OSubStream will also support arbitrary seeks.
 */

1377 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

1378 9 SubStream 0 141313 9 SubStream 9 SubStream 0 0 0 1 1069 1072 0 2 1070 1071 0 0 1 0 1379 0 0 0 0 68
/**
 * Combined ISubStream and OSubStream for bidirectional I/O.
 */

1379 8 iostream 0 2048 13 std::iostream 13 std::iostream 0 0 0 0 0 0 0 0 0 0 0 0 0

1380 9 Multifile 0 75777 9 Multifile 9 Multifile 0 0 0 1 1073 861 1 1627 62 1074 1075 1076 1077 1078 1079 1080 1081 1082 1083 1084 1085 1086 1087 1088 1089 1090 1091 1092 1093 1094 1095 1096 1097 1098 1099 1100 1101 1102 1103 1104 1105 1106 1107 1108 1109 1110 1111 1112 1113 1114 1115 1116 1117 1118 1119 1120 1121 1122 1123 1124 1125 1126 1127 1128 1129 1130 1131 1132 1133 1134 1135 1 1641 0 1 0 1352 0 0 0 0 47
/**
 * A file that contains a set of files.
 */

1381 6 string 0 2105344 11 std::string 11 std::string 0 0 1382 0 0 0 0 0 0 0 0 0 0

1382 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

1383 7 Namable 0 141313 7 Namable 7 Namable 0 0 0 1 1136 1143 1 1628 6 1137 1138 1139 1140 1141 1142 0 0 1 0 1326 0 0 0 0 125
/**
 * A base class for all things which can have a name.  The name is either
 * empty or nonempty, but it is never NULL.
 */

1384 12 string const 0 8832 17 std::string const 17 std::string const 0 0 1381 0 0 0 0 0 0 0 0 0 0

1385 14 OpenSSLWrapper 0 43009 14 OpenSSLWrapper 14 OpenSSLWrapper 0 0 0 0 0 0 8 1144 1145 1146 1147 1148 1149 1150 1151 0 0 0 0 0 205
/**
 * Provides an interface wrapper around the OpenSSL library, to ensure that
 * the library is properly initialized in the application, and to provide some
 * hooks into global OpenSSL context data.
 */

1386 11 SubfileInfo 0 141313 11 SubfileInfo 11 SubfileInfo 0 0 0 1 1152 1160 0 7 1153 1154 1155 1156 1157 1158 1159 0 0 0 0 0 200
/**
 * This class records a particular byte sub-range within an existing file on
 * disk.  Generally, the filename is understood as a physical file on disk,
 * and not to be looked up via the vfs.
 */

1387 11 VirtualFile 0 141313 11 VirtualFile 11 VirtualFile 0 0 0 0 1190 0 29 1161 1162 1163 1164 1165 1166 1167 1168 1169 1170 1171 1172 1173 1174 1175 1176 1177 1178 1179 1180 1181 1182 1183 1184 1185 1186 1187 1188 1189 0 0 1 0 1367 0 0 0 0 91
/**
 * The abstract base class for a file or directory within the
 * VirtualFileSystem.
 */

1388 20 VirtualFileComposite 0 141313 20 VirtualFileComposite 20 VirtualFileComposite 0 0 0 0 1192 0 1 1191 0 0 1 0 1387 0 0 0 0 223
/**
 * A composite directory within the VirtualFileSystem: this maps to more than
 * one directory on different mount points.  The resulting directory appears
 * to be the union of all the individual simple directories.
 */

1389 16 VirtualFileMount 0 75777 16 VirtualFileMount 16 VirtualFileMount 0 0 0 0 994 0 6 1193 1194 1195 1196 1197 1198 0 0 1 0 1367 0 0 0 0 159
/**
 * The abstract base class for a mount definition used within a
 * VirtualFileSystem.  Normally users don't need to monkey with this class
 * directly.
 */

1390 25 VirtualFileMountMultifile 0 75777 25 VirtualFileMountMultifile 25 VirtualFileMountMultifile 0 0 0 1 1199 994 0 2 1200 1201 0 0 1 0 1389 0 0 0 0 66
/**
 * Maps a Multifile's contents into the VirtualFileSystem.
 */

1391 23 VirtualFileMountRamdisk 0 141313 23 VirtualFileMountRamdisk 23 VirtualFileMountRamdisk 0 0 0 1 1202 1204 0 1 1203 0 0 1 0 1389 0 0 0 0 384
/**
 * Simulates an actual directory on disk with in-memory storage.  This is
 * useful mainly for performing high level functions that expect disk I/O
 * without actually writing files to disk.  Naturally, there are significant
 * limits to the size of the files that may be written with this system; and
 * "files" written here are not automatically persistent between sessions.
 */

1392 22 VirtualFileMountSystem 0 141313 22 VirtualFileMountSystem 22 VirtualFileMountSystem 0 0 0 1 1205 1208 0 2 1206 1207 0 0 1 0 1389 0 0 0 0 66
/**
 * Maps an actual OS directory into the VirtualFileSystem.
 */

1393 17 VirtualFileSimple 0 141313 17 VirtualFileSimple 17 VirtualFileSimple 0 0 0 0 1212 0 3 1209 1210 1211 0 0 1 0 1387 0 0 0 0 183
/**
 * A simple file or directory within the VirtualFileSystem: this maps to
 * exactly one file on one mount point.  Most directories, and all regular
 * files, are of this kind.
 */

1394 13 TemporaryFile 0 75777 13 TemporaryFile 13 TemporaryFile 0 0 0 1 1213 998 0 1 1214 0 0 1 0 1366 0 0 0 0 206
/**
 * This is a special kind of FileReference class that automatically deletes
 * the file in question when it is deleted.  It is not responsible for
 * creating, opening, or closing the file, however.
 */

1395 17 IDecompressStream 0 141313 17 IDecompressStream 17 IDecompressStream 0 0 0 1 1215 1218 0 2 1216 1217 0 0 1 0 1375 0 0 0 0 323
/**
 * An input stream object that uses zlib to decompress (inflate) the input
 * from another source stream on-the-fly.
 *
 * Attach an IDecompressStream to an existing istream that provides compressed
 * data, and read the corresponding uncompressed data from the
 * IDecompressStream.
 *
 * Seeking is not supported.
 */

1396 15 OCompressStream 0 141313 15 OCompressStream 15 OCompressStream 0 0 0 1 1219 1222 0 2 1220 1221 0 0 1 0 1377 0 0 0 0 311
/**
 * An input stream object that uses zlib to compress (deflate) data to another
 * destination stream on-the-fly.
 *
 * Attach an OCompressStream to an existing ostream that will accept
 * compressed data, and write your uncompressed source data to the
 * OCompressStream.
 *
 * Seeking is not supported.
 */

1397 15 VirtualFileList 0 75777 15 VirtualFileList 15 VirtualFileList 0 0 0 1 1229 861 0 6 1223 1224 1225 1226 1227 1228 1 1642 0 1 0 1352 0 0 0 0 80
/**
 * A list of VirtualFiles, as returned by VirtualFile::scan_directory().
 */

1398 17 VirtualFileSystem 0 26625 17 VirtualFileSystem 17 VirtualFileSystem 0 0 0 1 1230 1231 1 1629 37 1232 1233 1234 1235 1236 1237 1238 1247 1248 1249 1250 1251 1252 1253 1254 1255 1256 1257 1258 1259 1260 1261 1262 1263 1264 1265 1266 1267 1268 1269 1270 1271 1272 1273 1274 1275 1276 1 1643 0 0 0 1 1399 352
/**
 * A hierarchy of directories and files that appears to be one continuous file
 * system, even though the files may originate from several different sources
 * that may not be related to the actual OS's file system.
 *
 * For instance, a VirtualFileSystem can transparently mount one or more
 * Multifiles as their own subdirectory hierarchies.
 */

1399 10 MountFlags 0 794624 29 VirtualFileSystem::MountFlags 29 VirtualFileSystem::MountFlags 1398 0 0 0 0 0 0 0 0 0 1 12 MF_read_only 31 VirtualFileSystem::MF_read_only 0
2 0 0

1400 29 PointerTo< VirtualFileMount > 0 141313 29 PointerTo< VirtualFileMount > 29 PointerTo< VirtualFileMount > 0 0 0 1 1241 1246 0 4 1242 1243 1244 1245 0 0 1 0 1401 0 0 0 0 0

1401 33 PointerToBase< VirtualFileMount > 0 43009 33 PointerToBase< VirtualFileMount > 33 PointerToBase< VirtualFileMount > 0 0 0 0 0 0 2 1239 1240 0 0 1 0 1325 0 0 0 0 0

1402 9 TrueClock 0 43009 9 TrueClock 9 TrueClock 0 0 0 0 0 4 1630 1631 1632 1633 6 1277 1278 1279 1280 1281 1282 0 0 0 0 0 467
/**
 * An interface to whatever real-time clock we might have available in the
 * current environment.  There is only one TrueClock in existence, and it
 * constructs itself.
 *
 * The TrueClock returns elapsed real time in seconds since some undefined
 * epoch.  Since it is not defined at what time precisely the clock indicates
 * zero, this value can only be meaningfully used to measure elapsed time, by
 * sampling it at two different times and subtracting.
 */

1403 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

1404 9 Patchfile 0 26625 9 Patchfile 9 Patchfile 0 0 0 1 1283 1284 5 1634 1635 1636 1637 1638 14 1285 1286 1287 1288 1289 1290 1291 1292 1293 1294 1295 1296 1297 1298 0 0 0 0 0 10
/**
 *
 */

1405 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 1406 0 0 0 0 0 0 0 0 0 0

1406 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

1407 13 HashVal const 0 8832 13 HashVal const 13 HashVal const 0 0 1372 0 0 0 0 0 0 0 0 0 0

1408 12 ProfileTimer 0 26625 12 ProfileTimer 12 ProfileTimer 0 0 0 1 1300 1301 0 9 1302 1303 1304 1305 1306 1307 1308 1309 1310 0 0 0 0 0 713
/*
    ProfileTimer

    HowTo:
      Create a ProfileTimer and hold onto it.
      Call init() whenever you like (the timer doesn't
        start yet).
      Call on() to start the timer.
      While the timer is on, call mark() at each point of interest,
        in the code you are timing.
      You can turn the timer off() and on() to skip things you
        don't want to time.
      When your timing is finished, call printTo() to see the
        results (e.g. myTimer.printTo(cerr)).

    Notes:
      You should be able to time things down to the millisecond
      well enough, but if you call on() and off() within micro-
      seconds of each other, I don't think you'll get very good
      results.
*/

1409 17 WeakPointerToVoid 0 141313 17 WeakPointerToVoid 17 WeakPointerToVoid 0 0 0 0 1313 0 2 1311 1312 0 0 1 0 1325 0 0 0 0 152
/**
 * This is the specialization of PointerToVoid for weak pointers.  It needs an
 * additional flag to indicate that the pointer has been deleted.
 */

1410 15 WindowsRegistry 0 141313 15 WindowsRegistry 15 WindowsRegistry 0 0 0 1 1319 1320 0 5 1314 1315 1316 1317 1318 0 0 0 0 2 1411 1412 249
/**
 * This class provides a hook to Python to read and write strings and integers
 * to the windows registry.  It automatically converts strings from utf-8
 * encoding and stores them in Unicode (and conversely reconverts them on
 * retrieval).
 */

1411 8 RegLevel 0 794624 25 WindowsRegistry::RegLevel 25 WindowsRegistry::RegLevel 1410 0 0 0 0 0 0 0 0 0 2 10 rl_machine 27 WindowsRegistry::rl_machine 0
0 7 rl_user 24 WindowsRegistry::rl_user 0
1 0 0

1412 4 Type 0 794624 21 WindowsRegistry::Type 21 WindowsRegistry::Type 1410 0 0 0 0 0 0 0 0 0 3 6 T_none 23 WindowsRegistry::T_none 0
0 5 T_int 22 WindowsRegistry::T_int 0
1 8 T_string 25 WindowsRegistry::T_string 0
2 0 0

1413 12 PTA_stdfloat 0 2105345 12 PTA_stdfloat 12 PTA_stdfloat 0 0 1341 0 0 0 0 0 0 0 0 0 0

1414 13 CPTA_stdfloat 0 2105345 13 CPTA_stdfloat 13 CPTA_stdfloat 0 0 1327 0 0 0 0 0 0 0 0 0 0

1415 37 ConstPointerToArray< double > const * 0 8576 37 ConstPointerToArray< double > const * 37 ConstPointerToArray< double > const * 0 0 1416 0 0 0 0 0 0 0 0 0 0

1416 35 ConstPointerToArray< double > const 0 8832 35 ConstPointerToArray< double > const 35 ConstPointerToArray< double > const 0 0 1322 0 0 0 0 0 0 0 0 0 0

1417 31 ConstPointerToArray< double > * 0 8576 31 ConstPointerToArray< double > * 31 ConstPointerToArray< double > * 0 0 1322 0 0 0 0 0 0 0 0 0 0

1418 32 PointerToArray< double > const * 0 8576 32 PointerToArray< double > const * 32 PointerToArray< double > const * 0 0 1419 0 0 0 0 0 0 0 0 0 0

1419 30 PointerToArray< double > const 0 8832 30 PointerToArray< double > const 30 PointerToArray< double > const 0 0 1340 0 0 0 0 0 0 0 0 0 0

1420 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

1421 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 1422 0 0 0 0 0 0 0 0 0 0

1422 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 1423 0 0 0 0 0 0 0 0 0 0

1423 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

1424 11 Py_buffer * 0 8576 11 Py_buffer * 11 Py_buffer * 0 0 1425 0 0 0 0 0 0 0 0 0 0

1425 9 Py_buffer 0 2105344 9 Py_buffer 9 Py_buffer 0 0 1426 0 0 0 0 0 0 0 0 0 0

1426 10 bufferinfo 0 1024 10 bufferinfo 10 bufferinfo 0 0 0 0 0 0 0 0 0 0 0 0 0

1427 36 PointerToArrayBase< double > const * 0 8576 36 PointerToArrayBase< double > const * 36 PointerToArrayBase< double > const * 0 0 1428 0 0 0 0 0 0 0 0 0 0

1428 34 PointerToArrayBase< double > const 0 8832 34 PointerToArrayBase< double > const 34 PointerToArrayBase< double > const 0 0 1323 0 0 0 0 0 0 0 0 0 0

1429 51 PointerToBase< ReferenceCountedVector< double > > * 0 8576 51 PointerToBase< ReferenceCountedVector< double > > * 51 PointerToBase< ReferenceCountedVector< double > > * 0 0 1324 0 0 0 0 0 0 0 0 0 0

1430 57 PointerToBase< ReferenceCountedVector< double > > const * 0 8576 57 PointerToBase< ReferenceCountedVector< double > > const * 57 PointerToBase< ReferenceCountedVector< double > > const * 0 0 1431 0 0 0 0 0 0 0 0 0 0

1431 55 PointerToBase< ReferenceCountedVector< double > > const 0 8832 55 PointerToBase< ReferenceCountedVector< double > > const 55 PointerToBase< ReferenceCountedVector< double > > const 0 0 1324 0 0 0 0 0 0 0 0 0 0

1432 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 1377 0 0 0 0 0 0 0 0 0 0

1433 21 PointerToVoid const * 0 8576 21 PointerToVoid const * 21 PointerToVoid const * 0 0 1434 0 0 0 0 0 0 0 0 0 0

1434 19 PointerToVoid const 0 8832 19 PointerToVoid const 19 PointerToVoid const 0 0 1325 0 0 0 0 0 0 0 0 0 0

1435 36 ConstPointerToArray< float > const * 0 8576 36 ConstPointerToArray< float > const * 36 ConstPointerToArray< float > const * 0 0 1436 0 0 0 0 0 0 0 0 0 0

1436 34 ConstPointerToArray< float > const 0 8832 34 ConstPointerToArray< float > const 34 ConstPointerToArray< float > const 0 0 1328 0 0 0 0 0 0 0 0 0 0

1437 30 ConstPointerToArray< float > * 0 8576 30 ConstPointerToArray< float > * 30 ConstPointerToArray< float > * 0 0 1328 0 0 0 0 0 0 0 0 0 0

1438 31 PointerToArray< float > const * 0 8576 31 PointerToArray< float > const * 31 PointerToArray< float > const * 0 0 1439 0 0 0 0 0 0 0 0 0 0

1439 29 PointerToArray< float > const 0 8832 29 PointerToArray< float > const 29 PointerToArray< float > const 0 0 1342 0 0 0 0 0 0 0 0 0 0

1440 35 PointerToArrayBase< float > const * 0 8576 35 PointerToArrayBase< float > const * 35 PointerToArrayBase< float > const * 0 0 1441 0 0 0 0 0 0 0 0 0 0

1441 33 PointerToArrayBase< float > const 0 8832 33 PointerToArrayBase< float > const 33 PointerToArrayBase< float > const 0 0 1329 0 0 0 0 0 0 0 0 0 0

1442 50 PointerToBase< ReferenceCountedVector< float > > * 0 8576 50 PointerToBase< ReferenceCountedVector< float > > * 50 PointerToBase< ReferenceCountedVector< float > > * 0 0 1330 0 0 0 0 0 0 0 0 0 0

1443 56 PointerToBase< ReferenceCountedVector< float > > const * 0 8576 56 PointerToBase< ReferenceCountedVector< float > > const * 56 PointerToBase< ReferenceCountedVector< float > > const * 0 0 1444 0 0 0 0 0 0 0 0 0 0

1444 54 PointerToBase< ReferenceCountedVector< float > > const 0 8832 54 PointerToBase< ReferenceCountedVector< float > > const 54 PointerToBase< ReferenceCountedVector< float > > const 0 0 1330 0 0 0 0 0 0 0 0 0 0

1445 34 ConstPointerToArray< int > const * 0 8576 34 ConstPointerToArray< int > const * 34 ConstPointerToArray< int > const * 0 0 1446 0 0 0 0 0 0 0 0 0 0

1446 32 ConstPointerToArray< int > const 0 8832 32 ConstPointerToArray< int > const 32 ConstPointerToArray< int > const 0 0 1332 0 0 0 0 0 0 0 0 0 0

1447 28 ConstPointerToArray< int > * 0 8576 28 ConstPointerToArray< int > * 28 ConstPointerToArray< int > * 0 0 1332 0 0 0 0 0 0 0 0 0 0

1448 29 PointerToArray< int > const * 0 8576 29 PointerToArray< int > const * 29 PointerToArray< int > const * 0 0 1449 0 0 0 0 0 0 0 0 0 0

1449 27 PointerToArray< int > const 0 8832 27 PointerToArray< int > const 27 PointerToArray< int > const 0 0 1344 0 0 0 0 0 0 0 0 0 0

1450 33 PointerToArrayBase< int > const * 0 8576 33 PointerToArrayBase< int > const * 33 PointerToArrayBase< int > const * 0 0 1451 0 0 0 0 0 0 0 0 0 0

1451 31 PointerToArrayBase< int > const 0 8832 31 PointerToArrayBase< int > const 31 PointerToArrayBase< int > const 0 0 1333 0 0 0 0 0 0 0 0 0 0

1452 48 PointerToBase< ReferenceCountedVector< int > > * 0 8576 48 PointerToBase< ReferenceCountedVector< int > > * 48 PointerToBase< ReferenceCountedVector< int > > * 0 0 1334 0 0 0 0 0 0 0 0 0 0

1453 54 PointerToBase< ReferenceCountedVector< int > > const * 0 8576 54 PointerToBase< ReferenceCountedVector< int > > const * 54 PointerToBase< ReferenceCountedVector< int > > const * 0 0 1454 0 0 0 0 0 0 0 0 0 0

1454 52 PointerToBase< ReferenceCountedVector< int > > const 0 8832 52 PointerToBase< ReferenceCountedVector< int > > const 52 PointerToBase< ReferenceCountedVector< int > > const 0 0 1334 0 0 0 0 0 0 0 0 0 0

1455 44 ConstPointerToArray< unsigned char > const * 0 8576 44 ConstPointerToArray< unsigned char > const * 44 ConstPointerToArray< unsigned char > const * 0 0 1456 0 0 0 0 0 0 0 0 0 0

1456 42 ConstPointerToArray< unsigned char > const 0 8832 42 ConstPointerToArray< unsigned char > const 42 ConstPointerToArray< unsigned char > const 0 0 1336 0 0 0 0 0 0 0 0 0 0

1457 38 ConstPointerToArray< unsigned char > * 0 8576 38 ConstPointerToArray< unsigned char > * 38 ConstPointerToArray< unsigned char > * 0 0 1336 0 0 0 0 0 0 0 0 0 0

1458 39 PointerToArray< unsigned char > const * 0 8576 39 PointerToArray< unsigned char > const * 39 PointerToArray< unsigned char > const * 0 0 1459 0 0 0 0 0 0 0 0 0 0

1459 37 PointerToArray< unsigned char > const 0 8832 37 PointerToArray< unsigned char > const 37 PointerToArray< unsigned char > const 0 0 1346 0 0 0 0 0 0 0 0 0 0

1460 13 unsigned char 0 8198 13 unsigned char 13 unsigned char 0 5 0 0 0 0 0 0 0 0 0 0 0

1461 43 PointerToArrayBase< unsigned char > const * 0 8576 43 PointerToArrayBase< unsigned char > const * 43 PointerToArrayBase< unsigned char > const * 0 0 1462 0 0 0 0 0 0 0 0 0 0

1462 41 PointerToArrayBase< unsigned char > const 0 8832 41 PointerToArrayBase< unsigned char > const 41 PointerToArrayBase< unsigned char > const 0 0 1337 0 0 0 0 0 0 0 0 0 0

1463 58 PointerToBase< ReferenceCountedVector< unsigned char > > * 0 8576 58 PointerToBase< ReferenceCountedVector< unsigned char > > * 58 PointerToBase< ReferenceCountedVector< unsigned char > > * 0 0 1338 0 0 0 0 0 0 0 0 0 0

1464 64 PointerToBase< ReferenceCountedVector< unsigned char > > const * 0 8576 64 PointerToBase< ReferenceCountedVector< unsigned char > > const * 64 PointerToBase< ReferenceCountedVector< unsigned char > > const * 0 0 1465 0 0 0 0 0 0 0 0 0 0

1465 62 PointerToBase< ReferenceCountedVector< unsigned char > > const 0 8832 62 PointerToBase< ReferenceCountedVector< unsigned char > > const 62 PointerToBase< ReferenceCountedVector< unsigned char > > const 0 0 1338 0 0 0 0 0 0 0 0 0 0

1466 26 PointerToArray< double > * 0 8576 26 PointerToArray< double > * 26 PointerToArray< double > * 0 0 1340 0 0 0 0 0 0 0 0 0 0

1467 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 1468 0 0 0 0 0 0 0 0 0 0

1468 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

1469 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

1470 25 PointerToArray< float > * 0 8576 25 PointerToArray< float > * 25 PointerToArray< float > * 0 0 1342 0 0 0 0 0 0 0 0 0 0

1471 23 PointerToArray< int > * 0 8576 23 PointerToArray< int > * 23 PointerToArray< int > * 0 0 1344 0 0 0 0 0 0 0 0 0 0

1472 33 PointerToArray< unsigned char > * 0 8576 33 PointerToArray< unsigned char > * 33 PointerToArray< unsigned char > * 0 0 1346 0 0 0 0 0 0 0 0 0 0

1473 19 MemoryUsage const * 0 8576 19 MemoryUsage const * 19 MemoryUsage const * 0 0 1474 0 0 0 0 0 0 0 0 0 0

1474 17 MemoryUsage const 0 8832 17 MemoryUsage const 17 MemoryUsage const 0 0 1347 0 0 0 0 0 0 0 0 0 0

1475 13 MemoryUsage * 0 8576 13 MemoryUsage * 13 MemoryUsage * 0 0 1347 0 0 0 0 0 0 0 0 0 0

1476 21 MemoryUsagePointers * 0 8576 21 MemoryUsagePointers * 21 MemoryUsagePointers * 0 0 1373 0 0 0 0 0 0 0 0 0 0

1477 22 ReferenceCount const * 0 8576 22 ReferenceCount const * 22 ReferenceCount const * 0 0 1478 0 0 0 0 0 0 0 0 0 0

1478 20 ReferenceCount const 0 8832 20 ReferenceCount const 20 ReferenceCount const 0 0 1352 0 0 0 0 0 0 0 0 0 0

1479 16 ReferenceCount * 0 8576 16 ReferenceCount * 16 ReferenceCount * 0 0 1352 0 0 0 0 0 0 0 0 0 0

1480 12 MemoryBase * 0 8576 12 MemoryBase * 12 MemoryBase * 0 0 1326 0 0 0 0 0 0 0 0 0 0

1481 14 Buffer const * 0 8576 14 Buffer const * 14 Buffer const * 0 0 1482 0 0 0 0 0 0 0 0 0 0

1482 12 Buffer const 0 8832 12 Buffer const 12 Buffer const 0 0 1354 0 0 0 0 0 0 0 0 0 0

1483 8 Buffer * 0 8576 8 Buffer * 8 Buffer * 0 0 1354 0 0 0 0 0 0 0 0 0 0

1484 27 PStatCollectorForwardBase * 0 8576 27 PStatCollectorForwardBase * 27 PStatCollectorForwardBase * 0 0 1355 0 0 0 0 0 0 0 0 0 0

1485 26 NodeReferenceCount const * 0 8576 26 NodeReferenceCount const * 26 NodeReferenceCount const * 0 0 1486 0 0 0 0 0 0 0 0 0 0

1486 24 NodeReferenceCount const 0 8832 24 NodeReferenceCount const 24 NodeReferenceCount const 0 0 1356 0 0 0 0 0 0 0 0 0 0

1487 10 Datagram * 0 8576 10 Datagram * 10 Datagram * 0 0 1357 0 0 0 0 0 0 0 0 0 0

1488 16 Datagram const * 0 8576 16 Datagram const * 16 Datagram const * 0 0 1489 0 0 0 0 0 0 0 0 0 0

1489 14 Datagram const 0 8832 14 Datagram const 14 Datagram const 0 0 1357 0 0 0 0 0 0 0 0 0 0

1490 12 vector_uchar 0 2105344 12 vector_uchar 12 vector_uchar 0 0 1491 0 0 0 0 0 0 0 0 0 0

1491 24 pvector< unsigned char > 0 2048 24 pvector< unsigned char > 24 pvector< unsigned char > 0 0 0 0 0 0 0 0 0 0 0 0 0

1492 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

1493 6 int8_t 0 2105344 6 int8_t 6 int8_t 0 0 1494 0 0 0 0 0 0 0 0 0 0

1494 11 signed char 0 8202 11 signed char 11 signed char 0 5 0 0 0 0 0 0 0 0 0 0 0

1495 7 uint8_t 0 2105344 7 uint8_t 7 uint8_t 0 0 1460 0 0 0 0 0 0 0 0 0 0

1496 7 int16_t 0 2105344 7 int16_t 7 int16_t 0 0 1497 0 0 0 0 0 0 0 0 0 0

1497 9 short int 0 8258 9 short int 9 short int 0 1 0 0 0 0 0 0 0 0 0 0 0

1498 7 int32_t 0 2105344 7 int32_t 7 int32_t 0 0 1353 0 0 0 0 0 0 0 0 0 0

1499 7 int64_t 0 2105344 7 int64_t 7 int64_t 0 0 1369 0 0 0 0 0 0 0 0 0 0

1500 8 uint16_t 0 2105344 8 uint16_t 8 uint16_t 0 0 1501 0 0 0 0 0 0 0 0 0 0

1501 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

1502 8 uint32_t 0 2105344 8 uint32_t 8 uint32_t 0 0 1492 0 0 0 0 0 0 0 0 0 0

1503 8 uint64_t 0 2105344 8 uint64_t 8 uint64_t 0 0 1351 0 0 0 0 0 0 0 0 0 0

1504 10 PN_float32 0 2105344 10 PN_float32 10 PN_float32 0 0 1406 0 0 0 0 0 0 0 0 0 0

1505 10 PN_float64 0 2105344 10 PN_float64 10 PN_float64 0 0 1403 0 0 0 0 0 0 0 0 0 0

1506 11 PTA_uchar * 0 8576 11 PTA_uchar * 11 PTA_uchar * 0 0 1345 0 0 0 0 0 0 0 0 0 0

1507 12 CPTA_uchar * 0 8576 12 CPTA_uchar * 12 CPTA_uchar * 0 0 1335 0 0 0 0 0 0 0 0 0 0

1508 19 DatagramGenerator * 0 8576 19 DatagramGenerator * 19 DatagramGenerator * 0 0 1359 0 0 0 0 0 0 0 0 0 0

1509 13 SubfileInfo * 0 8576 13 SubfileInfo * 13 SubfileInfo * 0 0 1386 0 0 0 0 0 0 0 0 0 0

1510 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 1362 0 0 0 0 0 0 0 0 0 0

1511 25 DatagramGenerator const * 0 8576 25 DatagramGenerator const * 25 DatagramGenerator const * 0 0 1512 0 0 0 0 0 0 0 0 0 0

1512 23 DatagramGenerator const 0 8832 23 DatagramGenerator const 23 DatagramGenerator const 0 0 1359 0 0 0 0 0 0 0 0 0 0

1513 6 time_t 0 2105344 6 time_t 6 time_t 0 0 1514 0 0 0 0 0 0 0 0 0 0

1514 8 long int 0 8210 8 long int 8 long int 0 1 0 0 0 0 0 0 0 0 0 0 0

1515 13 VirtualFile * 0 8576 13 VirtualFile * 13 VirtualFile * 0 0 1387 0 0 0 0 0 0 0 0 0 0

1516 18 DatagramIterator * 0 8576 18 DatagramIterator * 18 DatagramIterator * 0 0 1360 0 0 0 0 0 0 0 0 0 0

1517 24 DatagramIterator const * 0 8576 24 DatagramIterator const * 24 DatagramIterator const * 0 0 1518 0 0 0 0 0 0 0 0 0 0

1518 22 DatagramIterator const 0 8832 22 DatagramIterator const 22 DatagramIterator const 0 0 1360 0 0 0 0 0 0 0 0 0 0

1519 14 DatagramSink * 0 8576 14 DatagramSink * 14 DatagramSink * 0 0 1361 0 0 0 0 0 0 0 0 0 0

1520 19 SubfileInfo const * 0 8576 19 SubfileInfo const * 19 SubfileInfo const * 0 0 1521 0 0 0 0 0 0 0 0 0 0

1521 17 SubfileInfo const 0 8832 17 SubfileInfo const 17 SubfileInfo const 0 0 1386 0 0 0 0 0 0 0 0 0 0

1522 15 FileReference * 0 8576 15 FileReference * 15 FileReference * 0 0 1366 0 0 0 0 0 0 0 0 0 0

1523 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 1367 0 0 0 0 0 0 0 0 0 0

1524 13 TypedObject * 0 8576 13 TypedObject * 13 TypedObject * 0 0 1358 0 0 0 0 0 0 0 0 0 0

1525 9 Ramfile * 0 8576 9 Ramfile * 9 Ramfile * 0 0 1371 0 0 0 0 0 0 0 0 0 0

1526 15 Ramfile const * 0 8576 15 Ramfile const * 15 Ramfile const * 0 0 1527 0 0 0 0 0 0 0 0 0 0

1527 13 Ramfile const 0 8832 13 Ramfile const 13 Ramfile const 0 0 1371 0 0 0 0 0 0 0 0 0 0

1528 9 HashVal * 0 8576 9 HashVal * 9 HashVal * 0 0 1372 0 0 0 0 0 0 0 0 0 0

1529 15 HashVal const * 0 8576 15 HashVal const * 15 HashVal const * 0 0 1407 0 0 0 0 0 0 0 0 0 0

1530 9 istream * 0 8576 14 std::istream * 14 std::istream * 0 0 1375 0 0 0 0 0 0 0 0 0 0

1531 14 StreamWriter * 0 8576 14 StreamWriter * 14 StreamWriter * 0 0 1532 0 0 0 0 0 0 0 0 0 0

1532 12 StreamWriter 0 2048 12 StreamWriter 12 StreamWriter 0 0 0 0 0 0 0 0 0 0 0 0 300
/**
 * A StreamWriter object is used to write sequential binary data directly to
 * an ostream.  Its interface is very similar to Datagram by design; it's
 * primarily intended as a convenience to eliminate the overhead of writing
 * bytes to a Datagram and then writing the Datagram to a stream.
 */

1533 14 StreamReader * 0 8576 14 StreamReader * 14 StreamReader * 0 0 1534 0 0 0 0 0 0 0 0 0 0

1534 12 StreamReader 0 2048 12 StreamReader 12 StreamReader 0 0 0 0 0 0 0 0 0 0 0 0 158
/**
 * A class to read sequential binary data directly from an istream.  Its
 * interface is similar to DatagramIterator by design; see also StreamWriter.
 */

1535 27 MemoryUsagePointers const * 0 8576 27 MemoryUsagePointers const * 27 MemoryUsagePointers const * 0 0 1536 0 0 0 0 0 0 0 0 0 0

1536 25 MemoryUsagePointers const 0 8832 25 MemoryUsagePointers const 25 MemoryUsagePointers const 0 0 1373 0 0 0 0 0 0 0 0 0 0

1537 12 ISubStream * 0 8576 12 ISubStream * 12 ISubStream * 0 0 1374 0 0 0 0 0 0 0 0 0 0

1538 16 IStreamWrapper * 0 8576 16 IStreamWrapper * 16 IStreamWrapper * 0 0 1539 0 0 0 0 0 0 0 0 0 0

1539 14 IStreamWrapper 0 2048 14 IStreamWrapper 14 IStreamWrapper 0 0 0 0 0 0 0 0 0 0 0 0 168
/**
 * This class provides a locking wrapper around an arbitrary istream pointer.
 * A thread may use this class to perform an atomic seek/read/gcount
 * operation.
 */

1540 12 OSubStream * 0 8576 12 OSubStream * 12 OSubStream * 0 0 1376 0 0 0 0 0 0 0 0 0 0

1541 16 OStreamWrapper * 0 8576 16 OStreamWrapper * 16 OStreamWrapper * 0 0 1542 0 0 0 0 0 0 0 0 0 0

1542 14 OStreamWrapper 0 2048 14 OStreamWrapper 14 OStreamWrapper 0 0 0 0 0 0 0 0 0 0 0 0 159
/**
 * This class provides a locking wrapper around an arbitrary ostream pointer.
 * A thread may use this class to perform an atomic seek/write operation.
 */

1543 11 SubStream * 0 8576 11 SubStream * 11 SubStream * 0 0 1378 0 0 0 0 0 0 0 0 0 0

1544 15 StreamWrapper * 0 8576 15 StreamWrapper * 15 StreamWrapper * 0 0 1545 0 0 0 0 0 0 0 0 0 0

1545 13 StreamWrapper 0 2048 13 StreamWrapper 13 StreamWrapper 0 0 0 0 0 0 0 0 0 0 0 0 97
/**
 * This class provides a locking wrapper around a combination ostream/istream
 * pointer.
 */

1546 11 Multifile * 0 8576 11 Multifile * 11 Multifile * 0 0 1380 0 0 0 0 0 0 0 0 0 0

1547 10 iostream * 0 8576 15 std::iostream * 15 std::iostream * 0 0 1379 0 0 0 0 0 0 0 0 0 0

1548 17 Multifile const * 0 8576 17 Multifile const * 17 Multifile const * 0 0 1549 0 0 0 0 0 0 0 0 0 0

1549 15 Multifile const 0 8832 15 Multifile const 15 Multifile const 0 0 1380 0 0 0 0 0 0 0 0 0 0

1550 15 vector_string * 0 8576 15 vector_string * 15 vector_string * 0 0 1551 0 0 0 0 0 0 0 0 0 0

1551 13 vector_string 0 2105344 13 vector_string 13 vector_string 0 0 1552 0 0 0 0 0 0 0 0 0 0

1552 17 pvector< string > 0 2048 22 pvector< std::string > 22 pvector< std::string > 0 0 0 0 0 0 0 0 0 0 0 0 0

1553 15 Namable const * 0 8576 15 Namable const * 15 Namable const * 0 0 1554 0 0 0 0 0 0 0 0 0 0

1554 13 Namable const 0 8832 13 Namable const 13 Namable const 0 0 1383 0 0 0 0 0 0 0 0 0 0

1555 9 Namable * 0 8576 9 Namable * 9 Namable * 0 0 1383 0 0 0 0 0 0 0 0 0 0

1556 16 OpenSSLWrapper * 0 8576 16 OpenSSLWrapper * 16 OpenSSLWrapper * 0 0 1385 0 0 0 0 0 0 0 0 0 0

1557 12 X509_STORE * 0 8576 12 X509_STORE * 12 X509_STORE * 0 0 1558 0 0 0 0 0 0 0 0 0 0

1558 10 X509_STORE 0 2105344 10 X509_STORE 10 X509_STORE 0 0 1559 0 0 0 0 0 0 0 0 0 0

1559 13 x509_store_st 0 1024 13 x509_store_st 13 x509_store_st 0 0 0 0 0 0 0 0 0 0 0 0 0

1560 10 streamsize 0 2105344 15 std::streamsize 15 std::streamsize 0 0 1369 0 0 0 0 0 0 0 0 0 0

1561 19 VirtualFile const * 0 8576 19 VirtualFile const * 19 VirtualFile const * 0 0 1562 0 0 0 0 0 0 0 0 0 0

1562 17 VirtualFile const 0 8832 17 VirtualFile const 17 VirtualFile const 0 0 1387 0 0 0 0 0 0 0 0 0 0

1563 19 VirtualFileSystem * 0 8576 19 VirtualFileSystem * 19 VirtualFileSystem * 0 0 1398 0 0 0 0 0 0 0 0 0 0

1564 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 1363 0 0 0 0 0 0 0 0 0 0

1565 17 VirtualFileList * 0 8576 17 VirtualFileList * 17 VirtualFileList * 0 0 1397 0 0 0 0 0 0 0 0 0 0

1566 24 VirtualFileMount const * 0 8576 24 VirtualFileMount const * 24 VirtualFileMount const * 0 0 1567 0 0 0 0 0 0 0 0 0 0

1567 22 VirtualFileMount const 0 8832 22 VirtualFileMount const 22 VirtualFileMount const 0 0 1389 0 0 0 0 0 0 0 0 0 0

1568 27 VirtualFileMountMultifile * 0 8576 27 VirtualFileMountMultifile * 27 VirtualFileMountMultifile * 0 0 1390 0 0 0 0 0 0 0 0 0 0

1569 33 VirtualFileMountMultifile const * 0 8576 33 VirtualFileMountMultifile const * 33 VirtualFileMountMultifile const * 0 0 1570 0 0 0 0 0 0 0 0 0 0

1570 31 VirtualFileMountMultifile const 0 8832 31 VirtualFileMountMultifile const 31 VirtualFileMountMultifile const 0 0 1390 0 0 0 0 0 0 0 0 0 0

1571 25 VirtualFileMountRamdisk * 0 8576 25 VirtualFileMountRamdisk * 25 VirtualFileMountRamdisk * 0 0 1391 0 0 0 0 0 0 0 0 0 0

1572 24 VirtualFileMountSystem * 0 8576 24 VirtualFileMountSystem * 24 VirtualFileMountSystem * 0 0 1392 0 0 0 0 0 0 0 0 0 0

1573 30 VirtualFileMountSystem const * 0 8576 30 VirtualFileMountSystem const * 30 VirtualFileMountSystem const * 0 0 1574 0 0 0 0 0 0 0 0 0 0

1574 28 VirtualFileMountSystem const 0 8832 28 VirtualFileMountSystem const 28 VirtualFileMountSystem const 0 0 1392 0 0 0 0 0 0 0 0 0 0

1575 25 VirtualFileSimple const * 0 8576 25 VirtualFileSimple const * 25 VirtualFileSimple const * 0 0 1576 0 0 0 0 0 0 0 0 0 0

1576 23 VirtualFileSimple const 0 8832 23 VirtualFileSimple const 23 VirtualFileSimple const 0 0 1393 0 0 0 0 0 0 0 0 0 0

1577 18 VirtualFileMount * 0 8576 18 VirtualFileMount * 18 VirtualFileMount * 0 0 1389 0 0 0 0 0 0 0 0 0 0

1578 15 TemporaryFile * 0 8576 15 TemporaryFile * 15 TemporaryFile * 0 0 1394 0 0 0 0 0 0 0 0 0 0

1579 21 TemporaryFile const * 0 8576 21 TemporaryFile const * 21 TemporaryFile const * 0 0 1580 0 0 0 0 0 0 0 0 0 0

1580 19 TemporaryFile const 0 8832 19 TemporaryFile const 19 TemporaryFile const 0 0 1394 0 0 0 0 0 0 0 0 0 0

1581 19 IDecompressStream * 0 8576 19 IDecompressStream * 19 IDecompressStream * 0 0 1395 0 0 0 0 0 0 0 0 0 0

1582 17 OCompressStream * 0 8576 17 OCompressStream * 17 OCompressStream * 0 0 1396 0 0 0 0 0 0 0 0 0 0

1583 23 VirtualFileList const * 0 8576 23 VirtualFileList const * 23 VirtualFileList const * 0 0 1584 0 0 0 0 0 0 0 0 0 0

1584 21 VirtualFileList const 0 8832 21 VirtualFileList const 21 VirtualFileList const 0 0 1397 0 0 0 0 0 0 0 0 0 0

1585 25 VirtualFileSystem const * 0 8576 25 VirtualFileSystem const * 25 VirtualFileSystem const * 0 0 1586 0 0 0 0 0 0 0 0 0 0

1586 23 VirtualFileSystem const 0 8832 23 VirtualFileSystem const 23 VirtualFileSystem const 0 0 1398 0 0 0 0 0 0 0 0 0 0

1587 19 DSearchPath const * 0 8576 19 DSearchPath const * 19 DSearchPath const * 0 0 1588 0 0 0 0 0 0 0 0 0 0

1588 17 DSearchPath const 0 8832 17 DSearchPath const 17 DSearchPath const 0 0 1589 0 0 0 0 0 0 0 0 0 0

1589 11 DSearchPath 0 2048 11 DSearchPath 11 DSearchPath 0 0 0 0 0 0 0 0 0 0 0 0 304
/**
 * This class stores a list of directories that can be searched, in order, to
 * locate a particular file.  It is normally constructed by passing it a
 * traditional searchpath-style string, e.g.  a list of directory names
 * delimited by spaces or colons, but it can also be built up explicitly.
 */

1590 9 Results * 0 8576 22 DSearchPath::Results * 22 DSearchPath::Results * 0 0 1591 0 0 0 0 0 0 0 0 0 0

1591 7 Results 0 264192 20 DSearchPath::Results 20 DSearchPath::Results 1589 0 0 0 0 0 0 0 0 0 0 0 0

1592 31 PointerTo< VirtualFileMount > * 0 8576 31 PointerTo< VirtualFileMount > * 31 PointerTo< VirtualFileMount > * 0 0 1400 0 0 0 0 0 0 0 0 0 0

1593 17 decltype(nullptr) 0 8194 17 decltype(nullptr) 17 decltype(nullptr) 0 9 0 0 0 0 0 0 0 0 0 0 0

1594 37 PointerTo< VirtualFileMount > const * 0 8576 37 PointerTo< VirtualFileMount > const * 37 PointerTo< VirtualFileMount > const * 0 0 1595 0 0 0 0 0 0 0 0 0 0

1595 35 PointerTo< VirtualFileMount > const 0 8832 35 PointerTo< VirtualFileMount > const 35 PointerTo< VirtualFileMount > const 0 0 1400 0 0 0 0 0 0 0 0 0 0

1596 35 PointerToBase< VirtualFileMount > * 0 8576 35 PointerToBase< VirtualFileMount > * 35 PointerToBase< VirtualFileMount > * 0 0 1401 0 0 0 0 0 0 0 0 0 0

1597 41 PointerToBase< VirtualFileMount > const * 0 8576 41 PointerToBase< VirtualFileMount > const * 41 PointerToBase< VirtualFileMount > const * 0 0 1598 0 0 0 0 0 0 0 0 0 0

1598 39 PointerToBase< VirtualFileMount > const 0 8832 39 PointerToBase< VirtualFileMount > const 39 PointerToBase< VirtualFileMount > const 0 0 1401 0 0 0 0 0 0 0 0 0 0

1599 11 TrueClock * 0 8576 11 TrueClock * 11 TrueClock * 0 0 1402 0 0 0 0 0 0 0 0 0 0

1600 17 TrueClock const * 0 8576 17 TrueClock const * 17 TrueClock const * 0 0 1601 0 0 0 0 0 0 0 0 0 0

1601 15 TrueClock const 0 8832 15 TrueClock const 15 TrueClock const 0 0 1402 0 0 0 0 0 0 0 0 0 0

1602 11 Patchfile * 0 8576 11 Patchfile * 11 Patchfile * 0 0 1404 0 0 0 0 0 0 0 0 0 0

1603 17 Patchfile const * 0 8576 17 Patchfile const * 17 Patchfile const * 0 0 1604 0 0 0 0 0 0 0 0 0 0

1604 15 Patchfile const 0 8832 15 Patchfile const 15 Patchfile const 0 0 1404 0 0 0 0 0 0 0 0 0 0

1605 20 ProfileTimer const * 0 8576 20 ProfileTimer const * 20 ProfileTimer const * 0 0 1606 0 0 0 0 0 0 0 0 0 0

1606 18 ProfileTimer const 0 8832 18 ProfileTimer const 18 ProfileTimer const 0 0 1408 0 0 0 0 0 0 0 0 0 0

1607 14 ProfileTimer * 0 8576 14 ProfileTimer * 14 ProfileTimer * 0 0 1408 0 0 0 0 0 0 0 0 0 0

1608 25 WeakPointerToVoid const * 0 8576 25 WeakPointerToVoid const * 25 WeakPointerToVoid const * 0 0 1609 0 0 0 0 0 0 0 0 0 0

1609 23 WeakPointerToVoid const 0 8832 23 WeakPointerToVoid const 23 WeakPointerToVoid const 0 0 1409 0 0 0 0 0 0 0 0 0 0

1610 17 WindowsRegistry * 0 8576 17 WindowsRegistry * 17 WindowsRegistry * 0 0 1410 0 0 0 0 0 0 0 0 0 0

1611 23 WindowsRegistry const * 0 8576 23 WindowsRegistry const * 23 WindowsRegistry const * 0 0 1612 0 0 0 0 0 0 0 0 0 0

1612 21 WindowsRegistry const 0 8832 21 WindowsRegistry const 21 WindowsRegistry const 0 0 1410 0 0 0 0 0 0 0 0 0 0

0
26
1613 8 tracking 0 2 1349 837 0 0 0 0 0 0 0 21 MemoryUsage::tracking 0

1614 8 counting 0 2 1349 838 0 0 0 0 0 0 0 21 MemoryUsage::counting 0

1615 16 current_cpp_size 0 2 1350 839 0 0 0 0 0 0 0 29 MemoryUsage::current_cpp_size 0

1616 14 total_cpp_size 0 2 1350 840 0 0 0 0 0 0 0 27 MemoryUsage::total_cpp_size 0

1617 22 panda_heap_single_size 0 2 1350 841 0 0 0 0 0 0 0 35 MemoryUsage::panda_heap_single_size 0

1618 21 panda_heap_array_size 0 2 1350 842 0 0 0 0 0 0 0 34 MemoryUsage::panda_heap_array_size 0

1619 19 panda_heap_overhead 0 2 1350 843 0 0 0 0 0 0 0 32 MemoryUsage::panda_heap_overhead 0

1620 15 panda_mmap_size 0 2 1350 844 0 0 0 0 0 0 0 28 MemoryUsage::panda_mmap_size 0

1621 13 external_size 0 2 1350 845 0 0 0 0 0 0 0 26 MemoryUsage::external_size 0

1622 10 total_size 0 2 1350 846 0 0 0 0 0 0 0 23 MemoryUsage::total_size 0

1623 9 ref_count 0 2 1353 862 0 0 0 0 0 0 0 25 ReferenceCount::ref_count 31
// The current reference count.

1624 8 filename 0 2 1362 986 0 0 0 0 0 0 0 22 DatagramSink::filename 0

1625 4 file 0 2 1364 987 0 0 0 0 0 0 0 18 DatagramSink::file 0

1626 8 file_pos 0 2 1368 988 0 0 0 0 0 0 0 22 DatagramSink::file_pos 0

1627 12 magic_number 0 2 1381 1133 0 0 0 0 0 0 0 23 Multifile::magic_number 0

1628 4 name 0 6 1384 1140 1137 0 0 0 0 0 0 13 Namable::name 0

1629 6 mounts 0 66 1400 1238 0 0 0 0 1237 0 0 25 VirtualFileSystem::mounts 0

1630 9 long_time 0 2 1403 1277 0 0 0 0 0 0 0 20 TrueClock::long_time 203
// get_long_time() returns the most accurate timer we have over a long
// interval.  It may not be very precise for measuring short intervals, but
// it should not drift substantially over the long haul.

1631 10 short_time 0 2 1403 1278 0 0 0 0 0 0 0 21 TrueClock::short_time 206
// get_short_time() returns the most precise timer we have over a short
// interval.  It may tend to drift over the long haul, but it should have
// lots of digits to measure short intervals very precisely.

1632 14 short_raw_time 0 2 1403 1279 0 0 0 0 0 0 0 25 TrueClock::short_raw_time 144
// get_short_raw_time() is like get_short_time(), but does not apply any
// corrections (e.g.  paranoid-clock) to the result returned by the OS.

1633 11 error_count 0 2 1353 1280 0 0 0 0 0 0 0 22 TrueClock::error_count 0

1634 8 progress 0 2 1405 1290 0 0 0 0 0 0 0 19 Patchfile::progress 0

1635 15 allow_multifile 0 6 1349 1292 1291 0 0 0 0 0 0 26 Patchfile::allow_multifile 0

1636 16 footprint_length 0 6 1353 1294 1293 0 0 0 0 0 0 27 Patchfile::footprint_length 0

1637 11 source_hash 0 10 1407 1297 0 1296 0 0 0 0 0 22 Patchfile::source_hash 0

1638 11 result_hash 0 2 1407 1298 0 0 0 0 0 0 0 22 Patchfile::result_hash 0

5
1639 12 get_pointers 0 1052 1053 33 MemoryUsagePointers::get_pointers 0

1640 18 get_typed_pointers 0 1052 1054 39 MemoryUsagePointers::get_typed_pointers 0

1641 17 get_subfile_names 0 1111 1116 28 Multifile::get_subfile_names 0

1642 9 get_files 0 1223 1224 26 VirtualFileList::get_files 0

1643 10 get_mounts 0 1237 1238 29 VirtualFileSystem::get_mounts 0

