1730992849
3 3
16 libp3distributed 4 nqI8 14 panda3d.direct 
62
62 21 CConnectionRepository 0 260 124 44 CConnectionRepository::CConnectionRepository 0 1 1 10
/**
 *
 */
110
explicit CConnectionRepository::CConnectionRepository(bool has_owner_view = false, bool threaded_net = false);

63 22 ~CConnectionRepository 0 516 124 45 CConnectionRepository::~CConnectionRepository 0 0 10
/**
 *
 */
52
CConnectionRepository::~CConnectionRepository(void);

64 11 get_dc_file 0 4 124 34 CConnectionRepository::get_dc_file 0 1 2 69
/**
 * Returns the DCFile object associated with this repository.
 */
56
inline DCFile &CConnectionRepository::get_dc_file(void);

65 14 has_owner_view 0 4 124 37 CConnectionRepository::has_owner_view 0 1 3 92
/**
 * Returns true if this repository can have 'owner' views of distributed
 * objects.
 */
62
inline bool CConnectionRepository::has_owner_view(void) const;

66 20 set_handle_c_updates 0 4 124 43 CConnectionRepository::set_handle_c_updates 0 1 4 153
/**
 * Set true to specify this repository should process distributed updates
 * internally in C++ code, or false if it should return them to Python.
 */
79
inline void CConnectionRepository::set_handle_c_updates(bool handle_c_updates);

67 20 get_handle_c_updates 0 4 124 43 CConnectionRepository::get_handle_c_updates 0 1 5 145
/**
 * Returns true if this repository will process distributed updates internally
 * in C++ code, or false if it will return them to Python.
 */
68
inline bool CConnectionRepository::get_handle_c_updates(void) const;

68 19 set_client_datagram 0 4 124 42 CConnectionRepository::set_client_datagram 0 1 6 281
/**
 * Sets the client_datagram flag.  If this is true, incoming datagrams are not
 * expected to be prefixed with the server routing information like message
 * sender, channel number, etc.; otherwise, these server fields are parsed and
 * removed from each incoming datagram.
 */
77
inline void CConnectionRepository::set_client_datagram(bool client_datagram);

69 19 get_client_datagram 0 4 124 42 CConnectionRepository::get_client_datagram 0 1 7 44
/**
 * Returns the client_datagram flag.
 */
67
inline bool CConnectionRepository::get_client_datagram(void) const;

70 31 set_handle_datagrams_internally 0 4 124 54 CConnectionRepository::set_handle_datagrams_internally 0 1 8 321
/**
 * Sets the handle_datagrams_internally flag.  When true, certain message
 * types can be handled by the C++ code in in this module.  When false, all
 * datagrams, regardless of message type, are passed up to Python for
 * processing.
 *
 * The CMU distributed-object implementation requires this to be set false.
 */
101
inline void CConnectionRepository::set_handle_datagrams_internally(bool handle_datagrams_internally);

71 31 get_handle_datagrams_internally 0 4 124 54 CConnectionRepository::get_handle_datagrams_internally 0 1 9 56
/**
 * Returns the handle_datagrams_internally flag.
 */
79
inline bool CConnectionRepository::get_handle_datagrams_internally(void) const;

72 19 set_tcp_header_size 0 4 124 42 CConnectionRepository::set_tcp_header_size 0 1 10 270
/**
 * Sets the header size of TCP packets.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */
69
void CConnectionRepository::set_tcp_header_size(int tcp_header_size);

73 19 get_tcp_header_size 0 4 124 42 CConnectionRepository::get_tcp_header_size 0 1 11 86
/**
 * Returns the current setting of TCP header size.  See set_tcp_header_size().
 */
66
inline int CConnectionRepository::get_tcp_header_size(void) const;

74 21 set_python_repository 0 4 124 44 CConnectionRepository::set_python_repository 0 1 12 200
/**
 * Records the pointer to the Python class that derives from
 * CConnectionRepository.  This allows the C++ implementation to directly
 * manipulation some python structures on the repository.
 */
86
inline void CConnectionRepository::set_python_repository(PyObject *python_repository);

75 19 set_connection_http 0 4 124 42 CConnectionRepository::set_connection_http 0 1 13 200
/**
 * Once a connection has been established via the HTTP interface, gets the
 * connection and uses it.  The supplied HTTPChannel object must have a
 * connection available via get_connection().
 */
70
void CConnectionRepository::set_connection_http(HTTPChannel *channel);

76 10 get_stream 0 4 124 33 CConnectionRepository::get_stream 0 1 14 160
/**
 * Returns the SocketStream that internally represents the already-established
 * HTTP connection.  Returns NULL if there is no current HTTP connection.
 */
54
SocketStream *CConnectionRepository::get_stream(void);

77 15 try_connect_net 0 4 124 38 CConnectionRepository::try_connect_net 0 1 15 155
/**
 * Uses Panda's "net" library to try to connect to the server and port named
 * in the indicated URL.  Returns true if successful, false otherwise.
 */
64
bool CConnectionRepository::try_connect_net(URLSpec const &url);

78 7 get_qcm 0 4 124 30 CConnectionRepository::get_qcm 0 1 16 85
/**
 * Returns the QueuedConnectionManager object associated with the repository.
 */
69
inline QueuedConnectionManager &CConnectionRepository::get_qcm(void);

79 6 get_cw 0 4 124 29 CConnectionRepository::get_cw 0 1 17 78
/**
 * Returns the ConnectionWriter object associated with the repository.
 */
61
inline ConnectionWriter &CConnectionRepository::get_cw(void);

80 7 get_qcr 0 4 124 30 CConnectionRepository::get_qcr 0 1 18 84
/**
 * Returns the QueuedConnectionReader object associated with the repository.
 */
68
inline QueuedConnectionReader &CConnectionRepository::get_qcr(void);

81 14 connect_native 0 4 124 37 CConnectionRepository::connect_native 0 1 19 91
/**
 * Connects to the server using Panda's low-level and fast "native net"
 * library.
 */
63
bool CConnectionRepository::connect_native(URLSpec const &url);

82 7 get_bdc 0 4 124 30 CConnectionRepository::get_bdc 0 1 20 92
/**
 * Returns the Buffered_DatagramConnection object associated with the
 * repository.
 */
73
inline Buffered_DatagramConnection &CConnectionRepository::get_bdc(void);

83 14 check_datagram 0 4 124 37 CConnectionRepository::check_datagram 0 1 21 226
/**
 * Returns true if a new datagram is available, false otherwise.  If the
 * return value is true, the new datagram may be retrieved via get_datagram(),
 * or preferably, with get_datagram_iterator() and get_msg_type().
 */
49
bool CConnectionRepository::check_datagram(void);

84 12 get_datagram 0 4 124 35 CConnectionRepository::get_datagram 0 1 22 102
/**
 * Fills the datagram object with the datagram most recently retrieved by
 * check_datagram().
 */
62
inline void CConnectionRepository::get_datagram(Datagram &dg);

85 21 get_datagram_iterator 0 4 124 44 CConnectionRepository::get_datagram_iterator 0 1 23 256
/**
 * Fills the DatagramIterator object with the iterator for the datagram most
 * recently retrieved by check_datagram().  This iterator has already read
 * past the datagram header and the message type, and is positioned at the
 * beginning of data.
 */
79
inline void CConnectionRepository::get_datagram_iterator(DatagramIterator &di);

86 15 get_msg_channel 0 4 124 38 CConnectionRepository::get_msg_channel 0 1 24 160
/**
 * Returns the channel(s) to which the current message was sent, according to
 * the datagram headers.  This information is not available to the client.
 */
81
inline CHANNEL_TYPE CConnectionRepository::get_msg_channel(int offset = 0) const;

87 21 get_msg_channel_count 0 4 124 44 CConnectionRepository::get_msg_channel_count 0 1 25 0
68
inline int CConnectionRepository::get_msg_channel_count(void) const;

88 14 get_msg_sender 0 4 124 37 CConnectionRepository::get_msg_sender 0 1 26 144
/**
 * Returns the sender ID of the current message, according to the datagram
 * headers.  This information is not available to the client.
 */
70
inline CHANNEL_TYPE CConnectionRepository::get_msg_sender(void) const;

89 12 get_msg_type 0 4 124 35 CConnectionRepository::get_msg_type 0 1 27 139
// INLINE unsigned char get_sec_code() const;

/**
 * Returns the type ID of the current message, according to the datagram
 * headers.
 */
68
inline unsigned int CConnectionRepository::get_msg_type(void) const;

90 23 get_overflow_event_name 0 4 124 46 CConnectionRepository::get_overflow_event_name 0 1 28 94
/**
 * Returns event string that will be thrown if the datagram reader queue
 * overflows.
 */
86
static inline std::string const &CConnectionRepository::get_overflow_event_name(void);

91 12 is_connected 0 4 124 35 CConnectionRepository::is_connected 0 1 29 303
/**
 * Returns true if the connection to the gameserver is established and still
 * good, false if we are not connected.  A false value means either (a) we
 * never successfully connected, (b) we explicitly called disconnect(), or (c)
 * we were connected, but the connection was spontaneously lost.
 */
47
bool CConnectionRepository::is_connected(void);

92 13 send_datagram 0 4 124 36 CConnectionRepository::send_datagram 0 1 30 177
/**
 * Queues the indicated datagram for sending to the server.  It may not get
 * sent immediately if collect_tcp is in effect; call flush() to guarantee it
 * is sent now.
 */
62
bool CConnectionRepository::send_datagram(Datagram const &dg);

93 25 set_want_message_bundling 0 4 124 48 CConnectionRepository::set_want_message_bundling 0 1 31 51
/**
 * Enable/disable outbound message bundling
 */
72
inline void CConnectionRepository::set_want_message_bundling(bool flag);

94 25 get_want_message_bundling 0 4 124 48 CConnectionRepository::get_want_message_bundling 0 1 32 51
/**
 * Returns true if message bundling enabled
 */
73
inline bool CConnectionRepository::get_want_message_bundling(void) const;

95 17 set_in_quiet_zone 0 4 124 40 CConnectionRepository::set_in_quiet_zone 0 1 33 43
/**
 * Enables/disables quiet zone mode
 */
64
inline void CConnectionRepository::set_in_quiet_zone(bool flag);

96 17 get_in_quiet_zone 0 4 124 40 CConnectionRepository::get_in_quiet_zone 0 1 34 59
/**
 * Returns true if repository is in quiet zone mode
 */
65
inline bool CConnectionRepository::get_in_quiet_zone(void) const;

97 20 start_message_bundle 0 4 124 43 CConnectionRepository::start_message_bundle 0 1 35 300
/**
 * Send a set of messages to the state server that will be processed
 * atomically.  For instance, you can do a combined setLocation/setPos and
 * prevent race conditions where clients briefly get the setLocation but not
 * the setPos, because the state server hasn't processed the setPos yet
 */
55
void CConnectionRepository::start_message_bundle(void);

98 20 is_bundling_messages 0 4 124 43 CConnectionRepository::is_bundling_messages 0 1 36 92
/**
 * Returns true if repository is queueing outgoing messages into a message
 * bundle
 */
68
inline bool CConnectionRepository::is_bundling_messages(void) const;

99 19 send_message_bundle 0 4 124 42 CConnectionRepository::send_message_bundle 0 1 37 79
/**
 * Send network messages queued up since startMessageBundle was called.
 */
99
void CConnectionRepository::send_message_bundle(unsigned int channel, unsigned int sender_channel);

100 23 abandon_message_bundles 0 4 124 46 CConnectionRepository::abandon_message_bundles 0 1 38 74
/**
 * throw out any msgs that have been queued up for message bundles
 */
58
void CConnectionRepository::abandon_message_bundles(void);

101 10 bundle_msg 0 4 124 33 CConnectionRepository::bundle_msg 0 1 39 10
/**
 *
 */
59
void CConnectionRepository::bundle_msg(Datagram const &dg);

102 14 consider_flush 0 4 124 37 CConnectionRepository::consider_flush 0 1 40 143
/**
 * Sends the most recently queued data if enough time has elapsed.  This only
 * has meaning if set_collect_tcp() has been set to true.
 */
49
bool CConnectionRepository::consider_flush(void);

103 5 flush 0 4 124 28 CConnectionRepository::flush 0 1 41 120
/**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */
40
bool CConnectionRepository::flush(void);

104 10 disconnect 0 4 124 33 CConnectionRepository::disconnect 0 1 42 47
/**
 * Closes the connection to the server.
 */
45
void CConnectionRepository::disconnect(void);

105 8 shutdown 0 4 124 31 CConnectionRepository::shutdown 0 1 43 86
/**
 * May be called at application shutdown to ensure all threads are cleaned up.
 */
43
void CConnectionRepository::shutdown(void);

106 24 set_simulated_disconnect 0 4 124 47 CConnectionRepository::set_simulated_disconnect 0 1 44 185
/**
 * Sets the simulated disconnect flag.  While this is true, no datagrams will
 * be retrieved from or sent to the server.  The idea is to simulate a
 * temporary network outage.
 */
87
inline void CConnectionRepository::set_simulated_disconnect(bool simulated_disconnect);

107 24 get_simulated_disconnect 0 4 124 47 CConnectionRepository::get_simulated_disconnect 0 1 45 188
/**
 * Returns the simulated disconnect flag.  While this is true, no datagrams
 * will be retrieved from or sent to the server.  The idea is to simulate a
 * temporary network outage.
 */
72
inline bool CConnectionRepository::get_simulated_disconnect(void) const;

108 14 toggle_verbose 0 4 124 37 CConnectionRepository::toggle_verbose 0 1 46 137
/**
 * Toggles the current setting of the verbose flag.  When true, this describes
 * every message going back and forth on the wire.
 */
56
inline void CConnectionRepository::toggle_verbose(void);

109 11 set_verbose 0 4 124 34 CConnectionRepository::set_verbose 0 1 47 120
/**
 * Directly sets the verbose flag.  When true, this describes every message
 * going back and forth on the wire.
 */
61
inline void CConnectionRepository::set_verbose(bool verbose);

110 11 get_verbose 0 4 124 34 CConnectionRepository::get_verbose 0 1 48 137
/**
 * Returns the current setting of the verbose flag.  When true, this describes
 * every message going back and forth on the wire.
 */
59
inline bool CConnectionRepository::get_verbose(void) const;

111 16 set_time_warning 0 4 124 39 CConnectionRepository::set_time_warning 0 1 49 170
/**
 * Directly sets the time_warning field.  When non zero, this describes every
 * message going back and forth on the wire when the msg handling time is over
 * it
 */
72
inline void CConnectionRepository::set_time_warning(float time_warning);

112 16 get_time_warning 0 4 124 39 CConnectionRepository::get_time_warning 0 1 50 65
/**
 * Returns the current setting of the time_warning field.
 */
65
inline float CConnectionRepository::get_time_warning(void) const;

113 26 CDistributedSmoothNodeBase 0 260 125 54 CDistributedSmoothNodeBase::CDistributedSmoothNodeBase 0 2 51 52 10
/**
 *
 */
170
CDistributedSmoothNodeBase::CDistributedSmoothNodeBase(void);
inline CDistributedSmoothNodeBase::CDistributedSmoothNodeBase(CDistributedSmoothNodeBase const &) = default;

114 27 ~CDistributedSmoothNodeBase 0 516 125 55 CDistributedSmoothNodeBase::~CDistributedSmoothNodeBase 0 0 10
/**
 *
 */
62
CDistributedSmoothNodeBase::~CDistributedSmoothNodeBase(void);

115 14 set_repository 0 4 125 42 CDistributedSmoothNodeBase::set_repository 0 1 53 110
/**
 * Tells the C++ instance definition about the AI or Client repository, used
 * for sending datagrams.
 */
122
inline void CDistributedSmoothNodeBase::set_repository(CConnectionRepository *repository, bool is_ai, CHANNEL_TYPE ai_id);

116 15 set_clock_delta 0 4 125 43 CDistributedSmoothNodeBase::set_clock_delta 0 1 54 80
/**
 * Tells the C++ instance definition about the global ClockDelta object.
 */
79
inline void CDistributedSmoothNodeBase::set_clock_delta(PyObject *clock_delta);

117 10 initialize 0 4 125 38 CDistributedSmoothNodeBase::initialize 0 1 55 248
/**
 * Initializes the internal structures from some constructs that are normally
 * stored only in Python.  Also reads the current node's pos & hpr values in
 * preparation for transmitting them via one of the broadcast_pos_hpr_*()
 * methods.
 */
108
void CDistributedSmoothNodeBase::initialize(NodePath const &node_path, DCClass *dclass, CHANNEL_TYPE do_id);

118 15 send_everything 0 4 125 43 CDistributedSmoothNodeBase::send_everything 0 1 56 63
/**
 * Broadcasts the current pos/hpr in its complete form.
 */
55
void CDistributedSmoothNodeBase::send_everything(void);

119 22 broadcast_pos_hpr_full 0 4 125 50 CDistributedSmoothNodeBase::broadcast_pos_hpr_full 0 1 57 143
/**
 * Examines the complete pos/hpr information to see which of the six elements
 * have changed, and broadcasts the appropriate messages.
 */
62
void CDistributedSmoothNodeBase::broadcast_pos_hpr_full(void);

120 21 broadcast_pos_hpr_xyh 0 4 125 49 CDistributedSmoothNodeBase::broadcast_pos_hpr_xyh 0 1 58 108
/**
 * Examines only X, Y, and H of the pos/hpr information, and broadcasts the
 * appropriate messages.
 */
61
void CDistributedSmoothNodeBase::broadcast_pos_hpr_xyh(void);

121 20 broadcast_pos_hpr_xy 0 4 125 48 CDistributedSmoothNodeBase::broadcast_pos_hpr_xy 0 1 59 104
/**
 * Examines only X and Y of the pos/hpr information, and broadcasts the
 * appropriate messages.
 */
60
void CDistributedSmoothNodeBase::broadcast_pos_hpr_xy(void);

122 10 set_curr_l 0 4 125 38 CDistributedSmoothNodeBase::set_curr_l 0 1 60 54
/**
 * Appends the timestamp and sends the update.
 */
56
void CDistributedSmoothNodeBase::set_curr_l(uint64_t l);

123 12 print_curr_l 0 4 125 40 CDistributedSmoothNodeBase::print_curr_l 0 1 61 0
52
void CDistributedSmoothNodeBase::print_curr_l(void);

61
1 0 0 7 2 127 63 0 10 /**
 *
 */ 2 14 has_owner_view 5 126  12 threaded_net 5 126  
2 0 0 6 4 128 0 0 69 /**
 * Returns the DCFile object associated with this repository.
 */ 1 4 this 3 127  
3 0 0 6 5 126 0 0 92 /**
 * Returns true if this repository can have 'owner' views of distributed
 * objects.
 */ 1 4 this 3 130  
4 0 0 4 6 132 0 0 153 /**
 * Set true to specify this repository should process distributed updates
 * internally in C++ code, or false if it should return them to Python.
 */ 2 4 this 3 127  16 handle_c_updates 1 126  
5 0 0 6 7 126 0 0 145 /**
 * Returns true if this repository will process distributed updates internally
 * in C++ code, or false if it will return them to Python.
 */ 1 4 this 3 130  
6 0 0 4 8 132 0 0 281 /**
 * Sets the client_datagram flag.  If this is true, incoming datagrams are not
 * expected to be prefixed with the server routing information like message
 * sender, channel number, etc.; otherwise, these server fields are parsed and
 * removed from each incoming datagram.
 */ 2 4 this 3 127  15 client_datagram 1 126  
7 0 0 6 9 126 0 0 44 /**
 * Returns the client_datagram flag.
 */ 1 4 this 3 130  
8 0 0 4 10 132 0 0 321 /**
 * Sets the handle_datagrams_internally flag.  When true, certain message
 * types can be handled by the C++ code in in this module.  When false, all
 * datagrams, regardless of message type, are passed up to Python for
 * processing.
 *
 * The CMU distributed-object implementation requires this to be set false.
 */ 2 4 this 3 127  27 handle_datagrams_internally 1 126  
9 0 0 6 11 126 0 0 56 /**
 * Returns the handle_datagrams_internally flag.
 */ 1 4 this 3 130  
10 0 0 4 12 132 0 0 270 /**
 * Sets the header size of TCP packets.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */ 2 4 this 3 127  15 tcp_header_size 1 133  
11 0 0 6 13 133 0 0 86 /**
 * Returns the current setting of TCP header size.  See set_tcp_header_size().
 */ 1 4 this 3 130  
12 0 0 4 14 132 0 0 200 /**
 * Records the pointer to the Python class that derives from
 * CConnectionRepository.  This allows the C++ implementation to directly
 * manipulation some python structures on the repository.
 */ 2 4 this 3 127  17 python_repository 1 134  
13 0 0 4 15 132 0 0 200 /**
 * Once a connection has been established via the HTTP interface, gets the
 * connection and uses it.  The supplied HTTPChannel object must have a
 * connection available via get_connection().
 */ 2 4 this 3 127  7 channel 1 137  
14 0 0 6 16 139 0 0 160 /**
 * Returns the SocketStream that internally represents the already-established
 * HTTP connection.  Returns NULL if there is no current HTTP connection.
 */ 1 4 this 3 127  
15 0 0 6 17 126 0 0 155 /**
 * Uses Panda's "net" library to try to connect to the server and port named
 * in the indicated URL.  Returns true if successful, false otherwise.
 */ 2 4 this 3 127  3 url 1 141  
16 0 0 6 18 144 0 0 85 /**
 * Returns the QueuedConnectionManager object associated with the repository.
 */ 1 4 this 3 127  
17 0 0 6 19 146 0 0 78 /**
 * Returns the ConnectionWriter object associated with the repository.
 */ 1 4 this 3 127  
18 0 0 6 20 148 0 0 84 /**
 * Returns the QueuedConnectionReader object associated with the repository.
 */ 1 4 this 3 127  
19 0 0 6 21 126 0 0 91 /**
 * Connects to the server using Panda's low-level and fast "native net"
 * library.
 */ 2 4 this 3 127  3 url 1 141  
20 0 0 6 22 150 0 0 92 /**
 * Returns the Buffered_DatagramConnection object associated with the
 * repository.
 */ 1 4 this 3 127  
21 0 0 6 23 126 0 0 226 /**
 * Returns true if a new datagram is available, false otherwise.  If the
 * return value is true, the new datagram may be retrieved via get_datagram(),
 * or preferably, with get_datagram_iterator() and get_msg_type().
 */ 1 4 this 3 127  
22 0 0 4 24 132 0 0 102 /**
 * Fills the datagram object with the datagram most recently retrieved by
 * check_datagram().
 */ 2 4 this 3 127  2 dg 1 152  
23 0 0 4 25 132 0 0 256 /**
 * Fills the DatagramIterator object with the iterator for the datagram most
 * recently retrieved by check_datagram().  This iterator has already read
 * past the datagram header and the message type, and is positioned at the
 * beginning of data.
 */ 2 4 this 3 127  2 di 1 154  
24 0 0 6 26 156 0 0 160 /**
 * Returns the channel(s) to which the current message was sent, according to
 * the datagram headers.  This information is not available to the client.
 */ 2 4 this 3 130  6 offset 5 133  
25 0 0 6 27 133 0 0 0 1 4 this 3 130  
26 0 0 6 28 156 0 0 144 /**
 * Returns the sender ID of the current message, according to the datagram
 * headers.  This information is not available to the client.
 */ 1 4 this 3 130  
27 0 0 6 29 159 0 0 92 /**
 * Returns the type ID of the current message, according to the datagram
 * headers.
 */ 1 4 this 3 130  
28 0 0 6 30 160 0 0 94 /**
 * Returns event string that will be thrown if the datagram reader queue
 * overflows.
 */ 0 
29 0 0 6 31 126 0 0 303 /**
 * Returns true if the connection to the gameserver is established and still
 * good, false if we are not connected.  A false value means either (a) we
 * never successfully connected, (b) we explicitly called disconnect(), or (c)
 * we were connected, but the connection was spontaneously lost.
 */ 1 4 this 3 127  
30 0 0 6 32 126 0 0 177 /**
 * Queues the indicated datagram for sending to the server.  It may not get
 * sent immediately if collect_tcp is in effect; call flush() to guarantee it
 * is sent now.
 */ 2 4 this 3 127  2 dg 1 161  
31 0 0 4 33 132 0 0 51 /**
 * Enable/disable outbound message bundling
 */ 2 4 this 3 127  4 flag 1 126  
32 0 0 6 34 126 0 0 51 /**
 * Returns true if message bundling enabled
 */ 1 4 this 3 130  
33 0 0 4 35 132 0 0 43 /**
 * Enables/disables quiet zone mode
 */ 2 4 this 3 127  4 flag 1 126  
34 0 0 6 36 126 0 0 59 /**
 * Returns true if repository is in quiet zone mode
 */ 1 4 this 3 130  
35 0 0 4 37 132 0 0 300 /**
 * Send a set of messages to the state server that will be processed
 * atomically.  For instance, you can do a combined setLocation/setPos and
 * prevent race conditions where clients briefly get the setLocation but not
 * the setPos, because the state server hasn't processed the setPos yet
 */ 1 4 this 3 127  
36 0 0 6 38 126 0 0 92 /**
 * Returns true if repository is queueing outgoing messages into a message
 * bundle
 */ 1 4 this 3 130  
37 0 0 4 39 132 0 0 79 /**
 * Send network messages queued up since startMessageBundle was called.
 */ 3 4 this 3 127  7 channel 1 159  14 sender_channel 1 159  
38 0 0 4 40 132 0 0 74 /**
 * throw out any msgs that have been queued up for message bundles
 */ 1 4 this 3 127  
39 0 0 4 41 132 0 0 10 /**
 *
 */ 2 4 this 3 127  2 dg 1 161  
40 0 0 6 42 126 0 0 143 /**
 * Sends the most recently queued data if enough time has elapsed.  This only
 * has meaning if set_collect_tcp() has been set to true.
 */ 1 4 this 3 127  
41 0 0 6 43 126 0 0 120 /**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */ 1 4 this 3 127  
42 0 0 4 44 132 0 0 47 /**
 * Closes the connection to the server.
 */ 1 4 this 3 127  
43 0 0 4 45 132 0 0 86 /**
 * May be called at application shutdown to ensure all threads are cleaned up.
 */ 1 4 this 3 127  
44 0 0 4 46 132 0 0 185 /**
 * Sets the simulated disconnect flag.  While this is true, no datagrams will
 * be retrieved from or sent to the server.  The idea is to simulate a
 * temporary network outage.
 */ 2 4 this 3 127  20 simulated_disconnect 1 126  
45 0 0 6 47 126 0 0 188 /**
 * Returns the simulated disconnect flag.  While this is true, no datagrams
 * will be retrieved from or sent to the server.  The idea is to simulate a
 * temporary network outage.
 */ 1 4 this 3 130  
46 0 0 4 48 132 0 0 137 /**
 * Toggles the current setting of the verbose flag.  When true, this describes
 * every message going back and forth on the wire.
 */ 1 4 this 3 127  
47 0 0 4 49 132 0 0 120 /**
 * Directly sets the verbose flag.  When true, this describes every message
 * going back and forth on the wire.
 */ 2 4 this 3 127  7 verbose 1 126  
48 0 0 6 50 126 0 0 137 /**
 * Returns the current setting of the verbose flag.  When true, this describes
 * every message going back and forth on the wire.
 */ 1 4 this 3 130  
49 0 0 4 51 132 0 0 170 /**
 * Directly sets the time_warning field.  When non zero, this describes every
 * message going back and forth on the wire when the msg handling time is over
 * it
 */ 2 4 this 3 127  12 time_warning 1 163  
50 0 0 6 52 163 0 0 65 /**
 * Returns the current setting of the time_warning field.
 */ 1 4 this 3 130  
51 0 0 7 54 164 114 0 10 /**
 *
 */ 0 
52 0 0 15 54 164 114 0 0 1 6 param0 0 165  
53 0 0 4 56 132 0 0 110 /**
 * Tells the C++ instance definition about the AI or Client repository, used
 * for sending datagrams.
 */ 4 4 this 3 164  10 repository 1 127  5 is_ai 1 126  5 ai_id 1 156  
54 0 0 4 57 132 0 0 80 /**
 * Tells the C++ instance definition about the global ClockDelta object.
 */ 2 4 this 3 164  11 clock_delta 1 134  
55 0 0 4 58 132 0 0 248 /**
 * Initializes the internal structures from some constructs that are normally
 * stored only in Python.  Also reads the current node's pos & hpr values in
 * preparation for transmitting them via one of the broadcast_pos_hpr_*()
 * methods.
 */ 4 4 this 3 164  9 node_path 1 167  6 dclass 1 170  5 do_id 1 156  
56 0 0 4 59 132 0 0 63 /**
 * Broadcasts the current pos/hpr in its complete form.
 */ 1 4 this 3 164  
57 0 0 4 60 132 0 0 143 /**
 * Examines the complete pos/hpr information to see which of the six elements
 * have changed, and broadcasts the appropriate messages.
 */ 1 4 this 3 164  
58 0 0 4 61 132 0 0 108 /**
 * Examines only X, Y, and H of the pos/hpr information, and broadcasts the
 * appropriate messages.
 */ 1 4 this 3 164  
59 0 0 4 62 132 0 0 104 /**
 * Examines only X and Y of the pos/hpr information, and broadcasts the
 * appropriate messages.
 */ 1 4 this 3 164  
60 0 0 4 63 132 0 0 54 /**
 * Appends the timestamp and sends the update.
 */ 2 4 this 3 164  1 l 1 157  
61 0 0 4 64 132 0 0 0 1 4 this 3 164  
48
124 21 CConnectionRepository 0 26625 21 CConnectionRepository 21 CConnectionRepository 0 0 0 1 62 63 0 49 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110 111 112 0 0 0 0 0 568
/**
 * This class implements the C++ side of the ConnectionRepository object.  In
 * particular, it manages the connection to the server once it has been opened
 * (but does not open it directly).  It manages reading and writing datagrams
 * on the connection and monitoring for unexpected disconnects as well as
 * handling intentional disconnects.
 *
 * Certain server messages, like field updates, are handled entirely within
 * the C++ layer, while server messages that are not understood by the C++
 * layer are returned up to the Python layer for processing.
 */

125 26 CDistributedSmoothNodeBase 0 26625 26 CDistributedSmoothNodeBase 26 CDistributedSmoothNodeBase 0 0 0 1 113 114 0 9 115 116 117 118 119 120 121 122 123 0 0 0 0 0 142
/**
 * This class defines some basic methods of DistributedSmoothNodeBase which
 * have been moved into C++ as a performance optimization.
 */

126 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

127 23 CConnectionRepository * 0 8576 23 CConnectionRepository * 23 CConnectionRepository * 0 0 124 0 0 0 0 0 0 0 0 0 0

128 8 DCFile * 0 8576 8 DCFile * 8 DCFile * 0 0 129 0 0 0 0 0 0 0 0 0 0

129 6 DCFile 0 2048 6 DCFile 6 DCFile 0 0 0 0 0 0 0 0 0 0 0 0 101
/**
 * Represents the complete list of Distributed Class descriptions as read from
 * a .dc file.
 */

130 29 CConnectionRepository const * 0 8576 29 CConnectionRepository const * 29 CConnectionRepository const * 0 0 131 0 0 0 0 0 0 0 0 0 0

131 27 CConnectionRepository const 0 8832 27 CConnectionRepository const 27 CConnectionRepository const 0 0 124 0 0 0 0 0 0 0 0 0 0

132 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

133 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

134 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 135 0 0 0 0 0 0 0 0 0 0

135 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 136 0 0 0 0 0 0 0 0 0 0

136 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

137 13 HTTPChannel * 0 8576 13 HTTPChannel * 13 HTTPChannel * 0 0 138 0 0 0 0 0 0 0 0 0 0

138 11 HTTPChannel 0 2048 11 HTTPChannel 11 HTTPChannel 0 0 0 0 0 0 0 0 0 0 0 0 0

139 14 SocketStream * 0 8576 14 SocketStream * 14 SocketStream * 0 0 140 0 0 0 0 0 0 0 0 0 0

140 12 SocketStream 0 2048 12 SocketStream 12 SocketStream 0 0 0 0 0 0 0 0 0 0 0 0 97
/**
 * A base class for iostreams that read and write to a (possibly non-blocking)
 * socket.
 */

141 15 URLSpec const * 0 8576 15 URLSpec const * 15 URLSpec const * 0 0 142 0 0 0 0 0 0 0 0 0 0

142 13 URLSpec const 0 8832 13 URLSpec const 13 URLSpec const 0 0 143 0 0 0 0 0 0 0 0 0 0

143 7 URLSpec 0 2048 7 URLSpec 7 URLSpec 0 0 0 0 0 0 0 0 0 0 0 0 0

144 25 QueuedConnectionManager * 0 8576 25 QueuedConnectionManager * 25 QueuedConnectionManager * 0 0 145 0 0 0 0 0 0 0 0 0 0

145 23 QueuedConnectionManager 0 2048 23 QueuedConnectionManager 23 QueuedConnectionManager 0 0 0 0 0 0 0 0 0 0 0 0 423
/**
 * This flavor of ConnectionManager will queue up all of the reset-connection
 * messages from the ConnectionReaders and ConnectionWriters and report them
 * to the client on demand.
 *
 * When a reset connection has been discovered via
 * reset_connection_available()/get_reset_connection(), it is still the
 * responsibility of the client to call close_connection() on that connection
 * to free up its resources.
 */

146 18 ConnectionWriter * 0 8576 18 ConnectionWriter * 18 ConnectionWriter * 0 0 147 0 0 0 0 0 0 0 0 0 0

147 16 ConnectionWriter 0 2048 16 ConnectionWriter 16 ConnectionWriter 0 0 0 0 0 0 0 0 0 0 0 0 294
/**
 * This class handles threaded delivery of datagrams to various TCP or UDP
 * sockets.
 *
 * A ConnectionWriter may define an arbitrary number of threads (0 or more) to
 * write its datagrams to sockets.  The number of threads is specified at
 * construction time and cannot be changed.
 */

148 24 QueuedConnectionReader * 0 8576 24 QueuedConnectionReader * 24 QueuedConnectionReader * 0 0 149 0 0 0 0 0 0 0 0 0 0

149 22 QueuedConnectionReader 0 2048 22 QueuedConnectionReader 22 QueuedConnectionReader 0 0 0 0 0 0 0 0 0 0 0 0 291
/**
 * This flavor of ConnectionReader will read from its sockets and queue up all
 * of the datagrams read for later receipt by the client code.  This class is
 * useful for client code that doesn't want to deal with threading and is
 * willing to poll for datagrams at its convenience.
 */

150 29 Buffered_DatagramConnection * 0 8576 29 Buffered_DatagramConnection * 29 Buffered_DatagramConnection * 0 0 151 0 0 0 0 0 0 0 0 0 0

151 27 Buffered_DatagramConnection 0 2048 27 Buffered_DatagramConnection 27 Buffered_DatagramConnection 0 0 0 0 0 0 0 0 0 0 0 0 176
// there are 3 states 1. Socket not even assigned,,,, 2. Socket Assigned and
// trying to get a active connect open 3. Socket is open and  writable.. (
// Fully powered up )...

152 10 Datagram * 0 8576 10 Datagram * 10 Datagram * 0 0 153 0 0 0 0 0 0 0 0 0 0

153 8 Datagram 0 2048 8 Datagram 8 Datagram 0 0 0 0 0 0 0 0 0 0 0 0 565
/**
 * An ordered list of data elements, formatted in memory for transmission over
 * a socket or writing to a data file.
 *
 * Data elements should be added one at a time, in order, to the Datagram.
 * The nature and contents of the data elements are totally up to the user.
 * When a Datagram has been transmitted and received, its data elements may be
 * extracted using a DatagramIterator; it is up to the caller to know the
 * correct type of each data element in order.
 *
 * A Datagram is itself headerless; it is simply a collection of data
 * elements.
 */

154 18 DatagramIterator * 0 8576 18 DatagramIterator * 18 DatagramIterator * 0 0 155 0 0 0 0 0 0 0 0 0 0

155 16 DatagramIterator 0 2048 16 DatagramIterator 16 DatagramIterator 0 0 0 0 0 0 0 0 0 0 0 0 365
/**
 * A class to retrieve the individual data elements previously stored in a
 * Datagram.  Elements may be retrieved one at a time; it is up to the caller
 * to know the correct type and order of each element.
 *
 * Note that it is the responsibility of the caller to ensure that the datagram
 * object is not destructed while this DatagramIterator is in use.
 */

156 12 CHANNEL_TYPE 0 2105344 12 CHANNEL_TYPE 12 CHANNEL_TYPE 0 0 157 0 0 0 0 0 0 0 0 0 0

157 8 uint64_t 0 2105344 8 uint64_t 8 uint64_t 0 0 158 0 0 0 0 0 0 0 0 0 0

158 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

159 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

160 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

161 16 Datagram const * 0 8576 16 Datagram const * 16 Datagram const * 0 0 162 0 0 0 0 0 0 0 0 0 0

162 14 Datagram const 0 8832 14 Datagram const 14 Datagram const 0 0 153 0 0 0 0 0 0 0 0 0 0

163 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

164 28 CDistributedSmoothNodeBase * 0 8576 28 CDistributedSmoothNodeBase * 28 CDistributedSmoothNodeBase * 0 0 125 0 0 0 0 0 0 0 0 0 0

165 34 CDistributedSmoothNodeBase const * 0 8576 34 CDistributedSmoothNodeBase const * 34 CDistributedSmoothNodeBase const * 0 0 166 0 0 0 0 0 0 0 0 0 0

166 32 CDistributedSmoothNodeBase const 0 8832 32 CDistributedSmoothNodeBase const 32 CDistributedSmoothNodeBase const 0 0 125 0 0 0 0 0 0 0 0 0 0

167 16 NodePath const * 0 8576 16 NodePath const * 16 NodePath const * 0 0 168 0 0 0 0 0 0 0 0 0 0

168 14 NodePath const 0 8832 14 NodePath const 14 NodePath const 0 0 169 0 0 0 0 0 0 0 0 0 0

169 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

170 9 DCClass * 0 8576 9 DCClass * 9 DCClass * 0 0 171 0 0 0 0 0 0 0 0 0 0

171 7 DCClass 0 2048 7 DCClass 7 DCClass 0 0 0 0 0 0 0 0 0 0 0 0 80
/**
 * Defines a particular DistributedClass as read from an input .dc file.
 */

0
0
0
