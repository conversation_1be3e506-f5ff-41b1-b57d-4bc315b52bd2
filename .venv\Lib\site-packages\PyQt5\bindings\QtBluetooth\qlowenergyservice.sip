// qlowenergyservice.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QLowEnergyService : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qlowenergyservice.h>
%End

public:
    enum ServiceType
    {
        PrimaryService,
        IncludedService,
    };

    typedef QFlags<QLowEnergyService::ServiceType> ServiceTypes;

    enum ServiceError
    {
        NoError,
        OperationError,
        CharacteristicWriteError,
        DescriptorWriteError,
%If (Qt_5_5_0 -)
        CharacteristicReadError,
%End
%If (Qt_5_5_0 -)
        DescriptorReadError,
%End
%If (Qt_5_5_0 -)
        UnknownError,
%End
    };

    enum ServiceState
    {
        InvalidService,
        DiscoveryRequired,
        DiscoveringServices,
        ServiceDiscovered,
%If (Qt_5_7_0 -)
        LocalService,
%End
    };

    enum WriteMode
    {
        WriteWithResponse,
        WriteWithoutResponse,
%If (Qt_5_7_0 -)
        WriteSigned,
%End
    };

    virtual ~QLowEnergyService();
    QList<QBluetoothUuid> includedServices() const;
    QLowEnergyService::ServiceTypes type() const;
    QLowEnergyService::ServiceState state() const;
    QLowEnergyCharacteristic characteristic(const QBluetoothUuid &uuid) const;
    QList<QLowEnergyCharacteristic> characteristics() const;
    QBluetoothUuid serviceUuid() const;
    QString serviceName() const;
    void discoverDetails();
    QLowEnergyService::ServiceError error() const;
    bool contains(const QLowEnergyCharacteristic &characteristic) const;
    bool contains(const QLowEnergyDescriptor &descriptor) const;
    void writeCharacteristic(const QLowEnergyCharacteristic &characteristic, const QByteArray &newValue, QLowEnergyService::WriteMode mode = QLowEnergyService::WriteWithResponse);
    void writeDescriptor(const QLowEnergyDescriptor &descriptor, const QByteArray &newValue);

signals:
    void stateChanged(QLowEnergyService::ServiceState newState);
    void characteristicChanged(const QLowEnergyCharacteristic &info, const QByteArray &value);
    void characteristicWritten(const QLowEnergyCharacteristic &info, const QByteArray &value);
    void descriptorWritten(const QLowEnergyDescriptor &info, const QByteArray &value);
    void error(QLowEnergyService::ServiceError error);

public:
%If (Qt_5_5_0 -)
    void readCharacteristic(const QLowEnergyCharacteristic &characteristic);
%End
%If (Qt_5_5_0 -)
    void readDescriptor(const QLowEnergyDescriptor &descriptor);
%End

signals:
%If (Qt_5_5_0 -)
    void characteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value);
%End
%If (Qt_5_5_0 -)
    void descriptorRead(const QLowEnergyDescriptor &info, const QByteArray &value);
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QLowEnergyService::ServiceType> operator|(QLowEnergyService::ServiceType f1, QFlags<QLowEnergyService::ServiceType> f2);
%End
