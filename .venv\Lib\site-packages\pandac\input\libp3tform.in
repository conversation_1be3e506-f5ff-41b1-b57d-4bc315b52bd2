1730992849
3 3
10 libp3tform 4 v_mB 12 panda3d.core 
258
266 13 ButtonThrower 0 260 524 28 ButtonThrower::ButtonThrower 0 2 1 2 10
/**
 *
 */
133
explicit ButtonThrower::ButtonThrower(std::string const &name);
inline ButtonThrower::ButtonThrower(ButtonThrower const &) = default;

267 21 set_button_down_event 0 4 524 36 ButtonThrower::set_button_down_event 0 1 3 510
/**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is depressed.  Unlike the specific events that are unique to each
 * key, this same event name is used for *all* button events, and the name of
 * the button pressed (possibly with modifier prefixes) will be sent as a
 * parameter.
 *
 * If this string is empty, no event is generated.  It is possible to generate
 * both generic events and specific events for the same button.
 *
 * See also set_keystroke_event().
 */
87
inline void ButtonThrower::set_button_down_event(std::string const &button_down_event);

268 21 get_button_down_event 0 4 524 36 ButtonThrower::get_button_down_event 0 1 4 114
/**
 * Returns the button_down_event that has been set on this ButtonThrower.  See
 * set_button_down_event().
 */
75
inline std::string const &ButtonThrower::get_button_down_event(void) const;

269 19 set_button_up_event 0 4 524 34 ButtonThrower::set_button_up_event 0 1 5 137
/**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is released.  See set_button_down_event().
 */
83
inline void ButtonThrower::set_button_up_event(std::string const &button_up_event);

270 19 get_button_up_event 0 4 524 34 ButtonThrower::get_button_up_event 0 1 6 110
/**
 * Returns the button_up_event that has been set on this ButtonThrower.  See
 * set_button_up_event().
 */
73
inline std::string const &ButtonThrower::get_button_up_event(void) const;

271 23 set_button_repeat_event 0 4 524 38 ButtonThrower::set_button_repeat_event 0 1 7 517
/**
 * Specifies the generic event that is generated (if any) repeatedly while a
 * key or button is held down.  Unlike the specific events that are unique to
 * each key, this same event name is used for *all* button events, and the
 * name of the button pressed (possibly with modifier prefixes) will be sent
 * as a parameter.
 *
 * If this string is empty, no event is generated.  It is possible to generate
 * both generic events and specific events for the same button.
 *
 * See also set_keystroke_event().
 */
91
inline void ButtonThrower::set_button_repeat_event(std::string const &button_repeat_event);

272 23 get_button_repeat_event 0 4 524 38 ButtonThrower::get_button_repeat_event 0 1 8 117
/**
 * Returns the button_repeat_event that has been set on this ButtonThrower.
 * See set_button_repeat_event().
 */
77
inline std::string const &ButtonThrower::get_button_repeat_event(void) const;

273 19 set_keystroke_event 0 4 524 34 ButtonThrower::set_keystroke_event 0 1 9 846
/**
 * Specifies the event that is generated (if any) for each keystroke that is
 * received.  A keystroke is different than a button event: it represents the
 * semantic meaning of the sequence of keys that have been pressed.  For
 * instance, pressing shift and 4 together will generate the button event
 * "shift-4", but it will generate the keystroke "$".
 *
 * If a key is held down, keyrepeat will cause the same keystroke event to be
 * generated repeatedly.  This is different from the corresponding down event,
 * which will only be generated once, followed by a number of button repeat
 * events.
 *
 * This event is generated with a single wstring parameter, which is a one-
 * character string that contains the keystroke generated.  If this event
 * string is empty, no event is generated.
 *
 * See also set_button_down_event().
 */
83
inline void ButtonThrower::set_keystroke_event(std::string const &keystroke_event);

274 19 get_keystroke_event 0 4 524 34 ButtonThrower::get_keystroke_event 0 1 10 110
/**
 * Returns the keystroke_event that has been set on this ButtonThrower.  See
 * set_keystroke_event().
 */
73
inline std::string const &ButtonThrower::get_keystroke_event(void) const;

275 19 set_candidate_event 0 4 524 34 ButtonThrower::set_candidate_event 0 1 11 804
/**
 * Specifies the event that is generated (if any) for each IME candidate
 * string event received.  Events of this nature are received only when the
 * user is entering data using a Microsoft Input Method Editor, typically used
 * for Asian languages such as Japanese or Korean.
 *
 * If you are designing a typing user interface, you should track this event
 * to support the use of the IME.  In response to this event, you should
 * display the candidate string in the entry box, with the appropriate
 * sections highlighted, so the user can scroll through the available choices.
 *
 * This event is generated with four parameters, in order: the candidate
 * string, the character at which to start the highlight, the character at
 * which to end the highlight, and the current cursor position.
 */
83
inline void ButtonThrower::set_candidate_event(std::string const &candidate_event);

276 19 get_candidate_event 0 4 524 34 ButtonThrower::get_candidate_event 0 1 12 110
/**
 * Returns the candidate_event that has been set on this ButtonThrower.  See
 * set_candidate_event().
 */
73
inline std::string const &ButtonThrower::get_candidate_event(void) const;

277 14 set_move_event 0 4 524 29 ButtonThrower::set_move_event 0 1 13 108
/**
 * Specifies the event that is generated (if any) each time the mouse is moved
 * within the window.
 */
73
inline void ButtonThrower::set_move_event(std::string const &move_event);

278 14 get_move_event 0 4 524 29 ButtonThrower::get_move_event 0 1 14 100
/**
 * Returns the move_event that has been set on this ButtonThrower.  See
 * set_move_event().
 */
68
inline std::string const &ButtonThrower::get_move_event(void) const;

279 25 set_raw_button_down_event 0 4 524 40 ButtonThrower::set_raw_button_down_event 0 1 15 192
/**
 * Like set_button_down_event, but uses the raw, untransformed scan key from
 * the operating system.  This uses buttons that are independent of the user's
 * selected keyboard layout.
 */
95
inline void ButtonThrower::set_raw_button_down_event(std::string const &raw_button_down_event);

280 25 get_raw_button_down_event 0 4 524 40 ButtonThrower::get_raw_button_down_event 0 1 16 121
/**
 * Returns the raw_button_down_event that has been set on this ButtonThrower.
 * See set_raw_button_down_event().
 */
79
inline std::string const &ButtonThrower::get_raw_button_down_event(void) const;

281 23 set_raw_button_up_event 0 4 524 38 ButtonThrower::set_raw_button_up_event 0 1 17 141
/**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is released.  See set_raw_button_down_event().
 */
91
inline void ButtonThrower::set_raw_button_up_event(std::string const &raw_button_up_event);

282 23 get_raw_button_up_event 0 4 524 38 ButtonThrower::get_raw_button_up_event 0 1 18 117
/**
 * Returns the raw_button_up_event that has been set on this ButtonThrower.
 * See set_raw_button_up_event().
 */
77
inline std::string const &ButtonThrower::get_raw_button_up_event(void) const;

283 10 set_prefix 0 4 524 25 ButtonThrower::set_prefix 0 1 19 229
/**
 * Sets the prefix which is prepended to all specific event names (that is,
 * event names generated from the button name itself, as opposed to the
 * generic event names like set_button_down_event) thrown by this object.
 */
65
inline void ButtonThrower::set_prefix(std::string const &prefix);

284 10 get_prefix 0 4 524 25 ButtonThrower::get_prefix 0 1 20 92
/**
 * Returns the prefix that has been set on this ButtonThrower.  See
 * set_prefix().
 */
64
inline std::string const &ButtonThrower::get_prefix(void) const;

285 17 set_specific_flag 0 4 524 32 ButtonThrower::set_specific_flag 0 1 21 307
/**
 * Sets the flag that indicates whether specific events (events prefixed by
 * set_prefix, and based on the event name) should be generated at all.  This
 * is true by default, but may be disabled if you are only interested in the
 * generic events (for instance, events like set_button_down_event).
 */
65
inline void ButtonThrower::set_specific_flag(bool specific_flag);

286 17 get_specific_flag 0 4 524 32 ButtonThrower::get_specific_flag 0 1 22 116
/**
 * Returns the flag that indicates whether specific events should be
 * generated.  See set_specific_flag().
 */
57
inline bool ButtonThrower::get_specific_flag(void) const;

287 13 set_time_flag 0 4 524 28 ButtonThrower::set_time_flag 0 1 23 400
/**
 * Sets the flag that indicates whether the time of the button event should be
 * passed as a parameter or not.  When this is true, an additional parameter
 * is generated on each event (before all the parameters named by
 * add_parameter) that consists of a single double value, and reflects the
 * time the button was pressed or released, as a value from
 * ClockObject::get_global_clock().
 */
57
inline void ButtonThrower::set_time_flag(bool time_flag);

288 13 get_time_flag 0 4 524 28 ButtonThrower::get_time_flag 0 1 24 115
/**
 * Returns the flag that indicates whether the time of the button event should
 * be passed as a parameter.
 */
53
inline bool ButtonThrower::get_time_flag(void) const;

289 13 add_parameter 0 4 524 28 ButtonThrower::add_parameter 0 1 25 137
/**
 * Adds the indicated parameter to the list of parameters that will be passed
 * with each event generated by this ButtonThrower.
 */
61
void ButtonThrower::add_parameter(EventParameter const &obj);

290 18 get_num_parameters 0 4 524 33 ButtonThrower::get_num_parameters 0 1 26 155
/**
 * Returns the number of parameters that have been added to the list of
 * parameters to be passed with each event generated by this ButtonThrower.
 */
50
int ButtonThrower::get_num_parameters(void) const;

291 13 get_parameter 0 4 524 28 ButtonThrower::get_parameter 0 1 27 141
/**
 * Returns the nth parameter that has been added to the list of parameters
 * passed with each event generated by this ButtonThrower.
 */
57
EventParameter ButtonThrower::get_parameter(int n) const;

292 20 get_modifier_buttons 0 4 524 35 ButtonThrower::get_modifier_buttons 0 1 28 463
/**
 * Returns the set of ModifierButtons that the ButtonThrower will consider
 * important enough to prepend the event name with.  Normally, this set will
 * be empty, and the ButtonThrower will therefore ignore all ModifierButtons
 * attached to the key events, but if one or more buttons have been added to
 * this set, and those modifier buttons are set on the button event, then the
 * event name will be prepended with the names of the modifier buttons.
 */
78
inline ModifierButtons const &ButtonThrower::get_modifier_buttons(void) const;

293 20 set_modifier_buttons 0 4 524 35 ButtonThrower::set_modifier_buttons 0 1 29 653
/**
 * Changes the set of ModifierButtons that the ButtonThrower will consider
 * important enough to prepend the event name with.  Normally, this set will
 * be empty, and the ButtonThrower will therefore ignore all ModifierButtons
 * attached to the key events, but if one or more buttons have been added to
 * this set, then the event name will be prepended with the names of the
 * modifier buttons.
 *
 * It is recommended that you change this setting by first calling
 * get_modifier_buttons(), making adjustments, and passing the new value to
 * set_modifier_buttons().  This way the current state of the modifier buttons
 * will not be lost.
 */
77
inline void ButtonThrower::set_modifier_buttons(ModifierButtons const &mods);

294 24 set_throw_buttons_active 0 4 524 39 ButtonThrower::set_throw_buttons_active 0 1 30 293
/**
 * Sets the flag that indicates whether the ButtonThrower will only process
 * events for the explicitly named buttons or not.  Normally this is false,
 * meaning all buttons are processed; set it true to indicate that only some
 * buttons should be processed.  See add_throw_button().
 */
63
inline void ButtonThrower::set_throw_buttons_active(bool flag);

295 24 get_throw_buttons_active 0 4 524 39 ButtonThrower::get_throw_buttons_active 0 1 31 173
/**
 * Returns the flag that indicates whether the ButtonThrower will only process
 * events for the explicitly named buttons or not.  See
 * set_throw_buttons_active().
 */
64
inline bool ButtonThrower::get_throw_buttons_active(void) const;

296 16 add_throw_button 0 4 524 31 ButtonThrower::add_throw_button 0 1 32 663
/**
 * Adds a new button to the set of buttons that the ButtonThrower explicitly
 * processes.
 *
 * If set_throw_buttons_active is false (which is the default), the
 * ButtonThrower will process all buttons.  Otherwise, the ButtonThrower will
 * only process events for the button(s) explicitly named by this function;
 * buttons not on the list will be ignored by this object and passed on
 * downstream to the child node(s) in the data graph.  A button that *is* on
 * the list will be processed by the ButtonThrower and not passed on to the
 * child node(s).
 *
 * The return value is true if the button is added, or false if it was already
 * in the set.
 */
94
bool ButtonThrower::add_throw_button(ModifierButtons const &mods, ButtonHandle const &button);

297 19 remove_throw_button 0 4 524 34 ButtonThrower::remove_throw_button 0 1 33 231
/**
 * Removes the indicated button from the set of buttons that the ButtonThrower
 * explicitly processes.  See add_throw_button().
 *
 * The return value is true if the button is removed, or false if it was not
 * on the set.
 */
97
bool ButtonThrower::remove_throw_button(ModifierButtons const &mods, ButtonHandle const &button);

298 16 has_throw_button 0 4 524 31 ButtonThrower::has_throw_button 0 2 34 35 498
/**
 * Returns true if the indicated button is on the set of buttons that will be
 * processed by the ButtonThrower, false otherwise.  See add_throw_button().
 */

/**
 * Returns true if the indicated button, in conjunction with any nonspecified
 * modifier buttons, is on the set of buttons that will be processed by the
 * ButtonThrower.  That is to say, returns true if this button was ever passed
 * as the second parameter add_throw_button(), regardless of what the first
 * parameter was.
 */
172
bool ButtonThrower::has_throw_button(ModifierButtons const &mods, ButtonHandle const &button) const;
bool ButtonThrower::has_throw_button(ButtonHandle const &button) const;

299 19 clear_throw_buttons 0 4 524 34 ButtonThrower::clear_throw_buttons 0 1 36 105
/**
 * Empties the set of buttons that were added via add_throw_button().  See
 * add_throw_button().
 */
46
void ButtonThrower::clear_throw_buttons(void);

300 14 get_class_type 0 4 524 29 ButtonThrower::get_class_type 0 1 37 0
54
static TypeHandle ButtonThrower::get_class_type(void);

301 14 require_button 0 4 533 34 MouseInterfaceNode::require_button 0 1 39 289
/**
 * Indicates that the indicated button must be in the required state (either
 * up or down) in order for this particular MouseInterfaceNode to do anything.
 * For instance, this may be called to make a Trackball object respect mouse
 * input only when the control key is held down.
 */
82
void MouseInterfaceNode::require_button(ButtonHandle const &button, bool is_down);

302 12 clear_button 0 4 533 32 MouseInterfaceNode::clear_button 0 1 40 105
/**
 * Removes any requirement on the indicated button set by an earlier call to
 * require_button().
 */
66
void MouseInterfaceNode::clear_button(ButtonHandle const &button);

303 17 clear_all_buttons 0 4 533 37 MouseInterfaceNode::clear_all_buttons 0 1 41 93
/**
 * Removes all requirements on buttons set by an earlier call to
 * require_button().
 */
49
void MouseInterfaceNode::clear_all_buttons(void);

304 14 get_class_type 0 4 533 34 MouseInterfaceNode::get_class_type 0 1 42 0
59
static TypeHandle MouseInterfaceNode::get_class_type(void);

305 18 MouseInterfaceNode 0 260 533 38 MouseInterfaceNode::MouseInterfaceNode 0 1 38 10
/**
 *
 */
84
inline MouseInterfaceNode::MouseInterfaceNode(MouseInterfaceNode const &) = default;

306 14 DriveInterface 0 260 534 30 DriveInterface::DriveInterface 0 2 43 44 10
/**
 *
 */
143
explicit DriveInterface::DriveInterface(std::string const &name = "");
inline DriveInterface::DriveInterface(DriveInterface const &) = default;

307 17 set_forward_speed 0 4 534 33 DriveInterface::set_forward_speed 0 1 45 144
/**
 * Sets the speed of full forward motion, when the mouse is at the very top of
 * the window.  This is in units (e.g.  feet) per second.
 */
65
inline void DriveInterface::set_forward_speed(PN_stdfloat speed);

308 17 get_forward_speed 0 4 534 33 DriveInterface::get_forward_speed 0 1 46 147
/**
 * Returns the speed of full forward motion, when the mouse is at the very top
 * of the window.  This is in units (e.g.  feet) per second.
 */
65
inline PN_stdfloat DriveInterface::get_forward_speed(void) const;

309 17 set_reverse_speed 0 4 534 33 DriveInterface::set_reverse_speed 0 1 47 147
/**
 * Sets the speed of full reverse motion, when the mouse is at the very bottom
 * of the window.  This is in units (e.g.  feet) per second.
 */
65
inline void DriveInterface::set_reverse_speed(PN_stdfloat speed);

310 17 get_reverse_speed 0 4 534 33 DriveInterface::get_reverse_speed 0 1 48 150
/**
 * Returns the speed of full reverse motion, when the mouse is at the very
 * bottom of the window.  This is in units (e.g.  feet) per second.
 */
65
inline PN_stdfloat DriveInterface::get_reverse_speed(void) const;

311 16 set_rotate_speed 0 4 534 32 DriveInterface::set_rotate_speed 0 1 49 161
/**
 * Sets the maximum rate at which the user can rotate left or right, when the
 * mouse is at the very edge of the window.  This is in degrees per second.
 */
64
inline void DriveInterface::set_rotate_speed(PN_stdfloat speed);

312 16 get_rotate_speed 0 4 534 32 DriveInterface::get_rotate_speed 0 1 50 167
/**
 * Returns the maximum rate at which the user can rotate left or right, when
 * the mouse is at the very edge of the window.  This is in degrees per
 * second.
 */
64
inline PN_stdfloat DriveInterface::get_rotate_speed(void) const;

313 22 set_vertical_dead_zone 0 4 534 38 DriveInterface::set_vertical_dead_zone 0 1 51 300
/**
 * Sets the size of the horizontal bar in the center of the screen that
 * represents the "dead zone" of vertical motion: the region in which the
 * mouse does not report vertical motion.  This is in a fraction of the window
 * height, so 0.5 will set a dead zone as large as half the screen.
 */
69
inline void DriveInterface::set_vertical_dead_zone(PN_stdfloat zone);

314 22 get_vertical_dead_zone 0 4 534 38 DriveInterface::get_vertical_dead_zone 0 1 52 303
/**
 * Returns the size of the horizontal bar in the center of the screen that
 * represents the "dead zone" of vertical motion: the region in which the
 * mouse does not report vertical motion.  This is in a fraction of the window
 * height, so 0.5 will set a dead zone as large as half the screen.
 */
70
inline PN_stdfloat DriveInterface::get_vertical_dead_zone(void) const;

315 24 set_horizontal_dead_zone 0 4 534 40 DriveInterface::set_horizontal_dead_zone 0 1 53 301
/**
 * Sets the size of the vertical bar in the center of the screen that
 * represents the "dead zone" of horizontal motion: the region in which the
 * mouse does not report horizontal motion.  This is in a fraction of the
 * window width, so 0.5 will set a dead zone as large as half the screen.
 */
71
inline void DriveInterface::set_horizontal_dead_zone(PN_stdfloat zone);

316 24 get_horizontal_dead_zone 0 4 534 40 DriveInterface::get_horizontal_dead_zone 0 1 54 304
/**
 * Returns the size of the vertical bar in the center of the screen that
 * represents the "dead zone" of horizontal motion: the region in which the
 * mouse does not report horizontal motion.  This is in a fraction of the
 * window width, so 0.5 will set a dead zone as large as half the screen.
 */
72
inline PN_stdfloat DriveInterface::get_horizontal_dead_zone(void) const;

317 25 set_vertical_ramp_up_time 0 4 534 41 DriveInterface::set_vertical_ramp_up_time 0 1 55 173
/**
 * Sets the amount of time, in seconds, it takes between the time an up or
 * down arrow key is pressed and the time it registers full forward or
 * backward motion.
 */
80
inline void DriveInterface::set_vertical_ramp_up_time(PN_stdfloat ramp_up_time);

318 25 get_vertical_ramp_up_time 0 4 534 41 DriveInterface::get_vertical_ramp_up_time 0 1 56 176
/**
 * Returns the amount of time, in seconds, it takes between the time an up or
 * down arrow key is pressed and the time it registers full forward or
 * backward motion.
 */
73
inline PN_stdfloat DriveInterface::get_vertical_ramp_up_time(void) const;

319 27 set_vertical_ramp_down_time 0 4 534 43 DriveInterface::set_vertical_ramp_down_time 0 1 57 149
/**
 * Sets the amount of time, in seconds, it takes between the time an up or
 * down arrow key is released and the time it registers no motion.
 */
84
inline void DriveInterface::set_vertical_ramp_down_time(PN_stdfloat ramp_down_time);

320 27 get_vertical_ramp_down_time 0 4 534 43 DriveInterface::get_vertical_ramp_down_time 0 1 58 152
/**
 * Returns the amount of time, in seconds, it takes between the time an up or
 * down arrow key is released and the time it registers no motion.
 */
75
inline PN_stdfloat DriveInterface::get_vertical_ramp_down_time(void) const;

321 27 set_horizontal_ramp_up_time 0 4 534 43 DriveInterface::set_horizontal_ramp_up_time 0 1 59 154
/**
 * Sets the amount of time, in seconds, it takes between the time a left or
 * right arrow key is pressed and the time it registers full rotation.
 */
82
inline void DriveInterface::set_horizontal_ramp_up_time(PN_stdfloat ramp_up_time);

322 27 get_horizontal_ramp_up_time 0 4 534 43 DriveInterface::get_horizontal_ramp_up_time 0 1 60 157
/**
 * Returns the amount of time, in seconds, it takes between the time a left or
 * right arrow key is pressed and the time it registers full rotation.
 */
75
inline PN_stdfloat DriveInterface::get_horizontal_ramp_up_time(void) const;

323 29 set_horizontal_ramp_down_time 0 4 534 45 DriveInterface::set_horizontal_ramp_down_time 0 1 61 151
/**
 * Sets the amount of time, in seconds, it takes between the time a left or
 * right arrow key is released and the time it registers no motion.
 */
86
inline void DriveInterface::set_horizontal_ramp_down_time(PN_stdfloat ramp_down_time);

324 29 get_horizontal_ramp_down_time 0 4 534 45 DriveInterface::get_horizontal_ramp_down_time 0 1 62 154
/**
 * Returns the amount of time, in seconds, it takes between the time a left or
 * right arrow key is released and the time it registers no motion.
 */
77
inline PN_stdfloat DriveInterface::get_horizontal_ramp_down_time(void) const;

325 9 get_speed 0 4 534 25 DriveInterface::get_speed 0 1 63 64
/**
 * Returns the speed of the previous update in units/sec
 */
57
inline PN_stdfloat DriveInterface::get_speed(void) const;

326 13 get_rot_speed 0 4 534 29 DriveInterface::get_rot_speed 0 1 64 68
/**
 * Returns the rot_speed of the previous update in units/sec
 */
61
inline PN_stdfloat DriveInterface::get_rot_speed(void) const;

327 5 reset 0 4 534 21 DriveInterface::reset 0 1 65 108
/**
 * Reinitializes the driver to the origin and resets any knowledge about
 * buttons being held down.
 */
33
void DriveInterface::reset(void);

328 7 get_pos 0 4 534 23 DriveInterface::get_pos 0 1 66 41
/**
 * Returns the driver's position.
 */
58
inline LPoint3 const &DriveInterface::get_pos(void) const;

329 5 get_x 0 4 534 21 DriveInterface::get_x 0 1 67 0
53
inline PN_stdfloat DriveInterface::get_x(void) const;

330 5 get_y 0 4 534 21 DriveInterface::get_y 0 1 68 0
53
inline PN_stdfloat DriveInterface::get_y(void) const;

331 5 get_z 0 4 534 21 DriveInterface::get_z 0 1 69 0
53
inline PN_stdfloat DriveInterface::get_z(void) const;

332 7 set_pos 0 4 534 23 DriveInterface::set_pos 0 2 70 71 47
/**
 * Directly sets the driver's position.
 */
140
inline void DriveInterface::set_pos(LVecBase3 const &vec);
inline void DriveInterface::set_pos(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

333 5 set_x 0 4 534 21 DriveInterface::set_x 0 1 72 0
49
inline void DriveInterface::set_x(PN_stdfloat x);

334 5 set_y 0 4 534 21 DriveInterface::set_y 0 1 73 0
49
inline void DriveInterface::set_y(PN_stdfloat y);

335 5 set_z 0 4 534 21 DriveInterface::set_z 0 1 74 0
49
inline void DriveInterface::set_z(PN_stdfloat z);

336 7 get_hpr 0 4 534 23 DriveInterface::get_hpr 0 1 75 44
/**
 * Returns the driver's orientation.
 */
60
inline LVecBase3 const &DriveInterface::get_hpr(void) const;

337 5 get_h 0 4 534 21 DriveInterface::get_h 0 1 76 0
53
inline PN_stdfloat DriveInterface::get_h(void) const;

338 5 get_p 0 4 534 21 DriveInterface::get_p 0 1 77 0
53
inline PN_stdfloat DriveInterface::get_p(void) const;

339 5 get_r 0 4 534 21 DriveInterface::get_r 0 1 78 0
53
inline PN_stdfloat DriveInterface::get_r(void) const;

340 7 set_hpr 0 4 534 23 DriveInterface::set_hpr 0 2 79 80 50
/**
 * Directly sets the driver's orientation.
 */
140
inline void DriveInterface::set_hpr(LVecBase3 const &hpr);
inline void DriveInterface::set_hpr(PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);

341 5 set_h 0 4 534 21 DriveInterface::set_h 0 1 81 0
49
inline void DriveInterface::set_h(PN_stdfloat h);

342 5 set_p 0 4 534 21 DriveInterface::set_p 0 1 82 0
49
inline void DriveInterface::set_p(PN_stdfloat p);

343 5 set_r 0 4 534 21 DriveInterface::set_r 0 1 83 0
49
inline void DriveInterface::set_r(PN_stdfloat r);

344 14 set_force_roll 0 4 534 30 DriveInterface::set_force_roll 0 1 84 86
/**
 * This function is no longer used and does nothing.  It will be removed soon.
 */
60
void DriveInterface::set_force_roll(PN_stdfloat force_roll);

345 16 set_ignore_mouse 0 4 534 32 DriveInterface::set_ignore_mouse 0 1 85 411
/**
 * Changes the state of the ignore_mouse flag.  If this flag is true, the
 * DriveInterface will ignore mouse down button events (but still recognize
 * mouse up button events); the user will not be able to start the
 * DriveInterface going again if it is stopped, but if the user is currently
 * holding down a mouse button it will not stop immediately until the user
 * eventually releases the button.
 */
64
inline void DriveInterface::set_ignore_mouse(bool ignore_mouse);

346 16 get_ignore_mouse 0 4 534 32 DriveInterface::get_ignore_mouse 0 1 86 92
/**
 * Returns the current setting of the ignore_mouse flag.  See
 * set_ignore_mouse().
 */
57
inline bool DriveInterface::get_ignore_mouse(void) const;

347 15 set_force_mouse 0 4 534 31 DriveInterface::set_force_mouse 0 1 87 155
/**
 * Changes the state of the force_mouse flag.  If this flag is true, the mouse
 * button need not be held down in order to drive the avatar around.
 */
62
inline void DriveInterface::set_force_mouse(bool force_mouse);

348 15 get_force_mouse 0 4 534 31 DriveInterface::get_force_mouse 0 1 88 90
/**
 * Returns the current setting of the force_mouse flag.  See
 * set_force_mouse().
 */
56
inline bool DriveInterface::get_force_mouse(void) const;

349 19 set_stop_this_frame 0 4 534 35 DriveInterface::set_stop_this_frame 0 1 89 335
/**
 * If stop_this_frame is true, the next time the frame is computed no motion
 * will be allowed, and then the flag is reset to false.  This can be used to
 * prevent too much movement when we know a long time has artificially
 * elapsed, for instance when we take a screenshot, without munging the clock
 * for everything else.
 */
70
inline void DriveInterface::set_stop_this_frame(bool stop_this_frame);

350 19 get_stop_this_frame 0 4 534 35 DriveInterface::get_stop_this_frame 0 1 90 98
/**
 * Returns the current setting of the stop_this_frame flag.  See
 * set_stop_this_frame().
 */
60
inline bool DriveInterface::get_stop_this_frame(void) const;

351 7 set_mat 0 4 534 23 DriveInterface::set_mat 0 1 91 64
/**
 * Stores the indicated transform in the DriveInterface.
 */
50
void DriveInterface::set_mat(LMatrix4 const &mat);

352 7 get_mat 0 4 534 23 DriveInterface::get_mat 0 1 92 41
/**
 * Returns the current transform.
 */
46
LMatrix4 const &DriveInterface::get_mat(void);

353 12 force_dgraph 0 4 534 28 DriveInterface::force_dgraph 0 1 93 287
/**
 * This is a special kludge for DriveInterface to allow us to avoid the one-
 * frame latency after a collision.  It forces an immediate partial data flow
 * for all data graph nodes below this node, causing all data nodes that
 * depend on this matrix to be updated immediately.
 */
40
void DriveInterface::force_dgraph(void);

354 14 get_class_type 0 4 534 30 DriveInterface::get_class_type 0 1 94 0
55
static TypeHandle DriveInterface::get_class_type(void);

355 14 MouseSubregion 0 260 535 30 MouseSubregion::MouseSubregion 0 2 95 96 10
/**
 *
 */
138
explicit MouseSubregion::MouseSubregion(std::string const &name);
inline MouseSubregion::MouseSubregion(MouseSubregion const &) = default;

356 8 get_left 0 4 535 24 MouseSubregion::get_left 0 1 97 135
/**
 * Retrieves the x coordinate of the left edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */
56
inline PN_stdfloat MouseSubregion::get_left(void) const;

357 9 get_right 0 4 535 25 MouseSubregion::get_right 0 1 98 136
/**
 * Retrieves the x coordinate of the right edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */
57
inline PN_stdfloat MouseSubregion::get_right(void) const;

358 10 get_bottom 0 4 535 26 MouseSubregion::get_bottom 0 1 99 137
/**
 * Retrieves the y coordinate of the bottom edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */
58
inline PN_stdfloat MouseSubregion::get_bottom(void) const;

359 7 get_top 0 4 535 23 MouseSubregion::get_top 0 1 100 134
/**
 * Retrieves the y coordinate of the top edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */
55
inline PN_stdfloat MouseSubregion::get_top(void) const;

360 14 set_dimensions 0 4 535 30 MouseSubregion::set_dimensions 0 1 101 288
/**
 * Changes the region of the window in which the mouse is considered to be
 * active.  The parameters are identical to those for a DisplayRegion: they
 * range from 0 to 1, where 0,0 is the lower left corner and 1,1 is the upper
 * right; (0, 1, 0, 1) represents the whole window.
 */
103
inline void MouseSubregion::set_dimensions(PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);

361 14 get_class_type 0 4 535 30 MouseSubregion::get_class_type 0 1 102 0
55
static TypeHandle MouseSubregion::get_class_type(void);

362 37 upcast_to_TypedWritableReferenceCount 0 12 536 57 MouseWatcherRegion::upcast_to_TypedWritableReferenceCount 0 1 121 61
upcast from MouseWatcherRegion to TypedWritableReferenceCount
93
TypedWritableReferenceCount *MouseWatcherRegion::upcast_to_TypedWritableReferenceCount(void);

363 30 downcast_to_MouseWatcherRegion 0 12 537 59 TypedWritableReferenceCount::downcast_to_MouseWatcherRegion 0 0 63
downcast from TypedWritableReferenceCount to MouseWatcherRegion
86
MouseWatcherRegion *TypedWritableReferenceCount::downcast_to_MouseWatcherRegion(void);

364 17 upcast_to_Namable 0 12 536 37 MouseWatcherRegion::upcast_to_Namable 0 1 122 41
upcast from MouseWatcherRegion to Namable
53
Namable *MouseWatcherRegion::upcast_to_Namable(void);

365 30 downcast_to_MouseWatcherRegion 0 12 538 39 Namable::downcast_to_MouseWatcherRegion 0 0 43
downcast from Namable to MouseWatcherRegion
66
MouseWatcherRegion *Namable::downcast_to_MouseWatcherRegion(void);

366 18 MouseWatcherRegion 0 260 536 38 MouseWatcherRegion::MouseWatcherRegion 0 3 103 104 105 22
/**
 *
 */

/**
 *
 */
344
inline explicit MouseWatcherRegion::MouseWatcherRegion(std::string const &name, PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);
inline explicit MouseWatcherRegion::MouseWatcherRegion(std::string const &name, LVecBase4 const &frame);
inline MouseWatcherRegion::MouseWatcherRegion(MouseWatcherRegion const &) = default;

367 9 set_frame 0 4 536 29 MouseWatcherRegion::set_frame 0 2 106 107 22
/**
 *
 */

/**
 *
 */
183
inline void MouseWatcherRegion::set_frame(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);
inline void MouseWatcherRegion::set_frame(LVecBase4 const &frame);

368 9 get_frame 0 4 536 29 MouseWatcherRegion::get_frame 0 1 108 10
/**
 *
 */
66
inline LVecBase4 const &MouseWatcherRegion::get_frame(void) const;

369 8 get_area 0 4 536 28 MouseWatcherRegion::get_area 0 1 109 54
/**
 * Returns the area of the rectangular region.
 */
60
inline PN_stdfloat MouseWatcherRegion::get_area(void) const;

370 8 set_sort 0 4 536 28 MouseWatcherRegion::set_sort 0 1 110 366
/**
 * Changes the sorting order of this particular region.  The sorting order is
 * used to resolve conflicts in the case of overlapping region; the region
 * with the highest sort value will be preferred, and between regions of the
 * same sort value, the smallest region will be preferred.  The default
 * sorting order, if none is explicitly specified, is 0.
 */
51
inline void MouseWatcherRegion::set_sort(int sort);

371 8 get_sort 0 4 536 28 MouseWatcherRegion::get_sort 0 1 111 77
/**
 * Returns the current sorting order of this region.  See set_sort().
 */
52
inline int MouseWatcherRegion::get_sort(void) const;

372 10 set_active 0 4 536 30 MouseWatcherRegion::set_active 0 1 112 237
/**
 * Sets whether the region is active or not.  If it is not active, the
 * MouseWatcher will never consider the mouse to be over the region.  The
 * region might still receive keypress events if its set_keyboard() flag is
 * true.
 */
56
inline void MouseWatcherRegion::set_active(bool active);

373 10 get_active 0 4 536 30 MouseWatcherRegion::get_active 0 1 113 74
/**
 * Returns whether the region is active or not.  See set_active().
 */
55
inline bool MouseWatcherRegion::get_active(void) const;

374 12 set_keyboard 0 4 536 32 MouseWatcherRegion::set_keyboard 0 1 114 301
/**
 * Sets whether the region is interested in global keyboard events.  If this
 * is true, then any keyboard button events will be passed to press() and
 * release() regardless of the position of the mouse onscreen; otherwise,
 * these events will only be passed if the mouse is over the region.
 */
60
inline void MouseWatcherRegion::set_keyboard(bool keyboard);

375 12 get_keyboard 0 4 536 32 MouseWatcherRegion::get_keyboard 0 1 115 101
/**
 * Returns whether the region is interested in global keyboard events; see
 * set_keyboard().
 */
57
inline bool MouseWatcherRegion::get_keyboard(void) const;

376 18 set_suppress_flags 0 4 536 38 MouseWatcherRegion::set_suppress_flags 0 1 116 394
/**
 * Sets which events are suppressed when the mouse is over the region.  This
 * is the union of zero or more various SF_* values.  Normally, this is 0,
 * indicating that no events are suppressed.
 *
 * If you set this to a non-zero value, for instance SF_mouse_position, then
 * the mouse position will not be sent along the data graph when the mouse is
 * over this particular region.
 */
71
inline void MouseWatcherRegion::set_suppress_flags(int suppress_flags);

377 18 get_suppress_flags 0 4 536 38 MouseWatcherRegion::get_suppress_flags 0 1 117 73
/**
 * Returns the current suppress_flags.  See set_suppress_flags().
 */
62
inline int MouseWatcherRegion::get_suppress_flags(void) const;

378 6 output 0 4 536 26 MouseWatcherRegion::output 0 1 118 10
/**
 *
 */
57
void MouseWatcherRegion::output(std::ostream &out) const;

379 5 write 0 4 536 25 MouseWatcherRegion::write 0 1 119 10
/**
 *
 */
78
void MouseWatcherRegion::write(std::ostream &out, int indent_level = 0) const;

380 14 get_class_type 0 4 536 34 MouseWatcherRegion::get_class_type 0 1 120 0
59
static TypeHandle MouseWatcherRegion::get_class_type(void);

381 19 ~MouseWatcherRegion 0 516 536 39 MouseWatcherRegion::~MouseWatcherRegion 0 0 0
46
MouseWatcherRegion::~MouseWatcherRegion(void);

382 17 ~MouseWatcherBase 0 518 546 35 MouseWatcherBase::~MouseWatcherBase 0 0 10
/**
 *
 */
50
virtual MouseWatcherBase::~MouseWatcherBase(void);

383 10 add_region 0 4 546 28 MouseWatcherBase::add_region 0 1 123 150
/**
 * Adds the indicated region to the set of regions in the group.  It is no
 * longer an error to call this for the same region more than once.
 */
74
void MouseWatcherBase::add_region(PointerTo< MouseWatcherRegion > region);

384 10 has_region 0 4 546 28 MouseWatcherBase::has_region 0 1 124 115
/**
 * Returns true if the indicated region has already been added to the
 * MouseWatcherBase, false otherwise.
 */
68
bool MouseWatcherBase::has_region(MouseWatcherRegion *region) const;

385 13 remove_region 0 4 546 31 MouseWatcherBase::remove_region 0 1 125 152
/**
 * Removes the indicated region from the group.  Returns true if it was
 * successfully removed, or false if it wasn't there in the first place.
 */
65
bool MouseWatcherBase::remove_region(MouseWatcherRegion *region);

386 11 find_region 0 4 546 29 MouseWatcherBase::find_region 0 1 126 170
/**
 * Returns a pointer to the first region found with the indicated name.  If
 * multiple regions share the same name, the one that is returned is
 * indeterminate.
 */
81
MouseWatcherRegion *MouseWatcherBase::find_region(std::string const &name) const;

387 13 clear_regions 0 4 546 31 MouseWatcherBase::clear_regions 0 1 127 50
/**
 * Removes all the regions from the group.
 */
43
void MouseWatcherBase::clear_regions(void);

388 12 sort_regions 0 4 546 30 MouseWatcherBase::sort_regions 0 1 128 66
/**
 * Sorts all the regions in this group into pointer order.
 */
49
inline void MouseWatcherBase::sort_regions(void);

389 9 is_sorted 0 4 546 27 MouseWatcherBase::is_sorted 0 1 129 78
/**
 * Returns true if the group has already been sorted, false otherwise.
 */
52
inline bool MouseWatcherBase::is_sorted(void) const;

390 15 get_num_regions 0 4 546 33 MouseWatcherBase::get_num_regions 0 1 130 54
/**
 * Returns the number of regions in the group.
 */
58
std::size_t MouseWatcherBase::get_num_regions(void) const;

391 10 get_region 0 4 546 28 MouseWatcherBase::get_region 0 1 131 209
/**
 * Returns the nth region of the group; returns NULL if there is no nth
 * region.  Note that this is not thread-safe; another thread might have
 * removed the nth region before you called this method.
 */
70
MouseWatcherRegion *MouseWatcherBase::get_region(std::size_t n) const;

392 6 output 0 4 546 24 MouseWatcherBase::output 0 1 132 10
/**
 *
 */
55
void MouseWatcherBase::output(std::ostream &out) const;

393 5 write 0 4 546 23 MouseWatcherBase::write 0 1 133 10
/**
 *
 */
76
void MouseWatcherBase::write(std::ostream &out, int indent_level = 0) const;

394 12 show_regions 0 4 546 30 MouseWatcherBase::show_regions 0 1 134 179
/**
 * Enables the visualization of all of the regions handled by this
 * MouseWatcherBase.  The supplied NodePath should be the root of the 2-d
 * scene graph for the window.
 */
107
void MouseWatcherBase::show_regions(NodePath const &render2d, std::string const &bin_name, int draw_order);

395 9 set_color 0 4 546 27 MouseWatcherBase::set_color 0 1 135 114
/**
 * Specifies the color used to draw the region rectangles for the regions
 * visualized by show_regions().
 */
54
void MouseWatcherBase::set_color(LColor const &color);

396 12 hide_regions 0 4 546 30 MouseWatcherBase::hide_regions 0 1 136 80
/**
 * Stops the visualization created by a previous call to show_regions().
 */
42
void MouseWatcherBase::hide_regions(void);

397 14 update_regions 0 4 546 32 MouseWatcherBase::update_regions 0 1 137 65
/**
 * Refreshes the visualization created by show_regions().
 */
44
void MouseWatcherBase::update_regions(void);

398 14 get_class_type 0 4 546 32 MouseWatcherBase::get_class_type 0 1 138 0
57
static TypeHandle MouseWatcherBase::get_class_type(void);

399 26 upcast_to_MouseWatcherBase 0 12 548 45 MouseWatcherGroup::upcast_to_MouseWatcherBase 0 1 140 49
upcast from MouseWatcherGroup to MouseWatcherBase
70
MouseWatcherBase *MouseWatcherGroup::upcast_to_MouseWatcherBase(void);

400 29 downcast_to_MouseWatcherGroup 0 12 546 47 MouseWatcherBase::downcast_to_MouseWatcherGroup 0 0 51
downcast from MouseWatcherBase to MouseWatcherGroup
73
MouseWatcherGroup *MouseWatcherBase::downcast_to_MouseWatcherGroup(void);

401 24 upcast_to_ReferenceCount 0 12 548 43 MouseWatcherGroup::upcast_to_ReferenceCount 0 1 141 47
upcast from MouseWatcherGroup to ReferenceCount
66
ReferenceCount *MouseWatcherGroup::upcast_to_ReferenceCount(void);

402 29 downcast_to_MouseWatcherGroup 0 12 549 45 ReferenceCount::downcast_to_MouseWatcherGroup 0 0 49
downcast from ReferenceCount to MouseWatcherGroup
71
MouseWatcherGroup *ReferenceCount::downcast_to_MouseWatcherGroup(void);

403 14 get_class_type 0 4 548 33 MouseWatcherGroup::get_class_type 0 1 139 0
58
static TypeHandle MouseWatcherGroup::get_class_type(void);

404 18 ~MouseWatcherGroup 0 516 548 37 MouseWatcherGroup::~MouseWatcherGroup 0 0 0
44
MouseWatcherGroup::~MouseWatcherGroup(void);

405 18 upcast_to_DataNode 0 12 550 32 MouseWatcher::upcast_to_DataNode 0 1 204 36
upcast from MouseWatcher to DataNode
49
DataNode *MouseWatcher::upcast_to_DataNode(void);

406 24 downcast_to_MouseWatcher 0 12 525 34 DataNode::downcast_to_MouseWatcher 0 0 38
downcast from DataNode to MouseWatcher
55
MouseWatcher *DataNode::downcast_to_MouseWatcher(void);

407 26 upcast_to_MouseWatcherBase 0 12 550 40 MouseWatcher::upcast_to_MouseWatcherBase 0 1 205 44
upcast from MouseWatcher to MouseWatcherBase
65
MouseWatcherBase *MouseWatcher::upcast_to_MouseWatcherBase(void);

408 24 downcast_to_MouseWatcher 0 12 546 42 MouseWatcherBase::downcast_to_MouseWatcher 0 0 46
downcast from MouseWatcherBase to MouseWatcher
63
MouseWatcher *MouseWatcherBase::downcast_to_MouseWatcher(void);

409 12 MouseWatcher 0 260 550 26 MouseWatcher::MouseWatcher 0 1 142 10
/**
 *
 */
66
explicit MouseWatcher::MouseWatcher(std::string const &name = "");

410 13 ~MouseWatcher 0 518 550 27 MouseWatcher::~MouseWatcher 0 0 10
/**
 *
 */
42
virtual MouseWatcher::~MouseWatcher(void);

411 13 remove_region 0 4 550 27 MouseWatcher::remove_region 0 1 143 152
/**
 * Removes the indicated region from the group.  Returns true if it was
 * successfully removed, or false if it wasn't there in the first place.
 */
61
bool MouseWatcher::remove_region(MouseWatcherRegion *region);

412 9 has_mouse 0 4 550 23 MouseWatcher::has_mouse 0 1 144 113
/**
 * Returns true if the mouse is anywhere within the window, false otherwise.
 * Also see is_mouse_open().
 */
48
inline bool MouseWatcher::has_mouse(void) const;

413 13 is_mouse_open 0 4 550 27 MouseWatcher::is_mouse_open 0 1 145 212
/**
 * Returns true if the mouse is within the window and not over some particular
 * MouseWatcherRegion that is marked to suppress mouse events; that is, that
 * the mouse is in open space within the window.
 */
52
inline bool MouseWatcher::is_mouse_open(void) const;

414 9 get_mouse 0 4 550 23 MouseWatcher::get_mouse 0 1 146 146
/**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current position of the mouse within the window.
 */
58
inline LPoint2 const &MouseWatcher::get_mouse(void) const;

415 11 get_mouse_x 0 4 550 25 MouseWatcher::get_mouse_x 0 1 147 148
/**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current X position of the mouse within the window.
 */
57
inline PN_stdfloat MouseWatcher::get_mouse_x(void) const;

416 11 get_mouse_y 0 4 550 25 MouseWatcher::get_mouse_y 0 1 148 148
/**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current Y position of the mouse within the window.
 */
57
inline PN_stdfloat MouseWatcher::get_mouse_y(void) const;

417 9 set_frame 0 4 550 23 MouseWatcher::set_frame 0 2 149 150 686
/**
 * Sets the frame of the MouseWatcher.  See the next flavor of this method for
 * a more verbose explanation.
 */

/**
 * Sets the frame of the MouseWatcher.  This determines the coordinate space
 * in which the MouseWatcherRegions should be expected to live.  Normally,
 * this is left at -1, 1, -1, 1, which is the default setting, and matches the
 * mouse coordinate range.
 *
 * Whatever values you specify here indicate the shape of the full screen, and
 * the MouseWatcherRegions will be given in coordinate space matching it.  For
 * instance, if you specify (0, 1, 0, 1), then a MouseWatcherRegion with the
 * frame (0, 1, 0, .5) will cover the lower half of the screen.
 */
171
inline void MouseWatcher::set_frame(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);
inline void MouseWatcher::set_frame(LVecBase4 const &frame);

418 9 get_frame 0 4 550 23 MouseWatcher::get_frame 0 1 151 67
/**
 * Returns the frame of the MouseWatcher.  See set_frame().
 */
60
inline LVecBase4 const &MouseWatcher::get_frame(void) const;

419 14 is_over_region 0 4 550 28 MouseWatcher::is_over_region 0 3 152 153 154 259
/**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */

/**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */

/**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */
199
inline bool MouseWatcher::is_over_region(void) const;
inline bool MouseWatcher::is_over_region(PN_stdfloat x, PN_stdfloat y) const;
inline bool MouseWatcher::is_over_region(LPoint2 const &pos) const;

420 15 get_over_region 0 4 550 29 MouseWatcher::get_over_region 0 3 155 156 157 443
/**
 * Returns the smallest region the mouse is currently over, or NULL if it is
 * over no region.
 */

/**
 * Returns the smallest region the indicated point is over, or NULL if it is
 * over no region.
 */

/**
 * Returns the preferred region the mouse is over.  In the case of overlapping
 * regions, the region with the largest sort order is preferred; if two
 * regions have the same sort order, then the smaller region is preferred.
 */
240
inline MouseWatcherRegion *MouseWatcher::get_over_region(void) const;
inline MouseWatcherRegion *MouseWatcher::get_over_region(PN_stdfloat x, PN_stdfloat y) const;
MouseWatcherRegion *MouseWatcher::get_over_region(LPoint2 const &pos) const;

421 14 is_button_down 0 4 550 28 MouseWatcher::is_button_down 0 1 158 97
/**
 * Returns true if the indicated button is currently being held down, false
 * otherwise.
 */
68
inline bool MouseWatcher::is_button_down(ButtonHandle button) const;

422 23 set_button_down_pattern 0 4 550 37 MouseWatcher::set_button_down_pattern 0 1 159 432
/**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is depressed.  This is a string that may contain any of the
 * following:
 *
 * %r  - the name of the region the mouse is over %b  - the name of the button
 * pressed.
 *
 * The event name will be based on the in_pattern string specified here, with
 * all occurrences of the above strings replaced with the corresponding
 * values.
 */
78
inline void MouseWatcher::set_button_down_pattern(std::string const &pattern);

423 23 get_button_down_pattern 0 4 550 37 MouseWatcher::get_button_down_pattern 0 1 160 137
/**
 * Returns the string that indicates how event names are generated when a
 * button is depressed.  See set_button_down_pattern().
 */
76
inline std::string const &MouseWatcher::get_button_down_pattern(void) const;

424 21 set_button_up_pattern 0 4 550 35 MouseWatcher::set_button_up_pattern 0 1 161 145
/**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is released.  See set_button_down_pattern().
 */
76
inline void MouseWatcher::set_button_up_pattern(std::string const &pattern);

425 21 get_button_up_pattern 0 4 550 35 MouseWatcher::get_button_up_pattern 0 1 162 136
/**
 * Returns the string that indicates how event names are generated when a
 * button is released.  See set_button_down_pattern().
 */
74
inline std::string const &MouseWatcher::get_button_up_pattern(void) const;

426 25 set_button_repeat_pattern 0 4 550 39 MouseWatcher::set_button_repeat_pattern 0 1 163 477
/**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is continuously held and generates keyrepeat "down" events.
 * This is a string that may contain any of the following:
 *
 * %r  - the name of the region the mouse is over %b  - the name of the button
 * pressed.
 *
 * The event name will be based on the in_pattern string specified here, with
 * all occurrences of the above strings replaced with the corresponding
 * values.
 */
80
inline void MouseWatcher::set_button_repeat_pattern(std::string const &pattern);

427 25 get_button_repeat_pattern 0 4 550 39 MouseWatcher::get_button_repeat_pattern 0 1 164 197
/**
 * Returns the string that indicates how event names are names are generated
 * when a button is continuously held and generates keyrepeat "down" events.
 * See set_button_repeat_pattern().
 */
78
inline std::string const &MouseWatcher::get_button_repeat_pattern(void) const;

428 17 set_enter_pattern 0 4 550 31 MouseWatcher::set_enter_pattern 0 1 165 289
/**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse enters a region.  This is different from within_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */
72
inline void MouseWatcher::set_enter_pattern(std::string const &pattern);

429 17 get_enter_pattern 0 4 550 31 MouseWatcher::get_enter_pattern 0 1 166 280
/**
 * Returns the string that indicates how event names are generated when the
 * mouse enters a region.  This is different from within_pattern, in that a
 * mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */
70
inline std::string const &MouseWatcher::get_enter_pattern(void) const;

430 17 set_leave_pattern 0 4 550 31 MouseWatcher::set_leave_pattern 0 1 167 290
/**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse leaves a region.  This is different from without_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */
72
inline void MouseWatcher::set_leave_pattern(std::string const &pattern);

431 17 get_leave_pattern 0 4 550 31 MouseWatcher::get_leave_pattern 0 1 168 281
/**
 * Returns the string that indicates how event names are generated when the
 * mouse leaves a region.  This is different from without_pattern, in that a
 * mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */
70
inline std::string const &MouseWatcher::get_leave_pattern(void) const;

432 18 set_within_pattern 0 4 550 32 MouseWatcher::set_within_pattern 0 1 169 294
/**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse wanders over a region.  This is different from
 * enter_pattern, in that a mouse is only "entered" in the topmost region at a
 * given time, while it might be "within" multiple nested regions.
 */
73
inline void MouseWatcher::set_within_pattern(std::string const &pattern);

433 18 get_within_pattern 0 4 550 32 MouseWatcher::get_within_pattern 0 1 170 285
/**
 * Returns the string that indicates how event names are generated when the
 * mouse wanders over a region.  This is different from enter_pattern, in that
 * a mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */
71
inline std::string const &MouseWatcher::get_within_pattern(void) const;

434 19 set_without_pattern 0 4 550 33 MouseWatcher::set_without_pattern 0 1 171 296
/**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse wanders out of a region.  This is different from
 * leave_pattern, in that a mouse is only "entered" in the topmost region at a
 * given time, while it might be "within" multiple nested regions.
 */
74
inline void MouseWatcher::set_without_pattern(std::string const &pattern);

435 19 get_without_pattern 0 4 550 33 MouseWatcher::get_without_pattern 0 1 172 287
/**
 * Returns the string that indicates how event names are generated when the
 * mouse wanders out of a region.  This is different from leave_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */
72
inline std::string const &MouseWatcher::get_without_pattern(void) const;

436 12 set_geometry 0 4 550 26 MouseWatcher::set_geometry 0 1 173 279
/**
 * Sets the node that will be transformed each frame by the mouse's
 * coordinates.  It will also be hidden when the mouse goes outside the
 * window.  This can be used to implement a software mouse pointer for when a
 * hardware (or system) mouse pointer is unavailable.
 */
56
inline void MouseWatcher::set_geometry(PandaNode *node);

437 12 has_geometry 0 4 550 26 MouseWatcher::has_geometry 0 1 174 130
/**
 * Returns true if a software mouse pointer has been setup via set_geometry(),
 * or false otherwise.  See set_geometry().
 */
51
inline bool MouseWatcher::has_geometry(void) const;

438 12 get_geometry 0 4 550 26 MouseWatcher::get_geometry 0 1 175 152
/**
 * Returns the node that has been set as the software mouse pointer, or NULL
 * if no node has been set.  See has_geometry() and set_geometry().
 */
57
inline PandaNode *MouseWatcher::get_geometry(void) const;

439 14 clear_geometry 0 4 550 28 MouseWatcher::clear_geometry 0 1 176 74
/**
 * Stops the use of the software cursor set up via set_geometry().
 */
47
inline void MouseWatcher::clear_geometry(void);

440 17 set_extra_handler 0 4 550 31 MouseWatcher::set_extra_handler 0 1 177 150
/**
 * As an optimization for the C++ Gui, an extra handler can be registered with
 * a mouseWatcher so that events can be dealt with much sooner.
 */
62
inline void MouseWatcher::set_extra_handler(EventHandler *eh);

441 17 get_extra_handler 0 4 550 31 MouseWatcher::get_extra_handler 0 1 178 150
/**
 * As an optimization for the C++ Gui, an extra handler can be registered with
 * a mouseWatcher so that events can be dealt with much sooner.
 */
65
inline EventHandler *MouseWatcher::get_extra_handler(void) const;

442 20 set_modifier_buttons 0 4 550 34 MouseWatcher::set_modifier_buttons 0 1 179 125
/**
 * Sets the buttons that should be monitored as modifier buttons for
 * generating events to the MouseWatcherRegions.
 */
76
inline void MouseWatcher::set_modifier_buttons(ModifierButtons const &mods);

443 20 get_modifier_buttons 0 4 550 34 MouseWatcher::get_modifier_buttons 0 1 180 118
/**
 * Returns the set of buttons that are being monitored as modifier buttons, as
 * well as their current state.
 */
70
inline ModifierButtons MouseWatcher::get_modifier_buttons(void) const;

444 18 set_display_region 0 4 550 32 MouseWatcher::set_display_region 0 1 181 402
/**
 * Constrains the MouseWatcher to watching the mouse within a particular
 * indicated region of the screen.  DataNodes parented under the MouseWatcher
 * will observe the mouse and keyboard events only when the mouse is within
 * the indicated region, and the observed range will be from -1 .. 1 across
 * the region.
 *
 * Do not delete the DisplayRegion while it is owned by the MouseWatcher.
 */
64
inline void MouseWatcher::set_display_region(DisplayRegion *dr);

445 20 clear_display_region 0 4 550 34 MouseWatcher::clear_display_region 0 1 182 144
/**
 * Removes the display region constraint from the MouseWatcher, and restores
 * it to the default behavior of watching the whole window.
 */
53
inline void MouseWatcher::clear_display_region(void);

446 18 get_display_region 0 4 550 32 MouseWatcher::get_display_region 0 1 183 134
/**
 * Returns the display region the MouseWatcher is constrained to by
 * set_display_region(), or NULL if it is not constrained.
 */
67
inline DisplayRegion *MouseWatcher::get_display_region(void) const;

447 18 has_display_region 0 4 550 32 MouseWatcher::has_display_region 0 1 184 245
/**
 * Returns true if the MouseWatcher has been constrained to a particular
 * region of the screen via set_display_region(), or false otherwise.  If this
 * returns true, get_display_region() may be used to return the particular
 * region.
 */
57
inline bool MouseWatcher::has_display_region(void) const;

448 9 add_group 0 4 550 23 MouseWatcher::add_group 0 1 185 496
/**
 * Adds the indicated group of regions to the set of regions the MouseWatcher
 * will monitor each frame.
 *
 * Since the MouseWatcher itself inherits from MouseWatcherBase, this
 * operation is normally not necessary--you can simply add the Regions you
 * care about one at a time.  Adding a complete group is useful when you may
 * want to explicitly remove the regions as a group later.
 *
 * Returns true if the group was successfully added, or false if it was
 * already on the list.
 */
55
bool MouseWatcher::add_group(MouseWatcherGroup *group);

449 12 remove_group 0 4 550 26 MouseWatcher::remove_group 0 1 186 214
/**
 * Removes the indicated group from the set of extra groups associated with
 * the MouseWatcher.  Returns true if successful, or false if the group was
 * already removed or was never added via add_group().
 */
58
bool MouseWatcher::remove_group(MouseWatcherGroup *group);

450 13 replace_group 0 4 550 27 MouseWatcher::replace_group 0 1 187 362
/**
 * Atomically removes old_group from the MouseWatcher, and replaces it with
 * new_group.  Presumably old_group and new_group might have some regions in
 * common; these are handled properly.
 *
 * If old_group is not already present, simply adds new_group and returns
 * false.  Otherwise, removes old_group and adds new_group, and then returns
 * true.
 */
93
bool MouseWatcher::replace_group(MouseWatcherGroup *old_group, MouseWatcherGroup *new_group);

451 14 get_num_groups 0 4 550 28 MouseWatcher::get_num_groups 0 1 188 94
/**
 * Returns the number of separate groups added to the MouseWatcher via
 * add_group().
 */
45
int MouseWatcher::get_num_groups(void) const;

452 9 get_group 0 4 550 23 MouseWatcher::get_group 0 1 189 75
/**
 * Returns the nth group added to the MouseWatcher via add_group().
 */
56
MouseWatcherGroup *MouseWatcher::get_group(int n) const;

453 22 set_inactivity_timeout 0 4 550 36 MouseWatcher::set_inactivity_timeout 0 1 190 480
/**
 * Sets an inactivity timeout on the mouse activity.  When this timeout (in
 * seconds) is exceeded with no keyboard or mouse activity, all currently-held
 * buttons are automatically released.  This is intended to help protect
 * against people who inadvertently (or intentionally) leave a keyboard key
 * stuck down and then wander away from the keyboard.
 *
 * Also, when this timeout expires, the event specified by
 * set_inactivity_timeout_event() will be generated.
 */
65
inline void MouseWatcher::set_inactivity_timeout(double timeout);

454 22 has_inactivity_timeout 0 4 550 36 MouseWatcher::has_inactivity_timeout 0 1 191 79
/**
 * Returns true if an inactivity timeout has been set, false otherwise.
 */
61
inline bool MouseWatcher::has_inactivity_timeout(void) const;

455 22 get_inactivity_timeout 0 4 550 36 MouseWatcher::get_inactivity_timeout 0 1 192 135
/**
 * Returns the inactivity timeout that has been set.  It is an error to call
 * this if has_inactivity_timeout() returns false.
 */
63
inline double MouseWatcher::get_inactivity_timeout(void) const;

456 24 clear_inactivity_timeout 0 4 550 38 MouseWatcher::clear_inactivity_timeout 0 1 193 141
/**
 * Removes the inactivity timeout and restores the MouseWatcher to its default
 * behavior of allowing a key to be held indefinitely.
 */
57
inline void MouseWatcher::clear_inactivity_timeout(void);

457 28 set_inactivity_timeout_event 0 4 550 42 MouseWatcher::set_inactivity_timeout_event 0 1 194 139
/**
 * Specifies the event string that will be generated when the inactivity
 * timeout counter expires.  See set_inactivity_timeout().
 */
81
inline void MouseWatcher::set_inactivity_timeout_event(std::string const &event);

458 28 get_inactivity_timeout_event 0 4 550 42 MouseWatcher::get_inactivity_timeout_event 0 1 195 137
/**
 * Returns the event string that will be generated when the inactivity timeout
 * counter expires.  See set_inactivity_timeout().
 */
81
inline std::string const &MouseWatcher::get_inactivity_timeout_event(void) const;

459 13 get_trail_log 0 4 550 27 MouseWatcher::get_trail_log 0 1 196 395
/**
 * Obtain the mouse trail log.  This is a PointerEventList.  Does not make a
 * copy, therefore, this PointerEventList will be updated each time
 * process_events gets called.
 *
 * To use trail logging, you need to enable the generation of pointer events
 * in the GraphicsWindowInputDevice and set the trail log duration in the
 * MouseWatcher.  Otherwise, the trail log will be empty.
 */
82
inline ConstPointerTo< PointerEventList > MouseWatcher::get_trail_log(void) const;

460 16 num_trail_recent 0 4 550 30 MouseWatcher::num_trail_recent 0 1 197 174
/**
 * This counter indicates how many events were added to the trail log this
 * frame.  The trail log is updated once per frame, during the process_events
 * operation.
 */
62
inline std::size_t MouseWatcher::num_trail_recent(void) const;

461 22 set_trail_log_duration 0 4 550 36 MouseWatcher::set_trail_log_duration 0 1 198 198
/**
 * If the duration is nonzero, causes the MouseWatcher to log the mouse's
 * trail.  Events older than the specified duration are discarded.  If the
 * duration is zero, logging is disabled.
 */
59
void MouseWatcher::set_trail_log_duration(double duration);

462 14 get_trail_node 0 4 550 28 MouseWatcher::get_trail_node 0 1 199 618
/**
 * Returns a GeomNode that represents the mouse trail.  The intent is that you
 * should reparent this GeomNode to Render2D, and then forget about it.  The
 * MouseWatcher will continually update the trail node.  There is only one
 * trail node, it does not create a new one each time you call get_trail_node.
 *
 * This is not a particularly beautiful way to render a mouse trail.  It is
 * intended more for debugging purposes than for finished applications.  Even
 * so, It is suggested that you might want to apply a line thickness and
 * antialias mode to the line --- doing so makes it look a lot better.
 */
57
PointerTo< GeomNode > MouseWatcher::get_trail_node(void);

463 16 clear_trail_node 0 4 550 30 MouseWatcher::clear_trail_node 0 1 200 256
/**
 * If you have previously fetched the trail node using get_trail_node, then
 * the MouseWatcher is continually updating the trail node every frame.  Using
 * clear_trail_node causes the MouseWatcher to forget the trail node and stop
 * updating it.
 */
42
void MouseWatcher::clear_trail_node(void);

464 15 clear_trail_log 0 4 550 29 MouseWatcher::clear_trail_log 0 1 201 117
/**
 * Clears the mouse trail log.  This does not prevent further accumulation of
 * the log given future events.
 */
48
inline void MouseWatcher::clear_trail_log(void);

465 13 note_activity 0 4 550 27 MouseWatcher::note_activity 0 1 202 586
/**
 * Can be used in conjunction with the inactivity timeout to inform the
 * MouseWatcher that the user has just performed some action which proves
 * he/she is present.  It may be necessary to call this for external events,
 * such as joystick action, that the MouseWatcher might otherwise not know
 * about.  This will reset the current inactivity timer.  When the inactivity
 * timer reaches the length of time specified by set_inactivity_timeout(),
 * with no keyboard or mouse activity and no calls to note_activity(), then
 * any buttons held will be automatically released.
 */
39
void MouseWatcher::note_activity(void);

466 14 get_class_type 0 4 550 28 MouseWatcher::get_class_type 0 1 203 0
53
static TypeHandle MouseWatcher::get_class_type(void);

467 22 ~MouseWatcherParameter 0 516 551 45 MouseWatcherParameter::~MouseWatcherParameter 0 0 10
/**
 *
 */
59
inline MouseWatcherParameter::~MouseWatcherParameter(void);

468 10 has_button 0 4 551 33 MouseWatcherParameter::has_button 0 1 206 105
/**
 * Returns true if this parameter has an associated mouse or keyboard button,
 * false otherwise.
 */
58
inline bool MouseWatcherParameter::has_button(void) const;

469 10 get_button 0 4 551 33 MouseWatcherParameter::get_button 0 1 207 153
/**
 * Returns the mouse or keyboard button associated with this event.  If
 * has_button(), above, returns false, this returns ButtonHandle::none().
 */
66
inline ButtonHandle MouseWatcherParameter::get_button(void) const;

470 12 is_keyrepeat 0 4 551 35 MouseWatcherParameter::is_keyrepeat 0 1 208 126
/**
 * Returns true if the button-down even was generated due to keyrepeat, or
 * false if it was an original button down.
 */
60
inline bool MouseWatcherParameter::is_keyrepeat(void) const;

471 11 has_keycode 0 4 551 34 MouseWatcherParameter::has_keycode 0 1 209 85
/**
 * Returns true if this parameter has an associated keycode, false otherwise.
 */
59
inline bool MouseWatcherParameter::has_keycode(void) const;

472 11 get_keycode 0 4 551 34 MouseWatcherParameter::get_keycode 0 1 210 118
/**
 * Returns the keycode associated with this event.  If has_keycode(), above,
 * returns false, this returns 0.
 */
58
inline int MouseWatcherParameter::get_keycode(void) const;

473 13 has_candidate 0 4 551 36 MouseWatcherParameter::has_candidate 0 1 211 97
/**
 * Returns true if this parameter has an associated candidate string, false
 * otherwise.
 */
61
inline bool MouseWatcherParameter::has_candidate(void) const;

474 28 get_candidate_string_encoded 0 4 551 51 MouseWatcherParameter::get_candidate_string_encoded 0 2 212 213 290
/**
 * Returns the candidate string associated with this event.  If
 * has_candidate(), above, returns false, this returns the empty string.
 */

/**
 * Returns the candidate string associated with this event.  If
 * has_candidate(), above, returns false, this returns the empty string.
 */
193
inline std::string MouseWatcherParameter::get_candidate_string_encoded(void) const;
inline std::string MouseWatcherParameter::get_candidate_string_encoded(TextEncoder::Encoding encoding) const;

475 19 get_highlight_start 0 4 551 42 MouseWatcherParameter::get_highlight_start 0 1 214 75
/**
 * Returns the first highlighted character in the candidate string.
 */
74
inline std::size_t MouseWatcherParameter::get_highlight_start(void) const;

476 17 get_highlight_end 0 4 551 40 MouseWatcherParameter::get_highlight_end 0 1 215 91
/**
 * Returns one more than the last highlighted character in the candidate
 * string.
 */
72
inline std::size_t MouseWatcherParameter::get_highlight_end(void) const;

477 14 get_cursor_pos 0 4 551 37 MouseWatcherParameter::get_cursor_pos 0 1 216 86
/**
 * Returns the position of the user's edit cursor within the candidate string.
 */
69
inline std::size_t MouseWatcherParameter::get_cursor_pos(void) const;

478 20 get_modifier_buttons 0 4 551 43 MouseWatcherParameter::get_modifier_buttons 0 1 217 106
/**
 * Returns the set of modifier buttons that were being held down while the
 * event was generated.
 */
86
inline ModifierButtons const &MouseWatcherParameter::get_modifier_buttons(void) const;

479 9 has_mouse 0 4 551 32 MouseWatcherParameter::has_mouse 0 1 218 95
/**
 * Returns true if this parameter has an associated mouse position, false
 * otherwise.
 */
57
inline bool MouseWatcherParameter::has_mouse(void) const;

480 9 get_mouse 0 4 551 32 MouseWatcherParameter::get_mouse 0 1 219 176
/**
 * Returns the mouse position at the time the event was generated, in the
 * normalized range (-1 .. 1).  It is valid to call this only if has_mouse()
 * returned true.
 */
67
inline LPoint2 const &MouseWatcherParameter::get_mouse(void) const;

481 10 is_outside 0 4 551 33 MouseWatcherParameter::is_outside 0 1 220 163
/**
 * Returns true if the mouse was outside the region at the time the event was
 * generated, false otherwise.  This is only valid for "release" type events.
 */
58
inline bool MouseWatcherParameter::is_outside(void) const;

482 6 output 0 4 551 29 MouseWatcherParameter::output 0 1 221 10
/**
 *
 */
60
void MouseWatcherParameter::output(std::ostream &out) const;

483 9 Trackball 0 260 552 20 Trackball::Trackball 0 2 222 223 10
/**
 *
 */
113
explicit Trackball::Trackball(std::string const &name);
inline Trackball::Trackball(Trackball const &) = default;

484 5 reset 0 4 552 16 Trackball::reset 0 1 224 52
/**
 * Reinitializes all transforms to identity.
 */
28
void Trackball::reset(void);

485 17 get_forward_scale 0 4 552 28 Trackball::get_forward_scale 0 1 225 104
/**
 * Returns the scale factor applied to forward and backward motion.  See
 * set_forward_scale().
 */
53
PN_stdfloat Trackball::get_forward_scale(void) const;

486 17 set_forward_scale 0 4 552 28 Trackball::set_forward_scale 0 1 226 173
/**
 * Changes the scale factor applied to forward and backward motion.  The
 * larger this number, the faster the model will move in response to dollying
 * in and out.
 */
56
void Trackball::set_forward_scale(PN_stdfloat fwdscale);

487 7 get_pos 0 4 552 18 Trackball::get_pos 0 1 227 57
/**
 * Return the offset from the center of rotation.
 */
46
LPoint3 const &Trackball::get_pos(void) const;

488 5 get_x 0 4 552 16 Trackball::get_x 0 1 228 0
41
PN_stdfloat Trackball::get_x(void) const;

489 5 get_y 0 4 552 16 Trackball::get_y 0 1 229 0
41
PN_stdfloat Trackball::get_y(void) const;

490 5 get_z 0 4 552 16 Trackball::get_z 0 1 230 0
41
PN_stdfloat Trackball::get_z(void) const;

491 7 set_pos 0 4 552 18 Trackball::set_pos 0 2 231 232 62
/**
 * Directly set the offset from the rotational origin.
 */
116
void Trackball::set_pos(LVecBase3 const &vec);
void Trackball::set_pos(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

492 5 set_x 0 4 552 16 Trackball::set_x 0 1 233 0
37
void Trackball::set_x(PN_stdfloat x);

493 5 set_y 0 4 552 16 Trackball::set_y 0 1 234 0
37
void Trackball::set_y(PN_stdfloat y);

494 5 set_z 0 4 552 16 Trackball::set_z 0 1 235 0
37
void Trackball::set_z(PN_stdfloat z);

495 7 get_hpr 0 4 552 18 Trackball::get_hpr 0 1 236 46
/**
 * Return the trackball's orientation.
 */
41
LVecBase3 Trackball::get_hpr(void) const;

496 5 get_h 0 4 552 16 Trackball::get_h 0 1 237 0
41
PN_stdfloat Trackball::get_h(void) const;

497 5 get_p 0 4 552 16 Trackball::get_p 0 1 238 0
41
PN_stdfloat Trackball::get_p(void) const;

498 5 get_r 0 4 552 16 Trackball::get_r 0 1 239 0
41
PN_stdfloat Trackball::get_r(void) const;

499 7 set_hpr 0 4 552 18 Trackball::set_hpr 0 2 240 241 48
/**
 * Directly set the mover's orientation.
 */
116
void Trackball::set_hpr(LVecBase3 const &hpr);
void Trackball::set_hpr(PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);

500 5 set_h 0 4 552 16 Trackball::set_h 0 1 242 0
37
void Trackball::set_h(PN_stdfloat h);

501 5 set_p 0 4 552 16 Trackball::set_p 0 1 243 0
37
void Trackball::set_p(PN_stdfloat p);

502 5 set_r 0 4 552 16 Trackball::set_r 0 1 244 0
37
void Trackball::set_r(PN_stdfloat r);

503 17 reset_origin_here 0 4 552 28 Trackball::reset_origin_here 0 1 245 148
/**
 * Reposition the center of rotation to coincide with the current translation
 * offset.  Future rotations will be about the current origin.
 */
40
void Trackball::reset_origin_here(void);

504 11 move_origin 0 4 552 22 Trackball::move_origin 0 1 246 60
/**
 * Moves the center of rotation by the given amount.
 */
73
void Trackball::move_origin(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

505 10 get_origin 0 4 552 21 Trackball::get_origin 0 1 247 50
/**
 * Returns the current center of rotation.
 */
42
LPoint3 Trackball::get_origin(void) const;

506 10 set_origin 0 4 552 21 Trackball::set_origin 0 1 248 48
/**
 * Directly sets the center of rotation.
 */
52
void Trackball::set_origin(LVecBase3 const &origin);

507 16 set_control_mode 0 4 552 27 Trackball::set_control_mode 0 1 249 348
/**
 * Sets the control mode.  Normally this is CM_default, which means each mouse
 * button serves its normal function.  When it is CM_truck, CM_pan, CM_dolly,
 * or CM_roll, all of the mouse buttons serve the indicated function instead
 * of their normal function.  This can be used in conjunction with some
 * external way of changing modes.
 */
70
void Trackball::set_control_mode(Trackball::ControlMode control_mode);

508 16 get_control_mode 0 4 552 27 Trackball::get_control_mode 0 1 250 61
/**
 * Returns the control mode.  See set_control_mode().
 */
63
Trackball::ControlMode Trackball::get_control_mode(void) const;

509 10 set_invert 0 4 552 21 Trackball::set_invert 0 1 251 162
/**
 * Sets the invert flag.  When this is set, the inverse matrix is generated,
 * suitable for joining to a camera, instead of parenting the scene under it.
 */
38
void Trackball::set_invert(bool flag);

510 10 get_invert 0 4 552 21 Trackball::get_invert 0 1 252 168
/**
 * Returns the invert flag.  When this is set, the inverse matrix is
 * generated, suitable for joining to a camera, instead of parenting the scene
 * under it.
 */
39
bool Trackball::get_invert(void) const;

511 10 set_rel_to 0 4 552 21 Trackball::set_rel_to 0 1 253 275
/**
 * Sets the NodePath that all trackball manipulations are to be assumed to be
 * relative to.  For instance, set your camera node here to make the trackball
 * motion camera relative.  The default is the empty path, which means
 * trackball motion is in global space.
 */
51
void Trackball::set_rel_to(NodePath const &rel_to);

512 10 get_rel_to 0 4 552 21 Trackball::get_rel_to 0 1 254 103
/**
 * Returns the NodePath that all trackball manipulations are relative to, or
 * the empty path.
 */
50
NodePath const &Trackball::get_rel_to(void) const;

513 21 set_coordinate_system 0 4 552 32 Trackball::set_coordinate_system 0 1 255 243
/**
 * Sets the coordinate system of the Trackball.  Normally, this is the default
 * coordinate system.  This changes the axes the Trackball manipulates so that
 * the user interface remains consistent across different coordinate systems.
 */
59
void Trackball::set_coordinate_system(CoordinateSystem cs);

514 21 get_coordinate_system 0 4 552 32 Trackball::get_coordinate_system 0 1 256 91
/**
 * Returns the coordinate system of the Trackball.  See
 * set_coordinate_system().
 */
62
CoordinateSystem Trackball::get_coordinate_system(void) const;

515 7 set_mat 0 4 552 18 Trackball::set_mat 0 1 257 131
/**
 * Stores the indicated transform in the trackball.  This is a transform in
 * global space, regardless of the rel_to node.
 */
45
void Trackball::set_mat(LMatrix4 const &mat);

516 7 get_mat 0 4 552 18 Trackball::get_mat 0 1 258 68
/**
 * Returns the matrix represented by the trackball rotation.
 */
47
LMatrix4 const &Trackball::get_mat(void) const;

517 13 get_trans_mat 0 4 552 24 Trackball::get_trans_mat 0 1 259 143
/**
 * Returns the actual transform that will be applied to the scene graph.  This
 * is the same as get_mat(), unless invert is in effect.
 */
53
LMatrix4 const &Trackball::get_trans_mat(void) const;

518 14 get_class_type 0 4 552 25 Trackball::get_class_type 0 1 260 0
50
static TypeHandle Trackball::get_class_type(void);

519 12 Transform2SG 0 260 554 26 Transform2SG::Transform2SG 0 2 261 262 10
/**
 *
 */
128
explicit Transform2SG::Transform2SG(std::string const &name);
inline Transform2SG::Transform2SG(Transform2SG const &) = default;

520 8 set_node 0 4 554 22 Transform2SG::set_node 0 1 263 54
/**
 * Sets the node that this object will adjust.
 */
45
void Transform2SG::set_node(PandaNode *node);

521 8 get_node 0 4 554 22 Transform2SG::get_node 0 1 264 102
/**
 * Returns the node that this object will adjust, or NULL if the node has not
 * yet been set.
 */
46
PandaNode *Transform2SG::get_node(void) const;

522 14 get_class_type 0 4 554 28 Transform2SG::get_class_type 0 1 265 0
53
static TypeHandle Transform2SG::get_class_type(void);

523 13 ~Transform2SG 0 516 554 27 Transform2SG::~Transform2SG 0 0 0
34
Transform2SG::~Transform2SG(void);

265
1 0 0 15 3 557 0 0 0 1 6 param0 0 555  
2 0 0 7 3 557 0 0 10 /**
 *
 */ 1 4 name 1 558  
3 0 0 4 4 559 0 0 510 /**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is depressed.  Unlike the specific events that are unique to each
 * key, this same event name is used for *all* button events, and the name of
 * the button pressed (possibly with modifier prefixes) will be sent as a
 * parameter.
 *
 * If this string is empty, no event is generated.  It is possible to generate
 * both generic events and specific events for the same button.
 *
 * See also set_keystroke_event().
 */ 2 4 this 3 557  17 button_down_event 1 558  
4 0 0 6 5 558 0 0 114 /**
 * Returns the button_down_event that has been set on this ButtonThrower.  See
 * set_button_down_event().
 */ 1 4 this 3 555  
5 0 0 4 6 559 0 0 137 /**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is released.  See set_button_down_event().
 */ 2 4 this 3 557  15 button_up_event 1 558  
6 0 0 6 7 558 0 0 110 /**
 * Returns the button_up_event that has been set on this ButtonThrower.  See
 * set_button_up_event().
 */ 1 4 this 3 555  
7 0 0 4 8 559 0 0 517 /**
 * Specifies the generic event that is generated (if any) repeatedly while a
 * key or button is held down.  Unlike the specific events that are unique to
 * each key, this same event name is used for *all* button events, and the
 * name of the button pressed (possibly with modifier prefixes) will be sent
 * as a parameter.
 *
 * If this string is empty, no event is generated.  It is possible to generate
 * both generic events and specific events for the same button.
 *
 * See also set_keystroke_event().
 */ 2 4 this 3 557  19 button_repeat_event 1 558  
8 0 0 6 9 558 0 0 117 /**
 * Returns the button_repeat_event that has been set on this ButtonThrower.
 * See set_button_repeat_event().
 */ 1 4 this 3 555  
9 0 0 4 10 559 0 0 846 /**
 * Specifies the event that is generated (if any) for each keystroke that is
 * received.  A keystroke is different than a button event: it represents the
 * semantic meaning of the sequence of keys that have been pressed.  For
 * instance, pressing shift and 4 together will generate the button event
 * "shift-4", but it will generate the keystroke "$".
 *
 * If a key is held down, keyrepeat will cause the same keystroke event to be
 * generated repeatedly.  This is different from the corresponding down event,
 * which will only be generated once, followed by a number of button repeat
 * events.
 *
 * This event is generated with a single wstring parameter, which is a one-
 * character string that contains the keystroke generated.  If this event
 * string is empty, no event is generated.
 *
 * See also set_button_down_event().
 */ 2 4 this 3 557  15 keystroke_event 1 558  
10 0 0 6 11 558 0 0 110 /**
 * Returns the keystroke_event that has been set on this ButtonThrower.  See
 * set_keystroke_event().
 */ 1 4 this 3 555  
11 0 0 4 12 559 0 0 804 /**
 * Specifies the event that is generated (if any) for each IME candidate
 * string event received.  Events of this nature are received only when the
 * user is entering data using a Microsoft Input Method Editor, typically used
 * for Asian languages such as Japanese or Korean.
 *
 * If you are designing a typing user interface, you should track this event
 * to support the use of the IME.  In response to this event, you should
 * display the candidate string in the entry box, with the appropriate
 * sections highlighted, so the user can scroll through the available choices.
 *
 * This event is generated with four parameters, in order: the candidate
 * string, the character at which to start the highlight, the character at
 * which to end the highlight, and the current cursor position.
 */ 2 4 this 3 557  15 candidate_event 1 558  
12 0 0 6 13 558 0 0 110 /**
 * Returns the candidate_event that has been set on this ButtonThrower.  See
 * set_candidate_event().
 */ 1 4 this 3 555  
13 0 0 4 14 559 0 0 108 /**
 * Specifies the event that is generated (if any) each time the mouse is moved
 * within the window.
 */ 2 4 this 3 557  10 move_event 1 558  
14 0 0 6 15 558 0 0 100 /**
 * Returns the move_event that has been set on this ButtonThrower.  See
 * set_move_event().
 */ 1 4 this 3 555  
15 0 0 4 16 559 0 0 192 /**
 * Like set_button_down_event, but uses the raw, untransformed scan key from
 * the operating system.  This uses buttons that are independent of the user's
 * selected keyboard layout.
 */ 2 4 this 3 557  21 raw_button_down_event 1 558  
16 0 0 6 17 558 0 0 121 /**
 * Returns the raw_button_down_event that has been set on this ButtonThrower.
 * See set_raw_button_down_event().
 */ 1 4 this 3 555  
17 0 0 4 18 559 0 0 141 /**
 * Specifies the generic event that is generated (if any) each time a key or
 * button is released.  See set_raw_button_down_event().
 */ 2 4 this 3 557  19 raw_button_up_event 1 558  
18 0 0 6 19 558 0 0 117 /**
 * Returns the raw_button_up_event that has been set on this ButtonThrower.
 * See set_raw_button_up_event().
 */ 1 4 this 3 555  
19 0 0 4 31 559 0 0 229 /**
 * Sets the prefix which is prepended to all specific event names (that is,
 * event names generated from the button name itself, as opposed to the
 * generic event names like set_button_down_event) thrown by this object.
 */ 2 4 this 3 557  6 prefix 1 558  
20 0 0 6 32 558 0 0 92 /**
 * Returns the prefix that has been set on this ButtonThrower.  See
 * set_prefix().
 */ 1 4 this 3 555  
21 0 0 4 33 559 0 0 307 /**
 * Sets the flag that indicates whether specific events (events prefixed by
 * set_prefix, and based on the event name) should be generated at all.  This
 * is true by default, but may be disabled if you are only interested in the
 * generic events (for instance, events like set_button_down_event).
 */ 2 4 this 3 557  13 specific_flag 1 529  
22 0 0 6 34 529 0 0 116 /**
 * Returns the flag that indicates whether specific events should be
 * generated.  See set_specific_flag().
 */ 1 4 this 3 555  
23 0 0 4 38 559 0 0 400 /**
 * Sets the flag that indicates whether the time of the button event should be
 * passed as a parameter or not.  When this is true, an additional parameter
 * is generated on each event (before all the parameters named by
 * add_parameter) that consists of a single double value, and reflects the
 * time the button was pressed or released, as a value from
 * ClockObject::get_global_clock().
 */ 2 4 this 3 557  9 time_flag 1 529  
24 0 0 6 39 529 0 0 115 /**
 * Returns the flag that indicates whether the time of the button event should
 * be passed as a parameter.
 */ 1 4 this 3 555  
25 0 0 4 41 559 0 0 137 /**
 * Adds the indicated parameter to the list of parameters that will be passed
 * with each event generated by this ButtonThrower.
 */ 2 4 this 3 557  3 obj 1 560  
26 0 0 6 42 545 0 0 155 /**
 * Returns the number of parameters that have been added to the list of
 * parameters to be passed with each event generated by this ButtonThrower.
 */ 1 4 this 3 555  
27 0 0 7 43 562 0 0 141 /**
 * Returns the nth parameter that has been added to the list of parameters
 * passed with each event generated by this ButtonThrower.
 */ 2 4 this 3 555  1 n 1 545  
28 0 0 6 47 563 0 0 463 /**
 * Returns the set of ModifierButtons that the ButtonThrower will consider
 * important enough to prepend the event name with.  Normally, this set will
 * be empty, and the ButtonThrower will therefore ignore all ModifierButtons
 * attached to the key events, but if one or more buttons have been added to
 * this set, and those modifier buttons are set on the button event, then the
 * event name will be prepended with the names of the modifier buttons.
 */ 1 4 this 3 555  
29 0 0 4 48 559 0 0 653 /**
 * Changes the set of ModifierButtons that the ButtonThrower will consider
 * important enough to prepend the event name with.  Normally, this set will
 * be empty, and the ButtonThrower will therefore ignore all ModifierButtons
 * attached to the key events, but if one or more buttons have been added to
 * this set, then the event name will be prepended with the names of the
 * modifier buttons.
 *
 * It is recommended that you change this setting by first calling
 * get_modifier_buttons(), making adjustments, and passing the new value to
 * set_modifier_buttons().  This way the current state of the modifier buttons
 * will not be lost.
 */ 2 4 this 3 557  4 mods 1 563  
30 0 0 4 52 559 0 0 293 /**
 * Sets the flag that indicates whether the ButtonThrower will only process
 * events for the explicitly named buttons or not.  Normally this is false,
 * meaning all buttons are processed; set it true to indicate that only some
 * buttons should be processed.  See add_throw_button().
 */ 2 4 this 3 557  4 flag 1 529  
31 0 0 6 53 529 0 0 173 /**
 * Returns the flag that indicates whether the ButtonThrower will only process
 * events for the explicitly named buttons or not.  See
 * set_throw_buttons_active().
 */ 1 4 this 3 555  
32 0 0 6 55 529 0 0 663 /**
 * Adds a new button to the set of buttons that the ButtonThrower explicitly
 * processes.
 *
 * If set_throw_buttons_active is false (which is the default), the
 * ButtonThrower will process all buttons.  Otherwise, the ButtonThrower will
 * only process events for the button(s) explicitly named by this function;
 * buttons not on the list will be ignored by this object and passed on
 * downstream to the child node(s) in the data graph.  A button that *is* on
 * the list will be processed by the ButtonThrower and not passed on to the
 * child node(s).
 *
 * The return value is true if the button is added, or false if it was already
 * in the set.
 */ 3 4 this 3 557  4 mods 1 563  6 button 1 564  
33 0 0 6 56 529 0 0 231 /**
 * Removes the indicated button from the set of buttons that the ButtonThrower
 * explicitly processes.  See add_throw_button().
 *
 * The return value is true if the button is removed, or false if it was not
 * on the set.
 */ 3 4 this 3 557  4 mods 1 563  6 button 1 564  
34 0 0 6 57 529 0 0 334 /**
 * Returns true if the indicated button, in conjunction with any nonspecified
 * modifier buttons, is on the set of buttons that will be processed by the
 * ButtonThrower.  That is to say, returns true if this button was ever passed
 * as the second parameter add_throw_button(), regardless of what the first
 * parameter was.
 */ 2 4 this 3 555  6 button 1 564  
35 0 0 6 57 529 0 0 162 /**
 * Returns true if the indicated button is on the set of buttons that will be
 * processed by the ButtonThrower, false otherwise.  See add_throw_button().
 */ 3 4 this 3 555  4 mods 1 563  6 button 1 564  
36 0 0 4 58 559 0 0 105 /**
 * Empties the set of buttons that were added via add_throw_button().  See
 * add_throw_button().
 */ 1 4 this 3 557  
37 0 0 7 59 568 0 0 0 0 
38 0 0 15 65 571 0 0 0 1 6 param0 0 569  
39 0 0 4 61 559 0 0 289 /**
 * Indicates that the indicated button must be in the required state (either
 * up or down) in order for this particular MouseInterfaceNode to do anything.
 * For instance, this may be called to make a Trackball object respect mouse
 * input only when the control key is held down.
 */ 3 4 this 3 571  6 button 1 564  7 is_down 1 529  
40 0 0 4 62 559 0 0 105 /**
 * Removes any requirement on the indicated button set by an earlier call to
 * require_button().
 */ 2 4 this 3 571  6 button 1 564  
41 0 0 4 63 559 0 0 93 /**
 * Removes all requirements on buttons set by an earlier call to
 * require_button().
 */ 1 4 this 3 571  
42 0 0 7 64 568 0 0 0 0 
43 0 0 15 67 574 0 0 0 1 6 param0 0 572  
44 0 0 7 67 574 0 0 10 /**
 *
 */ 1 4 name 5 558  
45 0 0 4 68 559 0 0 144 /**
 * Sets the speed of full forward motion, when the mouse is at the very top of
 * the window.  This is in units (e.g.  feet) per second.
 */ 2 4 this 3 574  5 speed 1 543  
46 0 0 6 69 543 0 0 147 /**
 * Returns the speed of full forward motion, when the mouse is at the very top
 * of the window.  This is in units (e.g.  feet) per second.
 */ 1 4 this 3 572  
47 0 0 4 70 559 0 0 147 /**
 * Sets the speed of full reverse motion, when the mouse is at the very bottom
 * of the window.  This is in units (e.g.  feet) per second.
 */ 2 4 this 3 574  5 speed 1 543  
48 0 0 6 71 543 0 0 150 /**
 * Returns the speed of full reverse motion, when the mouse is at the very
 * bottom of the window.  This is in units (e.g.  feet) per second.
 */ 1 4 this 3 572  
49 0 0 4 72 559 0 0 161 /**
 * Sets the maximum rate at which the user can rotate left or right, when the
 * mouse is at the very edge of the window.  This is in degrees per second.
 */ 2 4 this 3 574  5 speed 1 543  
50 0 0 6 73 543 0 0 167 /**
 * Returns the maximum rate at which the user can rotate left or right, when
 * the mouse is at the very edge of the window.  This is in degrees per
 * second.
 */ 1 4 this 3 572  
51 0 0 4 74 559 0 0 300 /**
 * Sets the size of the horizontal bar in the center of the screen that
 * represents the "dead zone" of vertical motion: the region in which the
 * mouse does not report vertical motion.  This is in a fraction of the window
 * height, so 0.5 will set a dead zone as large as half the screen.
 */ 2 4 this 3 574  4 zone 1 543  
52 0 0 6 75 543 0 0 303 /**
 * Returns the size of the horizontal bar in the center of the screen that
 * represents the "dead zone" of vertical motion: the region in which the
 * mouse does not report vertical motion.  This is in a fraction of the window
 * height, so 0.5 will set a dead zone as large as half the screen.
 */ 1 4 this 3 572  
53 0 0 4 76 559 0 0 301 /**
 * Sets the size of the vertical bar in the center of the screen that
 * represents the "dead zone" of horizontal motion: the region in which the
 * mouse does not report horizontal motion.  This is in a fraction of the
 * window width, so 0.5 will set a dead zone as large as half the screen.
 */ 2 4 this 3 574  4 zone 1 543  
54 0 0 6 77 543 0 0 304 /**
 * Returns the size of the vertical bar in the center of the screen that
 * represents the "dead zone" of horizontal motion: the region in which the
 * mouse does not report horizontal motion.  This is in a fraction of the
 * window width, so 0.5 will set a dead zone as large as half the screen.
 */ 1 4 this 3 572  
55 0 0 4 78 559 0 0 173 /**
 * Sets the amount of time, in seconds, it takes between the time an up or
 * down arrow key is pressed and the time it registers full forward or
 * backward motion.
 */ 2 4 this 3 574  12 ramp_up_time 1 543  
56 0 0 6 79 543 0 0 176 /**
 * Returns the amount of time, in seconds, it takes between the time an up or
 * down arrow key is pressed and the time it registers full forward or
 * backward motion.
 */ 1 4 this 3 572  
57 0 0 4 80 559 0 0 149 /**
 * Sets the amount of time, in seconds, it takes between the time an up or
 * down arrow key is released and the time it registers no motion.
 */ 2 4 this 3 574  14 ramp_down_time 1 543  
58 0 0 6 81 543 0 0 152 /**
 * Returns the amount of time, in seconds, it takes between the time an up or
 * down arrow key is released and the time it registers no motion.
 */ 1 4 this 3 572  
59 0 0 4 82 559 0 0 154 /**
 * Sets the amount of time, in seconds, it takes between the time a left or
 * right arrow key is pressed and the time it registers full rotation.
 */ 2 4 this 3 574  12 ramp_up_time 1 543  
60 0 0 6 83 543 0 0 157 /**
 * Returns the amount of time, in seconds, it takes between the time a left or
 * right arrow key is pressed and the time it registers full rotation.
 */ 1 4 this 3 572  
61 0 0 4 84 559 0 0 151 /**
 * Sets the amount of time, in seconds, it takes between the time a left or
 * right arrow key is released and the time it registers no motion.
 */ 2 4 this 3 574  14 ramp_down_time 1 543  
62 0 0 6 85 543 0 0 154 /**
 * Returns the amount of time, in seconds, it takes between the time a left or
 * right arrow key is released and the time it registers no motion.
 */ 1 4 this 3 572  
63 0 0 6 86 543 0 0 64 /**
 * Returns the speed of the previous update in units/sec
 */ 1 4 this 3 572  
64 0 0 6 87 543 0 0 68 /**
 * Returns the rot_speed of the previous update in units/sec
 */ 1 4 this 3 572  
65 0 0 4 88 559 0 0 108 /**
 * Reinitializes the driver to the origin and resets any knowledge about
 * buttons being held down.
 */ 1 4 this 3 574  
66 0 0 6 89 575 0 0 41 /**
 * Returns the driver's position.
 */ 1 4 this 3 572  
67 0 0 6 90 543 0 0 0 1 4 this 3 572  
68 0 0 6 91 543 0 0 0 1 4 this 3 572  
69 0 0 6 92 543 0 0 0 1 4 this 3 572  
70 0 0 4 93 559 0 0 47 /**
 * Directly sets the driver's position.
 */ 2 4 this 3 574  3 vec 1 579  
71 0 0 4 93 559 0 0 0 4 4 this 3 574  1 x 1 543  1 y 1 543  1 z 1 543  
72 0 0 4 94 559 0 0 0 2 4 this 3 574  1 x 1 543  
73 0 0 4 95 559 0 0 0 2 4 this 3 574  1 y 1 543  
74 0 0 4 96 559 0 0 0 2 4 this 3 574  1 z 1 543  
75 0 0 6 97 579 0 0 44 /**
 * Returns the driver's orientation.
 */ 1 4 this 3 572  
76 0 0 6 98 543 0 0 0 1 4 this 3 572  
77 0 0 6 99 543 0 0 0 1 4 this 3 572  
78 0 0 6 100 543 0 0 0 1 4 this 3 572  
79 0 0 4 101 559 0 0 50 /**
 * Directly sets the driver's orientation.
 */ 2 4 this 3 574  3 hpr 1 579  
80 0 0 4 101 559 0 0 0 4 4 this 3 574  1 h 1 543  1 p 1 543  1 r 1 543  
81 0 0 4 102 559 0 0 0 2 4 this 3 574  1 h 1 543  
82 0 0 4 103 559 0 0 0 2 4 this 3 574  1 p 1 543  
83 0 0 4 104 559 0 0 0 2 4 this 3 574  1 r 1 543  
84 0 0 4 105 559 0 0 86 /**
 * This function is no longer used and does nothing.  It will be removed soon.
 */ 2 4 this 3 574  10 force_roll 1 543  
85 0 0 4 106 559 0 0 411 /**
 * Changes the state of the ignore_mouse flag.  If this flag is true, the
 * DriveInterface will ignore mouse down button events (but still recognize
 * mouse up button events); the user will not be able to start the
 * DriveInterface going again if it is stopped, but if the user is currently
 * holding down a mouse button it will not stop immediately until the user
 * eventually releases the button.
 */ 2 4 this 3 574  12 ignore_mouse 1 529  
86 0 0 6 107 529 0 0 92 /**
 * Returns the current setting of the ignore_mouse flag.  See
 * set_ignore_mouse().
 */ 1 4 this 3 572  
87 0 0 4 108 559 0 0 155 /**
 * Changes the state of the force_mouse flag.  If this flag is true, the mouse
 * button need not be held down in order to drive the avatar around.
 */ 2 4 this 3 574  11 force_mouse 1 529  
88 0 0 6 109 529 0 0 90 /**
 * Returns the current setting of the force_mouse flag.  See
 * set_force_mouse().
 */ 1 4 this 3 572  
89 0 0 4 110 559 0 0 335 /**
 * If stop_this_frame is true, the next time the frame is computed no motion
 * will be allowed, and then the flag is reset to false.  This can be used to
 * prevent too much movement when we know a long time has artificially
 * elapsed, for instance when we take a screenshot, without munging the clock
 * for everything else.
 */ 2 4 this 3 574  15 stop_this_frame 1 529  
90 0 0 6 111 529 0 0 98 /**
 * Returns the current setting of the stop_this_frame flag.  See
 * set_stop_this_frame().
 */ 1 4 this 3 572  
91 0 0 4 112 559 0 0 64 /**
 * Stores the indicated transform in the DriveInterface.
 */ 2 4 this 3 574  3 mat 1 583  
92 0 0 6 113 583 0 0 41 /**
 * Returns the current transform.
 */ 1 4 this 3 574  
93 0 0 4 114 559 0 0 287 /**
 * This is a special kludge for DriveInterface to allow us to avoid the one-
 * frame latency after a collision.  It forces an immediate partial data flow
 * for all data graph nodes below this node, causing all data nodes that
 * depend on this matrix to be updated immediately.
 */ 1 4 this 3 574  
94 0 0 7 115 568 0 0 0 0 
95 0 0 15 117 589 0 0 0 1 6 param0 0 587  
96 0 0 7 117 589 0 0 10 /**
 *
 */ 1 4 name 1 558  
97 0 0 6 118 543 0 0 135 /**
 * Retrieves the x coordinate of the left edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */ 1 4 this 3 587  
98 0 0 6 119 543 0 0 136 /**
 * Retrieves the x coordinate of the right edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */ 1 4 this 3 587  
99 0 0 6 120 543 0 0 137 /**
 * Retrieves the y coordinate of the bottom edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */ 1 4 this 3 587  
100 0 0 6 121 543 0 0 134 /**
 * Retrieves the y coordinate of the top edge of the rectangle within the
 * window.  This number will be in the range [0..1].
 */ 1 4 this 3 587  
101 0 0 4 122 559 0 0 288 /**
 * Changes the region of the window in which the mouse is considered to be
 * active.  The parameters are identical to those for a DisplayRegion: they
 * range from 0 to 1, where 0,0 is the lower left corner and 1,1 is the upper
 * right; (0, 1, 0, 1) represents the whole window.
 */ 5 4 this 3 589  1 l 1 543  1 r 1 543  1 b 1 543  1 t 1 543  
102 0 0 7 123 568 0 0 0 0 
103 0 0 15 131 547 381 0 0 1 6 param0 0 590  
104 0 0 7 131 547 381 0 10 /**
 *
 */ 2 4 name 1 558  5 frame 1 592  
105 0 0 7 131 547 381 0 10 /**
 *
 */ 5 4 name 1 558  4 left 1 543  5 right 1 543  6 bottom 1 543  3 top 1 543  
106 0 0 4 132 559 0 0 10 /**
 *
 */ 2 4 this 3 547  5 frame 1 592  
107 0 0 4 132 559 0 0 10 /**
 *
 */ 5 4 this 3 547  4 left 1 543  5 right 1 543  6 bottom 1 543  3 top 1 543  
108 0 0 6 133 592 0 0 10 /**
 *
 */ 1 4 this 3 590  
109 0 0 6 134 543 0 0 54 /**
 * Returns the area of the rectangular region.
 */ 1 4 this 3 590  
110 0 0 4 135 559 0 0 366 /**
 * Changes the sorting order of this particular region.  The sorting order is
 * used to resolve conflicts in the case of overlapping region; the region
 * with the highest sort value will be preferred, and between regions of the
 * same sort value, the smallest region will be preferred.  The default
 * sorting order, if none is explicitly specified, is 0.
 */ 2 4 this 3 547  4 sort 1 545  
111 0 0 6 136 545 0 0 77 /**
 * Returns the current sorting order of this region.  See set_sort().
 */ 1 4 this 3 590  
112 0 0 4 137 559 0 0 237 /**
 * Sets whether the region is active or not.  If it is not active, the
 * MouseWatcher will never consider the mouse to be over the region.  The
 * region might still receive keypress events if its set_keyboard() flag is
 * true.
 */ 2 4 this 3 547  6 active 1 529  
113 0 0 6 138 529 0 0 74 /**
 * Returns whether the region is active or not.  See set_active().
 */ 1 4 this 3 590  
114 0 0 4 139 559 0 0 301 /**
 * Sets whether the region is interested in global keyboard events.  If this
 * is true, then any keyboard button events will be passed to press() and
 * release() regardless of the position of the mouse onscreen; otherwise,
 * these events will only be passed if the mouse is over the region.
 */ 2 4 this 3 547  8 keyboard 1 529  
115 0 0 6 140 529 0 0 101 /**
 * Returns whether the region is interested in global keyboard events; see
 * set_keyboard().
 */ 1 4 this 3 590  
116 0 0 4 142 559 0 0 394 /**
 * Sets which events are suppressed when the mouse is over the region.  This
 * is the union of zero or more various SF_* values.  Normally, this is 0,
 * indicating that no events are suppressed.
 *
 * If you set this to a non-zero value, for instance SF_mouse_position, then
 * the mouse position will not be sent along the data graph when the mouse is
 * over this particular region.
 */ 2 4 this 3 547  14 suppress_flags 1 545  
117 0 0 6 143 545 0 0 73 /**
 * Returns the current suppress_flags.  See set_suppress_flags().
 */ 1 4 this 3 590  
118 0 0 4 144 559 0 0 10 /**
 *
 */ 2 4 this 3 590  3 out 1 593  
119 0 0 4 145 559 0 0 10 /**
 *
 */ 3 4 this 3 590  3 out 1 593  12 indent_level 5 545  
120 0 0 7 158 568 0 0 0 0 
121 0 0 7 126 595 0 0 0 1 4 this 3 547  
122 0 0 6 129 596 0 0 0 1 4 this 3 547  
123 0 0 4 162 559 0 0 150 /**
 * Adds the indicated region to the set of regions in the group.  It is no
 * longer an error to call this for the same region more than once.
 */ 2 4 this 3 597  6 region 1 547  
124 0 0 6 163 529 0 0 115 /**
 * Returns true if the indicated region has already been added to the
 * MouseWatcherBase, false otherwise.
 */ 2 4 this 3 598  6 region 1 547  
125 0 0 6 164 529 0 0 152 /**
 * Removes the indicated region from the group.  Returns true if it was
 * successfully removed, or false if it wasn't there in the first place.
 */ 2 4 this 3 597  6 region 1 547  
126 0 0 7 165 547 381 0 170 /**
 * Returns a pointer to the first region found with the indicated name.  If
 * multiple regions share the same name, the one that is returned is
 * indeterminate.
 */ 2 4 this 3 598  4 name 1 558  
127 0 0 4 166 559 0 0 50 /**
 * Removes all the regions from the group.
 */ 1 4 this 3 597  
128 0 0 4 167 559 0 0 66 /**
 * Sorts all the regions in this group into pointer order.
 */ 1 4 this 3 597  
129 0 0 6 168 529 0 0 78 /**
 * Returns true if the group has already been sorted, false otherwise.
 */ 1 4 this 3 598  
130 0 0 6 170 600 0 0 54 /**
 * Returns the number of regions in the group.
 */ 1 4 this 3 598  
131 0 0 7 171 547 381 0 209 /**
 * Returns the nth region of the group; returns NULL if there is no nth
 * region.  Note that this is not thread-safe; another thread might have
 * removed the nth region before you called this method.
 */ 2 4 this 3 598  1 n 1 600  
132 0 0 4 175 559 0 0 10 /**
 *
 */ 2 4 this 3 598  3 out 1 593  
133 0 0 4 176 559 0 0 10 /**
 *
 */ 3 4 this 3 598  3 out 1 593  12 indent_level 5 545  
134 0 0 4 177 559 0 0 179 /**
 * Enables the visualization of all of the regions handled by this
 * MouseWatcherBase.  The supplied NodePath should be the root of the 2-d
 * scene graph for the window.
 */ 4 4 this 3 597  8 render2d 1 602  8 bin_name 1 558  10 draw_order 1 545  
135 0 0 4 178 559 0 0 114 /**
 * Specifies the color used to draw the region rectangles for the regions
 * visualized by show_regions().
 */ 2 4 this 3 597  5 color 1 605  
136 0 0 4 179 559 0 0 80 /**
 * Stops the visualization created by a previous call to show_regions().
 */ 1 4 this 3 597  
137 0 0 4 180 559 0 0 65 /**
 * Refreshes the visualization created by show_regions().
 */ 1 4 this 3 597  
138 0 0 7 181 568 0 0 0 0 
139 0 0 7 188 568 0 0 0 0 
140 0 0 6 183 597 0 0 0 1 4 this 3 609  
141 0 0 7 186 610 0 0 0 1 4 this 3 609  
142 0 0 7 195 611 410 0 10 /**
 *
 */ 1 4 name 5 558  
143 0 0 6 197 529 0 0 152 /**
 * Removes the indicated region from the group.  Returns true if it was
 * successfully removed, or false if it wasn't there in the first place.
 */ 2 4 this 3 611  6 region 1 547  
144 0 0 6 198 529 0 0 113 /**
 * Returns true if the mouse is anywhere within the window, false otherwise.
 * Also see is_mouse_open().
 */ 1 4 this 3 612  
145 0 0 6 199 529 0 0 212 /**
 * Returns true if the mouse is within the window and not over some particular
 * MouseWatcherRegion that is marked to suppress mouse events; that is, that
 * the mouse is in open space within the window.
 */ 1 4 this 3 612  
146 0 0 6 200 614 0 0 146 /**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current position of the mouse within the window.
 */ 1 4 this 3 612  
147 0 0 6 201 543 0 0 148 /**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current X position of the mouse within the window.
 */ 1 4 this 3 612  
148 0 0 6 202 543 0 0 148 /**
 * It is only valid to call this if has_mouse() returns true.  If so, this
 * returns the current Y position of the mouse within the window.
 */ 1 4 this 3 612  
149 0 0 4 203 559 0 0 567 /**
 * Sets the frame of the MouseWatcher.  This determines the coordinate space
 * in which the MouseWatcherRegions should be expected to live.  Normally,
 * this is left at -1, 1, -1, 1, which is the default setting, and matches the
 * mouse coordinate range.
 *
 * Whatever values you specify here indicate the shape of the full screen, and
 * the MouseWatcherRegions will be given in coordinate space matching it.  For
 * instance, if you specify (0, 1, 0, 1), then a MouseWatcherRegion with the
 * frame (0, 1, 0, .5) will cover the lower half of the screen.
 */ 2 4 this 3 611  5 frame 1 592  
150 0 0 4 203 559 0 0 117 /**
 * Sets the frame of the MouseWatcher.  See the next flavor of this method for
 * a more verbose explanation.
 */ 5 4 this 3 611  4 left 1 543  5 right 1 543  6 bottom 1 543  3 top 1 543  
151 0 0 6 204 592 0 0 67 /**
 * Returns the frame of the MouseWatcher.  See set_frame().
 */ 1 4 this 3 612  
152 0 0 6 205 529 0 0 85 /**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */ 1 4 this 3 612  
153 0 0 6 205 529 0 0 85 /**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */ 2 4 this 3 612  3 pos 1 614  
154 0 0 6 205 529 0 0 85 /**
 * Returns true if the mouse is over any rectangular region, false otherwise.
 */ 3 4 this 3 612  1 x 1 543  1 y 1 543  
155 0 0 7 206 547 381 0 103 /**
 * Returns the smallest region the mouse is currently over, or NULL if it is
 * over no region.
 */ 1 4 this 3 612  
156 0 0 7 206 547 381 0 233 /**
 * Returns the preferred region the mouse is over.  In the case of overlapping
 * regions, the region with the largest sort order is preferred; if two
 * regions have the same sort order, then the smaller region is preferred.
 */ 2 4 this 3 612  3 pos 1 614  
157 0 0 7 206 547 381 0 103 /**
 * Returns the smallest region the indicated point is over, or NULL if it is
 * over no region.
 */ 3 4 this 3 612  1 x 1 543  1 y 1 543  
158 0 0 6 207 529 0 0 97 /**
 * Returns true if the indicated button is currently being held down, false
 * otherwise.
 */ 2 4 this 3 612  6 button 1 618  
159 0 0 4 208 559 0 0 432 /**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is depressed.  This is a string that may contain any of the
 * following:
 *
 * %r  - the name of the region the mouse is over %b  - the name of the button
 * pressed.
 *
 * The event name will be based on the in_pattern string specified here, with
 * all occurrences of the above strings replaced with the corresponding
 * values.
 */ 2 4 this 3 611  7 pattern 1 558  
160 0 0 6 209 558 0 0 137 /**
 * Returns the string that indicates how event names are generated when a
 * button is depressed.  See set_button_down_pattern().
 */ 1 4 this 3 612  
161 0 0 4 210 559 0 0 145 /**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is released.  See set_button_down_pattern().
 */ 2 4 this 3 611  7 pattern 1 558  
162 0 0 6 211 558 0 0 136 /**
 * Returns the string that indicates how event names are generated when a
 * button is released.  See set_button_down_pattern().
 */ 1 4 this 3 612  
163 0 0 4 212 559 0 0 477 /**
 * Sets the pattern string that indicates how the event names are generated
 * when a button is continuously held and generates keyrepeat "down" events.
 * This is a string that may contain any of the following:
 *
 * %r  - the name of the region the mouse is over %b  - the name of the button
 * pressed.
 *
 * The event name will be based on the in_pattern string specified here, with
 * all occurrences of the above strings replaced with the corresponding
 * values.
 */ 2 4 this 3 611  7 pattern 1 558  
164 0 0 6 213 558 0 0 197 /**
 * Returns the string that indicates how event names are names are generated
 * when a button is continuously held and generates keyrepeat "down" events.
 * See set_button_repeat_pattern().
 */ 1 4 this 3 612  
165 0 0 4 214 559 0 0 289 /**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse enters a region.  This is different from within_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */ 2 4 this 3 611  7 pattern 1 558  
166 0 0 6 215 558 0 0 280 /**
 * Returns the string that indicates how event names are generated when the
 * mouse enters a region.  This is different from within_pattern, in that a
 * mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */ 1 4 this 3 612  
167 0 0 4 216 559 0 0 290 /**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse leaves a region.  This is different from without_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */ 2 4 this 3 611  7 pattern 1 558  
168 0 0 6 217 558 0 0 281 /**
 * Returns the string that indicates how event names are generated when the
 * mouse leaves a region.  This is different from without_pattern, in that a
 * mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */ 1 4 this 3 612  
169 0 0 4 218 559 0 0 294 /**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse wanders over a region.  This is different from
 * enter_pattern, in that a mouse is only "entered" in the topmost region at a
 * given time, while it might be "within" multiple nested regions.
 */ 2 4 this 3 611  7 pattern 1 558  
170 0 0 6 219 558 0 0 285 /**
 * Returns the string that indicates how event names are generated when the
 * mouse wanders over a region.  This is different from enter_pattern, in that
 * a mouse is only "entered" in the topmost region at a given time, while it
 * might be "within" multiple nested regions.
 */ 1 4 this 3 612  
171 0 0 4 220 559 0 0 296 /**
 * Sets the pattern string that indicates how the event names are generated
 * when the mouse wanders out of a region.  This is different from
 * leave_pattern, in that a mouse is only "entered" in the topmost region at a
 * given time, while it might be "within" multiple nested regions.
 */ 2 4 this 3 611  7 pattern 1 558  
172 0 0 6 221 558 0 0 287 /**
 * Returns the string that indicates how event names are generated when the
 * mouse wanders out of a region.  This is different from leave_pattern, in
 * that a mouse is only "entered" in the topmost region at a given time, while
 * it might be "within" multiple nested regions.
 */ 1 4 this 3 612  
173 0 0 4 222 559 0 0 279 /**
 * Sets the node that will be transformed each frame by the mouse's
 * coordinates.  It will also be hidden when the mouse goes outside the
 * window.  This can be used to implement a software mouse pointer for when a
 * hardware (or system) mouse pointer is unavailable.
 */ 2 4 this 3 611  4 node 1 619  
174 0 0 6 223 529 0 0 130 /**
 * Returns true if a software mouse pointer has been setup via set_geometry(),
 * or false otherwise.  See set_geometry().
 */ 1 4 this 3 612  
175 0 0 7 224 619 0 0 152 /**
 * Returns the node that has been set as the software mouse pointer, or NULL
 * if no node has been set.  See has_geometry() and set_geometry().
 */ 1 4 this 3 612  
176 0 0 4 225 559 0 0 74 /**
 * Stops the use of the software cursor set up via set_geometry().
 */ 1 4 this 3 611  
177 0 0 4 226 559 0 0 150 /**
 * As an optimization for the C++ Gui, an extra handler can be registered with
 * a mouseWatcher so that events can be dealt with much sooner.
 */ 2 4 this 3 611  2 eh 1 621  
178 0 0 6 227 621 0 0 150 /**
 * As an optimization for the C++ Gui, an extra handler can be registered with
 * a mouseWatcher so that events can be dealt with much sooner.
 */ 1 4 this 3 612  
179 0 0 4 228 559 0 0 125 /**
 * Sets the buttons that should be monitored as modifier buttons for
 * generating events to the MouseWatcherRegions.
 */ 2 4 this 3 611  4 mods 1 563  
180 0 0 7 229 623 0 0 118 /**
 * Returns the set of buttons that are being monitored as modifier buttons, as
 * well as their current state.
 */ 1 4 this 3 612  
181 0 0 4 230 559 0 0 402 /**
 * Constrains the MouseWatcher to watching the mouse within a particular
 * indicated region of the screen.  DataNodes parented under the MouseWatcher
 * will observe the mouse and keyboard events only when the mouse is within
 * the indicated region, and the observed range will be from -1 .. 1 across
 * the region.
 *
 * Do not delete the DisplayRegion while it is owned by the MouseWatcher.
 */ 2 4 this 3 611  2 dr 1 624  
182 0 0 4 231 559 0 0 144 /**
 * Removes the display region constraint from the MouseWatcher, and restores
 * it to the default behavior of watching the whole window.
 */ 1 4 this 3 611  
183 0 0 7 232 624 0 0 134 /**
 * Returns the display region the MouseWatcher is constrained to by
 * set_display_region(), or NULL if it is not constrained.
 */ 1 4 this 3 612  
184 0 0 6 233 529 0 0 245 /**
 * Returns true if the MouseWatcher has been constrained to a particular
 * region of the screen via set_display_region(), or false otherwise.  If this
 * returns true, get_display_region() may be used to return the particular
 * region.
 */ 1 4 this 3 612  
185 0 0 6 234 529 0 0 496 /**
 * Adds the indicated group of regions to the set of regions the MouseWatcher
 * will monitor each frame.
 *
 * Since the MouseWatcher itself inherits from MouseWatcherBase, this
 * operation is normally not necessary--you can simply add the Regions you
 * care about one at a time.  Adding a complete group is useful when you may
 * want to explicitly remove the regions as a group later.
 *
 * Returns true if the group was successfully added, or false if it was
 * already on the list.
 */ 2 4 this 3 611  5 group 1 609  
186 0 0 6 235 529 0 0 214 /**
 * Removes the indicated group from the set of extra groups associated with
 * the MouseWatcher.  Returns true if successful, or false if the group was
 * already removed or was never added via add_group().
 */ 2 4 this 3 611  5 group 1 609  
187 0 0 6 236 529 0 0 362 /**
 * Atomically removes old_group from the MouseWatcher, and replaces it with
 * new_group.  Presumably old_group and new_group might have some regions in
 * common; these are handled properly.
 *
 * If old_group is not already present, simply adds new_group and returns
 * false.  Otherwise, removes old_group and adds new_group, and then returns
 * true.
 */ 3 4 this 3 611  9 old_group 1 609  9 new_group 1 609  
188 0 0 6 237 545 0 0 94 /**
 * Returns the number of separate groups added to the MouseWatcher via
 * add_group().
 */ 1 4 this 3 612  
189 0 0 7 238 609 404 0 75 /**
 * Returns the nth group added to the MouseWatcher via add_group().
 */ 2 4 this 3 612  1 n 1 545  
190 0 0 4 240 559 0 0 480 /**
 * Sets an inactivity timeout on the mouse activity.  When this timeout (in
 * seconds) is exceeded with no keyboard or mouse activity, all currently-held
 * buttons are automatically released.  This is intended to help protect
 * against people who inadvertently (or intentionally) leave a keyboard key
 * stuck down and then wander away from the keyboard.
 *
 * Also, when this timeout expires, the event specified by
 * set_inactivity_timeout_event() will be generated.
 */ 2 4 this 3 611  7 timeout 1 626  
191 0 0 6 241 529 0 0 79 /**
 * Returns true if an inactivity timeout has been set, false otherwise.
 */ 1 4 this 3 612  
192 0 0 6 242 626 0 0 135 /**
 * Returns the inactivity timeout that has been set.  It is an error to call
 * this if has_inactivity_timeout() returns false.
 */ 1 4 this 3 612  
193 0 0 4 243 559 0 0 141 /**
 * Removes the inactivity timeout and restores the MouseWatcher to its default
 * behavior of allowing a key to be held indefinitely.
 */ 1 4 this 3 611  
194 0 0 4 244 559 0 0 139 /**
 * Specifies the event string that will be generated when the inactivity
 * timeout counter expires.  See set_inactivity_timeout().
 */ 2 4 this 3 611  5 event 1 558  
195 0 0 6 245 558 0 0 137 /**
 * Returns the event string that will be generated when the inactivity timeout
 * counter expires.  See set_inactivity_timeout().
 */ 1 4 this 3 612  
196 0 0 7 246 629 0 0 395 /**
 * Obtain the mouse trail log.  This is a PointerEventList.  Does not make a
 * copy, therefore, this PointerEventList will be updated each time
 * process_events gets called.
 *
 * To use trail logging, you need to enable the generation of pointer events
 * in the GraphicsWindowInputDevice and set the trail log duration in the
 * MouseWatcher.  Otherwise, the trail log will be empty.
 */ 1 4 this 3 612  
197 0 0 6 247 600 0 0 174 /**
 * This counter indicates how many events were added to the trail log this
 * frame.  The trail log is updated once per frame, during the process_events
 * operation.
 */ 1 4 this 3 612  
198 0 0 4 248 559 0 0 198 /**
 * If the duration is nonzero, causes the MouseWatcher to log the mouse's
 * trail.  Events older than the specified duration are discarded.  If the
 * duration is zero, logging is disabled.
 */ 2 4 this 3 611  8 duration 1 626  
199 0 0 7 249 631 0 0 618 /**
 * Returns a GeomNode that represents the mouse trail.  The intent is that you
 * should reparent this GeomNode to Render2D, and then forget about it.  The
 * MouseWatcher will continually update the trail node.  There is only one
 * trail node, it does not create a new one each time you call get_trail_node.
 *
 * This is not a particularly beautiful way to render a mouse trail.  It is
 * intended more for debugging purposes than for finished applications.  Even
 * so, It is suggested that you might want to apply a line thickness and
 * antialias mode to the line --- doing so makes it look a lot better.
 */ 1 4 this 3 611  
200 0 0 4 250 559 0 0 256 /**
 * If you have previously fetched the trail node using get_trail_node, then
 * the MouseWatcher is continually updating the trail node every frame.  Using
 * clear_trail_node causes the MouseWatcher to forget the trail node and stop
 * updating it.
 */ 1 4 this 3 611  
201 0 0 4 251 559 0 0 117 /**
 * Clears the mouse trail log.  This does not prevent further accumulation of
 * the log given future events.
 */ 1 4 this 3 611  
202 0 0 4 252 559 0 0 586 /**
 * Can be used in conjunction with the inactivity timeout to inform the
 * MouseWatcher that the user has just performed some action which proves
 * he/she is present.  It may be necessary to call this for external events,
 * such as joystick action, that the MouseWatcher might otherwise not know
 * about.  This will reset the current inactivity timer.  When the inactivity
 * timer reaches the length of time specified by set_inactivity_timeout(),
 * with no keyboard or mouse activity and no calls to note_activity(), then
 * any buttons held will be automatically released.
 */ 1 4 this 3 611  
203 0 0 7 253 568 0 0 0 0 
204 0 0 7 191 632 0 0 0 1 4 this 3 611  
205 0 0 6 193 597 0 0 0 1 4 this 3 611  
206 0 0 6 256 529 0 0 105 /**
 * Returns true if this parameter has an associated mouse or keyboard button,
 * false otherwise.
 */ 1 4 this 3 633  
207 0 0 7 257 618 0 0 153 /**
 * Returns the mouse or keyboard button associated with this event.  If
 * has_button(), above, returns false, this returns ButtonHandle::none().
 */ 1 4 this 3 633  
208 0 0 6 258 529 0 0 126 /**
 * Returns true if the button-down even was generated due to keyrepeat, or
 * false if it was an original button down.
 */ 1 4 this 3 633  
209 0 0 6 259 529 0 0 85 /**
 * Returns true if this parameter has an associated keycode, false otherwise.
 */ 1 4 this 3 633  
210 0 0 6 260 545 0 0 118 /**
 * Returns the keycode associated with this event.  If has_keycode(), above,
 * returns false, this returns 0.
 */ 1 4 this 3 633  
211 0 0 6 261 529 0 0 97 /**
 * Returns true if this parameter has an associated candidate string, false
 * otherwise.
 */ 1 4 this 3 633  
212 0 0 6 262 558 0 0 144 /**
 * Returns the candidate string associated with this event.  If
 * has_candidate(), above, returns false, this returns the empty string.
 */ 1 4 this 3 633  
213 0 0 6 262 558 0 0 144 /**
 * Returns the candidate string associated with this event.  If
 * has_candidate(), above, returns false, this returns the empty string.
 */ 2 4 this 3 633  8 encoding 1 635  
214 0 0 6 263 600 0 0 75 /**
 * Returns the first highlighted character in the candidate string.
 */ 1 4 this 3 633  
215 0 0 6 264 600 0 0 91 /**
 * Returns one more than the last highlighted character in the candidate
 * string.
 */ 1 4 this 3 633  
216 0 0 6 265 600 0 0 86 /**
 * Returns the position of the user's edit cursor within the candidate string.
 */ 1 4 this 3 633  
217 0 0 6 266 563 0 0 106 /**
 * Returns the set of modifier buttons that were being held down while the
 * event was generated.
 */ 1 4 this 3 633  
218 0 0 6 267 529 0 0 95 /**
 * Returns true if this parameter has an associated mouse position, false
 * otherwise.
 */ 1 4 this 3 633  
219 0 0 6 268 614 0 0 176 /**
 * Returns the mouse position at the time the event was generated, in the
 * normalized range (-1 .. 1).  It is valid to call this only if has_mouse()
 * returned true.
 */ 1 4 this 3 633  
220 0 0 6 269 529 0 0 163 /**
 * Returns true if the mouse was outside the region at the time the event was
 * generated, false otherwise.  This is only valid for "release" type events.
 */ 1 4 this 3 633  
221 0 0 4 270 559 0 0 10 /**
 *
 */ 2 4 this 3 633  3 out 1 593  
222 0 0 15 272 639 0 0 0 1 6 param0 0 637  
223 0 0 7 272 639 0 0 10 /**
 *
 */ 1 4 name 1 558  
224 0 0 4 273 559 0 0 52 /**
 * Reinitializes all transforms to identity.
 */ 1 4 this 3 639  
225 0 0 6 274 543 0 0 104 /**
 * Returns the scale factor applied to forward and backward motion.  See
 * set_forward_scale().
 */ 1 4 this 3 637  
226 0 0 4 275 559 0 0 173 /**
 * Changes the scale factor applied to forward and backward motion.  The
 * larger this number, the faster the model will move in response to dollying
 * in and out.
 */ 2 4 this 3 639  8 fwdscale 1 543  
227 0 0 6 276 575 0 0 57 /**
 * Return the offset from the center of rotation.
 */ 1 4 this 3 637  
228 0 0 6 277 543 0 0 0 1 4 this 3 637  
229 0 0 6 278 543 0 0 0 1 4 this 3 637  
230 0 0 6 279 543 0 0 0 1 4 this 3 637  
231 0 0 4 280 559 0 0 62 /**
 * Directly set the offset from the rotational origin.
 */ 2 4 this 3 639  3 vec 1 579  
232 0 0 4 280 559 0 0 0 4 4 this 3 639  1 x 1 543  1 y 1 543  1 z 1 543  
233 0 0 4 281 559 0 0 0 2 4 this 3 639  1 x 1 543  
234 0 0 4 282 559 0 0 0 2 4 this 3 639  1 y 1 543  
235 0 0 4 283 559 0 0 0 2 4 this 3 639  1 z 1 543  
236 0 0 7 284 640 0 0 46 /**
 * Return the trackball's orientation.
 */ 1 4 this 3 637  
237 0 0 6 285 543 0 0 0 1 4 this 3 637  
238 0 0 6 286 543 0 0 0 1 4 this 3 637  
239 0 0 6 287 543 0 0 0 1 4 this 3 637  
240 0 0 4 288 559 0 0 48 /**
 * Directly set the mover's orientation.
 */ 2 4 this 3 639  3 hpr 1 579  
241 0 0 4 288 559 0 0 0 4 4 this 3 639  1 h 1 543  1 p 1 543  1 r 1 543  
242 0 0 4 289 559 0 0 0 2 4 this 3 639  1 h 1 543  
243 0 0 4 290 559 0 0 0 2 4 this 3 639  1 p 1 543  
244 0 0 4 291 559 0 0 0 2 4 this 3 639  1 r 1 543  
245 0 0 4 292 559 0 0 148 /**
 * Reposition the center of rotation to coincide with the current translation
 * offset.  Future rotations will be about the current origin.
 */ 1 4 this 3 639  
246 0 0 4 293 559 0 0 60 /**
 * Moves the center of rotation by the given amount.
 */ 4 4 this 3 639  1 x 1 543  1 y 1 543  1 z 1 543  
247 0 0 7 294 641 0 0 50 /**
 * Returns the current center of rotation.
 */ 1 4 this 3 637  
248 0 0 4 295 559 0 0 48 /**
 * Directly sets the center of rotation.
 */ 2 4 this 3 639  6 origin 1 579  
249 0 0 4 297 559 0 0 348 /**
 * Sets the control mode.  Normally this is CM_default, which means each mouse
 * button serves its normal function.  When it is CM_truck, CM_pan, CM_dolly,
 * or CM_roll, all of the mouse buttons serve the indicated function instead
 * of their normal function.  This can be used in conjunction with some
 * external way of changing modes.
 */ 2 4 this 3 639  12 control_mode 1 553  
250 0 0 6 298 553 0 0 61 /**
 * Returns the control mode.  See set_control_mode().
 */ 1 4 this 3 637  
251 0 0 4 299 559 0 0 162 /**
 * Sets the invert flag.  When this is set, the inverse matrix is generated,
 * suitable for joining to a camera, instead of parenting the scene under it.
 */ 2 4 this 3 639  4 flag 1 529  
252 0 0 6 300 529 0 0 168 /**
 * Returns the invert flag.  When this is set, the inverse matrix is
 * generated, suitable for joining to a camera, instead of parenting the scene
 * under it.
 */ 1 4 this 3 637  
253 0 0 4 301 559 0 0 275 /**
 * Sets the NodePath that all trackball manipulations are to be assumed to be
 * relative to.  For instance, set your camera node here to make the trackball
 * motion camera relative.  The default is the empty path, which means
 * trackball motion is in global space.
 */ 2 4 this 3 639  6 rel_to 1 602  
254 0 0 6 302 602 0 0 103 /**
 * Returns the NodePath that all trackball manipulations are relative to, or
 * the empty path.
 */ 1 4 this 3 637  
255 0 0 4 303 559 0 0 243 /**
 * Sets the coordinate system of the Trackball.  Normally, this is the default
 * coordinate system.  This changes the axes the Trackball manipulates so that
 * the user interface remains consistent across different coordinate systems.
 */ 2 4 this 3 639  2 cs 1 642  
256 0 0 6 304 642 0 0 91 /**
 * Returns the coordinate system of the Trackball.  See
 * set_coordinate_system().
 */ 1 4 this 3 637  
257 0 0 4 305 559 0 0 131 /**
 * Stores the indicated transform in the trackball.  This is a transform in
 * global space, regardless of the rel_to node.
 */ 2 4 this 3 639  3 mat 1 583  
258 0 0 6 306 583 0 0 68 /**
 * Returns the matrix represented by the trackball rotation.
 */ 1 4 this 3 637  
259 0 0 6 307 583 0 0 143 /**
 * Returns the actual transform that will be applied to the scene graph.  This
 * is the same as get_mat(), unless invert is in effect.
 */ 1 4 this 3 637  
260 0 0 7 308 568 0 0 0 0 
261 0 0 15 310 645 523 0 0 1 6 param0 0 643  
262 0 0 7 310 645 523 0 10 /**
 *
 */ 1 4 name 1 558  
263 0 0 4 311 559 0 0 54 /**
 * Sets the node that this object will adjust.
 */ 2 4 this 3 645  4 node 1 619  
264 0 0 7 312 619 0 0 102 /**
 * Returns the node that this object will adjust, or NULL if the node has not
 * yet been set.
 */ 1 4 this 3 643  
265 0 0 7 313 568 0 0 0 0 
122
524 13 ButtonThrower 0 75777 13 ButtonThrower 13 ButtonThrower 0 0 0 1 266 0 14 646 647 648 649 650 651 652 653 654 655 656 657 658 659 34 267 268 269 270 271 272 273 274 275 276 277 278 279 280 281 282 283 284 285 286 287 288 289 290 291 292 293 294 295 296 297 298 299 300 1 668 0 1 0 525 0 0 0 0 397
/**
 * Throws Panda Events for button down/up events generated within the data
 * graph.
 *
 * This is a DataNode which is intended to be parented to the data graph below
 * a device which is generating a sequence of button events, like a
 * MouseAndKeyboard device.  It simply takes each button it finds and throws a
 * corresponding event based on the button name via the throw_event() call.
 */

525 8 DataNode 0 2048 8 DataNode 8 DataNode 0 0 0 0 0 0 0 0 0 0 0 0 422
/**
 * The fundamental type of node for the data graph.  The DataNode class is
 * itself primarily intended as an abstract class; it defines no inputs and no
 * outputs.  Most kinds of data nodes will derive from this to specify the
 * inputs and outputs in the constructor.
 *
 * DataNode does not attempt to cycle its data with a PipelineCycler.  The
 * data graph is intended to be used only within a single thread.
 */

526 12 string const 0 8832 17 std::string const 17 std::string const 0 0 527 0 0 0 0 0 0 0 0 0 0

527 6 string 0 2105344 11 std::string 11 std::string 0 0 528 0 0 0 0 0 0 0 0 0 0

528 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

529 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

530 14 EventParameter 0 2048 14 EventParameter 14 EventParameter 0 0 0 0 0 0 0 0 0 0 0 0 389
/**
 * An optional parameter associated with an event.  Each event may have zero
 * or more of these.  Each parameter stores a pointer to a
 * TypedWritableReferenceCount object, which of course could be pretty much
 * anything.  To store a simple value like a double or a string, the
 * EventParameter constructors transparently use the ParamValue template class
 * from paramValue.h.
 */

531 21 ModifierButtons const 0 8832 21 ModifierButtons const 21 ModifierButtons const 0 0 532 0 0 0 0 0 0 0 0 0 0

532 15 ModifierButtons 0 2048 15 ModifierButtons 15 ModifierButtons 0 0 0 0 0 0 0 0 0 0 0 0 135
/**
 * This class monitors the state of a number of individual buttons and tracks
 * whether each button is known to be down or up.
 */

533 18 MouseInterfaceNode 0 75777 18 MouseInterfaceNode 18 MouseInterfaceNode 0 0 0 1 305 0 0 4 301 302 303 304 0 0 1 0 525 0 0 0 0 249
/**
 * This is the base class for some classes that monitor the mouse and keyboard
 * input and perform some action due to their state.
 *
 * It collects together some common interface; in particular, the
 * require_button() and related methods.
 */

534 14 DriveInterface 0 75777 14 DriveInterface 14 DriveInterface 0 0 0 1 306 0 0 48 307 308 309 310 311 312 313 314 315 316 317 318 319 320 321 322 323 324 325 326 327 328 329 330 331 332 333 334 335 336 337 338 339 340 341 342 343 344 345 346 347 348 349 350 351 352 353 354 0 0 1 0 533 0 0 0 0 191
/**
 * This is a TFormer, similar to Trackball, that moves around a transform
 * matrix in response to mouse input.  The basic motion is on a horizontal
 * plane, as if driving a vehicle.
 */

535 14 MouseSubregion 0 75777 14 MouseSubregion 14 MouseSubregion 0 0 0 1 355 0 0 6 356 357 358 359 360 361 0 0 1 0 533 0 0 0 0 321
/**
 * The MouseSubregion object scales the mouse inputs from within a rectangular
 * region of the screen, as if they were the full-screen inputs.
 *
 * If you choose your MouseSubregion coordinates to exactly match a
 * DisplayRegion within your window, you end up with a virtual mouse within
 * your DisplayRegion.
 */

536 18 MouseWatcherRegion 0 141313 18 MouseWatcherRegion 18 MouseWatcherRegion 0 0 0 1 366 381 6 660 661 662 663 664 665 14 367 368 369 370 371 372 373 374 375 376 377 378 379 380 0 0 2 3 537 362 363 3 538 364 365 0 1 539 101
/**
 * This is the class that defines a rectangular region on the screen for the
 * MouseWatcher.
 */

537 27 TypedWritableReferenceCount 0 2048 27 TypedWritableReferenceCount 27 TypedWritableReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 414
/**
 * A base class for things which need to inherit from both TypedWritable and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedWritables and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

538 7 Namable 0 2048 7 Namable 7 Namable 0 0 0 0 0 0 0 0 0 0 0 0 125
/**
 * A base class for all things which can have a name.  The name is either
 * empty or nonempty, but it is never NULL.
 */

539 13 SuppressFlags 0 794624 33 MouseWatcherRegion::SuppressFlags 33 MouseWatcherRegion::SuppressFlags 536 0 0 0 0 0 0 0 0 0 4 15 SF_mouse_button 35 MouseWatcherRegion::SF_mouse_button 0
1 15 SF_other_button 35 MouseWatcherRegion::SF_other_button 0
2 13 SF_any_button 33 MouseWatcherRegion::SF_any_button 0
3 17 SF_mouse_position 37 MouseWatcherRegion::SF_mouse_position 0
4 0 0

540 15 LVecBase4 const 0 8832 15 LVecBase4 const 15 LVecBase4 const 0 0 541 0 0 0 0 0 0 0 0 0 0

541 9 LVecBase4 0 2105344 9 LVecBase4 9 LVecBase4 0 0 542 0 0 0 0 0 0 0 0 0 0

542 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

543 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 544 0 0 0 0 0 0 0 0 0 0

544 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

545 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

546 16 MouseWatcherBase 0 26625 16 MouseWatcherBase 16 MouseWatcherBase 0 0 0 0 382 2 666 667 16 383 384 385 386 387 388 389 390 391 392 393 394 395 396 397 398 1 669 0 0 0 0 270
/**
 * This represents a collection of MouseWatcherRegions that may be managed as
 * a group.  This is the base class for both MouseWatcherGroup and
 * MouseWatcher, and exists so that we don't have to make MouseWatcher inherit
 * from ReferenceCount more than once.
 */

547 20 MouseWatcherRegion * 0 8576 20 MouseWatcherRegion * 20 MouseWatcherRegion * 0 0 536 0 0 0 0 0 0 0 0 0 0

548 17 MouseWatcherGroup 0 141313 17 MouseWatcherGroup 17 MouseWatcherGroup 0 0 0 0 404 0 1 403 0 0 2 3 546 399 400 3 549 401 402 0 0 215
/**
 * This represents a collection of MouseWatcherRegions that may be managed as
 * a group.  The implementation for this is in MouseWatcherBase; this class
 * exists so that we can inherit from ReferenceCount.
 */

549 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

550 12 MouseWatcher 0 26625 12 MouseWatcher 12 MouseWatcher 0 0 0 1 409 410 0 56 411 412 413 414 415 416 417 418 419 420 421 422 423 424 425 426 427 428 429 430 431 432 433 434 435 436 437 438 439 440 441 442 443 444 445 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 461 462 463 464 465 466 1 670 0 2 3 525 405 406 3 546 407 408 0 0 1142
/**
 * This TFormer maintains a list of rectangular regions on the screen that are
 * considered special mouse regions; typically these will be click buttons.
 * When the mouse passes in or out of one of these regions, or when a button
 * is clicked while the mouse is in one of these regions, an event is thrown.
 *
 * Mouse events may also be suppressed from the rest of the datagraph in these
 * special regions.
 *
 * This class can also implement a software mouse pointer by automatically
 * generating a transform to apply to a piece of geometry placed under the 2-d
 * scene graph.  It will move the geometry around according to the mouse's
 * known position.
 *
 * Finally, this class can keep a record of the mouse trail.  This is useful
 * if you want to know, not just where the mouse is, but the exact sequence of
 * movements it took to get there.  This information is mainly useful for
 * gesture-recognition code.  To use trail logging, you need to enable the
 * generation of pointer events in the GraphicsWindowInputDevice and set the
 * trail log duration in the MouseWatcher.  Otherwise, the trail log will be
 * empty.
 */

551 21 MouseWatcherParameter 0 26625 21 MouseWatcherParameter 21 MouseWatcherParameter 0 0 0 0 467 0 15 468 469 470 471 472 473 474 475 476 477 478 479 480 481 482 0 0 0 0 0 139
/**
 * This is sent along as a parameter to most events generated for a region to
 * indicate the mouse and button state for the event.
 */

552 9 Trackball 0 75777 9 Trackball 9 Trackball 0 0 0 1 483 0 0 35 484 485 486 487 488 489 490 491 492 493 494 495 496 497 498 499 500 501 502 503 504 505 506 507 508 509 510 511 512 513 514 515 516 517 518 0 0 1 0 533 0 0 0 1 553 410
/**
 * Trackball acts like Performer in trackball mode.  It can either spin around
 * a piece of geometry directly, or it can spin around a camera with the
 * inverse transform to make it appear that the whole world is spinning.
 *
 * The Trackball object actually just places a transform in the data graph;
 * parent a Transform2SG node under it to actually transform objects (or
 * cameras) in the world.
 */

553 11 ControlMode 0 794624 22 Trackball::ControlMode 22 Trackball::ControlMode 552 0 0 0 0 0 0 0 0 0 5 10 CM_default 21 Trackball::CM_default 0
0 8 CM_truck 19 Trackball::CM_truck 19
// Normally mouse 1
1 6 CM_pan 17 Trackball::CM_pan 19
// Normally mouse 2
2 8 CM_dolly 19 Trackball::CM_dolly 19
// Normally mouse 3
3 7 CM_roll 18 Trackball::CM_roll 23
// Normally mouse 2 + 3
4 0 0

554 12 Transform2SG 0 141313 12 Transform2SG 12 Transform2SG 0 0 0 1 519 523 0 3 520 521 522 0 0 1 0 525 0 0 0 0 146
/**
 * input: Transform (matrix)
 *
 * output: none, but applies the matrix as the transform transition for a
 * given arc of the scene graph.
 */

555 21 ButtonThrower const * 0 8576 21 ButtonThrower const * 21 ButtonThrower const * 0 0 556 0 0 0 0 0 0 0 0 0 0

556 19 ButtonThrower const 0 8832 19 ButtonThrower const 19 ButtonThrower const 0 0 524 0 0 0 0 0 0 0 0 0 0

557 15 ButtonThrower * 0 8576 15 ButtonThrower * 15 ButtonThrower * 0 0 524 0 0 0 0 0 0 0 0 0 0

558 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

559 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

560 22 EventParameter const * 0 8576 22 EventParameter const * 22 EventParameter const * 0 0 561 0 0 0 0 0 0 0 0 0 0

561 20 EventParameter const 0 8832 20 EventParameter const 20 EventParameter const 0 0 530 0 0 0 0 0 0 0 0 0 0

562 16 EventParameter * 0 8576 16 EventParameter * 16 EventParameter * 0 0 530 0 0 0 0 0 0 0 0 0 0

563 23 ModifierButtons const * 0 8576 23 ModifierButtons const * 23 ModifierButtons const * 0 0 531 0 0 0 0 0 0 0 0 0 0

564 20 ButtonHandle const * 0 8576 20 ButtonHandle const * 20 ButtonHandle const * 0 0 565 0 0 0 0 0 0 0 0 0 0

565 18 ButtonHandle const 0 8832 18 ButtonHandle const 18 ButtonHandle const 0 0 566 0 0 0 0 0 0 0 0 0 0

566 12 ButtonHandle 0 16779264 12 ButtonHandle 12 ButtonHandle 0 0 0 0 0 0 0 0 0 0 0 0 162
/**
 * A ButtonHandle represents a single button from any device, including
 * keyboard buttons and mouse buttons (but see KeyboardButton and
 * MouseButton).
 */

567 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

568 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 567 0 0 0 0 0 0 0 0 0 0

569 26 MouseInterfaceNode const * 0 8576 26 MouseInterfaceNode const * 26 MouseInterfaceNode const * 0 0 570 0 0 0 0 0 0 0 0 0 0

570 24 MouseInterfaceNode const 0 8832 24 MouseInterfaceNode const 24 MouseInterfaceNode const 0 0 533 0 0 0 0 0 0 0 0 0 0

571 20 MouseInterfaceNode * 0 8576 20 MouseInterfaceNode * 20 MouseInterfaceNode * 0 0 533 0 0 0 0 0 0 0 0 0 0

572 22 DriveInterface const * 0 8576 22 DriveInterface const * 22 DriveInterface const * 0 0 573 0 0 0 0 0 0 0 0 0 0

573 20 DriveInterface const 0 8832 20 DriveInterface const 20 DriveInterface const 0 0 534 0 0 0 0 0 0 0 0 0 0

574 16 DriveInterface * 0 8576 16 DriveInterface * 16 DriveInterface * 0 0 534 0 0 0 0 0 0 0 0 0 0

575 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 576 0 0 0 0 0 0 0 0 0 0

576 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 577 0 0 0 0 0 0 0 0 0 0

577 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 578 0 0 0 0 0 0 0 0 0 0

578 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

579 17 LVecBase3 const * 0 8576 17 LVecBase3 const * 17 LVecBase3 const * 0 0 580 0 0 0 0 0 0 0 0 0 0

580 15 LVecBase3 const 0 8832 15 LVecBase3 const 15 LVecBase3 const 0 0 581 0 0 0 0 0 0 0 0 0 0

581 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 582 0 0 0 0 0 0 0 0 0 0

582 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

583 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 584 0 0 0 0 0 0 0 0 0 0

584 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 585 0 0 0 0 0 0 0 0 0 0

585 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 586 0 0 0 0 0 0 0 0 0 0

586 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

587 22 MouseSubregion const * 0 8576 22 MouseSubregion const * 22 MouseSubregion const * 0 0 588 0 0 0 0 0 0 0 0 0 0

588 20 MouseSubregion const 0 8832 20 MouseSubregion const 20 MouseSubregion const 0 0 535 0 0 0 0 0 0 0 0 0 0

589 16 MouseSubregion * 0 8576 16 MouseSubregion * 16 MouseSubregion * 0 0 535 0 0 0 0 0 0 0 0 0 0

590 26 MouseWatcherRegion const * 0 8576 26 MouseWatcherRegion const * 26 MouseWatcherRegion const * 0 0 591 0 0 0 0 0 0 0 0 0 0

591 24 MouseWatcherRegion const 0 8832 24 MouseWatcherRegion const 24 MouseWatcherRegion const 0 0 536 0 0 0 0 0 0 0 0 0 0

592 17 LVecBase4 const * 0 8576 17 LVecBase4 const * 17 LVecBase4 const * 0 0 540 0 0 0 0 0 0 0 0 0 0

593 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 594 0 0 0 0 0 0 0 0 0 0

594 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

595 29 TypedWritableReferenceCount * 0 8576 29 TypedWritableReferenceCount * 29 TypedWritableReferenceCount * 0 0 537 0 0 0 0 0 0 0 0 0 0

596 9 Namable * 0 8576 9 Namable * 9 Namable * 0 0 538 0 0 0 0 0 0 0 0 0 0

597 18 MouseWatcherBase * 0 8576 18 MouseWatcherBase * 18 MouseWatcherBase * 0 0 546 0 0 0 0 0 0 0 0 0 0

598 24 MouseWatcherBase const * 0 8576 24 MouseWatcherBase const * 24 MouseWatcherBase const * 0 0 599 0 0 0 0 0 0 0 0 0 0

599 22 MouseWatcherBase const 0 8832 22 MouseWatcherBase const 22 MouseWatcherBase const 0 0 546 0 0 0 0 0 0 0 0 0 0

600 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 601 0 0 0 0 0 0 0 0 0 0

601 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

602 16 NodePath const * 0 8576 16 NodePath const * 16 NodePath const * 0 0 603 0 0 0 0 0 0 0 0 0 0

603 14 NodePath const 0 8832 14 NodePath const 14 NodePath const 0 0 604 0 0 0 0 0 0 0 0 0 0

604 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

605 14 LColor const * 0 8576 14 LColor const * 14 LColor const * 0 0 606 0 0 0 0 0 0 0 0 0 0

606 12 LColor const 0 8832 12 LColor const 12 LColor const 0 0 607 0 0 0 0 0 0 0 0 0 0

607 6 LColor 0 2105344 6 LColor 6 LColor 0 0 608 0 0 0 0 0 0 0 0 0 0

608 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 542 0 0 0 0 0 0 0 0 0 0

609 19 MouseWatcherGroup * 0 8576 19 MouseWatcherGroup * 19 MouseWatcherGroup * 0 0 548 0 0 0 0 0 0 0 0 0 0

610 16 ReferenceCount * 0 8576 16 ReferenceCount * 16 ReferenceCount * 0 0 549 0 0 0 0 0 0 0 0 0 0

611 14 MouseWatcher * 0 8576 14 MouseWatcher * 14 MouseWatcher * 0 0 550 0 0 0 0 0 0 0 0 0 0

612 20 MouseWatcher const * 0 8576 20 MouseWatcher const * 20 MouseWatcher const * 0 0 613 0 0 0 0 0 0 0 0 0 0

613 18 MouseWatcher const 0 8832 18 MouseWatcher const 18 MouseWatcher const 0 0 550 0 0 0 0 0 0 0 0 0 0

614 15 LPoint2 const * 0 8576 15 LPoint2 const * 15 LPoint2 const * 0 0 615 0 0 0 0 0 0 0 0 0 0

615 13 LPoint2 const 0 8832 13 LPoint2 const 13 LPoint2 const 0 0 616 0 0 0 0 0 0 0 0 0 0

616 7 LPoint2 0 2105344 7 LPoint2 7 LPoint2 0 0 617 0 0 0 0 0 0 0 0 0 0

617 8 LPoint2f 0 2048 8 LPoint2f 8 LPoint2f 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

618 14 ButtonHandle * 0 8576 14 ButtonHandle * 14 ButtonHandle * 0 0 566 0 0 0 0 0 0 0 0 0 0

619 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 620 0 0 0 0 0 0 0 0 0 0

620 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

621 14 EventHandler * 0 8576 14 EventHandler * 14 EventHandler * 0 0 622 0 0 0 0 0 0 0 0 0 0

622 12 EventHandler 0 2048 12 EventHandler 12 EventHandler 0 0 0 0 0 0 0 0 0 0 0 0 384
/**
 * A class to monitor events from the C++ side of things.  It maintains a set
 * of "hooks", function pointers assigned to event names, and calls the
 * appropriate hooks when the matching event is detected.
 *
 * This class is not necessary when the hooks are detected and processed
 * entirely by the scripting language, e.g.  via Scheme hooks or the messenger
 * in Python.
 */

623 17 ModifierButtons * 0 8576 17 ModifierButtons * 17 ModifierButtons * 0 0 532 0 0 0 0 0 0 0 0 0 0

624 15 DisplayRegion * 0 8576 15 DisplayRegion * 15 DisplayRegion * 0 0 625 0 0 0 0 0 0 0 0 0 0

625 13 DisplayRegion 0 2048 13 DisplayRegion 13 DisplayRegion 0 0 0 0 0 0 0 0 0 0 0 0 399
/**
 * A rectangular subregion within a window for rendering into.  Typically,
 * there is one DisplayRegion that covers the whole window, but you may also
 * create smaller DisplayRegions for having different regions within the
 * window that represent different scenes.  You may also stack up
 * DisplayRegions like panes of glass, usually for layering 2-d interfaces on
 * top of a 3-d scene.
 */

626 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

627 22 PointerEventList const 0 8832 22 PointerEventList const 22 PointerEventList const 0 0 628 0 0 0 0 0 0 0 0 0 0

628 16 PointerEventList 0 2048 16 PointerEventList 16 PointerEventList 0 0 0 0 0 0 0 0 0 0 0 0 228
/**
 * Records a set of pointer events that happened recently.  This class is
 * usually used only in the data graph, to transmit the recent pointer
 * presses, but it may be used anywhere a list of PointerEvents is desired.
 */

629 24 PointerEventList const * 0 8576 24 PointerEventList const * 24 PointerEventList const * 0 0 627 0 0 0 0 0 0 0 0 0 0

630 8 GeomNode 0 2048 8 GeomNode 8 GeomNode 0 0 0 0 0 0 0 0 0 0 0 0 208
/**
 * A node that holds Geom objects, renderable pieces of geometry.  This is the
 * primary kind of leaf node in the scene graph; almost all visible objects
 * will be contained in a GeomNode somewhere.
 */

631 10 GeomNode * 0 8576 10 GeomNode * 10 GeomNode * 0 0 630 0 0 0 0 0 0 0 0 0 0

632 10 DataNode * 0 8576 10 DataNode * 10 DataNode * 0 0 525 0 0 0 0 0 0 0 0 0 0

633 29 MouseWatcherParameter const * 0 8576 29 MouseWatcherParameter const * 29 MouseWatcherParameter const * 0 0 634 0 0 0 0 0 0 0 0 0 0

634 27 MouseWatcherParameter const 0 8832 27 MouseWatcherParameter const 27 MouseWatcherParameter const 0 0 551 0 0 0 0 0 0 0 0 0 0

635 8 Encoding 0 794624 21 TextEncoder::Encoding 21 TextEncoder::Encoding 636 0 0 0 0 0 0 0 0 0 4 9 E_iso8859 22 TextEncoder::E_iso8859 0
0 6 E_utf8 19 TextEncoder::E_utf8 0
1 9 E_utf16be 22 TextEncoder::E_utf16be 0
2 9 E_unicode 22 TextEncoder::E_unicode 33
// Deprecated alias for E_utf16be
2 0 0

636 11 TextEncoder 0 2048 11 TextEncoder 11 TextEncoder 0 0 0 0 0 0 0 0 0 0 0 0 399
/**
 * This class can be used to convert text between multiple representations,
 * e.g.  UTF-8 to UTF-16.  You may use it as a static class object, passing
 * the encoding each time, or you may create an instance and use that object,
 * which will record the current encoding and retain the current string.
 *
 * This class is also a base class of TextNode, which inherits this
 * functionality.
 */

637 17 Trackball const * 0 8576 17 Trackball const * 17 Trackball const * 0 0 638 0 0 0 0 0 0 0 0 0 0

638 15 Trackball const 0 8832 15 Trackball const 15 Trackball const 0 0 552 0 0 0 0 0 0 0 0 0 0

639 11 Trackball * 0 8576 11 Trackball * 11 Trackball * 0 0 552 0 0 0 0 0 0 0 0 0 0

640 11 LVecBase3 * 0 8576 11 LVecBase3 * 11 LVecBase3 * 0 0 581 0 0 0 0 0 0 0 0 0 0

641 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 577 0 0 0 0 0 0 0 0 0 0

642 16 CoordinateSystem 0 532480 16 CoordinateSystem 16 CoordinateSystem 0 0 0 0 0 0 0 0 0 0 6 10 CS_default 10 CS_default 210
// The CS_default entry does not refer to a particular coordinate system,
// but rather to the value stored in default_coordinate_system, which in
// turn is loaded from the config variable "coordinate-system".
0 12 CS_zup_right 12 CS_zup_right 21
// Z-Up, Right-handed
1 12 CS_yup_right 12 CS_yup_right 21
// Y-Up, Right-handed
2 11 CS_zup_left 11 CS_zup_left 20
// Z-Up, Left-handed
3 11 CS_yup_left 11 CS_yup_left 20
// Y-Up, Left-handed
4 10 CS_invalid 10 CS_invalid 156
// CS_invalid is not a coordinate system at all.  It can be used in user-
// input processing code to indicate a contradictory coordinate system
// request.
5 0 0

643 20 Transform2SG const * 0 8576 20 Transform2SG const * 20 Transform2SG const * 0 0 644 0 0 0 0 0 0 0 0 0 0

644 18 Transform2SG const 0 8832 18 Transform2SG const 18 Transform2SG const 0 0 554 0 0 0 0 0 0 0 0 0 0

645 14 Transform2SG * 0 8576 14 Transform2SG * 14 Transform2SG * 0 0 554 0 0 0 0 0 0 0 0 0 0

0
22
646 17 button_down_event 0 6 526 268 267 0 0 0 0 0 0 32 ButtonThrower::button_down_event 0

647 15 button_up_event 0 6 526 270 269 0 0 0 0 0 0 30 ButtonThrower::button_up_event 0

648 19 button_repeat_event 0 6 526 272 271 0 0 0 0 0 0 34 ButtonThrower::button_repeat_event 0

649 15 keystroke_event 0 6 526 274 273 0 0 0 0 0 0 30 ButtonThrower::keystroke_event 0

650 15 candidate_event 0 6 526 276 275 0 0 0 0 0 0 30 ButtonThrower::candidate_event 0

651 10 move_event 0 6 526 278 277 0 0 0 0 0 0 25 ButtonThrower::move_event 0

652 21 raw_button_down_event 0 6 526 280 279 0 0 0 0 0 0 36 ButtonThrower::raw_button_down_event 0

653 19 raw_button_up_event 0 6 526 282 281 0 0 0 0 0 0 34 ButtonThrower::raw_button_up_event 0

654 6 prefix 0 6 526 284 283 0 0 0 0 0 0 21 ButtonThrower::prefix 0

655 13 specific_flag 0 6 529 286 285 0 0 0 0 0 0 28 ButtonThrower::specific_flag 0

656 9 time_flag 0 6 529 288 287 0 0 0 0 0 0 24 ButtonThrower::time_flag 0

657 10 parameters 0 66 530 291 0 0 0 0 290 0 0 25 ButtonThrower::parameters 0

658 16 modifier_buttons 0 6 531 292 293 0 0 0 0 0 0 31 ButtonThrower::modifier_buttons 0

659 20 throw_buttons_active 0 6 529 295 294 0 0 0 0 0 0 35 ButtonThrower::throw_buttons_active 0

660 5 frame 0 6 540 368 367 0 0 0 0 0 0 25 MouseWatcherRegion::frame 0

661 4 area 0 2 543 369 0 0 0 0 0 0 0 24 MouseWatcherRegion::area 0

662 4 sort 0 6 545 371 370 0 0 0 0 0 0 24 MouseWatcherRegion::sort 0

663 6 active 0 6 529 373 372 0 0 0 0 0 0 26 MouseWatcherRegion::active 0

664 8 keyboard 0 6 529 375 374 0 0 0 0 0 0 28 MouseWatcherRegion::keyboard 0

665 14 suppress_flags 0 6 545 377 376 0 0 0 0 0 0 34 MouseWatcherRegion::suppress_flags 0

666 6 sorted 0 2 529 389 0 0 0 0 0 0 0 24 MouseWatcherBase::sorted 0

667 7 regions 0 66 547 391 0 0 0 0 390 0 0 25 MouseWatcherBase::regions 0

3
668 14 get_parameters 0 290 291 29 ButtonThrower::get_parameters 0

669 11 get_regions 0 390 391 29 MouseWatcherBase::get_regions 0

670 10 get_groups 0 451 452 24 MouseWatcher::get_groups 0

