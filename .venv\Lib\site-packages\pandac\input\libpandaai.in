1730992849
3 3
10 libpandaai 4 s1eG 10 panda3d.ai 
52
55 12 ~AIBehaviors 0 516 107 25 AIBehaviors::~AIBehaviors 0 0 0
32
AIBehaviors::~AIBehaviors(void);

56 4 seek 0 4 107 17 AIBehaviors::seek 0 2 2 3 219
/**
 * This function activates seek and makes an object of the Seek class.  This
 * is the function we want the user to call for seek to be done.  This
 * function is overloaded to accept a NodePath or an LVecBase3.
 */
128
void AIBehaviors::seek(NodePath target_object, float seek_wt = 1.0);
void AIBehaviors::seek(LVecBase3 pos, float seek_wt = 1.0);

57 4 flee 0 4 107 17 AIBehaviors::flee 0 2 4 5 159
/**
 * This function activates flee_activate and creates an object of the Flee
 * class.  This function is overloaded to accept a NodePath or an LVecBase3.
 */
248
void AIBehaviors::flee(NodePath target_object, double panic_distance = 10.0, double relax_distance = 10.0, float flee_wt = 1.0);
void AIBehaviors::flee(LVecBase3 pos, double panic_distance = 10.0, double relax_distance = 10.0, float flee_wt = 1.0);

58 6 pursue 0 4 107 19 AIBehaviors::pursue 0 1 6 115
/**
 * This function activates pursue.  This is the function we want the user to
 * call for pursue to be done.
 */
72
void AIBehaviors::pursue(NodePath target_object, float pursue_wt = 1.0);

59 5 evade 0 4 107 18 AIBehaviors::evade 0 1 7 50
/**
 * This function activates evade_activate.
 */
130
void AIBehaviors::evade(NodePath target_object, double panic_distance = 10.0, double relax_distance = 10.0, float evade_wt = 1.0);

60 7 arrival 0 4 107 20 AIBehaviors::arrival 0 1 8 117
/**
 * This function activates arrival.  This is the function we want the user to
 * call for arrival to be done.
 */
50
void AIBehaviors::arrival(double distance = 10.0);

61 5 flock 0 4 107 18 AIBehaviors::flock 0 1 9 113
/**
 * This function activates flock.  This is the function we want the user to
 * call for flock to be done.
 */
40
void AIBehaviors::flock(float flock_wt);

62 6 wander 0 4 107 19 AIBehaviors::wander 0 1 10 114
/**
 * This function activates wander.  This is the function we want the user to
 * call for flock to be done.
 */
112
void AIBehaviors::wander(double wander_radius = 5.0, int flag = 0, double aoe = 0.0, float wander_weight = 1.0);

63 18 obstacle_avoidance 0 4 107 31 AIBehaviors::obstacle_avoidance 0 1 11 169
/**
 * This function activates obstacle avoidance for a given character.  This is
 * the function we want the user to call for obstacle avoidance to be
 * performed.
 */
64
void AIBehaviors::obstacle_avoidance(float feeler_length = 1.0);

64 11 path_follow 0 4 107 24 AIBehaviors::path_follow 0 1 12 120
/**
 * This function activates path following.  This is the function we want the
 * user to call for path following.
 */
53
void AIBehaviors::path_follow(float follow_wt = 1.0);

65 11 add_to_path 0 4 107 24 AIBehaviors::add_to_path 0 1 13 62
/**
 * This function adds positions to the path to follow.
 */
45
void AIBehaviors::add_to_path(LVecBase3 pos);

66 12 start_follow 0 4 107 25 AIBehaviors::start_follow 0 1 14 0
60
void AIBehaviors::start_follow(std::string type = "normal");

67 14 init_path_find 0 4 107 27 AIBehaviors::init_path_find 0 1 15 165
// should have different function names.

/**
 * This function activates path finding in the character.  This function
 * accepts the meshdata in .csv format.
 *
 */
63
void AIBehaviors::init_path_find(char const *navmesh_filename);

68 12 path_find_to 0 4 107 25 AIBehaviors::path_find_to 0 2 16 17 452
/**
 * This function checks for the source and target in the navigation mesh for
 * its availability and then finds the best path via the A* algorithm Then it
 * calls the path follower to make the object follow the path.
 */

/**
 * This function checks for the source and target in the navigation mesh for
 * its availability and then finds the best path via the A* algorithm Then it
 * calls the path follower to make the object follow the path.
 */
153
void AIBehaviors::path_find_to(LVecBase3 pos, std::string type = "normal");
void AIBehaviors::path_find_to(NodePath target, std::string type = "normal");

69 19 add_static_obstacle 0 4 107 32 AIBehaviors::add_static_obstacle 0 1 18 262
/**
 * This function allows the user to dynamically add obstacles to the game
 * environment.  The function will update the nodes within the bounding volume
 * of the obstacle as non-traversable.  Hence will not be considered by the
 * pathfinding algorithm.
 */
57
void AIBehaviors::add_static_obstacle(NodePath obstacle);

70 20 add_dynamic_obstacle 0 4 107 33 AIBehaviors::add_dynamic_obstacle 0 1 19 98
/**
 * This function starts the pathfinding obstacle navigation for the passed in
 * obstacle.
 */
58
void AIBehaviors::add_dynamic_obstacle(NodePath obstacle);

71 9 remove_ai 0 4 107 22 AIBehaviors::remove_ai 0 1 20 59
/**
 * This function removes individual or all the AIs.
 */
49
void AIBehaviors::remove_ai(std::string ai_type);

72 8 pause_ai 0 4 107 21 AIBehaviors::pause_ai 0 1 21 58
/**
 * This function pauses individual or all the AIs.
 */
48
void AIBehaviors::pause_ai(std::string ai_type);

73 9 resume_ai 0 4 107 22 AIBehaviors::resume_ai 0 1 22 58
/**
 * This function resumes individual or all the AIs
 */
49
void AIBehaviors::resume_ai(std::string ai_type);

74 15 behavior_status 0 4 107 28 AIBehaviors::behavior_status 0 1 23 149
/**
 * This function returns the status of an AI Type whether it is active, paused
 * or disabled.  It returns -1 if an invalid string is passed.
 */
62
std::string AIBehaviors::behavior_status(std::string ai_type);

75 11 AIBehaviors 0 260 107 24 AIBehaviors::AIBehaviors 0 1 1 0
63
inline AIBehaviors::AIBehaviors(AIBehaviors const &) = default;

76 8 get_mass 0 4 108 21 AICharacter::get_mass 0 1 26 0
35
double AICharacter::get_mass(void);

77 8 set_mass 0 4 108 21 AICharacter::set_mass 0 1 27 0
37
void AICharacter::set_mass(double m);

78 12 get_velocity 0 4 108 25 AICharacter::get_velocity 0 1 28 0
42
LVecBase3 AICharacter::get_velocity(void);

79 13 get_max_force 0 4 108 26 AICharacter::get_max_force 0 1 29 0
40
double AICharacter::get_max_force(void);

80 13 set_max_force 0 4 108 26 AICharacter::set_max_force 0 1 30 0
50
void AICharacter::set_max_force(double max_force);

81 13 get_node_path 0 4 108 26 AICharacter::get_node_path 0 1 31 0
42
NodePath AICharacter::get_node_path(void);

82 13 set_node_path 0 4 108 26 AICharacter::set_node_path 0 1 32 0
45
void AICharacter::set_node_path(NodePath np);

83 16 get_ai_behaviors 0 4 108 29 AICharacter::get_ai_behaviors 0 1 33 0
49
AIBehaviors *AICharacter::get_ai_behaviors(void);

84 12 set_pf_guide 0 4 108 25 AICharacter::set_pf_guide 0 1 34 74
// This function is used to enable or disable the guides for path finding.
46
void AICharacter::set_pf_guide(bool pf_guide);

85 11 AICharacter 0 260 108 24 AICharacter::AICharacter 0 2 24 25 0
191
explicit AICharacter::AICharacter(std::string model_name, NodePath model_np, double mass, double movt_force, double max_force);
inline AICharacter::AICharacter(AICharacter const &) = default;

86 8 get_name 0 4 108 21 AICharacter::get_name 0 0 0
40
std::string AICharacter::get_name(void);

87 6 AINode 0 260 112 14 AINode::AINode 0 2 35 36 0
139
explicit AINode::AINode(int grid_x, int grid_y, LVecBase3 pos, float w, float l, float h);
inline AINode::AINode(AINode const &) = default;

88 7 ~AINode 0 516 112 15 AINode::~AINode 0 0 0
22
AINode::~AINode(void);

89 8 contains 0 4 112 16 AINode::contains 0 1 37 113
/**
 * This is a handy function which returns true if the passed position is
 * within the node's dimensions.
 */
40
bool AINode::contains(float x, float y);

90 5 Flock 0 260 113 12 Flock::Flock 0 2 38 39 0
225
explicit Flock::Flock(unsigned int flock_id, double vcone_angle, double vcone_radius, unsigned int separation_wt = 2, unsigned int cohesion_wt = 4, unsigned int alignment_wt = 1);
inline Flock::Flock(Flock const &) = default;

91 6 ~Flock 0 516 113 13 Flock::~Flock 0 0 0
20
Flock::~Flock(void);

92 11 add_ai_char 0 4 113 18 Flock::add_ai_char 0 1 40 113
// Function to add the ai characters to _ai_char_list.

/**
 * This function adds AI characters to the flock.
 */
46
void Flock::add_ai_char(AICharacter *ai_char);

93 6 get_id 0 4 113 13 Flock::get_id 0 1 41 50
// Function to access the private member flock_id.
33
unsigned int Flock::get_id(void);

94 7 AIWorld 0 260 114 16 AIWorld::AIWorld 0 2 42 43 0
86
AIWorld::AIWorld(NodePath render);
inline AIWorld::AIWorld(AIWorld const &) = default;

95 8 ~AIWorld 0 516 114 17 AIWorld::~AIWorld 0 0 0
24
AIWorld::~AIWorld(void);

96 11 add_ai_char 0 4 114 20 AIWorld::add_ai_char 0 1 44 0
46
void AIWorld::add_ai_char(AICharacter *ai_ch);

97 14 remove_ai_char 0 4 114 23 AIWorld::remove_ai_char 0 1 45 0
47
void AIWorld::remove_ai_char(std::string name);

98 9 add_flock 0 4 114 18 AIWorld::add_flock 0 1 46 163
/**
 * This function adds all the AI characters in the Flock object to the
 * AICharPool.  This function allows adding the AI characetrs as part of a
 * flock.
 */
38
void AIWorld::add_flock(Flock *flock);

99 9 flock_off 0 4 114 18 AIWorld::flock_off 0 1 47 103
/**
 * This function turns off the flock behavior temporarily.  Similar to pausing
 * the behavior.
 */
47
void AIWorld::flock_off(unsigned int flock_id);

100 8 flock_on 0 4 114 17 AIWorld::flock_on 0 1 48 53
/**
 * This function turns on the flock behavior.
 */
46
void AIWorld::flock_on(unsigned int flock_id);

101 12 remove_flock 0 4 114 21 AIWorld::remove_flock 0 1 49 63
/**
 * This function removes the flock behavior completely.
 */
50
void AIWorld::remove_flock(unsigned int flock_id);

102 9 get_flock 0 4 114 18 AIWorld::get_flock 0 1 50 74
/**
 * This function returns a handle to the Flock whose id is passed.
 */
48
Flock AIWorld::get_flock(unsigned int flock_id);

103 12 add_obstacle 0 4 114 21 AIWorld::add_obstacle 0 1 51 111
/**
 * This function adds the nodepath as an obstacle that is needed by the
 * obstacle avoidance behavior.
 */
46
void AIWorld::add_obstacle(NodePath obstacle);

104 15 remove_obstacle 0 4 114 24 AIWorld::remove_obstacle 0 1 52 123
/**
 * This function removes the nodepath from the obstacles list that is needed
 * by the obstacle avoidance behavior.
 */
49
void AIWorld::remove_obstacle(NodePath obstacle);

105 10 print_list 0 4 114 19 AIWorld::print_list 0 1 53 134
/**
 * This function prints the names of the AI characters that have been added to
 * the AIWorld.  Useful for debugging purposes.
 */
31
void AIWorld::print_list(void);

106 6 update 0 4 114 15 AIWorld::update 0 1 54 130
/**
 * The AIWorld update function calls the update function of all the AI
 * characters which have been added to the AIWorld.
 */
27
void AIWorld::update(void);

54
1 0 0 15 22 117 55 0 0 1 6 param0 0 115  
2 0 0 4 3 122 0 0 0 3 4 this 3 117  3 pos 1 118  7 seek_wt 5 121  
3 0 0 4 3 122 0 0 219 /**
 * This function activates seek and makes an object of the Seek class.  This
 * is the function we want the user to call for seek to be done.  This
 * function is overloaded to accept a NodePath or an LVecBase3.
 */ 3 4 this 3 117  13 target_object 1 123  7 seek_wt 5 121  
4 0 0 4 4 122 0 0 0 5 4 this 3 117  3 pos 1 118  14 panic_distance 5 125  14 relax_distance 5 125  7 flee_wt 5 121  
5 0 0 4 4 122 0 0 159 /**
 * This function activates flee_activate and creates an object of the Flee
 * class.  This function is overloaded to accept a NodePath or an LVecBase3.
 */ 5 4 this 3 117  13 target_object 1 123  14 panic_distance 5 125  14 relax_distance 5 125  7 flee_wt 5 121  
6 0 0 4 5 122 0 0 115 /**
 * This function activates pursue.  This is the function we want the user to
 * call for pursue to be done.
 */ 3 4 this 3 117  13 target_object 1 123  9 pursue_wt 5 121  
7 0 0 4 6 122 0 0 50 /**
 * This function activates evade_activate.
 */ 5 4 this 3 117  13 target_object 1 123  14 panic_distance 5 125  14 relax_distance 5 125  8 evade_wt 5 121  
8 0 0 4 7 122 0 0 117 /**
 * This function activates arrival.  This is the function we want the user to
 * call for arrival to be done.
 */ 2 4 this 3 117  8 distance 5 125  
9 0 0 4 8 122 0 0 113 /**
 * This function activates flock.  This is the function we want the user to
 * call for flock to be done.
 */ 2 4 this 3 117  8 flock_wt 1 121  
10 0 0 4 9 122 0 0 114 /**
 * This function activates wander.  This is the function we want the user to
 * call for flock to be done.
 */ 5 4 this 3 117  13 wander_radius 5 125  4 flag 5 126  3 aoe 5 125  13 wander_weight 5 121  
11 0 0 4 10 122 0 0 169 /**
 * This function activates obstacle avoidance for a given character.  This is
 * the function we want the user to call for obstacle avoidance to be
 * performed.
 */ 2 4 this 3 117  13 feeler_length 5 121  
12 0 0 4 11 122 0 0 120 /**
 * This function activates path following.  This is the function we want the
 * user to call for path following.
 */ 2 4 this 3 117  9 follow_wt 5 121  
13 0 0 4 12 122 0 0 62 /**
 * This function adds positions to the path to follow.
 */ 2 4 this 3 117  3 pos 1 118  
14 0 0 4 13 122 0 0 0 2 4 this 3 117  4 type 5 127  
15 0 0 4 14 122 0 0 123 /**
 * This function activates path finding in the character.  This function
 * accepts the meshdata in .csv format.
 *
 */ 2 4 this 3 117  16 navmesh_filename 1 127  
16 0 0 4 15 122 0 0 225 /**
 * This function checks for the source and target in the navigation mesh for
 * its availability and then finds the best path via the A* algorithm Then it
 * calls the path follower to make the object follow the path.
 */ 3 4 this 3 117  3 pos 1 118  4 type 5 127  
17 0 0 4 15 122 0 0 225 /**
 * This function checks for the source and target in the navigation mesh for
 * its availability and then finds the best path via the A* algorithm Then it
 * calls the path follower to make the object follow the path.
 */ 3 4 this 3 117  6 target 1 123  4 type 5 127  
18 0 0 4 16 122 0 0 262 /**
 * This function allows the user to dynamically add obstacles to the game
 * environment.  The function will update the nodes within the bounding volume
 * of the obstacle as non-traversable.  Hence will not be considered by the
 * pathfinding algorithm.
 */ 2 4 this 3 117  8 obstacle 1 123  
19 0 0 4 17 122 0 0 98 /**
 * This function starts the pathfinding obstacle navigation for the passed in
 * obstacle.
 */ 2 4 this 3 117  8 obstacle 1 123  
20 0 0 4 18 122 0 0 59 /**
 * This function removes individual or all the AIs.
 */ 2 4 this 3 117  7 ai_type 1 127  
21 0 0 4 19 122 0 0 58 /**
 * This function pauses individual or all the AIs.
 */ 2 4 this 3 117  7 ai_type 1 127  
22 0 0 4 20 122 0 0 58 /**
 * This function resumes individual or all the AIs
 */ 2 4 this 3 117  7 ai_type 1 127  
23 0 0 6 21 127 0 0 149 /**
 * This function returns the status of an AI Type whether it is active, paused
 * or disabled.  It returns -1 if an invalid string is passed.
 */ 2 4 this 3 117  7 ai_type 1 127  
24 0 0 15 34 130 0 0 0 1 6 param0 0 128  
25 0 0 7 34 130 0 0 0 5 10 model_name 1 127  8 model_np 1 123  4 mass 1 125  10 movt_force 1 125  9 max_force 1 125  
26 0 0 6 25 125 0 0 0 1 4 this 3 130  
27 0 0 4 26 122 0 0 0 2 4 this 3 130  1 m 1 125  
28 0 0 7 27 118 0 0 0 1 4 this 3 130  
29 0 0 6 28 125 0 0 0 1 4 this 3 130  
30 0 0 4 29 122 0 0 0 2 4 this 3 130  9 max_force 1 125  
31 0 0 7 30 123 0 0 0 1 4 this 3 130  
32 0 0 4 31 122 0 0 0 2 4 this 3 130  2 np 1 123  
33 0 0 6 32 117 0 0 0 1 4 this 3 130  
34 0 0 4 33 122 0 0 74 // This function is used to enable or disable the guides for path finding. 2 4 this 3 130  8 pf_guide 1 131  
35 0 0 15 40 134 88 0 0 1 6 param0 0 132  
36 0 0 7 40 134 88 0 0 6 6 grid_x 1 126  6 grid_y 1 126  3 pos 1 118  1 w 1 121  1 l 1 121  1 h 1 121  
37 0 0 6 42 131 0 0 113 /**
 * This is a handy function which returns true if the passed position is
 * within the node's dimensions.
 */ 3 4 this 3 134  1 x 1 121  1 y 1 121  
38 0 0 15 44 137 91 0 0 1 6 param0 0 135  
39 0 0 7 44 137 91 0 0 6 8 flock_id 1 138  11 vcone_angle 1 125  12 vcone_radius 1 125  13 separation_wt 5 138  11 cohesion_wt 5 138  12 alignment_wt 5 138  
40 0 0 4 46 122 0 0 57 /**
 * This function adds AI characters to the flock.
 */ 2 4 this 3 137  7 ai_char 1 130  
41 0 0 6 47 138 0 0 50 // Function to access the private member flock_id. 1 4 this 3 137  
42 0 0 15 49 141 95 0 0 1 6 param0 0 139  
43 0 0 23 49 141 95 0 0 1 6 render 1 123  
44 0 0 4 51 122 0 0 0 2 4 this 3 141  5 ai_ch 1 130  
45 0 0 4 52 122 0 0 0 2 4 this 3 141  4 name 1 127  
46 0 0 4 53 122 0 0 163 /**
 * This function adds all the AI characters in the Flock object to the
 * AICharPool.  This function allows adding the AI characetrs as part of a
 * flock.
 */ 2 4 this 3 141  5 flock 1 137  
47 0 0 4 54 122 0 0 103 /**
 * This function turns off the flock behavior temporarily.  Similar to pausing
 * the behavior.
 */ 2 4 this 3 141  8 flock_id 1 138  
48 0 0 4 55 122 0 0 53 /**
 * This function turns on the flock behavior.
 */ 2 4 this 3 141  8 flock_id 1 138  
49 0 0 4 56 122 0 0 63 /**
 * This function removes the flock behavior completely.
 */ 2 4 this 3 141  8 flock_id 1 138  
50 0 0 7 57 137 91 0 74 /**
 * This function returns a handle to the Flock whose id is passed.
 */ 2 4 this 3 141  8 flock_id 1 138  
51 0 0 4 58 122 0 0 111 /**
 * This function adds the nodepath as an obstacle that is needed by the
 * obstacle avoidance behavior.
 */ 2 4 this 3 141  8 obstacle 1 123  
52 0 0 4 59 122 0 0 123 /**
 * This function removes the nodepath from the obstacles list that is needed
 * by the obstacle avoidance behavior.
 */ 2 4 this 3 141  8 obstacle 1 123  
53 0 0 4 60 122 0 0 134 /**
 * This function prints the names of the AI characters that have been added to
 * the AIWorld.  Useful for debugging purposes.
 */ 1 4 this 3 141  
54 0 0 4 61 122 0 0 130 /**
 * The AIWorld update function calls the update function of all the AI
 * characters which have been added to the AIWorld.
 */ 1 4 this 3 141  
35
107 11 AIBehaviors 0 26625 11 AIBehaviors 11 AIBehaviors 0 0 0 1 75 55 0 19 56 57 58 59 60 61 62 63 64 65 66 67 68 69 70 71 72 73 74 0 0 0 0 0 618
/**
 * This class implements all the steering behaviors of the AI framework, such
 * as seek, flee, pursue, evade, wander and flock.  Each steering behavior has
 * a weight which is used when more than one type of steering behavior is
 * acting on the same ai character.  The weight decides the contribution of
 * each type of steering behavior.  The AICharacter class has a handle to an
 * object of this class and this allows to invoke the steering behaviors via
 * the AICharacter.  This class also provides functionality such as pausing,
 * resuming and removing the AI behaviors of an AI character at anytime.
 */

108 11 AICharacter 0 75777 11 AICharacter 11 AICharacter 0 0 0 1 85 0 1 142 9 76 77 78 79 80 81 82 83 84 0 0 1 0 109 0 0 0 0 0

109 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

110 6 string 0 2105344 11 std::string 11 std::string 0 0 111 0 0 0 0 0 0 0 0 0 0

111 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

112 6 AINode 0 26625 6 AINode 6 AINode 0 0 0 1 87 88 0 1 89 0 0 0 0 0 365
/**
 * This class is used to assign the nodes on the mesh.  It holds all the data
 * necessary to compute A* algorithm.  It also maintains a lot of vital
 * information such as the neighbor nodes of each node and also its position
 * on the mesh.  Note: The Mesh Generator which is a standalone tool makes use
 * of this class to generate the nodes on the mesh.
 */

113 5 Flock 0 26625 5 Flock 5 Flock 0 0 0 1 90 91 0 2 92 93 0 0 0 0 0 114
/**
 * This class is used to define the flock attributes and the AI characters
 * which are part of the flock.
 */

114 7 AIWorld 0 26625 7 AIWorld 7 AIWorld 0 0 0 1 94 95 0 11 96 97 98 99 100 101 102 103 104 105 106 0 0 0 0 0 329
/**
 * A class that implements the virtual AI world which keeps track of the AI
 * characters active at any given time.  It contains a linked list of AI
 * characters, obstactle data and unique name for each character.  It also
 * updates each characters state.  The AI characters can also be added to the
 * world as flocks.
 */

115 19 AIBehaviors const * 0 8576 19 AIBehaviors const * 19 AIBehaviors const * 0 0 116 0 0 0 0 0 0 0 0 0 0

116 17 AIBehaviors const 0 8832 17 AIBehaviors const 17 AIBehaviors const 0 0 107 0 0 0 0 0 0 0 0 0 0

117 13 AIBehaviors * 0 8576 13 AIBehaviors * 13 AIBehaviors * 0 0 107 0 0 0 0 0 0 0 0 0 0

118 11 LVecBase3 * 0 8576 11 LVecBase3 * 11 LVecBase3 * 0 0 119 0 0 0 0 0 0 0 0 0 0

119 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 120 0 0 0 0 0 0 0 0 0 0

120 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

121 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

122 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

123 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 124 0 0 0 0 0 0 0 0 0 0

124 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

125 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

126 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

127 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

128 19 AICharacter const * 0 8576 19 AICharacter const * 19 AICharacter const * 0 0 129 0 0 0 0 0 0 0 0 0 0

129 17 AICharacter const 0 8832 17 AICharacter const 17 AICharacter const 0 0 108 0 0 0 0 0 0 0 0 0 0

130 13 AICharacter * 0 8576 13 AICharacter * 13 AICharacter * 0 0 108 0 0 0 0 0 0 0 0 0 0

131 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

132 14 AINode const * 0 8576 14 AINode const * 14 AINode const * 0 0 133 0 0 0 0 0 0 0 0 0 0

133 12 AINode const 0 8832 12 AINode const 12 AINode const 0 0 112 0 0 0 0 0 0 0 0 0 0

134 8 AINode * 0 8576 8 AINode * 8 AINode * 0 0 112 0 0 0 0 0 0 0 0 0 0

135 13 Flock const * 0 8576 13 Flock const * 13 Flock const * 0 0 136 0 0 0 0 0 0 0 0 0 0

136 11 Flock const 0 8832 11 Flock const 11 Flock const 0 0 113 0 0 0 0 0 0 0 0 0 0

137 7 Flock * 0 8576 7 Flock * 7 Flock * 0 0 113 0 0 0 0 0 0 0 0 0 0

138 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

139 15 AIWorld const * 0 8576 15 AIWorld const * 15 AIWorld const * 0 0 140 0 0 0 0 0 0 0 0 0 0

140 13 AIWorld const 0 8832 13 AIWorld const 13 AIWorld const 0 0 114 0 0 0 0 0 0 0 0 0 0

141 9 AIWorld * 0 8576 9 AIWorld * 9 AIWorld * 0 0 114 0 0 0 0 0 0 0 0 0 0

0
1
142 4 name 0 2 110 86 0 0 0 0 0 0 0 17 AICharacter::name 0

0
