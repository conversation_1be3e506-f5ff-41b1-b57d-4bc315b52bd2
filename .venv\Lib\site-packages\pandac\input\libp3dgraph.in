1730992849
3 3
11 libp3dgraph 4 YJkp 12 panda3d.core 
12
12 18 DataGraphTraverser 0 260 24 38 DataGraphTraverser::DataGraphTraverser 0 2 1 2 10
/**
 *
 */
188
explicit DataGraphTraverser::DataGraphTraverser(Thread *current_thread = Thread::get_current_thread());
inline DataGraphTraverser::DataGraphTraverser(DataGraphTraverser const &) = default;

13 19 ~DataGraphTraverser 0 516 24 39 DataGraphTraverser::~DataGraphTraverser 0 0 10
/**
 *
 */
46
DataGraphTraverser::~DataGraphTraverser(void);

14 18 get_current_thread 0 4 24 38 DataGraphTraverser::get_current_thread 0 1 3 109
/**
 * Returns the currently-executing thread object, as passed to the
 * DataGraphTraverser constructor.
 */
66
inline Thread *DataGraphTraverser::get_current_thread(void) const;

15 8 traverse 0 4 24 28 DataGraphTraverser::traverse 0 1 4 77
/**
 * Starts the traversal of the data graph at the indicated root node.
 */
51
void DataGraphTraverser::traverse(PandaNode *node);

16 14 traverse_below 0 4 24 34 DataGraphTraverser::traverse_below 0 1 5 170
/**
 * Continues the traversal to all the children of the indicated node, passing
 * in the given data, without actually calling transmit_data() on the given
 * node.
 */
89
void DataGraphTraverser::traverse_below(PandaNode *node, DataNodeTransmit const &output);

17 17 collect_leftovers 0 4 24 37 DataGraphTraverser::collect_leftovers 0 1 6 184
/**
 * Pick up any nodes that didn't get completely traversed.  These must be
 * nodes that have multiple parents, with at least one parent completely
 * outside of the data graph.
 */
49
void DataGraphTraverser::collect_leftovers(void);

18 8 DataNode 0 260 25 18 DataNode::DataNode 0 1 7 22
/**
 *
 */

/**
 *
 */
60
inline explicit DataNode::DataNode(std::string const &name);

19 12 write_inputs 0 4 25 22 DataNode::write_inputs 0 1 8 109
/**
 * Writes to the indicated ostream a list of all the inputs this DataNode
 * might expect to receive.
 */
53
void DataNode::write_inputs(std::ostream &out) const;

20 13 write_outputs 0 4 25 23 DataNode::write_outputs 0 1 9 101
/**
 * Writes to the indicated ostream a list of all the outputs this DataNode
 * might generate.
 */
54
void DataNode::write_outputs(std::ostream &out) const;

21 17 write_connections 0 4 25 27 DataNode::write_connections 0 1 10 134
/**
 * Writes to the indicated ostream a list of all the connections currently
 * showing between this DataNode and its parent(s).
 */
58
void DataNode::write_connections(std::ostream &out) const;

22 14 get_class_type 0 4 25 24 DataNode::get_class_type 0 1 11 0
49
static TypeHandle DataNode::get_class_type(void);

23 9 ~DataNode 0 516 25 19 DataNode::~DataNode 0 0 0
26
DataNode::~DataNode(void);

11
1 0 0 15 2 29 13 0 0 1 6 param0 0 27  
2 0 0 7 2 29 13 0 10 /**
 *
 */ 1 14 current_thread 5 30  
3 0 0 7 4 30 0 0 109 /**
 * Returns the currently-executing thread object, as passed to the
 * DataGraphTraverser constructor.
 */ 1 4 this 3 27  
4 0 0 4 5 33 0 0 77 /**
 * Starts the traversal of the data graph at the indicated root node.
 */ 2 4 this 3 29  4 node 1 32  
5 0 0 4 6 33 0 0 170 /**
 * Continues the traversal to all the children of the indicated node, passing
 * in the given data, without actually calling transmit_data() on the given
 * node.
 */ 3 4 this 3 29  4 node 1 32  6 output 1 34  
6 0 0 4 7 33 0 0 184 /**
 * Pick up any nodes that didn't get completely traversed.  These must be
 * nodes that have multiple parents, with at least one parent completely
 * outside of the data graph.
 */ 1 4 this 3 29  
7 0 0 7 10 38 23 0 10 /**
 *
 */ 1 4 name 1 37  
8 0 0 4 11 33 0 0 109 /**
 * Writes to the indicated ostream a list of all the inputs this DataNode
 * might expect to receive.
 */ 2 4 this 3 39  3 out 1 41  
9 0 0 4 12 33 0 0 101 /**
 * Writes to the indicated ostream a list of all the outputs this DataNode
 * might generate.
 */ 2 4 this 3 39  3 out 1 41  
10 0 0 4 13 33 0 0 134 /**
 * Writes to the indicated ostream a list of all the connections currently
 * showing between this DataNode and its parent(s).
 */ 2 4 this 3 39  3 out 1 41  
11 0 0 7 14 44 0 0 0 0 
21
24 18 DataGraphTraverser 0 26625 18 DataGraphTraverser 18 DataGraphTraverser 0 0 0 1 12 13 0 4 14 15 16 17 0 0 0 0 0 247
/**
 * This object supervises the traversal of the data graph and the moving of
 * data from one DataNode to its children.  The data graph is used to manage
 * data from input devices, etc.  See the overview of the data graph in
 * dataNode.h.
 */

25 8 DataNode 0 141313 8 DataNode 8 DataNode 0 0 0 1 18 23 0 4 19 20 21 22 0 0 1 0 26 0 0 0 0 422
/**
 * The fundamental type of node for the data graph.  The DataNode class is
 * itself primarily intended as an abstract class; it defines no inputs and no
 * outputs.  Most kinds of data nodes will derive from this to specify the
 * inputs and outputs in the constructor.
 *
 * DataNode does not attempt to cycle its data with a PipelineCycler.  The
 * data graph is intended to be used only within a single thread.
 */

26 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

27 26 DataGraphTraverser const * 0 8576 26 DataGraphTraverser const * 26 DataGraphTraverser const * 0 0 28 0 0 0 0 0 0 0 0 0 0

28 24 DataGraphTraverser const 0 8832 24 DataGraphTraverser const 24 DataGraphTraverser const 0 0 24 0 0 0 0 0 0 0 0 0 0

29 20 DataGraphTraverser * 0 8576 20 DataGraphTraverser * 20 DataGraphTraverser * 0 0 24 0 0 0 0 0 0 0 0 0 0

30 8 Thread * 0 8576 8 Thread * 8 Thread * 0 0 31 0 0 0 0 0 0 0 0 0 0

31 6 Thread 0 2048 6 Thread 6 Thread 0 0 0 0 0 0 0 0 0 0 0 0 396
/**
 * A thread; that is, a lightweight process.  This is an abstract base class;
 * to use it, you must subclass from it and redefine thread_main().
 *
 * The thread itself will keep a reference count on the Thread object while it
 * is running; when the thread returns from its root function, the Thread
 * object will automatically be destructed if no other pointers are
 * referencing it.
 */

32 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 26 0 0 0 0 0 0 0 0 0 0

33 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

34 24 DataNodeTransmit const * 0 8576 24 DataNodeTransmit const * 24 DataNodeTransmit const * 0 0 35 0 0 0 0 0 0 0 0 0 0

35 22 DataNodeTransmit const 0 8832 22 DataNodeTransmit const 22 DataNodeTransmit const 0 0 36 0 0 0 0 0 0 0 0 0 0

36 16 DataNodeTransmit 0 1050624 16 DataNodeTransmit 16 DataNodeTransmit 0 0 0 0 0 0 0 0 0 0 0 0 192
/**
 * Encapsulates the data generated from (or sent into) any particular
 * DataNode.  This is basically just an array of EventParameters, one for each
 * registered input or output wire.
 */

37 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

38 10 DataNode * 0 8576 10 DataNode * 10 DataNode * 0 0 25 0 0 0 0 0 0 0 0 0 0

39 16 DataNode const * 0 8576 16 DataNode const * 16 DataNode const * 0 0 40 0 0 0 0 0 0 0 0 0 0

40 14 DataNode const 0 8832 14 DataNode const 14 DataNode const 0 0 25 0 0 0 0 0 0 0 0 0 0

41 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 42 0 0 0 0 0 0 0 0 0 0

42 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

43 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

44 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 43 0 0 0 0 0 0 0 0 0 0

0
0
0
