'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_ARB_buffer_region'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_ARB_buffer_region',error_checker=_errors._error_checker)
WGL_BACK_COLOR_BUFFER_BIT_ARB=_C('WGL_BACK_COLOR_BUFFER_BIT_ARB',0x00000002)
WGL_DEPTH_BUFFER_BIT_ARB=_C('WGL_DEPTH_BUFFER_BIT_ARB',0x00000004)
WGL_FRONT_COLOR_BUFFER_BIT_ARB=_C('WGL_FRONT_COLOR_BUFFER_BIT_ARB',0x00000001)
WGL_STENCIL_BUFFER_BIT_ARB=_C('WGL_STENCIL_BUFFER_BIT_ARB',0x00000008)
@_f
@_p.types(_cs.HANDLE,_cs.HDC,_cs.c_int,_cs.UINT)
def wglCreateBufferRegionARB(hDC,iLayerPlane,uType):pass
@_f
@_p.types(_cs.VOID,_cs.HANDLE)
def wglDeleteBufferRegionARB(hRegion):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int)
def wglRestoreBufferRegionARB(hRegion,x,y,width,height,xSrc,ySrc):pass
@_f
@_p.types(_cs.BOOL,_cs.HANDLE,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int)
def wglSaveBufferRegionARB(hRegion,x,y,width,height):pass
