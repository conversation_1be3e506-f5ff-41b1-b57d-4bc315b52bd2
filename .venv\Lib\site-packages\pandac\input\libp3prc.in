1730992849
3 3
8 libp3prc 4 Flsb 12 panda3d.core 
383
385 11 ConfigFlags 0 260 768 24 ConfigFlags::ConfigFlags 0 2 1 2 0
112
inline ConfigFlags::ConfigFlags(void) = default;
inline ConfigFlags::ConfigFlags(ConfigFlags const &) = default;

386 12 ~ConfigFlags 0 516 768 25 ConfigFlags::~ConfigFlags 0 0 0
32
ConfigFlags::~ConfigFlags(void);

387 16 get_default_page 0 4 771 28 ConfigPage::get_default_page 0 1 3 136
/**
 * Returns a pointer to the global "default page".  This is the ConfigPage
 * that lists all variables' original default values.
 */
54
static ConfigPage *ConfigPage::get_default_page(void);

388 14 get_local_page 0 4 771 26 ConfigPage::get_local_page 0 1 4 191
/**
 * Returns a pointer to the global "local page".  This is the ConfigPage that
 * lists the locally-assigned values for any variables in the world that have
 * such a local assignment.
 */
52
static ConfigPage *ConfigPage::get_local_page(void);

389 8 get_name 0 4 771 20 ConfigPage::get_name 0 1 5 115
/**
 * Returns the name of the page.  If the page was loaded from a .prc file,
 * this is usually the filename.
 */
59
inline std::string const &ConfigPage::get_name(void) const;

390 10 is_special 0 4 771 22 ConfigPage::is_special 0 1 6 248
/**
 * Returns true if this is the special "default" or "local" page, or false if
 * it is an ordinary page, e.g.  an implicit page loaded from a prc file at
 * startup, or an explicit page created by
 * ConfigPageManager::make_explicit_page().
 */
47
inline bool ConfigPage::is_special(void) const;

391 11 is_implicit 0 4 771 23 ConfigPage::is_implicit 0 1 7 180
/**
 * Returns true if the page was loaded by implicitly searching the config path
 * on startup, or false if it was explicitly loaded by dynamic code after
 * initial startup.
 */
48
inline bool ConfigPage::is_implicit(void) const;

392 8 set_sort 0 4 771 20 ConfigPage::set_sort 0 1 8 363
/**
 * Changes the explicit sort order of this particular ConfigPage.  Lower-
 * numbered pages supercede higher-numbered pages.  Initially, all explicitly-
 * loaded pages have sort value 0, and implicitly-loaded pages (found on disk)
 * have sort value 10; you may set an individual page higher or lower to
 * influence its priority relative to other pages.
 */
36
void ConfigPage::set_sort(int sort);

393 8 get_sort 0 4 771 20 ConfigPage::get_sort 0 1 9 93
/**
 * Returns the explicit sort order of this particular ConfigPage.  See
 * set_sort().
 */
44
inline int ConfigPage::get_sort(void) const;

394 12 get_page_seq 0 4 771 24 ConfigPage::get_page_seq 0 1 10 413
/**
 * Returns the sequence number of the page.
 *
 * Sequence numbers for a particular class (implicit vs.  explicit) of pages
 * are assigned as each page is loaded; each page is given a higher sequence
 * number than all the pages loaded before it.
 *
 * The implicit_load pages, which are discovered in the file system
 * automatically, have a different set of sequence numbers than the explicit
 * pages.
 */
48
inline int ConfigPage::get_page_seq(void) const;

395 15 get_trust_level 0 4 771 27 ConfigPage::get_trust_level 0 1 11 302
/**
 * Returns the trust level associated with this page.  An untrusted page is
 * trust level 0; if the page was loaded from a signed .prc file, its trust
 * level is the index number of the certificate that signed it.  Generally, a
 * higher trust level value represents a greater level of trust.
 */
51
inline int ConfigPage::get_trust_level(void) const;

396 15 set_trust_level 0 4 771 27 ConfigPage::set_trust_level 0 1 12 195
/**
 * Explicitly sets the trust level on this particular page.  Note that any
 * subsequent changes to the page, or to any variable declarations on it, will
 * reset the trust level to zero.
 */
57
inline void ConfigPage::set_trust_level(int trust_level);

397 13 get_signature 0 4 771 25 ConfigPage::get_signature 0 1 13 153
/**
 * Returns the raw binary signature that was found in the prc file, if any.
 * This method is probably not terribly useful for most applications.
 */
64
inline std::string const &ConfigPage::get_signature(void) const;

398 5 clear 0 4 771 17 ConfigPage::clear 0 1 14 57
/**
 * Removes all of the declarations from the page.
 */
29
void ConfigPage::clear(void);

399 8 read_prc 0 4 771 20 ConfigPage::read_prc 0 1 15 392
/**
 * Reads the contents of a complete prc file, as returned by the indicated
 * istream, into the current page file.  Returns true on success, or false on
 * some I/O error.
 *
 * This is a low-level interface.  Normally you do not need to call it
 * directly.  See the global functions load_prc_file() and unload_prc_file(),
 * defined in panda/src/putil, for a higher-level interface.
 */
44
bool ConfigPage::read_prc(std::istream &in);

400 18 read_encrypted_prc 0 4 771 30 ConfigPage::read_encrypted_prc 0 1 16 155
/**
 * Automatically decrypts and reads the stream, given the indicated password.
 * Note that if the password is incorrect, the result may be garbage.
 */
83
bool ConfigPage::read_encrypted_prc(std::istream &in, std::string const &password);

401 16 make_declaration 0 4 771 28 ConfigPage::make_declaration 0 2 17 18 168
/**
 * Adds the indicated variable/value pair as a new declaration on the page.
 */

/**
 * Adds the indicated variable/value pair as a new declaration on the page.
 */
208
ConfigDeclaration *ConfigPage::make_declaration(std::string const &variable, std::string const &value);
ConfigDeclaration *ConfigPage::make_declaration(ConfigVariableCore *variable, std::string const &value);

402 18 delete_declaration 0 4 771 30 ConfigPage::delete_declaration 0 1 19 171
/**
 * Removes the indicated declaration from the page and deletes it.  Returns
 * true if the declaration is successfully removed, false if it was not on the
 * page.
 */
61
bool ConfigPage::delete_declaration(ConfigDeclaration *decl);

403 20 get_num_declarations 0 4 771 32 ConfigPage::get_num_declarations 0 1 20 58
/**
 * Returns the number of declarations on the page.
 */
57
std::size_t ConfigPage::get_num_declarations(void) const;

404 15 get_declaration 0 4 771 27 ConfigPage::get_declaration 0 1 21 51
/**
 * Returns the nth declaration on the page.
 */
74
ConfigDeclaration const *ConfigPage::get_declaration(std::size_t n) const;

405 18 modify_declaration 0 4 771 30 ConfigPage::modify_declaration 0 1 22 180
/**
 * Returns a modifiable pointer to the nth declaration on the page.  Any
 * modifications will appear in the output, if the page is written out with
 * ConfigPage::write().
 */
65
ConfigDeclaration *ConfigPage::modify_declaration(std::size_t n);

406 17 get_variable_name 0 4 771 29 ConfigPage::get_variable_name 0 1 23 73
/**
 * Returns the variable named by the nth declaration on the page.
 */
63
std::string ConfigPage::get_variable_name(std::size_t n) const;

407 16 get_string_value 0 4 771 28 ConfigPage::get_string_value 0 1 24 73
/**
 * Returns the value assigned by the nth declaration on the page.
 */
62
std::string ConfigPage::get_string_value(std::size_t n) const;

408 16 is_variable_used 0 4 771 28 ConfigPage::is_variable_used 0 1 25 105
/**
 * Returns true if the nth active variable on the page has been used by code,
 * false otherwise.
 */
55
bool ConfigPage::is_variable_used(std::size_t n) const;

409 8 get_page 0 4 778 27 ConfigDeclaration::get_page 0 1 29 67
/**
 * Returns the page on which this declaration can be found.
 */
59
inline ConfigPage *ConfigDeclaration::get_page(void) const;

410 12 get_variable 0 4 778 31 ConfigDeclaration::get_variable 0 1 30 148
/**
 * Returns the variable that this declaration names.  This variable may or may
 * not have been defined by the time the declaration is read.
 */
71
inline ConfigVariableCore *ConfigDeclaration::get_variable(void) const;

411 8 get_name 0 4 781 28 ConfigVariableCore::get_name 0 1 53 44
/**
 * Returns the name of the variable.
 */
67
inline std::string const &ConfigVariableCore::get_name(void) const;

412 7 is_used 0 4 781 27 ConfigVariableCore::is_used 0 1 54 118
/**
 * Returns true if the variable has been referenced by a ConfigVariable
 * somewhere in code, false otherwise.
 */
52
inline bool ConfigVariableCore::is_used(void) const;

413 14 get_value_type 0 4 781 34 ConfigVariableCore::get_value_type 0 1 55 125
/**
 * Returns the stated type of this variable.  If the variable has not yet been
 * defined, this will be VT_undefined.
 */
77
inline ConfigFlags::ValueType ConfigVariableCore::get_value_type(void) const;

414 15 get_description 0 4 781 35 ConfigVariableCore::get_description 0 1 56 82
/**
 * Returns the brief description of this variable, if it has been defined.
 */
74
inline std::string const &ConfigVariableCore::get_description(void) const;

415 9 get_flags 0 4 781 29 ConfigVariableCore::get_flags 0 1 57 250
/**
 * Returns the flags value as set by set_flags().  This includes the trust
 * level and some other settings.  See the individual methods is_closed(),
 * get_trust_level(), etc.  to pull out the semantic meaning of these flags
 * individually.
 */
53
inline int ConfigVariableCore::get_flags(void) const;

416 9 is_closed 0 4 781 29 ConfigVariableCore::is_closed 0 1 58 413
/**
 * Returns true if the variable is not trusted by any prc file (and hence
 * cannot be modified from its compiled-in default value), or false for the
 * normal case, in which the variable can be modified by any prc file at or
 * above its trust level (see get_trust_level()).
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */
54
inline bool ConfigVariableCore::is_closed(void) const;

417 15 get_trust_level 0 4 781 35 ConfigVariableCore::get_trust_level 0 1 59 479
/**
 * Returns the minimum trust_level a prc file must demonstrate in order to
 * redefine the value for this variable.  Arguably, this should be called the
 * "mistrust level", since the larger the value, the more suspicious we are of
 * prc files.  This value is not used if is_closed() returns true, which
 * indicates no file may be trusted.
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */
59
inline int ConfigVariableCore::get_trust_level(void) const;

418 10 is_dynamic 0 4 781 30 ConfigVariableCore::is_dynamic 0 1 60 232
/**
 * Returns true if the variable was indicated as "dynamic" by its constructor,
 * indicating that its name was dynamically generated, possibly from a large
 * pool, and it should not be listed along with the other variables.
 */
55
inline bool ConfigVariableCore::is_dynamic(void) const;

419 17 get_default_value 0 4 781 37 ConfigVariableCore::get_default_value 0 1 61 137
/**
 * Returns the default variable specified for this variable.  If the variable
 * has not yet been defined, this will return NULL.
 */
82
inline ConfigDeclaration const *ConfigVariableCore::get_default_value(void) const;

420 14 set_value_type 0 4 781 34 ConfigVariableCore::set_value_type 0 1 62 212
/**
 * Specifies the type of this variable.  See get_value_type().  It is not an
 * error to call this multiple times, but if the value changes once
 * get_declaration() has been called, a warning is printed.
 */
75
void ConfigVariableCore::set_value_type(ConfigFlags::ValueType value_type);

421 9 set_flags 0 4 781 29 ConfigVariableCore::set_flags 0 1 63 214
/**
 * Specifies the trust level of this variable.  See get_flags().  It is not an
 * error to call this multiple times, but if the value changes once
 * get_declaration() has been called, a warning is printed.
 */
46
void ConfigVariableCore::set_flags(int flags);

422 15 set_description 0 4 781 35 ConfigVariableCore::set_description 0 1 64 232
/**
 * Specifies the one-line description of this variable.  See
 * get_description().  It is not an error to call this multiple times, but if
 * the value changes once get_declaration() has been called, a warning is
 * printed.
 */
73
void ConfigVariableCore::set_description(std::string const &description);

423 17 set_default_value 0 4 781 37 ConfigVariableCore::set_default_value 0 1 65 97
/**
 * Specifies the default value for this variable if it is not defined in any
 * prc file.
 */
77
void ConfigVariableCore::set_default_value(std::string const &default_value);

424 8 set_used 0 4 781 28 ConfigVariableCore::set_used 0 1 66 75
/**
 * Marks that the variable has been "declared" by a ConfigVariable.
 */
47
inline void ConfigVariableCore::set_used(void);

425 16 make_local_value 0 4 781 36 ConfigVariableCore::make_local_value 0 1 67 324
/**
 * Creates a new local value for this variable, if there is not already one
 * specified.  This will shadow any values defined in the various .prc files.
 *
 * If there is already a local value defined for this variable, simply returns
 * that one.
 *
 * Use clear_local_value() to remove the local value definition.
 */
62
ConfigDeclaration *ConfigVariableCore::make_local_value(void);

426 17 clear_local_value 0 4 781 37 ConfigVariableCore::clear_local_value 0 1 68 240
/**
 * Removes the local value defined for this variable, and allows its value to
 * be once again retrieved from the .prc files.
 *
 * Returns true if the value was successfully removed, false if it did not
 * exist in the first place.
 */
49
bool ConfigVariableCore::clear_local_value(void);

427 15 has_local_value 0 4 781 35 ConfigVariableCore::has_local_value 0 1 69 148
/**
 * Returns true if this variable's value has been shadowed by a local
 * assignment (as created via make_local_value()), or false otherwise.
 */
60
inline bool ConfigVariableCore::has_local_value(void) const;

428 9 has_value 0 4 781 29 ConfigVariableCore::has_value 0 1 70 149
/**
 * Returns true if this variable has an explicit value, either from a prc file
 * or locally set, or false if variable has its default value.
 */
47
bool ConfigVariableCore::has_value(void) const;

429 20 get_num_declarations 0 4 781 40 ConfigVariableCore::get_num_declarations 0 1 71 194
/**
 * Returns the number of declarations that contribute to this variable's
 * value.  If the variable has been defined, this will always be at least 1
 * (for the default value, at least).
 */
65
std::size_t ConfigVariableCore::get_num_declarations(void) const;

430 15 get_declaration 0 4 781 35 ConfigVariableCore::get_declaration 0 1 72 285
/**
 * Returns the nth declarations that contributes to this variable's value.
 * The declarations are arranged in order such that earlier declarations
 * shadow later declarations; thus, get_declaration(0) is always defined and
 * always returns the current value of the variable.
 */
82
ConfigDeclaration const *ConfigVariableCore::get_declaration(std::size_t n) const;

431 18 get_num_references 0 4 781 38 ConfigVariableCore::get_num_references 0 1 73 157
/**
 * Returns the number of prc files that reference this variable.  This is not
 * exactly the same as the number of declarations; see get_reference().
 */
70
inline std::size_t ConfigVariableCore::get_num_references(void) const;

432 13 get_reference 0 4 781 33 ConfigVariableCore::get_reference 0 1 74 335
/**
 * Returns the nth declaration in a prc file that references this variable.
 * This is similar, but not identical to, get_declaration().  The difference
 * is that this will list *only* true references in a prc file, and will not
 * list default values or locally-assigned values; it also will list even the
 * untrusted files.
 */
87
inline ConfigDeclaration const *ConfigVariableCore::get_reference(std::size_t n) const;

433 26 get_num_trusted_references 0 4 781 46 ConfigVariableCore::get_num_trusted_references 0 1 75 115
/**
 * Returns the number of trusted prc files that reference this variable.  See
 * also get_num_references().
 */
78
inline std::size_t ConfigVariableCore::get_num_trusted_references(void) const;

434 21 get_trusted_reference 0 4 781 41 ConfigVariableCore::get_trusted_reference 0 1 76 428
/**
 * Returns the nth declaration in a trusted prc file that references this
 * variable.  This is similar, but not identical to, get_declaration().  The
 * difference is that this will list *only* true references in a prc file, and
 * will not list default values or locally-assigned values.
 *
 * This is also similar to get_reference(), except that it only lists the
 * trusted declarations, omitting the untrusted ones.
 */
95
inline ConfigDeclaration const *ConfigVariableCore::get_trusted_reference(std::size_t n) const;

435 25 get_num_unique_references 0 4 781 45 ConfigVariableCore::get_num_unique_references 0 1 77 107
/**
 * Returns the number of trusted, unique (by string value) values there exist
 * for this variable.
 */
77
inline std::size_t ConfigVariableCore::get_num_unique_references(void) const;

436 20 get_unique_reference 0 4 781 40 ConfigVariableCore::get_unique_reference 0 1 78 157
/**
 * Returns the nth trusted, unique value for this variable.  This is similar
 * to get_trusted_reference(), except that duplicate values are removed.
 */
94
inline ConfigDeclaration const *ConfigVariableCore::get_unique_reference(std::size_t n) const;

437 6 output 0 4 781 26 ConfigVariableCore::output 0 1 79 10
/**
 *
 */
57
void ConfigVariableCore::output(std::ostream &out) const;

438 5 write 0 4 781 25 ConfigVariableCore::write 0 1 80 10
/**
 *
 */
56
void ConfigVariableCore::write(std::ostream &out) const;

439 16 get_string_value 0 4 778 35 ConfigDeclaration::get_string_value 0 1 31 186
/**
 * Returns the value assigned to this variable.  This is the original one-line
 * text defined for the variable in the .prc file (or passed to
 * ConfigPage::make_declaration()).
 */
74
inline std::string const &ConfigDeclaration::get_string_value(void) const;

440 16 set_string_value 0 4 778 35 ConfigDeclaration::set_string_value 0 1 32 55
/**
 * Changes the value assigned to this variable.
 */
74
inline void ConfigDeclaration::set_string_value(std::string const &value);

441 13 get_num_words 0 4 778 32 ConfigDeclaration::get_num_words 0 1 33 156
/**
 * Returns the number of words in the declaration's value.  A word is defined
 * as a sequence of non-whitespace characters delimited by whitespace.
 */
64
inline std::size_t ConfigDeclaration::get_num_words(void) const;

442 15 has_string_word 0 4 778 34 ConfigDeclaration::has_string_word 0 1 34 184
/**
 * Returns true if the declaration's value has a valid string value for the
 * nth word.  This is really the same thing as asking if there are at least n
 * words in the value.
 */
68
inline bool ConfigDeclaration::has_string_word(std::size_t n) const;

443 13 has_bool_word 0 4 778 32 ConfigDeclaration::has_bool_word 0 1 35 97
/**
 * Returns true if the declaration's value has a valid boolean value for the
 * nth word.
 */
66
inline bool ConfigDeclaration::has_bool_word(std::size_t n) const;

444 12 has_int_word 0 4 778 31 ConfigDeclaration::has_int_word 0 1 36 97
/**
 * Returns true if the declaration's value has a valid integer value for the
 * nth word.
 */
65
inline bool ConfigDeclaration::has_int_word(std::size_t n) const;

445 14 has_int64_word 0 4 778 33 ConfigDeclaration::has_int64_word 0 1 37 95
/**
 * Returns true if the declaration's value has a valid int64 value for the nth
 * word.
 */
67
inline bool ConfigDeclaration::has_int64_word(std::size_t n) const;

446 15 has_double_word 0 4 778 34 ConfigDeclaration::has_double_word 0 1 38 97
/**
 * Returns true if the declaration's value has a valid integer value for the
 * nth word.
 */
68
inline bool ConfigDeclaration::has_double_word(std::size_t n) const;

447 15 get_string_word 0 4 778 34 ConfigDeclaration::get_string_word 0 1 39 153
/**
 * Returns the string value of the nth word of the declaration's value, or
 * empty string if there is no nth value.  See also has_string_word().
 */
75
inline std::string ConfigDeclaration::get_string_word(std::size_t n) const;

448 13 get_bool_word 0 4 778 32 ConfigDeclaration::get_bool_word 0 1 40 145
/**
 * Returns the boolean value of the nth word of the declaration's value, or
 * false if there is no nth value.  See also has_bool_word().
 */
66
inline bool ConfigDeclaration::get_bool_word(std::size_t n) const;

449 12 get_int_word 0 4 778 31 ConfigDeclaration::get_int_word 0 1 41 140
/**
 * Returns the integer value of the nth word of the declaration's value, or 0
 * if there is no nth value.  See also has_int_word().
 */
64
inline int ConfigDeclaration::get_int_word(std::size_t n) const;

450 14 get_int64_word 0 4 778 33 ConfigDeclaration::get_int64_word 0 1 42 140
/**
 * Returns the int64 value of the nth word of the declaration's value, or 0 if
 * there is no nth value.  See also has_int64_word().
 */
70
inline int64_t ConfigDeclaration::get_int64_word(std::size_t n) const;

451 15 get_double_word 0 4 778 34 ConfigDeclaration::get_double_word 0 1 43 143
/**
 * Returns the integer value of the nth word of the declaration's value, or 0
 * if there is no nth value.  See also has_double_word().
 */
70
inline double ConfigDeclaration::get_double_word(std::size_t n) const;

452 15 set_string_word 0 4 778 34 ConfigDeclaration::set_string_word 0 1 44 92
/**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */
81
void ConfigDeclaration::set_string_word(std::size_t n, std::string const &value);

453 13 set_bool_word 0 4 778 32 ConfigDeclaration::set_bool_word 0 1 45 92
/**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */
65
void ConfigDeclaration::set_bool_word(std::size_t n, bool value);

454 12 set_int_word 0 4 778 31 ConfigDeclaration::set_int_word 0 1 46 92
/**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */
63
void ConfigDeclaration::set_int_word(std::size_t n, int value);

455 14 set_int64_word 0 4 778 33 ConfigDeclaration::set_int64_word 0 1 47 92
/**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */
69
void ConfigDeclaration::set_int64_word(std::size_t n, int64_t value);

456 15 set_double_word 0 4 778 34 ConfigDeclaration::set_double_word 0 1 48 92
/**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */
69
void ConfigDeclaration::set_double_word(std::size_t n, double value);

457 18 get_filename_value 0 4 778 37 ConfigDeclaration::get_filename_value 0 1 49 100
/**
 * Interprets the string value as a filename and returns it, with any
 * variables expanded.
 */
59
Filename ConfigDeclaration::get_filename_value(void) const;

458 12 get_decl_seq 0 4 778 31 ConfigDeclaration::get_decl_seq 0 1 50 254
/**
 * Returns the sequence number of the declaration within the page.  Sequence
 * numbers are assigned as each declaration is created; each declaration is
 * given a higher sequence number than all the declarations created in the
 * page before it.
 */
55
inline int ConfigDeclaration::get_decl_seq(void) const;

459 6 output 0 4 778 25 ConfigDeclaration::output 0 1 51 10
/**
 *
 */
56
void ConfigDeclaration::output(std::ostream &out) const;

460 5 write 0 4 778 24 ConfigDeclaration::write 0 1 52 10
/**
 *
 */
55
void ConfigDeclaration::write(std::ostream &out) const;

461 6 output 0 4 771 18 ConfigPage::output 0 1 26 10
/**
 *
 */
49
void ConfigPage::output(std::ostream &out) const;

462 22 output_brief_signature 0 4 771 34 ConfigPage::output_brief_signature 0 1 27 61
/**
 * Outputs the first few hex digits of the signature.
 */
65
void ConfigPage::output_brief_signature(std::ostream &out) const;

463 5 write 0 4 771 17 ConfigPage::write 0 1 28 10
/**
 *
 */
48
void ConfigPage::write(std::ostream &out) const;

464 6 Notify 0 260 785 14 Notify::Notify 0 2 81 82 10
/**
 *
 */
70
Notify::Notify(void);
inline Notify::Notify(Notify const &) = default;

465 7 ~Notify 0 516 785 15 Notify::~Notify 0 0 10
/**
 *
 */
22
Notify::~Notify(void);

466 15 set_ostream_ptr 0 4 785 23 Notify::set_ostream_ptr 0 1 83 250
/**
 * Changes the ostream that all subsequent Notify messages will be written to.
 * If the previous ostream was set with delete_later = true, this will delete
 * the previous ostream.  If ostream_ptr is NULL, this resets the default to
 * cerr.
 */
71
void Notify::set_ostream_ptr(PyObject *ostream_ptr, bool delete_later);

467 15 get_ostream_ptr 0 4 785 23 Notify::get_ostream_ptr 0 1 84 67
/**
 * Returns the system-wide ostream for all Notify messages.
 */
50
std::ostream *Notify::get_ostream_ptr(void) const;

468 18 set_assert_handler 0 4 785 26 Notify::set_assert_handler 0 0 540
/**
 * Sets a pointer to a C function that will be called when an assertion test
 * fails.  This function may decide what to do when that happens: it may
 * choose to abort or return.  If it returns, it should return true to
 * indicate that the assertion should be respected (and the calling function
 * should return out of its block of code), or false to indicate that the
 * assertion should be completely ignored.
 *
 * If an assert handler is installed, it completely replaces the default
 * behavior of nassertr() and nassertv().
 */
71
void Notify::set_assert_handler(Notify::AssertHandler *assert_handler);

469 20 clear_assert_handler 0 4 785 28 Notify::clear_assert_handler 0 1 85 110
/**
 * Removes the installed assert handler and restores default behavior of
 * nassertr() and nassertv().
 */
40
void Notify::clear_assert_handler(void);

470 18 has_assert_handler 0 4 785 26 Notify::has_assert_handler 0 1 86 85
/**
 * Returns true if a user assert handler has been installed, false otherwise.
 */
44
bool Notify::has_assert_handler(void) const;

471 18 get_assert_handler 0 4 785 26 Notify::get_assert_handler 0 1 87 110
/**
 * Returns a pointer to the user-installed assert handler, if one was
 * installed, or NULL otherwise.
 */
62
Notify::AssertHandler *Notify::get_assert_handler(void) const;

472 17 has_assert_failed 0 4 785 25 Notify::has_assert_failed 0 1 88 637
/**
 * Returns true if an assertion test has failed (and not been ignored) since
 * the last call to clear_assert_failed().
 *
 * When an assertion test fails, the assert handler may decide either to
 * abort, return, or ignore the assertion.  Naturally, if it decides to abort,
 * this flag is irrelevant.  If it chooses to ignore the assertion, the flag
 * is not set.  However, if the assert handler chooses to return out of the
 * function (the normal case), it will also set this flag to indicate that an
 * assertion failure has occurred.
 *
 * This will also be the behavior in the absence of a user-defined assert
 * handler.
 */
50
inline bool Notify::has_assert_failed(void) const;

473 24 get_assert_error_message 0 4 785 32 Notify::get_assert_error_message 0 1 89 100
/**
 * Returns the error message that corresponds to the assertion that most
 * recently failed.
 */
71
inline std::string const &Notify::get_assert_error_message(void) const;

474 19 clear_assert_failed 0 4 785 27 Notify::clear_assert_failed 0 1 90 114
/**
 * Resets the assert_failed flag that is set whenever an assertion test fails.
 * See has_assert_failed().
 */
46
inline void Notify::clear_assert_failed(void);

475 16 get_top_category 0 4 785 24 Notify::get_top_category 0 1 91 129
/**
 * Returns the topmost Category in the hierarchy.  This may be used to
 * traverse the hierarchy of available Categories.
 */
47
NotifyCategory *Notify::get_top_category(void);

476 12 get_category 0 4 785 20 Notify::get_category 0 3 92 93 94 844
/**
 * Finds or creates a new Category given the basename of the category and its
 * parent in the category hierarchy.  The parent pointer may be NULL to
 * indicate this is a top-level Category.
 */

/**
 * Finds or creates a new Category given the basename of the category and the
 * fullname of its parent.  This is another way to create a category when you
 * don't have a pointer to its parent handy, but you know the name of its
 * parent.  If the parent Category does not already exist, it will be created.
 */

/**
 * Finds or creates a new Category given the fullname of the Category.  This
 * name should be a sequence of colon-separated names of parent Categories,
 * ending in the basename of this Category, e.g.  display:glxdisplay.  This is
 * a shorthand way to define a Category when a pointer to its parent is not
 * handy.
 */
269
NotifyCategory *Notify::get_category(std::string const &basename, NotifyCategory *parent_category);
NotifyCategory *Notify::get_category(std::string const &basename, std::string const &parent_fullname);
NotifyCategory *Notify::get_category(std::string const &fullname);

477 3 out 0 4 785 11 Notify::out 0 1 95 189
/**
 * A convenient way to get the ostream that should be written to for a Notify-
 * type message.  Also see Category::out() for a message that is specific to a
 * particular Category.
 */
39
static std::ostream &Notify::out(void);

478 4 null 0 4 785 12 Notify::null 0 1 96 159
/**
 * A convenient way to get an ostream that doesn't do anything.  Returned by
 * Category::out() when a particular Category and/or Severity is disabled.
 */
40
static std::ostream &Notify::null(void);

479 12 write_string 0 4 785 20 Notify::write_string 0 1 97 209
/**
 * A convenient way for scripting languages, which may know nothing about
 * ostreams, to write to Notify.  This writes a single string, followed by an
 * implicit newline, to the Notify output stream.
 */
57
static void Notify::write_string(std::string const &str);

480 3 ptr 0 4 785 11 Notify::ptr 0 1 98 104
/**
 * Returns the pointer to the global Notify object.  There is only one of
 * these in the world.
 */
33
static Notify *Notify::ptr(void);

481 21 loaded_implicit_pages 0 4 786 40 ConfigPageManager::loaded_implicit_pages 0 1 99 157
/**
 * Returns true if the implicit `*.prc` files have already been loaded, false
 * otherwise.  Normally this will only be false briefly before startup.
 */
65
inline bool ConfigPageManager::loaded_implicit_pages(void) const;

482 19 load_implicit_pages 0 4 786 38 ConfigPageManager::load_implicit_pages 0 1 100 252
/**
 * Searches the PRC_DIR and/or PRC_PATH directories for `*.prc` files and loads
 * them in as pages.  This is normally called automatically at startup time,
 * when the first variable's value is referenced.  See also
 * reload_implicit_pages().
 */
57
inline void ConfigPageManager::load_implicit_pages(void);

483 21 reload_implicit_pages 0 4 786 40 ConfigPageManager::reload_implicit_pages 0 1 101 211
/**
 * Searches the PRC_DIR and/or PRC_PATH directories for *.prc files and loads
 * them in as pages.
 *
 * This may be called after startup, to force the system to re-read all of the
 * implicit prc files.
 */
52
void ConfigPageManager::reload_implicit_pages(void);

484 15 get_search_path 0 4 786 34 ConfigPageManager::get_search_path 0 1 102 274
/**
 * Returns the search path used to locate implicit .prc files.  This is
 * determined by the PRC_DIR and PRC_PATH environment variables.  The object
 * returned by this method may be modified to change the path at runtime, and
 * then reload_implicit_pages() called.
 */
61
inline DSearchPath &ConfigPageManager::get_search_path(void);

485 20 get_num_prc_patterns 0 4 786 39 ConfigPageManager::get_num_prc_patterns 0 1 103 270
/**
 * Returns the number of patterns, like `*.prc`, that are compiled in that
 * will be searched for as default config filenames.  Normally there is only
 * one pattern, and it is `*.prc`, but others may be specified with the
 * PRC_FILENAME variable in Config.pp.
 */
71
inline std::size_t ConfigPageManager::get_num_prc_patterns(void) const;

486 15 get_prc_pattern 0 4 786 34 ConfigPageManager::get_prc_pattern 0 1 104 131
/**
 * Returns the nth filename pattern that will be considered a match as a valid
 * config file.  See get_num_prc_patterns().
 */
75
inline std::string ConfigPageManager::get_prc_pattern(std::size_t n) const;

487 30 get_num_prc_encrypted_patterns 0 4 786 49 ConfigPageManager::get_num_prc_encrypted_patterns 0 1 105 170
/**
 * Returns the number of patterns, like `*.pre`, that are compiled in that
 * will be searched for as special config files that are understood to be
 * encrypted.
 */
81
inline std::size_t ConfigPageManager::get_num_prc_encrypted_patterns(void) const;

488 25 get_prc_encrypted_pattern 0 4 786 44 ConfigPageManager::get_prc_encrypted_pattern 0 1 106 151
/**
 * Returns the nth filename pattern that will be considered a match as a valid
 * encrypted config file.  See get_num_prc_encrypted_patterns().
 */
85
inline std::string ConfigPageManager::get_prc_encrypted_pattern(std::size_t n) const;

489 31 get_num_prc_executable_patterns 0 4 786 50 ConfigPageManager::get_num_prc_executable_patterns 0 1 107 232
/**
 * Returns the number of patterns, like `*.exe`, that are compiled in that
 * will be searched for as special config files that are to be executed as a
 * program, and their output taken to be input.  This is normally empty.
 */
82
inline std::size_t ConfigPageManager::get_num_prc_executable_patterns(void) const;

490 26 get_prc_executable_pattern 0 4 786 45 ConfigPageManager::get_prc_executable_pattern 0 1 108 159
/**
 * Returns the nth filename pattern that will be considered a match as a valid
 * executable-style config file.  See get_num_prc_executable_patterns().
 */
86
inline std::string ConfigPageManager::get_prc_executable_pattern(std::size_t n) const;

491 18 make_explicit_page 0 4 786 37 ConfigPageManager::make_explicit_page 0 1 109 206
/**
 * Creates and returns a new, empty ConfigPage.  This page will be stacked on
 * top of any pages that were created before; it may shadow variable
 * declarations that are defined in previous pages.
 */
75
ConfigPage *ConfigPageManager::make_explicit_page(std::string const &name);

492 20 delete_explicit_page 0 4 786 39 ConfigPageManager::delete_explicit_page 0 1 110 318
/**
 * Removes a previously-constructed ConfigPage from the set of active pages,
 * and deletes it.  The ConfigPage object is no longer valid after this call.
 * Returns true if the page is successfully deleted, or false if it was
 * unknown (which should never happen if the page was legitimately
 * constructed).
 */
63
bool ConfigPageManager::delete_explicit_page(ConfigPage *page);

493 22 get_num_implicit_pages 0 4 786 41 ConfigPageManager::get_num_implicit_pages 0 1 111 174
/**
 * Returns the current number of implicitly-loaded ConfigPages in the world.
 * These represent files that were automatically discovered on the disk as
 * .prc files.
 */
73
inline std::size_t ConfigPageManager::get_num_implicit_pages(void) const;

494 17 get_implicit_page 0 4 786 36 ConfigPageManager::get_implicit_page 0 1 112 94
/**
 * Returns the nth implicit ConfigPage in the world.  See
 * get_num_implicit_pages().
 */
77
inline ConfigPage *ConfigPageManager::get_implicit_page(std::size_t n) const;

495 22 get_num_explicit_pages 0 4 786 41 ConfigPageManager::get_num_explicit_pages 0 1 113 214
/**
 * Returns the current number of explicitly-loaded ConfigPages in the world.
 * These represent pages that were loaded dynamically at runtime by explicit
 * calls to ConfigPageManager::make_explicit_page().
 */
73
inline std::size_t ConfigPageManager::get_num_explicit_pages(void) const;

496 17 get_explicit_page 0 4 786 36 ConfigPageManager::get_explicit_page 0 1 114 94
/**
 * Returns the nth explicit ConfigPage in the world.  See
 * get_num_explicit_pages().
 */
77
inline ConfigPage *ConfigPageManager::get_explicit_page(std::size_t n) const;

497 6 output 0 4 786 25 ConfigPageManager::output 0 1 115 10
/**
 *
 */
56
void ConfigPageManager::output(std::ostream &out) const;

498 5 write 0 4 786 24 ConfigPageManager::write 0 1 116 10
/**
 *
 */
55
void ConfigPageManager::write(std::ostream &out) const;

499 14 get_global_ptr 0 4 786 33 ConfigPageManager::get_global_ptr 0 1 117 10
/**
 *
 */
66
static ConfigPageManager *ConfigPageManager::get_global_ptr(void);

500 13 make_variable 0 4 788 36 ConfigVariableManager::make_variable 0 1 118 181
/**
 * Creates and returns a new, undefined ConfigVariableCore with the indicated
 * name; or if a variable with this name has already been created, returns
 * that one instead.
 */
82
ConfigVariableCore *ConfigVariableManager::make_variable(std::string const &name);

501 22 make_variable_template 0 4 788 45 ConfigVariableManager::make_variable_template 0 1 119 899
/**
 * Defines a variable "template" to match against dynamically-defined
 * variables that may or may not be created in the future.
 *
 * The template consists of a glob pattern, e.g.  `notify-level-*`, which will
 * be tested against any config variable passed to a future call to
 * make_variable().  If the pattern matches, the returned ConfigVariableCore
 * is copied to define the new variable, instead of creating a default, empty
 * one.
 *
 * This is useful to pre-specify default values for a family of variables that
 * all have similar properties, and all may not be created at the same time.
 * It is especially useful to avoid cluttering up the list of available
 * variables with user-declared variables that have not been defined yet by
 * the application (e.g. `egg-object-type-*`).
 *
 * This method basically pre-defines all variables that match the specified
 * glob pattern.
 */
215
ConfigVariableCore *ConfigVariableManager::make_variable_template(std::string const &pattern, ConfigFlags::ValueType type, std::string const &default_value, std::string const &description = string(), int flags = 0);

502 17 get_num_variables 0 4 788 40 ConfigVariableManager::get_num_variables 0 1 120 81
/**
 * Returns the current number of active ConfigVariableCores in the world.
 */
72
inline std::size_t ConfigVariableManager::get_num_variables(void) const;

503 12 get_variable 0 4 788 35 ConfigVariableManager::get_variable 0 1 121 66
/**
 * Returns the nth active ConfigVariableCore in the world.
 */
84
inline ConfigVariableCore *ConfigVariableManager::get_variable(std::size_t n) const;

504 17 get_variable_name 0 4 788 40 ConfigVariableManager::get_variable_name 0 1 122 73
/**
 * Returns the name of the nth active ConfigVariable in the list.
 */
74
std::string ConfigVariableManager::get_variable_name(std::size_t n) const;

505 16 is_variable_used 0 4 788 39 ConfigVariableManager::is_variable_used 0 1 123 111
/**
 * Returns true if the nth active ConfigVariable in the list has been used by
 * code, false otherwise.
 */
66
bool ConfigVariableManager::is_variable_used(std::size_t n) const;

506 6 output 0 4 788 29 ConfigVariableManager::output 0 1 124 10
/**
 *
 */
60
void ConfigVariableManager::output(std::ostream &out) const;

507 5 write 0 4 788 28 ConfigVariableManager::write 0 1 125 10
/**
 *
 */
59
void ConfigVariableManager::write(std::ostream &out) const;

508 19 write_prc_variables 0 4 788 42 ConfigVariableManager::write_prc_variables 0 1 126 343
/**
 * Writes all of the prc-set config variables, as they appear in a prc file
 * somewhere, one per line, very concisely.  This lists the dominant value in
 * the prc file; it does not list shadowed values, and it does not list
 * locally-set values.
 *
 * This is mainly intended for generating a hash of the input config file
 * state.
 */
73
void ConfigVariableManager::write_prc_variables(std::ostream &out) const;

509 21 list_unused_variables 0 4 788 44 ConfigVariableManager::list_unused_variables 0 1 127 133
/**
 * Writes a list of all the variables that have been defined in a prc file
 * without having been declared somewhere in code.
 */
62
void ConfigVariableManager::list_unused_variables(void) const;

510 14 list_variables 0 4 788 37 ConfigVariableManager::list_variables 0 1 128 123
/**
 * Writes a list of all the variables that have been declared somewhere in
 * code, along with a brief description.
 */
55
void ConfigVariableManager::list_variables(void) const;

511 22 list_dynamic_variables 0 4 788 45 ConfigVariableManager::list_dynamic_variables 0 1 129 241
/**
 * Writes a list of all the "dynamic" variables that have been declared
 * somewhere in code, along with a brief description.  This is a (usually
 * large) list of config variables that are declared with a generated variable
 * name.
 */
63
void ConfigVariableManager::list_dynamic_variables(void) const;

512 14 get_global_ptr 0 4 788 37 ConfigVariableManager::get_global_ptr 0 1 130 10
/**
 *
 */
74
static ConfigVariableManager *ConfigVariableManager::get_global_ptr(void);

513 8 get_name 0 4 789 28 ConfigVariableBase::get_name 0 1 131 44
/**
 * Returns the name of the variable.
 */
67
inline std::string const &ConfigVariableBase::get_name(void) const;

514 14 get_value_type 0 4 789 34 ConfigVariableBase::get_value_type 0 1 132 132
/**
 * Returns the stated type of this variable.  This should be VT_list, unless a
 * later variable declaration has changed it.
 */
77
inline ConfigFlags::ValueType ConfigVariableBase::get_value_type(void) const;

515 15 get_description 0 4 789 35 ConfigVariableBase::get_description 0 1 133 82
/**
 * Returns the brief description of this variable, if it has been defined.
 */
74
inline std::string const &ConfigVariableBase::get_description(void) const;

516 9 get_flags 0 4 789 29 ConfigVariableBase::get_flags 0 1 134 250
/**
 * Returns the flags value as set by set_flags().  This includes the trust
 * level and some other settings.  See the individual methods is_closed(),
 * get_trust_level(), etc.  to pull out the semantic meaning of these flags
 * individually.
 */
53
inline int ConfigVariableBase::get_flags(void) const;

517 9 is_closed 0 4 789 29 ConfigVariableBase::is_closed 0 1 135 413
/**
 * Returns true if the variable is not trusted by any prc file (and hence
 * cannot be modified from its compiled-in default value), or false for the
 * normal case, in which the variable can be modified by any prc file at or
 * above its trust level (see get_trust_level()).
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */
54
inline bool ConfigVariableBase::is_closed(void) const;

518 15 get_trust_level 0 4 789 35 ConfigVariableBase::get_trust_level 0 1 136 479
/**
 * Returns the minimum trust_level a prc file must demonstrate in order to
 * redefine the value for this variable.  Arguably, this should be called the
 * "mistrust level", since the larger the value, the more suspicious we are of
 * prc files.  This value is not used if is_closed() returns true, which
 * indicates no file may be trusted.
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */
59
inline int ConfigVariableBase::get_trust_level(void) const;

519 10 is_dynamic 0 4 789 30 ConfigVariableBase::is_dynamic 0 1 137 232
/**
 * Returns true if the variable was indicated as "dynamic" by its constructor,
 * indicating that its name was dynamically generated, possibly from a large
 * pool, and it should not be listed along with the other variables.
 */
55
inline bool ConfigVariableBase::is_dynamic(void) const;

520 17 clear_local_value 0 4 789 37 ConfigVariableBase::clear_local_value 0 1 138 240
/**
 * Removes the local value defined for this variable, and allows its value to
 * be once again retrieved from the .prc files.
 *
 * Returns true if the value was successfully removed, false if it did not
 * exist in the first place.
 */
56
inline bool ConfigVariableBase::clear_local_value(void);

521 15 has_local_value 0 4 789 35 ConfigVariableBase::has_local_value 0 1 139 148
/**
 * Returns true if this variable's value has been shadowed by a local
 * assignment (as created via make_local_value()), or false otherwise.
 */
60
inline bool ConfigVariableBase::has_local_value(void) const;

522 9 has_value 0 4 789 29 ConfigVariableBase::has_value 0 1 140 149
/**
 * Returns true if this variable has an explicit value, either from a prc file
 * or locally set, or false if variable has its default value.
 */
54
inline bool ConfigVariableBase::has_value(void) const;

523 6 output 0 4 789 26 ConfigVariableBase::output 0 1 141 10
/**
 *
 */
64
inline void ConfigVariableBase::output(std::ostream &out) const;

524 5 write 0 4 789 25 ConfigVariableBase::write 0 1 142 10
/**
 *
 */
63
inline void ConfigVariableBase::write(std::ostream &out) const;

525 14 ConfigVariable 0 260 790 30 ConfigVariable::ConfigVariable 0 2 143 144 458
/**
 * This constructor is only intended to be called from a specialized
 * ConfigVariableFoo derived class.
 */

/**
 * This constructor is only intended to be called from a specialized
 * ConfigVariableFoo derived class.
 */

/**
 * Use this constructor to make a ConfigVariable of an unspecified type.
 * Usually you'd want to do this just to reference a previously-defined
 * ConfigVariable of a specific type, without having to know what type it is.
 */
145
inline explicit ConfigVariable::ConfigVariable(std::string const &name);
inline ConfigVariable::ConfigVariable(ConfigVariable const &) = default;

526 15 ~ConfigVariable 0 516 790 31 ConfigVariable::~ConfigVariable 0 0 10
/**
 *
 */
45
inline ConfigVariable::~ConfigVariable(void);

527 16 get_string_value 0 4 790 32 ConfigVariable::get_string_value 0 1 145 77
/**
 * Returns the toplevel value of the variable, formatted as a string.
 */
71
inline std::string const &ConfigVariable::get_string_value(void) const;

528 16 set_string_value 0 4 790 32 ConfigVariable::set_string_value 0 1 146 177
/**
 * Changes the value assigned to this variable.  This creates a local value
 * that shadows any values defined in the .prc files, until
 * clear_local_value() is called.
 */
71
inline void ConfigVariable::set_string_value(std::string const &value);

529 11 clear_value 0 4 790 27 ConfigVariable::clear_value 0 1 147 129
/**
 * Removes the value assigned to this variable, and lets its original value
 * (as read from the prc files) show through.
 */
46
inline void ConfigVariable::clear_value(void);

530 13 get_num_words 0 4 790 29 ConfigVariable::get_num_words 0 1 148 153
/**
 * Returns the number of words in the variable's value.  A word is defined as
 * a sequence of non-whitespace characters delimited by whitespace.
 */
61
inline std::size_t ConfigVariable::get_num_words(void) const;

531 18 ConfigVariableBool 0 260 791 38 ConfigVariableBool::ConfigVariableBool 0 4 149 150 151 152 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
470
inline ConfigVariableBool::ConfigVariableBool(std::string const &name);
inline ConfigVariableBool::ConfigVariableBool(std::string const &name, bool default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableBool::ConfigVariableBool(std::string const &name, std::string const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableBool::ConfigVariableBool(ConfigVariableBool const &) = default;

532 10 operator = 0 4 791 30 ConfigVariableBool::operator = 0 1 153 0
55
inline void ConfigVariableBool::operator =(bool value);

533 22 operator typecast bool 0 132 791 42 ConfigVariableBool::operator typecast bool 0 1 161 0
34
inline operator bool (void) const;

534 4 size 0 4 791 24 ConfigVariableBool::size 0 1 154 62
/**
 * Returns the number of unique words in the variable.
 */
56
inline std::size_t ConfigVariableBool::size(void) const;

535 11 operator [] 0 4 791 31 ConfigVariableBool::operator [] 0 1 155 0
65
inline bool ConfigVariableBool::operator [](std::size_t n) const;

536 9 set_value 0 4 791 29 ConfigVariableBool::set_value 0 1 156 48
/**
 * Reassigns the variable's local value.
 */
54
inline void ConfigVariableBool::set_value(bool value);

537 9 get_value 0 4 791 29 ConfigVariableBool::get_value 0 1 157 40
/**
 * Returns the variable's value.
 */
54
inline bool ConfigVariableBool::get_value(void) const;

538 17 get_default_value 0 4 791 37 ConfigVariableBool::get_default_value 0 1 158 48
/**
 * Returns the variable's default value.
 */
62
inline bool ConfigVariableBool::get_default_value(void) const;

539 8 get_word 0 4 791 28 ConfigVariableBool::get_word 0 1 159 44
/**
 * Returns the variable's nth value.
 */
62
inline bool ConfigVariableBool::get_word(std::size_t n) const;

540 8 set_word 0 4 791 28 ConfigVariableBool::set_word 0 1 160 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
68
inline void ConfigVariableBool::set_word(std::size_t n, bool value);

541 19 ~ConfigVariableBool 0 516 791 39 ConfigVariableBool::~ConfigVariableBool 0 0 0
46
ConfigVariableBool::~ConfigVariableBool(void);

542 20 ConfigVariableDouble 0 260 792 42 ConfigVariableDouble::ConfigVariableDouble 0 4 162 163 164 165 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
490
inline ConfigVariableDouble::ConfigVariableDouble(std::string const &name);
inline ConfigVariableDouble::ConfigVariableDouble(std::string const &name, double default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableDouble::ConfigVariableDouble(std::string const &name, std::string const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableDouble::ConfigVariableDouble(ConfigVariableDouble const &) = default;

543 10 operator = 0 4 792 32 ConfigVariableDouble::operator = 0 1 166 0
59
inline void ConfigVariableDouble::operator =(double value);

544 24 operator typecast double 0 132 792 46 ConfigVariableDouble::operator typecast double 0 1 174 0
36
inline operator double (void) const;

545 4 size 0 4 792 26 ConfigVariableDouble::size 0 1 167 62
/**
 * Returns the number of unique words in the variable.
 */
58
inline std::size_t ConfigVariableDouble::size(void) const;

546 11 operator [] 0 4 792 33 ConfigVariableDouble::operator [] 0 1 168 0
69
inline double ConfigVariableDouble::operator [](std::size_t n) const;

547 9 set_value 0 4 792 31 ConfigVariableDouble::set_value 0 1 169 48
/**
 * Reassigns the variable's local value.
 */
58
inline void ConfigVariableDouble::set_value(double value);

548 9 get_value 0 4 792 31 ConfigVariableDouble::get_value 0 1 170 40
/**
 * Returns the variable's value.
 */
58
inline double ConfigVariableDouble::get_value(void) const;

549 17 get_default_value 0 4 792 39 ConfigVariableDouble::get_default_value 0 1 171 48
/**
 * Returns the variable's default value.
 */
66
inline double ConfigVariableDouble::get_default_value(void) const;

550 8 get_word 0 4 792 30 ConfigVariableDouble::get_word 0 1 172 44
/**
 * Returns the variable's nth value.
 */
66
inline double ConfigVariableDouble::get_word(std::size_t n) const;

551 8 set_word 0 4 792 30 ConfigVariableDouble::set_word 0 1 173 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
72
inline void ConfigVariableDouble::set_word(std::size_t n, double value);

552 21 ~ConfigVariableDouble 0 516 792 43 ConfigVariableDouble::~ConfigVariableDouble 0 0 0
50
ConfigVariableDouble::~ConfigVariableDouble(void);

553 22 ConfigVariableFilename 0 260 794 46 ConfigVariableFilename::ConfigVariableFilename 0 3 175 176 177 22
/**
 *
 */

/**
 *
 */
345
inline ConfigVariableFilename::ConfigVariableFilename(std::string const &name);
inline ConfigVariableFilename::ConfigVariableFilename(std::string const &name, Filename const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableFilename::ConfigVariableFilename(ConfigVariableFilename const &) = default;

554 10 operator = 0 4 794 34 ConfigVariableFilename::operator = 0 1 178 0
70
inline void ConfigVariableFilename::operator =(Filename const &value);

555 17 operator typecast 0 132 794 41 ConfigVariableFilename::operator typecast 0 1 198 0
45
inline operator Filename const &(void) const;

556 5 c_str 0 4 794 29 ConfigVariableFilename::c_str 0 1 179 88
// These methods help the ConfigVariableFilename act like a Filename object.

/**
 *
 */
61
inline char const *ConfigVariableFilename::c_str(void) const;

557 5 empty 0 4 794 29 ConfigVariableFilename::empty 0 1 180 10
/**
 *
 */
54
inline bool ConfigVariableFilename::empty(void) const;

558 6 length 0 4 794 30 ConfigVariableFilename::length 0 1 181 10
/**
 *
 */
62
inline std::size_t ConfigVariableFilename::length(void) const;

559 11 operator [] 0 4 794 35 ConfigVariableFilename::operator [] 0 1 182 0
69
inline char ConfigVariableFilename::operator [](std::size_t n) const;

560 12 get_fullpath 0 4 794 36 ConfigVariableFilename::get_fullpath 0 1 183 183
/**
 * Returns the entire filename: directory, basename, extension.  This is the
 * same thing returned by the string typecast operator, so this function is a
 * little redundant.
 */
68
inline std::string ConfigVariableFilename::get_fullpath(void) const;

561 11 get_dirname 0 4 794 35 ConfigVariableFilename::get_dirname 0 1 184 139
/**
 * Returns the directory part of the filename.  This is everything in the
 * filename up to, but not including the rightmost slash.
 */
67
inline std::string ConfigVariableFilename::get_dirname(void) const;

562 12 get_basename 0 4 794 36 ConfigVariableFilename::get_basename 0 1 185 145
/**
 * Returns the basename part of the filename.  This is everything in the
 * filename after the rightmost slash, including any extensions.
 */
68
inline std::string ConfigVariableFilename::get_basename(void) const;

563 25 get_fullpath_wo_extension 0 4 794 49 ConfigVariableFilename::get_fullpath_wo_extension 0 1 186 96
/**
 * Returns the full filename--directory and basename parts--except for the
 * extension.
 */
81
inline std::string ConfigVariableFilename::get_fullpath_wo_extension(void) const;

564 25 get_basename_wo_extension 0 4 794 49 ConfigVariableFilename::get_basename_wo_extension 0 1 187 81
/**
 * Returns the basename part of the filename, without the file extension.
 */
81
inline std::string ConfigVariableFilename::get_basename_wo_extension(void) const;

565 13 get_extension 0 4 794 37 ConfigVariableFilename::get_extension 0 1 188 140
/**
 * Returns the file extension.  This is everything after the rightmost dot, if
 * there is one, or the empty string if there is not.
 */
69
inline std::string ConfigVariableFilename::get_extension(void) const;

566 11 operator == 0 4 794 35 ConfigVariableFilename::operator == 0 1 189 34
// Comparison operators are handy.
77
inline bool ConfigVariableFilename::operator ==(Filename const &other) const;

567 11 operator != 0 4 794 35 ConfigVariableFilename::operator != 0 1 190 0
77
inline bool ConfigVariableFilename::operator !=(Filename const &other) const;

568 10 operator < 0 4 794 34 ConfigVariableFilename::operator < 0 1 191 0
76
inline bool ConfigVariableFilename::operator <(Filename const &other) const;

569 9 set_value 0 4 794 33 ConfigVariableFilename::set_value 0 1 192 48
/**
 * Reassigns the variable's local value.
 */
69
inline void ConfigVariableFilename::set_value(Filename const &value);

570 9 get_value 0 4 794 33 ConfigVariableFilename::get_value 0 1 193 40
/**
 * Returns the variable's value.
 */
62
inline Filename ConfigVariableFilename::get_value(void) const;

571 17 get_default_value 0 4 794 41 ConfigVariableFilename::get_default_value 0 1 194 48
/**
 * Returns the variable's default value.
 */
70
inline Filename ConfigVariableFilename::get_default_value(void) const;

572 8 get_word 0 4 794 32 ConfigVariableFilename::get_word 0 1 195 44
/**
 * Returns the variable's nth value.
 */
70
inline Filename ConfigVariableFilename::get_word(std::size_t n) const;

573 8 set_word 0 4 794 32 ConfigVariableFilename::set_word 0 1 196 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
83
inline void ConfigVariableFilename::set_word(std::size_t n, Filename const &value);

574 10 __fspath__ 0 4 794 34 ConfigVariableFilename::__fspath__ 0 1 197 145
/**
 * Allows a ConfigVariableFilename object to be passed to any Python function
 * that accepts an os.PathLike object.
 *
 * @since 1.10.13
 */
67
inline std::wstring ConfigVariableFilename::__fspath__(void) const;

575 23 ~ConfigVariableFilename 0 516 794 47 ConfigVariableFilename::~ConfigVariableFilename 0 0 0
54
ConfigVariableFilename::~ConfigVariableFilename(void);

576 17 ConfigVariableInt 0 260 796 36 ConfigVariableInt::ConfigVariableInt 0 4 199 200 201 202 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
460
inline ConfigVariableInt::ConfigVariableInt(std::string const &name);
inline ConfigVariableInt::ConfigVariableInt(std::string const &name, int default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableInt::ConfigVariableInt(std::string const &name, std::string const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableInt::ConfigVariableInt(ConfigVariableInt const &) = default;

577 10 operator = 0 4 796 29 ConfigVariableInt::operator = 0 1 203 0
53
inline void ConfigVariableInt::operator =(int value);

578 21 operator typecast int 0 132 796 40 ConfigVariableInt::operator typecast int 0 1 211 0
33
inline operator int (void) const;

579 4 size 0 4 796 23 ConfigVariableInt::size 0 1 204 62
/**
 * Returns the number of unique words in the variable.
 */
55
inline std::size_t ConfigVariableInt::size(void) const;

580 11 operator [] 0 4 796 30 ConfigVariableInt::operator [] 0 1 205 0
63
inline int ConfigVariableInt::operator [](std::size_t n) const;

581 9 set_value 0 4 796 28 ConfigVariableInt::set_value 0 1 206 48
/**
 * Reassigns the variable's local value.
 */
52
inline void ConfigVariableInt::set_value(int value);

582 9 get_value 0 4 796 28 ConfigVariableInt::get_value 0 1 207 40
/**
 * Returns the variable's value.
 */
52
inline int ConfigVariableInt::get_value(void) const;

583 17 get_default_value 0 4 796 36 ConfigVariableInt::get_default_value 0 1 208 48
/**
 * Returns the variable's default value.
 */
60
inline int ConfigVariableInt::get_default_value(void) const;

584 8 get_word 0 4 796 27 ConfigVariableInt::get_word 0 1 209 44
/**
 * Returns the variable's nth value.
 */
60
inline int ConfigVariableInt::get_word(std::size_t n) const;

585 8 set_word 0 4 796 27 ConfigVariableInt::set_word 0 1 210 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
66
inline void ConfigVariableInt::set_word(std::size_t n, int value);

586 18 ~ConfigVariableInt 0 516 796 37 ConfigVariableInt::~ConfigVariableInt 0 0 0
44
ConfigVariableInt::~ConfigVariableInt(void);

587 19 ConfigVariableInt64 0 260 797 40 ConfigVariableInt64::ConfigVariableInt64 0 4 212 213 214 215 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
482
inline ConfigVariableInt64::ConfigVariableInt64(std::string const &name);
inline ConfigVariableInt64::ConfigVariableInt64(std::string const &name, int64_t default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableInt64::ConfigVariableInt64(std::string const &name, std::string const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableInt64::ConfigVariableInt64(ConfigVariableInt64 const &) = default;

588 10 operator = 0 4 797 31 ConfigVariableInt64::operator = 0 1 216 0
59
inline void ConfigVariableInt64::operator =(int64_t value);

589 25 operator typecast int64_t 0 132 797 46 ConfigVariableInt64::operator typecast int64_t 0 1 224 0
37
inline operator int64_t (void) const;

590 4 size 0 4 797 25 ConfigVariableInt64::size 0 1 217 62
/**
 * Returns the number of unique words in the variable.
 */
57
inline std::size_t ConfigVariableInt64::size(void) const;

591 11 operator [] 0 4 797 32 ConfigVariableInt64::operator [] 0 1 218 0
69
inline int64_t ConfigVariableInt64::operator [](std::size_t n) const;

592 9 set_value 0 4 797 30 ConfigVariableInt64::set_value 0 1 219 48
/**
 * Reassigns the variable's local value.
 */
58
inline void ConfigVariableInt64::set_value(int64_t value);

593 9 get_value 0 4 797 30 ConfigVariableInt64::get_value 0 1 220 40
/**
 * Returns the variable's value.
 */
58
inline int64_t ConfigVariableInt64::get_value(void) const;

594 17 get_default_value 0 4 797 38 ConfigVariableInt64::get_default_value 0 1 221 48
/**
 * Returns the variable's default value.
 */
66
inline int64_t ConfigVariableInt64::get_default_value(void) const;

595 8 get_word 0 4 797 29 ConfigVariableInt64::get_word 0 1 222 44
/**
 * Returns the variable's nth value.
 */
66
inline int64_t ConfigVariableInt64::get_word(std::size_t n) const;

596 8 set_word 0 4 797 29 ConfigVariableInt64::set_word 0 1 223 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
72
inline void ConfigVariableInt64::set_word(std::size_t n, int64_t value);

597 20 ~ConfigVariableInt64 0 516 797 41 ConfigVariableInt64::~ConfigVariableInt64 0 0 0
48
ConfigVariableInt64::~ConfigVariableInt64(void);

598 18 ConfigVariableList 0 260 800 38 ConfigVariableList::ConfigVariableList 0 2 225 226 10
/**
 *
 */
214
inline ConfigVariableList::ConfigVariableList(std::string const &name, std::string const &description = string(), int flags = 0);
inline ConfigVariableList::ConfigVariableList(ConfigVariableList const &) = default;

599 19 ~ConfigVariableList 0 516 800 39 ConfigVariableList::~ConfigVariableList 0 0 10
/**
 *
 */
53
inline ConfigVariableList::~ConfigVariableList(void);

600 14 get_num_values 0 4 800 34 ConfigVariableList::get_num_values 0 1 227 56
/**
 * Returns the number of values in the variable.
 */
66
inline std::size_t ConfigVariableList::get_num_values(void) const;

601 16 get_string_value 0 4 800 36 ConfigVariableList::get_string_value 0 1 228 49
/**
 * Returns the nth value of the variable.
 */
77
inline std::string ConfigVariableList::get_string_value(std::size_t n) const;

602 21 get_num_unique_values 0 4 800 41 ConfigVariableList::get_num_unique_values 0 1 229 63
/**
 * Returns the number of unique values in the variable.
 */
73
inline std::size_t ConfigVariableList::get_num_unique_values(void) const;

603 16 get_unique_value 0 4 800 36 ConfigVariableList::get_unique_value 0 1 230 56
/**
 * Returns the nth unique value of the variable.
 */
77
inline std::string ConfigVariableList::get_unique_value(std::size_t n) const;

604 4 size 0 4 800 24 ConfigVariableList::size 0 1 231 63
/**
 * Returns the number of unique values of the variable.
 */
56
inline std::size_t ConfigVariableList::size(void) const;

605 11 operator [] 0 4 800 31 ConfigVariableList::operator [] 0 1 232 0
72
inline std::string ConfigVariableList::operator [](std::size_t n) const;

606 6 output 0 4 800 26 ConfigVariableList::output 0 1 233 10
/**
 *
 */
57
void ConfigVariableList::output(std::ostream &out) const;

607 5 write 0 4 800 25 ConfigVariableList::write 0 1 234 10
/**
 *
 */
56
void ConfigVariableList::write(std::ostream &out) const;

608 24 ConfigVariableSearchPath 0 260 801 50 ConfigVariableSearchPath::ConfigVariableSearchPath 0 3 235 236 237 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
471
inline ConfigVariableSearchPath::ConfigVariableSearchPath(std::string const &name, std::string const &description = string(), int flags = 0);
inline ConfigVariableSearchPath::ConfigVariableSearchPath(std::string const &name, DSearchPath const &default_value, std::string const &description, int flags = 0);
inline ConfigVariableSearchPath::ConfigVariableSearchPath(std::string const &name, std::string const &default_value, std::string const &description, int flags = 0);

609 25 ~ConfigVariableSearchPath 0 516 801 51 ConfigVariableSearchPath::~ConfigVariableSearchPath 0 0 10
/**
 *
 */
65
inline ConfigVariableSearchPath::~ConfigVariableSearchPath(void);

610 29 operator typecast DSearchPath 0 132 801 55 ConfigVariableSearchPath::operator typecast DSearchPath 0 1 255 0
41
inline operator DSearchPath (void) const;

611 9 get_value 0 4 801 35 ConfigVariableSearchPath::get_value 0 1 238 10
/**
 *
 */
67
inline DSearchPath ConfigVariableSearchPath::get_value(void) const;

612 17 get_default_value 0 4 801 43 ConfigVariableSearchPath::get_default_value 0 1 239 10
/**
 *
 */
82
inline DSearchPath const &ConfigVariableSearchPath::get_default_value(void) const;

613 17 clear_local_value 0 4 801 43 ConfigVariableSearchPath::clear_local_value 0 1 240 113
/**
 * Removes all the directories locally added to the search list, and restores
 * it to its original form.
 */
62
inline bool ConfigVariableSearchPath::clear_local_value(void);

614 5 clear 0 4 801 31 ConfigVariableSearchPath::clear 0 1 241 113
/**
 * Removes all the directories locally added to the search list, and restores
 * it to its original form.
 */
50
inline void ConfigVariableSearchPath::clear(void);

615 16 append_directory 0 4 801 42 ConfigVariableSearchPath::append_directory 0 1 242 62
/**
 * Adds a new directory to the end of the search list.
 */
82
inline void ConfigVariableSearchPath::append_directory(Filename const &directory);

616 17 prepend_directory 0 4 801 43 ConfigVariableSearchPath::prepend_directory 0 1 243 64
/**
 * Adds a new directory to the front of the search list.
 */
83
inline void ConfigVariableSearchPath::prepend_directory(Filename const &directory);

617 11 append_path 0 4 801 37 ConfigVariableSearchPath::append_path 0 2 244 245 198
/**
 * Adds all of the directories listed in the search path to the end of the
 * search list.
 */

/**
 * Adds all of the directories listed in the search path to the end of the
 * search list.
 */
192
inline void ConfigVariableSearchPath::append_path(std::string const &path, std::string const &separator = string());
inline void ConfigVariableSearchPath::append_path(DSearchPath const &path);

618 12 prepend_path 0 4 801 38 ConfigVariableSearchPath::prepend_path 0 1 246 104
/**
 * Adds all of the directories listed in the search path to the beginning of
 * the search list.
 */
76
inline void ConfigVariableSearchPath::prepend_path(DSearchPath const &path);

619 8 is_empty 0 4 801 34 ConfigVariableSearchPath::is_empty 0 1 247 69
/**
 * Returns true if the search list is empty, false otherwise.
 */
59
inline bool ConfigVariableSearchPath::is_empty(void) const;

620 19 get_num_directories 0 4 801 45 ConfigVariableSearchPath::get_num_directories 0 1 248 64
/**
 * Returns the number of directories on the search list.
 */
77
inline std::size_t ConfigVariableSearchPath::get_num_directories(void) const;

621 13 get_directory 0 4 801 39 ConfigVariableSearchPath::get_directory 0 1 249 56
/**
 * Returns the nth directory on the search list.
 */
77
inline Filename ConfigVariableSearchPath::get_directory(std::size_t n) const;

622 9 find_file 0 4 801 35 ConfigVariableSearchPath::find_file 0 1 250 197
/**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Returns the full matching pathname of the first match if found, or
 * the empty string if not found.
 */
84
inline Filename ConfigVariableSearchPath::find_file(Filename const &filename) const;

623 14 find_all_files 0 4 801 40 ConfigVariableSearchPath::find_all_files 0 2 251 252 549
/**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Fills up the results list with *all* of the matching filenames
 * found, if any.  Returns the number of matches found.
 *
 * It is the responsibility of the the caller to clear the results list first;
 * otherwise, the newly-found files will be appended to the list.
 */

/**
 * This variant of find_all_files() returns the new Results object, instead of
 * filling on in on the parameter list.  This is a little more convenient to
 * call from Python.
 */
225
inline std::size_t ConfigVariableSearchPath::find_all_files(Filename const &filename, DSearchPath::Results &results) const;
inline DSearchPath::Results ConfigVariableSearchPath::find_all_files(Filename const &filename) const;

624 6 output 0 4 801 32 ConfigVariableSearchPath::output 0 1 253 10
/**
 *
 */
70
inline void ConfigVariableSearchPath::output(std::ostream &out) const;

625 5 write 0 4 801 31 ConfigVariableSearchPath::write 0 1 254 10
/**
 *
 */
69
inline void ConfigVariableSearchPath::write(std::ostream &out) const;

626 20 ConfigVariableString 0 260 803 42 ConfigVariableString::ConfigVariableString 0 3 256 257 258 22
/**
 *
 */

/**
 *
 */
334
inline ConfigVariableString::ConfigVariableString(std::string const &name);
inline ConfigVariableString::ConfigVariableString(std::string const &name, std::string const &default_value, std::string const &description = string(), int flags = 0);
inline ConfigVariableString::ConfigVariableString(ConfigVariableString const &) = default;

627 10 operator = 0 4 803 32 ConfigVariableString::operator = 0 1 259 0
71
inline void ConfigVariableString::operator =(std::string const &value);

628 17 operator typecast 0 132 803 39 ConfigVariableString::operator typecast 0 1 272 0
48
inline operator std::string const &(void) const;

629 5 c_str 0 4 803 27 ConfigVariableString::c_str 0 1 260 88
// These methods help the ConfigVariableString act like a C++ string object.

/**
 *
 */
59
inline char const *ConfigVariableString::c_str(void) const;

630 5 empty 0 4 803 27 ConfigVariableString::empty 0 1 261 10
/**
 *
 */
52
inline bool ConfigVariableString::empty(void) const;

631 6 length 0 4 803 28 ConfigVariableString::length 0 1 262 10
/**
 *
 */
60
inline std::size_t ConfigVariableString::length(void) const;

632 11 operator [] 0 4 803 33 ConfigVariableString::operator [] 0 1 263 0
67
inline char ConfigVariableString::operator [](std::size_t n) const;

633 11 operator == 0 4 803 33 ConfigVariableString::operator == 0 1 264 34
// Comparison operators are handy.
78
inline bool ConfigVariableString::operator ==(std::string const &other) const;

634 11 operator != 0 4 803 33 ConfigVariableString::operator != 0 1 265 0
78
inline bool ConfigVariableString::operator !=(std::string const &other) const;

635 10 operator < 0 4 803 32 ConfigVariableString::operator < 0 1 266 0
77
inline bool ConfigVariableString::operator <(std::string const &other) const;

636 9 set_value 0 4 803 31 ConfigVariableString::set_value 0 1 267 48
/**
 * Reassigns the variable's local value.
 */
70
inline void ConfigVariableString::set_value(std::string const &value);

637 9 get_value 0 4 803 31 ConfigVariableString::get_value 0 1 268 40
/**
 * Returns the variable's value.
 */
70
inline std::string const &ConfigVariableString::get_value(void) const;

638 17 get_default_value 0 4 803 39 ConfigVariableString::get_default_value 0 1 269 48
/**
 * Returns the variable's default value.
 */
71
inline std::string ConfigVariableString::get_default_value(void) const;

639 8 get_word 0 4 803 30 ConfigVariableString::get_word 0 1 270 44
/**
 * Returns the variable's nth value.
 */
71
inline std::string ConfigVariableString::get_word(std::size_t n) const;

640 8 set_word 0 4 803 30 ConfigVariableString::set_word 0 1 271 107
/**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */
84
inline void ConfigVariableString::set_word(std::size_t n, std::string const &value);

641 21 ~ConfigVariableString 0 516 803 43 ConfigVariableString::~ConfigVariableString 0 0 0
50
ConfigVariableString::~ConfigVariableString(void);

642 20 upcast_to_MemoryBase 0 12 804 36 NotifyCategory::upcast_to_MemoryBase 0 1 295 40
upcast from NotifyCategory to MemoryBase
55
MemoryBase *NotifyCategory::upcast_to_MemoryBase(void);

643 26 downcast_to_NotifyCategory 0 12 805 38 MemoryBase::downcast_to_NotifyCategory 0 0 42
downcast from MemoryBase to NotifyCategory
61
NotifyCategory *MemoryBase::downcast_to_NotifyCategory(void);

644 21 upcast_to_ConfigFlags 0 12 804 37 NotifyCategory::upcast_to_ConfigFlags 0 1 296 41
upcast from NotifyCategory to ConfigFlags
57
ConfigFlags *NotifyCategory::upcast_to_ConfigFlags(void);

645 26 downcast_to_NotifyCategory 0 12 768 39 ConfigFlags::downcast_to_NotifyCategory 0 0 43
downcast from ConfigFlags to NotifyCategory
62
NotifyCategory *ConfigFlags::downcast_to_NotifyCategory(void);

646 12 get_fullname 0 4 804 28 NotifyCategory::get_fullname 0 1 274 10
/**
 *
 */
60
inline std::string NotifyCategory::get_fullname(void) const;

647 12 get_basename 0 4 804 28 NotifyCategory::get_basename 0 1 275 10
/**
 *
 */
60
inline std::string NotifyCategory::get_basename(void) const;

648 12 get_severity 0 4 804 28 NotifyCategory::get_severity 0 1 276 10
/**
 *
 */
63
inline NotifySeverity NotifyCategory::get_severity(void) const;

649 12 set_severity 0 4 804 28 NotifyCategory::set_severity 0 1 277 150
/**
 * Sets the severity level of messages that will be reported from this
 * Category.  This allows any message of this severity level or higher.
 */
66
inline void NotifyCategory::set_severity(NotifySeverity severity);

650 5 is_on 0 4 804 21 NotifyCategory::is_on 0 1 278 110
/**
 * Returns true if messages of the indicated severity level ought to be
 * reported for this Category.
 */
65
inline bool NotifyCategory::is_on(NotifySeverity severity) const;

651 7 is_spam 0 4 804 23 NotifyCategory::is_spam 0 1 279 446
// When NOTIFY_DEBUG is not defined, the categories will never be set to
// "spam" or "debug" severities, and these methods are redefined to be
// static to make it more obvious to the compiler.  However, we still want
// to present a consistent interface to our scripting language, so during
// the interrogate pass (that is, when CPPPARSER is defined), we still
// pretend they're nonstatic.

/**
 * A shorthand way to write is_on(NS_spam).
 */
48
inline bool NotifyCategory::is_spam(void) const;

652 8 is_debug 0 4 804 24 NotifyCategory::is_debug 0 1 280 52
/**
 * A shorthand way to write is_on(NS_debug).
 */
49
inline bool NotifyCategory::is_debug(void) const;

653 7 is_info 0 4 804 23 NotifyCategory::is_info 0 1 281 51
/**
 * A shorthand way to write is_on(NS_info).
 */
48
inline bool NotifyCategory::is_info(void) const;

654 10 is_warning 0 4 804 26 NotifyCategory::is_warning 0 1 282 54
/**
 * A shorthand way to write is_on(NS_warning).
 */
51
inline bool NotifyCategory::is_warning(void) const;

655 8 is_error 0 4 804 24 NotifyCategory::is_error 0 1 283 52
/**
 * A shorthand way to write is_on(NS_error).
 */
49
inline bool NotifyCategory::is_error(void) const;

656 8 is_fatal 0 4 804 24 NotifyCategory::is_fatal 0 1 284 52
/**
 * A shorthand way to write is_on(NS_fatal).
 */
49
inline bool NotifyCategory::is_fatal(void) const;

657 3 out 0 4 804 19 NotifyCategory::out 0 1 285 277
/**
 * Begins a new message to this Category at the indicated severity level.  If
 * the indicated severity level is enabled, this writes a prefixing string to
 * the Notify::out() stream and returns that.  If the severity level is
 * disabled, this returns Notify::null().
 */
85
std::ostream &NotifyCategory::out(NotifySeverity severity, bool prefix = true) const;

658 4 spam 0 4 804 20 NotifyCategory::spam 0 1 286 49
/**
 * A shorthand way to write out(NS_spam).
 */
68
inline std::ostream &NotifyCategory::spam(bool prefix = true) const;

659 5 debug 0 4 804 21 NotifyCategory::debug 0 1 287 50
/**
 * A shorthand way to write out(NS_debug).
 */
69
inline std::ostream &NotifyCategory::debug(bool prefix = true) const;

660 4 info 0 4 804 20 NotifyCategory::info 0 1 288 49
/**
 * A shorthand way to write out(NS_info).
 */
68
inline std::ostream &NotifyCategory::info(bool prefix = true) const;

661 7 warning 0 4 804 23 NotifyCategory::warning 0 1 289 52
/**
 * A shorthand way to write out(NS_warning).
 */
71
inline std::ostream &NotifyCategory::warning(bool prefix = true) const;

662 5 error 0 4 804 21 NotifyCategory::error 0 1 290 50
/**
 * A shorthand way to write out(NS_error).
 */
69
inline std::ostream &NotifyCategory::error(bool prefix = true) const;

663 5 fatal 0 4 804 21 NotifyCategory::fatal 0 1 291 50
/**
 * A shorthand way to write out(NS_fatal).
 */
69
inline std::ostream &NotifyCategory::fatal(bool prefix = true) const;

664 16 get_num_children 0 4 804 32 NotifyCategory::get_num_children 0 1 292 78
/**
 * Returns the number of child Categories of this particular Category.
 */
57
std::size_t NotifyCategory::get_num_children(void) const;

665 9 get_child 0 4 804 25 NotifyCategory::get_child 0 1 293 70
/**
 * Returns the nth child Category of this particular Category.
 */
63
NotifyCategory *NotifyCategory::get_child(std::size_t i) const;

666 16 set_server_delta 0 4 804 32 NotifyCategory::set_server_delta 0 1 294 207
/**
 * Sets a global delta (in seconds) between the local time and the server's
 * time, for the purpose of synchronizing the time stamps in the log messages
 * of the client with that of a known server.
 */
61
static void NotifyCategory::set_server_delta(long int delta);

667 14 NotifyCategory 0 260 804 30 NotifyCategory::NotifyCategory 0 1 273 10
/**
 *
 */
72
inline NotifyCategory::NotifyCategory(NotifyCategory const &) = default;

668 15 ~NotifyCategory 0 516 804 31 NotifyCategory::~NotifyCategory 0 0 0
38
NotifyCategory::~NotifyCategory(void);

669 14 IDecryptStream 0 260 807 30 IDecryptStream::IDecryptStream 0 2 297 298 22
/**
 *
 */

/**
 *
 */
161
inline IDecryptStream::IDecryptStream(void);
inline explicit IDecryptStream::IDecryptStream(std::istream *source, bool owns_source, std::string const &password);

670 4 open 0 4 807 20 IDecryptStream::open 0 1 299 10
/**
 *
 */
113
inline IDecryptStream &IDecryptStream::open(std::istream *source, bool owns_source, std::string const &password);

671 5 close 0 4 807 21 IDecryptStream::close 0 1 300 124
/**
 * Resets the EncryptStream to empty, but does not actually close the source
 * istream unless owns_source was true.
 */
51
inline IDecryptStream &IDecryptStream::close(void);

672 13 get_algorithm 0 4 807 29 IDecryptStream::get_algorithm 0 1 301 74
/**
 * Returns the encryption algorithm that was read from the stream.
 */
68
inline std::string const &IDecryptStream::get_algorithm(void) const;

673 14 get_key_length 0 4 807 30 IDecryptStream::get_key_length 0 1 302 85
/**
 * Returns the encryption key length, in bits, that was read from the stream.
 */
54
inline int IDecryptStream::get_key_length(void) const;

674 19 get_iteration_count 0 4 807 35 IDecryptStream::get_iteration_count 0 1 303 63
/**
 * Returns the value that was was read from the stream.
 */
59
inline int IDecryptStream::get_iteration_count(void) const;

675 15 ~IDecryptStream 0 516 807 31 IDecryptStream::~IDecryptStream 0 0 0
38
IDecryptStream::~IDecryptStream(void);

676 14 OEncryptStream 0 260 809 30 OEncryptStream::OEncryptStream 0 2 304 305 22
/**
 *
 */

/**
 *
 */
157
inline OEncryptStream::OEncryptStream(void);
inline explicit OEncryptStream::OEncryptStream(std::ostream *dest, bool owns_dest, std::string const &password);

677 4 open 0 4 809 20 OEncryptStream::open 0 1 306 10
/**
 *
 */
109
inline OEncryptStream &OEncryptStream::open(std::ostream *dest, bool owns_dest, std::string const &password);

678 5 close 0 4 809 21 OEncryptStream::close 0 1 307 120
/**
 * Resets the EncryptStream to empty, but does not actually close the dest
 * ostream unless owns_dest was true.
 */
51
inline OEncryptStream &OEncryptStream::close(void);

679 13 set_algorithm 0 4 809 29 OEncryptStream::set_algorithm 0 1 308 383
/**
 * Specifies the encryption algorithm that should be used for future calls to
 * open().  The default is whatever is specified by the encryption-algorithm
 * config variable.  The complete set of available algorithms is defined by
 * the current version of OpenSSL.
 *
 * If an invalid algorithm is specified, there is no immediate error return
 * code, but open() will fail.
 */
72
inline void OEncryptStream::set_algorithm(std::string const &algorithm);

680 14 set_key_length 0 4 809 30 OEncryptStream::set_key_length 0 1 309 347
/**
 * Specifies the length of the key, in bits, that should be used to encrypt
 * the stream in future calls to open().  The default is whatever is specified
 * by the encryption-key-length config variable.
 *
 * If an invalid key_length for the chosen algorithm is specified, there is no
 * immediate error return code, but open() will fail.
 */
59
inline void OEncryptStream::set_key_length(int key_length);

681 19 set_iteration_count 0 4 809 35 OEncryptStream::set_iteration_count 0 1 310 495
/**
 * Specifies the number of times to repeatedly hash the key before writing it
 * to the stream in future calls to open().  Its purpose is to make it
 * computationally more expensive for an attacker to search the key space
 * exhaustively.  This should be a multiple of 1,000 and should not exceed
 * about 65 million; the value 0 indicates just one application of the hashing
 * algorithm.
 *
 * The default is whatever is specified by the encryption-iteration-count
 * config variable.
 */
69
inline void OEncryptStream::set_iteration_count(int iteration_count);

682 13 get_algorithm 0 4 809 29 OEncryptStream::get_algorithm 0 0 74
/**
 * Returns the encryption algorithm that was read from the stream.
 */
68
inline std::string const &OEncryptStream::get_algorithm(void) const;

683 14 get_key_length 0 4 809 30 OEncryptStream::get_key_length 0 0 85
/**
 * Returns the encryption key length, in bits, that was read from the stream.
 */
54
inline int OEncryptStream::get_key_length(void) const;

684 19 get_iteration_count 0 4 809 35 OEncryptStream::get_iteration_count 0 0 63
/**
 * Returns the value that was was read from the stream.
 */
59
inline int OEncryptStream::get_iteration_count(void) const;

685 15 ~OEncryptStream 0 516 809 31 OEncryptStream::~OEncryptStream 0 0 0
38
OEncryptStream::~OEncryptStream(void);

686 12 StreamReader 0 260 811 26 StreamReader::StreamReader 0 2 311 312 189
/**
 *
 */

/**
 * If owns_stream is true, the stream pointer will be deleted when the
 * StreamReader destructs.
 */

/**
 * The copy constructor does not copy ownership of the stream.
 */
140
inline explicit StreamReader::StreamReader(std::istream *in, bool owns_stream);
inline StreamReader::StreamReader(StreamReader const &copy);

687 10 operator = 0 4 811 24 StreamReader::operator = 0 1 313 0
63
inline void StreamReader::operator =(StreamReader const &copy);

688 13 ~StreamReader 0 516 811 27 StreamReader::~StreamReader 0 0 10
/**
 *
 */
41
inline StreamReader::~StreamReader(void);

689 11 get_istream 0 4 811 25 StreamReader::get_istream 0 1 314 37
/**
 * Returns the stream in use.
 */
59
inline std::istream *StreamReader::get_istream(void) const;

690 8 get_bool 0 4 811 22 StreamReader::get_bool 0 1 315 36
/**
 * Extracts a boolean value.
 */
41
inline bool StreamReader::get_bool(void);

691 8 get_int8 0 4 811 22 StreamReader::get_int8 0 1 316 43
/**
 * Extracts a signed 8-bit integer.
 */
43
inline int8_t StreamReader::get_int8(void);

692 9 get_uint8 0 4 811 23 StreamReader::get_uint8 0 1 317 46
/**
 * Extracts an unsigned 8-bit integer.
 */
45
inline uint8_t StreamReader::get_uint8(void);

693 9 get_int16 0 4 811 23 StreamReader::get_int16 0 1 318 44
/**
 * Extracts a signed 16-bit integer.
 */
45
inline int16_t StreamReader::get_int16(void);

694 9 get_int32 0 4 811 23 StreamReader::get_int32 0 1 319 44
/**
 * Extracts a signed 32-bit integer.
 */
45
inline int32_t StreamReader::get_int32(void);

695 9 get_int64 0 4 811 23 StreamReader::get_int64 0 1 320 44
/**
 * Extracts a signed 64-bit integer.
 */
45
inline int64_t StreamReader::get_int64(void);

696 10 get_uint16 0 4 811 24 StreamReader::get_uint16 0 1 321 47
/**
 * Extracts an unsigned 16-bit integer.
 */
47
inline uint16_t StreamReader::get_uint16(void);

697 10 get_uint32 0 4 811 24 StreamReader::get_uint32 0 1 322 47
/**
 * Extracts an unsigned 32-bit integer.
 */
47
inline uint32_t StreamReader::get_uint32(void);

698 10 get_uint64 0 4 811 24 StreamReader::get_uint64 0 1 323 47
/**
 * Extracts an unsigned 64-bit integer.
 */
47
inline uint64_t StreamReader::get_uint64(void);

699 11 get_float32 0 4 811 25 StreamReader::get_float32 0 1 324 186
/**
 * Extracts a 32-bit single-precision floating-point number.  Since this kind
 * of float is not necessarily portable across different architectures,
 * special care is required.
 */
45
inline float StreamReader::get_float32(void);

700 11 get_float64 0 4 811 25 StreamReader::get_float64 0 1 325 51
/**
 * Extracts a 64-bit floating-point number.
 */
50
inline PN_float64 StreamReader::get_float64(void);

701 12 get_be_int16 0 4 811 26 StreamReader::get_be_int16 0 1 326 55
/**
 * Extracts a signed big-endian 16-bit integer.
 */
48
inline int16_t StreamReader::get_be_int16(void);

702 12 get_be_int32 0 4 811 26 StreamReader::get_be_int32 0 1 327 55
/**
 * Extracts a signed big-endian 32-bit integer.
 */
48
inline int32_t StreamReader::get_be_int32(void);

703 12 get_be_int64 0 4 811 26 StreamReader::get_be_int64 0 1 328 55
/**
 * Extracts a signed big-endian 64-bit integer.
 */
48
inline int64_t StreamReader::get_be_int64(void);

704 13 get_be_uint16 0 4 811 27 StreamReader::get_be_uint16 0 1 329 58
/**
 * Extracts an unsigned big-endian 16-bit integer.
 */
50
inline uint16_t StreamReader::get_be_uint16(void);

705 13 get_be_uint32 0 4 811 27 StreamReader::get_be_uint32 0 1 330 58
/**
 * Extracts an unsigned big-endian 32-bit integer.
 */
50
inline uint32_t StreamReader::get_be_uint32(void);

706 13 get_be_uint64 0 4 811 27 StreamReader::get_be_uint64 0 1 331 58
/**
 * Extracts an unsigned big-endian 64-bit integer.
 */
50
inline uint64_t StreamReader::get_be_uint64(void);

707 14 get_be_float32 0 4 811 28 StreamReader::get_be_float32 0 1 332 197
/**
 * Extracts a 32-bit single-precision big-endian floating-point number.  Since
 * this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */
48
inline float StreamReader::get_be_float32(void);

708 14 get_be_float64 0 4 811 28 StreamReader::get_be_float64 0 1 333 62
/**
 * Extracts a 64-bit big-endian floating-point number.
 */
53
inline PN_float64 StreamReader::get_be_float64(void);

709 10 get_string 0 4 811 24 StreamReader::get_string 0 1 334 45
/**
 * Extracts a variable-length string.
 */
43
std::string StreamReader::get_string(void);

710 12 get_string32 0 4 811 26 StreamReader::get_string32 0 1 335 72
/**
 * Extracts a variable-length string with a 32-bit length field.
 */
45
std::string StreamReader::get_string32(void);

711 12 get_z_string 0 4 811 26 StreamReader::get_z_string 0 1 336 74
/**
 * Extracts a variable-length string, as a NULL-terminated string.
 */
45
std::string StreamReader::get_z_string(void);

712 16 get_fixed_string 0 4 811 30 StreamReader::get_fixed_string 0 1 337 128
/**
 * Extracts a fixed-length string.  However, if a zero byte occurs within the
 * string, it marks the end of the string.
 */
61
std::string StreamReader::get_fixed_string(std::size_t size);

713 10 skip_bytes 0 4 811 24 StreamReader::skip_bytes 0 1 338 66
/**
 * Skips over the indicated number of bytes in the stream.
 */
48
void StreamReader::skip_bytes(std::size_t size);

714 13 extract_bytes 0 4 811 27 StreamReader::extract_bytes 0 1 339 380
/**
 * Extracts the indicated number of bytes in the stream into the given
 * character buffer.  Assumes that the buffer is big enough to hold the
 * requested number of bytes.  Returns the number of bytes that were
 * successfully written.
 */

/**
 * Extracts the indicated number of bytes in the stream and returns them as a
 * string.  Returns empty string at end-of-file.
 */
136
std::size_t StreamReader::extract_bytes(unsigned char *into, std::size_t size);
PyObject *StreamReader::extract_bytes(std::size_t size);

715 8 readline 0 4 811 22 StreamReader::readline 0 1 340 303
/**
 * Assumes the stream represents a text file, and extracts one line up to and
 * including the trailing newline character.  Returns empty string when the
 * end of file is reached.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's File.readline() function.
 */
39
PyObject *StreamReader::readline(void);

716 9 readlines 0 4 811 23 StreamReader::readlines 0 1 341 0
40
PyObject *StreamReader::readlines(void);

717 12 StreamWriter 0 260 813 26 StreamWriter::StreamWriter 0 2 342 343 94
/**
 *
 */

/**
 *
 */

/**
 * The copy constructor does not copy ownership of the stream.
 */
141
inline explicit StreamWriter::StreamWriter(std::ostream *out, bool owns_stream);
inline StreamWriter::StreamWriter(StreamWriter const &copy);

718 10 operator = 0 4 813 24 StreamWriter::operator = 0 1 344 0
63
inline void StreamWriter::operator =(StreamWriter const &copy);

719 13 ~StreamWriter 0 516 813 27 StreamWriter::~StreamWriter 0 0 10
/**
 *
 */
41
inline StreamWriter::~StreamWriter(void);

720 11 get_ostream 0 4 813 25 StreamWriter::get_ostream 0 1 345 37
/**
 * Returns the stream in use.
 */
59
inline std::ostream *StreamWriter::get_ostream(void) const;

721 8 add_bool 0 4 813 22 StreamWriter::add_bool 0 1 346 46
/**
 * Adds a boolean value to the stream.
 */
47
inline void StreamWriter::add_bool(bool value);

722 8 add_int8 0 4 813 22 StreamWriter::add_int8 0 1 347 53
/**
 * Adds a signed 8-bit integer to the stream.
 */
49
inline void StreamWriter::add_int8(int8_t value);

723 9 add_uint8 0 4 813 23 StreamWriter::add_uint8 0 1 348 56
/**
 * Adds an unsigned 8-bit integer to the stream.
 */
51
inline void StreamWriter::add_uint8(uint8_t value);

724 9 add_int16 0 4 813 23 StreamWriter::add_int16 0 1 349 104
// The default numeric packing is little-endian.

/**
 * Adds a signed 16-bit integer to the stream.
 */
51
inline void StreamWriter::add_int16(int16_t value);

725 9 add_int32 0 4 813 23 StreamWriter::add_int32 0 1 350 54
/**
 * Adds a signed 32-bit integer to the stream.
 */
51
inline void StreamWriter::add_int32(int32_t value);

726 9 add_int64 0 4 813 23 StreamWriter::add_int64 0 1 351 54
/**
 * Adds a signed 64-bit integer to the stream.
 */
51
inline void StreamWriter::add_int64(int64_t value);

727 10 add_uint16 0 4 813 24 StreamWriter::add_uint16 0 1 352 57
/**
 * Adds an unsigned 16-bit integer to the stream.
 */
53
inline void StreamWriter::add_uint16(uint16_t value);

728 10 add_uint32 0 4 813 24 StreamWriter::add_uint32 0 1 353 57
/**
 * Adds an unsigned 32-bit integer to the stream.
 */
53
inline void StreamWriter::add_uint32(uint32_t value);

729 10 add_uint64 0 4 813 24 StreamWriter::add_uint64 0 1 354 57
/**
 * Adds an unsigned 64-bit integer to the stream.
 */
53
inline void StreamWriter::add_uint64(uint64_t value);

730 11 add_float32 0 4 813 25 StreamWriter::add_float32 0 1 355 196
/**
 * Adds a 32-bit single-precision floating-point number to the stream.  Since
 * this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */
51
inline void StreamWriter::add_float32(float value);

731 11 add_float64 0 4 813 25 StreamWriter::add_float64 0 1 356 61
/**
 * Adds a 64-bit floating-point number to the stream.
 */
56
inline void StreamWriter::add_float64(PN_float64 value);

732 12 add_be_int16 0 4 813 26 StreamWriter::add_be_int16 0 1 357 140
// These functions pack numbers big-endian, in case that's desired.

/**
 * Adds a signed 16-bit big-endian integer to the streamWriter.
 */
54
inline void StreamWriter::add_be_int16(int16_t value);

733 12 add_be_int32 0 4 813 26 StreamWriter::add_be_int32 0 1 358 71
/**
 * Adds a signed 32-bit big-endian integer to the streamWriter.
 */
54
inline void StreamWriter::add_be_int32(int32_t value);

734 12 add_be_int64 0 4 813 26 StreamWriter::add_be_int64 0 1 359 71
/**
 * Adds a signed 64-bit big-endian integer to the streamWriter.
 */
54
inline void StreamWriter::add_be_int64(int64_t value);

735 13 add_be_uint16 0 4 813 27 StreamWriter::add_be_uint16 0 1 360 74
/**
 * Adds an unsigned 16-bit big-endian integer to the streamWriter.
 */
56
inline void StreamWriter::add_be_uint16(uint16_t value);

736 13 add_be_uint32 0 4 813 27 StreamWriter::add_be_uint32 0 1 361 74
/**
 * Adds an unsigned 32-bit big-endian integer to the streamWriter.
 */
56
inline void StreamWriter::add_be_uint32(uint32_t value);

737 13 add_be_uint64 0 4 813 27 StreamWriter::add_be_uint64 0 1 362 74
/**
 * Adds an unsigned 64-bit big-endian integer to the streamWriter.
 */
56
inline void StreamWriter::add_be_uint64(uint64_t value);

738 14 add_be_float32 0 4 813 28 StreamWriter::add_be_float32 0 1 363 207
/**
 * Adds a 32-bit single-precision big-endian floating-point number to the
 * stream.  Since this kind of float is not necessarily portable across
 * different architectures, special care is required.
 */
54
inline void StreamWriter::add_be_float32(float value);

739 14 add_be_float64 0 4 813 28 StreamWriter::add_be_float64 0 1 364 78
/**
 * Adds a 64-bit big-endian floating-point number to the streamWriter.
 */
59
inline void StreamWriter::add_be_float64(PN_float64 value);

740 10 add_string 0 4 813 24 StreamWriter::add_string 0 1 365 107
/**
 * Adds a variable-length string to the stream.  This actually adds a count
 * followed by n bytes.
 */
61
inline void StreamWriter::add_string(std::string const &str);

741 12 add_string32 0 4 813 26 StreamWriter::add_string32 0 1 366 84
/**
 * Adds a variable-length string to the stream, using a 32-bit length field.
 */
63
inline void StreamWriter::add_string32(std::string const &str);

742 12 add_z_string 0 4 813 26 StreamWriter::add_z_string 0 1 367 84
/**
 * Adds a variable-length string to the stream, as a NULL-terminated string.
 */
56
inline void StreamWriter::add_z_string(std::string str);

743 16 add_fixed_string 0 4 813 30 StreamWriter::add_fixed_string 0 1 368 236
/**
 * Adds a fixed-length string to the stream.  If the string given is less than
 * the requested size, this will pad the string out with zeroes; if it is
 * greater than the requested size, this will silently truncate the string.
 */
85
inline void StreamWriter::add_fixed_string(std::string const &str, std::size_t size);

744 9 pad_bytes 0 4 813 23 StreamWriter::pad_bytes 0 1 369 65
/**
 * Adds the indicated number of zero bytes to the stream.
 */
47
void StreamWriter::pad_bytes(std::size_t size);

745 11 append_data 0 4 813 25 StreamWriter::append_data 0 1 370 140
/**
 * Appends some more raw data to the end of the streamWriter.
 */

/**
 * Appends some more raw data to the end of the streamWriter.
 */
47
void StreamWriter::append_data(PyObject *data);

746 5 flush 0 4 813 19 StreamWriter::flush 0 1 371 50
/**
 * Calls flush() on the underlying stream.
 */
38
inline void StreamWriter::flush(void);

747 5 write 0 4 813 19 StreamWriter::write 0 1 372 132
/**
 * A synonym of append_data().  This is useful when assigning the StreamWriter
 * to sys.stderr and/or sys.stdout in Python.
 */
56
inline void StreamWriter::write(std::string const &str);

748 13 get_softspace 0 20 813 27 StreamWriter::get_softspace 0 0 39
getter for int StreamWriter::softspace;
44
int StreamWriter::get_softspace(void) const;

749 13 set_softspace 0 36 813 27 StreamWriter::set_softspace 0 0 39
setter for int StreamWriter::softspace;
44
void StreamWriter::set_softspace(int value);

750 7 acquire 0 4 815 26 StreamWrapperBase::acquire 0 1 373 572
/**
 * Acquires the internal lock.
 *
 * User code should call this to take temporary possession of the stream and
 * perform direct I/O operations on it, for instance to make several
 * sequential atomic reads.  You may not call any of the StreamWrapper methods
 * while the lock is held, other than release().
 *
 * Use with extreme caution!  This is a very low-level, non-recursive lock.
 * You must call acquire() only once, and you must later call release()
 * exactly once.  Failing to do so may result in a hard deadlock with no
 * available debugging features.
 */
45
inline void StreamWrapperBase::acquire(void);

751 7 release 0 4 815 26 StreamWrapperBase::release 0 1 374 135
/**
 * Releases the internal lock.  Must be called exactly once following a call
 * to acquire().  See the cautions with acquire().
 */
45
inline void StreamWrapperBase::release(void);

752 18 ~StreamWrapperBase 0 516 815 37 StreamWrapperBase::~StreamWrapperBase 0 0 0
44
StreamWrapperBase::~StreamWrapperBase(void);

753 27 upcast_to_StreamWrapperBase 0 12 816 43 IStreamWrapper::upcast_to_StreamWrapperBase 0 1 377 47
upcast from IStreamWrapper to StreamWrapperBase
69
StreamWrapperBase *IStreamWrapper::upcast_to_StreamWrapperBase(void);

754 14 IStreamWrapper 0 260 816 30 IStreamWrapper::IStreamWrapper 0 1 375 22
/**
 *
 */

/**
 *
 */
69
inline explicit IStreamWrapper::IStreamWrapper(std::istream &stream);

755 15 ~IStreamWrapper 0 516 816 31 IStreamWrapper::~IStreamWrapper 0 0 10
/**
 *
 */
38
IStreamWrapper::~IStreamWrapper(void);

756 11 get_istream 0 4 816 27 IStreamWrapper::get_istream 0 1 376 55
/**
 * Returns the istream this object is wrapping.
 */
61
inline std::istream *IStreamWrapper::get_istream(void) const;

757 27 upcast_to_StreamWrapperBase 0 12 817 43 OStreamWrapper::upcast_to_StreamWrapperBase 0 1 380 47
upcast from OStreamWrapper to StreamWrapperBase
69
StreamWrapperBase *OStreamWrapper::upcast_to_StreamWrapperBase(void);

758 14 OStreamWrapper 0 260 817 30 OStreamWrapper::OStreamWrapper 0 1 378 22
/**
 *
 */

/**
 *
 */
69
inline explicit OStreamWrapper::OStreamWrapper(std::ostream &stream);

759 15 ~OStreamWrapper 0 516 817 31 OStreamWrapper::~OStreamWrapper 0 0 10
/**
 *
 */
38
OStreamWrapper::~OStreamWrapper(void);

760 11 get_ostream 0 4 817 27 OStreamWrapper::get_ostream 0 1 379 55
/**
 * Returns the ostream this object is wrapping.
 */
61
inline std::ostream *OStreamWrapper::get_ostream(void) const;

761 24 upcast_to_IStreamWrapper 0 12 818 39 StreamWrapper::upcast_to_IStreamWrapper 0 1 383 43
upcast from StreamWrapper to IStreamWrapper
62
IStreamWrapper *StreamWrapper::upcast_to_IStreamWrapper(void);

762 25 downcast_to_StreamWrapper 0 12 816 41 IStreamWrapper::downcast_to_StreamWrapper 0 0 45
downcast from IStreamWrapper to StreamWrapper
63
StreamWrapper *IStreamWrapper::downcast_to_StreamWrapper(void);

763 24 upcast_to_OStreamWrapper 0 12 818 39 StreamWrapper::upcast_to_OStreamWrapper 0 1 384 43
upcast from StreamWrapper to OStreamWrapper
62
OStreamWrapper *StreamWrapper::upcast_to_OStreamWrapper(void);

764 25 downcast_to_StreamWrapper 0 12 817 41 OStreamWrapper::downcast_to_StreamWrapper 0 0 45
downcast from OStreamWrapper to StreamWrapper
63
StreamWrapper *OStreamWrapper::downcast_to_StreamWrapper(void);

765 13 StreamWrapper 0 260 818 28 StreamWrapper::StreamWrapper 0 1 381 22
/**
 *
 */

/**
 *
 */
68
inline explicit StreamWrapper::StreamWrapper(std::iostream &stream);

766 14 ~StreamWrapper 0 516 818 29 StreamWrapper::~StreamWrapper 0 0 10
/**
 *
 */
36
StreamWrapper::~StreamWrapper(void);

767 12 get_iostream 0 4 818 27 StreamWrapper::get_iostream 0 1 382 56
/**
 * Returns the iostream this object is wrapping.
 */
62
inline std::iostream *StreamWrapper::get_iostream(void) const;

384
1 0 0 7 4 821 386 0 0 0 
2 0 0 15 4 821 386 0 0 1 6 param0 0 822  
3 0 0 6 7 779 0 0 136 /**
 * Returns a pointer to the global "default page".  This is the ConfigPage
 * that lists all variables' original default values.
 */ 0 
4 0 0 6 8 779 0 0 191 /**
 * Returns a pointer to the global "local page".  This is the ConfigPage that
 * lists the locally-assigned values for any variables in the world that have
 * such a local assignment.
 */ 0 
5 0 0 6 9 826 0 0 115 /**
 * Returns the name of the page.  If the page was loaded from a .prc file,
 * this is usually the filename.
 */ 1 4 this 3 824  
6 0 0 6 14 775 0 0 248 /**
 * Returns true if this is the special "default" or "local" page, or false if
 * it is an ordinary page, e.g.  an implicit page loaded from a prc file at
 * startup, or an explicit page created by
 * ConfigPageManager::make_explicit_page().
 */ 1 4 this 3 824  
7 0 0 6 15 775 0 0 180 /**
 * Returns true if the page was loaded by implicitly searching the config path
 * on startup, or false if it was explicitly loaded by dynamic code after
 * initial startup.
 */ 1 4 this 3 824  
8 0 0 4 19 827 0 0 363 /**
 * Changes the explicit sort order of this particular ConfigPage.  Lower-
 * numbered pages supercede higher-numbered pages.  Initially, all explicitly-
 * loaded pages have sort value 0, and implicitly-loaded pages (found on disk)
 * have sort value 10; you may set an individual page higher or lower to
 * influence its priority relative to other pages.
 */ 2 4 this 3 779  4 sort 1 776  
9 0 0 6 20 776 0 0 93 /**
 * Returns the explicit sort order of this particular ConfigPage.  See
 * set_sort().
 */ 1 4 this 3 824  
10 0 0 6 23 776 0 0 413 /**
 * Returns the sequence number of the page.
 *
 * Sequence numbers for a particular class (implicit vs.  explicit) of pages
 * are assigned as each page is loaded; each page is given a higher sequence
 * number than all the pages loaded before it.
 *
 * The implicit_load pages, which are discovered in the file system
 * automatically, have a different set of sequence numbers than the explicit
 * pages.
 */ 1 4 this 3 824  
11 0 0 6 24 776 0 0 302 /**
 * Returns the trust level associated with this page.  An untrusted page is
 * trust level 0; if the page was loaded from a signed .prc file, its trust
 * level is the index number of the certificate that signed it.  Generally, a
 * higher trust level value represents a greater level of trust.
 */ 1 4 this 3 824  
12 0 0 4 25 827 0 0 195 /**
 * Explicitly sets the trust level on this particular page.  Note that any
 * subsequent changes to the page, or to any variable declarations on it, will
 * reset the trust level to zero.
 */ 2 4 this 3 779  11 trust_level 1 776  
13 0 0 6 26 826 0 0 153 /**
 * Returns the raw binary signature that was found in the prc file, if any.
 * This method is probably not terribly useful for most applications.
 */ 1 4 this 3 824  
14 0 0 4 30 827 0 0 57 /**
 * Removes all of the declarations from the page.
 */ 1 4 this 3 779  
15 0 0 6 31 775 0 0 392 /**
 * Reads the contents of a complete prc file, as returned by the indicated
 * istream, into the current page file.  Returns true on success, or false on
 * some I/O error.
 *
 * This is a low-level interface.  Normally you do not need to call it
 * directly.  See the global functions load_prc_file() and unload_prc_file(),
 * defined in panda/src/putil, for a higher-level interface.
 */ 2 4 this 3 779  2 in 1 812  
16 0 0 6 32 775 0 0 155 /**
 * Automatically decrypts and reads the stream, given the indicated password.
 * Note that if the password is incorrect, the result may be garbage.
 */ 3 4 this 3 779  2 in 1 812  8 password 1 826  
17 0 0 6 33 777 0 0 83 /**
 * Adds the indicated variable/value pair as a new declaration on the page.
 */ 3 4 this 3 779  8 variable 1 780  5 value 1 826  
18 0 0 6 33 777 0 0 83 /**
 * Adds the indicated variable/value pair as a new declaration on the page.
 */ 3 4 this 3 779  8 variable 1 826  5 value 1 826  
19 0 0 6 34 775 0 0 171 /**
 * Removes the indicated declaration from the page and deletes it.  Returns
 * true if the declaration is successfully removed, false if it was not on the
 * page.
 */ 2 4 this 3 779  4 decl 1 777  
20 0 0 6 35 828 0 0 58 /**
 * Returns the number of declarations on the page.
 */ 1 4 this 3 824  
21 0 0 6 36 782 0 0 51 /**
 * Returns the nth declaration on the page.
 */ 2 4 this 3 824  1 n 1 828  
22 0 0 6 37 777 0 0 180 /**
 * Returns a modifiable pointer to the nth declaration on the page.  Any
 * modifications will appear in the output, if the page is written out with
 * ConfigPage::write().
 */ 2 4 this 3 779  1 n 1 828  
23 0 0 6 38 826 0 0 73 /**
 * Returns the variable named by the nth declaration on the page.
 */ 2 4 this 3 824  1 n 1 828  
24 0 0 6 39 826 0 0 73 /**
 * Returns the value assigned by the nth declaration on the page.
 */ 2 4 this 3 824  1 n 1 828  
25 0 0 6 40 775 0 0 105 /**
 * Returns true if the nth active variable on the page has been used by code,
 * false otherwise.
 */ 2 4 this 3 824  1 n 1 828  
26 0 0 4 119 827 0 0 10 /**
 *
 */ 2 4 this 3 824  3 out 1 814  
27 0 0 4 120 827 0 0 61 /**
 * Outputs the first few hex digits of the signature.
 */ 2 4 this 3 824  3 out 1 814  
28 0 0 4 121 827 0 0 10 /**
 *
 */ 2 4 this 3 824  3 out 1 814  
29 0 0 6 44 779 0 0 67 /**
 * Returns the page on which this declaration can be found.
 */ 1 4 this 3 782  
30 0 0 6 45 780 0 0 148 /**
 * Returns the variable that this declaration names.  This variable may or may
 * not have been defined by the time the declaration is read.
 */ 1 4 this 3 782  
31 0 0 6 97 826 0 0 186 /**
 * Returns the value assigned to this variable.  This is the original one-line
 * text defined for the variable in the .prc file (or passed to
 * ConfigPage::make_declaration()).
 */ 1 4 this 3 782  
32 0 0 4 98 827 0 0 55 /**
 * Changes the value assigned to this variable.
 */ 2 4 this 3 777  5 value 1 826  
33 0 0 6 99 828 0 0 156 /**
 * Returns the number of words in the declaration's value.  A word is defined
 * as a sequence of non-whitespace characters delimited by whitespace.
 */ 1 4 this 3 782  
34 0 0 6 100 775 0 0 184 /**
 * Returns true if the declaration's value has a valid string value for the
 * nth word.  This is really the same thing as asking if there are at least n
 * words in the value.
 */ 2 4 this 3 782  1 n 1 828  
35 0 0 6 101 775 0 0 97 /**
 * Returns true if the declaration's value has a valid boolean value for the
 * nth word.
 */ 2 4 this 3 782  1 n 1 828  
36 0 0 6 102 775 0 0 97 /**
 * Returns true if the declaration's value has a valid integer value for the
 * nth word.
 */ 2 4 this 3 782  1 n 1 828  
37 0 0 6 103 775 0 0 95 /**
 * Returns true if the declaration's value has a valid int64 value for the nth
 * word.
 */ 2 4 this 3 782  1 n 1 828  
38 0 0 6 104 775 0 0 97 /**
 * Returns true if the declaration's value has a valid integer value for the
 * nth word.
 */ 2 4 this 3 782  1 n 1 828  
39 0 0 6 105 826 0 0 153 /**
 * Returns the string value of the nth word of the declaration's value, or
 * empty string if there is no nth value.  See also has_string_word().
 */ 2 4 this 3 782  1 n 1 828  
40 0 0 6 106 775 0 0 145 /**
 * Returns the boolean value of the nth word of the declaration's value, or
 * false if there is no nth value.  See also has_bool_word().
 */ 2 4 this 3 782  1 n 1 828  
41 0 0 6 107 776 0 0 140 /**
 * Returns the integer value of the nth word of the declaration's value, or 0
 * if there is no nth value.  See also has_int_word().
 */ 2 4 this 3 782  1 n 1 828  
42 0 0 6 108 798 0 0 140 /**
 * Returns the int64 value of the nth word of the declaration's value, or 0 if
 * there is no nth value.  See also has_int64_word().
 */ 2 4 this 3 782  1 n 1 828  
43 0 0 6 109 793 0 0 143 /**
 * Returns the integer value of the nth word of the declaration's value, or 0
 * if there is no nth value.  See also has_double_word().
 */ 2 4 this 3 782  1 n 1 828  
44 0 0 4 110 827 0 0 92 /**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */ 3 4 this 3 777  1 n 1 828  5 value 1 826  
45 0 0 4 111 827 0 0 92 /**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */ 3 4 this 3 777  1 n 1 828  5 value 1 775  
46 0 0 4 112 827 0 0 92 /**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */ 3 4 this 3 777  1 n 1 828  5 value 1 776  
47 0 0 4 113 827 0 0 92 /**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */ 3 4 this 3 777  1 n 1 828  5 value 1 798  
48 0 0 4 114 827 0 0 92 /**
 * Changes the nth word to the indicated value without affecting the other
 * words.
 */ 3 4 this 3 777  1 n 1 828  5 value 1 793  
49 0 0 7 115 830 0 0 100 /**
 * Interprets the string value as a filename and returns it, with any
 * variables expanded.
 */ 1 4 this 3 782  
50 0 0 6 116 776 0 0 254 /**
 * Returns the sequence number of the declaration within the page.  Sequence
 * numbers are assigned as each declaration is created; each declaration is
 * given a higher sequence number than all the declarations created in the
 * page before it.
 */ 1 4 this 3 782  
51 0 0 4 117 827 0 0 10 /**
 *
 */ 2 4 this 3 782  3 out 1 814  
52 0 0 4 118 827 0 0 10 /**
 *
 */ 2 4 this 3 782  3 out 1 814  
53 0 0 6 51 826 0 0 44 /**
 * Returns the name of the variable.
 */ 1 4 this 3 831  
54 0 0 6 52 775 0 0 118 /**
 * Returns true if the variable has been referenced by a ConfigVariable
 * somewhere in code, false otherwise.
 */ 1 4 this 3 831  
55 0 0 6 53 769 0 0 125 /**
 * Returns the stated type of this variable.  If the variable has not yet been
 * defined, this will be VT_undefined.
 */ 1 4 this 3 831  
56 0 0 6 54 826 0 0 82 /**
 * Returns the brief description of this variable, if it has been defined.
 */ 1 4 this 3 831  
57 0 0 6 55 776 0 0 250 /**
 * Returns the flags value as set by set_flags().  This includes the trust
 * level and some other settings.  See the individual methods is_closed(),
 * get_trust_level(), etc.  to pull out the semantic meaning of these flags
 * individually.
 */ 1 4 this 3 831  
58 0 0 6 56 775 0 0 413 /**
 * Returns true if the variable is not trusted by any prc file (and hence
 * cannot be modified from its compiled-in default value), or false for the
 * normal case, in which the variable can be modified by any prc file at or
 * above its trust level (see get_trust_level()).
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */ 1 4 this 3 831  
59 0 0 6 57 776 0 0 479 /**
 * Returns the minimum trust_level a prc file must demonstrate in order to
 * redefine the value for this variable.  Arguably, this should be called the
 * "mistrust level", since the larger the value, the more suspicious we are of
 * prc files.  This value is not used if is_closed() returns true, which
 * indicates no file may be trusted.
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */ 1 4 this 3 831  
60 0 0 6 58 775 0 0 232 /**
 * Returns true if the variable was indicated as "dynamic" by its constructor,
 * indicating that its name was dynamically generated, possibly from a large
 * pool, and it should not be listed along with the other variables.
 */ 1 4 this 3 831  
61 0 0 6 59 782 0 0 137 /**
 * Returns the default variable specified for this variable.  If the variable
 * has not yet been defined, this will return NULL.
 */ 1 4 this 3 831  
62 0 0 4 60 827 0 0 212 /**
 * Specifies the type of this variable.  See get_value_type().  It is not an
 * error to call this multiple times, but if the value changes once
 * get_declaration() has been called, a warning is printed.
 */ 2 4 this 3 780  10 value_type 1 769  
63 0 0 4 61 827 0 0 214 /**
 * Specifies the trust level of this variable.  See get_flags().  It is not an
 * error to call this multiple times, but if the value changes once
 * get_declaration() has been called, a warning is printed.
 */ 2 4 this 3 780  5 flags 1 776  
64 0 0 4 62 827 0 0 232 /**
 * Specifies the one-line description of this variable.  See
 * get_description().  It is not an error to call this multiple times, but if
 * the value changes once get_declaration() has been called, a warning is
 * printed.
 */ 2 4 this 3 780  11 description 1 826  
65 0 0 4 63 827 0 0 97 /**
 * Specifies the default value for this variable if it is not defined in any
 * prc file.
 */ 2 4 this 3 780  13 default_value 1 826  
66 0 0 4 64 827 0 0 75 /**
 * Marks that the variable has been "declared" by a ConfigVariable.
 */ 1 4 this 3 780  
67 0 0 6 65 777 0 0 324 /**
 * Creates a new local value for this variable, if there is not already one
 * specified.  This will shadow any values defined in the various .prc files.
 *
 * If there is already a local value defined for this variable, simply returns
 * that one.
 *
 * Use clear_local_value() to remove the local value definition.
 */ 1 4 this 3 780  
68 0 0 6 66 775 0 0 240 /**
 * Removes the local value defined for this variable, and allows its value to
 * be once again retrieved from the .prc files.
 *
 * Returns true if the value was successfully removed, false if it did not
 * exist in the first place.
 */ 1 4 this 3 780  
69 0 0 6 67 775 0 0 148 /**
 * Returns true if this variable's value has been shadowed by a local
 * assignment (as created via make_local_value()), or false otherwise.
 */ 1 4 this 3 831  
70 0 0 6 68 775 0 0 149 /**
 * Returns true if this variable has an explicit value, either from a prc file
 * or locally set, or false if variable has its default value.
 */ 1 4 this 3 831  
71 0 0 6 69 828 0 0 194 /**
 * Returns the number of declarations that contribute to this variable's
 * value.  If the variable has been defined, this will always be at least 1
 * (for the default value, at least).
 */ 1 4 this 3 831  
72 0 0 6 70 782 0 0 285 /**
 * Returns the nth declarations that contributes to this variable's value.
 * The declarations are arranged in order such that earlier declarations
 * shadow later declarations; thus, get_declaration(0) is always defined and
 * always returns the current value of the variable.
 */ 2 4 this 3 831  1 n 1 828  
73 0 0 6 72 828 0 0 157 /**
 * Returns the number of prc files that reference this variable.  This is not
 * exactly the same as the number of declarations; see get_reference().
 */ 1 4 this 3 831  
74 0 0 6 73 782 0 0 335 /**
 * Returns the nth declaration in a prc file that references this variable.
 * This is similar, but not identical to, get_declaration().  The difference
 * is that this will list *only* true references in a prc file, and will not
 * list default values or locally-assigned values; it also will list even the
 * untrusted files.
 */ 2 4 this 3 831  1 n 1 828  
75 0 0 6 75 828 0 0 115 /**
 * Returns the number of trusted prc files that reference this variable.  See
 * also get_num_references().
 */ 1 4 this 3 831  
76 0 0 6 76 782 0 0 428 /**
 * Returns the nth declaration in a trusted prc file that references this
 * variable.  This is similar, but not identical to, get_declaration().  The
 * difference is that this will list *only* true references in a prc file, and
 * will not list default values or locally-assigned values.
 *
 * This is also similar to get_reference(), except that it only lists the
 * trusted declarations, omitting the untrusted ones.
 */ 2 4 this 3 831  1 n 1 828  
77 0 0 6 78 828 0 0 107 /**
 * Returns the number of trusted, unique (by string value) values there exist
 * for this variable.
 */ 1 4 this 3 831  
78 0 0 6 79 782 0 0 157 /**
 * Returns the nth trusted, unique value for this variable.  This is similar
 * to get_trusted_reference(), except that duplicate values are removed.
 */ 2 4 this 3 831  1 n 1 828  
79 0 0 4 84 827 0 0 10 /**
 *
 */ 2 4 this 3 831  3 out 1 814  
80 0 0 4 85 827 0 0 10 /**
 *
 */ 2 4 this 3 831  3 out 1 814  
81 0 0 7 124 833 465 0 10 /**
 *
 */ 0 
82 0 0 15 124 833 465 0 0 1 6 param0 0 834  
83 0 0 36 126 827 0 0 0 3 4 this 3 833  11 ostream_ptr 1 836  12 delete_later 1 775  
84 0 0 6 127 814 0 0 67 /**
 * Returns the system-wide ostream for all Notify messages.
 */ 1 4 this 3 834  
85 0 0 4 129 827 0 0 110 /**
 * Removes the installed assert handler and restores default behavior of
 * nassertr() and nassertv().
 */ 1 4 this 3 833  
86 0 0 6 130 775 0 0 85 /**
 * Returns true if a user assert handler has been installed, false otherwise.
 */ 1 4 this 3 834  
87 0 0 4 131 827 0 0 110 /**
 * Returns a pointer to the user-installed assert handler, if one was
 * installed, or NULL otherwise.
 */ 1 4 this 3 834  
88 0 0 6 132 775 0 0 637 /**
 * Returns true if an assertion test has failed (and not been ignored) since
 * the last call to clear_assert_failed().
 *
 * When an assertion test fails, the assert handler may decide either to
 * abort, return, or ignore the assertion.  Naturally, if it decides to abort,
 * this flag is irrelevant.  If it chooses to ignore the assertion, the flag
 * is not set.  However, if the assert handler chooses to return out of the
 * function (the normal case), it will also set this flag to indicate that an
 * assertion failure has occurred.
 *
 * This will also be the behavior in the absence of a user-defined assert
 * handler.
 */ 1 4 this 3 834  
89 0 0 6 133 826 0 0 100 /**
 * Returns the error message that corresponds to the assertion that most
 * recently failed.
 */ 1 4 this 3 834  
90 0 0 4 134 827 0 0 114 /**
 * Resets the assert_failed flag that is set whenever an assertion test fails.
 * See has_assert_failed().
 */ 1 4 this 3 833  
91 0 0 6 135 806 0 0 129 /**
 * Returns the topmost Category in the hierarchy.  This may be used to
 * traverse the hierarchy of available Categories.
 */ 1 4 this 3 833  
92 0 0 6 136 806 0 0 325 /**
 * Finds or creates a new Category given the fullname of the Category.  This
 * name should be a sequence of colon-separated names of parent Categories,
 * ending in the basename of this Category, e.g.  display:glxdisplay.  This is
 * a shorthand way to define a Category when a pointer to its parent is not
 * handy.
 */ 2 4 this 3 833  8 fullname 1 826  
93 0 0 6 136 806 0 0 199 /**
 * Finds or creates a new Category given the basename of the category and its
 * parent in the category hierarchy.  The parent pointer may be NULL to
 * indicate this is a top-level Category.
 */ 3 4 this 3 833  8 basename 1 826  15 parent_category 1 806  
94 0 0 6 136 806 0 0 316 /**
 * Finds or creates a new Category given the basename of the category and the
 * fullname of its parent.  This is another way to create a category when you
 * don't have a pointer to its parent handy, but you know the name of its
 * parent.  If the parent Category does not already exist, it will be created.
 */ 3 4 this 3 833  8 basename 1 826  15 parent_fullname 1 826  
95 0 0 6 137 814 0 0 189 /**
 * A convenient way to get the ostream that should be written to for a Notify-
 * type message.  Also see Category::out() for a message that is specific to a
 * particular Category.
 */ 0 
96 0 0 6 138 814 0 0 159 /**
 * A convenient way to get an ostream that doesn't do anything.  Returned by
 * Category::out() when a particular Category and/or Severity is disabled.
 */ 0 
97 0 0 4 139 827 0 0 209 /**
 * A convenient way for scripting languages, which may know nothing about
 * ostreams, to write to Notify.  This writes a single string, followed by an
 * implicit newline, to the Notify output stream.
 */ 1 3 str 1 826  
98 0 0 6 140 833 0 0 104 /**
 * Returns the pointer to the global Notify object.  There is only one of
 * these in the world.
 */ 0 
99 0 0 6 142 775 0 0 157 /**
 * Returns true if the implicit `*.prc` files have already been loaded, false
 * otherwise.  Normally this will only be false briefly before startup.
 */ 1 4 this 3 839  
100 0 0 4 143 827 0 0 252 /**
 * Searches the PRC_DIR and/or PRC_PATH directories for `*.prc` files and loads
 * them in as pages.  This is normally called automatically at startup time,
 * when the first variable's value is referenced.  See also
 * reload_implicit_pages().
 */ 1 4 this 3 841  
101 0 0 4 144 827 0 0 211 /**
 * Searches the PRC_DIR and/or PRC_PATH directories for *.prc files and loads
 * them in as pages.
 *
 * This may be called after startup, to force the system to re-read all of the
 * implicit prc files.
 */ 1 4 this 3 841  
102 0 0 6 145 842 0 0 274 /**
 * Returns the search path used to locate implicit .prc files.  This is
 * determined by the PRC_DIR and PRC_PATH environment variables.  The object
 * returned by this method may be modified to change the path at runtime, and
 * then reload_implicit_pages() called.
 */ 1 4 this 3 841  
103 0 0 6 146 828 0 0 270 /**
 * Returns the number of patterns, like `*.prc`, that are compiled in that
 * will be searched for as default config filenames.  Normally there is only
 * one pattern, and it is `*.prc`, but others may be specified with the
 * PRC_FILENAME variable in Config.pp.
 */ 1 4 this 3 839  
104 0 0 6 147 826 0 0 131 /**
 * Returns the nth filename pattern that will be considered a match as a valid
 * config file.  See get_num_prc_patterns().
 */ 2 4 this 3 839  1 n 1 828  
105 0 0 6 148 828 0 0 170 /**
 * Returns the number of patterns, like `*.pre`, that are compiled in that
 * will be searched for as special config files that are understood to be
 * encrypted.
 */ 1 4 this 3 839  
106 0 0 6 149 826 0 0 151 /**
 * Returns the nth filename pattern that will be considered a match as a valid
 * encrypted config file.  See get_num_prc_encrypted_patterns().
 */ 2 4 this 3 839  1 n 1 828  
107 0 0 6 150 828 0 0 232 /**
 * Returns the number of patterns, like `*.exe`, that are compiled in that
 * will be searched for as special config files that are to be executed as a
 * program, and their output taken to be input.  This is normally empty.
 */ 1 4 this 3 839  
108 0 0 6 151 826 0 0 159 /**
 * Returns the nth filename pattern that will be considered a match as a valid
 * executable-style config file.  See get_num_prc_executable_patterns().
 */ 2 4 this 3 839  1 n 1 828  
109 0 0 6 152 779 0 0 206 /**
 * Creates and returns a new, empty ConfigPage.  This page will be stacked on
 * top of any pages that were created before; it may shadow variable
 * declarations that are defined in previous pages.
 */ 2 4 this 3 841  4 name 1 826  
110 0 0 6 153 775 0 0 318 /**
 * Removes a previously-constructed ConfigPage from the set of active pages,
 * and deletes it.  The ConfigPage object is no longer valid after this call.
 * Returns true if the page is successfully deleted, or false if it was
 * unknown (which should never happen if the page was legitimately
 * constructed).
 */ 2 4 this 3 841  4 page 1 779  
111 0 0 6 154 828 0 0 174 /**
 * Returns the current number of implicitly-loaded ConfigPages in the world.
 * These represent files that were automatically discovered on the disk as
 * .prc files.
 */ 1 4 this 3 839  
112 0 0 6 155 779 0 0 94 /**
 * Returns the nth implicit ConfigPage in the world.  See
 * get_num_implicit_pages().
 */ 2 4 this 3 839  1 n 1 828  
113 0 0 6 156 828 0 0 214 /**
 * Returns the current number of explicitly-loaded ConfigPages in the world.
 * These represent pages that were loaded dynamically at runtime by explicit
 * calls to ConfigPageManager::make_explicit_page().
 */ 1 4 this 3 839  
114 0 0 6 157 779 0 0 94 /**
 * Returns the nth explicit ConfigPage in the world.  See
 * get_num_explicit_pages().
 */ 2 4 this 3 839  1 n 1 828  
115 0 0 4 158 827 0 0 10 /**
 *
 */ 2 4 this 3 839  3 out 1 814  
116 0 0 4 159 827 0 0 10 /**
 *
 */ 2 4 this 3 839  3 out 1 814  
117 0 0 6 160 841 0 0 10 /**
 *
 */ 0 
118 0 0 6 169 780 0 0 181 /**
 * Creates and returns a new, undefined ConfigVariableCore with the indicated
 * name; or if a variable with this name has already been created, returns
 * that one instead.
 */ 2 4 this 3 843  4 name 1 826  
119 0 0 6 170 780 0 0 899 /**
 * Defines a variable "template" to match against dynamically-defined
 * variables that may or may not be created in the future.
 *
 * The template consists of a glob pattern, e.g.  `notify-level-*`, which will
 * be tested against any config variable passed to a future call to
 * make_variable().  If the pattern matches, the returned ConfigVariableCore
 * is copied to define the new variable, instead of creating a default, empty
 * one.
 *
 * This is useful to pre-specify default values for a family of variables that
 * all have similar properties, and all may not be created at the same time.
 * It is especially useful to avoid cluttering up the list of available
 * variables with user-declared variables that have not been defined yet by
 * the application (e.g. `egg-object-type-*`).
 *
 * This method basically pre-defines all variables that match the specified
 * glob pattern.
 */ 6 4 this 3 843  7 pattern 1 826  4 type 1 769  13 default_value 1 826  11 description 5 826  5 flags 5 776  
120 0 0 6 171 828 0 0 81 /**
 * Returns the current number of active ConfigVariableCores in the world.
 */ 1 4 this 3 844  
121 0 0 6 172 780 0 0 66 /**
 * Returns the nth active ConfigVariableCore in the world.
 */ 2 4 this 3 844  1 n 1 828  
122 0 0 6 174 826 0 0 73 /**
 * Returns the name of the nth active ConfigVariable in the list.
 */ 2 4 this 3 844  1 n 1 828  
123 0 0 6 175 775 0 0 111 /**
 * Returns true if the nth active ConfigVariable in the list has been used by
 * code, false otherwise.
 */ 2 4 this 3 844  1 n 1 828  
124 0 0 4 177 827 0 0 10 /**
 *
 */ 2 4 this 3 844  3 out 1 814  
125 0 0 4 178 827 0 0 10 /**
 *
 */ 2 4 this 3 844  3 out 1 814  
126 0 0 4 179 827 0 0 343 /**
 * Writes all of the prc-set config variables, as they appear in a prc file
 * somewhere, one per line, very concisely.  This lists the dominant value in
 * the prc file; it does not list shadowed values, and it does not list
 * locally-set values.
 *
 * This is mainly intended for generating a hash of the input config file
 * state.
 */ 2 4 this 3 844  3 out 1 814  
127 0 0 4 180 827 0 0 133 /**
 * Writes a list of all the variables that have been defined in a prc file
 * without having been declared somewhere in code.
 */ 1 4 this 3 844  
128 0 0 4 181 827 0 0 123 /**
 * Writes a list of all the variables that have been declared somewhere in
 * code, along with a brief description.
 */ 1 4 this 3 844  
129 0 0 4 182 827 0 0 241 /**
 * Writes a list of all the "dynamic" variables that have been declared
 * somewhere in code, along with a brief description.  This is a (usually
 * large) list of config variables that are declared with a generated variable
 * name.
 */ 1 4 this 3 844  
130 0 0 6 183 843 0 0 10 /**
 *
 */ 0 
131 0 0 6 185 826 0 0 44 /**
 * Returns the name of the variable.
 */ 1 4 this 3 846  
132 0 0 6 186 769 0 0 132 /**
 * Returns the stated type of this variable.  This should be VT_list, unless a
 * later variable declaration has changed it.
 */ 1 4 this 3 846  
133 0 0 6 187 826 0 0 82 /**
 * Returns the brief description of this variable, if it has been defined.
 */ 1 4 this 3 846  
134 0 0 6 188 776 0 0 250 /**
 * Returns the flags value as set by set_flags().  This includes the trust
 * level and some other settings.  See the individual methods is_closed(),
 * get_trust_level(), etc.  to pull out the semantic meaning of these flags
 * individually.
 */ 1 4 this 3 846  
135 0 0 6 189 775 0 0 413 /**
 * Returns true if the variable is not trusted by any prc file (and hence
 * cannot be modified from its compiled-in default value), or false for the
 * normal case, in which the variable can be modified by any prc file at or
 * above its trust level (see get_trust_level()).
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */ 1 4 this 3 846  
136 0 0 6 190 776 0 0 479 /**
 * Returns the minimum trust_level a prc file must demonstrate in order to
 * redefine the value for this variable.  Arguably, this should be called the
 * "mistrust level", since the larger the value, the more suspicious we are of
 * prc files.  This value is not used if is_closed() returns true, which
 * indicates no file may be trusted.
 *
 * This value only has effect in a release build (specifically, when
 * PRC_RESPECT_TRUST_LEVEL is defined true in Config.pp).
 */ 1 4 this 3 846  
137 0 0 6 191 775 0 0 232 /**
 * Returns true if the variable was indicated as "dynamic" by its constructor,
 * indicating that its name was dynamically generated, possibly from a large
 * pool, and it should not be listed along with the other variables.
 */ 1 4 this 3 846  
138 0 0 6 198 775 0 0 240 /**
 * Removes the local value defined for this variable, and allows its value to
 * be once again retrieved from the .prc files.
 *
 * Returns true if the value was successfully removed, false if it did not
 * exist in the first place.
 */ 1 4 this 3 848  
139 0 0 6 199 775 0 0 148 /**
 * Returns true if this variable's value has been shadowed by a local
 * assignment (as created via make_local_value()), or false otherwise.
 */ 1 4 this 3 846  
140 0 0 6 200 775 0 0 149 /**
 * Returns true if this variable has an explicit value, either from a prc file
 * or locally set, or false if variable has its default value.
 */ 1 4 this 3 846  
141 0 0 4 201 827 0 0 10 /**
 *
 */ 2 4 this 3 846  3 out 1 814  
142 0 0 4 202 827 0 0 10 /**
 *
 */ 2 4 this 3 846  3 out 1 814  
143 0 0 15 204 851 526 0 0 1 6 param0 0 849  
144 0 0 7 204 851 526 0 230 /**
 * Use this constructor to make a ConfigVariable of an unspecified type.
 * Usually you'd want to do this just to reference a previously-defined
 * ConfigVariable of a specific type, without having to know what type it is.
 */ 1 4 name 1 826  
145 0 0 6 206 826 0 0 77 /**
 * Returns the toplevel value of the variable, formatted as a string.
 */ 1 4 this 3 849  
146 0 0 4 207 827 0 0 177 /**
 * Changes the value assigned to this variable.  This creates a local value
 * that shadows any values defined in the .prc files, until
 * clear_local_value() is called.
 */ 2 4 this 3 851  5 value 1 826  
147 0 0 4 208 827 0 0 129 /**
 * Removes the value assigned to this variable, and lets its original value
 * (as read from the prc files) show through.
 */ 1 4 this 3 851  
148 0 0 6 209 828 0 0 153 /**
 * Returns the number of words in the variable's value.  A word is defined as
 * a sequence of non-whitespace characters delimited by whitespace.
 */ 1 4 this 3 849  
149 0 0 15 211 854 541 0 0 1 6 param0 0 852  
150 0 0 23 211 854 541 0 10 /**
 *
 */ 1 4 name 1 826  
151 0 0 23 211 854 541 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 775  11 description 5 826  5 flags 5 776  
152 0 0 23 211 854 541 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 5 826  5 flags 5 776  
153 0 0 6 212 854 0 0 0 2 4 this 3 854  5 value 1 775  
154 0 0 6 214 828 0 0 62 /**
 * Returns the number of unique words in the variable.
 */ 1 4 this 3 852  
155 0 0 6 215 775 0 0 0 2 4 this 3 852  1 n 1 828  
156 0 0 4 216 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 854  5 value 1 775  
157 0 0 6 217 775 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 852  
158 0 0 6 218 775 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 852  
159 0 0 6 221 775 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 852  1 n 1 828  
160 0 0 4 222 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 854  1 n 1 828  5 value 1 775  
161 0 0 6 213 775 0 0 0 1 4 this 3 852  
162 0 0 15 225 857 552 0 0 1 6 param0 0 855  
163 0 0 23 225 857 552 0 10 /**
 *
 */ 1 4 name 1 826  
164 0 0 23 225 857 552 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 793  11 description 5 826  5 flags 5 776  
165 0 0 23 225 857 552 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 5 826  5 flags 5 776  
166 0 0 6 226 857 0 0 0 2 4 this 3 857  5 value 1 793  
167 0 0 6 228 828 0 0 62 /**
 * Returns the number of unique words in the variable.
 */ 1 4 this 3 855  
168 0 0 6 229 793 0 0 0 2 4 this 3 855  1 n 1 828  
169 0 0 4 230 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 857  5 value 1 793  
170 0 0 6 231 793 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 855  
171 0 0 6 232 793 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 855  
172 0 0 6 236 793 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 855  1 n 1 828  
173 0 0 4 237 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 857  1 n 1 828  5 value 1 793  
174 0 0 6 227 793 0 0 0 1 4 this 3 855  
175 0 0 15 240 860 575 0 0 1 6 param0 0 858  
176 0 0 23 240 860 575 0 10 /**
 *
 */ 1 4 name 1 826  
177 0 0 23 240 860 575 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 861  11 description 5 826  5 flags 5 776  
178 0 0 6 241 860 0 0 0 2 4 this 3 860  5 value 1 861  
179 0 0 6 243 826 0 0 76 // These methods help the ConfigVariableFilename act like a Filename object. 1 4 this 3 858  
180 0 0 6 244 775 0 0 10 /**
 *
 */ 1 4 this 3 858  
181 0 0 6 245 828 0 0 10 /**
 *
 */ 1 4 this 3 858  
182 0 0 6 246 863 0 0 0 2 4 this 3 858  1 n 1 828  
183 0 0 6 247 826 0 0 183 /**
 * Returns the entire filename: directory, basename, extension.  This is the
 * same thing returned by the string typecast operator, so this function is a
 * little redundant.
 */ 1 4 this 3 858  
184 0 0 6 248 826 0 0 139 /**
 * Returns the directory part of the filename.  This is everything in the
 * filename up to, but not including the rightmost slash.
 */ 1 4 this 3 858  
185 0 0 6 249 826 0 0 145 /**
 * Returns the basename part of the filename.  This is everything in the
 * filename after the rightmost slash, including any extensions.
 */ 1 4 this 3 858  
186 0 0 6 250 826 0 0 96 /**
 * Returns the full filename--directory and basename parts--except for the
 * extension.
 */ 1 4 this 3 858  
187 0 0 6 251 826 0 0 81 /**
 * Returns the basename part of the filename, without the file extension.
 */ 1 4 this 3 858  
188 0 0 6 252 826 0 0 140 /**
 * Returns the file extension.  This is everything after the rightmost dot, if
 * there is one, or the empty string if there is not.
 */ 1 4 this 3 858  
189 0 0 6 253 775 0 0 34 // Comparison operators are handy. 2 4 this 3 858  5 other 1 861  
190 0 0 6 254 775 0 0 0 2 4 this 3 858  5 other 1 861  
191 0 0 6 255 775 0 0 0 2 4 this 3 858  5 other 1 861  
192 0 0 4 256 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 860  5 value 1 861  
193 0 0 7 257 830 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 858  
194 0 0 7 258 830 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 858  
195 0 0 7 262 830 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 858  1 n 1 828  
196 0 0 4 263 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 860  1 n 1 828  5 value 1 861  
197 0 0 6 264 826 0 0 145 /**
 * Allows a ConfigVariableFilename object to be passed to any Python function
 * that accepts an os.PathLike object.
 *
 * @since 1.10.13
 */ 1 4 this 3 858  
198 0 0 6 242 861 0 0 0 1 4 this 3 858  
199 0 0 15 267 866 586 0 0 1 6 param0 0 864  
200 0 0 23 267 866 586 0 10 /**
 *
 */ 1 4 name 1 826  
201 0 0 23 267 866 586 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 776  11 description 5 826  5 flags 5 776  
202 0 0 23 267 866 586 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 5 826  5 flags 5 776  
203 0 0 6 268 866 0 0 0 2 4 this 3 866  5 value 1 776  
204 0 0 6 270 828 0 0 62 /**
 * Returns the number of unique words in the variable.
 */ 1 4 this 3 864  
205 0 0 6 271 776 0 0 0 2 4 this 3 864  1 n 1 828  
206 0 0 4 272 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 866  5 value 1 776  
207 0 0 6 273 776 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 864  
208 0 0 6 274 776 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 864  
209 0 0 6 277 776 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 864  1 n 1 828  
210 0 0 4 278 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 866  1 n 1 828  5 value 1 776  
211 0 0 6 269 776 0 0 0 1 4 this 3 864  
212 0 0 15 281 869 597 0 0 1 6 param0 0 867  
213 0 0 23 281 869 597 0 10 /**
 *
 */ 1 4 name 1 826  
214 0 0 23 281 869 597 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 798  11 description 5 826  5 flags 5 776  
215 0 0 23 281 869 597 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 5 826  5 flags 5 776  
216 0 0 6 282 869 0 0 0 2 4 this 3 869  5 value 1 798  
217 0 0 6 284 828 0 0 62 /**
 * Returns the number of unique words in the variable.
 */ 1 4 this 3 867  
218 0 0 6 285 798 0 0 0 2 4 this 3 867  1 n 1 828  
219 0 0 4 286 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 869  5 value 1 798  
220 0 0 6 287 798 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 867  
221 0 0 6 288 798 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 867  
222 0 0 6 293 798 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 867  1 n 1 828  
223 0 0 4 294 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 869  1 n 1 828  5 value 1 798  
224 0 0 6 283 798 0 0 0 1 4 this 3 867  
225 0 0 15 297 872 599 0 0 1 6 param0 0 870  
226 0 0 23 297 872 599 0 10 /**
 *
 */ 3 4 name 1 826  11 description 5 826  5 flags 5 776  
227 0 0 6 299 828 0 0 56 /**
 * Returns the number of values in the variable.
 */ 1 4 this 3 870  
228 0 0 6 300 826 0 0 49 /**
 * Returns the nth value of the variable.
 */ 2 4 this 3 870  1 n 1 828  
229 0 0 6 301 828 0 0 63 /**
 * Returns the number of unique values in the variable.
 */ 1 4 this 3 870  
230 0 0 6 302 826 0 0 56 /**
 * Returns the nth unique value of the variable.
 */ 2 4 this 3 870  1 n 1 828  
231 0 0 6 303 828 0 0 63 /**
 * Returns the number of unique values of the variable.
 */ 1 4 this 3 870  
232 0 0 6 304 826 0 0 0 2 4 this 3 870  1 n 1 828  
233 0 0 4 305 827 0 0 10 /**
 *
 */ 2 4 this 3 870  3 out 1 814  
234 0 0 4 306 827 0 0 10 /**
 *
 */ 2 4 this 3 870  3 out 1 814  
235 0 0 23 308 874 609 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 873  11 description 1 826  5 flags 5 776  
236 0 0 23 308 874 609 0 10 /**
 *
 */ 3 4 name 1 826  11 description 5 826  5 flags 5 776  
237 0 0 23 308 874 609 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 1 826  5 flags 5 776  
238 0 0 7 311 842 0 0 10 /**
 *
 */ 1 4 this 3 875  
239 0 0 6 312 873 0 0 10 /**
 *
 */ 1 4 this 3 875  
240 0 0 6 316 775 0 0 113 /**
 * Removes all the directories locally added to the search list, and restores
 * it to its original form.
 */ 1 4 this 3 874  
241 0 0 4 317 827 0 0 113 /**
 * Removes all the directories locally added to the search list, and restores
 * it to its original form.
 */ 1 4 this 3 874  
242 0 0 4 318 827 0 0 62 /**
 * Adds a new directory to the end of the search list.
 */ 2 4 this 3 874  9 directory 1 861  
243 0 0 4 319 827 0 0 64 /**
 * Adds a new directory to the front of the search list.
 */ 2 4 this 3 874  9 directory 1 861  
244 0 0 4 320 827 0 0 98 /**
 * Adds all of the directories listed in the search path to the end of the
 * search list.
 */ 2 4 this 3 874  4 path 1 873  
245 0 0 4 320 827 0 0 98 /**
 * Adds all of the directories listed in the search path to the end of the
 * search list.
 */ 3 4 this 3 874  4 path 1 826  9 separator 5 826  
246 0 0 4 321 827 0 0 104 /**
 * Adds all of the directories listed in the search path to the beginning of
 * the search list.
 */ 2 4 this 3 874  4 path 1 873  
247 0 0 6 322 775 0 0 69 /**
 * Returns true if the search list is empty, false otherwise.
 */ 1 4 this 3 875  
248 0 0 6 323 828 0 0 64 /**
 * Returns the number of directories on the search list.
 */ 1 4 this 3 875  
249 0 0 7 324 830 0 0 56 /**
 * Returns the nth directory on the search list.
 */ 2 4 this 3 875  1 n 1 828  
250 0 0 7 327 830 0 0 197 /**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Returns the full matching pathname of the first match if found, or
 * the empty string if not found.
 */ 2 4 this 3 875  8 filename 1 861  
251 0 0 7 328 878 0 0 184 /**
 * This variant of find_all_files() returns the new Results object, instead of
 * filling on in on the parameter list.  This is a little more convenient to
 * call from Python.
 */ 2 4 this 3 875  8 filename 1 861  
252 0 0 6 328 828 0 0 363 /**
 * Searches all the directories in the search list for the indicated file, in
 * order.  Fills up the results list with *all* of the matching filenames
 * found, if any.  Returns the number of matches found.
 *
 * It is the responsibility of the the caller to clear the results list first;
 * otherwise, the newly-found files will be appended to the list.
 */ 3 4 this 3 875  8 filename 1 861  7 results 1 878  
253 0 0 4 329 827 0 0 10 /**
 *
 */ 2 4 this 3 875  3 out 1 814  
254 0 0 4 330 827 0 0 10 /**
 *
 */ 2 4 this 3 875  3 out 1 814  
255 0 0 7 310 842 0 0 0 1 4 this 3 875  
256 0 0 15 332 881 641 0 0 1 6 param0 0 879  
257 0 0 23 332 881 641 0 10 /**
 *
 */ 1 4 name 1 826  
258 0 0 23 332 881 641 0 10 /**
 *
 */ 4 4 name 1 826  13 default_value 1 826  11 description 5 826  5 flags 5 776  
259 0 0 6 333 881 0 0 0 2 4 this 3 881  5 value 1 826  
260 0 0 6 335 826 0 0 76 // These methods help the ConfigVariableString act like a C++ string object. 1 4 this 3 879  
261 0 0 6 336 775 0 0 10 /**
 *
 */ 1 4 this 3 879  
262 0 0 6 337 828 0 0 10 /**
 *
 */ 1 4 this 3 879  
263 0 0 6 338 863 0 0 0 2 4 this 3 879  1 n 1 828  
264 0 0 6 339 775 0 0 34 // Comparison operators are handy. 2 4 this 3 879  5 other 1 826  
265 0 0 6 340 775 0 0 0 2 4 this 3 879  5 other 1 826  
266 0 0 6 341 775 0 0 0 2 4 this 3 879  5 other 1 826  
267 0 0 4 342 827 0 0 48 /**
 * Reassigns the variable's local value.
 */ 2 4 this 3 881  5 value 1 826  
268 0 0 6 343 826 0 0 40 /**
 * Returns the variable's value.
 */ 1 4 this 3 879  
269 0 0 6 344 826 0 0 48 /**
 * Returns the variable's default value.
 */ 1 4 this 3 879  
270 0 0 6 347 826 0 0 44 /**
 * Returns the variable's nth value.
 */ 2 4 this 3 879  1 n 1 828  
271 0 0 4 348 827 0 0 107 /**
 * Reassigns the variable's nth value.  This makes a local copy of the
 * variable's overall value.
 */ 3 4 this 3 881  1 n 1 828  5 value 1 826  
272 0 0 6 334 826 0 0 0 1 4 this 3 879  
273 0 0 15 383 806 668 0 0 1 6 param0 0 882  
274 0 0 6 356 826 0 0 10 /**
 *
 */ 1 4 this 3 882  
275 0 0 6 357 826 0 0 10 /**
 *
 */ 1 4 this 3 882  
276 0 0 6 358 784 0 0 10 /**
 *
 */ 1 4 this 3 882  
277 0 0 4 359 827 0 0 150 /**
 * Sets the severity level of messages that will be reported from this
 * Category.  This allows any message of this severity level or higher.
 */ 2 4 this 3 806  8 severity 1 784  
278 0 0 6 363 775 0 0 110 /**
 * Returns true if messages of the indicated severity level ought to be
 * reported for this Category.
 */ 2 4 this 3 882  8 severity 1 784  
279 0 0 6 364 775 0 0 393 // When NOTIFY_DEBUG is not defined, the categories will never be set to
// "spam" or "debug" severities, and these methods are redefined to be
// static to make it more obvious to the compiler.  However, we still want
// to present a consistent interface to our scripting language, so during
// the interrogate pass (that is, when CPPPARSER is defined), we still
// pretend they're nonstatic. 1 4 this 3 882  
280 0 0 6 365 775 0 0 52 /**
 * A shorthand way to write is_on(NS_debug).
 */ 1 4 this 3 882  
281 0 0 6 366 775 0 0 51 /**
 * A shorthand way to write is_on(NS_info).
 */ 1 4 this 3 882  
282 0 0 6 367 775 0 0 54 /**
 * A shorthand way to write is_on(NS_warning).
 */ 1 4 this 3 882  
283 0 0 6 368 775 0 0 52 /**
 * A shorthand way to write is_on(NS_error).
 */ 1 4 this 3 882  
284 0 0 6 369 775 0 0 52 /**
 * A shorthand way to write is_on(NS_fatal).
 */ 1 4 this 3 882  
285 0 0 6 370 814 0 0 277 /**
 * Begins a new message to this Category at the indicated severity level.  If
 * the indicated severity level is enabled, this writes a prefixing string to
 * the Notify::out() stream and returns that.  If the severity level is
 * disabled, this returns Notify::null().
 */ 3 4 this 3 882  8 severity 1 784  6 prefix 5 775  
286 0 0 6 371 814 0 0 49 /**
 * A shorthand way to write out(NS_spam).
 */ 2 4 this 3 882  6 prefix 5 775  
287 0 0 6 372 814 0 0 50 /**
 * A shorthand way to write out(NS_debug).
 */ 2 4 this 3 882  6 prefix 5 775  
288 0 0 6 373 814 0 0 49 /**
 * A shorthand way to write out(NS_info).
 */ 2 4 this 3 882  6 prefix 5 775  
289 0 0 6 374 814 0 0 52 /**
 * A shorthand way to write out(NS_warning).
 */ 2 4 this 3 882  6 prefix 5 775  
290 0 0 6 375 814 0 0 50 /**
 * A shorthand way to write out(NS_error).
 */ 2 4 this 3 882  6 prefix 5 775  
291 0 0 6 376 814 0 0 50 /**
 * A shorthand way to write out(NS_fatal).
 */ 2 4 this 3 882  6 prefix 5 775  
292 0 0 6 377 828 0 0 78 /**
 * Returns the number of child Categories of this particular Category.
 */ 1 4 this 3 882  
293 0 0 6 378 806 0 0 70 /**
 * Returns the nth child Category of this particular Category.
 */ 2 4 this 3 882  1 i 1 828  
294 0 0 4 382 827 0 0 207 /**
 * Sets a global delta (in seconds) between the local time and the server's
 * time, for the purpose of synchronizing the time stamps in the log messages
 * of the client with that of a known server.
 */ 1 5 delta 1 884  
295 0 0 6 352 885 0 0 0 1 4 this 3 806  
296 0 0 6 354 821 0 0 0 1 4 this 3 806  
297 0 0 7 387 886 675 0 10 /**
 *
 */ 0 
298 0 0 7 387 886 675 0 10 /**
 *
 */ 3 6 source 1 812  11 owns_source 1 775  8 password 1 826  
299 0 0 6 388 886 0 0 10 /**
 *
 */ 4 4 this 3 886  6 source 1 812  11 owns_source 1 775  8 password 1 826  
300 0 0 6 389 886 0 0 124 /**
 * Resets the EncryptStream to empty, but does not actually close the source
 * istream unless owns_source was true.
 */ 1 4 this 3 886  
301 0 0 6 390 826 0 0 74 /**
 * Returns the encryption algorithm that was read from the stream.
 */ 1 4 this 3 887  
302 0 0 6 391 776 0 0 85 /**
 * Returns the encryption key length, in bits, that was read from the stream.
 */ 1 4 this 3 887  
303 0 0 6 392 776 0 0 63 /**
 * Returns the value that was was read from the stream.
 */ 1 4 this 3 887  
304 0 0 7 399 889 685 0 10 /**
 *
 */ 0 
305 0 0 7 399 889 685 0 10 /**
 *
 */ 3 4 dest 1 814  9 owns_dest 1 775  8 password 1 826  
306 0 0 6 400 889 0 0 10 /**
 *
 */ 4 4 this 3 889  4 dest 1 814  9 owns_dest 1 775  8 password 1 826  
307 0 0 6 401 889 0 0 120 /**
 * Resets the EncryptStream to empty, but does not actually close the dest
 * ostream unless owns_dest was true.
 */ 1 4 this 3 889  
308 0 0 4 402 827 0 0 383 /**
 * Specifies the encryption algorithm that should be used for future calls to
 * open().  The default is whatever is specified by the encryption-algorithm
 * config variable.  The complete set of available algorithms is defined by
 * the current version of OpenSSL.
 *
 * If an invalid algorithm is specified, there is no immediate error return
 * code, but open() will fail.
 */ 2 4 this 3 889  9 algorithm 1 826  
309 0 0 4 403 827 0 0 347 /**
 * Specifies the length of the key, in bits, that should be used to encrypt
 * the stream in future calls to open().  The default is whatever is specified
 * by the encryption-key-length config variable.
 *
 * If an invalid key_length for the chosen algorithm is specified, there is no
 * immediate error return code, but open() will fail.
 */ 2 4 this 3 889  10 key_length 1 776  
310 0 0 4 404 827 0 0 495 /**
 * Specifies the number of times to repeatedly hash the key before writing it
 * to the stream in future calls to open().  Its purpose is to make it
 * computationally more expensive for an attacker to search the key space
 * exhaustively.  This should be a multiple of 1,000 and should not exceed
 * about 65 million; the value 0 indicates just one application of the hashing
 * algorithm.
 *
 * The default is whatever is specified by the encryption-iteration-count
 * config variable.
 */ 2 4 this 3 889  15 iteration_count 1 776  
311 0 0 15 413 892 688 0 70 /**
 * The copy constructor does not copy ownership of the stream.
 */ 1 4 copy 1 890  
312 0 0 7 413 892 688 0 105 /**
 * If owns_stream is true, the stream pointer will be deleted when the
 * StreamReader destructs.
 */ 2 2 in 1 812  11 owns_stream 1 775  
313 0 0 6 414 892 0 0 0 2 4 this 3 892  4 copy 1 890  
314 0 0 6 416 812 0 0 37 /**
 * Returns the stream in use.
 */ 1 4 this 3 890  
315 0 0 6 419 775 0 0 36 /**
 * Extracts a boolean value.
 */ 1 4 this 3 892  
316 0 0 6 420 893 0 0 43 /**
 * Extracts a signed 8-bit integer.
 */ 1 4 this 3 892  
317 0 0 6 421 895 0 0 46 /**
 * Extracts an unsigned 8-bit integer.
 */ 1 4 this 3 892  
318 0 0 6 422 897 0 0 44 /**
 * Extracts a signed 16-bit integer.
 */ 1 4 this 3 892  
319 0 0 6 423 899 0 0 44 /**
 * Extracts a signed 32-bit integer.
 */ 1 4 this 3 892  
320 0 0 6 424 798 0 0 44 /**
 * Extracts a signed 64-bit integer.
 */ 1 4 this 3 892  
321 0 0 6 425 900 0 0 47 /**
 * Extracts an unsigned 16-bit integer.
 */ 1 4 this 3 892  
322 0 0 6 426 902 0 0 47 /**
 * Extracts an unsigned 32-bit integer.
 */ 1 4 this 3 892  
323 0 0 6 427 904 0 0 47 /**
 * Extracts an unsigned 64-bit integer.
 */ 1 4 this 3 892  
324 0 0 6 428 905 0 0 186 /**
 * Extracts a 32-bit single-precision floating-point number.  Since this kind
 * of float is not necessarily portable across different architectures,
 * special care is required.
 */ 1 4 this 3 892  
325 0 0 6 429 906 0 0 51 /**
 * Extracts a 64-bit floating-point number.
 */ 1 4 this 3 892  
326 0 0 6 430 897 0 0 55 /**
 * Extracts a signed big-endian 16-bit integer.
 */ 1 4 this 3 892  
327 0 0 6 431 899 0 0 55 /**
 * Extracts a signed big-endian 32-bit integer.
 */ 1 4 this 3 892  
328 0 0 6 432 798 0 0 55 /**
 * Extracts a signed big-endian 64-bit integer.
 */ 1 4 this 3 892  
329 0 0 6 433 900 0 0 58 /**
 * Extracts an unsigned big-endian 16-bit integer.
 */ 1 4 this 3 892  
330 0 0 6 434 902 0 0 58 /**
 * Extracts an unsigned big-endian 32-bit integer.
 */ 1 4 this 3 892  
331 0 0 6 435 904 0 0 58 /**
 * Extracts an unsigned big-endian 64-bit integer.
 */ 1 4 this 3 892  
332 0 0 6 436 905 0 0 197 /**
 * Extracts a 32-bit single-precision big-endian floating-point number.  Since
 * this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */ 1 4 this 3 892  
333 0 0 6 437 906 0 0 62 /**
 * Extracts a 64-bit big-endian floating-point number.
 */ 1 4 this 3 892  
334 0 0 6 438 826 0 0 45 /**
 * Extracts a variable-length string.
 */ 1 4 this 3 892  
335 0 0 6 439 826 0 0 72 /**
 * Extracts a variable-length string with a 32-bit length field.
 */ 1 4 this 3 892  
336 0 0 6 440 826 0 0 74 /**
 * Extracts a variable-length string, as a NULL-terminated string.
 */ 1 4 this 3 892  
337 0 0 6 441 826 0 0 128 /**
 * Extracts a fixed-length string.  However, if a zero byte occurs within the
 * string, it marks the end of the string.
 */ 2 4 this 3 892  4 size 1 828  
338 0 0 4 442 827 0 0 66 /**
 * Skips over the indicated number of bytes in the stream.
 */ 2 4 this 3 892  4 size 1 828  
339 0 0 38 443 836 0 0 134 /**
 * Extracts the indicated number of bytes in the stream and returns them as a
 * string.  Returns empty string at end-of-file.
 */ 2 4 this 3 892  4 size 1 828  
340 0 0 38 444 836 0 0 303 /**
 * Assumes the stream represents a text file, and extracts one line up to and
 * including the trailing newline character.  Returns empty string when the
 * end of file is reached.
 *
 * The interface here is intentionally designed to be similar to that for
 * Python's File.readline() function.
 */ 1 4 this 3 892  
341 0 0 38 445 836 0 0 0 1 4 this 3 892  
342 0 0 15 447 909 719 0 70 /**
 * The copy constructor does not copy ownership of the stream.
 */ 1 4 copy 1 907  
343 0 0 7 447 909 719 0 10 /**
 *
 */ 2 3 out 1 814  11 owns_stream 1 775  
344 0 0 6 448 909 0 0 0 2 4 this 3 909  4 copy 1 907  
345 0 0 6 450 814 0 0 37 /**
 * Returns the stream in use.
 */ 1 4 this 3 907  
346 0 0 4 453 827 0 0 46 /**
 * Adds a boolean value to the stream.
 */ 2 4 this 3 909  5 value 1 775  
347 0 0 4 454 827 0 0 53 /**
 * Adds a signed 8-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 893  
348 0 0 4 455 827 0 0 56 /**
 * Adds an unsigned 8-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 895  
349 0 0 4 456 827 0 0 54 /**
 * Adds a signed 16-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 897  
350 0 0 4 457 827 0 0 54 /**
 * Adds a signed 32-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 899  
351 0 0 4 458 827 0 0 54 /**
 * Adds a signed 64-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 798  
352 0 0 4 459 827 0 0 57 /**
 * Adds an unsigned 16-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 900  
353 0 0 4 460 827 0 0 57 /**
 * Adds an unsigned 32-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 902  
354 0 0 4 461 827 0 0 57 /**
 * Adds an unsigned 64-bit integer to the stream.
 */ 2 4 this 3 909  5 value 1 904  
355 0 0 4 462 827 0 0 196 /**
 * Adds a 32-bit single-precision floating-point number to the stream.  Since
 * this kind of float is not necessarily portable across different
 * architectures, special care is required.
 */ 2 4 this 3 909  5 value 1 905  
356 0 0 4 463 827 0 0 61 /**
 * Adds a 64-bit floating-point number to the stream.
 */ 2 4 this 3 909  5 value 1 906  
357 0 0 4 464 827 0 0 71 /**
 * Adds a signed 16-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 897  
358 0 0 4 465 827 0 0 71 /**
 * Adds a signed 32-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 899  
359 0 0 4 466 827 0 0 71 /**
 * Adds a signed 64-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 798  
360 0 0 4 467 827 0 0 74 /**
 * Adds an unsigned 16-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 900  
361 0 0 4 468 827 0 0 74 /**
 * Adds an unsigned 32-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 902  
362 0 0 4 469 827 0 0 74 /**
 * Adds an unsigned 64-bit big-endian integer to the streamWriter.
 */ 2 4 this 3 909  5 value 1 904  
363 0 0 4 470 827 0 0 207 /**
 * Adds a 32-bit single-precision big-endian floating-point number to the
 * stream.  Since this kind of float is not necessarily portable across
 * different architectures, special care is required.
 */ 2 4 this 3 909  5 value 1 905  
364 0 0 4 471 827 0 0 78 /**
 * Adds a 64-bit big-endian floating-point number to the streamWriter.
 */ 2 4 this 3 909  5 value 1 906  
365 0 0 4 472 827 0 0 107 /**
 * Adds a variable-length string to the stream.  This actually adds a count
 * followed by n bytes.
 */ 2 4 this 3 909  3 str 1 826  
366 0 0 4 473 827 0 0 84 /**
 * Adds a variable-length string to the stream, using a 32-bit length field.
 */ 2 4 this 3 909  3 str 1 826  
367 0 0 4 474 827 0 0 84 /**
 * Adds a variable-length string to the stream, as a NULL-terminated string.
 */ 2 4 this 3 909  3 str 1 826  
368 0 0 4 475 827 0 0 236 /**
 * Adds a fixed-length string to the stream.  If the string given is less than
 * the requested size, this will pad the string out with zeroes; if it is
 * greater than the requested size, this will silently truncate the string.
 */ 3 4 this 3 909  3 str 1 826  4 size 1 828  
369 0 0 4 476 827 0 0 65 /**
 * Adds the indicated number of zero bytes to the stream.
 */ 2 4 this 3 909  4 size 1 828  
370 0 0 36 477 827 0 0 0 2 4 this 3 909  4 data 1 836  
371 0 0 4 478 827 0 0 50 /**
 * Calls flush() on the underlying stream.
 */ 1 4 this 3 909  
372 0 0 4 479 827 0 0 132 /**
 * A synonym of append_data().  This is useful when assigning the StreamWriter
 * to sys.stderr and/or sys.stdout in Python.
 */ 2 4 this 3 909  3 str 1 826  
373 0 0 4 484 827 0 0 572 /**
 * Acquires the internal lock.
 *
 * User code should call this to take temporary possession of the stream and
 * perform direct I/O operations on it, for instance to make several
 * sequential atomic reads.  You may not call any of the StreamWrapper methods
 * while the lock is held, other than release().
 *
 * Use with extreme caution!  This is a very low-level, non-recursive lock.
 * You must call acquire() only once, and you must later call release()
 * exactly once.  Failing to do so may result in a hard deadlock with no
 * available debugging features.
 */ 1 4 this 3 910  
374 0 0 4 485 827 0 0 135 /**
 * Releases the internal lock.  Must be called exactly once following a call
 * to acquire().  See the cautions with acquire().
 */ 1 4 this 3 910  
375 0 0 7 489 911 755 0 10 /**
 *
 */ 1 6 stream 1 812  
376 0 0 6 491 812 0 0 55 /**
 * Returns the istream this object is wrapping.
 */ 1 4 this 3 912  
377 0 0 6 488 910 0 0 0 1 4 this 3 911  
378 0 0 7 495 914 759 0 10 /**
 *
 */ 1 6 stream 1 814  
379 0 0 6 497 814 0 0 55 /**
 * Returns the ostream this object is wrapping.
 */ 1 4 this 3 915  
380 0 0 6 494 910 0 0 0 1 4 this 3 914  
381 0 0 7 504 917 766 0 10 /**
 *
 */ 1 6 stream 1 819  
382 0 0 6 506 819 0 0 56 /**
 * Returns the iostream this object is wrapping.
 */ 1 4 this 3 918  
383 0 0 6 500 911 0 0 0 1 4 this 3 917  
384 0 0 6 502 914 0 0 0 1 4 this 3 917  
152
768 11 ConfigFlags 0 141313 11 ConfigFlags 11 ConfigFlags 0 0 0 1 385 386 0 0 0 0 0 0 2 769 770 195
/**
 * This class is the base class of both ConfigVariable and ConfigVariableCore.
 * It exists only to provide a convenient name scoping for some enumerated
 * values common to both classes.
 */

769 9 ValueType 0 794624 22 ConfigFlags::ValueType 22 ConfigFlags::ValueType 768 0 0 0 0 0 0 0 0 0 11 12 VT_undefined 25 ConfigFlags::VT_undefined 0
0 7 VT_list 20 ConfigFlags::VT_list 0
1 9 VT_string 22 ConfigFlags::VT_string 0
2 11 VT_filename 24 ConfigFlags::VT_filename 0
3 7 VT_bool 20 ConfigFlags::VT_bool 0
4 6 VT_int 19 ConfigFlags::VT_int 0
5 9 VT_double 22 ConfigFlags::VT_double 0
6 7 VT_enum 20 ConfigFlags::VT_enum 0
7 14 VT_search_path 27 ConfigFlags::VT_search_path 0
8 8 VT_int64 21 ConfigFlags::VT_int64 0
9 8 VT_color 21 ConfigFlags::VT_color 0
10 0 0

770 13 VariableFlags 0 794624 26 ConfigFlags::VariableFlags 26 ConfigFlags::VariableFlags 768 0 0 0 0 0 0 0 0 0 5 18 F_trust_level_mask 31 ConfigFlags::F_trust_level_mask 138
// Trust level.  We have the bottom twelve bits reserved for a trust level
// indicator; then the open and closed bits are a special case.
4095 6 F_open 19 ConfigFlags::F_open 0
4096 8 F_closed 21 ConfigFlags::F_closed 0
8192 9 F_dynamic 22 ConfigFlags::F_dynamic 171
// F_dynamic means that the variable name is generated dynamically
// (possibly from a very large pool) and should not be included in the
// normal list of variable names.
16384 9 F_dconfig 22 ConfigFlags::F_dconfig 162
// F_dconfig means that the variable was constructed from the legacy
// DConfig system, rather than directly by the user.  You shouldn't pass
// this in directly.
32768 0 0

771 10 ConfigPage 0 43009 10 ConfigPage 10 ConfigPage 0 0 0 0 0 8 920 921 922 923 924 925 926 927 25 387 388 389 390 391 392 393 394 395 396 397 398 399 400 401 402 403 404 405 406 407 408 461 462 463 0 0 0 0 0 262
/**
 * A page of ConfigDeclarations that may be loaded or unloaded.  Typically
 * this represents a single .prc file that is read from disk at runtime, but
 * it may also represent a list of declarations built up by application code
 * and explicitly loaded.
 */

772 12 string const 0 8832 17 std::string const 17 std::string const 0 0 773 0 0 0 0 0 0 0 0 0 0

773 6 string 0 2105344 11 std::string 11 std::string 0 0 774 0 0 0 0 0 0 0 0 0 0

774 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

775 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

776 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

777 19 ConfigDeclaration * 0 8576 19 ConfigDeclaration * 19 ConfigDeclaration * 0 0 778 0 0 0 0 0 0 0 0 0 0

778 17 ConfigDeclaration 0 43009 17 ConfigDeclaration 17 ConfigDeclaration 0 0 0 0 0 2 928 929 24 409 410 439 440 441 442 443 444 445 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 0 0 1 0 768 0 0 0 0 249
/**
 * A single declaration of a config variable, typically defined as one line in
 * a .prc file, e.g.  "show-frame-rate-meter 1".  This is really just a
 * pairing of a string name (actually, a ConfigVariableCore pointer) to a
 * string value.
 */

779 12 ConfigPage * 0 8576 12 ConfigPage * 12 ConfigPage * 0 0 771 0 0 0 0 0 0 0 0 0 0

780 20 ConfigVariableCore * 0 8576 20 ConfigVariableCore * 20 ConfigVariableCore * 0 0 781 0 0 0 0 0 0 0 0 0 0

781 18 ConfigVariableCore 0 43009 18 ConfigVariableCore 18 ConfigVariableCore 0 0 0 0 0 12 930 931 932 933 934 935 936 937 938 939 940 941 28 411 412 413 414 415 416 417 418 419 420 421 422 423 424 425 426 427 428 429 430 431 432 433 434 435 436 437 438 4 986 987 988 989 0 1 0 768 0 0 0 0 345
/**
 * The internal definition of a ConfigVariable.  This object is shared between
 * all instances of a ConfigVariable that use the same variable name.
 *
 * You cannot create a ConfigVariableCore instance directly; instead, use the
 * make() method, which may return a shared instance.  Once created, these
 * objects are never destructed.
 */

782 25 ConfigDeclaration const * 0 8576 25 ConfigDeclaration const * 25 ConfigDeclaration const * 0 0 783 0 0 0 0 0 0 0 0 0 0

783 23 ConfigDeclaration const 0 8832 23 ConfigDeclaration const 23 ConfigDeclaration const 0 0 778 0 0 0 0 0 0 0 0 0 0

784 14 NotifySeverity 0 532481 14 NotifySeverity 14 NotifySeverity 0 0 0 0 0 0 0 0 0 0 7 14 NS_unspecified 14 NS_unspecified 41
// Never used, a special case internally.
0 7 NS_spam 7 NS_spam 0
1 8 NS_debug 8 NS_debug 0
2 7 NS_info 7 NS_info 0
3 10 NS_warning 10 NS_warning 0
4 8 NS_error 8 NS_error 37
// Specifically, a recoverable error.
5 8 NS_fatal 8 NS_fatal 55
// A nonrecoverable error--expect abort() or core dump.
6 0 0

785 6 Notify 0 26625 6 Notify 6 Notify 0 0 0 1 464 465 0 15 466 467 468 469 470 471 472 473 474 475 476 477 478 479 480 0 0 0 0 0 529
/**
 * An object that handles general error reporting to the user.  It contains a
 * pointer to an ostream, initially cerr, which can be reset at will to point
 * to different output devices, according to the needs of the application.
 * All output generated within Panda should vector through the Notify ostream.
 *
 * This also includes a collection of Categories and Severities, which may be
 * independently enabled or disabled, so that error messages may be squelched
 * or respected according to the wishes of the user.
 */

786 17 ConfigPageManager 0 43009 17 ConfigPageManager 17 ConfigPageManager 0 0 0 0 0 6 942 943 944 945 946 947 19 481 482 483 484 485 486 487 488 489 490 491 492 493 494 495 496 497 498 499 0 0 1 0 768 0 0 0 0 124
/**
 * A global object that maintains the set of ConfigPages everywhere in the
 * world, and keeps them in sorted order.
 */

787 11 DSearchPath 0 2048 11 DSearchPath 11 DSearchPath 0 0 0 0 0 0 0 0 0 0 0 0 304
/**
 * This class stores a list of directories that can be searched, in order, to
 * locate a particular file.  It is normally constructed by passing it a
 * traditional searchpath-style string, e.g.  a list of directory names
 * delimited by spaces or colons, but it can also be built up explicitly.
 */

788 21 ConfigVariableManager 0 43009 21 ConfigVariableManager 21 ConfigVariableManager 0 0 0 0 0 1 948 13 500 501 502 503 504 505 506 507 508 509 510 511 512 1 990 0 0 0 0 163
/**
 * A global object that maintains the set of ConfigVariables (actually,
 * ConfigVariableCores) everywhere in the world, and keeps them in sorted
 * order.
 */

789 18 ConfigVariableBase 0 43009 18 ConfigVariableBase 18 ConfigVariableBase 0 0 0 0 0 6 949 950 951 952 953 954 12 513 514 515 516 517 518 519 520 521 522 523 524 0 0 1 0 768 0 0 0 0 417
/**
 * This class is the base class for both ConfigVariableList and ConfigVariable
 * (and hence for all of the ConfigVariableBool, ConfigVaribleString, etc.
 * classes).  It collects together the common interface for all generic
 * ConfigVariables.
 *
 * Mostly, this class serves as a thin wrapper around ConfigVariableCore
 * and/or ConfigDeclaration, more or less duplicating the interface presented
 * there.
 */

790 14 ConfigVariable 0 26625 14 ConfigVariable 14 ConfigVariable 0 0 0 1 525 526 0 4 527 528 529 530 0 0 1 0 789 0 0 0 0 415
/**
 * This is a generic, untyped ConfigVariable.  It is also the base class for
 * the typed ConfigVariables, and contains all of the code common to
 * ConfigVariables of all types (except ConfigVariableList, which is a bit of
 * a special case).
 *
 * Mostly, this class serves as a thin wrapper around ConfigVariableCore
 * and/or ConfigDeclaration, more or less duplicating the interface presented
 * there.
 */

791 18 ConfigVariableBool 0 141313 18 ConfigVariableBool 18 ConfigVariableBool 0 0 0 1 531 541 2 955 956 8 532 534 535 536 537 538 539 540 0 1 533 1 0 790 0 0 0 0 86
/**
 * This is a convenience class to specialize ConfigVariable as a boolean type.
 */

792 20 ConfigVariableDouble 0 141313 20 ConfigVariableDouble 20 ConfigVariableDouble 0 0 0 1 542 552 2 957 958 8 543 545 546 547 548 549 550 551 0 1 544 1 0 790 0 0 0 0 97
/**
 * This is a convenience class to specialize ConfigVariable as a floating-
 * point type.
 */

793 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

794 22 ConfigVariableFilename 0 141313 22 ConfigVariableFilename 22 ConfigVariableFilename 0 0 0 1 553 575 2 959 960 20 554 556 557 558 559 560 561 562 563 564 565 566 567 568 569 570 571 572 573 574 0 1 555 1 0 790 0 0 0 0 327
/**
 * This is a convenience class to specialize ConfigVariable as a Filename
 * type.  It is almost the same thing as ConfigVariableString, except it
 * handles an implicit Filename::expand_from() operation so that the user may
 * put OS-specific filenames, or filenames based on environment variables, in
 * the prc file.
 */

795 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

796 17 ConfigVariableInt 0 141313 17 ConfigVariableInt 17 ConfigVariableInt 0 0 0 1 576 586 2 961 962 8 577 579 580 581 582 583 584 585 0 1 578 1 0 790 0 0 0 0 90
/**
 * This is a convenience class to specialize ConfigVariable as an integer
 * type.
 */

797 19 ConfigVariableInt64 0 141313 19 ConfigVariableInt64 19 ConfigVariableInt64 0 0 0 1 587 597 2 963 964 8 588 590 591 592 593 594 595 596 0 1 589 1 0 790 0 0 0 0 96
/**
 * This is a convenience class to specialize ConfigVariable as a 64-bit
 * integer type.
 */

798 7 int64_t 0 2105344 7 int64_t 7 int64_t 0 0 799 0 0 0 0 0 0 0 0 0 0

799 13 long long int 0 8226 13 long long int 13 long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

800 18 ConfigVariableList 0 26625 18 ConfigVariableList 18 ConfigVariableList 0 0 0 1 598 599 0 8 600 601 602 603 604 605 606 607 0 0 1 0 789 0 0 0 0 481
/**
 * This class is similar to ConfigVariable, but it reports its value as a list
 * of strings.  In this special case, all of the declarations of the variable
 * are returned as the elements of this list, in order.
 *
 * Note that this is different from a normal ConfigVariableString, which just
 * returns its topmost value, which can optionally be treated as a number of
 * discrete words by dividing it at the spaces.
 *
 * A ConfigVariableList cannot be modified locally.
 */

801 24 ConfigVariableSearchPath 0 26625 24 ConfigVariableSearchPath 24 ConfigVariableSearchPath 0 0 0 1 608 609 3 965 966 967 15 611 612 613 614 615 616 617 618 619 620 621 622 623 624 625 1 991 1 610 1 0 789 0 0 0 0 731
/**
 * This is similar to a ConfigVariableList, but it returns its list as a
 * DSearchPath, as a list of directories.
 *
 * You may locally append directories to the end of the search path with the
 * methods here, or prepend them to the beginning.  Use these methods to make
 * adjustments to the path; do not attempt to directly modify the const
 * DSearchPath object returned by get_value().
 *
 * Unlike other ConfigVariable types, local changes (made by calling
 * append_directory() and prepend_directory()) are specific to this particular
 * instance of the ConfigVariableSearchPath.  A separate instance of the same
 * variable, created by using the same name to the constructor, will not
 * reflect the local changes.
 */

802 17 DSearchPath const 0 8832 17 DSearchPath const 17 DSearchPath const 0 0 787 0 0 0 0 0 0 0 0 0 0

803 20 ConfigVariableString 0 141313 20 ConfigVariableString 20 ConfigVariableString 0 0 0 1 626 641 2 968 969 13 627 629 630 631 632 633 634 635 636 637 638 639 640 0 1 628 1 0 790 0 0 0 0 85
/**
 * This is a convenience class to specialize ConfigVariable as a string type.
 */

804 14 NotifyCategory 0 141313 14 NotifyCategory 14 NotifyCategory 0 0 0 1 667 668 4 970 971 972 973 21 646 647 648 649 650 651 652 653 654 655 656 657 658 659 660 661 662 663 664 665 666 1 992 0 2 3 805 642 643 3 768 644 645 0 0 284
/**
 * A particular category of error messages.  Typically there will be one of
 * these per package, so that we can turn on or off error messages at least at
 * a package level; further nested categories can be created within a package
 * if a finer grain of control is required.
 */

805 10 MemoryBase 0 2048 10 MemoryBase 10 MemoryBase 0 0 0 0 0 0 0 0 0 0 0 0 428
/**
 * This class is intended to be the base class of all objects in Panda that
 * might be allocated and deleted via the new and delete operators.  It
 * redefines these operators to provide some memory tracking support.
 *
 * We used to try to override the global operator new and delete methods, but
 * that seems to cause problems when including header files for C++-based
 * system libraries (such as are found on OSX).
 */

806 16 NotifyCategory * 0 8576 16 NotifyCategory * 16 NotifyCategory * 0 0 804 0 0 0 0 0 0 0 0 0 0

807 14 IDecryptStream 0 141313 14 IDecryptStream 14 IDecryptStream 0 0 0 1 669 675 3 974 975 976 5 670 671 672 673 674 0 0 1 0 808 0 0 0 0 340
/**
 * An input stream object that uses OpenSSL to decrypt the input from another
 * source stream on-the-fly.
 *
 * Attach an IDecryptStream to an existing istream that provides encrypted
 * data, as generated by an OEncryptStream, and read the corresponding
 * unencrypted data from the IDecryptStream.
 *
 * Seeking is not supported.
 */

808 7 istream 0 2048 12 std::istream 12 std::istream 0 0 0 0 0 0 0 0 0 0 0 0 0

809 14 OEncryptStream 0 141313 14 OEncryptStream 14 OEncryptStream 0 0 0 1 676 685 3 977 978 979 5 677 678 679 680 681 0 0 1 0 810 0 0 0 0 296
/**
 * An input stream object that uses OpenSSL to encrypt data to another
 * destination stream on-the-fly.
 *
 * Attach an OEncryptStream to an existing ostream that will accept encrypted
 * data, and write your unencrypted source data to the OEncryptStream.
 *
 * Seeking is not supported.
 */

810 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

811 12 StreamReader 0 26625 12 StreamReader 12 StreamReader 0 0 0 1 686 688 1 980 29 687 689 690 691 692 693 694 695 696 697 698 699 700 701 702 703 704 705 706 707 708 709 710 711 712 713 714 715 716 0 0 0 0 0 158
/**
 * A class to read sequential binary data directly from an istream.  Its
 * interface is similar to DatagramIterator by design; see also StreamWriter.
 */

812 9 istream * 0 8576 14 std::istream * 14 std::istream * 0 0 808 0 0 0 0 0 0 0 0 0 0

813 12 StreamWriter 0 26625 12 StreamWriter 12 StreamWriter 0 0 0 1 717 719 2 981 982 29 718 720 721 722 723 724 725 726 727 728 729 730 731 732 733 734 735 736 737 738 739 740 741 742 743 744 745 746 747 0 0 0 0 0 300
/**
 * A StreamWriter object is used to write sequential binary data directly to
 * an ostream.  Its interface is very similar to Datagram by design; it's
 * primarily intended as a convenience to eliminate the overhead of writing
 * bytes to a Datagram and then writing the Datagram to a stream.
 */

814 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 810 0 0 0 0 0 0 0 0 0 0

815 17 StreamWrapperBase 0 141313 17 StreamWrapperBase 17 StreamWrapperBase 0 0 0 0 752 0 2 750 751 0 0 0 0 0 116
/**
 * The base class for both IStreamWrapper and OStreamWrapper, this provides
 * the common locking interface.
 */

816 14 IStreamWrapper 0 26625 14 IStreamWrapper 14 IStreamWrapper 0 0 0 1 754 755 1 983 1 756 0 0 1 5 815 753 0 0 0 168
/**
 * This class provides a locking wrapper around an arbitrary istream pointer.
 * A thread may use this class to perform an atomic seek/read/gcount
 * operation.
 */

817 14 OStreamWrapper 0 26625 14 OStreamWrapper 14 OStreamWrapper 0 0 0 1 758 759 1 984 1 760 0 0 1 5 815 757 0 0 0 159
/**
 * This class provides a locking wrapper around an arbitrary ostream pointer.
 * A thread may use this class to perform an atomic seek/write operation.
 */

818 13 StreamWrapper 0 26625 13 StreamWrapper 13 StreamWrapper 0 0 0 1 765 766 1 985 1 767 0 0 2 3 816 761 762 3 817 763 764 0 0 97
/**
 * This class provides a locking wrapper around a combination ostream/istream
 * pointer.
 */

819 10 iostream * 0 8576 15 std::iostream * 15 std::iostream * 0 0 820 0 0 0 0 0 0 0 0 0 0

820 8 iostream 0 2048 13 std::iostream 13 std::iostream 0 0 0 0 0 0 0 0 0 0 0 0 0

821 13 ConfigFlags * 0 8576 13 ConfigFlags * 13 ConfigFlags * 0 0 768 0 0 0 0 0 0 0 0 0 0

822 19 ConfigFlags const * 0 8576 19 ConfigFlags const * 19 ConfigFlags const * 0 0 823 0 0 0 0 0 0 0 0 0 0

823 17 ConfigFlags const 0 8832 17 ConfigFlags const 17 ConfigFlags const 0 0 768 0 0 0 0 0 0 0 0 0 0

824 18 ConfigPage const * 0 8576 18 ConfigPage const * 18 ConfigPage const * 0 0 825 0 0 0 0 0 0 0 0 0 0

825 16 ConfigPage const 0 8832 16 ConfigPage const 16 ConfigPage const 0 0 771 0 0 0 0 0 0 0 0 0 0

826 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

827 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

828 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 829 0 0 0 0 0 0 0 0 0 0

829 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

830 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 795 0 0 0 0 0 0 0 0 0 0

831 26 ConfigVariableCore const * 0 8576 26 ConfigVariableCore const * 26 ConfigVariableCore const * 0 0 832 0 0 0 0 0 0 0 0 0 0

832 24 ConfigVariableCore const 0 8832 24 ConfigVariableCore const 24 ConfigVariableCore const 0 0 781 0 0 0 0 0 0 0 0 0 0

833 8 Notify * 0 8576 8 Notify * 8 Notify * 0 0 785 0 0 0 0 0 0 0 0 0 0

834 14 Notify const * 0 8576 14 Notify const * 14 Notify const * 0 0 835 0 0 0 0 0 0 0 0 0 0

835 12 Notify const 0 8832 12 Notify const 12 Notify const 0 0 785 0 0 0 0 0 0 0 0 0 0

836 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 837 0 0 0 0 0 0 0 0 0 0

837 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 838 0 0 0 0 0 0 0 0 0 0

838 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

839 25 ConfigPageManager const * 0 8576 25 ConfigPageManager const * 25 ConfigPageManager const * 0 0 840 0 0 0 0 0 0 0 0 0 0

840 23 ConfigPageManager const 0 8832 23 ConfigPageManager const 23 ConfigPageManager const 0 0 786 0 0 0 0 0 0 0 0 0 0

841 19 ConfigPageManager * 0 8576 19 ConfigPageManager * 19 ConfigPageManager * 0 0 786 0 0 0 0 0 0 0 0 0 0

842 13 DSearchPath * 0 8576 13 DSearchPath * 13 DSearchPath * 0 0 787 0 0 0 0 0 0 0 0 0 0

843 23 ConfigVariableManager * 0 8576 23 ConfigVariableManager * 23 ConfigVariableManager * 0 0 788 0 0 0 0 0 0 0 0 0 0

844 29 ConfigVariableManager const * 0 8576 29 ConfigVariableManager const * 29 ConfigVariableManager const * 0 0 845 0 0 0 0 0 0 0 0 0 0

845 27 ConfigVariableManager const 0 8832 27 ConfigVariableManager const 27 ConfigVariableManager const 0 0 788 0 0 0 0 0 0 0 0 0 0

846 26 ConfigVariableBase const * 0 8576 26 ConfigVariableBase const * 26 ConfigVariableBase const * 0 0 847 0 0 0 0 0 0 0 0 0 0

847 24 ConfigVariableBase const 0 8832 24 ConfigVariableBase const 24 ConfigVariableBase const 0 0 789 0 0 0 0 0 0 0 0 0 0

848 20 ConfigVariableBase * 0 8576 20 ConfigVariableBase * 20 ConfigVariableBase * 0 0 789 0 0 0 0 0 0 0 0 0 0

849 22 ConfigVariable const * 0 8576 22 ConfigVariable const * 22 ConfigVariable const * 0 0 850 0 0 0 0 0 0 0 0 0 0

850 20 ConfigVariable const 0 8832 20 ConfigVariable const 20 ConfigVariable const 0 0 790 0 0 0 0 0 0 0 0 0 0

851 16 ConfigVariable * 0 8576 16 ConfigVariable * 16 ConfigVariable * 0 0 790 0 0 0 0 0 0 0 0 0 0

852 26 ConfigVariableBool const * 0 8576 26 ConfigVariableBool const * 26 ConfigVariableBool const * 0 0 853 0 0 0 0 0 0 0 0 0 0

853 24 ConfigVariableBool const 0 8832 24 ConfigVariableBool const 24 ConfigVariableBool const 0 0 791 0 0 0 0 0 0 0 0 0 0

854 20 ConfigVariableBool * 0 8576 20 ConfigVariableBool * 20 ConfigVariableBool * 0 0 791 0 0 0 0 0 0 0 0 0 0

855 28 ConfigVariableDouble const * 0 8576 28 ConfigVariableDouble const * 28 ConfigVariableDouble const * 0 0 856 0 0 0 0 0 0 0 0 0 0

856 26 ConfigVariableDouble const 0 8832 26 ConfigVariableDouble const 26 ConfigVariableDouble const 0 0 792 0 0 0 0 0 0 0 0 0 0

857 22 ConfigVariableDouble * 0 8576 22 ConfigVariableDouble * 22 ConfigVariableDouble * 0 0 792 0 0 0 0 0 0 0 0 0 0

858 30 ConfigVariableFilename const * 0 8576 30 ConfigVariableFilename const * 30 ConfigVariableFilename const * 0 0 859 0 0 0 0 0 0 0 0 0 0

859 28 ConfigVariableFilename const 0 8832 28 ConfigVariableFilename const 28 ConfigVariableFilename const 0 0 794 0 0 0 0 0 0 0 0 0 0

860 24 ConfigVariableFilename * 0 8576 24 ConfigVariableFilename * 24 ConfigVariableFilename * 0 0 794 0 0 0 0 0 0 0 0 0 0

861 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 862 0 0 0 0 0 0 0 0 0 0

862 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 795 0 0 0 0 0 0 0 0 0 0

863 4 char 0 8194 4 char 4 char 0 5 0 0 0 0 0 0 0 0 0 0 0

864 25 ConfigVariableInt const * 0 8576 25 ConfigVariableInt const * 25 ConfigVariableInt const * 0 0 865 0 0 0 0 0 0 0 0 0 0

865 23 ConfigVariableInt const 0 8832 23 ConfigVariableInt const 23 ConfigVariableInt const 0 0 796 0 0 0 0 0 0 0 0 0 0

866 19 ConfigVariableInt * 0 8576 19 ConfigVariableInt * 19 ConfigVariableInt * 0 0 796 0 0 0 0 0 0 0 0 0 0

867 27 ConfigVariableInt64 const * 0 8576 27 ConfigVariableInt64 const * 27 ConfigVariableInt64 const * 0 0 868 0 0 0 0 0 0 0 0 0 0

868 25 ConfigVariableInt64 const 0 8832 25 ConfigVariableInt64 const 25 ConfigVariableInt64 const 0 0 797 0 0 0 0 0 0 0 0 0 0

869 21 ConfigVariableInt64 * 0 8576 21 ConfigVariableInt64 * 21 ConfigVariableInt64 * 0 0 797 0 0 0 0 0 0 0 0 0 0

870 26 ConfigVariableList const * 0 8576 26 ConfigVariableList const * 26 ConfigVariableList const * 0 0 871 0 0 0 0 0 0 0 0 0 0

871 24 ConfigVariableList const 0 8832 24 ConfigVariableList const 24 ConfigVariableList const 0 0 800 0 0 0 0 0 0 0 0 0 0

872 20 ConfigVariableList * 0 8576 20 ConfigVariableList * 20 ConfigVariableList * 0 0 800 0 0 0 0 0 0 0 0 0 0

873 19 DSearchPath const * 0 8576 19 DSearchPath const * 19 DSearchPath const * 0 0 802 0 0 0 0 0 0 0 0 0 0

874 26 ConfigVariableSearchPath * 0 8576 26 ConfigVariableSearchPath * 26 ConfigVariableSearchPath * 0 0 801 0 0 0 0 0 0 0 0 0 0

875 32 ConfigVariableSearchPath const * 0 8576 32 ConfigVariableSearchPath const * 32 ConfigVariableSearchPath const * 0 0 876 0 0 0 0 0 0 0 0 0 0

876 30 ConfigVariableSearchPath const 0 8832 30 ConfigVariableSearchPath const 30 ConfigVariableSearchPath const 0 0 801 0 0 0 0 0 0 0 0 0 0

877 7 Results 0 264192 20 DSearchPath::Results 20 DSearchPath::Results 787 0 0 0 0 0 0 0 0 0 0 0 0

878 9 Results * 0 8576 22 DSearchPath::Results * 22 DSearchPath::Results * 0 0 877 0 0 0 0 0 0 0 0 0 0

879 28 ConfigVariableString const * 0 8576 28 ConfigVariableString const * 28 ConfigVariableString const * 0 0 880 0 0 0 0 0 0 0 0 0 0

880 26 ConfigVariableString const 0 8832 26 ConfigVariableString const 26 ConfigVariableString const 0 0 803 0 0 0 0 0 0 0 0 0 0

881 22 ConfigVariableString * 0 8576 22 ConfigVariableString * 22 ConfigVariableString * 0 0 803 0 0 0 0 0 0 0 0 0 0

882 22 NotifyCategory const * 0 8576 22 NotifyCategory const * 22 NotifyCategory const * 0 0 883 0 0 0 0 0 0 0 0 0 0

883 20 NotifyCategory const 0 8832 20 NotifyCategory const 20 NotifyCategory const 0 0 804 0 0 0 0 0 0 0 0 0 0

884 8 long int 0 8210 8 long int 8 long int 0 1 0 0 0 0 0 0 0 0 0 0 0

885 12 MemoryBase * 0 8576 12 MemoryBase * 12 MemoryBase * 0 0 805 0 0 0 0 0 0 0 0 0 0

886 16 IDecryptStream * 0 8576 16 IDecryptStream * 16 IDecryptStream * 0 0 807 0 0 0 0 0 0 0 0 0 0

887 22 IDecryptStream const * 0 8576 22 IDecryptStream const * 22 IDecryptStream const * 0 0 888 0 0 0 0 0 0 0 0 0 0

888 20 IDecryptStream const 0 8832 20 IDecryptStream const 20 IDecryptStream const 0 0 807 0 0 0 0 0 0 0 0 0 0

889 16 OEncryptStream * 0 8576 16 OEncryptStream * 16 OEncryptStream * 0 0 809 0 0 0 0 0 0 0 0 0 0

890 20 StreamReader const * 0 8576 20 StreamReader const * 20 StreamReader const * 0 0 891 0 0 0 0 0 0 0 0 0 0

891 18 StreamReader const 0 8832 18 StreamReader const 18 StreamReader const 0 0 811 0 0 0 0 0 0 0 0 0 0

892 14 StreamReader * 0 8576 14 StreamReader * 14 StreamReader * 0 0 811 0 0 0 0 0 0 0 0 0 0

893 6 int8_t 0 2105344 6 int8_t 6 int8_t 0 0 894 0 0 0 0 0 0 0 0 0 0

894 11 signed char 0 8202 11 signed char 11 signed char 0 5 0 0 0 0 0 0 0 0 0 0 0

895 7 uint8_t 0 2105344 7 uint8_t 7 uint8_t 0 0 896 0 0 0 0 0 0 0 0 0 0

896 13 unsigned char 0 8198 13 unsigned char 13 unsigned char 0 5 0 0 0 0 0 0 0 0 0 0 0

897 7 int16_t 0 2105344 7 int16_t 7 int16_t 0 0 898 0 0 0 0 0 0 0 0 0 0

898 9 short int 0 8258 9 short int 9 short int 0 1 0 0 0 0 0 0 0 0 0 0 0

899 7 int32_t 0 2105344 7 int32_t 7 int32_t 0 0 776 0 0 0 0 0 0 0 0 0 0

900 8 uint16_t 0 2105344 8 uint16_t 8 uint16_t 0 0 901 0 0 0 0 0 0 0 0 0 0

901 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

902 8 uint32_t 0 2105344 8 uint32_t 8 uint32_t 0 0 903 0 0 0 0 0 0 0 0 0 0

903 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

904 8 uint64_t 0 2105344 8 uint64_t 8 uint64_t 0 0 829 0 0 0 0 0 0 0 0 0 0

905 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

906 10 PN_float64 0 2105344 10 PN_float64 10 PN_float64 0 0 793 0 0 0 0 0 0 0 0 0 0

907 20 StreamWriter const * 0 8576 20 StreamWriter const * 20 StreamWriter const * 0 0 908 0 0 0 0 0 0 0 0 0 0

908 18 StreamWriter const 0 8832 18 StreamWriter const 18 StreamWriter const 0 0 813 0 0 0 0 0 0 0 0 0 0

909 14 StreamWriter * 0 8576 14 StreamWriter * 14 StreamWriter * 0 0 813 0 0 0 0 0 0 0 0 0 0

910 19 StreamWrapperBase * 0 8576 19 StreamWrapperBase * 19 StreamWrapperBase * 0 0 815 0 0 0 0 0 0 0 0 0 0

911 16 IStreamWrapper * 0 8576 16 IStreamWrapper * 16 IStreamWrapper * 0 0 816 0 0 0 0 0 0 0 0 0 0

912 22 IStreamWrapper const * 0 8576 22 IStreamWrapper const * 22 IStreamWrapper const * 0 0 913 0 0 0 0 0 0 0 0 0 0

913 20 IStreamWrapper const 0 8832 20 IStreamWrapper const 20 IStreamWrapper const 0 0 816 0 0 0 0 0 0 0 0 0 0

914 16 OStreamWrapper * 0 8576 16 OStreamWrapper * 16 OStreamWrapper * 0 0 817 0 0 0 0 0 0 0 0 0 0

915 22 OStreamWrapper const * 0 8576 22 OStreamWrapper const * 22 OStreamWrapper const * 0 0 916 0 0 0 0 0 0 0 0 0 0

916 20 OStreamWrapper const 0 8832 20 OStreamWrapper const 20 OStreamWrapper const 0 0 817 0 0 0 0 0 0 0 0 0 0

917 15 StreamWrapper * 0 8576 15 StreamWrapper * 15 StreamWrapper * 0 0 818 0 0 0 0 0 0 0 0 0 0

918 21 StreamWrapper const * 0 8576 21 StreamWrapper const * 21 StreamWrapper const * 0 0 919 0 0 0 0 0 0 0 0 0 0

919 19 StreamWrapper const 0 8832 19 StreamWrapper const 19 StreamWrapper const 0 0 818 0 0 0 0 0 0 0 0 0 0

0
66
920 4 name 0 2 772 389 0 0 0 0 0 0 0 16 ConfigPage::name 0

921 7 special 0 2 775 390 0 0 0 0 0 0 0 19 ConfigPage::special 0

922 8 implicit 0 2 775 391 0 0 0 0 0 0 0 20 ConfigPage::implicit 0

923 4 sort 0 6 776 393 392 0 0 0 0 0 0 16 ConfigPage::sort 0

924 8 page_seq 0 2 776 394 0 0 0 0 0 0 0 20 ConfigPage::page_seq 0

925 11 trust_level 0 6 776 395 396 0 0 0 0 0 0 23 ConfigPage::trust_level 0

926 9 signature 0 2 772 397 0 0 0 0 0 0 0 21 ConfigPage::signature 0

927 12 declarations 0 66 777 405 0 0 0 0 403 0 0 24 ConfigPage::declarations 0

928 4 page 0 2 779 409 0 0 0 0 0 0 0 23 ConfigDeclaration::page 0

929 8 variable 0 2 780 410 0 0 0 0 0 0 0 27 ConfigDeclaration::variable 0

930 12 declarations 0 66 782 430 0 0 0 0 429 0 0 32 ConfigVariableCore::declarations 0

931 4 name 0 2 772 411 0 0 0 0 0 0 0 24 ConfigVariableCore::name 0

932 4 used 0 2 775 412 0 0 0 0 0 0 0 24 ConfigVariableCore::used 0

933 6 closed 0 2 775 416 0 0 0 0 0 0 0 26 ConfigVariableCore::closed 0

934 11 trust_level 0 2 776 417 0 0 0 0 0 0 0 31 ConfigVariableCore::trust_level 0

935 7 dynamic 0 2 775 418 0 0 0 0 0 0 0 27 ConfigVariableCore::dynamic 0

936 10 value_type 0 6 769 413 420 0 0 0 0 0 0 30 ConfigVariableCore::value_type 0

937 11 description 0 6 772 414 422 0 0 0 0 0 0 31 ConfigVariableCore::description 0

938 13 default_value 0 6 782 419 423 0 0 0 0 0 0 33 ConfigVariableCore::default_value 0

939 10 references 0 66 782 432 0 0 0 0 431 0 0 30 ConfigVariableCore::references 0

940 18 trusted_references 0 66 782 434 0 0 0 0 433 0 0 38 ConfigVariableCore::trusted_references 0

941 17 unique_references 0 66 782 436 0 0 0 0 435 0 0 37 ConfigVariableCore::unique_references 0

942 11 search_path 0 2 787 484 0 0 0 0 0 0 0 30 ConfigPageManager::search_path 0

943 12 prc_patterns 0 66 773 486 0 0 0 0 485 0 0 31 ConfigPageManager::prc_patterns 0

944 22 prc_encrypted_patterns 0 66 773 488 0 0 0 0 487 0 0 41 ConfigPageManager::prc_encrypted_patterns 0

945 23 prc_executable_patterns 0 66 773 490 0 0 0 0 489 0 0 42 ConfigPageManager::prc_executable_patterns 0

946 14 implicit_pages 0 66 779 494 0 0 0 0 493 0 0 33 ConfigPageManager::implicit_pages 0

947 14 explicit_pages 0 66 779 496 0 0 0 0 495 0 0 33 ConfigPageManager::explicit_pages 0

948 9 variables 0 66 780 503 0 0 0 0 502 0 0 32 ConfigVariableManager::variables 0

949 4 name 0 2 772 513 0 0 0 0 0 0 0 24 ConfigVariableBase::name 0

950 10 value_type 0 2 769 514 0 0 0 0 0 0 0 30 ConfigVariableBase::value_type 0

951 11 description 0 2 772 515 0 0 0 0 0 0 0 31 ConfigVariableBase::description 0

952 6 closed 0 2 775 517 0 0 0 0 0 0 0 26 ConfigVariableBase::closed 0

953 11 trust_level 0 2 776 518 0 0 0 0 0 0 0 31 ConfigVariableBase::trust_level 0

954 7 dynamic 0 2 775 519 0 0 0 0 0 0 0 27 ConfigVariableBase::dynamic 0

955 5 value 0 6 775 537 536 0 0 0 0 0 0 25 ConfigVariableBool::value 0

956 13 default_value 0 2 775 538 0 0 0 0 0 0 0 33 ConfigVariableBool::default_value 0

957 5 value 0 6 793 548 547 0 0 0 0 0 0 27 ConfigVariableDouble::value 0

958 13 default_value 0 2 793 549 0 0 0 0 0 0 0 35 ConfigVariableDouble::default_value 0

959 5 value 0 6 795 570 569 0 0 0 0 0 0 29 ConfigVariableFilename::value 0

960 13 default_value 0 2 795 571 0 0 0 0 0 0 0 37 ConfigVariableFilename::default_value 0

961 5 value 0 6 776 582 581 0 0 0 0 0 0 24 ConfigVariableInt::value 0

962 13 default_value 0 2 776 583 0 0 0 0 0 0 0 32 ConfigVariableInt::default_value 0

963 5 value 0 6 798 593 592 0 0 0 0 0 0 26 ConfigVariableInt64::value 0

964 13 default_value 0 2 798 594 0 0 0 0 0 0 0 34 ConfigVariableInt64::default_value 0

965 5 value 0 2 787 611 0 0 0 0 0 0 0 31 ConfigVariableSearchPath::value 0

966 13 default_value 0 2 802 612 0 0 0 0 0 0 0 39 ConfigVariableSearchPath::default_value 0

967 11 directories 0 66 795 621 0 0 0 0 620 0 0 37 ConfigVariableSearchPath::directories 0

968 5 value 0 6 772 637 636 0 0 0 0 0 0 27 ConfigVariableString::value 0

969 13 default_value 0 2 773 638 0 0 0 0 0 0 0 35 ConfigVariableString::default_value 0

970 8 fullname 0 2 773 646 0 0 0 0 0 0 0 24 NotifyCategory::fullname 0

971 8 basename 0 2 773 647 0 0 0 0 0 0 0 24 NotifyCategory::basename 0

972 8 severity 0 6 784 648 649 0 0 0 0 0 0 24 NotifyCategory::severity 0

973 8 children 0 66 806 665 0 0 0 0 664 0 0 24 NotifyCategory::children 0

974 9 algorithm 0 2 772 672 0 0 0 0 0 0 0 25 IDecryptStream::algorithm 0

975 10 key_length 0 2 776 673 0 0 0 0 0 0 0 26 IDecryptStream::key_length 0

976 15 iteration_count 0 2 776 674 0 0 0 0 0 0 0 31 IDecryptStream::iteration_count 0

977 9 algorithm 0 6 772 682 679 0 0 0 0 0 0 25 OEncryptStream::algorithm 0

978 10 key_length 0 6 776 683 680 0 0 0 0 0 0 26 OEncryptStream::key_length 0

979 15 iteration_count 0 6 776 684 681 0 0 0 0 0 0 31 OEncryptStream::iteration_count 0

980 7 istream 0 2 812 689 0 0 0 0 0 0 0 12 std::istream 0

981 7 ostream 0 2 814 720 0 0 0 0 0 0 0 12 std::ostream 0

982 9 softspace 0 6 776 748 749 0 0 0 0 0 0 23 StreamWriter::softspace 54
// Python 2 needs this for printing to work correctly.

983 7 istream 0 2 812 756 0 0 0 0 0 0 0 23 IStreamWrapper::istream 0

984 7 ostream 0 2 814 760 0 0 0 0 0 0 0 23 OStreamWrapper::ostream 0

985 8 iostream 0 2 819 767 0 0 0 0 0 0 0 23 StreamWrapper::iostream 0

7
986 16 get_declarations 0 429 430 36 ConfigVariableCore::get_declarations 0

987 14 get_references 0 431 432 34 ConfigVariableCore::get_references 0

988 22 get_trusted_references 0 433 434 42 ConfigVariableCore::get_trusted_references 0

989 21 get_unique_references 0 435 436 41 ConfigVariableCore::get_unique_references 0

990 13 get_variables 0 502 503 36 ConfigVariableManager::get_variables 0

991 15 get_directories 0 620 621 41 ConfigVariableSearchPath::get_directories 0

992 12 get_children 0 664 665 28 NotifyCategory::get_children 0

