'''OpenGL extension ARB.robustness_application_isolation

This module customises the behaviour of the 
OpenGL.raw.WGL.ARB.robustness_application_isolation to provide a more 
Python-friendly API

Overview (from the spec)
	
	GL_ARB_robustness and supporting window system extensions allow
	creating an OpenGL context supporting graphics reset notification
	behavior.  GL_ARB_robustness_isolation provides stronger
	guarantees about the possible side-effects of a graphics reset.
	
	It is expected that there may be a performance cost associated
	with isolating an application or share group from other contexts
	on the GPU. For this reason, GL_ARB_robustness_isolation is
	phrased as an opt-in mechanism, with a new context creation bit
	defined in the window system bindings. It is expected that
	implementations might only advertise the strings in this extension
	if both the implementation supports the desired isolation
	properties, and the context was created with the appropriate reset
	isolation bit.

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/ARB/robustness_application_isolation.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.ARB.robustness_application_isolation import *
from OpenGL.raw.WGL.ARB.robustness_application_isolation import _EXTENSION_NAME

def glInitRobustnessApplicationIsolationARB():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION