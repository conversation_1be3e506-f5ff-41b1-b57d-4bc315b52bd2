1730992849
3 3
10 libp3putil 4 xqRv 12 panda3d.core 
957
970 5 clear 0 4 1929 56 PointerToBase< ReferenceCountedVector< ushort > >::clear 0 1 18 0
75
inline void PointerToBase< ReferenceCountedVector< ushort > >::clear(void);

971 6 output 0 4 1929 57 PointerToBase< ReferenceCountedVector< ushort > >::output 0 1 19 0
88
void PointerToBase< ReferenceCountedVector< ushort > >::output(std::ostream &out) const;

972 19 ~PointerToArrayBase 0 516 1928 49 PointerToArrayBase< ushort >::~PointerToArrayBase 0 0 0
63
inline PointerToArrayBase< ushort >::~PointerToArrayBase(void);

973 11 operator == 0 4 1928 41 PointerToArrayBase< ushort >::operator == 0 1 16 64
// These are implemented in PointerToVoid, but expose them here.
103
inline bool PointerToArrayBase< ushort >::operator ==(PointerToArrayBase< ushort > const &other) const;

974 11 operator != 0 4 1928 41 PointerToArrayBase< ushort >::operator != 0 1 17 0
103
inline bool PointerToArrayBase< ushort >::operator !=(PointerToArrayBase< ushort > const &other) const;

975 19 ConstPointerToArray 0 260 1927 50 ConstPointerToArray< ushort >::ConstPointerToArray 0 2 1 2 0
198
inline ConstPointerToArray< ushort >::ConstPointerToArray(PointerToArray< ushort > const &copy);
inline ConstPointerToArray< ushort >::ConstPointerToArray(ConstPointerToArray< ushort > const &copy);

976 5 clear 0 4 1927 36 ConstPointerToArray< ushort >::clear 0 1 3 0
55
inline void ConstPointerToArray< ushort >::clear(void);

977 4 size 0 4 1927 35 ConstPointerToArray< ushort >::size 0 1 4 0
78
inline unsigned long long int ConstPointerToArray< ushort >::size(void) const;

978 11 get_element 0 4 1927 42 ConstPointerToArray< ushort >::get_element 0 1 5 0
96
inline ushort const &ConstPointerToArray< ushort >::get_element(unsigned long long int n) const;

979 11 __getitem__ 0 4 1927 42 ConstPointerToArray< ushort >::__getitem__ 0 1 6 0
89
ushort const &ConstPointerToArray< ushort >::__getitem__(unsigned long long int n) const;

980 8 get_data 0 4 1927 39 ConstPointerToArray< ushort >::get_data 0 1 7 0
62
PyObject *ConstPointerToArray< ushort >::get_data(void) const;

981 11 get_subdata 0 4 1927 42 ConstPointerToArray< ushort >::get_subdata 0 1 8 0
115
PyObject *ConstPointerToArray< ushort >::get_subdata(unsigned long long int n, unsigned long long int count) const;

982 13 get_ref_count 0 4 1927 44 ConstPointerToArray< ushort >::get_ref_count 0 1 9 0
68
inline int ConstPointerToArray< ushort >::get_ref_count(void) const;

983 18 get_node_ref_count 0 4 1927 49 ConstPointerToArray< ushort >::get_node_ref_count 0 1 10 0
73
inline int ConstPointerToArray< ushort >::get_node_ref_count(void) const;

984 5 count 0 4 1927 36 ConstPointerToArray< ushort >::count 0 1 11 0
89
inline unsigned long long int ConstPointerToArray< ushort >::count(ushort const &) const;

985 10 __reduce__ 0 4 1927 41 ConstPointerToArray< ushort >::__reduce__ 0 1 12 0
74
PyObject *ConstPointerToArray< ushort >::__reduce__(PyObject *self) const;

986 13 __getbuffer__ 0 4 1927 44 ConstPointerToArray< ushort >::__getbuffer__ 0 1 13 0
99
int ConstPointerToArray< ushort >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

987 17 __releasebuffer__ 0 4 1927 48 ConstPointerToArray< ushort >::__releasebuffer__ 0 1 14 0
93
void ConstPointerToArray< ushort >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

988 12 __deepcopy__ 0 4 1927 43 ConstPointerToArray< ushort >::__deepcopy__ 0 1 15 0
96
ConstPointerToArray< ushort > ConstPointerToArray< ushort >::__deepcopy__(PyObject *memo) const;

989 20 ~ConstPointerToArray 0 516 1927 51 ConstPointerToArray< ushort >::~ConstPointerToArray 0 0 0
58
ConstPointerToArray< ushort >::~ConstPointerToArray(void);

990 14 PointerToArray 0 260 1931 40 PointerToArray< ushort >::PointerToArray 0 3 20 21 22 0
279
inline PointerToArray< ushort >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((ushort const *)(0))));
inline PointerToArray< ushort >::PointerToArray(PointerToArray< ushort > const &copy);
PointerToArray< ushort >::PointerToArray(PyObject *self, PyObject *source);

991 11 empty_array 0 4 1931 37 PointerToArray< ushort >::empty_array 0 1 23 0
171
static inline PointerToArray< ushort > PointerToArray< ushort >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((ushort const *)(0))));

992 5 clear 0 4 1931 31 PointerToArray< ushort >::clear 0 1 24 0
50
inline void PointerToArray< ushort >::clear(void);

993 4 size 0 4 1931 30 PointerToArray< ushort >::size 0 1 25 0
73
inline unsigned long long int PointerToArray< ushort >::size(void) const;

994 9 push_back 0 4 1931 35 PointerToArray< ushort >::push_back 0 1 26 0
65
inline void PointerToArray< ushort >::push_back(ushort const &x);

995 8 pop_back 0 4 1931 34 PointerToArray< ushort >::pop_back 0 1 27 0
53
inline void PointerToArray< ushort >::pop_back(void);

996 11 get_element 0 4 1931 37 PointerToArray< ushort >::get_element 0 1 28 0
91
inline ushort const &PointerToArray< ushort >::get_element(unsigned long long int n) const;

997 11 set_element 0 4 1931 37 PointerToArray< ushort >::set_element 0 1 29 0
97
inline void PointerToArray< ushort >::set_element(unsigned long long int n, ushort const &value);

998 11 __getitem__ 0 4 1931 37 PointerToArray< ushort >::__getitem__ 0 1 30 0
84
ushort const &PointerToArray< ushort >::__getitem__(unsigned long long int n) const;

999 11 __setitem__ 0 4 1931 37 PointerToArray< ushort >::__setitem__ 0 1 31 0
90
void PointerToArray< ushort >::__setitem__(unsigned long long int n, ushort const &value);

1000 8 get_data 0 4 1931 34 PointerToArray< ushort >::get_data 0 1 32 0
57
PyObject *PointerToArray< ushort >::get_data(void) const;

1001 8 set_data 0 4 1931 34 PointerToArray< ushort >::set_data 0 1 33 0
56
void PointerToArray< ushort >::set_data(PyObject *data);

1002 11 get_subdata 0 4 1931 37 PointerToArray< ushort >::get_subdata 0 1 34 0
110
PyObject *PointerToArray< ushort >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1003 11 set_subdata 0 4 1931 37 PointerToArray< ushort >::set_subdata 0 1 35 0
145
inline void PointerToArray< ushort >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1004 13 get_ref_count 0 4 1931 39 PointerToArray< ushort >::get_ref_count 0 1 36 0
63
inline int PointerToArray< ushort >::get_ref_count(void) const;

1005 18 get_node_ref_count 0 4 1931 44 PointerToArray< ushort >::get_node_ref_count 0 1 37 0
68
inline int PointerToArray< ushort >::get_node_ref_count(void) const;

1006 5 count 0 4 1931 31 PointerToArray< ushort >::count 0 1 38 0
84
inline unsigned long long int PointerToArray< ushort >::count(ushort const &) const;

1007 10 __reduce__ 0 4 1931 36 PointerToArray< ushort >::__reduce__ 0 1 39 0
69
PyObject *PointerToArray< ushort >::__reduce__(PyObject *self) const;

1008 13 __getbuffer__ 0 4 1931 39 PointerToArray< ushort >::__getbuffer__ 0 1 40 0
88
int PointerToArray< ushort >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1009 17 __releasebuffer__ 0 4 1931 43 PointerToArray< ushort >::__releasebuffer__ 0 1 41 0
88
void PointerToArray< ushort >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1010 12 __deepcopy__ 0 4 1931 38 PointerToArray< ushort >::__deepcopy__ 0 1 42 0
86
PointerToArray< ushort > PointerToArray< ushort >::__deepcopy__(PyObject *memo) const;

1011 15 ~PointerToArray 0 516 1931 41 PointerToArray< ushort >::~PointerToArray 0 0 0
48
PointerToArray< ushort >::~PointerToArray(void);

1012 14 ~AnimInterface 0 518 1932 29 AnimInterface::~AnimInterface 0 0 10
/**
 *
 */
44
virtual AnimInterface::~AnimInterface(void);

1013 4 play 0 4 1932 19 AnimInterface::play 0 2 43 44 477
/**
 * Runs the entire animation from beginning to end and stops.
 */

/**
 * Runs the animation from the frame "from" to and including the frame "to",
 * at which point the animation is stopped.  Both "from" and "to" frame
 * numbers may be outside the range (0, get_num_frames()) and the animation
 * will follow the range correctly, reporting numbers modulo get_num_frames().
 * For instance, play(0, get_num_frames() * 2) will play the animation twice
 * and then stop.
 */
95
inline void AnimInterface::play(void);
inline void AnimInterface::play(double from, double to);

1014 4 loop 0 4 1932 19 AnimInterface::loop 0 2 45 46 392
/**
 * Starts the entire animation looping.  If restart is true, the animation is
 * restarted from the beginning; otherwise, it continues from the current
 * frame.
 */

/**
 * Loops the animation from the frame "from" to and including the frame "to",
 * indefinitely.  If restart is true, the animation is restarted from the
 * beginning; otherwise, it continues from the current frame.
 */
117
inline void AnimInterface::loop(bool restart);
inline void AnimInterface::loop(bool restart, double from, double to);

1015 8 pingpong 0 4 1932 23 AnimInterface::pingpong 0 2 47 48 369
/**
 * Starts the entire animation bouncing back and forth between its first frame
 * and last frame.  If restart is true, the animation is restarted from the
 * beginning; otherwise, it continues from the current frame.
 */

/**
 * Loops the animation from the frame "from" to and including the frame "to",
 * and then back in the opposite direction, indefinitely.
 */
125
inline void AnimInterface::pingpong(bool restart);
inline void AnimInterface::pingpong(bool restart, double from, double to);

1016 4 stop 0 4 1932 19 AnimInterface::stop 0 1 49 130
/**
 * Stops a currently playing or looping animation right where it is.  The
 * animation remains posed at the current frame.
 */
38
inline void AnimInterface::stop(void);

1017 4 pose 0 4 1932 19 AnimInterface::pose 0 1 50 72
/**
 * Sets the animation to the indicated frame and holds it there.
 */
46
inline void AnimInterface::pose(double frame);

1018 13 set_play_rate 0 4 1932 28 AnimInterface::set_play_rate 0 1 51 243
/**
 * Changes the rate at which the animation plays.  1.0 is the normal speed,
 * 2.0 is twice normal speed, and 0.5 is half normal speed.  0.0 is legal to
 * pause the animation, and a negative value will play the animation
 * backwards.
 */
59
inline void AnimInterface::set_play_rate(double play_rate);

1019 13 get_play_rate 0 4 1932 28 AnimInterface::get_play_rate 0 1 52 79
/**
 * Returns the rate at which the animation plays.  See set_play_rate().
 */
55
inline double AnimInterface::get_play_rate(void) const;

1020 14 get_frame_rate 0 4 1932 29 AnimInterface::get_frame_rate 0 1 53 234
/**
 * Returns the native frame rate of the animation.  This is the number of
 * frames per second that will elapse when the play_rate is set to 1.0.  It is
 * a fixed property of the animation and may not be adjusted by the user.
 */
56
inline double AnimInterface::get_frame_rate(void) const;

1021 14 get_num_frames 0 6 1932 29 AnimInterface::get_num_frames 0 1 54 278
/**
 * Returns the number of frames in the animation.  This is a property of the
 * animation and may not be directly adjusted by the user (although it may
 * change without warning with certain kinds of animations, since this is a
 * virtual method that may be overridden).
 */
54
virtual int AnimInterface::get_num_frames(void) const;

1022 9 get_frame 0 4 1932 24 AnimInterface::get_frame 0 1 55 116
/**
 * Returns the current integer frame number.  This number will be in the range
 * 0 <= f < get_num_frames().
 */
48
inline int AnimInterface::get_frame(void) const;

1023 14 get_next_frame 0 4 1932 29 AnimInterface::get_next_frame 0 1 56 330
/**
 * Returns the current integer frame number + 1, constrained to the range 0 <=
 * f < get_num_frames().
 *
 * If the play mode is PM_play, this will clamp to the same value as
 * get_frame() at the end of the animation.  If the play mode is any other
 * value, this will wrap around to frame 0 at the end of the animation.
 */
53
inline int AnimInterface::get_next_frame(void) const;

1024 8 get_frac 0 4 1932 23 AnimInterface::get_frac 0 1 57 314
/**
 * Returns the fractional part of the current frame.  Normally, this is in the
 * range 0.0 <= f < 1.0, but in the one special case of an animation playing
 * to its end frame and stopping, it might exactly equal 1.0.
 *
 * It will always be true that get_full_frame() + get_frac() ==
 * get_full_fframe().
 */
50
inline double AnimInterface::get_frac(void) const;

1025 14 get_full_frame 0 4 1932 29 AnimInterface::get_full_frame 0 1 58 372
/**
 * Returns the current integer frame number.
 *
 * Unlike the value returned by get_frame(), this frame number may extend
 * beyond the range of get_num_frames() if the frame range passed to play(),
 * loop(), etc.  did.
 *
 * Unlike the value returned by get_full_fframe(), this return value will
 * never exceed the value passed to to_frame in the play() method.
 */
53
inline int AnimInterface::get_full_frame(void) const;

1026 15 get_full_fframe 0 4 1932 30 AnimInterface::get_full_fframe 0 1 59 490
/**
 * Returns the current floating-point frame number.
 *
 * Unlike the value returned by get_frame(), this frame number may extend
 * beyond the range of get_num_frames() if the frame range passed to play(),
 * loop(), etc.  did.
 *
 * Unlike the value returned by get_full_frame(), this return value may equal
 * (to_frame + 1.0), when the animation has played to its natural end.
 * However, in this case the return value of get_full_frame() will be
 * to_frame, not (to_frame + 1).
 */
57
inline double AnimInterface::get_full_fframe(void) const;

1027 10 is_playing 0 4 1932 25 AnimInterface::is_playing 0 1 60 208
/**
 * Returns true if the animation is currently playing, false if it is stopped
 * (e.g.  because stop() or pose() was called, or because it reached the end
 * of the animation after play() was called).
 */
50
inline bool AnimInterface::is_playing(void) const;

1028 6 output 0 6 1932 21 AnimInterface::output 0 1 61 10
/**
 *
 */
60
virtual void AnimInterface::output(std::ostream &out) const;

1029 14 get_class_type 0 4 1932 29 AnimInterface::get_class_type 0 1 62 0
54
static TypeHandle AnimInterface::get_class_type(void);

1030 9 UpdateSeq 0 260 1937 20 UpdateSeq::UpdateSeq 0 2 63 64 132
/**
 * Creates an UpdateSeq in the given state.
 */

/**
 * Creates an UpdateSeq in the 'initial' state.
 */

/**
 *
 */

/**
 *
 */
89
constexpr UpdateSeq::UpdateSeq(void);
inline UpdateSeq::UpdateSeq(UpdateSeq const &copy);

1031 7 initial 0 4 1937 18 UpdateSeq::initial 0 1 65 0
52
static constexpr UpdateSeq UpdateSeq::initial(void);

1032 3 old 0 4 1937 14 UpdateSeq::old 0 1 66 0
48
static constexpr UpdateSeq UpdateSeq::old(void);

1033 5 fresh 0 4 1937 16 UpdateSeq::fresh 0 1 67 0
50
static constexpr UpdateSeq UpdateSeq::fresh(void);

1034 10 operator = 0 4 1937 21 UpdateSeq::operator = 0 1 68 10
/**
 *
 */
57
inline void UpdateSeq::operator =(UpdateSeq const &copy);

1035 5 clear 0 4 1937 16 UpdateSeq::clear 0 1 69 55
/**
 * Resets the UpdateSeq to the 'initial' state.
 */
35
inline void UpdateSeq::clear(void);

1036 10 is_initial 0 4 1937 21 UpdateSeq::is_initial 0 1 70 67
/**
 * Returns true if the UpdateSeq is in the 'initial' state.
 */
46
inline bool UpdateSeq::is_initial(void) const;

1037 6 is_old 0 4 1937 17 UpdateSeq::is_old 0 1 71 63
/**
 * Returns true if the UpdateSeq is in the 'old' state.
 */
42
inline bool UpdateSeq::is_old(void) const;

1038 8 is_fresh 0 4 1937 19 UpdateSeq::is_fresh 0 1 72 65
/**
 * Returns true if the UpdateSeq is in the 'fresh' state.
 */
44
inline bool UpdateSeq::is_fresh(void) const;

1039 10 is_special 0 4 1937 21 UpdateSeq::is_special 0 1 73 105
/**
 * Returns true if the UpdateSeq is in any special states, i.e.  'initial',
 * 'old', or 'fresh'.
 */
46
inline bool UpdateSeq::is_special(void) const;

1040 11 operator == 0 4 1937 22 UpdateSeq::operator == 0 1 74 0
65
inline bool UpdateSeq::operator ==(UpdateSeq const &other) const;

1041 11 operator != 0 4 1937 22 UpdateSeq::operator != 0 1 75 0
65
inline bool UpdateSeq::operator !=(UpdateSeq const &other) const;

1042 10 operator < 0 4 1937 21 UpdateSeq::operator < 0 1 76 0
64
inline bool UpdateSeq::operator <(UpdateSeq const &other) const;

1043 11 operator <= 0 4 1937 22 UpdateSeq::operator <= 0 1 77 0
65
inline bool UpdateSeq::operator <=(UpdateSeq const &other) const;

1044 10 operator > 0 4 1937 21 UpdateSeq::operator > 0 1 78 0
64
inline bool UpdateSeq::operator >(UpdateSeq const &other) const;

1045 11 operator >= 0 4 1937 22 UpdateSeq::operator >= 0 1 79 0
65
inline bool UpdateSeq::operator >=(UpdateSeq const &other) const;

1046 11 operator ++ 0 68 1937 22 UpdateSeq::operator ++ 0 1 80 0
46
inline UpdateSeq UpdateSeq::operator ++(void);

1047 11 operator ++ 0 4 1937 22 UpdateSeq::operator ++ 0 1 81 0
46
inline UpdateSeq UpdateSeq::operator ++(int );

1048 7 get_seq 0 4 1937 18 UpdateSeq::get_seq 0 1 82 107
/**
 * Returns the internal integer value associated with the UpdateSeq.  Useful
 * for debugging only.
 */
60
inline AtomicAdjust::Integer UpdateSeq::get_seq(void) const;

1049 6 output 0 4 1937 17 UpdateSeq::output 0 1 83 10
/**
 *
 */
55
inline void UpdateSeq::output(std::ostream &out) const;

1050 10 ~UpdateSeq 0 516 1937 21 UpdateSeq::~UpdateSeq 0 0 0
28
UpdateSeq::~UpdateSeq(void);

1051 6 fillin 0 6 1941 21 TypedWritable::fillin 0 1 84 308
/**
 * This internal function is intended to be called by each class's
 * make_from_bam() method to read in all of the relevant data from the BamFile
 * for the new object.  It is also called directly by the BamReader to re-read
 * the data for an object that has been placed on the stream for an update.
 */
79
virtual void TypedWritable::fillin(DatagramIterator &scan, BamReader *manager);

1052 17 mark_bam_modified 0 4 1941 32 TypedWritable::mark_bam_modified 0 1 85 345
/**
 * Increments the bam_modified counter, so that this object will be
 * invalidated and retransmitted on any open bam streams.  This should
 * normally not need to be called by user code; it should be called internally
 * when the object has been changed in a way that legitimately requires its
 * retransmission to any connected clients.
 */
51
inline void TypedWritable::mark_bam_modified(void);

1053 16 get_bam_modified 0 4 1941 31 TypedWritable::get_bam_modified 0 1 86 140
/**
 * Returns the current bam_modified counter.  This counter is normally
 * incremented automatically whenever the object is modified.
 */
61
inline UpdateSeq TypedWritable::get_bam_modified(void) const;

1054 10 __reduce__ 0 4 1941 25 TypedWritable::__reduce__ 0 1 87 0
58
PyObject *TypedWritable::__reduce__(PyObject *self) const;

1055 18 __reduce_persist__ 0 4 1941 33 TypedWritable::__reduce_persist__ 0 1 88 0
85
PyObject *TypedWritable::__reduce_persist__(PyObject *self, PyObject *pickler) const;

1056 20 encode_to_bam_stream 0 4 1941 35 TypedWritable::encode_to_bam_stream 0 2 89 90 854
/**
 * Converts the TypedWritable object into a single stream of data using a
 * BamWriter, and returns that data as a bytes object.  Returns an empty bytes
 * object on failure.
 *
 * This is a convenience method particularly useful for cases when you are
 * only serializing a single object.  If you have many objects to process, it
 * is more efficient to use the same BamWriter to serialize all of them
 * together.
 */

/**
 * Converts the TypedWritable object into a single stream of data using a
 * BamWriter, and stores that data in the indicated string.  Returns true on
 * success, false on failure.
 *
 * This is a convenience method particularly useful for cases when you are
 * only serializing a single object.  If you have many objects to process, it
 * is more efficient to use the same BamWriter to serialize all of them
 * together.
 */
165
inline vector_uchar TypedWritable::encode_to_bam_stream(void) const;
bool TypedWritable::encode_to_bam_stream(vector_uchar &data, BamWriter *writer = nullptr) const;

1057 26 decode_raw_from_bam_stream 0 4 1941 41 TypedWritable::decode_raw_from_bam_stream 0 0 953
/**
 * Reads the bytes created by a previous call to encode_to_bam_stream(), and
 * extracts the single object on those bytes.  Returns true on success, false
 * on error.
 *
 * This variant sets the TypedWritable and ReferenceCount pointers separately;
 * both are pointers to the same object.  The reference count is not
 * incremented; it is the caller's responsibility to manage the reference
 * count.
 *
 * Note that this method cannot be used to retrieve objects that do not
 * inherit from ReferenceCount, because these objects cannot persist beyond
 * the lifetime of the BamReader that reads them.  To retrieve these objects
 * from a bam stream, you must construct a BamReader directly.
 *
 * If you happen to know that the particular object in question inherits from
 * TypedWritableReferenceCount or PandaNode, consider calling the variant of
 * decode_from_bam_stream() defined for those methods, which presents a
 * simpler interface.
 */
149
static bool TypedWritable::decode_raw_from_bam_stream(TypedWritable *&ptr, ReferenceCount *&ref_ptr, vector_uchar data, BamReader *reader = nullptr);

1058 14 get_class_type 0 4 1941 29 TypedWritable::get_class_type 0 1 91 0
54
static TypeHandle TypedWritable::get_class_type(void);

1059 23 upcast_to_TypedWritable 0 12 1943 52 TypedWritableReferenceCount::upcast_to_TypedWritable 0 1 94 56
upcast from TypedWritableReferenceCount to TypedWritable
74
TypedWritable *TypedWritableReferenceCount::upcast_to_TypedWritable(void);

1060 39 downcast_to_TypedWritableReferenceCount 0 12 1941 54 TypedWritable::downcast_to_TypedWritableReferenceCount 0 0 58
downcast from TypedWritable to TypedWritableReferenceCount
90
TypedWritableReferenceCount *TypedWritable::downcast_to_TypedWritableReferenceCount(void);

1061 24 upcast_to_ReferenceCount 0 12 1943 53 TypedWritableReferenceCount::upcast_to_ReferenceCount 0 1 95 57
upcast from TypedWritableReferenceCount to ReferenceCount
76
ReferenceCount *TypedWritableReferenceCount::upcast_to_ReferenceCount(void);

1062 39 downcast_to_TypedWritableReferenceCount 0 12 1944 55 ReferenceCount::downcast_to_TypedWritableReferenceCount 0 0 59
downcast from ReferenceCount to TypedWritableReferenceCount
91
TypedWritableReferenceCount *ReferenceCount::downcast_to_TypedWritableReferenceCount(void);

1063 22 decode_from_bam_stream 0 4 1943 51 TypedWritableReferenceCount::decode_from_bam_stream 0 1 92 491
/**
 * Reads the bytes created by a previous call to encode_to_bam_stream(), and
 * extracts and returns the single object on those bytes.  Returns NULL on
 * error.
 *
 * This method is intended to replace decode_raw_from_bam_stream() when you
 * know the stream in question returns an object of type
 * TypedWritableReferenceCount, allowing for easier reference count
 * management.  Note that the caller is still responsible for maintaining the
 * reference count on the return value.
 */
148
static PointerTo< TypedWritableReferenceCount > TypedWritableReferenceCount::decode_from_bam_stream(vector_uchar data, BamReader *reader = nullptr);

1064 14 get_class_type 0 4 1943 43 TypedWritableReferenceCount::get_class_type 0 1 93 0
68
static TypeHandle TypedWritableReferenceCount::get_class_type(void);

1065 28 ~TypedWritableReferenceCount 0 516 1943 57 TypedWritableReferenceCount::~TypedWritableReferenceCount 0 0 0
64
TypedWritableReferenceCount::~TypedWritableReferenceCount(void);

1066 37 upcast_to_TypedWritableReferenceCount 0 12 1945 53 BamCacheRecord::upcast_to_TypedWritableReferenceCount 0 1 118 57
upcast from BamCacheRecord to TypedWritableReferenceCount
89
TypedWritableReferenceCount *BamCacheRecord::upcast_to_TypedWritableReferenceCount(void);

1067 26 downcast_to_BamCacheRecord 0 12 1943 55 TypedWritableReferenceCount::downcast_to_BamCacheRecord 0 0 59
downcast from TypedWritableReferenceCount to BamCacheRecord
78
BamCacheRecord *TypedWritableReferenceCount::downcast_to_BamCacheRecord(void);

1068 24 upcast_to_LinkedListNode 0 12 1945 40 BamCacheRecord::upcast_to_LinkedListNode 0 0 44
upcast from BamCacheRecord to LinkedListNode
63
LinkedListNode *BamCacheRecord::upcast_to_LinkedListNode(void);

1069 26 downcast_to_BamCacheRecord 0 12 1946 42 LinkedListNode::downcast_to_BamCacheRecord 0 0 46
downcast from LinkedListNode to BamCacheRecord
65
BamCacheRecord *LinkedListNode::downcast_to_BamCacheRecord(void);

1070 15 ~BamCacheRecord 0 518 1945 31 BamCacheRecord::~BamCacheRecord 0 0 10
/**
 *
 */
46
virtual BamCacheRecord::~BamCacheRecord(void);

1071 9 make_copy 0 4 1945 25 BamCacheRecord::make_copy 0 1 96 179
/**
 * Returns a duplicate of the BamCacheRecord.  The duplicate will not have a
 * data pointer set, even though one may have been assigned to the original
 * via set_data().
 */
73
inline PointerTo< BamCacheRecord > BamCacheRecord::make_copy(void) const;

1072 11 operator == 0 4 1945 27 BamCacheRecord::operator == 0 1 97 0
75
inline bool BamCacheRecord::operator ==(BamCacheRecord const &other) const;

1073 19 get_source_pathname 0 4 1945 35 BamCacheRecord::get_source_pathname 0 1 98 237
/**
 * Returns the full pathname to the source file that originally generated this
 * cache request.  In some cases, for instance in the case of a of a multipage
 * texture like "cube_#.png", this may not not a true filename on disk.
 */
71
inline Filename const &BamCacheRecord::get_source_pathname(void) const;

1074 18 get_cache_filename 0 4 1945 34 BamCacheRecord::get_cache_filename 0 1 99 230
/**
 * Returns the name of the cache file as hashed from the source_pathname.
 * This will be relative to the root of the cache directory, and it will not
 * include any suffixes that may be appended to resolve hash conflicts.
 */
70
inline Filename const &BamCacheRecord::get_cache_filename(void) const;

1075 20 get_source_timestamp 0 4 1945 36 BamCacheRecord::get_source_timestamp 0 1 100 204
/**
 * Returns the file timestamp of the original source file that generated this
 * cache record, if available.  In some cases the original file timestamp is
 * not available, and this will return 0.
 */
63
inline time_t BamCacheRecord::get_source_timestamp(void) const;

1076 17 get_recorded_time 0 4 1945 33 BamCacheRecord::get_recorded_time 0 1 101 84
/**
 * Returns the time at which this particular record was recorded or updated.
 */
60
inline time_t BamCacheRecord::get_recorded_time(void) const;

1077 23 get_num_dependent_files 0 4 1945 39 BamCacheRecord::get_num_dependent_files 0 1 102 75
/**
 * Returns the number of source files that contribute to the cache.
 */
63
inline int BamCacheRecord::get_num_dependent_files(void) const;

1078 22 get_dependent_pathname 0 4 1945 38 BamCacheRecord::get_dependent_pathname 0 1 103 94
/**
 * Returns the full pathname of the nth source files that contributes to the
 * cache.
 */
75
inline Filename const &BamCacheRecord::get_dependent_pathname(int n) const;

1079 20 dependents_unchanged 0 4 1945 36 BamCacheRecord::dependents_unchanged 0 1 104 124
/**
 * Returns true if all of the dependent files are still the same as when the
 * cache was recorded, false otherwise.
 */
54
bool BamCacheRecord::dependents_unchanged(void) const;

1080 21 clear_dependent_files 0 4 1945 37 BamCacheRecord::clear_dependent_files 0 1 105 80
/**
 * Empties the list of files that contribute to the data in this record.
 */
49
void BamCacheRecord::clear_dependent_files(void);

1081 18 add_dependent_file 0 4 1945 34 BamCacheRecord::add_dependent_file 0 2 106 107 309
/**
 * Adds the indicated file to the list of files that will be loaded to
 * generate the data in this record.  This should be called once for the
 * primary source file, and again for each secondary source file, if any.
 */

/**
 * Variant of add_dependent_file that takes an already opened VirtualFile.
 */
132
void BamCacheRecord::add_dependent_file(Filename const &pathname);
void BamCacheRecord::add_dependent_file(VirtualFile const *file);

1082 8 has_data 0 4 1945 24 BamCacheRecord::has_data 0 1 108 130
/**
 * Returns true if this cache record has an in-memory data object associated--
 * that is, the object stored in the cache.
 */
49
inline bool BamCacheRecord::has_data(void) const;

1083 10 clear_data 0 4 1945 26 BamCacheRecord::clear_data 0 1 109 147
/**
 * Removes the in-memory data object associated with this record, if any.
 * This does not affect the on-disk representation of the record.
 */
45
inline void BamCacheRecord::clear_data(void);

1084 8 get_data 0 4 1945 24 BamCacheRecord::get_data 0 1 110 139
/**
 * Returns a pointer to the data stored in the record, or NULL if there is no
 * data.  The pointer is not removed from the record.
 */
59
inline TypedWritable *BamCacheRecord::get_data(void) const;

1085 12 extract_data 0 4 1945 28 BamCacheRecord::extract_data 0 0 416
/**
 * Fills ptr and ref_ptr with the two different-typed pointers to the same
 * object, the data stored within this record.  This transfers ownership of
 * the data pointer; the caller will be responsible for managing the reference
 * counts on this object subsequently.
 *
 * Returns true if the record contained any data (and the pointers have been
 * filled), false if it didn't (and the pointers are NULL).
 */
88
inline bool BamCacheRecord::extract_data(TypedWritable *&ptr, ReferenceCount *&ref_ptr);

1086 8 set_data 0 4 1945 24 BamCacheRecord::set_data 0 4 111 112 113 114 916
/**
 * Stores a new data object on the record.  You should pass the same pointer
 * twice, to both parameters; this allows the C++ typecasting to automatically
 * convert the pointer into both a TypedWritable and a ReferenceCount pointer,
 * so that the BamCacheRecord object can reliably manage the reference counts.
 *
 * You may pass 0 or NULL as the second parameter.  If you do this, the
 * BamCacheRecord will not manage the object's reference count; it will be up
 * to you to ensure the object is not deleted during the lifetime of the
 * BamCacheRecord object.
 */

/**
 * This variant on set_data() is provided to easily pass objects deriving from
 * TypedWritable.
 */

/**
 * This variant on set_data() is provided to easily pass objects deriving from
 * TypedWritableReferenceCount.
 */

/**
 * This variant on set_data() is provided just to allow Python code to pass a
 * 0 as the second parameter.
 */
281
inline void BamCacheRecord::set_data(TypedWritable *ptr, ReferenceCount *ref_ptr);
inline void BamCacheRecord::set_data(TypedWritable *ptr);
inline void BamCacheRecord::set_data(TypedWritableReferenceCount *ptr);
inline void BamCacheRecord::set_data(TypedWritable *ptr, int dummy);

1087 6 output 0 4 1945 22 BamCacheRecord::output 0 1 115 10
/**
 *
 */
53
void BamCacheRecord::output(std::ostream &out) const;

1088 5 write 0 4 1945 21 BamCacheRecord::write 0 1 116 10
/**
 *
 */
74
void BamCacheRecord::write(std::ostream &out, int indent_level = 0) const;

1089 14 get_class_type 0 4 1945 30 BamCacheRecord::get_class_type 0 1 117 0
55
static TypeHandle BamCacheRecord::get_class_type(void);

1090 8 BamCache 0 260 1951 18 BamCache::BamCache 0 1 119 10
/**
 *
 */
25
BamCache::BamCache(void);

1091 9 ~BamCache 0 516 1951 19 BamCache::~BamCache 0 0 10
/**
 *
 */
26
BamCache::~BamCache(void);

1092 10 set_active 0 4 1951 20 BamCache::set_active 0 1 120 347
/**
 * Changes the state of the active flag.  "active" means that the cache should
 * be consulted automatically on loads, "not active" means that objects should
 * be loaded directly without consulting the cache.
 *
 * This represents the global flag.  Also see the individual cache_models,
 * cache_textures, cache_compressed_textures flags.
 */
44
inline void BamCache::set_active(bool flag);

1093 10 get_active 0 4 1951 20 BamCache::get_active 0 1 121 381
/**
 * Returns true if the BamCache is currently active, false if it is not.
 * "active" means that the cache should be consulted automatically on loads,
 * "not active" means that objects should be loaded directly without
 * consulting the cache.
 *
 * This represents the global flag.  Also see the individual cache_models,
 * cache_textures, cache_compressed_textures flags.
 */
45
inline bool BamCache::get_active(void) const;

1094 16 set_cache_models 0 4 1951 26 BamCache::set_cache_models 0 1 122 118
/**
 * Indicates whether model files (e.g.  egg files and bam files) will be
 * stored in the cache, as bam files.
 */
50
inline void BamCache::set_cache_models(bool flag);

1095 16 get_cache_models 0 4 1951 26 BamCache::get_cache_models 0 1 123 172
/**
 * Returns whether model files (e.g.  egg files and bam files) will be stored
 * in the cache, as bam files.
 *
 * This also returns false if get_active() is false.
 */
51
inline bool BamCache::get_cache_models(void) const;

1096 18 set_cache_textures 0 4 1951 28 BamCache::set_cache_textures 0 1 124 101
/**
 * Indicates whether texture files will be stored in the cache, as
 * uncompressed txo files.
 */
52
inline void BamCache::set_cache_textures(bool flag);

1097 18 get_cache_textures 0 4 1951 28 BamCache::get_cache_textures 0 1 125 174
/**
 * Returns whether texture files (e.g.  egg files and bam files) will be
 * stored in the cache, as txo files.
 *
 * This also returns false if get_active() is false.
 */
53
inline bool BamCache::get_cache_textures(void) const;

1098 29 set_cache_compressed_textures 0 4 1951 39 BamCache::set_cache_compressed_textures 0 1 126 722
/**
 * Indicates whether compressed texture files will be stored in the cache, as
 * compressed txo files.  The compressed data may either be generated in-CPU,
 * via the squish library, or it may be extracted from the GSG after the
 * texture has been loaded.
 *
 * This may be set in conjunction with set_cache_textures(), or independently
 * of it.  If set_cache_textures() is true and this is false, all textures
 * will be cached in their uncompressed form.  If set_cache_textures() is
 * false and this is true, only compressed textures will be cached, and they
 * will be cached in their compressed form.  If both are true, all textures
 * will be cached, in their uncompressed or compressed form appropriately.
 */
63
inline void BamCache::set_cache_compressed_textures(bool flag);

1099 29 get_cache_compressed_textures 0 4 1951 39 BamCache::get_cache_compressed_textures 0 1 127 202
/**
 * Returns whether compressed texture files will be stored in the cache, as
 * compressed txo files.  See set_cache_compressed_textures().
 *
 * This also returns false if get_active() is false.
 */
64
inline bool BamCache::get_cache_compressed_textures(void) const;

1100 26 set_cache_compiled_shaders 0 4 1951 36 BamCache::set_cache_compiled_shaders 0 1 128 184
/**
 * Indicates whether compiled shader programs will be stored in the cache, as
 * binary .sho files.  This may not be supported by all shader languages or
 * graphics renderers.
 */
60
inline void BamCache::set_cache_compiled_shaders(bool flag);

1101 26 get_cache_compiled_shaders 0 4 1951 36 BamCache::get_cache_compiled_shaders 0 1 129 196
/**
 * Returns whether compiled shader programs will be stored in the cache, as
 * binary .txo files.  See set_cache_compiled_shaders().
 *
 * This also returns false if get_active() is false.
 */
61
inline bool BamCache::get_cache_compiled_shaders(void) const;

1102 8 set_root 0 4 1951 18 BamCache::set_root 0 1 130 372
/**
 * Changes the current root pathname of the cache.  This specifies where the
 * cache files are stored on disk.  This should name a directory that is on a
 * disk local to the machine (not on a network-mounted disk), for instance,
 * /tmp/panda-cache or /c/panda-cache.
 *
 * If the directory does not already exist, it will be created as a result of
 * this call.
 */
46
void BamCache::set_root(Filename const &root);

1103 8 get_root 0 4 1951 18 BamCache::get_root 0 1 131 75
/**
 * Returns the current root pathname of the cache.  See set_root().
 */
47
inline Filename BamCache::get_root(void) const;

1104 14 set_flush_time 0 4 1951 24 BamCache::set_flush_time 0 1 132 86
/**
 * Specifies the time in seconds between automatic flushes of the cache index.
 */
53
inline void BamCache::set_flush_time(int flush_time);

1105 14 get_flush_time 0 4 1951 24 BamCache::get_flush_time 0 1 133 84
/**
 * Returns the time in seconds between automatic flushes of the cache index.
 */
48
inline int BamCache::get_flush_time(void) const;

1106 20 set_cache_max_kbytes 0 4 1951 30 BamCache::set_cache_max_kbytes 0 1 134 430
/**
 * Specifies the maximum size, in kilobytes, which the cache is allowed to
 * grow to.  If a newly cached file would exceed this size, an older file is
 * removed from the cache.
 *
 * Note that in the case of multiple different processes simultaneously
 * operating on the same cache directory, the actual cache size may slightly
 * exceed this value from time to time due to latency in checking between the
 * processes.
 */
59
inline void BamCache::set_cache_max_kbytes(int max_kbytes);

1107 20 get_cache_max_kbytes 0 4 1951 30 BamCache::get_cache_max_kbytes 0 1 135 121
/**
 * Returns the maximum size, in kilobytes, which the cache is allowed to grow
 * to.  See set_cache_max_kbytes().
 */
54
inline int BamCache::get_cache_max_kbytes(void) const;

1108 13 set_read_only 0 4 1951 23 BamCache::set_read_only 0 1 136 252
/**
 * Can be used to put the cache in read-only mode, or take it out of read-only
 * mode.  Note that if you put it into read-write mode, and it discovers that
 * it does not have write access, it will put itself right back into read-only
 * mode.
 */
45
inline void BamCache::set_read_only(bool ro);

1109 13 get_read_only 0 4 1951 23 BamCache::get_read_only 0 1 137 230
/**
 * Returns true if the cache is in read-only mode.  Normally, the cache starts
 * in read-write mode.  It can put itself into read-only mode automatically if
 * it discovers that it does not have write access to the cache.
 */
48
inline bool BamCache::get_read_only(void) const;

1110 6 lookup 0 4 1951 16 BamCache::lookup 0 1 138 782
/**
 * Looks up a file in the cache.
 *
 * If the file is cacheable, then regardless of whether the file is found in
 * the cache or not, this returns a BamCacheRecord.  On the other hand, if the
 * file cannot be cached, returns NULL.
 *
 * If record->has_data() returns true, then the file was found in the cache,
 * and you may call record->extract_data() to get the object.  If
 * record->has_data() returns false, then the file was not found in the cache
 * or the cache was stale; and you should reload the source file (calling
 * record->add_dependent_file() for each file loaded, including the original
 * source file), and then call record->set_data() to record the resulting
 * loaded object; and finally, you should call store() to write the cached
 * record to disk.
 */
114
PointerTo< BamCacheRecord > BamCache::lookup(Filename const &source_filename, std::string const &cache_extension);

1111 5 store 0 4 1951 15 BamCache::store 0 1 139 212
/**
 * Flushes a cache entry to disk.  You must have retrieved the cache record
 * via a prior call to lookup(), and then stored the data via
 * record->set_data().  Returns true on success, false on failure.
 */
45
bool BamCache::store(BamCacheRecord *record);

1112 20 consider_flush_index 0 4 1951 30 BamCache::consider_flush_index 0 1 140 92
/**
 * Flushes the index if enough time has elapsed since the index was last
 * flushed.
 */
42
void BamCache::consider_flush_index(void);

1113 11 flush_index 0 4 1951 21 BamCache::flush_index 0 1 141 48
/**
 * Ensures the index is written to disk.
 */
33
void BamCache::flush_index(void);

1114 10 list_index 0 4 1951 20 BamCache::list_index 0 1 142 63
/**
 * Writes the contents of the index to standard output.
 */
73
void BamCache::list_index(std::ostream &out, int indent_level = 0) const;

1115 14 get_global_ptr 0 4 1951 24 BamCache::get_global_ptr 0 1 143 124
/**
 * Returns a pointer to the global BamCache object, which is used
 * automatically by the ModelPool and TexturePool.
 */
55
static inline BamCache *BamCache::get_global_ptr(void);

1116 27 consider_flush_global_index 0 4 1951 37 BamCache::consider_flush_global_index 0 1 144 84
/**
 * If there is a global BamCache object, calls consider_flush_index() on it.
 */
63
static inline void BamCache::consider_flush_global_index(void);

1117 18 flush_global_index 0 4 1951 28 BamCache::flush_global_index 0 1 145 75
/**
 * If there is a global BamCache object, calls flush_index() on it.
 */
54
static inline void BamCache::flush_global_index(void);

1118 8 BamEnums 0 260 1952 18 BamEnums::BamEnums 0 2 146 147 0
97
inline BamEnums::BamEnums(void) = default;
inline BamEnums::BamEnums(BamEnums const &) = default;

1119 9 ~BamEnums 0 516 1952 19 BamEnums::~BamEnums 0 0 0
26
BamEnums::~BamEnums(void);

1120 13 LoaderOptions 0 260 1956 28 LoaderOptions::LoaderOptions 0 3 148 149 150 22
/**
 *
 */

/**
 *
 */
247
LoaderOptions::LoaderOptions(int flags = (::LoaderOptions::LF_search | ::LoaderOptions::LF_report_errors));
constexpr LoaderOptions::LoaderOptions(int flags, int texture_flags);
inline LoaderOptions::LoaderOptions(LoaderOptions const &) = default;

1121 9 set_flags 0 4 1956 24 LoaderOptions::set_flags 0 1 151 10
/**
 *
 */
48
inline void LoaderOptions::set_flags(int flags);

1122 9 get_flags 0 4 1956 24 LoaderOptions::get_flags 0 1 152 10
/**
 *
 */
48
inline int LoaderOptions::get_flags(void) const;

1123 17 set_texture_flags 0 4 1956 32 LoaderOptions::set_texture_flags 0 1 153 10
/**
 *
 */
56
inline void LoaderOptions::set_texture_flags(int flags);

1124 17 get_texture_flags 0 4 1956 32 LoaderOptions::get_texture_flags 0 1 154 10
/**
 *
 */
56
inline int LoaderOptions::get_texture_flags(void) const;

1125 21 set_texture_num_views 0 4 1956 36 LoaderOptions::set_texture_num_views 0 1 155 459
/**
 * Specifies the expected number of views to load for the texture.  This is
 * ignored unless TF_multiview is included in texture_flags.  This must be
 * specified when loading a 3-d multiview texture or 2-d texture array, in
 * which case it is used to differentiate z levels from separate views; it
 * may be zero in the case of 2-d textures or cube maps, in which case the
 * number of views can be inferred from the number of images found on disk.
 */
64
inline void LoaderOptions::set_texture_num_views(int num_views);

1126 21 get_texture_num_views 0 4 1956 36 LoaderOptions::get_texture_num_views 0 1 156 39
/**
 * See set_texture_num_views().
 */
60
inline int LoaderOptions::get_texture_num_views(void) const;

1127 22 set_auto_texture_scale 0 4 1956 37 LoaderOptions::set_auto_texture_scale 0 1 157 209
/**
 * Set this flag to ATS_none, ATS_up, ATS_down, or ATS_pad to control how a
 * texture is scaled from disk when it is subsequently loaded.  Set it to
 * ATS_unspecified to restore the default behavior.
 */
74
inline void LoaderOptions::set_auto_texture_scale(AutoTextureScale scale);

1128 22 get_auto_texture_scale 0 4 1956 37 LoaderOptions::get_auto_texture_scale 0 1 158 40
/**
 * See set_auto_texture_scale().
 */
74
inline AutoTextureScale LoaderOptions::get_auto_texture_scale(void) const;

1129 6 output 0 4 1956 21 LoaderOptions::output 0 1 159 10
/**
 *
 */
52
void LoaderOptions::output(std::ostream &out) const;

1130 14 ~LoaderOptions 0 516 1956 29 LoaderOptions::~LoaderOptions 0 0 0
36
LoaderOptions::~LoaderOptions(void);

1131 9 BamReader 0 260 1959 20 BamReader::BamReader 0 1 160 50
// The primary interface for a caller.

/**
 *
 */
67
explicit BamReader::BamReader(DatagramGenerator *source = nullptr);

1132 10 ~BamReader 0 516 1959 21 BamReader::~BamReader 0 0 10
/**
 *
 */
28
BamReader::~BamReader(void);

1133 10 set_source 0 4 1959 21 BamReader::set_source 0 1 161 142
/**
 * Changes the source of future datagrams for this BamReader.  This also
 * implicitly calls init() if it has not already been called.
 */
54
void BamReader::set_source(DatagramGenerator *source);

1134 10 get_source 0 4 1959 21 BamReader::get_source 0 1 162 100
/**
 * Returns the current source of the BamReader as set by set_source() or the
 * constructor.
 */
54
inline DatagramGenerator *BamReader::get_source(void);

1135 4 init 0 4 1959 15 BamReader::init 0 1 163 210
/**
 * Initializes the BamReader prior to reading any objects from its source.
 * This includes reading the Bam header.
 *
 * This returns true if the BamReader successfully initialized, false
 * otherwise.
 */
27
bool BamReader::init(void);

1136 12 set_aux_data 0 4 1959 23 BamReader::set_aux_data 0 1 164 1007
/**
 * Associates an arbitrary block of data with the indicated object (or NULL),
 * and the indicated name.
 *
 * This is intended to provide a place for temporary storage for objects
 * reading themselves from the bam file.  To use it, inherit from
 * BamReader::AuxData and store whatever data you like there.  Then associate
 * your AuxData with the object as it is being read with set_aux_data().  You
 * may later set the aux data to NULL to remove it; or it will automatically
 * be removed (and deleted) after finalize() is called for the object in
 * question.
 *
 * If the TypedWritable pointer is NULL, the the aux data is stored globally
 * for the BamReader in general.  This pointer is available to any bam
 * objects, and will not be automatically removed until the BamReader itself
 * destructs.
 *
 * In either case, the name is just an arbitrary user-defined key.  If there
 * is already a data pointer stored for the obj/name pair, that data pointer
 * will be replaced (and deleted).
 */
100
void BamReader::set_aux_data(TypedWritable *obj, std::string const &name, BamReader::AuxData *data);

1137 12 get_aux_data 0 4 1959 23 BamReader::get_aux_data 0 1 165 173
/**
 * Returns the pointer previously associated with the bam reader by a previous
 * call to set_aux_data(), or NULL if data with the indicated key has not been
 * set.
 */
95
BamReader::AuxData *BamReader::get_aux_data(TypedWritable *obj, std::string const &name) const;

1138 12 get_filename 0 4 1959 23 BamReader::get_filename 0 1 166 199
/**
 * If a BAM is a file, then the BamReader should contain the name of the file.
 * This enables the reader to interpret pathnames in the BAM as relative to
 * the directory containing the BAM.
 */
59
inline Filename const &BamReader::get_filename(void) const;

1139 18 get_loader_options 0 4 1959 29 BamReader::get_loader_options 0 1 167 98
/**
 * Returns the LoaderOptions passed to the loader when the model was
 * requested, if any.
 */
70
inline LoaderOptions const &BamReader::get_loader_options(void) const;

1140 18 set_loader_options 0 4 1959 29 BamReader::set_loader_options 0 1 168 58
/**
 * Specifies the LoaderOptions for this BamReader.
 */
72
inline void BamReader::set_loader_options(LoaderOptions const &options);

1141 11 read_object 0 4 1959 22 BamReader::read_object 0 1 169 1393
/**
 * Reads a single object from the Bam file.  If the object type is known, a
 * new object of the appropriate type is created and returned; otherwise, NULL
 * is returned.  NULL is also returned when the end of the file is reached.
 * is_eof() may be called to differentiate between these two cases.
 *
 * This may be called repeatedly to extract out all the objects in the Bam
 * file, but typically (especially for scene graph files, indicated with the
 * .bam extension), only one object is retrieved directly from the Bam file:
 * the root of the scene graph.  The remaining objects will all be retrieved
 * recursively by the first object.
 *
 * Note that the object returned may not yet be complete.  In particular, some
 * of its pointers may not be filled in; you must call resolve() to fill in
 * all the available pointers before you can safely use any objects returned
 * by read_object().
 *
 * This flavor of read_object() requires the caller to know what type of
 * object it has received in order to properly manage the reference counts.
 */

/**
 * Reads a single object from the Bam file.
 *
 * This flavor of read_object() returns both a TypedWritable and a
 * ReferenceCount pointer to the same object, so the reference count may be
 * tracked reliably, without having to know precisely what type of object we
 * have.
 * @return true on success, or false on failure.
 */
120
TypedWritable *BamReader::read_object(void);
bool BamReader::read_object(TypedWritable *&ptr, ReferenceCount *&ref_ptr);

1142 6 is_eof 0 4 1959 17 BamReader::is_eof 0 1 170 138
/**
 * Returns true if the reader has reached end-of-file, false otherwise.  This
 * call is only valid after a call to read_object().
 */
42
inline bool BamReader::is_eof(void) const;

1143 7 resolve 0 4 1959 18 BamReader::resolve 0 1 171 566
/**
 * This may be called at any time during processing of the Bam file to resolve
 * all the known pointers so far.  It is usually called at the end of the
 * processing, after all objects have been read, which is generally the best
 * time to call it.
 *
 * This must be called at least once after reading a particular object via
 * read_object() in order to validate that object.
 *
 * The return value is true if all objects have been resolved, or false if
 * some objects are still outstanding (in which case you will need to call
 * resolve() again later).
 */
30
bool BamReader::resolve(void);

1144 14 change_pointer 0 4 1959 25 BamReader::change_pointer 0 1 172 406
/**
 * Indicates that an object recently read from the bam stream should be
 * replaced with a new object.  Any future occurrences of the original object
 * in the stream will henceforth return the new object instead.
 *
 * The return value is true if the replacement was successfully made, or false
 * if the object was not read from the stream (or if change_pointer had
 * already been called on it).
 */
100
bool BamReader::change_pointer(TypedWritable const *orig_pointer, TypedWritable const *new_pointer);

1145 18 get_file_major_ver 0 4 1959 29 BamReader::get_file_major_ver 0 1 173 81
/**
 * Returns the major version number of the Bam file currently being read.
 */
53
inline int BamReader::get_file_major_ver(void) const;

1146 18 get_file_minor_ver 0 4 1959 29 BamReader::get_file_minor_ver 0 1 174 81
/**
 * Returns the minor version number of the Bam file currently being read.
 */
53
inline int BamReader::get_file_minor_ver(void) const;

1147 15 get_file_endian 0 4 1959 26 BamReader::get_file_endian 0 1 175 254
/**
 * Returns the endian preference indicated by the Bam file currently being
 * read.  This does not imply that every number is stored using the indicated
 * convention, but individual objects may choose to respect this flag when
 * recording data.
 */
66
inline BamEnums::BamEndian BamReader::get_file_endian(void) const;

1148 24 get_file_stdfloat_double 0 4 1959 35 BamReader::get_file_stdfloat_double 0 1 176 220
/**
 * Returns true if the file stores all "standard" floats as 64-bit doubles, or
 * false if they are 32-bit floats.  This is determined by the compilation
 * flags of the version of Panda that generated this file.
 */
60
inline bool BamReader::get_file_stdfloat_double(void) const;

1149 21 get_current_major_ver 0 4 1959 32 BamReader::get_current_major_ver 0 1 177 174
/**
 * Returns the major version number of Bam files supported by the current code
 * base.  This must match get_file_major_ver() in order to successfully read a
 * file.
 */
56
inline int BamReader::get_current_major_ver(void) const;

1150 21 get_current_minor_ver 0 4 1959 32 BamReader::get_current_minor_ver 0 1 178 184
/**
 * Returns the minor version number of Bam files supported by the current code
 * base.  This must match or exceed get_file_minor_ver() in order to
 * successfully read a file.
 */
56
inline int BamReader::get_current_minor_ver(void) const;

1151 16 get_file_version 0 4 1959 27 BamReader::get_file_version 0 1 179 0
50
PyObject *BamReader::get_file_version(void) const;

1152 16 register_factory 0 4 1959 27 BamReader::register_factory 0 1 180 208
/**
 * Registers a factory function that is called when an object of the given
 * type is encountered within the .bam stream.
 *
 * @param user_data an optional pointer to be passed along to the function.
 */
75
static void BamReader::register_factory(TypeHandle handle, PyObject *func);

1153 9 BamWriter 0 260 1966 20 BamWriter::BamWriter 0 2 181 182 10
/**
 *
 */
120
explicit BamWriter::BamWriter(DatagramSink *target = nullptr);
inline BamWriter::BamWriter(BamWriter const &) = default;

1154 10 ~BamWriter 0 516 1966 21 BamWriter::~BamWriter 0 0 10
/**
 *
 */
28
BamWriter::~BamWriter(void);

1155 10 set_target 0 4 1966 21 BamWriter::set_target 0 1 183 153
/**
 * Changes the destination of future datagrams written by the BamWriter.  This
 * also implicitly calls init() if it has not already been called.
 */
49
void BamWriter::set_target(DatagramSink *target);

1156 10 get_target 0 4 1966 21 BamWriter::get_target 0 1 184 100
/**
 * Returns the current target of the BamWriter as set by set_target() or the
 * constructor.
 */
49
inline DatagramSink *BamWriter::get_target(void);

1157 4 init 0 4 1966 15 BamWriter::init 0 1 185 220
/**
 * Initializes the BamWriter prior to writing any objects to its output
 * stream.  This includes writing out the Bam header.
 *
 * This returns true if the BamWriter successfully initialized, false
 * otherwise.
 */
27
bool BamWriter::init(void);

1158 12 get_filename 0 4 1966 23 BamWriter::get_filename 0 1 186 197
/**
 * If a BAM is a file, then the BamWriter should contain the name of the file.
 * This enables the writer to convert pathnames in the BAM to relative to the
 * directory containing the BAM.
 */
59
inline Filename const &BamWriter::get_filename(void) const;

1159 12 write_object 0 4 1966 23 BamWriter::write_object 0 1 187 736
/**
 * Writes a single object to the Bam file, so that the
 * BamReader::read_object() can later correctly restore the object and all its
 * pointers.
 *
 * This implicitly also writes any additional objects this object references
 * (if they haven't already been written), so that pointers may be fully
 * resolved.
 *
 * This may be called repeatedly to write a sequence of objects to the Bam
 * file, but typically (especially for scene graph files, indicated with the
 * .bam extension), only one object is written directly from the Bam file: the
 * root of the scene graph.  The remaining objects will all be written
 * recursively by the first object.
 *
 * Returns true if the object is successfully written, false otherwise.
 */
55
bool BamWriter::write_object(TypedWritable const *obj);

1160 10 has_object 0 4 1966 21 BamWriter::has_object 0 1 188 170
/**
 * Returns true if the object has previously been written (or at least
 * requested to be written) to the bam file, or false if we've never heard of
 * it before.
 */
59
bool BamWriter::has_object(TypedWritable const *obj) const;

1161 5 flush 0 4 1966 16 BamWriter::flush 0 1 189 85
/**
 * Ensures that all data written thus far is manifested on the output stream.
 */
28
void BamWriter::flush(void);

1162 18 get_file_major_ver 0 4 1966 29 BamWriter::get_file_major_ver 0 1 190 84
/**
 * Returns the major version number of the Bam file currently being written.
 */
53
inline int BamWriter::get_file_major_ver(void) const;

1163 18 get_file_minor_ver 0 4 1966 29 BamWriter::get_file_minor_ver 0 1 191 84
/**
 * Returns the minor version number of the Bam file currently being written.
 */
53
inline int BamWriter::get_file_minor_ver(void) const;

1164 18 set_file_minor_ver 0 4 1966 29 BamWriter::set_file_minor_ver 0 1 192 232
/**
 * Changes the minor .bam version to write.  This should be called before
 * init().  Each Panda version has only a fairly narrow range of versions it
 * is able to write; consult the .bam documentation for more information.
 */
57
inline void BamWriter::set_file_minor_ver(int minor_ver);

1165 15 get_file_endian 0 4 1966 26 BamWriter::get_file_endian 0 1 193 257
/**
 * Returns the endian preference indicated by the Bam file currently being
 * written.  This does not imply that every number is stored using the
 * indicated convention, but individual objects may choose to respect this
 * flag when recording data.
 */
66
inline BamEnums::BamEndian BamWriter::get_file_endian(void) const;

1166 24 get_file_stdfloat_double 0 4 1966 35 BamWriter::get_file_stdfloat_double 0 1 194 248
/**
 * Returns true if the file will store all "standard" floats as 64-bit
 * doubles, or false if they are 32-bit floats.  This isn't runtime settable;
 * it's based on the compilation flags of the version of Panda that generated
 * this file.
 */
60
inline bool BamWriter::get_file_stdfloat_double(void) const;

1167 21 get_file_texture_mode 0 4 1966 32 BamWriter::get_file_texture_mode 0 1 195 196
/**
 * Returns the BamTextureMode preference indicated by the Bam file currently
 * being written.  Texture objects written to this Bam file will be encoded
 * according to the specified mode.
 */
77
inline BamEnums::BamTextureMode BamWriter::get_file_texture_mode(void) const;

1168 21 set_file_texture_mode 0 4 1966 32 BamWriter::set_file_texture_mode 0 1 196 312
/**
 * Changes the BamTextureMode preference for the Bam file currently being
 * written.  Texture objects written to this Bam file will be encoded
 * according to the specified mode.
 *
 * This should be called after the call to init(), or it will be overwritten
 * with the default mode in the config file.
 */
89
inline void BamWriter::set_file_texture_mode(BamEnums::BamTextureMode file_texture_mode);

1169 13 get_root_node 0 4 1966 24 BamWriter::get_root_node 0 1 197 165
/**
 * Returns the root node of the part of the scene graph we are currently
 * writing out.  This is used for determining what to make NodePaths relative
 * to.
 */
59
inline TypedWritable *BamWriter::get_root_node(void) const;

1170 13 set_root_node 0 4 1966 24 BamWriter::set_root_node 0 1 198 160
/**
 * Sets the root node of the part of the scene graph we are currently writing
 * out.  NodePaths written to this bam file will be relative to this node.
 */
63
inline void BamWriter::set_root_node(TypedWritable *root_node);

1171 16 get_file_version 0 4 1966 27 BamWriter::get_file_version 0 0 0
50
PyObject *BamWriter::get_file_version(void) const;

1172 7 BitMask 0 260 1969 32 BitMask< uint16_t, 16 >::BitMask 0 3 199 200 201 0
215
constexpr BitMask< uint16_t, 16 >::BitMask(void) = default;
inline constexpr BitMask< uint16_t, 16 >::BitMask(uint16_t init_value);
inline BitMask< uint16_t, 16 >::BitMask(BitMask< uint16_t, 16 > const &) = default;

1173 6 all_on 0 4 1969 31 BitMask< uint16_t, 16 >::all_on 0 1 202 0
76
static inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::all_on(void);

1174 7 all_off 0 4 1969 32 BitMask< uint16_t, 16 >::all_off 0 1 203 0
77
static inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::all_off(void);

1175 8 lower_on 0 4 1969 33 BitMask< uint16_t, 16 >::lower_on 0 1 204 0
85
static inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::lower_on(int on_bits);

1176 3 bit 0 4 1969 28 BitMask< uint16_t, 16 >::bit 0 1 205 0
78
static inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::bit(int index);

1177 5 range 0 4 1969 30 BitMask< uint16_t, 16 >::range 0 1 206 0
92
static inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::range(int low_bit, int size);

1178 16 has_max_num_bits 0 4 1969 41 BitMask< uint16_t, 16 >::has_max_num_bits 0 1 207 0
70
static constexpr bool BitMask< uint16_t, 16 >::has_max_num_bits(void);

1179 16 get_max_num_bits 0 4 1969 41 BitMask< uint16_t, 16 >::get_max_num_bits 0 1 208 0
69
static constexpr int BitMask< uint16_t, 16 >::get_max_num_bits(void);

1180 12 get_num_bits 0 4 1969 37 BitMask< uint16_t, 16 >::get_num_bits 0 1 209 0
64
constexpr int BitMask< uint16_t, 16 >::get_num_bits(void) const;

1181 7 get_bit 0 4 1969 32 BitMask< uint16_t, 16 >::get_bit 0 1 210 0
62
inline bool BitMask< uint16_t, 16 >::get_bit(int index) const;

1182 7 set_bit 0 4 1969 32 BitMask< uint16_t, 16 >::set_bit 0 1 211 0
56
inline void BitMask< uint16_t, 16 >::set_bit(int index);

1183 9 clear_bit 0 4 1969 34 BitMask< uint16_t, 16 >::clear_bit 0 1 212 0
58
inline void BitMask< uint16_t, 16 >::clear_bit(int index);

1184 10 set_bit_to 0 4 1969 35 BitMask< uint16_t, 16 >::set_bit_to 0 1 213 0
71
inline void BitMask< uint16_t, 16 >::set_bit_to(int index, bool value);

1185 7 is_zero 0 4 1969 32 BitMask< uint16_t, 16 >::is_zero 0 1 214 0
57
inline bool BitMask< uint16_t, 16 >::is_zero(void) const;

1186 9 is_all_on 0 4 1969 34 BitMask< uint16_t, 16 >::is_all_on 0 1 215 0
59
inline bool BitMask< uint16_t, 16 >::is_all_on(void) const;

1187 7 extract 0 4 1969 32 BitMask< uint16_t, 16 >::extract 0 1 216 0
78
inline uint16_t BitMask< uint16_t, 16 >::extract(int low_bit, int size) const;

1188 5 store 0 4 1969 30 BitMask< uint16_t, 16 >::store 0 1 217 0
82
inline void BitMask< uint16_t, 16 >::store(uint16_t value, int low_bit, int size);

1189 10 has_any_of 0 4 1969 35 BitMask< uint16_t, 16 >::has_any_of 0 1 218 0
77
inline bool BitMask< uint16_t, 16 >::has_any_of(int low_bit, int size) const;

1190 10 has_all_of 0 4 1969 35 BitMask< uint16_t, 16 >::has_all_of 0 1 219 0
77
inline bool BitMask< uint16_t, 16 >::has_all_of(int low_bit, int size) const;

1191 9 set_range 0 4 1969 34 BitMask< uint16_t, 16 >::set_range 0 1 220 0
70
inline void BitMask< uint16_t, 16 >::set_range(int low_bit, int size);

1192 11 clear_range 0 4 1969 36 BitMask< uint16_t, 16 >::clear_range 0 1 221 0
72
inline void BitMask< uint16_t, 16 >::clear_range(int low_bit, int size);

1193 12 set_range_to 0 4 1969 37 BitMask< uint16_t, 16 >::set_range_to 0 1 222 0
85
inline void BitMask< uint16_t, 16 >::set_range_to(bool value, int low_bit, int size);

1194 8 get_word 0 4 1969 33 BitMask< uint16_t, 16 >::get_word 0 1 223 0
62
inline uint16_t BitMask< uint16_t, 16 >::get_word(void) const;

1195 8 set_word 0 4 1969 33 BitMask< uint16_t, 16 >::set_word 0 1 224 0
62
inline void BitMask< uint16_t, 16 >::set_word(uint16_t value);

1196 15 get_num_on_bits 0 4 1969 40 BitMask< uint16_t, 16 >::get_num_on_bits 0 1 225 0
64
inline int BitMask< uint16_t, 16 >::get_num_on_bits(void) const;

1197 16 get_num_off_bits 0 4 1969 41 BitMask< uint16_t, 16 >::get_num_off_bits 0 1 226 0
65
inline int BitMask< uint16_t, 16 >::get_num_off_bits(void) const;

1198 17 get_lowest_on_bit 0 4 1969 42 BitMask< uint16_t, 16 >::get_lowest_on_bit 0 1 227 0
66
inline int BitMask< uint16_t, 16 >::get_lowest_on_bit(void) const;

1199 18 get_lowest_off_bit 0 4 1969 43 BitMask< uint16_t, 16 >::get_lowest_off_bit 0 1 228 0
67
inline int BitMask< uint16_t, 16 >::get_lowest_off_bit(void) const;

1200 18 get_highest_on_bit 0 4 1969 43 BitMask< uint16_t, 16 >::get_highest_on_bit 0 1 229 0
67
inline int BitMask< uint16_t, 16 >::get_highest_on_bit(void) const;

1201 19 get_highest_off_bit 0 4 1969 44 BitMask< uint16_t, 16 >::get_highest_off_bit 0 1 230 0
68
inline int BitMask< uint16_t, 16 >::get_highest_off_bit(void) const;

1202 29 get_next_higher_different_bit 0 4 1969 54 BitMask< uint16_t, 16 >::get_next_higher_different_bit 0 1 231 0
85
inline int BitMask< uint16_t, 16 >::get_next_higher_different_bit(int low_bit) const;

1203 15 invert_in_place 0 4 1969 40 BitMask< uint16_t, 16 >::invert_in_place 0 1 232 0
59
inline void BitMask< uint16_t, 16 >::invert_in_place(void);

1204 18 has_bits_in_common 0 4 1969 43 BitMask< uint16_t, 16 >::has_bits_in_common 0 1 233 0
100
inline bool BitMask< uint16_t, 16 >::has_bits_in_common(BitMask< uint16_t, 16 > const &other) const;

1205 5 clear 0 4 1969 30 BitMask< uint16_t, 16 >::clear 0 1 234 0
49
inline void BitMask< uint16_t, 16 >::clear(void);

1206 6 output 0 4 1969 31 BitMask< uint16_t, 16 >::output 0 1 235 0
62
void BitMask< uint16_t, 16 >::output(std::ostream &out) const;

1207 13 output_binary 0 4 1969 38 BitMask< uint16_t, 16 >::output_binary 0 1 236 0
91
void BitMask< uint16_t, 16 >::output_binary(std::ostream &out, int spaces_every = 4) const;

1208 10 output_hex 0 4 1969 35 BitMask< uint16_t, 16 >::output_hex 0 1 237 0
88
void BitMask< uint16_t, 16 >::output_hex(std::ostream &out, int spaces_every = 4) const;

1209 5 write 0 4 1969 30 BitMask< uint16_t, 16 >::write 0 1 238 0
83
void BitMask< uint16_t, 16 >::write(std::ostream &out, int indent_level = 0) const;

1210 11 operator == 0 4 1969 36 BitMask< uint16_t, 16 >::operator == 0 1 239 0
93
inline bool BitMask< uint16_t, 16 >::operator ==(BitMask< uint16_t, 16 > const &other) const;

1211 11 operator != 0 4 1969 36 BitMask< uint16_t, 16 >::operator != 0 1 240 0
93
inline bool BitMask< uint16_t, 16 >::operator !=(BitMask< uint16_t, 16 > const &other) const;

1212 10 operator < 0 4 1969 35 BitMask< uint16_t, 16 >::operator < 0 1 241 0
92
inline bool BitMask< uint16_t, 16 >::operator <(BitMask< uint16_t, 16 > const &other) const;

1213 10 compare_to 0 4 1969 35 BitMask< uint16_t, 16 >::compare_to 0 1 242 0
91
inline int BitMask< uint16_t, 16 >::compare_to(BitMask< uint16_t, 16 > const &other) const;

1214 10 operator & 0 4 1969 35 BitMask< uint16_t, 16 >::operator & 0 1 243 0
111
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator &(BitMask< uint16_t, 16 > const &other) const;

1215 10 operator | 0 4 1969 35 BitMask< uint16_t, 16 >::operator | 0 1 244 0
111
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator |(BitMask< uint16_t, 16 > const &other) const;

1216 10 operator ^ 0 4 1969 35 BitMask< uint16_t, 16 >::operator ^ 0 1 245 0
111
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator ^(BitMask< uint16_t, 16 > const &other) const;

1217 10 operator ~ 0 68 1969 35 BitMask< uint16_t, 16 >::operator ~ 0 1 246 0
79
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator ~(void) const;

1218 11 operator << 0 4 1969 36 BitMask< uint16_t, 16 >::operator << 0 1 247 0
85
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator <<(int shift) const;

1219 11 operator >> 0 4 1969 36 BitMask< uint16_t, 16 >::operator >> 0 1 248 0
85
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::operator >>(int shift) const;

1220 11 operator &= 0 4 1969 36 BitMask< uint16_t, 16 >::operator &= 0 1 249 0
87
inline void BitMask< uint16_t, 16 >::operator &=(BitMask< uint16_t, 16 > const &other);

1221 11 operator |= 0 4 1969 36 BitMask< uint16_t, 16 >::operator |= 0 1 250 0
87
inline void BitMask< uint16_t, 16 >::operator |=(BitMask< uint16_t, 16 > const &other);

1222 11 operator ^= 0 4 1969 36 BitMask< uint16_t, 16 >::operator ^= 0 1 251 0
87
inline void BitMask< uint16_t, 16 >::operator ^=(BitMask< uint16_t, 16 > const &other);

1223 12 operator <<= 0 4 1969 37 BitMask< uint16_t, 16 >::operator <<= 0 1 252 0
61
inline void BitMask< uint16_t, 16 >::operator <<=(int shift);

1224 12 operator >>= 0 4 1969 37 BitMask< uint16_t, 16 >::operator >>= 0 1 253 0
61
inline void BitMask< uint16_t, 16 >::operator >>=(int shift);

1225 19 flood_down_in_place 0 4 1969 44 BitMask< uint16_t, 16 >::flood_down_in_place 0 1 254 0
63
inline void BitMask< uint16_t, 16 >::flood_down_in_place(void);

1226 17 flood_up_in_place 0 4 1969 42 BitMask< uint16_t, 16 >::flood_up_in_place 0 1 255 0
61
inline void BitMask< uint16_t, 16 >::flood_up_in_place(void);

1227 15 flood_bits_down 0 4 1969 40 BitMask< uint16_t, 16 >::flood_bits_down 0 1 256 0
84
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::flood_bits_down(void) const;

1228 13 flood_bits_up 0 4 1969 38 BitMask< uint16_t, 16 >::flood_bits_up 0 1 257 0
82
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::flood_bits_up(void) const;

1229 21 keep_next_highest_bit 0 4 1969 46 BitMask< uint16_t, 16 >::keep_next_highest_bit 0 3 258 259 260 0
309
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_highest_bit(void) const;
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_highest_bit(int index) const;
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_highest_bit(BitMask< uint16_t, 16 > const &other) const;

1230 20 keep_next_lowest_bit 0 4 1969 45 BitMask< uint16_t, 16 >::keep_next_lowest_bit 0 3 261 262 263 0
306
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_lowest_bit(void) const;
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_lowest_bit(int index) const;
inline BitMask< uint16_t, 16 > BitMask< uint16_t, 16 >::keep_next_lowest_bit(BitMask< uint16_t, 16 > const &other) const;

1231 7 get_key 0 4 1969 32 BitMask< uint16_t, 16 >::get_key 0 1 264 0
56
inline int BitMask< uint16_t, 16 >::get_key(void) const;

1232 11 __nonzero__ 0 4 1969 36 BitMask< uint16_t, 16 >::__nonzero__ 0 1 265 0
61
inline bool BitMask< uint16_t, 16 >::__nonzero__(void) const;

1233 10 __reduce__ 0 4 1969 35 BitMask< uint16_t, 16 >::__reduce__ 0 1 266 0
68
PyObject *BitMask< uint16_t, 16 >::__reduce__(PyObject *self) const;

1234 14 get_class_type 0 4 1969 39 BitMask< uint16_t, 16 >::get_class_type 0 1 267 0
64
static TypeHandle BitMask< uint16_t, 16 >::get_class_type(void);

1235 8 ~BitMask 0 516 1969 33 BitMask< uint16_t, 16 >::~BitMask 0 0 0
40
BitMask< uint16_t, 16 >::~BitMask(void);

1236 7 BitMask 0 260 1971 32 BitMask< uint32_t, 32 >::BitMask 0 3 268 269 270 0
215
constexpr BitMask< uint32_t, 32 >::BitMask(void) = default;
inline constexpr BitMask< uint32_t, 32 >::BitMask(uint32_t init_value);
inline BitMask< uint32_t, 32 >::BitMask(BitMask< uint32_t, 32 > const &) = default;

1237 6 all_on 0 4 1971 31 BitMask< uint32_t, 32 >::all_on 0 1 271 0
76
static inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::all_on(void);

1238 7 all_off 0 4 1971 32 BitMask< uint32_t, 32 >::all_off 0 1 272 0
77
static inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::all_off(void);

1239 8 lower_on 0 4 1971 33 BitMask< uint32_t, 32 >::lower_on 0 1 273 0
85
static inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::lower_on(int on_bits);

1240 3 bit 0 4 1971 28 BitMask< uint32_t, 32 >::bit 0 1 274 0
78
static inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::bit(int index);

1241 5 range 0 4 1971 30 BitMask< uint32_t, 32 >::range 0 1 275 0
92
static inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::range(int low_bit, int size);

1242 16 has_max_num_bits 0 4 1971 41 BitMask< uint32_t, 32 >::has_max_num_bits 0 1 276 0
70
static constexpr bool BitMask< uint32_t, 32 >::has_max_num_bits(void);

1243 16 get_max_num_bits 0 4 1971 41 BitMask< uint32_t, 32 >::get_max_num_bits 0 1 277 0
69
static constexpr int BitMask< uint32_t, 32 >::get_max_num_bits(void);

1244 12 get_num_bits 0 4 1971 37 BitMask< uint32_t, 32 >::get_num_bits 0 1 278 0
64
constexpr int BitMask< uint32_t, 32 >::get_num_bits(void) const;

1245 7 get_bit 0 4 1971 32 BitMask< uint32_t, 32 >::get_bit 0 1 279 0
62
inline bool BitMask< uint32_t, 32 >::get_bit(int index) const;

1246 7 set_bit 0 4 1971 32 BitMask< uint32_t, 32 >::set_bit 0 1 280 0
56
inline void BitMask< uint32_t, 32 >::set_bit(int index);

1247 9 clear_bit 0 4 1971 34 BitMask< uint32_t, 32 >::clear_bit 0 1 281 0
58
inline void BitMask< uint32_t, 32 >::clear_bit(int index);

1248 10 set_bit_to 0 4 1971 35 BitMask< uint32_t, 32 >::set_bit_to 0 1 282 0
71
inline void BitMask< uint32_t, 32 >::set_bit_to(int index, bool value);

1249 7 is_zero 0 4 1971 32 BitMask< uint32_t, 32 >::is_zero 0 1 283 0
57
inline bool BitMask< uint32_t, 32 >::is_zero(void) const;

1250 9 is_all_on 0 4 1971 34 BitMask< uint32_t, 32 >::is_all_on 0 1 284 0
59
inline bool BitMask< uint32_t, 32 >::is_all_on(void) const;

1251 7 extract 0 4 1971 32 BitMask< uint32_t, 32 >::extract 0 1 285 0
78
inline uint32_t BitMask< uint32_t, 32 >::extract(int low_bit, int size) const;

1252 5 store 0 4 1971 30 BitMask< uint32_t, 32 >::store 0 1 286 0
82
inline void BitMask< uint32_t, 32 >::store(uint32_t value, int low_bit, int size);

1253 10 has_any_of 0 4 1971 35 BitMask< uint32_t, 32 >::has_any_of 0 1 287 0
77
inline bool BitMask< uint32_t, 32 >::has_any_of(int low_bit, int size) const;

1254 10 has_all_of 0 4 1971 35 BitMask< uint32_t, 32 >::has_all_of 0 1 288 0
77
inline bool BitMask< uint32_t, 32 >::has_all_of(int low_bit, int size) const;

1255 9 set_range 0 4 1971 34 BitMask< uint32_t, 32 >::set_range 0 1 289 0
70
inline void BitMask< uint32_t, 32 >::set_range(int low_bit, int size);

1256 11 clear_range 0 4 1971 36 BitMask< uint32_t, 32 >::clear_range 0 1 290 0
72
inline void BitMask< uint32_t, 32 >::clear_range(int low_bit, int size);

1257 12 set_range_to 0 4 1971 37 BitMask< uint32_t, 32 >::set_range_to 0 1 291 0
85
inline void BitMask< uint32_t, 32 >::set_range_to(bool value, int low_bit, int size);

1258 8 get_word 0 4 1971 33 BitMask< uint32_t, 32 >::get_word 0 1 292 0
62
inline uint32_t BitMask< uint32_t, 32 >::get_word(void) const;

1259 8 set_word 0 4 1971 33 BitMask< uint32_t, 32 >::set_word 0 1 293 0
62
inline void BitMask< uint32_t, 32 >::set_word(uint32_t value);

1260 15 get_num_on_bits 0 4 1971 40 BitMask< uint32_t, 32 >::get_num_on_bits 0 1 294 0
64
inline int BitMask< uint32_t, 32 >::get_num_on_bits(void) const;

1261 16 get_num_off_bits 0 4 1971 41 BitMask< uint32_t, 32 >::get_num_off_bits 0 1 295 0
65
inline int BitMask< uint32_t, 32 >::get_num_off_bits(void) const;

1262 17 get_lowest_on_bit 0 4 1971 42 BitMask< uint32_t, 32 >::get_lowest_on_bit 0 1 296 0
66
inline int BitMask< uint32_t, 32 >::get_lowest_on_bit(void) const;

1263 18 get_lowest_off_bit 0 4 1971 43 BitMask< uint32_t, 32 >::get_lowest_off_bit 0 1 297 0
67
inline int BitMask< uint32_t, 32 >::get_lowest_off_bit(void) const;

1264 18 get_highest_on_bit 0 4 1971 43 BitMask< uint32_t, 32 >::get_highest_on_bit 0 1 298 0
67
inline int BitMask< uint32_t, 32 >::get_highest_on_bit(void) const;

1265 19 get_highest_off_bit 0 4 1971 44 BitMask< uint32_t, 32 >::get_highest_off_bit 0 1 299 0
68
inline int BitMask< uint32_t, 32 >::get_highest_off_bit(void) const;

1266 29 get_next_higher_different_bit 0 4 1971 54 BitMask< uint32_t, 32 >::get_next_higher_different_bit 0 1 300 0
85
inline int BitMask< uint32_t, 32 >::get_next_higher_different_bit(int low_bit) const;

1267 15 invert_in_place 0 4 1971 40 BitMask< uint32_t, 32 >::invert_in_place 0 1 301 0
59
inline void BitMask< uint32_t, 32 >::invert_in_place(void);

1268 18 has_bits_in_common 0 4 1971 43 BitMask< uint32_t, 32 >::has_bits_in_common 0 1 302 0
100
inline bool BitMask< uint32_t, 32 >::has_bits_in_common(BitMask< uint32_t, 32 > const &other) const;

1269 5 clear 0 4 1971 30 BitMask< uint32_t, 32 >::clear 0 1 303 0
49
inline void BitMask< uint32_t, 32 >::clear(void);

1270 6 output 0 4 1971 31 BitMask< uint32_t, 32 >::output 0 1 304 0
62
void BitMask< uint32_t, 32 >::output(std::ostream &out) const;

1271 13 output_binary 0 4 1971 38 BitMask< uint32_t, 32 >::output_binary 0 1 305 0
91
void BitMask< uint32_t, 32 >::output_binary(std::ostream &out, int spaces_every = 4) const;

1272 10 output_hex 0 4 1971 35 BitMask< uint32_t, 32 >::output_hex 0 1 306 0
88
void BitMask< uint32_t, 32 >::output_hex(std::ostream &out, int spaces_every = 4) const;

1273 5 write 0 4 1971 30 BitMask< uint32_t, 32 >::write 0 1 307 0
83
void BitMask< uint32_t, 32 >::write(std::ostream &out, int indent_level = 0) const;

1274 11 operator == 0 4 1971 36 BitMask< uint32_t, 32 >::operator == 0 1 308 0
93
inline bool BitMask< uint32_t, 32 >::operator ==(BitMask< uint32_t, 32 > const &other) const;

1275 11 operator != 0 4 1971 36 BitMask< uint32_t, 32 >::operator != 0 1 309 0
93
inline bool BitMask< uint32_t, 32 >::operator !=(BitMask< uint32_t, 32 > const &other) const;

1276 10 operator < 0 4 1971 35 BitMask< uint32_t, 32 >::operator < 0 1 310 0
92
inline bool BitMask< uint32_t, 32 >::operator <(BitMask< uint32_t, 32 > const &other) const;

1277 10 compare_to 0 4 1971 35 BitMask< uint32_t, 32 >::compare_to 0 1 311 0
91
inline int BitMask< uint32_t, 32 >::compare_to(BitMask< uint32_t, 32 > const &other) const;

1278 10 operator & 0 4 1971 35 BitMask< uint32_t, 32 >::operator & 0 1 312 0
111
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator &(BitMask< uint32_t, 32 > const &other) const;

1279 10 operator | 0 4 1971 35 BitMask< uint32_t, 32 >::operator | 0 1 313 0
111
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator |(BitMask< uint32_t, 32 > const &other) const;

1280 10 operator ^ 0 4 1971 35 BitMask< uint32_t, 32 >::operator ^ 0 1 314 0
111
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator ^(BitMask< uint32_t, 32 > const &other) const;

1281 10 operator ~ 0 68 1971 35 BitMask< uint32_t, 32 >::operator ~ 0 1 315 0
79
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator ~(void) const;

1282 11 operator << 0 4 1971 36 BitMask< uint32_t, 32 >::operator << 0 1 316 0
85
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator <<(int shift) const;

1283 11 operator >> 0 4 1971 36 BitMask< uint32_t, 32 >::operator >> 0 1 317 0
85
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::operator >>(int shift) const;

1284 11 operator &= 0 4 1971 36 BitMask< uint32_t, 32 >::operator &= 0 1 318 0
87
inline void BitMask< uint32_t, 32 >::operator &=(BitMask< uint32_t, 32 > const &other);

1285 11 operator |= 0 4 1971 36 BitMask< uint32_t, 32 >::operator |= 0 1 319 0
87
inline void BitMask< uint32_t, 32 >::operator |=(BitMask< uint32_t, 32 > const &other);

1286 11 operator ^= 0 4 1971 36 BitMask< uint32_t, 32 >::operator ^= 0 1 320 0
87
inline void BitMask< uint32_t, 32 >::operator ^=(BitMask< uint32_t, 32 > const &other);

1287 12 operator <<= 0 4 1971 37 BitMask< uint32_t, 32 >::operator <<= 0 1 321 0
61
inline void BitMask< uint32_t, 32 >::operator <<=(int shift);

1288 12 operator >>= 0 4 1971 37 BitMask< uint32_t, 32 >::operator >>= 0 1 322 0
61
inline void BitMask< uint32_t, 32 >::operator >>=(int shift);

1289 19 flood_down_in_place 0 4 1971 44 BitMask< uint32_t, 32 >::flood_down_in_place 0 1 323 0
63
inline void BitMask< uint32_t, 32 >::flood_down_in_place(void);

1290 17 flood_up_in_place 0 4 1971 42 BitMask< uint32_t, 32 >::flood_up_in_place 0 1 324 0
61
inline void BitMask< uint32_t, 32 >::flood_up_in_place(void);

1291 15 flood_bits_down 0 4 1971 40 BitMask< uint32_t, 32 >::flood_bits_down 0 1 325 0
84
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::flood_bits_down(void) const;

1292 13 flood_bits_up 0 4 1971 38 BitMask< uint32_t, 32 >::flood_bits_up 0 1 326 0
82
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::flood_bits_up(void) const;

1293 21 keep_next_highest_bit 0 4 1971 46 BitMask< uint32_t, 32 >::keep_next_highest_bit 0 3 327 328 329 0
309
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_highest_bit(void) const;
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_highest_bit(int index) const;
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_highest_bit(BitMask< uint32_t, 32 > const &other) const;

1294 20 keep_next_lowest_bit 0 4 1971 45 BitMask< uint32_t, 32 >::keep_next_lowest_bit 0 3 330 331 332 0
306
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_lowest_bit(void) const;
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_lowest_bit(int index) const;
inline BitMask< uint32_t, 32 > BitMask< uint32_t, 32 >::keep_next_lowest_bit(BitMask< uint32_t, 32 > const &other) const;

1295 7 get_key 0 4 1971 32 BitMask< uint32_t, 32 >::get_key 0 1 333 0
56
inline int BitMask< uint32_t, 32 >::get_key(void) const;

1296 11 __nonzero__ 0 4 1971 36 BitMask< uint32_t, 32 >::__nonzero__ 0 1 334 0
61
inline bool BitMask< uint32_t, 32 >::__nonzero__(void) const;

1297 10 __reduce__ 0 4 1971 35 BitMask< uint32_t, 32 >::__reduce__ 0 1 335 0
68
PyObject *BitMask< uint32_t, 32 >::__reduce__(PyObject *self) const;

1298 14 get_class_type 0 4 1971 39 BitMask< uint32_t, 32 >::get_class_type 0 1 336 0
64
static TypeHandle BitMask< uint32_t, 32 >::get_class_type(void);

1299 8 ~BitMask 0 516 1971 33 BitMask< uint32_t, 32 >::~BitMask 0 0 0
40
BitMask< uint32_t, 32 >::~BitMask(void);

1300 7 BitMask 0 260 1973 32 BitMask< uint64_t, 64 >::BitMask 0 3 337 338 339 0
215
constexpr BitMask< uint64_t, 64 >::BitMask(void) = default;
inline constexpr BitMask< uint64_t, 64 >::BitMask(uint64_t init_value);
inline BitMask< uint64_t, 64 >::BitMask(BitMask< uint64_t, 64 > const &) = default;

1301 6 all_on 0 4 1973 31 BitMask< uint64_t, 64 >::all_on 0 1 340 0
76
static inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::all_on(void);

1302 7 all_off 0 4 1973 32 BitMask< uint64_t, 64 >::all_off 0 1 341 0
77
static inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::all_off(void);

1303 8 lower_on 0 4 1973 33 BitMask< uint64_t, 64 >::lower_on 0 1 342 0
85
static inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::lower_on(int on_bits);

1304 3 bit 0 4 1973 28 BitMask< uint64_t, 64 >::bit 0 1 343 0
78
static inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::bit(int index);

1305 5 range 0 4 1973 30 BitMask< uint64_t, 64 >::range 0 1 344 0
92
static inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::range(int low_bit, int size);

1306 16 has_max_num_bits 0 4 1973 41 BitMask< uint64_t, 64 >::has_max_num_bits 0 1 345 0
70
static constexpr bool BitMask< uint64_t, 64 >::has_max_num_bits(void);

1307 16 get_max_num_bits 0 4 1973 41 BitMask< uint64_t, 64 >::get_max_num_bits 0 1 346 0
69
static constexpr int BitMask< uint64_t, 64 >::get_max_num_bits(void);

1308 12 get_num_bits 0 4 1973 37 BitMask< uint64_t, 64 >::get_num_bits 0 1 347 0
64
constexpr int BitMask< uint64_t, 64 >::get_num_bits(void) const;

1309 7 get_bit 0 4 1973 32 BitMask< uint64_t, 64 >::get_bit 0 1 348 0
62
inline bool BitMask< uint64_t, 64 >::get_bit(int index) const;

1310 7 set_bit 0 4 1973 32 BitMask< uint64_t, 64 >::set_bit 0 1 349 0
56
inline void BitMask< uint64_t, 64 >::set_bit(int index);

1311 9 clear_bit 0 4 1973 34 BitMask< uint64_t, 64 >::clear_bit 0 1 350 0
58
inline void BitMask< uint64_t, 64 >::clear_bit(int index);

1312 10 set_bit_to 0 4 1973 35 BitMask< uint64_t, 64 >::set_bit_to 0 1 351 0
71
inline void BitMask< uint64_t, 64 >::set_bit_to(int index, bool value);

1313 7 is_zero 0 4 1973 32 BitMask< uint64_t, 64 >::is_zero 0 1 352 0
57
inline bool BitMask< uint64_t, 64 >::is_zero(void) const;

1314 9 is_all_on 0 4 1973 34 BitMask< uint64_t, 64 >::is_all_on 0 1 353 0
59
inline bool BitMask< uint64_t, 64 >::is_all_on(void) const;

1315 7 extract 0 4 1973 32 BitMask< uint64_t, 64 >::extract 0 1 354 0
78
inline uint64_t BitMask< uint64_t, 64 >::extract(int low_bit, int size) const;

1316 5 store 0 4 1973 30 BitMask< uint64_t, 64 >::store 0 1 355 0
82
inline void BitMask< uint64_t, 64 >::store(uint64_t value, int low_bit, int size);

1317 10 has_any_of 0 4 1973 35 BitMask< uint64_t, 64 >::has_any_of 0 1 356 0
77
inline bool BitMask< uint64_t, 64 >::has_any_of(int low_bit, int size) const;

1318 10 has_all_of 0 4 1973 35 BitMask< uint64_t, 64 >::has_all_of 0 1 357 0
77
inline bool BitMask< uint64_t, 64 >::has_all_of(int low_bit, int size) const;

1319 9 set_range 0 4 1973 34 BitMask< uint64_t, 64 >::set_range 0 1 358 0
70
inline void BitMask< uint64_t, 64 >::set_range(int low_bit, int size);

1320 11 clear_range 0 4 1973 36 BitMask< uint64_t, 64 >::clear_range 0 1 359 0
72
inline void BitMask< uint64_t, 64 >::clear_range(int low_bit, int size);

1321 12 set_range_to 0 4 1973 37 BitMask< uint64_t, 64 >::set_range_to 0 1 360 0
85
inline void BitMask< uint64_t, 64 >::set_range_to(bool value, int low_bit, int size);

1322 8 get_word 0 4 1973 33 BitMask< uint64_t, 64 >::get_word 0 1 361 0
62
inline uint64_t BitMask< uint64_t, 64 >::get_word(void) const;

1323 8 set_word 0 4 1973 33 BitMask< uint64_t, 64 >::set_word 0 1 362 0
62
inline void BitMask< uint64_t, 64 >::set_word(uint64_t value);

1324 15 get_num_on_bits 0 4 1973 40 BitMask< uint64_t, 64 >::get_num_on_bits 0 1 363 0
64
inline int BitMask< uint64_t, 64 >::get_num_on_bits(void) const;

1325 16 get_num_off_bits 0 4 1973 41 BitMask< uint64_t, 64 >::get_num_off_bits 0 1 364 0
65
inline int BitMask< uint64_t, 64 >::get_num_off_bits(void) const;

1326 17 get_lowest_on_bit 0 4 1973 42 BitMask< uint64_t, 64 >::get_lowest_on_bit 0 1 365 0
66
inline int BitMask< uint64_t, 64 >::get_lowest_on_bit(void) const;

1327 18 get_lowest_off_bit 0 4 1973 43 BitMask< uint64_t, 64 >::get_lowest_off_bit 0 1 366 0
67
inline int BitMask< uint64_t, 64 >::get_lowest_off_bit(void) const;

1328 18 get_highest_on_bit 0 4 1973 43 BitMask< uint64_t, 64 >::get_highest_on_bit 0 1 367 0
67
inline int BitMask< uint64_t, 64 >::get_highest_on_bit(void) const;

1329 19 get_highest_off_bit 0 4 1973 44 BitMask< uint64_t, 64 >::get_highest_off_bit 0 1 368 0
68
inline int BitMask< uint64_t, 64 >::get_highest_off_bit(void) const;

1330 29 get_next_higher_different_bit 0 4 1973 54 BitMask< uint64_t, 64 >::get_next_higher_different_bit 0 1 369 0
85
inline int BitMask< uint64_t, 64 >::get_next_higher_different_bit(int low_bit) const;

1331 15 invert_in_place 0 4 1973 40 BitMask< uint64_t, 64 >::invert_in_place 0 1 370 0
59
inline void BitMask< uint64_t, 64 >::invert_in_place(void);

1332 18 has_bits_in_common 0 4 1973 43 BitMask< uint64_t, 64 >::has_bits_in_common 0 1 371 0
100
inline bool BitMask< uint64_t, 64 >::has_bits_in_common(BitMask< uint64_t, 64 > const &other) const;

1333 5 clear 0 4 1973 30 BitMask< uint64_t, 64 >::clear 0 1 372 0
49
inline void BitMask< uint64_t, 64 >::clear(void);

1334 6 output 0 4 1973 31 BitMask< uint64_t, 64 >::output 0 1 373 0
62
void BitMask< uint64_t, 64 >::output(std::ostream &out) const;

1335 13 output_binary 0 4 1973 38 BitMask< uint64_t, 64 >::output_binary 0 1 374 0
91
void BitMask< uint64_t, 64 >::output_binary(std::ostream &out, int spaces_every = 4) const;

1336 10 output_hex 0 4 1973 35 BitMask< uint64_t, 64 >::output_hex 0 1 375 0
88
void BitMask< uint64_t, 64 >::output_hex(std::ostream &out, int spaces_every = 4) const;

1337 5 write 0 4 1973 30 BitMask< uint64_t, 64 >::write 0 1 376 0
83
void BitMask< uint64_t, 64 >::write(std::ostream &out, int indent_level = 0) const;

1338 11 operator == 0 4 1973 36 BitMask< uint64_t, 64 >::operator == 0 1 377 0
93
inline bool BitMask< uint64_t, 64 >::operator ==(BitMask< uint64_t, 64 > const &other) const;

1339 11 operator != 0 4 1973 36 BitMask< uint64_t, 64 >::operator != 0 1 378 0
93
inline bool BitMask< uint64_t, 64 >::operator !=(BitMask< uint64_t, 64 > const &other) const;

1340 10 operator < 0 4 1973 35 BitMask< uint64_t, 64 >::operator < 0 1 379 0
92
inline bool BitMask< uint64_t, 64 >::operator <(BitMask< uint64_t, 64 > const &other) const;

1341 10 compare_to 0 4 1973 35 BitMask< uint64_t, 64 >::compare_to 0 1 380 0
91
inline int BitMask< uint64_t, 64 >::compare_to(BitMask< uint64_t, 64 > const &other) const;

1342 10 operator & 0 4 1973 35 BitMask< uint64_t, 64 >::operator & 0 1 381 0
111
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator &(BitMask< uint64_t, 64 > const &other) const;

1343 10 operator | 0 4 1973 35 BitMask< uint64_t, 64 >::operator | 0 1 382 0
111
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator |(BitMask< uint64_t, 64 > const &other) const;

1344 10 operator ^ 0 4 1973 35 BitMask< uint64_t, 64 >::operator ^ 0 1 383 0
111
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator ^(BitMask< uint64_t, 64 > const &other) const;

1345 10 operator ~ 0 68 1973 35 BitMask< uint64_t, 64 >::operator ~ 0 1 384 0
79
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator ~(void) const;

1346 11 operator << 0 4 1973 36 BitMask< uint64_t, 64 >::operator << 0 1 385 0
85
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator <<(int shift) const;

1347 11 operator >> 0 4 1973 36 BitMask< uint64_t, 64 >::operator >> 0 1 386 0
85
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::operator >>(int shift) const;

1348 11 operator &= 0 4 1973 36 BitMask< uint64_t, 64 >::operator &= 0 1 387 0
87
inline void BitMask< uint64_t, 64 >::operator &=(BitMask< uint64_t, 64 > const &other);

1349 11 operator |= 0 4 1973 36 BitMask< uint64_t, 64 >::operator |= 0 1 388 0
87
inline void BitMask< uint64_t, 64 >::operator |=(BitMask< uint64_t, 64 > const &other);

1350 11 operator ^= 0 4 1973 36 BitMask< uint64_t, 64 >::operator ^= 0 1 389 0
87
inline void BitMask< uint64_t, 64 >::operator ^=(BitMask< uint64_t, 64 > const &other);

1351 12 operator <<= 0 4 1973 37 BitMask< uint64_t, 64 >::operator <<= 0 1 390 0
61
inline void BitMask< uint64_t, 64 >::operator <<=(int shift);

1352 12 operator >>= 0 4 1973 37 BitMask< uint64_t, 64 >::operator >>= 0 1 391 0
61
inline void BitMask< uint64_t, 64 >::operator >>=(int shift);

1353 19 flood_down_in_place 0 4 1973 44 BitMask< uint64_t, 64 >::flood_down_in_place 0 1 392 0
63
inline void BitMask< uint64_t, 64 >::flood_down_in_place(void);

1354 17 flood_up_in_place 0 4 1973 42 BitMask< uint64_t, 64 >::flood_up_in_place 0 1 393 0
61
inline void BitMask< uint64_t, 64 >::flood_up_in_place(void);

1355 15 flood_bits_down 0 4 1973 40 BitMask< uint64_t, 64 >::flood_bits_down 0 1 394 0
84
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::flood_bits_down(void) const;

1356 13 flood_bits_up 0 4 1973 38 BitMask< uint64_t, 64 >::flood_bits_up 0 1 395 0
82
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::flood_bits_up(void) const;

1357 21 keep_next_highest_bit 0 4 1973 46 BitMask< uint64_t, 64 >::keep_next_highest_bit 0 3 396 397 398 0
309
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_highest_bit(void) const;
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_highest_bit(int index) const;
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_highest_bit(BitMask< uint64_t, 64 > const &other) const;

1358 20 keep_next_lowest_bit 0 4 1973 45 BitMask< uint64_t, 64 >::keep_next_lowest_bit 0 3 399 400 401 0
306
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_lowest_bit(void) const;
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_lowest_bit(int index) const;
inline BitMask< uint64_t, 64 > BitMask< uint64_t, 64 >::keep_next_lowest_bit(BitMask< uint64_t, 64 > const &other) const;

1359 7 get_key 0 4 1973 32 BitMask< uint64_t, 64 >::get_key 0 1 402 0
56
inline int BitMask< uint64_t, 64 >::get_key(void) const;

1360 11 __nonzero__ 0 4 1973 36 BitMask< uint64_t, 64 >::__nonzero__ 0 1 403 0
61
inline bool BitMask< uint64_t, 64 >::__nonzero__(void) const;

1361 10 __reduce__ 0 4 1973 35 BitMask< uint64_t, 64 >::__reduce__ 0 1 404 0
68
PyObject *BitMask< uint64_t, 64 >::__reduce__(PyObject *self) const;

1362 14 get_class_type 0 4 1973 39 BitMask< uint64_t, 64 >::get_class_type 0 1 405 0
64
static TypeHandle BitMask< uint64_t, 64 >::get_class_type(void);

1363 8 ~BitMask 0 516 1973 33 BitMask< uint64_t, 64 >::~BitMask 0 0 0
40
BitMask< uint64_t, 64 >::~BitMask(void);

1364 8 BitArray 0 260 1976 18 BitArray::BitArray 0 4 406 407 408 409 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
190
inline BitArray::BitArray(void);
inline BitArray::BitArray(BitArray::WordType init_value);
BitArray::BitArray(SparseArray const &from);
inline BitArray::BitArray(BitArray const &) = default;

1365 6 all_on 0 4 1976 16 BitArray::all_on 0 1 410 69
/**
 * Returns a BitArray with an infinite array of bits, all on.
 */
46
static inline BitArray BitArray::all_on(void);

1366 7 all_off 0 4 1976 17 BitArray::all_off 0 1 411 53
/**
 * Returns a BitArray whose bits are all off.
 */
47
static inline BitArray BitArray::all_off(void);

1367 8 lower_on 0 4 1976 18 BitArray::lower_on 0 1 412 62
/**
 * Returns a BitArray whose lower on_bits bits are on.
 */
55
static inline BitArray BitArray::lower_on(int on_bits);

1368 3 bit 0 4 1976 13 BitArray::bit 0 1 413 61
/**
 * Returns a BitArray with only the indicated bit on.
 */
48
static inline BitArray BitArray::bit(int index);

1369 5 range 0 4 1976 15 BitArray::range 0 1 414 76
/**
 * Returns a BitArray whose size bits, beginning at low_bit, are on.
 */
62
static inline BitArray BitArray::range(int low_bit, int size);

1370 16 has_max_num_bits 0 4 1976 26 BitArray::has_max_num_bits 0 1 415 0
55
static constexpr bool BitArray::has_max_num_bits(void);

1371 16 get_max_num_bits 0 4 1976 26 BitArray::get_max_num_bits 0 1 416 0
54
static constexpr int BitArray::get_max_num_bits(void);

1372 21 get_num_bits_per_word 0 4 1976 31 BitArray::get_num_bits_per_word 0 1 417 0
59
static constexpr int BitArray::get_num_bits_per_word(void);

1373 12 get_num_bits 0 4 1976 22 BitArray::get_num_bits 0 1 418 300
/**
 * Returns the current number of possibly different bits in this array.  There
 * are actually an infinite number of bits, but every bit higher than this bit
 * will have the same value, either 0 or 1 (see get_highest_bits()).
 *
 * This number may grow and/or shrink automatically as needed.
 */
54
inline std::size_t BitArray::get_num_bits(void) const;

1374 7 get_bit 0 4 1976 17 BitArray::get_bit 0 1 419 191
/**
 * Returns true if the nth bit is set, false if it is cleared.  It is valid
 * for n to increase beyond get_num_bits(), but the return value
 * get_num_bits() will always be the same.
 */
47
inline bool BitArray::get_bit(int index) const;

1375 7 set_bit 0 4 1976 17 BitArray::set_bit 0 1 420 97
/**
 * Sets the nth bit on.  If n >= get_num_bits(), this automatically extends
 * the array.
 */
41
inline void BitArray::set_bit(int index);

1376 9 clear_bit 0 4 1976 19 BitArray::clear_bit 0 1 421 98
/**
 * Sets the nth bit off.  If n >= get_num_bits(), this automatically extends
 * the array.
 */
43
inline void BitArray::clear_bit(int index);

1377 10 set_bit_to 0 4 1976 20 BitArray::set_bit_to 0 1 422 84
/**
 * Sets the nth bit either on or off, according to the indicated bool value.
 */
56
inline void BitArray::set_bit_to(int index, bool value);

1378 16 get_highest_bits 0 4 1976 26 BitArray::get_highest_bits 0 1 423 118
/**
 * Returns true if the infinite set of bits beyond get_num_bits() are all on,
 * or false of they are all off.
 */
51
inline bool BitArray::get_highest_bits(void) const;

1379 7 is_zero 0 4 1976 17 BitArray::is_zero 0 1 424 71
/**
 * Returns true if the entire bitmask is zero, false otherwise.
 */
35
bool BitArray::is_zero(void) const;

1380 9 is_all_on 0 4 1976 19 BitArray::is_all_on 0 1 425 70
/**
 * Returns true if the entire bitmask is one, false otherwise.
 */
37
bool BitArray::is_all_on(void) const;

1381 7 extract 0 4 1976 17 BitArray::extract 0 1 426 187
/**
 * Returns a word that represents only the indicated range of bits within this
 * BitArray, shifted to the least-significant position.  size must be <=
 * get_num_bits_per_word().
 */
73
inline BitArray::WordType BitArray::extract(int low_bit, int size) const;

1382 5 store 0 4 1976 15 BitArray::store 0 1 427 92
/**
 * Stores the indicated word into the indicated range of bits with this
 * BitArray.
 */
77
inline void BitArray::store(BitArray::WordType value, int low_bit, int size);

1383 10 has_any_of 0 4 1976 20 BitArray::has_any_of 0 1 428 82
/**
 * Returns true if any bit in the indicated range is set, false otherwise.
 */
55
bool BitArray::has_any_of(int low_bit, int size) const;

1384 10 has_all_of 0 4 1976 20 BitArray::has_all_of 0 1 429 84
/**
 * Returns true if all bits in the indicated range are set, false otherwise.
 */
55
bool BitArray::has_all_of(int low_bit, int size) const;

1385 9 set_range 0 4 1976 19 BitArray::set_range 0 1 430 47
/**
 * Sets the indicated range of bits on.
 */
48
void BitArray::set_range(int low_bit, int size);

1386 11 clear_range 0 4 1976 21 BitArray::clear_range 0 1 431 48
/**
 * Sets the indicated range of bits off.
 */
50
void BitArray::clear_range(int low_bit, int size);

1387 12 set_range_to 0 4 1976 22 BitArray::set_range_to 0 1 432 64
/**
 * Sets the indicated range of bits to either on or off.
 */
70
inline void BitArray::set_range_to(bool value, int low_bit, int size);

1388 15 get_num_on_bits 0 4 1976 25 BitArray::get_num_on_bits 0 1 433 127
/**
 * Returns the number of bits that are set to 1 in the array.  Returns -1 if
 * there are an infinite number of 1 bits.
 */
42
int BitArray::get_num_on_bits(void) const;

1389 16 get_num_off_bits 0 4 1976 26 BitArray::get_num_off_bits 0 1 434 127
/**
 * Returns the number of bits that are set to 0 in the array.  Returns -1 if
 * there are an infinite number of 0 bits.
 */
43
int BitArray::get_num_off_bits(void) const;

1390 17 get_lowest_on_bit 0 4 1976 27 BitArray::get_lowest_on_bit 0 1 435 101
/**
 * Returns the index of the lowest 1 bit in the array.  Returns -1 if there
 * are no 1 bits.
 */
44
int BitArray::get_lowest_on_bit(void) const;

1391 18 get_lowest_off_bit 0 4 1976 28 BitArray::get_lowest_off_bit 0 1 436 101
/**
 * Returns the index of the lowest 0 bit in the array.  Returns -1 if there
 * are no 0 bits.
 */
45
int BitArray::get_lowest_off_bit(void) const;

1392 18 get_highest_on_bit 0 4 1976 28 BitArray::get_highest_on_bit 0 1 437 143
/**
 * Returns the index of the highest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there an infinite number of 1 bits.
 */
45
int BitArray::get_highest_on_bit(void) const;

1393 19 get_highest_off_bit 0 4 1976 29 BitArray::get_highest_off_bit 0 1 438 143
/**
 * Returns the index of the highest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there an infinite number of 1 bits.
 */
46
int BitArray::get_highest_off_bit(void) const;

1394 29 get_next_higher_different_bit 0 4 1976 39 BitArray::get_next_higher_different_bit 0 1 439 287
/**
 * Returns the index of the next bit in the array, above low_bit, whose value
 * is different that the value of low_bit.  Returns low_bit again if all bits
 * higher than low_bit have the same value.
 *
 * This can be used to quickly iterate through all of the bits in the array.
 */
63
int BitArray::get_next_higher_different_bit(int low_bit) const;

1395 13 get_num_words 0 4 1976 23 BitArray::get_num_words 0 1 440 75
/**
 * Returns the number of possibly-unique words stored in the array.
 */
55
inline std::size_t BitArray::get_num_words(void) const;

1396 8 get_word 0 4 1976 18 BitArray::get_word 0 1 441 175
/**
 * Returns the nth word in the array.  It is valid for n to be greater than
 * get_num_words(), but the return value beyond get_num_words() will always be
 * the same.
 */
66
inline BitArray::MaskType BitArray::get_word(std::size_t n) const;

1397 8 set_word 0 4 1976 18 BitArray::set_word 0 1 442 113
/**
 * Replaces the nth word in the array.  If n >= get_num_words(), this
 * automatically extends the array.
 */
72
inline void BitArray::set_word(std::size_t n, BitArray::WordType value);

1398 15 invert_in_place 0 4 1976 25 BitArray::invert_in_place 0 1 443 90
/**
 * Inverts all the bits in the BitArray.  This is equivalent to array =
 * ~array.
 */
37
void BitArray::invert_in_place(void);

1399 18 has_bits_in_common 0 4 1976 28 BitArray::has_bits_in_common 0 1 444 178
/**
 * Returns true if this BitArray has any "one" bits in common with the other
 * one, false otherwise.
 *
 * This is equivalent to (array & other) != 0, but may be faster.
 */
63
bool BitArray::has_bits_in_common(BitArray const &other) const;

1400 5 clear 0 4 1976 15 BitArray::clear 0 1 445 49
/**
 * Sets all the bits in the BitArray off.
 */
34
inline void BitArray::clear(void);

1401 6 output 0 4 1976 16 BitArray::output 0 1 446 193
/**
 * Writes the BitArray out as a hex number.  For a BitArray, this is always
 * the same as output_hex(); it's too confusing for the output format to
 * change back and forth at runtime.
 */
47
void BitArray::output(std::ostream &out) const;

1402 13 output_binary 0 4 1976 23 BitArray::output_binary 0 1 447 83
/**
 * Writes the BitArray out as a binary number, with spaces every four bits.
 */
76
void BitArray::output_binary(std::ostream &out, int spaces_every = 4) const;

1403 10 output_hex 0 4 1976 20 BitArray::output_hex 0 1 448 93
/**
 * Writes the BitArray out as a hexadecimal number, with spaces every four
 * digits.
 */
73
void BitArray::output_hex(std::ostream &out, int spaces_every = 4) const;

1404 5 write 0 4 1976 15 BitArray::write 0 1 449 99
/**
 * Writes the BitArray out as a binary or a hex number, according to the
 * number of bits.
 */
68
void BitArray::write(std::ostream &out, int indent_level = 0) const;

1405 11 operator == 0 4 1976 21 BitArray::operator == 0 1 450 0
63
inline bool BitArray::operator ==(BitArray const &other) const;

1406 11 operator != 0 4 1976 21 BitArray::operator != 0 1 451 0
63
inline bool BitArray::operator !=(BitArray const &other) const;

1407 10 operator < 0 4 1976 20 BitArray::operator < 0 1 452 0
62
inline bool BitArray::operator <(BitArray const &other) const;

1408 10 compare_to 0 4 1976 20 BitArray::compare_to 0 1 453 233
/**
 * Returns a number less than zero if this BitArray sorts before the indicated
 * other BitArray, greater than zero if it sorts after, or 0 if they are
 * equivalent.  This is based on the same ordering defined by operator <.
 */
54
int BitArray::compare_to(BitArray const &other) const;

1409 10 operator & 0 4 1976 20 BitArray::operator & 0 1 454 0
66
inline BitArray BitArray::operator &(BitArray const &other) const;

1410 10 operator | 0 4 1976 20 BitArray::operator | 0 1 455 0
66
inline BitArray BitArray::operator |(BitArray const &other) const;

1411 10 operator ^ 0 4 1976 20 BitArray::operator ^ 0 1 456 0
66
inline BitArray BitArray::operator ^(BitArray const &other) const;

1412 10 operator ~ 0 68 1976 20 BitArray::operator ~ 0 1 457 0
49
inline BitArray BitArray::operator ~(void) const;

1413 11 operator << 0 4 1976 21 BitArray::operator << 0 1 458 0
55
inline BitArray BitArray::operator <<(int shift) const;

1414 11 operator >> 0 4 1976 21 BitArray::operator >> 0 1 459 0
55
inline BitArray BitArray::operator >>(int shift) const;

1415 11 operator &= 0 4 1976 21 BitArray::operator &= 0 1 460 0
50
void BitArray::operator &=(BitArray const &other);

1416 11 operator |= 0 4 1976 21 BitArray::operator |= 0 1 461 0
50
void BitArray::operator |=(BitArray const &other);

1417 11 operator ^= 0 4 1976 21 BitArray::operator ^= 0 1 462 0
50
void BitArray::operator ^=(BitArray const &other);

1418 12 operator <<= 0 4 1976 22 BitArray::operator <<= 0 1 463 0
39
void BitArray::operator <<=(int shift);

1419 12 operator >>= 0 4 1976 22 BitArray::operator >>= 0 1 464 0
39
void BitArray::operator >>=(int shift);

1420 12 __getstate__ 0 4 1976 22 BitArray::__getstate__ 0 1 465 0
45
PyObject *BitArray::__getstate__(void) const;

1421 12 __setstate__ 0 4 1976 22 BitArray::__setstate__ 0 1 466 0
45
void BitArray::__setstate__(PyObject *state);

1422 14 get_class_type 0 4 1976 24 BitArray::get_class_type 0 1 467 0
49
static TypeHandle BitArray::get_class_type(void);

1423 9 ~BitArray 0 516 1976 19 BitArray::~BitArray 0 0 0
26
BitArray::~BitArray(void);

1424 12 ButtonHandle 0 260 1978 26 ButtonHandle::ButtonHandle 0 4 468 469 470 471 758
// The default constructor must do nothing, because we can't guarantee
// ordering of static initializers.  If the constructor tried to initialize
// its value, it  might happen after the value had already been set
// previously by another static initializer!

/**
 * Constructs a ButtonHandle with the corresponding index number, which may
 * have been returned by an earlier call to ButtonHandle::get_index().
 */

/**
 * Constructs a ButtonHandle with the corresponding name, which is looked up
 * in the ButtonRegistry.  This exists for the purpose of being able to
 * automatically coerce a string into a ButtonHandle; for most purposes, you
 * should use either the static KeyboardButton/MouseButton getters or
 * ButtonRegistry::register_button().
 */
219
inline ButtonHandle::ButtonHandle(void) = default;
constexpr ButtonHandle::ButtonHandle(int index);
ButtonHandle::ButtonHandle(std::string const &name);
inline ButtonHandle::ButtonHandle(ButtonHandle const &) = default;

1425 11 operator == 0 4 1978 25 ButtonHandle::operator == 0 1 472 0
71
inline bool ButtonHandle::operator ==(ButtonHandle const &other) const;

1426 11 operator != 0 4 1978 25 ButtonHandle::operator != 0 1 473 0
71
inline bool ButtonHandle::operator !=(ButtonHandle const &other) const;

1427 10 operator < 0 4 1978 24 ButtonHandle::operator < 0 1 474 0
70
inline bool ButtonHandle::operator <(ButtonHandle const &other) const;

1428 11 operator <= 0 4 1978 25 ButtonHandle::operator <= 0 1 475 0
71
inline bool ButtonHandle::operator <=(ButtonHandle const &other) const;

1429 10 operator > 0 4 1978 24 ButtonHandle::operator > 0 1 476 0
70
inline bool ButtonHandle::operator >(ButtonHandle const &other) const;

1430 11 operator >= 0 4 1978 25 ButtonHandle::operator >= 0 1 477 0
71
inline bool ButtonHandle::operator >=(ButtonHandle const &other) const;

1431 10 compare_to 0 4 1978 24 ButtonHandle::compare_to 0 1 478 209
/**
 * Sorts ButtonHandles arbitrarily (according to <, >, etc.).  Returns a
 * number less than 0 if this type sorts before the other one, greater than
 * zero if it sorts after, 0 if they are equivalent.
 */
69
inline int ButtonHandle::compare_to(ButtonHandle const &other) const;

1432 8 get_hash 0 4 1978 22 ButtonHandle::get_hash 0 1 479 54
/**
 * Returns a hash code suitable for phash_map.
 */
54
inline std::size_t ButtonHandle::get_hash(void) const;

1433 8 get_name 0 4 1978 22 ButtonHandle::get_name 0 1 480 42
/**
 * Returns the name of the button.
 */
47
std::string ButtonHandle::get_name(void) const;

1434 20 has_ascii_equivalent 0 4 1978 34 ButtonHandle::has_ascii_equivalent 0 1 481 121
/**
 * Returns true if the button was created with an ASCII equivalent code (e.g.
 * for a standard keyboard button).
 */
59
inline bool ButtonHandle::has_ascii_equivalent(void) const;

1435 20 get_ascii_equivalent 0 4 1978 34 ButtonHandle::get_ascii_equivalent 0 1 482 109
/**
 * Returns the character code associated with the button, or '\0' if no ASCII
 * code was associated.
 */
59
inline char ButtonHandle::get_ascii_equivalent(void) const;

1436 9 get_alias 0 4 1978 23 ButtonHandle::get_alias 0 1 483 373
/**
 * Returns the alias (alternate name) associated with the button, if any, or
 * ButtonHandle::none() if the button has no alias.
 *
 * Each button is allowed to have one alias, and multiple different buttons
 * can refer to the same alias.  The alias should be the more general name for
 * the button, for instance, shift is an alias for lshift, but not vice-versa.
 */
49
ButtonHandle ButtonHandle::get_alias(void) const;

1437 7 matches 0 4 1978 21 ButtonHandle::matches 0 1 484 263
/**
 * Returns true if this ButtonHandle is the same as the other one, or if the
 * other one is an alias for this one.  (Does not return true if this button
 * is an alias for the other one, however.)
 *
 * This is a more general comparison than operator ==.
 */
67
inline bool ButtonHandle::matches(ButtonHandle const &other) const;

1438 9 get_index 0 4 1978 23 ButtonHandle::get_index 0 1 485 363
/**
 * Returns the integer index associated with this ButtonHandle.  Each
 * different ButtonHandle will have a different index.  However, you probably
 * shouldn't be using this method; you should just treat the ButtonHandles as
 * opaque classes.  This is provided for the convenience of non-C++ scripting
 * languages to build a hashtable of ButtonHandles.
 */
50
constexpr int ButtonHandle::get_index(void) const;

1439 6 output 0 4 1978 20 ButtonHandle::output 0 1 486 10
/**
 *
 */
58
inline void ButtonHandle::output(std::ostream &out) const;

1440 4 none 0 4 1978 18 ButtonHandle::none 0 1 487 0
55
static constexpr ButtonHandle ButtonHandle::none(void);

1441 22 operator typecast bool 0 132 1978 36 ButtonHandle::operator typecast bool 0 1 489 0
34
inline operator bool (void) const;

1442 14 get_class_type 0 4 1978 28 ButtonHandle::get_class_type 0 1 488 0
53
static TypeHandle ButtonHandle::get_class_type(void);

1443 13 ~ButtonHandle 0 516 1978 27 ButtonHandle::~ButtonHandle 0 0 0
34
ButtonHandle::~ButtonHandle(void);

1444 10 get_button 0 4 1982 26 ButtonRegistry::get_button 0 1 491 150
/**
 * Finds a ButtonHandle in the registry matching the indicated name.  If there
 * is no such ButtonHandle, registers a new one and returns it.
 */
65
ButtonHandle ButtonRegistry::get_button(std::string const &name);

1445 11 find_button 0 4 1982 27 ButtonRegistry::find_button 0 1 492 144
/**
 * Finds a ButtonHandle in the registry matching the indicated name.  If there
 * is no such ButtonHandle, returns ButtonHandle::none().
 */
66
ButtonHandle ButtonRegistry::find_button(std::string const &name);

1446 17 find_ascii_button 0 4 1982 33 ButtonRegistry::find_ascii_button 0 1 493 169
/**
 * Finds a ButtonHandle in the registry matching the indicated ASCII
 * equivalent character.  If there is no such ButtonHandle, returns
 * ButtonHandle::none().
 */
76
ButtonHandle ButtonRegistry::find_ascii_button(char ascii_equivalent) const;

1447 5 write 0 4 1982 21 ButtonRegistry::write 0 1 494 10
/**
 *
 */
52
void ButtonRegistry::write(std::ostream &out) const;

1448 3 ptr 0 4 1982 19 ButtonRegistry::ptr 0 1 495 134
// ptr() returns the pointer to the global ButtonRegistry object.

/**
 * Returns the pointer to the global ButtonRegistry object.
 */
56
static inline ButtonRegistry *ButtonRegistry::ptr(void);

1449 14 ButtonRegistry 0 260 1982 30 ButtonRegistry::ButtonRegistry 0 1 490 10
/**
 *
 */
72
inline ButtonRegistry::ButtonRegistry(ButtonRegistry const &) = default;

1450 15 ~ButtonRegistry 0 516 1982 31 ButtonRegistry::~ButtonRegistry 0 0 0
38
ButtonRegistry::~ButtonRegistry(void);

1451 15 get_num_buttons 0 4 1983 26 ButtonMap::get_num_buttons 0 1 498 76
/**
 * Returns the number of buttons that this button mapping specifies.
 */
58
inline std::size_t ButtonMap::get_num_buttons(void) const;

1452 14 get_raw_button 0 4 1983 25 ButtonMap::get_raw_button 0 1 499 76
/**
 * Returns the underlying raw button associated with the nth button.
 */
67
inline ButtonHandle ButtonMap::get_raw_button(std::size_t i) const;

1453 17 get_mapped_button 0 4 1983 28 ButtonMap::get_mapped_button 0 3 500 501 502 437
/**
 * Returns the nth mapped button, meaning the button that the nth raw button
 * is mapped to.
 */

/**
 * Returns the button that the given button is mapped to, or
 * ButtonHandle::none() if this map does not specify a mapped button for the
 * given raw button.
 */

/**
 * Returns the button that the given button is mapped to, or
 * ButtonHandle::none() if this map does not specify a mapped button for the
 * given raw button.
 */
229
inline ButtonHandle ButtonMap::get_mapped_button(std::size_t i) const;
inline ButtonHandle ButtonMap::get_mapped_button(ButtonHandle raw) const;
inline ButtonHandle ButtonMap::get_mapped_button(std::string const &raw_name) const;

1454 23 get_mapped_button_label 0 4 1983 34 ButtonMap::get_mapped_button_label 0 3 503 504 505 837
/**
 * Returns the label associated with the nth mapped button, meaning the button
 * that the nth raw button is mapped to.
 */

/**
 * If the button map specifies a special name for the button (eg.  if the
 * operating system or keyboard device has a localized name describing the
 * key), returns it, or the empty string otherwise.
 *
 * Note that this is not the same as get_mapped_button().get_name(), which
 * returns the name of the Panda event associated with the button.
 */

/**
 * If the button map specifies a special name for the button (eg.  if the
 * operating system or keyboard device has a localized name describing the
 * key), returns it, or the empty string otherwise.
 *
 * Note that this is not the same as get_mapped_button().get_name(), which
 * returns the name of the Panda event associated with the button.
 */
265
inline std::string const &ButtonMap::get_mapped_button_label(std::size_t i) const;
inline std::string const &ButtonMap::get_mapped_button_label(ButtonHandle raw) const;
inline std::string const &ButtonMap::get_mapped_button_label(std::string const &raw_name) const;

1455 6 output 0 4 1983 17 ButtonMap::output 0 1 506 10
/**
 *
 */
48
void ButtonMap::output(std::ostream &out) const;

1456 5 write 0 4 1983 16 ButtonMap::write 0 1 507 10
/**
 *
 */
69
void ButtonMap::write(std::ostream &out, int indent_level = 0) const;

1457 14 get_class_type 0 4 1983 25 ButtonMap::get_class_type 0 1 508 0
50
static TypeHandle ButtonMap::get_class_type(void);

1458 9 ButtonMap 0 260 1983 20 ButtonMap::ButtonMap 0 2 496 497 0
102
inline ButtonMap::ButtonMap(void) = default;
inline ButtonMap::ButtonMap(ButtonMap const &) = default;

1459 10 ~ButtonMap 0 516 1983 21 ButtonMap::~ButtonMap 0 0 0
28
ButtonMap::~ButtonMap(void);

1460 6 output 0 6 1985 22 CallbackObject::output 0 1 510 10
/**
 *
 */
61
virtual void CallbackObject::output(std::ostream &out) const;

1461 4 make 0 4 1985 20 CallbackObject::make 0 1 511 0
76
static PointerTo< CallbackObject > CallbackObject::make(PyObject *function);

1462 14 get_class_type 0 4 1985 30 CallbackObject::get_class_type 0 1 512 0
55
static TypeHandle CallbackObject::get_class_type(void);

1463 14 CallbackObject 0 260 1985 30 CallbackObject::CallbackObject 0 1 509 10
/**
 *
 */
72
inline CallbackObject::CallbackObject(CallbackObject const &) = default;

1464 15 ~CallbackObject 0 516 1985 31 CallbackObject::~CallbackObject 0 0 0
38
CallbackObject::~CallbackObject(void);

1465 19 get_cache_ref_count 0 4 1986 54 CachedTypedWritableReferenceCount::get_cache_ref_count 0 1 513 47
/**
 * Returns the current reference count.
 */
78
inline int CachedTypedWritableReferenceCount::get_cache_ref_count(void) const;

1466 9 cache_ref 0 4 1986 44 CachedTypedWritableReferenceCount::cache_ref 0 1 514 108
/**
 * Explicitly increments the cache reference count and the normal reference
 * count simultaneously.
 */
69
inline void CachedTypedWritableReferenceCount::cache_ref(void) const;

1467 11 cache_unref 0 4 1986 46 CachedTypedWritableReferenceCount::cache_unref 0 1 515 202
/**
 * Explicitly decrements the cache reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */
71
inline bool CachedTypedWritableReferenceCount::cache_unref(void) const;

1468 24 test_ref_count_integrity 0 4 1986 59 CachedTypedWritableReferenceCount::test_ref_count_integrity 0 1 516 97
/**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */
84
inline bool CachedTypedWritableReferenceCount::test_ref_count_integrity(void) const;

1469 14 get_class_type 0 4 1986 49 CachedTypedWritableReferenceCount::get_class_type 0 1 517 0
74
static TypeHandle CachedTypedWritableReferenceCount::get_class_type(void);

1470 6 output 0 6 1987 20 CallbackData::output 0 1 518 10
/**
 *
 */
59
virtual void CallbackData::output(std::ostream &out) const;

1471 6 upcall 0 6 1987 20 CallbackData::upcall 0 1 519 159
/**
 * You should make this call during the callback if you want to continue the
 * normal function that would have been done in the absence of a callback.
 */
40
virtual void CallbackData::upcall(void);

1472 14 get_class_type 0 4 1987 28 CallbackData::get_class_type 0 1 520 0
53
static TypeHandle CallbackData::get_class_type(void);

1473 13 ~CallbackData 0 516 1987 27 CallbackData::~CallbackData 0 0 0
34
CallbackData::~CallbackData(void);

1474 20 PythonCallbackObject 0 260 1988 42 PythonCallbackObject::PythonCallbackObject 0 2 521 522 10
/**
 *
 */
177
PythonCallbackObject::PythonCallbackObject(PyObject *function = (& ::_Py_NoneStruct));
inline PythonCallbackObject::PythonCallbackObject(PythonCallbackObject const &) = default;

1475 12 operator new 0 4 1988 34 PythonCallbackObject::operator new 0 1 523 0
144
inline void *PythonCallbackObject::operator new(std::size_t size);
inline void *PythonCallbackObject::operator new(std::size_t size, void *ptr);

1476 15 operator delete 0 4 1988 37 PythonCallbackObject::operator delete 0 0 0
128
inline void PythonCallbackObject::operator delete(void *ptr);
inline void PythonCallbackObject::operator delete(void *, void *);

1477 12 validate_ptr 0 4 1988 34 PythonCallbackObject::validate_ptr 0 0 0
71
static inline bool PythonCallbackObject::validate_ptr(void const *ptr);

1478 12 set_function 0 4 1988 34 PythonCallbackObject::set_function 0 1 524 126
/**
 * Replaces the function that is called for the callback.  runs.  The
 * parameter should be a Python callable object.
 */
60
void PythonCallbackObject::set_function(PyObject *function);

1479 12 get_function 0 4 1988 34 PythonCallbackObject::get_function 0 1 525 64
/**
 * Returns the function that is called for the callback.
 */
51
PyObject *PythonCallbackObject::get_function(void);

1480 14 get_class_type 0 4 1988 36 PythonCallbackObject::get_class_type 0 1 526 0
61
static TypeHandle PythonCallbackObject::get_class_type(void);

1481 7 TimeVal 0 260 1989 16 TimeVal::TimeVal 0 2 527 528 10
/**
 *
 */
82
inline TimeVal::TimeVal(void);
inline TimeVal::TimeVal(TimeVal const &) = default;

1482 7 get_sec 0 4 1989 16 TimeVal::get_sec 0 1 529 10
/**
 *
 */
42
inline ulong TimeVal::get_sec(void) const;

1483 8 get_usec 0 4 1989 17 TimeVal::get_usec 0 1 530 10
/**
 *
 */
43
inline ulong TimeVal::get_usec(void) const;

1484 6 get_tv 0 20 1989 15 TimeVal::get_tv 0 0 32
getter for ulong TimeVal::tv[2];
35
ulong *TimeVal::get_tv(void) const;

1485 6 set_tv 0 36 1989 15 TimeVal::set_tv 0 0 32
setter for ulong TimeVal::tv[2];
37
void TimeVal::set_tv(ulong value[2]);

1486 8 ~TimeVal 0 516 1989 17 TimeVal::~TimeVal 0 0 0
24
TimeVal::~TimeVal(void);

1487 11 ClockObject 0 260 1993 24 ClockObject::ClockObject 0 2 531 532 22
/**
 *
 */

/**
 *
 */
126
ClockObject::ClockObject(ClockObject::Mode mode = ::ClockObject::M_normal);
ClockObject::ClockObject(ClockObject const &copy);

1488 8 set_mode 0 4 1993 21 ClockObject::set_mode 0 1 533 1841
/**
 * Changes the mode of the clock.  Normally, the clock is in mode M_normal.
 * In this mode, each call to tick() will set the value returned by
 * get_frame_time() to the current real time; thus, the clock simply reports
 * time advancing.
 *
 * Other possible modes:
 *
 * M_non_real_time - the clock ignores real time completely; at each call to
 * tick(), it pretends that exactly dt seconds have elapsed since the last
 * call to tick().  You may set the value of dt with set_dt() or
 * set_frame_rate().
 *
 * M_limited - the clock will run as fast as it can, as in M_normal, but will
 * not run faster than the rate specified by set_frame_rate().  If the
 * application would run faster than this rate, the clock will slow down the
 * application.
 *
 * M_integer - the clock will run as fast as it can, but the rate will be
 * constrained to be an integer multiple or divisor of the rate specified by
 * set_frame_rate().  The clock will slow down the application a bit to
 * guarantee this.
 *
 * M_integer_limited - a combination of M_limited and M_integer; the clock
 * will not run faster than set_frame_rate(), and if it runs slower, it will
 * run at a integer divisor of that rate.
 *
 * M_forced - the clock forces the application to run at the rate specified by
 * set_frame_rate().  If the application would run faster than this rate, the
 * clock will slow down the application; if the application would run slower
 * than this rate, the clock slows down time so that the application believes
 * it is running at the given rate.
 *
 * M_degrade - the clock runs at real time, but the application is slowed down
 * by a set factor of its frame rate, specified by set_degrade_factor().
 *
 * M_slave - the clock does not advance, but relies on the user to call
 * set_frame_time() and/or set_frame_count() each frame.
 */
51
void ClockObject::set_mode(ClockObject::Mode mode);

1489 8 get_mode 0 4 1993 21 ClockObject::get_mode 0 1 534 66
/**
 * Returns the current mode of the clock.  See set_mode().
 */
59
inline ClockObject::Mode ClockObject::get_mode(void) const;

1490 14 get_frame_time 0 4 1993 27 ClockObject::get_frame_time 0 1 535 350
/**
 * Returns the time in seconds as of the last time tick() was called
 * (typically, this will be as of the start of the current frame).
 *
 * This is generally the kind of time you want to ask for in most rendering
 * and animation contexts, since it's important that all of the animation for
 * a given frame remains in sync with each other.
 */
103
inline double ClockObject::get_frame_time(Thread *current_thread = Thread::get_current_thread()) const;

1491 13 get_real_time 0 4 1993 26 ClockObject::get_real_time 0 1 536 434
/**
 * Returns the actual number of seconds elapsed since the ClockObject was
 * created, or since it was last reset.  This is useful for doing real timing
 * measurements, e.g.  for performance statistics.
 *
 * This returns the most precise timer we have for short time intervals, but
 * it may tend to drift over the long haul.  If more accurate timekeeping is
 * needed over a long period of time, use get_long_time() instead.
 */
53
inline double ClockObject::get_real_time(void) const;

1492 13 get_long_time 0 4 1993 26 ClockObject::get_long_time 0 1 537 440
/**
 * Returns the actual number of seconds elapsed since the ClockObject was
 * created, or since it was last reset.
 *
 * This is similar to get_real_time(), except that it uses the most accurate
 * counter we have over a long period of time, and so it is less likely to
 * drift.  However, it may not be very precise for measuring short intervals.
 * On Windows, for instace, this is only accurate to within about 55
 * milliseconds.
 */
53
inline double ClockObject::get_long_time(void) const;

1493 5 reset 0 4 1993 18 ClockObject::reset 0 1 538 75
/**
 * Simultaneously resets both the time and the frame count to zero.
 */
37
inline void ClockObject::reset(void);

1494 13 set_real_time 0 4 1993 26 ClockObject::set_real_time 0 1 539 316
/**
 * Resets the clock to the indicated time.  This changes only the real time of
 * the clock as reported by get_real_time(), but does not immediately change
 * the time reported by get_frame_time()--that will change after the next call
 * to tick().  Also see reset(), set_frame_time(), and set_frame_count().
 */
45
void ClockObject::set_real_time(double time);

1495 14 set_frame_time 0 4 1993 27 ClockObject::set_frame_time 0 1 540 215
/**
 * Changes the time as reported for the current frame to the indicated time.
 * Normally, the way to adjust the frame time is via tick(); this function is
 * provided only for occasional special adjustments.
 */
101
void ClockObject::set_frame_time(double time, Thread *current_thread = Thread::get_current_thread());

1496 15 set_frame_count 0 4 1993 28 ClockObject::set_frame_count 0 1 541 132
/**
 * Resets the number of frames counted to the indicated number.  Also see
 * reset(), set_real_time(), and set_frame_time().
 */
106
void ClockObject::set_frame_count(int frame_count, Thread *current_thread = Thread::get_current_thread());

1497 15 get_frame_count 0 4 1993 28 ClockObject::get_frame_count 0 1 542 195
/**
 * Returns the number of times tick() has been called since the ClockObject
 * was created, or since it was last reset.  This is generally the number of
 * frames that have been rendered.
 */
101
inline int ClockObject::get_frame_count(Thread *current_thread = Thread::get_current_thread()) const;

1498 18 get_net_frame_rate 0 4 1993 31 ClockObject::get_net_frame_rate 0 1 543 245
/**
 * Returns the average frame rate since the last reset.  This is simply the
 * total number of frames divided by the total elapsed time.  This reports the
 * virtual frame rate if the clock is in (or has been in) M_non_real_time
 * mode.
 */
107
inline double ClockObject::get_net_frame_rate(Thread *current_thread = Thread::get_current_thread()) const;

1499 6 get_dt 0 4 1993 19 ClockObject::get_dt 0 1 544 130
/**
 * Returns the elapsed time for the previous frame: the number of seconds
 * elapsed between the last two calls to tick().
 */
95
inline double ClockObject::get_dt(Thread *current_thread = Thread::get_current_thread()) const;

1500 6 set_dt 0 4 1993 19 ClockObject::set_dt 0 1 545 291
/**
 * In non-real-time mode, sets the number of seconds that should appear to
 * elapse between frames.  In forced mode or limited mode, sets our target dt.
 * In normal mode, this has no effect.
 *
 * Also see set_frame_rate(), which is a different way to specify the same
 * quantity.
 */
36
void ClockObject::set_dt(double dt);

1501 14 set_frame_rate 0 4 1993 27 ClockObject::set_frame_rate 0 1 546 291
/**
 * In non-real-time mode, sets the number of frames per second that we should
 * appear to be running.  In forced mode or limited mode, sets our target
 * frame rate.  In normal mode, this has no effect.
 *
 * Also see set_dt(), which is a different way to specify the same quantity.
 */
52
void ClockObject::set_frame_rate(double frame_rate);

1502 10 get_max_dt 0 4 1993 23 ClockObject::get_max_dt 0 1 547 106
/**
 * Returns the current maximum allowable time elapsed between any two frames.
 * See set_max_dt().
 */
50
inline double ClockObject::get_max_dt(void) const;

1503 10 set_max_dt 0 4 1993 23 ClockObject::set_max_dt 0 1 548 511
/**
 * Sets a limit on the value returned by get_dt().  If this value is less than
 * zero, no limit is imposed; otherwise, this is the maximum value that will
 * ever be returned by get_dt(), regardless of how much time has actually
 * elapsed between frames.
 *
 * This limit is only imposed in real-time mode; in non-real-time mode, the dt
 * is fixed anyway and max_dt is ignored.
 *
 * This is generally used to guarantee reasonable behavior even in the
 * presence of a very slow or chuggy frame rame.
 */
51
inline void ClockObject::set_max_dt(double max_dt);

1504 18 get_degrade_factor 0 4 1993 31 ClockObject::get_degrade_factor 0 1 549 265
/**
 * In degrade mode, returns the ratio by which the performance is degraded.  A
 * value of 2.0 causes the clock to be slowed down by a factor of two
 * (reducing performance to 1/2 what would be otherwise).
 *
 * This has no effect if mode is not M_degrade.
 */
58
inline double ClockObject::get_degrade_factor(void) const;

1505 18 set_degrade_factor 0 4 1993 31 ClockObject::set_degrade_factor 0 1 550 262
/**
 * In degrade mode, sets the ratio by which the performance is degraded.  A
 * value of 2.0 causes the clock to be slowed down by a factor of two
 * (reducing performance to 1/2 what would be otherwise).
 *
 * This has no effect if mode is not M_degrade.
 */
67
inline void ClockObject::set_degrade_factor(double degrade_factor);

1506 31 set_average_frame_rate_interval 0 4 1993 44 ClockObject::set_average_frame_rate_interval 0 1 551 393
/**
 * Specifies the interval of time (in seconds) over which
 * get_average_frame_rate() averages the number of frames per second to
 * compute the frame rate.  Changing this does not necessarily immediately
 * change the result of get_average_frame_rate(), until this interval of time
 * has elapsed again.
 *
 * Setting this to zero disables the computation of get_average_frame_rate().
 */
70
inline void ClockObject::set_average_frame_rate_interval(double time);

1507 31 get_average_frame_rate_interval 0 4 1993 44 ClockObject::get_average_frame_rate_interval 0 1 552 162
/**
 * Returns the interval of time (in seconds) over which
 * get_average_frame_rate() averages the number of frames per second to
 * compute the frame rate.
 */
71
inline double ClockObject::get_average_frame_rate_interval(void) const;

1508 22 get_average_frame_rate 0 4 1993 35 ClockObject::get_average_frame_rate 0 1 553 214
/**
 * Returns the average frame rate in number of frames per second over the last
 * get_average_frame_rate_interval() seconds.  This measures the virtual frame
 * rate if the clock is in M_non_real_time mode.
 */
104
double ClockObject::get_average_frame_rate(Thread *current_thread = Thread::get_current_thread()) const;

1509 22 get_max_frame_duration 0 4 1993 35 ClockObject::get_max_frame_duration 0 1 554 105
/**
 * Returns the maximum frame duration over the last
 * get_average_frame_rate_interval() seconds.
 */
104
double ClockObject::get_max_frame_duration(Thread *current_thread = Thread::get_current_thread()) const;

1510 25 calc_frame_rate_deviation 0 4 1993 38 ClockObject::calc_frame_rate_deviation 0 1 555 539
/**
 * Returns the standard deviation of the frame times of the frames rendered
 * over the past get_average_frame_rate_interval() seconds.  This number gives
 * an estimate of the chugginess of the frame rate; if it is large, there is a
 * large variation in the frame rate; if is small, all of the frames are
 * consistent in length.
 *
 * A large value might also represent just a recent change in frame rate, for
 * instance, because the camera has just rotated from looking at a simple
 * scene to looking at a more complex scene.
 */
107
double ClockObject::calc_frame_rate_deviation(Thread *current_thread = Thread::get_current_thread()) const;

1511 4 tick 0 4 1993 17 ClockObject::tick 0 1 556 285
/**
 * Instructs the clock that a new frame has just begun.  In normal, real-time
 * mode, get_frame_time() will henceforth report the time as of this instant
 * as the current start-of-frame time.  In non-real-time mode,
 * get_frame_time() will be incremented by the value of dt.
 */
78
void ClockObject::tick(Thread *current_thread = Thread::get_current_thread());

1512 15 sync_frame_time 0 4 1993 28 ClockObject::sync_frame_time 0 1 557 417
/**
 * Resets the frame time to the current real time.  This is similar to tick(),
 * except that it does not advance the frame counter and does not affect dt.
 * This is intended to be used in the middle of a particularly long frame to
 * compensate for the time that has already elapsed.
 *
 * In non-real-time mode, this function has no effect (because in this mode
 * all frames take the same length of time).
 */
89
void ClockObject::sync_frame_time(Thread *current_thread = Thread::get_current_thread());

1513 12 check_errors 0 4 1993 25 ClockObject::check_errors 0 1 558 322
/**
 * Returns true if a clock error was detected since the last time
 * check_errors() was called.  A clock error means that something happened, an
 * OS or BIOS bug, for instance, that makes the current value of the clock
 * somewhat suspect, and an application may wish to resynchronize with any
 * external clocks.
 */
62
inline bool ClockObject::check_errors(Thread *current_thread);

1514 16 get_global_clock 0 4 1993 29 ClockObject::get_global_clock 0 1 559 159
/**
 * Returns a pointer to the global ClockObject.  This is the ClockObject that
 * most code should use for handling scene graph rendering and animation.
 */
63
static inline ClockObject *ClockObject::get_global_clock(void);

1515 14 get_class_type 0 4 1993 27 ClockObject::get_class_type 0 1 560 0
52
static TypeHandle ClockObject::get_class_type(void);

1516 24 parse_color_space_string 0 1 0 24 parse_color_space_string 0 1 960 0
60
ColorSpace parse_color_space_string(std::string const &str);

1517 18 format_color_space 0 1 0 18 format_color_space 0 1 961 0
46
std::string format_color_space(ColorSpace cs);

1518 14 get_model_path 0 1 0 14 get_model_path 0 1 962 0
47
ConfigVariableSearchPath &get_model_path(void);

1519 15 get_plugin_path 0 1 0 15 get_plugin_path 0 1 963 0
48
ConfigVariableSearchPath &get_plugin_path(void);

1520 9 cache_ref 0 4 1997 28 CopyOnWriteObject::cache_ref 0 1 561 62
/**
 * @see CachedTypedWritableReferenceCount::cache_ref()
 */
53
inline void CopyOnWriteObject::cache_ref(void) const;

1521 11 cache_unref 0 4 1997 30 CopyOnWriteObject::cache_unref 0 1 562 64
/**
 * @see CachedTypedWritableReferenceCount::cache_unref()
 */
55
inline bool CopyOnWriteObject::cache_unref(void) const;

1522 14 get_class_type 0 4 1997 33 CopyOnWriteObject::get_class_type 0 1 563 0
58
static TypeHandle CopyOnWriteObject::get_class_type(void);

1523 18 ~CopyOnWriteObject 0 516 1997 37 CopyOnWriteObject::~CopyOnWriteObject 0 0 0
44
CopyOnWriteObject::~CopyOnWriteObject(void);

1524 22 upcast_to_DatagramSink 0 12 1998 38 DatagramBuffer::upcast_to_DatagramSink 0 1 567 42
upcast from DatagramBuffer to DatagramSink
59
DatagramSink *DatagramBuffer::upcast_to_DatagramSink(void);

1525 26 downcast_to_DatagramBuffer 0 12 1968 40 DatagramSink::downcast_to_DatagramBuffer 0 0 44
downcast from DatagramSink to DatagramBuffer
63
DatagramBuffer *DatagramSink::downcast_to_DatagramBuffer(void);

1526 27 upcast_to_DatagramGenerator 0 12 1998 43 DatagramBuffer::upcast_to_DatagramGenerator 0 1 568 47
upcast from DatagramBuffer to DatagramGenerator
69
DatagramGenerator *DatagramBuffer::upcast_to_DatagramGenerator(void);

1527 26 downcast_to_DatagramBuffer 0 12 1961 45 DatagramGenerator::downcast_to_DatagramBuffer 0 0 49
downcast from DatagramGenerator to DatagramBuffer
68
DatagramBuffer *DatagramGenerator::downcast_to_DatagramBuffer(void);

1528 14 DatagramBuffer 0 260 1998 30 DatagramBuffer::DatagramBuffer 0 2 564 565 104
/**
 * Initializes an empty datagram buffer.
 */

/**
 * Initializes the buffer with the given data.
 */
111
inline DatagramBuffer::DatagramBuffer(void);
inline explicit DatagramBuffer::DatagramBuffer(vector_uchar data);

1529 5 clear 0 4 1998 21 DatagramBuffer::clear 0 1 566 38
/**
 * Clears the internal buffer.
 */
40
inline void DatagramBuffer::clear(void);

1530 8 get_data 0 4 1998 24 DatagramBuffer::get_data 0 0 39
/**
 * Returns the internal buffer.
 */
64
inline vector_uchar const &DatagramBuffer::get_data(void) const;

1531 8 set_data 0 4 1998 24 DatagramBuffer::set_data 0 0 52
/**
 * Replaces the data in the internal buffer.
 */
56
inline void DatagramBuffer::set_data(vector_uchar data);

1532 15 ~DatagramBuffer 0 516 1998 31 DatagramBuffer::~DatagramBuffer 0 0 0
38
DatagramBuffer::~DatagramBuffer(void);

1533 17 DatagramInputFile 0 260 2002 36 DatagramInputFile::DatagramInputFile 0 1 569 10
/**
 *
 */
50
inline DatagramInputFile::DatagramInputFile(void);

1534 4 open 0 4 2002 23 DatagramInputFile::open 0 3 570 571 572 432
/**
 * Opens the indicated filename for reading.  Returns true on success, false
 * on failure.
 */

/**
 * Opens the indicated filename for reading.  Returns true on success, false
 * on failure.
 */

/**
 * Starts reading from the indicated stream.  Returns true on success, false
 * on failure.  The DatagramInputFile does not take ownership of the stream;
 * you are responsible for closing or deleting it when you are done.
 */
206
bool DatagramInputFile::open(FileReference const *file);
inline bool DatagramInputFile::open(Filename const &filename);
bool DatagramInputFile::open(std::istream &in, Filename const &filename = Filename());

1535 10 get_stream 0 4 2002 29 DatagramInputFile::get_stream 0 1 573 61
/**
 * Returns the istream represented by the input file.
 */
57
inline std::istream &DatagramInputFile::get_stream(void);

1536 5 close 0 4 2002 24 DatagramInputFile::close 0 1 574 98
/**
 * Closes the file.  This is also implicitly done when the DatagramInputFile
 * destructs.
 */
36
void DatagramInputFile::close(void);

1537 11 read_header 0 4 2002 30 DatagramInputFile::read_header 0 0 257
/**
 * Reads a sequence of bytes from the beginning of the datagram file.  This
 * may be called any number of times after the file has been opened and before
 * the first datagram is read.  It may not be called once the first datagram
 * has been read.
 */
80
bool DatagramInputFile::read_header(std::string &header, std::size_t num_bytes);

1538 18 DatagramOutputFile 0 260 2003 38 DatagramOutputFile::DatagramOutputFile 0 1 575 10
/**
 *
 */
52
inline DatagramOutputFile::DatagramOutputFile(void);

1539 4 open 0 4 2003 24 DatagramOutputFile::open 0 3 576 577 578 434
/**
 * Opens the indicated filename for writing.  Returns true on success, false
 * on failure.
 */

/**
 * Opens the indicated filename for writing.  Returns true if successful,
 * false on failure.
 */

/**
 * Starts writing to the indicated stream.  Returns true on success, false on
 * failure.  The DatagramOutputFile does not take ownership of the stream; you
 * are responsible for closing or deleting it when you are done.
 */
210
bool DatagramOutputFile::open(FileReference const *file);
inline bool DatagramOutputFile::open(Filename const &filename);
bool DatagramOutputFile::open(std::ostream &out, Filename const &filename = Filename());

1540 5 close 0 4 2003 25 DatagramOutputFile::close 0 1 579 99
/**
 * Closes the file.  This is also implicitly done when the DatagramOutputFile
 * destructs.
 */
37
void DatagramOutputFile::close(void);

1541 12 write_header 0 4 2003 32 DatagramOutputFile::write_header 0 2 580 581 514
/**
 * Writes a sequence of bytes to the beginning of the datagram file.  This may
 * be called any number of times after the file has been opened and before the
 * first datagram is written.  It may not be called once the first datagram is
 * written.
 */

/**
 * Writes a sequence of bytes to the beginning of the datagram file.  This may
 * be called any number of times after the file has been opened and before the
 * first datagram is written.  It may not be called once the first datagram is
 * written.
 */
132
bool DatagramOutputFile::write_header(vector_uchar const &header);
bool DatagramOutputFile::write_header(std::string const &header);

1542 10 get_stream 0 4 2003 30 DatagramOutputFile::get_stream 0 0 62
/**
 * Returns the ostream represented by the output file.
 */
58
inline std::ostream &DatagramOutputFile::get_stream(void);

1543 13 DoubleBitMask 0 260 2005 45 DoubleBitMask< BitMaskNative >::DoubleBitMask 0 3 582 583 584 0
245
constexpr DoubleBitMask< BitMaskNative >::DoubleBitMask(void) = default;
DoubleBitMask< BitMaskNative >::DoubleBitMask(PyObject *init_value);
inline DoubleBitMask< BitMaskNative >::DoubleBitMask(DoubleBitMask< BitMaskNative > const &) = default;

1544 6 all_on 0 4 2005 38 DoubleBitMask< BitMaskNative >::all_on 0 1 585 0
90
static inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::all_on(void);

1545 7 all_off 0 4 2005 39 DoubleBitMask< BitMaskNative >::all_off 0 1 586 0
91
static inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::all_off(void);

1546 8 lower_on 0 4 2005 40 DoubleBitMask< BitMaskNative >::lower_on 0 1 587 0
99
static inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::lower_on(int on_bits);

1547 3 bit 0 4 2005 35 DoubleBitMask< BitMaskNative >::bit 0 1 588 0
92
static inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::bit(int index);

1548 5 range 0 4 2005 37 DoubleBitMask< BitMaskNative >::range 0 1 589 0
106
static inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::range(int low_bit, int size);

1549 16 has_max_num_bits 0 4 2005 48 DoubleBitMask< BitMaskNative >::has_max_num_bits 0 1 590 0
77
static constexpr bool DoubleBitMask< BitMaskNative >::has_max_num_bits(void);

1550 16 get_max_num_bits 0 4 2005 48 DoubleBitMask< BitMaskNative >::get_max_num_bits 0 1 591 0
76
static constexpr int DoubleBitMask< BitMaskNative >::get_max_num_bits(void);

1551 12 get_num_bits 0 4 2005 44 DoubleBitMask< BitMaskNative >::get_num_bits 0 1 592 0
71
constexpr int DoubleBitMask< BitMaskNative >::get_num_bits(void) const;

1552 7 get_bit 0 4 2005 39 DoubleBitMask< BitMaskNative >::get_bit 0 1 593 0
69
inline bool DoubleBitMask< BitMaskNative >::get_bit(int index) const;

1553 7 set_bit 0 4 2005 39 DoubleBitMask< BitMaskNative >::set_bit 0 1 594 0
63
inline void DoubleBitMask< BitMaskNative >::set_bit(int index);

1554 9 clear_bit 0 4 2005 41 DoubleBitMask< BitMaskNative >::clear_bit 0 1 595 0
65
inline void DoubleBitMask< BitMaskNative >::clear_bit(int index);

1555 10 set_bit_to 0 4 2005 42 DoubleBitMask< BitMaskNative >::set_bit_to 0 1 596 0
78
inline void DoubleBitMask< BitMaskNative >::set_bit_to(int index, bool value);

1556 7 is_zero 0 4 2005 39 DoubleBitMask< BitMaskNative >::is_zero 0 1 597 0
64
inline bool DoubleBitMask< BitMaskNative >::is_zero(void) const;

1557 9 is_all_on 0 4 2005 41 DoubleBitMask< BitMaskNative >::is_all_on 0 1 598 0
66
inline bool DoubleBitMask< BitMaskNative >::is_all_on(void) const;

1558 7 extract 0 4 2005 39 DoubleBitMask< BitMaskNative >::extract 0 1 599 0
85
inline uint64_t DoubleBitMask< BitMaskNative >::extract(int low_bit, int size) const;

1559 5 store 0 4 2005 37 DoubleBitMask< BitMaskNative >::store 0 1 600 0
89
inline void DoubleBitMask< BitMaskNative >::store(uint64_t value, int low_bit, int size);

1560 10 has_any_of 0 4 2005 42 DoubleBitMask< BitMaskNative >::has_any_of 0 1 601 0
84
inline bool DoubleBitMask< BitMaskNative >::has_any_of(int low_bit, int size) const;

1561 10 has_all_of 0 4 2005 42 DoubleBitMask< BitMaskNative >::has_all_of 0 1 602 0
84
inline bool DoubleBitMask< BitMaskNative >::has_all_of(int low_bit, int size) const;

1562 9 set_range 0 4 2005 41 DoubleBitMask< BitMaskNative >::set_range 0 1 603 0
77
inline void DoubleBitMask< BitMaskNative >::set_range(int low_bit, int size);

1563 11 clear_range 0 4 2005 43 DoubleBitMask< BitMaskNative >::clear_range 0 1 604 0
79
inline void DoubleBitMask< BitMaskNative >::clear_range(int low_bit, int size);

1564 12 set_range_to 0 4 2005 44 DoubleBitMask< BitMaskNative >::set_range_to 0 1 605 0
92
inline void DoubleBitMask< BitMaskNative >::set_range_to(bool value, int low_bit, int size);

1565 15 get_num_on_bits 0 4 2005 47 DoubleBitMask< BitMaskNative >::get_num_on_bits 0 1 606 0
71
inline int DoubleBitMask< BitMaskNative >::get_num_on_bits(void) const;

1566 16 get_num_off_bits 0 4 2005 48 DoubleBitMask< BitMaskNative >::get_num_off_bits 0 1 607 0
72
inline int DoubleBitMask< BitMaskNative >::get_num_off_bits(void) const;

1567 17 get_lowest_on_bit 0 4 2005 49 DoubleBitMask< BitMaskNative >::get_lowest_on_bit 0 1 608 0
73
inline int DoubleBitMask< BitMaskNative >::get_lowest_on_bit(void) const;

1568 18 get_lowest_off_bit 0 4 2005 50 DoubleBitMask< BitMaskNative >::get_lowest_off_bit 0 1 609 0
74
inline int DoubleBitMask< BitMaskNative >::get_lowest_off_bit(void) const;

1569 18 get_highest_on_bit 0 4 2005 50 DoubleBitMask< BitMaskNative >::get_highest_on_bit 0 1 610 0
74
inline int DoubleBitMask< BitMaskNative >::get_highest_on_bit(void) const;

1570 19 get_highest_off_bit 0 4 2005 51 DoubleBitMask< BitMaskNative >::get_highest_off_bit 0 1 611 0
75
inline int DoubleBitMask< BitMaskNative >::get_highest_off_bit(void) const;

1571 29 get_next_higher_different_bit 0 4 2005 61 DoubleBitMask< BitMaskNative >::get_next_higher_different_bit 0 1 612 0
92
inline int DoubleBitMask< BitMaskNative >::get_next_higher_different_bit(int low_bit) const;

1572 15 invert_in_place 0 4 2005 47 DoubleBitMask< BitMaskNative >::invert_in_place 0 1 613 0
66
inline void DoubleBitMask< BitMaskNative >::invert_in_place(void);

1573 18 has_bits_in_common 0 4 2005 50 DoubleBitMask< BitMaskNative >::has_bits_in_common 0 1 614 0
114
inline bool DoubleBitMask< BitMaskNative >::has_bits_in_common(DoubleBitMask< BitMaskNative > const &other) const;

1574 5 clear 0 4 2005 37 DoubleBitMask< BitMaskNative >::clear 0 1 615 0
56
inline void DoubleBitMask< BitMaskNative >::clear(void);

1575 6 output 0 4 2005 38 DoubleBitMask< BitMaskNative >::output 0 1 616 0
69
void DoubleBitMask< BitMaskNative >::output(std::ostream &out) const;

1576 13 output_binary 0 4 2005 45 DoubleBitMask< BitMaskNative >::output_binary 0 1 617 0
98
void DoubleBitMask< BitMaskNative >::output_binary(std::ostream &out, int spaces_every = 4) const;

1577 10 output_hex 0 4 2005 42 DoubleBitMask< BitMaskNative >::output_hex 0 1 618 0
95
void DoubleBitMask< BitMaskNative >::output_hex(std::ostream &out, int spaces_every = 4) const;

1578 5 write 0 4 2005 37 DoubleBitMask< BitMaskNative >::write 0 1 619 0
90
void DoubleBitMask< BitMaskNative >::write(std::ostream &out, int indent_level = 0) const;

1579 11 operator == 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator == 0 1 620 0
107
inline bool DoubleBitMask< BitMaskNative >::operator ==(DoubleBitMask< BitMaskNative > const &other) const;

1580 11 operator != 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator != 0 1 621 0
107
inline bool DoubleBitMask< BitMaskNative >::operator !=(DoubleBitMask< BitMaskNative > const &other) const;

1581 10 operator < 0 4 2005 42 DoubleBitMask< BitMaskNative >::operator < 0 1 622 0
106
inline bool DoubleBitMask< BitMaskNative >::operator <(DoubleBitMask< BitMaskNative > const &other) const;

1582 10 compare_to 0 4 2005 42 DoubleBitMask< BitMaskNative >::compare_to 0 1 623 0
105
inline int DoubleBitMask< BitMaskNative >::compare_to(DoubleBitMask< BitMaskNative > const &other) const;

1583 10 operator & 0 4 2005 42 DoubleBitMask< BitMaskNative >::operator & 0 1 624 0
132
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator &(DoubleBitMask< BitMaskNative > const &other) const;

1584 10 operator | 0 4 2005 42 DoubleBitMask< BitMaskNative >::operator | 0 1 625 0
132
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator |(DoubleBitMask< BitMaskNative > const &other) const;

1585 10 operator ^ 0 4 2005 42 DoubleBitMask< BitMaskNative >::operator ^ 0 1 626 0
132
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator ^(DoubleBitMask< BitMaskNative > const &other) const;

1586 10 operator ~ 0 68 2005 42 DoubleBitMask< BitMaskNative >::operator ~ 0 1 627 0
93
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator ~(void) const;

1587 11 operator << 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator << 0 1 628 0
99
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator <<(int shift) const;

1588 11 operator >> 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator >> 0 1 629 0
99
inline DoubleBitMask< BitMaskNative > DoubleBitMask< BitMaskNative >::operator >>(int shift) const;

1589 11 operator &= 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator &= 0 1 630 0
101
inline void DoubleBitMask< BitMaskNative >::operator &=(DoubleBitMask< BitMaskNative > const &other);

1590 11 operator |= 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator |= 0 1 631 0
101
inline void DoubleBitMask< BitMaskNative >::operator |=(DoubleBitMask< BitMaskNative > const &other);

1591 11 operator ^= 0 4 2005 43 DoubleBitMask< BitMaskNative >::operator ^= 0 1 632 0
101
inline void DoubleBitMask< BitMaskNative >::operator ^=(DoubleBitMask< BitMaskNative > const &other);

1592 12 operator <<= 0 4 2005 44 DoubleBitMask< BitMaskNative >::operator <<= 0 1 633 0
68
inline void DoubleBitMask< BitMaskNative >::operator <<=(int shift);

1593 12 operator >>= 0 4 2005 44 DoubleBitMask< BitMaskNative >::operator >>= 0 1 634 0
68
inline void DoubleBitMask< BitMaskNative >::operator >>=(int shift);

1594 10 __reduce__ 0 4 2005 42 DoubleBitMask< BitMaskNative >::__reduce__ 0 1 635 0
75
PyObject *DoubleBitMask< BitMaskNative >::__reduce__(PyObject *self) const;

1595 14 get_class_type 0 4 2005 46 DoubleBitMask< BitMaskNative >::get_class_type 0 1 636 0
71
static TypeHandle DoubleBitMask< BitMaskNative >::get_class_type(void);

1596 14 ~DoubleBitMask 0 516 2005 46 DoubleBitMask< BitMaskNative >::~DoubleBitMask 0 0 0
53
DoubleBitMask< BitMaskNative >::~DoubleBitMask(void);

1597 13 DoubleBitMask 0 260 2007 51 DoubleBitMask< DoubleBitMaskNative >::DoubleBitMask 0 3 637 638 639 0
269
constexpr DoubleBitMask< DoubleBitMaskNative >::DoubleBitMask(void) = default;
DoubleBitMask< DoubleBitMaskNative >::DoubleBitMask(PyObject *init_value);
inline DoubleBitMask< DoubleBitMaskNative >::DoubleBitMask(DoubleBitMask< DoubleBitMaskNative > const &) = default;

1598 6 all_on 0 4 2007 44 DoubleBitMask< DoubleBitMaskNative >::all_on 0 1 640 0
102
static inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::all_on(void);

1599 7 all_off 0 4 2007 45 DoubleBitMask< DoubleBitMaskNative >::all_off 0 1 641 0
103
static inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::all_off(void);

1600 8 lower_on 0 4 2007 46 DoubleBitMask< DoubleBitMaskNative >::lower_on 0 1 642 0
111
static inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::lower_on(int on_bits);

1601 3 bit 0 4 2007 41 DoubleBitMask< DoubleBitMaskNative >::bit 0 1 643 0
104
static inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::bit(int index);

1602 5 range 0 4 2007 43 DoubleBitMask< DoubleBitMaskNative >::range 0 1 644 0
118
static inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::range(int low_bit, int size);

1603 16 has_max_num_bits 0 4 2007 54 DoubleBitMask< DoubleBitMaskNative >::has_max_num_bits 0 1 645 0
83
static constexpr bool DoubleBitMask< DoubleBitMaskNative >::has_max_num_bits(void);

1604 16 get_max_num_bits 0 4 2007 54 DoubleBitMask< DoubleBitMaskNative >::get_max_num_bits 0 1 646 0
82
static constexpr int DoubleBitMask< DoubleBitMaskNative >::get_max_num_bits(void);

1605 12 get_num_bits 0 4 2007 50 DoubleBitMask< DoubleBitMaskNative >::get_num_bits 0 1 647 0
77
constexpr int DoubleBitMask< DoubleBitMaskNative >::get_num_bits(void) const;

1606 7 get_bit 0 4 2007 45 DoubleBitMask< DoubleBitMaskNative >::get_bit 0 1 648 0
75
inline bool DoubleBitMask< DoubleBitMaskNative >::get_bit(int index) const;

1607 7 set_bit 0 4 2007 45 DoubleBitMask< DoubleBitMaskNative >::set_bit 0 1 649 0
69
inline void DoubleBitMask< DoubleBitMaskNative >::set_bit(int index);

1608 9 clear_bit 0 4 2007 47 DoubleBitMask< DoubleBitMaskNative >::clear_bit 0 1 650 0
71
inline void DoubleBitMask< DoubleBitMaskNative >::clear_bit(int index);

1609 10 set_bit_to 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::set_bit_to 0 1 651 0
84
inline void DoubleBitMask< DoubleBitMaskNative >::set_bit_to(int index, bool value);

1610 7 is_zero 0 4 2007 45 DoubleBitMask< DoubleBitMaskNative >::is_zero 0 1 652 0
70
inline bool DoubleBitMask< DoubleBitMaskNative >::is_zero(void) const;

1611 9 is_all_on 0 4 2007 47 DoubleBitMask< DoubleBitMaskNative >::is_all_on 0 1 653 0
72
inline bool DoubleBitMask< DoubleBitMaskNative >::is_all_on(void) const;

1612 7 extract 0 4 2007 45 DoubleBitMask< DoubleBitMaskNative >::extract 0 1 654 0
91
inline uint64_t DoubleBitMask< DoubleBitMaskNative >::extract(int low_bit, int size) const;

1613 5 store 0 4 2007 43 DoubleBitMask< DoubleBitMaskNative >::store 0 1 655 0
95
inline void DoubleBitMask< DoubleBitMaskNative >::store(uint64_t value, int low_bit, int size);

1614 10 has_any_of 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::has_any_of 0 1 656 0
90
inline bool DoubleBitMask< DoubleBitMaskNative >::has_any_of(int low_bit, int size) const;

1615 10 has_all_of 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::has_all_of 0 1 657 0
90
inline bool DoubleBitMask< DoubleBitMaskNative >::has_all_of(int low_bit, int size) const;

1616 9 set_range 0 4 2007 47 DoubleBitMask< DoubleBitMaskNative >::set_range 0 1 658 0
83
inline void DoubleBitMask< DoubleBitMaskNative >::set_range(int low_bit, int size);

1617 11 clear_range 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::clear_range 0 1 659 0
85
inline void DoubleBitMask< DoubleBitMaskNative >::clear_range(int low_bit, int size);

1618 12 set_range_to 0 4 2007 50 DoubleBitMask< DoubleBitMaskNative >::set_range_to 0 1 660 0
98
inline void DoubleBitMask< DoubleBitMaskNative >::set_range_to(bool value, int low_bit, int size);

1619 15 get_num_on_bits 0 4 2007 53 DoubleBitMask< DoubleBitMaskNative >::get_num_on_bits 0 1 661 0
77
inline int DoubleBitMask< DoubleBitMaskNative >::get_num_on_bits(void) const;

1620 16 get_num_off_bits 0 4 2007 54 DoubleBitMask< DoubleBitMaskNative >::get_num_off_bits 0 1 662 0
78
inline int DoubleBitMask< DoubleBitMaskNative >::get_num_off_bits(void) const;

1621 17 get_lowest_on_bit 0 4 2007 55 DoubleBitMask< DoubleBitMaskNative >::get_lowest_on_bit 0 1 663 0
79
inline int DoubleBitMask< DoubleBitMaskNative >::get_lowest_on_bit(void) const;

1622 18 get_lowest_off_bit 0 4 2007 56 DoubleBitMask< DoubleBitMaskNative >::get_lowest_off_bit 0 1 664 0
80
inline int DoubleBitMask< DoubleBitMaskNative >::get_lowest_off_bit(void) const;

1623 18 get_highest_on_bit 0 4 2007 56 DoubleBitMask< DoubleBitMaskNative >::get_highest_on_bit 0 1 665 0
80
inline int DoubleBitMask< DoubleBitMaskNative >::get_highest_on_bit(void) const;

1624 19 get_highest_off_bit 0 4 2007 57 DoubleBitMask< DoubleBitMaskNative >::get_highest_off_bit 0 1 666 0
81
inline int DoubleBitMask< DoubleBitMaskNative >::get_highest_off_bit(void) const;

1625 29 get_next_higher_different_bit 0 4 2007 67 DoubleBitMask< DoubleBitMaskNative >::get_next_higher_different_bit 0 1 667 0
98
inline int DoubleBitMask< DoubleBitMaskNative >::get_next_higher_different_bit(int low_bit) const;

1626 15 invert_in_place 0 4 2007 53 DoubleBitMask< DoubleBitMaskNative >::invert_in_place 0 1 668 0
72
inline void DoubleBitMask< DoubleBitMaskNative >::invert_in_place(void);

1627 18 has_bits_in_common 0 4 2007 56 DoubleBitMask< DoubleBitMaskNative >::has_bits_in_common 0 1 669 0
126
inline bool DoubleBitMask< DoubleBitMaskNative >::has_bits_in_common(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1628 5 clear 0 4 2007 43 DoubleBitMask< DoubleBitMaskNative >::clear 0 1 670 0
62
inline void DoubleBitMask< DoubleBitMaskNative >::clear(void);

1629 6 output 0 4 2007 44 DoubleBitMask< DoubleBitMaskNative >::output 0 1 671 0
75
void DoubleBitMask< DoubleBitMaskNative >::output(std::ostream &out) const;

1630 13 output_binary 0 4 2007 51 DoubleBitMask< DoubleBitMaskNative >::output_binary 0 1 672 0
104
void DoubleBitMask< DoubleBitMaskNative >::output_binary(std::ostream &out, int spaces_every = 4) const;

1631 10 output_hex 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::output_hex 0 1 673 0
101
void DoubleBitMask< DoubleBitMaskNative >::output_hex(std::ostream &out, int spaces_every = 4) const;

1632 5 write 0 4 2007 43 DoubleBitMask< DoubleBitMaskNative >::write 0 1 674 0
96
void DoubleBitMask< DoubleBitMaskNative >::write(std::ostream &out, int indent_level = 0) const;

1633 11 operator == 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator == 0 1 675 0
119
inline bool DoubleBitMask< DoubleBitMaskNative >::operator ==(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1634 11 operator != 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator != 0 1 676 0
119
inline bool DoubleBitMask< DoubleBitMaskNative >::operator !=(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1635 10 operator < 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::operator < 0 1 677 0
118
inline bool DoubleBitMask< DoubleBitMaskNative >::operator <(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1636 10 compare_to 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::compare_to 0 1 678 0
117
inline int DoubleBitMask< DoubleBitMaskNative >::compare_to(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1637 10 operator & 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::operator & 0 1 679 0
150
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator &(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1638 10 operator | 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::operator | 0 1 680 0
150
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator |(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1639 10 operator ^ 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::operator ^ 0 1 681 0
150
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator ^(DoubleBitMask< DoubleBitMaskNative > const &other) const;

1640 10 operator ~ 0 68 2007 48 DoubleBitMask< DoubleBitMaskNative >::operator ~ 0 1 682 0
105
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator ~(void) const;

1641 11 operator << 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator << 0 1 683 0
111
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator <<(int shift) const;

1642 11 operator >> 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator >> 0 1 684 0
111
inline DoubleBitMask< DoubleBitMaskNative > DoubleBitMask< DoubleBitMaskNative >::operator >>(int shift) const;

1643 11 operator &= 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator &= 0 1 685 0
113
inline void DoubleBitMask< DoubleBitMaskNative >::operator &=(DoubleBitMask< DoubleBitMaskNative > const &other);

1644 11 operator |= 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator |= 0 1 686 0
113
inline void DoubleBitMask< DoubleBitMaskNative >::operator |=(DoubleBitMask< DoubleBitMaskNative > const &other);

1645 11 operator ^= 0 4 2007 49 DoubleBitMask< DoubleBitMaskNative >::operator ^= 0 1 687 0
113
inline void DoubleBitMask< DoubleBitMaskNative >::operator ^=(DoubleBitMask< DoubleBitMaskNative > const &other);

1646 12 operator <<= 0 4 2007 50 DoubleBitMask< DoubleBitMaskNative >::operator <<= 0 1 688 0
74
inline void DoubleBitMask< DoubleBitMaskNative >::operator <<=(int shift);

1647 12 operator >>= 0 4 2007 50 DoubleBitMask< DoubleBitMaskNative >::operator >>= 0 1 689 0
74
inline void DoubleBitMask< DoubleBitMaskNative >::operator >>=(int shift);

1648 10 __reduce__ 0 4 2007 48 DoubleBitMask< DoubleBitMaskNative >::__reduce__ 0 1 690 0
81
PyObject *DoubleBitMask< DoubleBitMaskNative >::__reduce__(PyObject *self) const;

1649 14 get_class_type 0 4 2007 52 DoubleBitMask< DoubleBitMaskNative >::get_class_type 0 1 691 0
77
static TypeHandle DoubleBitMask< DoubleBitMaskNative >::get_class_type(void);

1650 14 ~DoubleBitMask 0 516 2007 52 DoubleBitMask< DoubleBitMaskNative >::~DoubleBitMask 0 0 0
59
DoubleBitMask< DoubleBitMaskNative >::~DoubleBitMask(void);

1651 6 lstick 0 4 2010 21 GamepadButton::lstick 0 1 694 0
48
static ButtonHandle GamepadButton::lstick(void);

1652 6 rstick 0 4 2010 21 GamepadButton::rstick 0 1 695 0
48
static ButtonHandle GamepadButton::rstick(void);

1653 9 lshoulder 0 4 2010 24 GamepadButton::lshoulder 0 1 696 0
51
static ButtonHandle GamepadButton::lshoulder(void);

1654 9 rshoulder 0 4 2010 24 GamepadButton::rshoulder 0 1 697 0
51
static ButtonHandle GamepadButton::rshoulder(void);

1655 8 ltrigger 0 4 2010 23 GamepadButton::ltrigger 0 1 698 0
50
static ButtonHandle GamepadButton::ltrigger(void);

1656 8 rtrigger 0 4 2010 23 GamepadButton::rtrigger 0 1 699 0
50
static ButtonHandle GamepadButton::rtrigger(void);

1657 5 lgrip 0 4 2010 20 GamepadButton::lgrip 0 1 700 0
47
static ButtonHandle GamepadButton::lgrip(void);

1658 5 rgrip 0 4 2010 20 GamepadButton::rgrip 0 1 701 0
47
static ButtonHandle GamepadButton::rgrip(void);

1659 9 dpad_left 0 4 2010 24 GamepadButton::dpad_left 0 1 702 0
51
static ButtonHandle GamepadButton::dpad_left(void);

1660 10 dpad_right 0 4 2010 25 GamepadButton::dpad_right 0 1 703 0
52
static ButtonHandle GamepadButton::dpad_right(void);

1661 7 dpad_up 0 4 2010 22 GamepadButton::dpad_up 0 1 704 0
49
static ButtonHandle GamepadButton::dpad_up(void);

1662 9 dpad_down 0 4 2010 24 GamepadButton::dpad_down 0 1 705 0
51
static ButtonHandle GamepadButton::dpad_down(void);

1663 4 back 0 4 2010 19 GamepadButton::back 0 1 706 0
46
static ButtonHandle GamepadButton::back(void);

1664 5 guide 0 4 2010 20 GamepadButton::guide 0 1 707 0
47
static ButtonHandle GamepadButton::guide(void);

1665 5 start 0 4 2010 20 GamepadButton::start 0 1 708 0
47
static ButtonHandle GamepadButton::start(void);

1666 4 next 0 4 2010 19 GamepadButton::next 0 1 709 0
46
static ButtonHandle GamepadButton::next(void);

1667 8 previous 0 4 2010 23 GamepadButton::previous 0 1 710 0
50
static ButtonHandle GamepadButton::previous(void);

1668 6 face_a 0 4 2010 21 GamepadButton::face_a 0 1 711 0
48
static ButtonHandle GamepadButton::face_a(void);

1669 6 face_b 0 4 2010 21 GamepadButton::face_b 0 1 712 0
48
static ButtonHandle GamepadButton::face_b(void);

1670 6 face_c 0 4 2010 21 GamepadButton::face_c 0 1 713 0
48
static ButtonHandle GamepadButton::face_c(void);

1671 6 face_x 0 4 2010 21 GamepadButton::face_x 0 1 714 0
48
static ButtonHandle GamepadButton::face_x(void);

1672 6 face_y 0 4 2010 21 GamepadButton::face_y 0 1 715 0
48
static ButtonHandle GamepadButton::face_y(void);

1673 6 face_z 0 4 2010 21 GamepadButton::face_z 0 1 716 0
48
static ButtonHandle GamepadButton::face_z(void);

1674 6 face_1 0 4 2010 21 GamepadButton::face_1 0 1 717 0
48
static ButtonHandle GamepadButton::face_1(void);

1675 6 face_2 0 4 2010 21 GamepadButton::face_2 0 1 718 0
48
static ButtonHandle GamepadButton::face_2(void);

1676 7 trigger 0 4 2010 22 GamepadButton::trigger 0 1 719 74
// Flight stick buttons, takes zero-based index.  First is always trigger.
49
static ButtonHandle GamepadButton::trigger(void);

1677 8 joystick 0 4 2010 23 GamepadButton::joystick 0 1 720 169
/**
 * Returns the ButtonHandle associated with the particular numbered joystick
 * button (zero-based), if there is one, or ButtonHandle::none() if there is
 * not.
 */
63
static ButtonHandle GamepadButton::joystick(int button_number);

1678 6 hat_up 0 4 2010 21 GamepadButton::hat_up 0 1 721 0
48
static ButtonHandle GamepadButton::hat_up(void);

1679 8 hat_down 0 4 2010 23 GamepadButton::hat_down 0 1 722 0
50
static ButtonHandle GamepadButton::hat_down(void);

1680 8 hat_left 0 4 2010 23 GamepadButton::hat_left 0 1 723 0
50
static ButtonHandle GamepadButton::hat_left(void);

1681 9 hat_right 0 4 2010 24 GamepadButton::hat_right 0 1 724 0
51
static ButtonHandle GamepadButton::hat_right(void);

1682 13 GamepadButton 0 260 2010 28 GamepadButton::GamepadButton 0 2 692 693 0
122
inline GamepadButton::GamepadButton(void) = default;
inline GamepadButton::GamepadButton(GamepadButton const &) = default;

1683 14 ~GamepadButton 0 516 2010 29 GamepadButton::~GamepadButton 0 0 0
36
GamepadButton::~GamepadButton(void);

1684 9 ascii_key 0 4 2011 25 KeyboardButton::ascii_key 0 1 727 144
/**
 * Returns the ButtonHandle associated with the particular ASCII character, if
 * there is one, or ButtonHandle::none() if there is not.
 */
69
static ButtonHandle KeyboardButton::ascii_key(char ascii_equivalent);

1685 5 space 0 4 2011 21 KeyboardButton::space 0 1 728 0
48
static ButtonHandle KeyboardButton::space(void);

1686 9 backspace 0 4 2011 25 KeyboardButton::backspace 0 1 729 0
52
static ButtonHandle KeyboardButton::backspace(void);

1687 3 tab 0 4 2011 19 KeyboardButton::tab 0 1 730 0
46
static ButtonHandle KeyboardButton::tab(void);

1688 5 enter 0 4 2011 21 KeyboardButton::enter 0 1 731 0
48
static ButtonHandle KeyboardButton::enter(void);

1689 6 escape 0 4 2011 22 KeyboardButton::escape 0 1 732 0
49
static ButtonHandle KeyboardButton::escape(void);

1690 2 f1 0 4 2011 18 KeyboardButton::f1 0 1 733 0
45
static ButtonHandle KeyboardButton::f1(void);

1691 2 f2 0 4 2011 18 KeyboardButton::f2 0 1 734 0
45
static ButtonHandle KeyboardButton::f2(void);

1692 2 f3 0 4 2011 18 KeyboardButton::f3 0 1 735 0
45
static ButtonHandle KeyboardButton::f3(void);

1693 2 f4 0 4 2011 18 KeyboardButton::f4 0 1 736 0
45
static ButtonHandle KeyboardButton::f4(void);

1694 2 f5 0 4 2011 18 KeyboardButton::f5 0 1 737 0
45
static ButtonHandle KeyboardButton::f5(void);

1695 2 f6 0 4 2011 18 KeyboardButton::f6 0 1 738 0
45
static ButtonHandle KeyboardButton::f6(void);

1696 2 f7 0 4 2011 18 KeyboardButton::f7 0 1 739 0
45
static ButtonHandle KeyboardButton::f7(void);

1697 2 f8 0 4 2011 18 KeyboardButton::f8 0 1 740 0
45
static ButtonHandle KeyboardButton::f8(void);

1698 2 f9 0 4 2011 18 KeyboardButton::f9 0 1 741 0
45
static ButtonHandle KeyboardButton::f9(void);

1699 3 f10 0 4 2011 19 KeyboardButton::f10 0 1 742 0
46
static ButtonHandle KeyboardButton::f10(void);

1700 3 f11 0 4 2011 19 KeyboardButton::f11 0 1 743 0
46
static ButtonHandle KeyboardButton::f11(void);

1701 3 f12 0 4 2011 19 KeyboardButton::f12 0 1 744 0
46
static ButtonHandle KeyboardButton::f12(void);

1702 3 f13 0 4 2011 19 KeyboardButton::f13 0 1 745 59
// PC keyboards don't have these four buttons, but Macs do.
46
static ButtonHandle KeyboardButton::f13(void);

1703 3 f14 0 4 2011 19 KeyboardButton::f14 0 1 746 0
46
static ButtonHandle KeyboardButton::f14(void);

1704 3 f15 0 4 2011 19 KeyboardButton::f15 0 1 747 0
46
static ButtonHandle KeyboardButton::f15(void);

1705 3 f16 0 4 2011 19 KeyboardButton::f16 0 1 748 0
46
static ButtonHandle KeyboardButton::f16(void);

1706 4 left 0 4 2011 20 KeyboardButton::left 0 1 749 0
47
static ButtonHandle KeyboardButton::left(void);

1707 5 right 0 4 2011 21 KeyboardButton::right 0 1 750 0
48
static ButtonHandle KeyboardButton::right(void);

1708 2 up 0 4 2011 18 KeyboardButton::up 0 1 751 0
45
static ButtonHandle KeyboardButton::up(void);

1709 4 down 0 4 2011 20 KeyboardButton::down 0 1 752 0
47
static ButtonHandle KeyboardButton::down(void);

1710 7 page_up 0 4 2011 23 KeyboardButton::page_up 0 1 753 0
50
static ButtonHandle KeyboardButton::page_up(void);

1711 9 page_down 0 4 2011 25 KeyboardButton::page_down 0 1 754 0
52
static ButtonHandle KeyboardButton::page_down(void);

1712 4 home 0 4 2011 20 KeyboardButton::home 0 1 755 0
47
static ButtonHandle KeyboardButton::home(void);

1713 3 end 0 4 2011 19 KeyboardButton::end 0 1 756 0
46
static ButtonHandle KeyboardButton::end(void);

1714 6 insert 0 4 2011 22 KeyboardButton::insert 0 1 757 0
49
static ButtonHandle KeyboardButton::insert(void);

1715 3 del 0 4 2011 19 KeyboardButton::del 0 1 758 27
// delete is a C++ keyword.
46
static ButtonHandle KeyboardButton::del(void);

1716 4 help 0 4 2011 20 KeyboardButton::help 0 1 759 27
// delete is a C++ keyword.
47
static ButtonHandle KeyboardButton::help(void);

1717 4 menu 0 4 2011 20 KeyboardButton::menu 0 1 760 0
47
static ButtonHandle KeyboardButton::menu(void);

1718 5 shift 0 4 2011 21 KeyboardButton::shift 0 1 761 0
48
static ButtonHandle KeyboardButton::shift(void);

1719 7 control 0 4 2011 23 KeyboardButton::control 0 1 762 0
50
static ButtonHandle KeyboardButton::control(void);

1720 3 alt 0 4 2011 19 KeyboardButton::alt 0 1 763 0
46
static ButtonHandle KeyboardButton::alt(void);

1721 4 meta 0 4 2011 20 KeyboardButton::meta 0 1 764 0
47
static ButtonHandle KeyboardButton::meta(void);

1722 9 caps_lock 0 4 2011 25 KeyboardButton::caps_lock 0 1 765 0
52
static ButtonHandle KeyboardButton::caps_lock(void);

1723 10 shift_lock 0 4 2011 26 KeyboardButton::shift_lock 0 1 766 0
53
static ButtonHandle KeyboardButton::shift_lock(void);

1724 8 num_lock 0 4 2011 24 KeyboardButton::num_lock 0 1 767 0
51
static ButtonHandle KeyboardButton::num_lock(void);

1725 11 scroll_lock 0 4 2011 27 KeyboardButton::scroll_lock 0 1 768 0
54
static ButtonHandle KeyboardButton::scroll_lock(void);

1726 12 print_screen 0 4 2011 28 KeyboardButton::print_screen 0 1 769 0
55
static ButtonHandle KeyboardButton::print_screen(void);

1727 5 pause 0 4 2011 21 KeyboardButton::pause 0 1 770 0
48
static ButtonHandle KeyboardButton::pause(void);

1728 6 lshift 0 4 2011 22 KeyboardButton::lshift 0 1 771 0
49
static ButtonHandle KeyboardButton::lshift(void);

1729 6 rshift 0 4 2011 22 KeyboardButton::rshift 0 1 772 0
49
static ButtonHandle KeyboardButton::rshift(void);

1730 8 lcontrol 0 4 2011 24 KeyboardButton::lcontrol 0 1 773 0
51
static ButtonHandle KeyboardButton::lcontrol(void);

1731 8 rcontrol 0 4 2011 24 KeyboardButton::rcontrol 0 1 774 0
51
static ButtonHandle KeyboardButton::rcontrol(void);

1732 4 lalt 0 4 2011 20 KeyboardButton::lalt 0 1 775 0
47
static ButtonHandle KeyboardButton::lalt(void);

1733 4 ralt 0 4 2011 20 KeyboardButton::ralt 0 1 776 0
47
static ButtonHandle KeyboardButton::ralt(void);

1734 5 lmeta 0 4 2011 21 KeyboardButton::lmeta 0 1 777 0
48
static ButtonHandle KeyboardButton::lmeta(void);

1735 5 rmeta 0 4 2011 21 KeyboardButton::rmeta 0 1 778 0
48
static ButtonHandle KeyboardButton::rmeta(void);

1736 14 KeyboardButton 0 260 2011 30 KeyboardButton::KeyboardButton 0 2 725 726 0
127
inline KeyboardButton::KeyboardButton(void) = default;
inline KeyboardButton::KeyboardButton(KeyboardButton const &) = default;

1737 15 ~KeyboardButton 0 516 2011 31 KeyboardButton::~KeyboardButton 0 0 0
38
KeyboardButton::~KeyboardButton(void);

1738 13 load_prc_file 0 1 0 13 load_prc_file 0 1 964 1206
/**
 * A convenience function for loading explicit prc files from a disk file or
 * from within a multifile (via the virtual file system).  Save the return
 * value and pass it to unload_prc_file() if you ever want to unload this file
 * later.
 *
 * The filename is first searched along the default prc search path, and then
 * also along the model path, for convenience.
 *
 * This function is defined in putil instead of in dtool with the read of the
 * prc stuff, so that it can take advantage of the virtual file system (which
 * is defined in express), and the model path (which is in putil).
 */

/**
 * A convenience function for loading explicit prc files from a disk file or
 * from within a multifile (via the virtual file system).  Save the return
 * value and pass it to unload_prc_file() if you ever want to unload this file
 * later.
 *
 * The filename is first searched along the default prc search path, and then
 * also along the model path, for convenience.
 *
 * This function is defined in putil instead of in dtool with the read of the
 * prc stuff, so that it can take advantage of the virtual file system (which
 * is defined in express), and the model path (which is in putil).
 */
52
ConfigPage *load_prc_file(Filename const &filename);

1739 18 load_prc_file_data 0 1 0 18 load_prc_file_data 0 1 965 794
/**
 * Another convenience function to load a prc file from an explicit string,
 * which represents the contents of the prc file.
 *
 * The first parameter is an arbitrary name to assign to this in-memory prc
 * file.  Supply a filename if the data was read from a file, or use any other
 * name that is meaningful to you.  The name is only used when the set of
 * loaded prc files is listed.
 */

/**
 * Another convenience function to load a prc file from an explicit string,
 * which represents the contents of the prc file.
 *
 * The first parameter is an arbitrary name to assign to this in-memory prc
 * file.  Supply a filename if the data was read from a file, or use any other
 * name that is meaningful to you.  The name is only used when the set of
 * loaded prc files is listed.
 */
81
ConfigPage *load_prc_file_data(std::string const &name, std::string const &data);

1740 15 unload_prc_file 0 1 0 15 unload_prc_file 0 1 966 0
39
bool unload_prc_file(ConfigPage *page);

1741 18 hash_prc_variables 0 1 0 18 hash_prc_variables 0 1 967 0
39
void hash_prc_variables(HashVal &hash);

1742 15 ModifierButtons 0 260 2012 32 ModifierButtons::ModifierButtons 0 2 779 780 22
/**
 *
 */

/**
 *
 */
102
ModifierButtons::ModifierButtons(void);
ModifierButtons::ModifierButtons(ModifierButtons const &copy);

1743 16 ~ModifierButtons 0 516 2012 33 ModifierButtons::~ModifierButtons 0 0 10
/**
 *
 */
40
ModifierButtons::~ModifierButtons(void);

1744 10 operator = 0 4 2012 27 ModifierButtons::operator = 0 1 781 0
69
inline void ModifierButtons::operator =(ModifierButtons const &copy);

1745 11 operator == 0 4 2012 28 ModifierButtons::operator == 0 1 782 0
77
inline bool ModifierButtons::operator ==(ModifierButtons const &other) const;

1746 11 operator != 0 4 2012 28 ModifierButtons::operator != 0 1 783 0
77
inline bool ModifierButtons::operator !=(ModifierButtons const &other) const;

1747 10 operator < 0 4 2012 27 ModifierButtons::operator < 0 1 784 0
76
inline bool ModifierButtons::operator <(ModifierButtons const &other) const;

1748 10 operator & 0 4 2012 27 ModifierButtons::operator & 0 1 785 0
87
inline ModifierButtons ModifierButtons::operator &(ModifierButtons const &other) const;

1749 10 operator | 0 4 2012 27 ModifierButtons::operator | 0 1 786 0
87
inline ModifierButtons ModifierButtons::operator |(ModifierButtons const &other) const;

1750 11 operator &= 0 4 2012 28 ModifierButtons::operator &= 0 1 787 0
64
void ModifierButtons::operator &=(ModifierButtons const &other);

1751 11 operator |= 0 4 2012 28 ModifierButtons::operator |= 0 1 788 0
64
void ModifierButtons::operator |=(ModifierButtons const &other);

1752 15 set_button_list 0 4 2012 32 ModifierButtons::set_button_list 0 1 789 374
/**
 * Sets the list of buttons to watch to be the same as that of the other
 * ModifierButtons object.  This makes the lists pointer equivalent (until one
 * or the other is later modified).
 *
 * This will preserve the state of any button that was on the original list
 * and is also on the new lists.  Any other buttons will get reset to the
 * default state of "up".
 */
68
void ModifierButtons::set_button_list(ModifierButtons const &other);

1753 7 matches 0 4 2012 24 ModifierButtons::matches 0 1 790 232
/**
 * Returns true if the set of buttons indicated as down by this
 * ModifierButtons object is the same set of buttons indicated as down by the
 * other ModifierButtons object.  The buttons indicated as up are not
 * relevant.
 */
66
bool ModifierButtons::matches(ModifierButtons const &other) const;

1754 10 add_button 0 4 2012 27 ModifierButtons::add_button 0 1 791 248
/**
 * Adds the indicated button to the set of buttons that will be monitored for
 * upness and downness.  Returns true if the button was added, false if it was
 * already being monitored or if too many buttons are currently being
 * monitored.
 */
54
bool ModifierButtons::add_button(ButtonHandle button);

1755 10 has_button 0 4 2012 27 ModifierButtons::has_button 0 1 792 109
/**
 * Returns true if the indicated button is in the set of buttons being
 * monitored, false otherwise.
 */
60
bool ModifierButtons::has_button(ButtonHandle button) const;

1756 13 remove_button 0 4 2012 30 ModifierButtons::remove_button 0 1 793 313
/**
 * Removes the indicated button from the set of buttons being monitored.
 * Returns true if the button was removed, false if it was not being monitored
 * in the first place.
 *
 * Unlike the other methods, you cannot remove a button by removing its alias;
 * you have to remove exactly the button itself.
 */
57
bool ModifierButtons::remove_button(ButtonHandle button);

1757 15 get_num_buttons 0 4 2012 32 ModifierButtons::get_num_buttons 0 1 794 143
/**
 * Returns the number of buttons that the ModifierButtons object is monitoring
 * (e.g.  the number of buttons passed to add_button()).
 */
56
inline int ModifierButtons::get_num_buttons(void) const;

1758 10 get_button 0 4 2012 27 ModifierButtons::get_button 0 1 795 185
/**
 * Returns the nth button that the ModifierButtons object is monitoring (the
 * nth button passed to add_button()).  This must be in the range 0 <= index <
 * get_num_buttons().
 */
65
inline ButtonHandle ModifierButtons::get_button(int index) const;

1759 11 button_down 0 4 2012 28 ModifierButtons::button_down 0 1 796 305
/**
 * Records that a particular button has been pressed.  If the given button is
 * one of the buttons that is currently being monitored, this will update the
 * internal state appropriately; otherwise, it will do nothing.  Returns true
 * if the button is one that was monitored, or false otherwise.
 */
55
bool ModifierButtons::button_down(ButtonHandle button);

1760 9 button_up 0 4 2012 26 ModifierButtons::button_up 0 1 797 306
/**
 * Records that a particular button has been released.  If the given button is
 * one of the buttons that is currently being monitored, this will update the
 * internal state appropriately; otherwise, it will do nothing.  Returns true
 * if the button is one that was monitored, or false otherwise.
 */
53
bool ModifierButtons::button_up(ButtonHandle button);

1761 14 all_buttons_up 0 4 2012 31 ModifierButtons::all_buttons_up 0 1 798 66
/**
 * Marks all monitored buttons as being in the "up" state.
 */
50
inline void ModifierButtons::all_buttons_up(void);

1762 7 is_down 0 4 2012 24 ModifierButtons::is_down 0 2 799 800 264
/**
 * Returns true if the indicated button is known to be down, or false if it is
 * known to be up.
 */

/**
 * Returns true if the indicated button is known to be down, or false if it is
 * known to be up or if it is not in the set of buttons being tracked.
 */
112
bool ModifierButtons::is_down(ButtonHandle button) const;
inline bool ModifierButtons::is_down(int index) const;

1763 11 is_any_down 0 4 2012 28 ModifierButtons::is_any_down 0 1 801 109
/**
 * Returns true if any of the tracked button are known to be down, or false if
 * all of them are up.
 */
53
inline bool ModifierButtons::is_any_down(void) const;

1764 10 get_prefix 0 4 2012 27 ModifierButtons::get_prefix 0 1 802 150
/**
 * Returns a string which can be used to prefix any button name or event name
 * with the unique set of modifier buttons currently being held.
 */
52
std::string ModifierButtons::get_prefix(void) const;

1765 6 output 0 4 2012 23 ModifierButtons::output 0 1 803 69
/**
 * Writes a one-line summary of the buttons known to be down.
 */
54
void ModifierButtons::output(std::ostream &out) const;

1766 5 write 0 4 2012 22 ModifierButtons::write 0 1 804 123
/**
 * Writes a multi-line summary including all of the buttons being monitored
 * and which ones are known to be down.
 */
53
void ModifierButtons::write(std::ostream &out) const;

1767 6 button 0 4 2013 19 MouseButton::button 0 1 807 166
/**
 * Returns the ButtonHandle associated with the particular numbered mouse
 * button (zero-based), if there is one, or ButtonHandle::none() if there is
 * not.
 */
59
static ButtonHandle MouseButton::button(int button_number);

1768 3 one 0 4 2013 16 MouseButton::one 0 1 808 75
/**
 * Returns the ButtonHandle associated with the first mouse button.
 */
43
static ButtonHandle MouseButton::one(void);

1769 3 two 0 4 2013 16 MouseButton::two 0 1 809 76
/**
 * Returns the ButtonHandle associated with the second mouse button.
 */
43
static ButtonHandle MouseButton::two(void);

1770 5 three 0 4 2013 18 MouseButton::three 0 1 810 75
/**
 * Returns the ButtonHandle associated with the third mouse button.
 */
45
static ButtonHandle MouseButton::three(void);

1771 4 four 0 4 2013 17 MouseButton::four 0 1 811 76
/**
 * Returns the ButtonHandle associated with the fourth mouse button.
 */
44
static ButtonHandle MouseButton::four(void);

1772 4 five 0 4 2013 17 MouseButton::five 0 1 812 75
/**
 * Returns the ButtonHandle associated with the fifth mouse button.
 */
44
static ButtonHandle MouseButton::five(void);

1773 8 wheel_up 0 4 2013 21 MouseButton::wheel_up 0 1 813 98
/**
 * Returns the ButtonHandle generated when the mouse wheel is rolled one notch
 * upwards.
 */
48
static ButtonHandle MouseButton::wheel_up(void);

1774 10 wheel_down 0 4 2013 23 MouseButton::wheel_down 0 1 814 100
/**
 * Returns the ButtonHandle generated when the mouse wheel is rolled one notch
 * downwards.
 */
50
static ButtonHandle MouseButton::wheel_down(void);

1775 10 wheel_left 0 4 2013 23 MouseButton::wheel_left 0 1 815 148
/**
 * Returns the ButtonHandle generated when the mouse is scrolled to the left.
 * Usually, you'll only find the horizontal scroll on laptops.
 */
50
static ButtonHandle MouseButton::wheel_left(void);

1776 11 wheel_right 0 4 2013 24 MouseButton::wheel_right 0 1 816 149
/**
 * Returns the ButtonHandle generated when the mouse is scrolled to the right.
 * Usually, you'll only find the horizontal scroll on laptops.
 */
51
static ButtonHandle MouseButton::wheel_right(void);

1777 15 is_mouse_button 0 4 2013 28 MouseButton::is_mouse_button 0 1 817 117
/**
 * Returns true if the indicated ButtonHandle is a mouse button, false if it
 * is some other kind of button.
 */
62
static bool MouseButton::is_mouse_button(ButtonHandle button);

1778 11 MouseButton 0 260 2013 24 MouseButton::MouseButton 0 2 805 806 0
112
inline MouseButton::MouseButton(void) = default;
inline MouseButton::MouseButton(MouseButton const &) = default;

1779 12 ~MouseButton 0 516 2013 25 MouseButton::~MouseButton 0 0 0
32
MouseButton::~MouseButton(void);

1780 5 get_x 0 4 2015 18 PointerData::get_x 0 1 820 10
/**
 *
 */
45
inline double PointerData::get_x(void) const;

1781 5 get_y 0 4 2015 18 PointerData::get_y 0 1 821 10
/**
 *
 */
45
inline double PointerData::get_y(void) const;

1782 13 get_in_window 0 4 2015 26 PointerData::get_in_window 0 1 822 157
/**
 * If this returns false, the pointer is not currently present in the window
 * and the values returned by get_x() and get_y() may not be meaningful.
 */
51
inline bool PointerData::get_in_window(void) const;

1783 8 get_type 0 4 2015 21 PointerData::get_type 0 0 47
/**
 * Returns the type of pointing device.
 */
53
inline PointerType PointerData::get_type(void) const;

1784 6 get_id 0 4 2015 19 PointerData::get_id 0 0 273
/**
 * Returns a unique identifier for this pointer.  This is for tracking
 * individual fingers.  This value should not be assumed to have a specific
 * meaning other than that there will not be two different pointers active
 * simultaneously with the same identifier.
 */
43
inline int PointerData::get_id(void) const;

1785 12 get_pressure 0 4 2015 25 PointerData::get_pressure 0 0 119
/**
 * Returns the pressure of the pointer.  For mice, this will be 1.0 if any
 * button is pressed, 0.0 otherwise.
 */
52
inline double PointerData::get_pressure(void) const;

1786 11 PointerData 0 260 2015 24 PointerData::PointerData 0 2 818 819 0
112
inline PointerData::PointerData(void) = default;
inline PointerData::PointerData(PointerData const &) = default;

1787 12 ~PointerData 0 516 2015 25 PointerData::~PointerData 0 0 0
32
PointerData::~PointerData(void);

1788 18 get_node_ref_count 0 4 2017 44 NodeCachedReferenceCount::get_node_ref_count 0 1 823 47
/**
 * Returns the current reference count.
 */
68
inline int NodeCachedReferenceCount::get_node_ref_count(void) const;

1789 8 node_ref 0 4 2017 34 NodeCachedReferenceCount::node_ref 0 1 824 349
/**
 * Explicitly increments the reference count.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 */
59
inline void NodeCachedReferenceCount::node_ref(void) const;

1790 10 node_unref 0 4 2017 36 NodeCachedReferenceCount::node_unref 0 1 825 201
/**
 * Explicitly decrements the node reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */
61
inline bool NodeCachedReferenceCount::node_unref(void) const;

1791 24 test_ref_count_integrity 0 4 2017 50 NodeCachedReferenceCount::test_ref_count_integrity 0 1 826 97
/**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */
75
inline bool NodeCachedReferenceCount::test_ref_count_integrity(void) const;

1792 19 get_referenced_bits 0 4 2017 45 NodeCachedReferenceCount::get_referenced_bits 0 1 827 293
/**
 * Returns the union of the values defined in the Referenced enum that
 * represents the various things that appear to be holding a pointer to this
 * object.
 *
 * If R_node is included, at least one node is holding a pointer; if R_cache
 * is included, at least one cache element is.
 */
69
inline int NodeCachedReferenceCount::get_referenced_bits(void) const;

1793 14 get_class_type 0 4 2017 40 NodeCachedReferenceCount::get_class_type 0 1 828 0
65
static TypeHandle NodeCachedReferenceCount::get_class_type(void);

1794 11 SparseArray 0 260 2019 24 SparseArray::SparseArray 0 3 829 830 831 22
/**
 *
 */

/**
 *
 */
150
inline SparseArray::SparseArray(void);
SparseArray::SparseArray(BitArray const &from);
inline SparseArray::SparseArray(SparseArray const &) = default;

1795 6 all_on 0 4 2019 19 SparseArray::all_on 0 1 832 72
/**
 * Returns a SparseArray with an infinite array of bits, all on.
 */
52
static inline SparseArray SparseArray::all_on(void);

1796 7 all_off 0 4 2019 20 SparseArray::all_off 0 1 833 56
/**
 * Returns a SparseArray whose bits are all off.
 */
53
static inline SparseArray SparseArray::all_off(void);

1797 8 lower_on 0 4 2019 21 SparseArray::lower_on 0 1 834 65
/**
 * Returns a SparseArray whose lower on_bits bits are on.
 */
61
static inline SparseArray SparseArray::lower_on(int on_bits);

1798 3 bit 0 4 2019 16 SparseArray::bit 0 1 835 64
/**
 * Returns a SparseArray with only the indicated bit on.
 */
54
static inline SparseArray SparseArray::bit(int index);

1799 5 range 0 4 2019 18 SparseArray::range 0 1 836 79
/**
 * Returns a SparseArray whose size bits, beginning at low_bit, are on.
 */
68
static inline SparseArray SparseArray::range(int low_bit, int size);

1800 16 has_max_num_bits 0 4 2019 29 SparseArray::has_max_num_bits 0 1 837 393
/**
 * Returns true if there is a maximum number of bits that may be stored in
 * this structure, false otherwise.  If this returns true, the number may be
 * queried in get_max_num_bits().
 *
 * This method always returns false.  The SparseArray has no maximum number of
 * bits.  This method is defined so generic programming algorithms can use
 * BitMask or SparseArray interchangeably.
 */
55
static inline bool SparseArray::has_max_num_bits(void);

1801 16 get_max_num_bits 0 4 2019 29 SparseArray::get_max_num_bits 0 1 838 428
/**
 * If get_max_num_bits() returned true, this method may be called to return
 * the maximum number of bits that may be stored in this structure.  It is an
 * error to call this if get_max_num_bits() return false.
 *
 * It is always an error to call this method.  The SparseArray has no maximum
 * number of bits.  This method is defined so generic programming algorithms
 * can use BitMask or SparseArray interchangeably.
 */
54
static inline int SparseArray::get_max_num_bits(void);

1802 12 get_num_bits 0 4 2019 25 SparseArray::get_num_bits 0 1 839 300
/**
 * Returns the current number of possibly different bits in this array.  There
 * are actually an infinite number of bits, but every bit higher than this bit
 * will have the same value, either 0 or 1 (see get_highest_bits()).
 *
 * This number may grow and/or shrink automatically as needed.
 */
49
inline int SparseArray::get_num_bits(void) const;

1803 7 get_bit 0 4 2019 20 SparseArray::get_bit 0 1 840 191
/**
 * Returns true if the nth bit is set, false if it is cleared.  It is valid
 * for n to increase beyond get_num_bits(), but the return value
 * get_num_bits() will always be the same.
 */
50
inline bool SparseArray::get_bit(int index) const;

1804 7 set_bit 0 4 2019 20 SparseArray::set_bit 0 1 841 97
/**
 * Sets the nth bit on.  If n >= get_num_bits(), this automatically extends
 * the array.
 */
44
inline void SparseArray::set_bit(int index);

1805 9 clear_bit 0 4 2019 22 SparseArray::clear_bit 0 1 842 98
/**
 * Sets the nth bit off.  If n >= get_num_bits(), this automatically extends
 * the array.
 */
46
inline void SparseArray::clear_bit(int index);

1806 10 set_bit_to 0 4 2019 23 SparseArray::set_bit_to 0 1 843 84
/**
 * Sets the nth bit either on or off, according to the indicated bool value.
 */
59
inline void SparseArray::set_bit_to(int index, bool value);

1807 16 get_highest_bits 0 4 2019 29 SparseArray::get_highest_bits 0 1 844 118
/**
 * Returns true if the infinite set of bits beyond get_num_bits() are all on,
 * or false of they are all off.
 */
54
inline bool SparseArray::get_highest_bits(void) const;

1808 7 is_zero 0 4 2019 20 SparseArray::is_zero 0 1 845 71
/**
 * Returns true if the entire bitmask is zero, false otherwise.
 */
45
inline bool SparseArray::is_zero(void) const;

1809 9 is_all_on 0 4 2019 22 SparseArray::is_all_on 0 1 846 70
/**
 * Returns true if the entire bitmask is one, false otherwise.
 */
47
inline bool SparseArray::is_all_on(void) const;

1810 10 has_any_of 0 4 2019 23 SparseArray::has_any_of 0 1 847 82
/**
 * Returns true if any bit in the indicated range is set, false otherwise.
 */
65
inline bool SparseArray::has_any_of(int low_bit, int size) const;

1811 10 has_all_of 0 4 2019 23 SparseArray::has_all_of 0 1 848 84
/**
 * Returns true if all bits in the indicated range are set, false otherwise.
 */
65
inline bool SparseArray::has_all_of(int low_bit, int size) const;

1812 9 set_range 0 4 2019 22 SparseArray::set_range 0 1 849 47
/**
 * Sets the indicated range of bits on.
 */
58
inline void SparseArray::set_range(int low_bit, int size);

1813 11 clear_range 0 4 2019 24 SparseArray::clear_range 0 1 850 48
/**
 * Sets the indicated range of bits off.
 */
60
inline void SparseArray::clear_range(int low_bit, int size);

1814 12 set_range_to 0 4 2019 25 SparseArray::set_range_to 0 1 851 64
/**
 * Sets the indicated range of bits to either on or off.
 */
73
inline void SparseArray::set_range_to(bool value, int low_bit, int size);

1815 15 get_num_on_bits 0 4 2019 28 SparseArray::get_num_on_bits 0 1 852 127
/**
 * Returns the number of bits that are set to 1 in the array.  Returns -1 if
 * there are an infinite number of 1 bits.
 */
45
int SparseArray::get_num_on_bits(void) const;

1816 16 get_num_off_bits 0 4 2019 29 SparseArray::get_num_off_bits 0 1 853 127
/**
 * Returns the number of bits that are set to 0 in the array.  Returns -1 if
 * there are an infinite number of 0 bits.
 */
46
int SparseArray::get_num_off_bits(void) const;

1817 17 get_lowest_on_bit 0 4 2019 30 SparseArray::get_lowest_on_bit 0 1 854 146
/**
 * Returns the index of the lowest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there are an infinite number of 1 bits.
 */
47
int SparseArray::get_lowest_on_bit(void) const;

1818 18 get_lowest_off_bit 0 4 2019 31 SparseArray::get_lowest_off_bit 0 1 855 146
/**
 * Returns the index of the lowest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there are an infinite number of 1 bits.
 */
48
int SparseArray::get_lowest_off_bit(void) const;

1819 18 get_highest_on_bit 0 4 2019 31 SparseArray::get_highest_on_bit 0 1 856 143
/**
 * Returns the index of the highest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there an infinite number of 1 bits.
 */
48
int SparseArray::get_highest_on_bit(void) const;

1820 19 get_highest_off_bit 0 4 2019 32 SparseArray::get_highest_off_bit 0 1 857 143
/**
 * Returns the index of the highest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there an infinite number of 1 bits.
 */
49
int SparseArray::get_highest_off_bit(void) const;

1821 29 get_next_higher_different_bit 0 4 2019 42 SparseArray::get_next_higher_different_bit 0 1 858 287
/**
 * Returns the index of the next bit in the array, above low_bit, whose value
 * is different that the value of low_bit.  Returns low_bit again if all bits
 * higher than low_bit have the same value.
 *
 * This can be used to quickly iterate through all of the bits in the array.
 */
66
int SparseArray::get_next_higher_different_bit(int low_bit) const;

1822 15 invert_in_place 0 4 2019 28 SparseArray::invert_in_place 0 1 859 93
/**
 * Inverts all the bits in the SparseArray.  This is equivalent to array =
 * ~array.
 */
47
inline void SparseArray::invert_in_place(void);

1823 18 has_bits_in_common 0 4 2019 31 SparseArray::has_bits_in_common 0 1 860 181
/**
 * Returns true if this SparseArray has any "one" bits in common with the
 * other one, false otherwise.
 *
 * This is equivalent to (array & other) != 0, but may be faster.
 */
69
bool SparseArray::has_bits_in_common(SparseArray const &other) const;

1824 5 clear 0 4 2019 18 SparseArray::clear 0 1 861 52
/**
 * Sets all the bits in the SparseArray off.
 */
37
inline void SparseArray::clear(void);

1825 6 output 0 4 2019 19 SparseArray::output 0 1 862 10
/**
 *
 */
50
void SparseArray::output(std::ostream &out) const;

1826 11 operator == 0 4 2019 24 SparseArray::operator == 0 1 863 0
69
inline bool SparseArray::operator ==(SparseArray const &other) const;

1827 11 operator != 0 4 2019 24 SparseArray::operator != 0 1 864 0
69
inline bool SparseArray::operator !=(SparseArray const &other) const;

1828 10 operator < 0 4 2019 23 SparseArray::operator < 0 1 865 0
68
inline bool SparseArray::operator <(SparseArray const &other) const;

1829 10 compare_to 0 4 2019 23 SparseArray::compare_to 0 1 866 242
/**
 * Returns a number less than zero if this SparseArray sorts before the
 * indicated other SparseArray, greater than zero if it sorts after, or 0 if
 * they are equivalent.  This is based on the same ordering defined by
 * operator <.
 */
60
int SparseArray::compare_to(SparseArray const &other) const;

1830 10 operator & 0 4 2019 23 SparseArray::operator & 0 1 867 0
75
inline SparseArray SparseArray::operator &(SparseArray const &other) const;

1831 10 operator | 0 4 2019 23 SparseArray::operator | 0 1 868 0
75
inline SparseArray SparseArray::operator |(SparseArray const &other) const;

1832 10 operator ^ 0 4 2019 23 SparseArray::operator ^ 0 1 869 0
75
inline SparseArray SparseArray::operator ^(SparseArray const &other) const;

1833 10 operator ~ 0 68 2019 23 SparseArray::operator ~ 0 1 870 0
55
inline SparseArray SparseArray::operator ~(void) const;

1834 11 operator << 0 4 2019 24 SparseArray::operator << 0 1 871 0
61
inline SparseArray SparseArray::operator <<(int shift) const;

1835 11 operator >> 0 4 2019 24 SparseArray::operator >> 0 1 872 0
61
inline SparseArray SparseArray::operator >>(int shift) const;

1836 11 operator &= 0 4 2019 24 SparseArray::operator &= 0 1 873 0
56
void SparseArray::operator &=(SparseArray const &other);

1837 11 operator |= 0 4 2019 24 SparseArray::operator |= 0 1 874 0
56
void SparseArray::operator |=(SparseArray const &other);

1838 11 operator ^= 0 4 2019 24 SparseArray::operator ^= 0 1 875 0
56
void SparseArray::operator ^=(SparseArray const &other);

1839 12 operator <<= 0 4 2019 25 SparseArray::operator <<= 0 1 876 0
49
inline void SparseArray::operator <<=(int shift);

1840 12 operator >>= 0 4 2019 25 SparseArray::operator >>= 0 1 877 0
49
inline void SparseArray::operator >>=(int shift);

1841 10 is_inverse 0 4 2019 23 SparseArray::is_inverse 0 1 878 330
/**
 * If this is true, the SparseArray is actually defined as a list of subranges
 * of integers that are *not* in the set.  If this is false (the default),
 * then the subranges define the integers that *are* in the set.  This affects
 * the interpretation of the values returned by iterating through
 * get_num_subranges().
 */
48
inline bool SparseArray::is_inverse(void) const;

1842 17 get_num_subranges 0 4 2019 30 SparseArray::get_num_subranges 0 1 879 237
/**
 * Returns the number of separate subranges stored in the SparseArray.  You
 * can use this limit to iterate through the subranges, calling
 * get_subrange_begin() and get_subrange_end() for each one.
 *
 * Also see is_inverse().
 */
62
inline std::size_t SparseArray::get_num_subranges(void) const;

1843 18 get_subrange_begin 0 4 2019 31 SparseArray::get_subrange_begin 0 1 880 94
/**
 * Returns the first numeric element in the nth subrange.
 *
 * Also see is_inverse().
 */
64
inline int SparseArray::get_subrange_begin(std::size_t n) const;

1844 16 get_subrange_end 0 4 2019 29 SparseArray::get_subrange_end 0 1 881 104
/**
 * Returns the last numeric element, plus one, in the nth subrange.
 *
 * Also see is_inverse().
 */
62
inline int SparseArray::get_subrange_end(std::size_t n) const;

1845 12 __getstate__ 0 4 2019 25 SparseArray::__getstate__ 0 1 882 0
48
PyObject *SparseArray::__getstate__(void) const;

1846 12 __setstate__ 0 4 2019 25 SparseArray::__setstate__ 0 1 883 0
48
void SparseArray::__setstate__(PyObject *state);

1847 14 get_class_type 0 4 2019 27 SparseArray::get_class_type 0 1 884 0
52
static TypeHandle SparseArray::get_class_type(void);

1848 12 ~SparseArray 0 516 2019 25 SparseArray::~SparseArray 0 0 0
32
SparseArray::~SparseArray(void);

1849 14 get_value_type 0 6 2020 30 ParamValueBase::get_value_type 0 1 885 52
/**
 * Returns the type of the underlying value.
 */
69
virtual inline TypeHandle ParamValueBase::get_value_type(void) const;

1850 6 output 0 6 2020 22 ParamValueBase::output 0 1 886 0
65
virtual void ParamValueBase::output(std::ostream &out) const = 0;

1851 14 get_class_type 0 4 2020 30 ParamValueBase::get_class_type 0 1 887 0
55
static TypeHandle ParamValueBase::get_class_type(void);

1852 18 ParamTypedRefCount 0 260 2021 38 ParamTypedRefCount::ParamTypedRefCount 0 1 888 10
/**
 *
 */
80
inline ParamTypedRefCount::ParamTypedRefCount(TypedReferenceCount const *value);

1853 9 get_value 0 4 2021 29 ParamTypedRefCount::get_value 0 1 889 55
/**
 * Retrieves the value stored in the parameter.
 */
70
inline TypedReferenceCount *ParamTypedRefCount::get_value(void) const;

1854 14 get_class_type 0 4 2021 34 ParamTypedRefCount::get_class_type 0 1 890 0
59
static TypeHandle ParamTypedRefCount::get_class_type(void);

1855 10 ParamValue 0 260 2023 32 ParamValue< string >::ParamValue 0 1 891 0
66
inline ParamValue< string >::ParamValue(std::string const &value);

1856 9 set_value 0 4 2023 31 ParamValue< string >::set_value 0 1 892 0
134
inline void ParamValue< string >::set_value(std::string const &value);
inline void ParamValue< string >::set_value(Type const &value);

1857 9 get_value 0 4 2023 31 ParamValue< string >::get_value 0 1 893 0
70
inline std::string const &ParamValue< string >::get_value(void) const;

1858 14 get_class_type 0 4 2023 36 ParamValue< string >::get_class_type 0 1 894 0
61
static TypeHandle ParamValue< string >::get_class_type(void);

1859 10 ParamValue 0 260 2026 33 ParamValue< wstring >::ParamValue 0 1 895 0
68
inline ParamValue< wstring >::ParamValue(std::wstring const &value);

1860 9 set_value 0 4 2026 32 ParamValue< wstring >::set_value 0 1 896 0
137
inline void ParamValue< wstring >::set_value(std::wstring const &value);
inline void ParamValue< wstring >::set_value(Type const &value);

1861 9 get_value 0 4 2026 32 ParamValue< wstring >::get_value 0 1 897 0
72
inline std::wstring const &ParamValue< wstring >::get_value(void) const;

1862 14 get_class_type 0 4 2026 37 ParamValue< wstring >::get_class_type 0 1 898 0
62
static TypeHandle ParamValue< wstring >::get_class_type(void);

1863 10 ParamValue 0 260 2028 36 ParamValue< LVecBase2d >::ParamValue 0 1 899 0
69
inline ParamValue< LVecBase2d >::ParamValue(LVecBase2d const &value);

1864 9 set_value 0 4 2028 35 ParamValue< LVecBase2d >::set_value 0 1 900 0
141
inline void ParamValue< LVecBase2d >::set_value(LVecBase2d const &value);
inline void ParamValue< LVecBase2d >::set_value(Type const &value);

1865 9 get_value 0 4 2028 35 ParamValue< LVecBase2d >::get_value 0 1 901 0
73
inline LVecBase2d const &ParamValue< LVecBase2d >::get_value(void) const;

1866 14 get_class_type 0 4 2028 40 ParamValue< LVecBase2d >::get_class_type 0 1 902 0
65
static TypeHandle ParamValue< LVecBase2d >::get_class_type(void);

1867 10 ParamValue 0 260 2030 36 ParamValue< LVecBase2f >::ParamValue 0 1 903 0
69
inline ParamValue< LVecBase2f >::ParamValue(LVecBase2f const &value);

1868 9 set_value 0 4 2030 35 ParamValue< LVecBase2f >::set_value 0 1 904 0
141
inline void ParamValue< LVecBase2f >::set_value(LVecBase2f const &value);
inline void ParamValue< LVecBase2f >::set_value(Type const &value);

1869 9 get_value 0 4 2030 35 ParamValue< LVecBase2f >::get_value 0 1 905 0
73
inline LVecBase2f const &ParamValue< LVecBase2f >::get_value(void) const;

1870 14 get_class_type 0 4 2030 40 ParamValue< LVecBase2f >::get_class_type 0 1 906 0
65
static TypeHandle ParamValue< LVecBase2f >::get_class_type(void);

1871 10 ParamValue 0 260 2032 36 ParamValue< LVecBase2i >::ParamValue 0 1 907 0
69
inline ParamValue< LVecBase2i >::ParamValue(LVecBase2i const &value);

1872 9 set_value 0 4 2032 35 ParamValue< LVecBase2i >::set_value 0 1 908 0
141
inline void ParamValue< LVecBase2i >::set_value(LVecBase2i const &value);
inline void ParamValue< LVecBase2i >::set_value(Type const &value);

1873 9 get_value 0 4 2032 35 ParamValue< LVecBase2i >::get_value 0 1 909 0
73
inline LVecBase2i const &ParamValue< LVecBase2i >::get_value(void) const;

1874 14 get_class_type 0 4 2032 40 ParamValue< LVecBase2i >::get_class_type 0 1 910 0
65
static TypeHandle ParamValue< LVecBase2i >::get_class_type(void);

1875 10 ParamValue 0 260 2034 36 ParamValue< LVecBase3d >::ParamValue 0 1 911 0
69
inline ParamValue< LVecBase3d >::ParamValue(LVecBase3d const &value);

1876 9 set_value 0 4 2034 35 ParamValue< LVecBase3d >::set_value 0 1 912 0
141
inline void ParamValue< LVecBase3d >::set_value(LVecBase3d const &value);
inline void ParamValue< LVecBase3d >::set_value(Type const &value);

1877 9 get_value 0 4 2034 35 ParamValue< LVecBase3d >::get_value 0 1 913 0
73
inline LVecBase3d const &ParamValue< LVecBase3d >::get_value(void) const;

1878 14 get_class_type 0 4 2034 40 ParamValue< LVecBase3d >::get_class_type 0 1 914 0
65
static TypeHandle ParamValue< LVecBase3d >::get_class_type(void);

1879 10 ParamValue 0 260 2036 36 ParamValue< LVecBase3f >::ParamValue 0 1 915 0
69
inline ParamValue< LVecBase3f >::ParamValue(LVecBase3f const &value);

1880 9 set_value 0 4 2036 35 ParamValue< LVecBase3f >::set_value 0 1 916 0
141
inline void ParamValue< LVecBase3f >::set_value(LVecBase3f const &value);
inline void ParamValue< LVecBase3f >::set_value(Type const &value);

1881 9 get_value 0 4 2036 35 ParamValue< LVecBase3f >::get_value 0 1 917 0
73
inline LVecBase3f const &ParamValue< LVecBase3f >::get_value(void) const;

1882 14 get_class_type 0 4 2036 40 ParamValue< LVecBase3f >::get_class_type 0 1 918 0
65
static TypeHandle ParamValue< LVecBase3f >::get_class_type(void);

1883 10 ParamValue 0 260 2038 36 ParamValue< LVecBase3i >::ParamValue 0 1 919 0
69
inline ParamValue< LVecBase3i >::ParamValue(LVecBase3i const &value);

1884 9 set_value 0 4 2038 35 ParamValue< LVecBase3i >::set_value 0 1 920 0
141
inline void ParamValue< LVecBase3i >::set_value(LVecBase3i const &value);
inline void ParamValue< LVecBase3i >::set_value(Type const &value);

1885 9 get_value 0 4 2038 35 ParamValue< LVecBase3i >::get_value 0 1 921 0
73
inline LVecBase3i const &ParamValue< LVecBase3i >::get_value(void) const;

1886 14 get_class_type 0 4 2038 40 ParamValue< LVecBase3i >::get_class_type 0 1 922 0
65
static TypeHandle ParamValue< LVecBase3i >::get_class_type(void);

1887 10 ParamValue 0 260 2040 36 ParamValue< LVecBase4d >::ParamValue 0 1 923 0
69
inline ParamValue< LVecBase4d >::ParamValue(LVecBase4d const &value);

1888 9 set_value 0 4 2040 35 ParamValue< LVecBase4d >::set_value 0 1 924 0
141
inline void ParamValue< LVecBase4d >::set_value(LVecBase4d const &value);
inline void ParamValue< LVecBase4d >::set_value(Type const &value);

1889 9 get_value 0 4 2040 35 ParamValue< LVecBase4d >::get_value 0 1 925 0
73
inline LVecBase4d const &ParamValue< LVecBase4d >::get_value(void) const;

1890 14 get_class_type 0 4 2040 40 ParamValue< LVecBase4d >::get_class_type 0 1 926 0
65
static TypeHandle ParamValue< LVecBase4d >::get_class_type(void);

1891 10 ParamValue 0 260 2042 36 ParamValue< LVecBase4f >::ParamValue 0 1 927 0
69
inline ParamValue< LVecBase4f >::ParamValue(LVecBase4f const &value);

1892 9 set_value 0 4 2042 35 ParamValue< LVecBase4f >::set_value 0 1 928 0
141
inline void ParamValue< LVecBase4f >::set_value(LVecBase4f const &value);
inline void ParamValue< LVecBase4f >::set_value(Type const &value);

1893 9 get_value 0 4 2042 35 ParamValue< LVecBase4f >::get_value 0 1 929 0
73
inline LVecBase4f const &ParamValue< LVecBase4f >::get_value(void) const;

1894 14 get_class_type 0 4 2042 40 ParamValue< LVecBase4f >::get_class_type 0 1 930 0
65
static TypeHandle ParamValue< LVecBase4f >::get_class_type(void);

1895 10 ParamValue 0 260 2044 36 ParamValue< LVecBase4i >::ParamValue 0 1 931 0
69
inline ParamValue< LVecBase4i >::ParamValue(LVecBase4i const &value);

1896 9 set_value 0 4 2044 35 ParamValue< LVecBase4i >::set_value 0 1 932 0
141
inline void ParamValue< LVecBase4i >::set_value(LVecBase4i const &value);
inline void ParamValue< LVecBase4i >::set_value(Type const &value);

1897 9 get_value 0 4 2044 35 ParamValue< LVecBase4i >::get_value 0 1 933 0
73
inline LVecBase4i const &ParamValue< LVecBase4i >::get_value(void) const;

1898 14 get_class_type 0 4 2044 40 ParamValue< LVecBase4i >::get_class_type 0 1 934 0
65
static TypeHandle ParamValue< LVecBase4i >::get_class_type(void);

1899 10 ParamValue 0 260 2046 35 ParamValue< LMatrix3d >::ParamValue 0 1 935 0
67
inline ParamValue< LMatrix3d >::ParamValue(LMatrix3d const &value);

1900 9 set_value 0 4 2046 34 ParamValue< LMatrix3d >::set_value 0 1 936 0
138
inline void ParamValue< LMatrix3d >::set_value(LMatrix3d const &value);
inline void ParamValue< LMatrix3d >::set_value(Type const &value);

1901 9 get_value 0 4 2046 34 ParamValue< LMatrix3d >::get_value 0 1 937 0
71
inline LMatrix3d const &ParamValue< LMatrix3d >::get_value(void) const;

1902 14 get_class_type 0 4 2046 39 ParamValue< LMatrix3d >::get_class_type 0 1 938 0
64
static TypeHandle ParamValue< LMatrix3d >::get_class_type(void);

1903 10 ParamValue 0 260 2048 35 ParamValue< LMatrix3f >::ParamValue 0 1 939 0
67
inline ParamValue< LMatrix3f >::ParamValue(LMatrix3f const &value);

1904 9 set_value 0 4 2048 34 ParamValue< LMatrix3f >::set_value 0 1 940 0
138
inline void ParamValue< LMatrix3f >::set_value(LMatrix3f const &value);
inline void ParamValue< LMatrix3f >::set_value(Type const &value);

1905 9 get_value 0 4 2048 34 ParamValue< LMatrix3f >::get_value 0 1 941 0
71
inline LMatrix3f const &ParamValue< LMatrix3f >::get_value(void) const;

1906 14 get_class_type 0 4 2048 39 ParamValue< LMatrix3f >::get_class_type 0 1 942 0
64
static TypeHandle ParamValue< LMatrix3f >::get_class_type(void);

1907 10 ParamValue 0 260 2050 35 ParamValue< LMatrix4d >::ParamValue 0 1 943 0
67
inline ParamValue< LMatrix4d >::ParamValue(LMatrix4d const &value);

1908 9 set_value 0 4 2050 34 ParamValue< LMatrix4d >::set_value 0 1 944 0
138
inline void ParamValue< LMatrix4d >::set_value(LMatrix4d const &value);
inline void ParamValue< LMatrix4d >::set_value(Type const &value);

1909 9 get_value 0 4 2050 34 ParamValue< LMatrix4d >::get_value 0 1 945 0
71
inline LMatrix4d const &ParamValue< LMatrix4d >::get_value(void) const;

1910 14 get_class_type 0 4 2050 39 ParamValue< LMatrix4d >::get_class_type 0 1 946 0
64
static TypeHandle ParamValue< LMatrix4d >::get_class_type(void);

1911 10 ParamValue 0 260 2052 35 ParamValue< LMatrix4f >::ParamValue 0 1 947 0
67
inline ParamValue< LMatrix4f >::ParamValue(LMatrix4f const &value);

1912 9 set_value 0 4 2052 34 ParamValue< LMatrix4f >::set_value 0 1 948 0
138
inline void ParamValue< LMatrix4f >::set_value(LMatrix4f const &value);
inline void ParamValue< LMatrix4f >::set_value(Type const &value);

1913 9 get_value 0 4 2052 34 ParamValue< LMatrix4f >::get_value 0 1 949 0
71
inline LMatrix4f const &ParamValue< LMatrix4f >::get_value(void) const;

1914 14 get_class_type 0 4 2052 39 ParamValue< LMatrix4f >::get_class_type 0 1 950 0
64
static TypeHandle ParamValue< LMatrix4f >::get_class_type(void);

1915 14 get_class_type 0 4 2059 36 WritableConfigurable::get_class_type 0 1 951 0
61
static TypeHandle WritableConfigurable::get_class_type(void);

1916 21 ~WritableConfigurable 0 516 2059 43 WritableConfigurable::~WritableConfigurable 0 0 0
50
WritableConfigurable::~WritableConfigurable(void);

1917 17 UniqueIdAllocator 0 260 2060 36 UniqueIdAllocator::UniqueIdAllocator 0 2 952 953 56
/**
 * Create a free id pool in the range [min:max].
 */
165
explicit UniqueIdAllocator::UniqueIdAllocator(uint32_t min = 0, uint32_t max = 20);
inline UniqueIdAllocator::UniqueIdAllocator(UniqueIdAllocator const &) = default;

1918 18 ~UniqueIdAllocator 0 516 2060 37 UniqueIdAllocator::~UniqueIdAllocator 0 0 10
/**
 *
 */
44
UniqueIdAllocator::~UniqueIdAllocator(void);

1919 8 allocate 0 4 2060 27 UniqueIdAllocator::allocate 0 1 954 134
/**
 * Returns an id between _min and _max (that were passed to the constructor).
 * IndexEnd is returned if no ids are available.
 */
43
uint32_t UniqueIdAllocator::allocate(void);

1920 18 initial_reserve_id 0 4 2060 37 UniqueIdAllocator::initial_reserve_id 0 1 955 518
/**
 * This may be called to mark a particular id as having already been allocated
 * (for instance, by a prior pass).  The specified id is removed from the
 * available pool.
 *
 * Because of the limitations of this algorithm, this is most efficient when
 * it is called before the first call to allocate(), and when all the calls to
 * initial_reserve_id() are made in descending order by id.  However, this is
 * a performance warning only; if performance is not an issue, any id may be
 * reserved at any time.
 */
56
void UniqueIdAllocator::initial_reserve_id(uint32_t id);

1921 4 free 0 4 2060 23 UniqueIdAllocator::free 0 1 956 112
/**
 * Free an allocated index (index must be between _min and _max that were
 * passed to the constructor).
 */
45
void UniqueIdAllocator::free(uint32_t index);

1922 13 fraction_used 0 4 2060 32 UniqueIdAllocator::fraction_used 0 1 957 117
/**
 * return the decimal fraction of the pool that is used.  The range is 0 to
 * 1.0 (e.g.  75% would be 0.75).
 */
57
PN_stdfloat UniqueIdAllocator::fraction_used(void) const;

1923 6 output 0 4 2060 25 UniqueIdAllocator::output 0 1 958 42
/**
 * ...intended for debugging only.
 */
56
void UniqueIdAllocator::output(std::ostream &out) const;

1924 5 write 0 4 2060 24 UniqueIdAllocator::write 0 1 959 42
/**
 * ...intended for debugging only.
 */
55
void UniqueIdAllocator::write(std::ostream &out) const;

1925 39 py_decode_TypedWritable_from_bam_stream 0 1 0 39 py_decode_TypedWritable_from_bam_stream 0 1 968 203
/**
 * This wrapper is defined as a global function to suit pickle's needs.
 *
 * This hooks into the native pickle and cPickle modules, but it cannot
 * properly handle self-referential BAM objects.
 */
98
PyObject *py_decode_TypedWritable_from_bam_stream(PyObject *this_class, vector_uchar const &data);

1926 47 py_decode_TypedWritable_from_bam_stream_persist 0 1 0 47 py_decode_TypedWritable_from_bam_stream_persist 0 1 969 398
/**
 * This wrapper is defined as a global function to suit pickle's needs.
 *
 * This is similar to py_decode_TypedWritable_from_bam_stream, but it provides
 * additional support for the missing persistent-state object needed to
 * properly support self-referential BAM objects written to the pickle stream.
 * This hooks into the pickle and cPickle modules implemented in
 * direct/src/stdpy.
 */
127
PyObject *py_decode_TypedWritable_from_bam_stream_persist(PyObject *unpickler, PyObject *this_class, vector_uchar const &data);

969
1 0 0 15 10 2064 989 0 0 1 4 copy 1 2062  
2 0 0 23 10 2064 989 0 0 1 4 copy 1 2065  
3 0 0 4 11 2067 0 0 0 1 4 this 3 2064  
4 0 0 6 12 2068 0 0 0 1 4 this 3 2062  
5 0 0 6 13 2069 0 0 0 2 4 this 3 2062  1 n 1 2068  
6 0 0 38 14 2069 0 0 0 2 4 this 3 2062  1 n 1 2068  
7 0 0 38 15 1963 0 0 0 1 4 this 3 2062  
8 0 0 38 16 1963 0 0 0 3 4 this 3 2062  1 n 1 2068  5 count 1 2068  
9 0 0 6 17 1934 0 0 0 1 4 this 3 2062  
10 0 0 6 18 1934 0 0 0 1 4 this 3 2062  
11 0 0 6 19 2068 0 0 0 2 4 this 3 2062  6 param0 0 2069  
12 0 0 38 20 1963 0 0 0 1 4 this 3 2062  
13 0 0 38 21 1934 0 0 0 3 4 this 3 2062  4 view 1 2071  5 flags 1 1934  
14 0 0 36 22 2067 0 0 0 2 4 this 3 2062  4 view 1 2071  
15 0 0 39 23 2064 989 0 0 2 4 this 3 2062  4 memo 1 1963  
16 0 0 6 8 1935 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 2074  5 other 1 2074  
17 0 0 6 9 1935 0 0 0 2 4 this 3 2074  5 other 1 2074  
18 0 0 4 5 2067 0 0 0 1 4 this 3 2076  
19 0 0 4 6 2067 0 0 0 2 4 this 3 2077  3 out 1 2079  
20 0 0 15 26 2080 1011 0 0 1 4 copy 1 2065  
21 0 0 55 26 2080 1011 0 0 1 6 source 1 1963  
22 0 0 23 26 2080 1011 0 0 1 11 type_handle 5 2081  
23 0 0 7 27 2080 1011 0 0 2 1 n 1 2068  11 type_handle 5 2081  
24 0 0 4 28 2067 0 0 0 1 4 this 3 2080  
25 0 0 6 29 2068 0 0 0 1 4 this 3 2065  
26 0 0 4 30 2067 0 0 0 2 4 this 3 2080  1 x 1 2069  
27 0 0 4 31 2067 0 0 0 1 4 this 3 2080  
28 0 0 6 32 2069 0 0 0 2 4 this 3 2065  1 n 1 2068  
29 0 0 4 33 2067 0 0 0 3 4 this 3 2080  1 n 1 2068  5 value 1 2069  
30 0 0 38 34 2069 0 0 0 2 4 this 3 2065  1 n 1 2068  
31 0 0 36 35 2067 0 0 0 3 4 this 3 2080  1 n 1 2068  5 value 1 2069  
32 0 0 38 36 1963 0 0 0 1 4 this 3 2065  
33 0 0 36 37 2067 0 0 0 2 4 this 3 2080  4 data 1 1963  
34 0 0 38 38 1963 0 0 0 3 4 this 3 2065  1 n 1 2068  5 count 1 2068  
35 0 0 4 39 2067 0 0 0 4 4 this 3 2080  1 n 1 2068  5 count 1 2068  4 data 1 2083  
36 0 0 6 40 1934 0 0 0 1 4 this 3 2065  
37 0 0 6 41 1934 0 0 0 1 4 this 3 2065  
38 0 0 6 42 2068 0 0 0 2 4 this 3 2065  6 param0 0 2069  
39 0 0 38 43 1963 0 0 0 1 4 this 3 2065  
40 0 0 38 44 1934 0 0 0 3 4 this 3 2080  4 view 1 2071  5 flags 1 1934  
41 0 0 36 45 2067 0 0 0 2 4 this 3 2065  4 view 1 2071  
42 0 0 39 46 2080 1011 0 0 2 4 this 3 2065  4 memo 1 1963  
43 0 0 4 50 2067 0 0 69 /**
 * Runs the entire animation from beginning to end and stops.
 */ 1 4 this 3 2084  
44 0 0 4 50 2067 0 0 406 /**
 * Runs the animation from the frame "from" to and including the frame "to",
 * at which point the animation is stopped.  Both "from" and "to" frame
 * numbers may be outside the range (0, get_num_frames()) and the animation
 * will follow the range correctly, reporting numbers modulo get_num_frames().
 * For instance, play(0, get_num_frames() * 2) will play the animation twice
 * and then stop.
 */ 3 4 this 3 2084  4 from 1 1933  2 to 1 1933  
45 0 0 4 51 2067 0 0 169 /**
 * Starts the entire animation looping.  If restart is true, the animation is
 * restarted from the beginning; otherwise, it continues from the current
 * frame.
 */ 2 4 this 3 2084  7 restart 1 1935  
46 0 0 4 51 2067 0 0 221 /**
 * Loops the animation from the frame "from" to and including the frame "to",
 * indefinitely.  If restart is true, the animation is restarted from the
 * beginning; otherwise, it continues from the current frame.
 */ 4 4 this 3 2084  7 restart 1 1935  4 from 1 1933  2 to 1 1933  
47 0 0 4 52 2067 0 0 224 /**
 * Starts the entire animation bouncing back and forth between its first frame
 * and last frame.  If restart is true, the animation is restarted from the
 * beginning; otherwise, it continues from the current frame.
 */ 2 4 this 3 2084  7 restart 1 1935  
48 0 0 4 52 2067 0 0 143 /**
 * Loops the animation from the frame "from" to and including the frame "to",
 * and then back in the opposite direction, indefinitely.
 */ 4 4 this 3 2084  7 restart 1 1935  4 from 1 1933  2 to 1 1933  
49 0 0 4 53 2067 0 0 130 /**
 * Stops a currently playing or looping animation right where it is.  The
 * animation remains posed at the current frame.
 */ 1 4 this 3 2084  
50 0 0 4 54 2067 0 0 72 /**
 * Sets the animation to the indicated frame and holds it there.
 */ 2 4 this 3 2084  5 frame 1 1933  
51 0 0 4 55 2067 0 0 243 /**
 * Changes the rate at which the animation plays.  1.0 is the normal speed,
 * 2.0 is twice normal speed, and 0.5 is half normal speed.  0.0 is legal to
 * pause the animation, and a negative value will play the animation
 * backwards.
 */ 2 4 this 3 2084  9 play_rate 1 1933  
52 0 0 6 56 1933 0 0 79 /**
 * Returns the rate at which the animation plays.  See set_play_rate().
 */ 1 4 this 3 2085  
53 0 0 6 57 1933 0 0 234 /**
 * Returns the native frame rate of the animation.  This is the number of
 * frames per second that will elapse when the play_rate is set to 1.0.  It is
 * a fixed property of the animation and may not be adjusted by the user.
 */ 1 4 this 3 2085  
54 0 0 6 58 1934 0 0 278 /**
 * Returns the number of frames in the animation.  This is a property of the
 * animation and may not be directly adjusted by the user (although it may
 * change without warning with certain kinds of animations, since this is a
 * virtual method that may be overridden).
 */ 1 4 this 3 2085  
55 0 0 6 59 1934 0 0 116 /**
 * Returns the current integer frame number.  This number will be in the range
 * 0 <= f < get_num_frames().
 */ 1 4 this 3 2085  
56 0 0 6 60 1934 0 0 330 /**
 * Returns the current integer frame number + 1, constrained to the range 0 <=
 * f < get_num_frames().
 *
 * If the play mode is PM_play, this will clamp to the same value as
 * get_frame() at the end of the animation.  If the play mode is any other
 * value, this will wrap around to frame 0 at the end of the animation.
 */ 1 4 this 3 2085  
57 0 0 6 61 1933 0 0 314 /**
 * Returns the fractional part of the current frame.  Normally, this is in the
 * range 0.0 <= f < 1.0, but in the one special case of an animation playing
 * to its end frame and stopping, it might exactly equal 1.0.
 *
 * It will always be true that get_full_frame() + get_frac() ==
 * get_full_fframe().
 */ 1 4 this 3 2085  
58 0 0 6 62 1934 0 0 372 /**
 * Returns the current integer frame number.
 *
 * Unlike the value returned by get_frame(), this frame number may extend
 * beyond the range of get_num_frames() if the frame range passed to play(),
 * loop(), etc.  did.
 *
 * Unlike the value returned by get_full_fframe(), this return value will
 * never exceed the value passed to to_frame in the play() method.
 */ 1 4 this 3 2085  
59 0 0 6 63 1933 0 0 490 /**
 * Returns the current floating-point frame number.
 *
 * Unlike the value returned by get_frame(), this frame number may extend
 * beyond the range of get_num_frames() if the frame range passed to play(),
 * loop(), etc.  did.
 *
 * Unlike the value returned by get_full_frame(), this return value may equal
 * (to_frame + 1.0), when the animation has played to its natural end.
 * However, in this case the return value of get_full_frame() will be
 * to_frame, not (to_frame + 1).
 */ 1 4 this 3 2085  
60 0 0 6 64 1935 0 0 208 /**
 * Returns true if the animation is currently playing, false if it is stopped
 * (e.g.  because stop() or pose() was called, or because it reached the end
 * of the animation after play() was called).
 */ 1 4 this 3 2085  
61 0 0 4 65 2067 0 0 10 /**
 *
 */ 2 4 this 3 2085  3 out 1 2079  
62 0 0 7 78 2081 0 0 0 0 
63 0 0 7 81 2087 1050 0 55 /**
 * Creates an UpdateSeq in the 'initial' state.
 */ 0 
64 0 0 15 81 2087 1050 0 10 /**
 *
 */ 1 4 copy 1 2088  
65 0 0 7 82 2087 1050 0 0 0 
66 0 0 7 83 2087 1050 0 0 0 
67 0 0 7 84 2087 1050 0 0 0 
68 0 0 6 85 2087 0 0 10 /**
 *
 */ 2 4 this 3 2087  4 copy 1 2088  
69 0 0 4 86 2067 0 0 55 /**
 * Resets the UpdateSeq to the 'initial' state.
 */ 1 4 this 3 2087  
70 0 0 6 87 1935 0 0 67 /**
 * Returns true if the UpdateSeq is in the 'initial' state.
 */ 1 4 this 3 2088  
71 0 0 6 88 1935 0 0 63 /**
 * Returns true if the UpdateSeq is in the 'old' state.
 */ 1 4 this 3 2088  
72 0 0 6 89 1935 0 0 65 /**
 * Returns true if the UpdateSeq is in the 'fresh' state.
 */ 1 4 this 3 2088  
73 0 0 6 90 1935 0 0 105 /**
 * Returns true if the UpdateSeq is in any special states, i.e.  'initial',
 * 'old', or 'fresh'.
 */ 1 4 this 3 2088  
74 0 0 6 91 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
75 0 0 6 92 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
76 0 0 6 93 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
77 0 0 6 94 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
78 0 0 6 95 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
79 0 0 6 96 1935 0 0 0 2 4 this 3 2088  5 other 1 2088  
80 0 0 7 97 2087 1050 0 0 1 4 this 3 2087  
81 0 0 7 98 2087 1050 0 0 2 4 this 3 2087  6 param0 0 1934  
82 0 0 6 99 1938 0 0 107 /**
 * Returns the internal integer value associated with the UpdateSeq.  Useful
 * for debugging only.
 */ 1 4 this 3 2088  
83 0 0 4 104 2067 0 0 10 /**
 *
 */ 2 4 this 3 2088  3 out 1 2079  
84 0 0 4 108 2067 0 0 308 /**
 * This internal function is intended to be called by each class's
 * make_from_bam() method to read in all of the relevant data from the BamFile
 * for the new object.  It is also called directly by the BamReader to re-read
 * the data for an object that has been placed on the stream for an update.
 */ 3 4 this 3 1950  4 scan 1 2090  7 manager 1 2092  
85 0 0 4 109 2067 0 0 345 /**
 * Increments the bam_modified counter, so that this object will be
 * invalidated and retransmitted on any open bam streams.  This should
 * normally not need to be called by user code; it should be called internally
 * when the object has been changed in a way that legitimately requires its
 * retransmission to any connected clients.
 */ 1 4 this 3 1950  
86 0 0 7 110 2087 1050 0 140 /**
 * Returns the current bam_modified counter.  This counter is normally
 * incremented automatically whenever the object is modified.
 */ 1 4 this 3 2093  
87 0 0 38 111 1963 0 0 0 1 4 this 3 2093  
88 0 0 38 112 1963 0 0 0 2 4 this 3 2093  7 pickler 1 1963  
89 0 0 6 113 2000 0 0 423 /**
 * Converts the TypedWritable object into a single stream of data using a
 * BamWriter, and returns that data as a bytes object.  Returns an empty bytes
 * object on failure.
 *
 * This is a convenience method particularly useful for cases when you are
 * only serializing a single object.  If you have many objects to process, it
 * is more efficient to use the same BamWriter to serialize all of them
 * together.
 */ 1 4 this 3 2093  
90 0 0 6 113 1935 0 0 429 /**
 * Converts the TypedWritable object into a single stream of data using a
 * BamWriter, and stores that data in the indicated string.  Returns true on
 * success, false on failure.
 *
 * This is a convenience method particularly useful for cases when you are
 * only serializing a single object.  If you have many objects to process, it
 * is more efficient to use the same BamWriter to serialize all of them
 * together.
 */ 3 4 this 3 2093  4 data 1 2095  6 writer 5 2096  
91 0 0 7 115 2081 0 0 0 0 
92 0 0 7 122 2097 1065 0 491 /**
 * Reads the bytes created by a previous call to encode_to_bam_stream(), and
 * extracts and returns the single object on those bytes.  Returns NULL on
 * error.
 *
 * This method is intended to replace decode_raw_from_bam_stream() when you
 * know the stream in question returns an object of type
 * TypedWritableReferenceCount, allowing for easier reference count
 * management.  Note that the caller is still responsible for maintaining the
 * reference count on the return value.
 */ 2 4 data 1 2000  6 reader 5 2092  
93 0 0 7 123 2081 0 0 0 0 
94 0 0 6 117 1950 0 0 0 1 4 this 3 2097  
95 0 0 7 120 2098 0 0 0 1 4 this 3 2097  
96 0 0 7 132 2101 1070 0 179 /**
 * Returns a duplicate of the BamCacheRecord.  The duplicate will not have a
 * data pointer set, even though one may have been assigned to the original
 * via set_data().
 */ 1 4 this 3 2099  
97 0 0 6 133 1935 0 0 0 2 4 this 3 2099  5 other 1 2099  
98 0 0 6 134 2102 0 0 237 /**
 * Returns the full pathname to the source file that originally generated this
 * cache request.  In some cases, for instance in the case of a of a multipage
 * texture like "cube_#.png", this may not not a true filename on disk.
 */ 1 4 this 3 2099  
99 0 0 6 135 2102 0 0 230 /**
 * Returns the name of the cache file as hashed from the source_pathname.
 * This will be relative to the root of the cache directory, and it will not
 * include any suffixes that may be appended to resolve hash conflicts.
 */ 1 4 this 3 2099  
100 0 0 6 136 1949 0 0 204 /**
 * Returns the file timestamp of the original source file that generated this
 * cache record, if available.  In some cases the original file timestamp is
 * not available, and this will return 0.
 */ 1 4 this 3 2099  
101 0 0 6 137 1949 0 0 84 /**
 * Returns the time at which this particular record was recorded or updated.
 */ 1 4 this 3 2099  
102 0 0 6 145 1934 0 0 75 /**
 * Returns the number of source files that contribute to the cache.
 */ 1 4 this 3 2099  
103 0 0 6 146 2102 0 0 94 /**
 * Returns the full pathname of the nth source files that contributes to the
 * cache.
 */ 2 4 this 3 2099  1 n 1 1934  
104 0 0 6 147 1935 0 0 124 /**
 * Returns true if all of the dependent files are still the same as when the
 * cache was recorded, false otherwise.
 */ 1 4 this 3 2099  
105 0 0 4 148 2067 0 0 80 /**
 * Empties the list of files that contribute to the data in this record.
 */ 1 4 this 3 2101  
106 0 0 4 149 2067 0 0 225 /**
 * Adds the indicated file to the list of files that will be loaded to
 * generate the data in this record.  This should be called once for the
 * primary source file, and again for each secondary source file, if any.
 */ 2 4 this 3 2101  8 pathname 1 2102  
107 0 0 4 149 2067 0 0 82 /**
 * Variant of add_dependent_file that takes an already opened VirtualFile.
 */ 2 4 this 3 2101  4 file 1 2103  
108 0 0 6 150 1935 0 0 130 /**
 * Returns true if this cache record has an in-memory data object associated--
 * that is, the object stored in the cache.
 */ 1 4 this 3 2099  
109 0 0 4 151 2067 0 0 147 /**
 * Removes the in-memory data object associated with this record, if any.
 * This does not affect the on-disk representation of the record.
 */ 1 4 this 3 2101  
110 0 0 6 152 1950 0 0 139 /**
 * Returns a pointer to the data stored in the record, or NULL if there is no
 * data.  The pointer is not removed from the record.
 */ 1 4 this 3 2099  
111 0 0 4 154 2067 0 0 104 /**
 * This variant on set_data() is provided to easily pass objects deriving from
 * TypedWritable.
 */ 2 4 this 3 2101  3 ptr 1 1950  
112 0 0 4 154 2067 0 0 573 /**
 * Stores a new data object on the record.  You should pass the same pointer
 * twice, to both parameters; this allows the C++ typecasting to automatically
 * convert the pointer into both a TypedWritable and a ReferenceCount pointer,
 * so that the BamCacheRecord object can reliably manage the reference counts.
 *
 * You may pass 0 or NULL as the second parameter.  If you do this, the
 * BamCacheRecord will not manage the object's reference count; it will be up
 * to you to ensure the object is not deleted during the lifetime of the
 * BamCacheRecord object.
 */ 3 4 this 3 2101  3 ptr 1 1950  7 ref_ptr 1 2098  
113 0 0 4 154 2067 0 0 115 /**
 * This variant on set_data() is provided just to allow Python code to pass a
 * 0 as the second parameter.
 */ 3 4 this 3 2101  3 ptr 1 1950  5 dummy 1 1934  
114 0 0 4 154 2067 0 0 118 /**
 * This variant on set_data() is provided to easily pass objects deriving from
 * TypedWritableReferenceCount.
 */ 2 4 this 3 2101  3 ptr 1 2097  
115 0 0 4 157 2067 0 0 10 /**
 *
 */ 2 4 this 3 2099  3 out 1 2079  
116 0 0 4 158 2067 0 0 10 /**
 *
 */ 3 4 this 3 2099  3 out 1 2079  12 indent_level 5 1934  
117 0 0 7 159 2081 0 0 0 0 
118 0 0 7 126 2097 1065 0 0 1 4 this 3 2101  
119 0 0 7 161 2106 1091 0 10 /**
 *
 */ 0 
120 0 0 4 163 2067 0 0 347 /**
 * Changes the state of the active flag.  "active" means that the cache should
 * be consulted automatically on loads, "not active" means that objects should
 * be loaded directly without consulting the cache.
 *
 * This represents the global flag.  Also see the individual cache_models,
 * cache_textures, cache_compressed_textures flags.
 */ 2 4 this 3 2106  4 flag 1 1935  
121 0 0 6 164 1935 0 0 381 /**
 * Returns true if the BamCache is currently active, false if it is not.
 * "active" means that the cache should be consulted automatically on loads,
 * "not active" means that objects should be loaded directly without
 * consulting the cache.
 *
 * This represents the global flag.  Also see the individual cache_models,
 * cache_textures, cache_compressed_textures flags.
 */ 1 4 this 3 2107  
122 0 0 4 165 2067 0 0 118 /**
 * Indicates whether model files (e.g.  egg files and bam files) will be
 * stored in the cache, as bam files.
 */ 2 4 this 3 2106  4 flag 1 1935  
123 0 0 6 166 1935 0 0 172 /**
 * Returns whether model files (e.g.  egg files and bam files) will be stored
 * in the cache, as bam files.
 *
 * This also returns false if get_active() is false.
 */ 1 4 this 3 2107  
124 0 0 4 167 2067 0 0 101 /**
 * Indicates whether texture files will be stored in the cache, as
 * uncompressed txo files.
 */ 2 4 this 3 2106  4 flag 1 1935  
125 0 0 6 168 1935 0 0 174 /**
 * Returns whether texture files (e.g.  egg files and bam files) will be
 * stored in the cache, as txo files.
 *
 * This also returns false if get_active() is false.
 */ 1 4 this 3 2107  
126 0 0 4 169 2067 0 0 722 /**
 * Indicates whether compressed texture files will be stored in the cache, as
 * compressed txo files.  The compressed data may either be generated in-CPU,
 * via the squish library, or it may be extracted from the GSG after the
 * texture has been loaded.
 *
 * This may be set in conjunction with set_cache_textures(), or independently
 * of it.  If set_cache_textures() is true and this is false, all textures
 * will be cached in their uncompressed form.  If set_cache_textures() is
 * false and this is true, only compressed textures will be cached, and they
 * will be cached in their compressed form.  If both are true, all textures
 * will be cached, in their uncompressed or compressed form appropriately.
 */ 2 4 this 3 2106  4 flag 1 1935  
127 0 0 6 170 1935 0 0 202 /**
 * Returns whether compressed texture files will be stored in the cache, as
 * compressed txo files.  See set_cache_compressed_textures().
 *
 * This also returns false if get_active() is false.
 */ 1 4 this 3 2107  
128 0 0 4 171 2067 0 0 184 /**
 * Indicates whether compiled shader programs will be stored in the cache, as
 * binary .sho files.  This may not be supported by all shader languages or
 * graphics renderers.
 */ 2 4 this 3 2106  4 flag 1 1935  
129 0 0 6 172 1935 0 0 196 /**
 * Returns whether compiled shader programs will be stored in the cache, as
 * binary .txo files.  See set_cache_compiled_shaders().
 *
 * This also returns false if get_active() is false.
 */ 1 4 this 3 2107  
130 0 0 4 173 2067 0 0 372 /**
 * Changes the current root pathname of the cache.  This specifies where the
 * cache files are stored on disk.  This should name a directory that is on a
 * disk local to the machine (not on a network-mounted disk), for instance,
 * /tmp/panda-cache or /c/panda-cache.
 *
 * If the directory does not already exist, it will be created as a result of
 * this call.
 */ 2 4 this 3 2106  4 root 1 2102  
131 0 0 7 174 2109 0 0 75 /**
 * Returns the current root pathname of the cache.  See set_root().
 */ 1 4 this 3 2107  
132 0 0 4 175 2067 0 0 86 /**
 * Specifies the time in seconds between automatic flushes of the cache index.
 */ 2 4 this 3 2106  10 flush_time 1 1934  
133 0 0 6 176 1934 0 0 84 /**
 * Returns the time in seconds between automatic flushes of the cache index.
 */ 1 4 this 3 2107  
134 0 0 4 177 2067 0 0 430 /**
 * Specifies the maximum size, in kilobytes, which the cache is allowed to
 * grow to.  If a newly cached file would exceed this size, an older file is
 * removed from the cache.
 *
 * Note that in the case of multiple different processes simultaneously
 * operating on the same cache directory, the actual cache size may slightly
 * exceed this value from time to time due to latency in checking between the
 * processes.
 */ 2 4 this 3 2106  10 max_kbytes 1 1934  
135 0 0 6 178 1934 0 0 121 /**
 * Returns the maximum size, in kilobytes, which the cache is allowed to grow
 * to.  See set_cache_max_kbytes().
 */ 1 4 this 3 2107  
136 0 0 4 179 2067 0 0 252 /**
 * Can be used to put the cache in read-only mode, or take it out of read-only
 * mode.  Note that if you put it into read-write mode, and it discovers that
 * it does not have write access, it will put itself right back into read-only
 * mode.
 */ 2 4 this 3 2106  2 ro 1 1935  
137 0 0 6 180 1935 0 0 230 /**
 * Returns true if the cache is in read-only mode.  Normally, the cache starts
 * in read-write mode.  It can put itself into read-only mode automatically if
 * it discovers that it does not have write access to the cache.
 */ 1 4 this 3 2107  
138 0 0 7 181 2101 1070 0 782 /**
 * Looks up a file in the cache.
 *
 * If the file is cacheable, then regardless of whether the file is found in
 * the cache or not, this returns a BamCacheRecord.  On the other hand, if the
 * file cannot be cached, returns NULL.
 *
 * If record->has_data() returns true, then the file was found in the cache,
 * and you may call record->extract_data() to get the object.  If
 * record->has_data() returns false, then the file was not found in the cache
 * or the cache was stale; and you should reload the source file (calling
 * record->add_dependent_file() for each file loaded, including the original
 * source file), and then call record->set_data() to record the resulting
 * loaded object; and finally, you should call store() to write the cached
 * record to disk.
 */ 3 4 this 3 2106  15 source_filename 1 2102  15 cache_extension 1 2083  
139 0 0 6 182 1935 0 0 212 /**
 * Flushes a cache entry to disk.  You must have retrieved the cache record
 * via a prior call to lookup(), and then stored the data via
 * record->set_data().  Returns true on success, false on failure.
 */ 2 4 this 3 2106  6 record 1 2101  
140 0 0 4 183 2067 0 0 92 /**
 * Flushes the index if enough time has elapsed since the index was last
 * flushed.
 */ 1 4 this 3 2106  
141 0 0 4 184 2067 0 0 48 /**
 * Ensures the index is written to disk.
 */ 1 4 this 3 2106  
142 0 0 4 185 2067 0 0 63 /**
 * Writes the contents of the index to standard output.
 */ 3 4 this 3 2107  3 out 1 2079  12 indent_level 5 1934  
143 0 0 6 186 2106 0 0 124 /**
 * Returns a pointer to the global BamCache object, which is used
 * automatically by the ModelPool and TexturePool.
 */ 0 
144 0 0 4 187 2067 0 0 84 /**
 * If there is a global BamCache object, calls consider_flush_index() on it.
 */ 0 
145 0 0 4 188 2067 0 0 75 /**
 * If there is a global BamCache object, calls flush_index() on it.
 */ 0 
146 0 0 7 202 2110 1119 0 0 0 
147 0 0 15 202 2110 1119 0 0 1 6 param0 0 2111  
148 0 0 15 207 2114 1130 0 0 1 6 param0 0 2113  
149 0 0 23 207 2114 1130 0 10 /**
 *
 */ 1 5 flags 5 1934  
150 0 0 23 207 2114 1130 0 10 /**
 *
 */ 2 5 flags 1 1934  13 texture_flags 1 1934  
151 0 0 4 208 2067 0 0 10 /**
 *
 */ 2 4 this 3 2114  5 flags 1 1934  
152 0 0 6 209 1934 0 0 10 /**
 *
 */ 1 4 this 3 2113  
153 0 0 4 211 2067 0 0 10 /**
 *
 */ 2 4 this 3 2114  5 flags 1 1934  
154 0 0 6 212 1934 0 0 10 /**
 *
 */ 1 4 this 3 2113  
155 0 0 4 213 2067 0 0 459 /**
 * Specifies the expected number of views to load for the texture.  This is
 * ignored unless TF_multiview is included in texture_flags.  This must be
 * specified when loading a 3-d multiview texture or 2-d texture array, in
 * which case it is used to differentiate z levels from separate views; it
 * may be zero in the case of 2-d textures or cube maps, in which case the
 * number of views can be inferred from the number of images found on disk.
 */ 2 4 this 3 2114  9 num_views 1 1934  
156 0 0 6 214 1934 0 0 39 /**
 * See set_texture_num_views().
 */ 1 4 this 3 2113  
157 0 0 4 217 2067 0 0 209 /**
 * Set this flag to ATS_none, ATS_up, ATS_down, or ATS_pad to control how a
 * texture is scaled from disk when it is subsequently loaded.  Set it to
 * ATS_unspecified to restore the default behavior.
 */ 2 4 this 3 2114  5 scale 1 1936  
158 0 0 6 218 1936 0 0 40 /**
 * See set_auto_texture_scale().
 */ 1 4 this 3 2113  
159 0 0 4 220 2067 0 0 10 /**
 *
 */ 2 4 this 3 2113  3 out 1 2079  
160 0 0 7 223 2092 1132 0 38 // The primary interface for a caller. 1 6 source 5 1960  
161 0 0 4 225 2067 0 0 142 /**
 * Changes the source of future datagrams for this BamReader.  This also
 * implicitly calls init() if it has not already been called.
 */ 2 4 this 3 2092  6 source 1 1960  
162 0 0 6 226 1960 0 0 100 /**
 * Returns the current source of the BamReader as set by set_source() or the
 * constructor.
 */ 1 4 this 3 2092  
163 0 0 6 227 1935 0 0 210 /**
 * Initializes the BamReader prior to reading any objects from its source.
 * This includes reading the Bam header.
 *
 * This returns true if the BamReader successfully initialized, false
 * otherwise.
 */ 1 4 this 3 2092  
164 0 0 4 228 2067 0 0 1007 /**
 * Associates an arbitrary block of data with the indicated object (or NULL),
 * and the indicated name.
 *
 * This is intended to provide a place for temporary storage for objects
 * reading themselves from the bam file.  To use it, inherit from
 * BamReader::AuxData and store whatever data you like there.  Then associate
 * your AuxData with the object as it is being read with set_aux_data().  You
 * may later set the aux data to NULL to remove it; or it will automatically
 * be removed (and deleted) after finalize() is called for the object in
 * question.
 *
 * If the TypedWritable pointer is NULL, the the aux data is stored globally
 * for the BamReader in general.  This pointer is available to any bam
 * objects, and will not be automatically removed until the BamReader itself
 * destructs.
 *
 * In either case, the name is just an arbitrary user-defined key.  If there
 * is already a data pointer stored for the obj/name pair, that data pointer
 * will be replaced (and deleted).
 */ 4 4 this 3 2092  3 obj 1 1950  4 name 1 2083  4 data 1 2115  
165 0 0 7 229 2115 0 0 173 /**
 * Returns the pointer previously associated with the bam reader by a previous
 * call to set_aux_data(), or NULL if data with the indicated key has not been
 * set.
 */ 3 4 this 3 2117  3 obj 1 1950  4 name 1 2083  
166 0 0 6 230 2102 0 0 199 /**
 * If a BAM is a file, then the BamReader should contain the name of the file.
 * This enables the reader to interpret pathnames in the BAM as relative to
 * the directory containing the BAM.
 */ 1 4 this 3 2117  
167 0 0 6 231 2113 0 0 98 /**
 * Returns the LoaderOptions passed to the loader when the model was
 * requested, if any.
 */ 1 4 this 3 2117  
168 0 0 4 232 2067 0 0 58 /**
 * Specifies the LoaderOptions for this BamReader.
 */ 2 4 this 3 2092  7 options 1 2113  
169 0 0 6 233 1950 0 0 1059 /**
 * Reads a single object from the Bam file.  If the object type is known, a
 * new object of the appropriate type is created and returned; otherwise, NULL
 * is returned.  NULL is also returned when the end of the file is reached.
 * is_eof() may be called to differentiate between these two cases.
 *
 * This may be called repeatedly to extract out all the objects in the Bam
 * file, but typically (especially for scene graph files, indicated with the
 * .bam extension), only one object is retrieved directly from the Bam file:
 * the root of the scene graph.  The remaining objects will all be retrieved
 * recursively by the first object.
 *
 * Note that the object returned may not yet be complete.  In particular, some
 * of its pointers may not be filled in; you must call resolve() to fill in
 * all the available pointers before you can safely use any objects returned
 * by read_object().
 *
 * This flavor of read_object() requires the caller to know what type of
 * object it has received in order to properly manage the reference counts.
 */ 1 4 this 3 2092  
170 0 0 6 234 1935 0 0 138 /**
 * Returns true if the reader has reached end-of-file, false otherwise.  This
 * call is only valid after a call to read_object().
 */ 1 4 this 3 2117  
171 0 0 6 235 1935 0 0 566 /**
 * This may be called at any time during processing of the Bam file to resolve
 * all the known pointers so far.  It is usually called at the end of the
 * processing, after all objects have been read, which is generally the best
 * time to call it.
 *
 * This must be called at least once after reading a particular object via
 * read_object() in order to validate that object.
 *
 * The return value is true if all objects have been resolved, or false if
 * some objects are still outstanding (in which case you will need to call
 * resolve() again later).
 */ 1 4 this 3 2092  
172 0 0 6 236 1935 0 0 406 /**
 * Indicates that an object recently read from the bam stream should be
 * replaced with a new object.  Any future occurrences of the original object
 * in the stream will henceforth return the new object instead.
 *
 * The return value is true if the replacement was successfully made, or false
 * if the object was not read from the stream (or if change_pointer had
 * already been called on it).
 */ 3 4 this 3 2092  12 orig_pointer 1 2093  11 new_pointer 1 2093  
173 0 0 6 237 1934 0 0 81 /**
 * Returns the major version number of the Bam file currently being read.
 */ 1 4 this 3 2117  
174 0 0 6 238 1934 0 0 81 /**
 * Returns the minor version number of the Bam file currently being read.
 */ 1 4 this 3 2117  
175 0 0 6 239 1953 0 0 254 /**
 * Returns the endian preference indicated by the Bam file currently being
 * read.  This does not imply that every number is stored using the indicated
 * convention, but individual objects may choose to respect this flag when
 * recording data.
 */ 1 4 this 3 2117  
176 0 0 6 240 1935 0 0 220 /**
 * Returns true if the file stores all "standard" floats as 64-bit doubles, or
 * false if they are 32-bit floats.  This is determined by the compilation
 * flags of the version of Panda that generated this file.
 */ 1 4 this 3 2117  
177 0 0 6 241 1934 0 0 174 /**
 * Returns the major version number of Bam files supported by the current code
 * base.  This must match get_file_major_ver() in order to successfully read a
 * file.
 */ 1 4 this 3 2117  
178 0 0 6 242 1934 0 0 184 /**
 * Returns the minor version number of Bam files supported by the current code
 * base.  This must match or exceed get_file_minor_ver() in order to
 * successfully read a file.
 */ 1 4 this 3 2117  
179 0 0 38 243 1963 0 0 0 1 4 this 3 2117  
180 0 0 36 256 2067 0 0 0 2 6 handle 1 2081  4 func 1 1963  
181 0 0 15 258 2096 1154 0 0 1 6 param0 0 2119  
182 0 0 7 258 2096 1154 0 10 /**
 *
 */ 1 6 target 5 1967  
183 0 0 4 260 2067 0 0 153 /**
 * Changes the destination of future datagrams written by the BamWriter.  This
 * also implicitly calls init() if it has not already been called.
 */ 2 4 this 3 2096  6 target 1 1967  
184 0 0 6 261 1967 0 0 100 /**
 * Returns the current target of the BamWriter as set by set_target() or the
 * constructor.
 */ 1 4 this 3 2096  
185 0 0 6 262 1935 0 0 220 /**
 * Initializes the BamWriter prior to writing any objects to its output
 * stream.  This includes writing out the Bam header.
 *
 * This returns true if the BamWriter successfully initialized, false
 * otherwise.
 */ 1 4 this 3 2096  
186 0 0 6 263 2102 0 0 197 /**
 * If a BAM is a file, then the BamWriter should contain the name of the file.
 * This enables the writer to convert pathnames in the BAM to relative to the
 * directory containing the BAM.
 */ 1 4 this 3 2119  
187 0 0 6 264 1935 0 0 736 /**
 * Writes a single object to the Bam file, so that the
 * BamReader::read_object() can later correctly restore the object and all its
 * pointers.
 *
 * This implicitly also writes any additional objects this object references
 * (if they haven't already been written), so that pointers may be fully
 * resolved.
 *
 * This may be called repeatedly to write a sequence of objects to the Bam
 * file, but typically (especially for scene graph files, indicated with the
 * .bam extension), only one object is written directly from the Bam file: the
 * root of the scene graph.  The remaining objects will all be written
 * recursively by the first object.
 *
 * Returns true if the object is successfully written, false otherwise.
 */ 2 4 this 3 2096  3 obj 1 2093  
188 0 0 6 265 1935 0 0 170 /**
 * Returns true if the object has previously been written (or at least
 * requested to be written) to the bam file, or false if we've never heard of
 * it before.
 */ 2 4 this 3 2119  3 obj 1 2093  
189 0 0 4 266 2067 0 0 85 /**
 * Ensures that all data written thus far is manifested on the output stream.
 */ 1 4 this 3 2096  
190 0 0 6 267 1934 0 0 84 /**
 * Returns the major version number of the Bam file currently being written.
 */ 1 4 this 3 2119  
191 0 0 6 268 1934 0 0 84 /**
 * Returns the minor version number of the Bam file currently being written.
 */ 1 4 this 3 2119  
192 0 0 4 269 2067 0 0 232 /**
 * Changes the minor .bam version to write.  This should be called before
 * init().  Each Panda version has only a fairly narrow range of versions it
 * is able to write; consult the .bam documentation for more information.
 */ 2 4 this 3 2096  9 minor_ver 1 1934  
193 0 0 6 270 1953 0 0 257 /**
 * Returns the endian preference indicated by the Bam file currently being
 * written.  This does not imply that every number is stored using the
 * indicated convention, but individual objects may choose to respect this
 * flag when recording data.
 */ 1 4 this 3 2119  
194 0 0 6 271 1935 0 0 248 /**
 * Returns true if the file will store all "standard" floats as 64-bit
 * doubles, or false if they are 32-bit floats.  This isn't runtime settable;
 * it's based on the compilation flags of the version of Panda that generated
 * this file.
 */ 1 4 this 3 2119  
195 0 0 6 272 1955 0 0 196 /**
 * Returns the BamTextureMode preference indicated by the Bam file currently
 * being written.  Texture objects written to this Bam file will be encoded
 * according to the specified mode.
 */ 1 4 this 3 2119  
196 0 0 4 273 2067 0 0 312 /**
 * Changes the BamTextureMode preference for the Bam file currently being
 * written.  Texture objects written to this Bam file will be encoded
 * according to the specified mode.
 *
 * This should be called after the call to init(), or it will be overwritten
 * with the default mode in the config file.
 */ 2 4 this 3 2096  17 file_texture_mode 1 1955  
197 0 0 6 274 1950 0 0 165 /**
 * Returns the root node of the part of the scene graph we are currently
 * writing out.  This is used for determining what to make NodePaths relative
 * to.
 */ 1 4 this 3 2119  
198 0 0 4 275 2067 0 0 160 /**
 * Sets the root node of the part of the scene graph we are currently writing
 * out.  NodePaths written to this bam file will be relative to this node.
 */ 2 4 this 3 2096  9 root_node 1 1950  
199 0 0 7 287 2121 1235 0 0 0 
200 0 0 15 287 2121 1235 0 0 1 6 param0 0 2122  
201 0 0 23 287 2121 1235 0 0 1 10 init_value 1 2124  
202 0 0 7 288 2121 1235 0 0 0 
203 0 0 7 289 2121 1235 0 0 0 
204 0 0 7 290 2121 1235 0 0 1 7 on_bits 1 1934  
205 0 0 7 291 2121 1235 0 0 1 5 index 1 1934  
206 0 0 7 292 2121 1235 0 0 2 7 low_bit 1 1934  4 size 1 1934  
207 0 0 6 293 1935 0 0 0 0 
208 0 0 6 294 1934 0 0 0 0 
209 0 0 6 295 1934 0 0 0 1 4 this 3 2122  
210 0 0 6 296 1935 0 0 0 2 4 this 3 2122  5 index 1 1934  
211 0 0 4 297 2067 0 0 0 2 4 this 3 2121  5 index 1 1934  
212 0 0 4 298 2067 0 0 0 2 4 this 3 2121  5 index 1 1934  
213 0 0 4 299 2067 0 0 0 3 4 this 3 2121  5 index 1 1934  5 value 1 1935  
214 0 0 6 300 1935 0 0 0 1 4 this 3 2122  
215 0 0 6 301 1935 0 0 0 1 4 this 3 2122  
216 0 0 6 302 2124 0 0 0 3 4 this 3 2122  7 low_bit 1 1934  4 size 1 1934  
217 0 0 4 303 2067 0 0 0 4 4 this 3 2121  5 value 1 2124  7 low_bit 1 1934  4 size 1 1934  
218 0 0 6 304 1935 0 0 0 3 4 this 3 2122  7 low_bit 1 1934  4 size 1 1934  
219 0 0 6 305 1935 0 0 0 3 4 this 3 2122  7 low_bit 1 1934  4 size 1 1934  
220 0 0 4 306 2067 0 0 0 3 4 this 3 2121  7 low_bit 1 1934  4 size 1 1934  
221 0 0 4 307 2067 0 0 0 3 4 this 3 2121  7 low_bit 1 1934  4 size 1 1934  
222 0 0 4 308 2067 0 0 0 4 4 this 3 2121  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
223 0 0 6 309 2124 0 0 0 1 4 this 3 2122  
224 0 0 4 310 2067 0 0 0 2 4 this 3 2121  5 value 1 2124  
225 0 0 6 311 1934 0 0 0 1 4 this 3 2122  
226 0 0 6 312 1934 0 0 0 1 4 this 3 2122  
227 0 0 6 313 1934 0 0 0 1 4 this 3 2122  
228 0 0 6 314 1934 0 0 0 1 4 this 3 2122  
229 0 0 6 315 1934 0 0 0 1 4 this 3 2122  
230 0 0 6 316 1934 0 0 0 1 4 this 3 2122  
231 0 0 6 317 1934 0 0 0 2 4 this 3 2122  7 low_bit 1 1934  
232 0 0 4 318 2067 0 0 0 1 4 this 3 2121  
233 0 0 6 319 1935 0 0 0 2 4 this 3 2122  5 other 1 2122  
234 0 0 4 320 2067 0 0 0 1 4 this 3 2121  
235 0 0 4 321 2067 0 0 0 2 4 this 3 2122  3 out 1 2079  
236 0 0 4 322 2067 0 0 0 3 4 this 3 2122  3 out 1 2079  12 spaces_every 5 1934  
237 0 0 4 323 2067 0 0 0 3 4 this 3 2122  3 out 1 2079  12 spaces_every 5 1934  
238 0 0 4 324 2067 0 0 0 3 4 this 3 2122  3 out 1 2079  12 indent_level 5 1934  
239 0 0 6 325 1935 0 0 0 2 4 this 3 2122  5 other 1 2122  
240 0 0 6 326 1935 0 0 0 2 4 this 3 2122  5 other 1 2122  
241 0 0 6 327 1935 0 0 0 2 4 this 3 2122  5 other 1 2122  
242 0 0 6 328 1934 0 0 0 2 4 this 3 2122  5 other 1 2122  
243 0 0 7 329 2121 1235 0 0 2 4 this 3 2122  5 other 1 2122  
244 0 0 7 330 2121 1235 0 0 2 4 this 3 2122  5 other 1 2122  
245 0 0 7 331 2121 1235 0 0 2 4 this 3 2122  5 other 1 2122  
246 0 0 7 332 2121 1235 0 0 1 4 this 3 2122  
247 0 0 7 333 2121 1235 0 0 2 4 this 3 2122  5 shift 1 1934  
248 0 0 7 334 2121 1235 0 0 2 4 this 3 2122  5 shift 1 1934  
249 0 0 6 335 2121 0 0 0 2 4 this 3 2121  5 other 1 2122  
250 0 0 6 336 2121 0 0 0 2 4 this 3 2121  5 other 1 2122  
251 0 0 6 337 2121 0 0 0 2 4 this 3 2121  5 other 1 2122  
252 0 0 6 338 2121 0 0 0 2 4 this 3 2121  5 shift 1 1934  
253 0 0 6 339 2121 0 0 0 2 4 this 3 2121  5 shift 1 1934  
254 0 0 4 340 2067 0 0 0 1 4 this 3 2121  
255 0 0 4 341 2067 0 0 0 1 4 this 3 2121  
256 0 0 7 342 2121 1235 0 0 1 4 this 3 2122  
257 0 0 7 343 2121 1235 0 0 1 4 this 3 2122  
258 0 0 7 344 2121 1235 0 0 1 4 this 3 2122  
259 0 0 7 344 2121 1235 0 0 2 4 this 3 2122  5 other 1 2122  
260 0 0 7 344 2121 1235 0 0 2 4 this 3 2122  5 index 1 1934  
261 0 0 7 345 2121 1235 0 0 1 4 this 3 2122  
262 0 0 7 345 2121 1235 0 0 2 4 this 3 2122  5 other 1 2122  
263 0 0 7 345 2121 1235 0 0 2 4 this 3 2122  5 index 1 1934  
264 0 0 6 346 1934 0 0 0 1 4 this 3 2122  
265 0 0 6 347 1935 0 0 0 1 4 this 3 2122  
266 0 0 38 348 1963 0 0 0 1 4 this 3 2122  
267 0 0 7 349 2081 0 0 0 0 
268 0 0 7 353 2125 1299 0 0 0 
269 0 0 15 353 2125 1299 0 0 1 6 param0 0 2126  
270 0 0 23 353 2125 1299 0 0 1 10 init_value 1 2128  
271 0 0 7 354 2125 1299 0 0 0 
272 0 0 7 355 2125 1299 0 0 0 
273 0 0 7 356 2125 1299 0 0 1 7 on_bits 1 1934  
274 0 0 7 357 2125 1299 0 0 1 5 index 1 1934  
275 0 0 7 358 2125 1299 0 0 2 7 low_bit 1 1934  4 size 1 1934  
276 0 0 6 359 1935 0 0 0 0 
277 0 0 6 360 1934 0 0 0 0 
278 0 0 6 361 1934 0 0 0 1 4 this 3 2126  
279 0 0 6 362 1935 0 0 0 2 4 this 3 2126  5 index 1 1934  
280 0 0 4 363 2067 0 0 0 2 4 this 3 2125  5 index 1 1934  
281 0 0 4 364 2067 0 0 0 2 4 this 3 2125  5 index 1 1934  
282 0 0 4 365 2067 0 0 0 3 4 this 3 2125  5 index 1 1934  5 value 1 1935  
283 0 0 6 366 1935 0 0 0 1 4 this 3 2126  
284 0 0 6 367 1935 0 0 0 1 4 this 3 2126  
285 0 0 6 368 2128 0 0 0 3 4 this 3 2126  7 low_bit 1 1934  4 size 1 1934  
286 0 0 4 369 2067 0 0 0 4 4 this 3 2125  5 value 1 2128  7 low_bit 1 1934  4 size 1 1934  
287 0 0 6 370 1935 0 0 0 3 4 this 3 2126  7 low_bit 1 1934  4 size 1 1934  
288 0 0 6 371 1935 0 0 0 3 4 this 3 2126  7 low_bit 1 1934  4 size 1 1934  
289 0 0 4 372 2067 0 0 0 3 4 this 3 2125  7 low_bit 1 1934  4 size 1 1934  
290 0 0 4 373 2067 0 0 0 3 4 this 3 2125  7 low_bit 1 1934  4 size 1 1934  
291 0 0 4 374 2067 0 0 0 4 4 this 3 2125  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
292 0 0 6 375 2128 0 0 0 1 4 this 3 2126  
293 0 0 4 376 2067 0 0 0 2 4 this 3 2125  5 value 1 2128  
294 0 0 6 377 1934 0 0 0 1 4 this 3 2126  
295 0 0 6 378 1934 0 0 0 1 4 this 3 2126  
296 0 0 6 379 1934 0 0 0 1 4 this 3 2126  
297 0 0 6 380 1934 0 0 0 1 4 this 3 2126  
298 0 0 6 381 1934 0 0 0 1 4 this 3 2126  
299 0 0 6 382 1934 0 0 0 1 4 this 3 2126  
300 0 0 6 383 1934 0 0 0 2 4 this 3 2126  7 low_bit 1 1934  
301 0 0 4 384 2067 0 0 0 1 4 this 3 2125  
302 0 0 6 385 1935 0 0 0 2 4 this 3 2126  5 other 1 2126  
303 0 0 4 386 2067 0 0 0 1 4 this 3 2125  
304 0 0 4 387 2067 0 0 0 2 4 this 3 2126  3 out 1 2079  
305 0 0 4 388 2067 0 0 0 3 4 this 3 2126  3 out 1 2079  12 spaces_every 5 1934  
306 0 0 4 389 2067 0 0 0 3 4 this 3 2126  3 out 1 2079  12 spaces_every 5 1934  
307 0 0 4 390 2067 0 0 0 3 4 this 3 2126  3 out 1 2079  12 indent_level 5 1934  
308 0 0 6 391 1935 0 0 0 2 4 this 3 2126  5 other 1 2126  
309 0 0 6 392 1935 0 0 0 2 4 this 3 2126  5 other 1 2126  
310 0 0 6 393 1935 0 0 0 2 4 this 3 2126  5 other 1 2126  
311 0 0 6 394 1934 0 0 0 2 4 this 3 2126  5 other 1 2126  
312 0 0 7 395 2125 1299 0 0 2 4 this 3 2126  5 other 1 2126  
313 0 0 7 396 2125 1299 0 0 2 4 this 3 2126  5 other 1 2126  
314 0 0 7 397 2125 1299 0 0 2 4 this 3 2126  5 other 1 2126  
315 0 0 7 398 2125 1299 0 0 1 4 this 3 2126  
316 0 0 7 399 2125 1299 0 0 2 4 this 3 2126  5 shift 1 1934  
317 0 0 7 400 2125 1299 0 0 2 4 this 3 2126  5 shift 1 1934  
318 0 0 6 401 2125 0 0 0 2 4 this 3 2125  5 other 1 2126  
319 0 0 6 402 2125 0 0 0 2 4 this 3 2125  5 other 1 2126  
320 0 0 6 403 2125 0 0 0 2 4 this 3 2125  5 other 1 2126  
321 0 0 6 404 2125 0 0 0 2 4 this 3 2125  5 shift 1 1934  
322 0 0 6 405 2125 0 0 0 2 4 this 3 2125  5 shift 1 1934  
323 0 0 4 406 2067 0 0 0 1 4 this 3 2125  
324 0 0 4 407 2067 0 0 0 1 4 this 3 2125  
325 0 0 7 408 2125 1299 0 0 1 4 this 3 2126  
326 0 0 7 409 2125 1299 0 0 1 4 this 3 2126  
327 0 0 7 410 2125 1299 0 0 1 4 this 3 2126  
328 0 0 7 410 2125 1299 0 0 2 4 this 3 2126  5 other 1 2126  
329 0 0 7 410 2125 1299 0 0 2 4 this 3 2126  5 index 1 1934  
330 0 0 7 411 2125 1299 0 0 1 4 this 3 2126  
331 0 0 7 411 2125 1299 0 0 2 4 this 3 2126  5 other 1 2126  
332 0 0 7 411 2125 1299 0 0 2 4 this 3 2126  5 index 1 1934  
333 0 0 6 412 1934 0 0 0 1 4 this 3 2126  
334 0 0 6 413 1935 0 0 0 1 4 this 3 2126  
335 0 0 38 414 1963 0 0 0 1 4 this 3 2126  
336 0 0 7 415 2081 0 0 0 0 
337 0 0 7 419 2130 1363 0 0 0 
338 0 0 15 419 2130 1363 0 0 1 6 param0 0 2131  
339 0 0 23 419 2130 1363 0 0 1 10 init_value 1 2133  
340 0 0 7 420 2130 1363 0 0 0 
341 0 0 7 421 2130 1363 0 0 0 
342 0 0 7 422 2130 1363 0 0 1 7 on_bits 1 1934  
343 0 0 7 423 2130 1363 0 0 1 5 index 1 1934  
344 0 0 7 424 2130 1363 0 0 2 7 low_bit 1 1934  4 size 1 1934  
345 0 0 6 425 1935 0 0 0 0 
346 0 0 6 426 1934 0 0 0 0 
347 0 0 6 427 1934 0 0 0 1 4 this 3 2131  
348 0 0 6 428 1935 0 0 0 2 4 this 3 2131  5 index 1 1934  
349 0 0 4 429 2067 0 0 0 2 4 this 3 2130  5 index 1 1934  
350 0 0 4 430 2067 0 0 0 2 4 this 3 2130  5 index 1 1934  
351 0 0 4 431 2067 0 0 0 3 4 this 3 2130  5 index 1 1934  5 value 1 1935  
352 0 0 6 432 1935 0 0 0 1 4 this 3 2131  
353 0 0 6 433 1935 0 0 0 1 4 this 3 2131  
354 0 0 6 434 2133 0 0 0 3 4 this 3 2131  7 low_bit 1 1934  4 size 1 1934  
355 0 0 4 435 2067 0 0 0 4 4 this 3 2130  5 value 1 2133  7 low_bit 1 1934  4 size 1 1934  
356 0 0 6 436 1935 0 0 0 3 4 this 3 2131  7 low_bit 1 1934  4 size 1 1934  
357 0 0 6 437 1935 0 0 0 3 4 this 3 2131  7 low_bit 1 1934  4 size 1 1934  
358 0 0 4 438 2067 0 0 0 3 4 this 3 2130  7 low_bit 1 1934  4 size 1 1934  
359 0 0 4 439 2067 0 0 0 3 4 this 3 2130  7 low_bit 1 1934  4 size 1 1934  
360 0 0 4 440 2067 0 0 0 4 4 this 3 2130  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
361 0 0 6 441 2133 0 0 0 1 4 this 3 2131  
362 0 0 4 442 2067 0 0 0 2 4 this 3 2130  5 value 1 2133  
363 0 0 6 443 1934 0 0 0 1 4 this 3 2131  
364 0 0 6 444 1934 0 0 0 1 4 this 3 2131  
365 0 0 6 445 1934 0 0 0 1 4 this 3 2131  
366 0 0 6 446 1934 0 0 0 1 4 this 3 2131  
367 0 0 6 447 1934 0 0 0 1 4 this 3 2131  
368 0 0 6 448 1934 0 0 0 1 4 this 3 2131  
369 0 0 6 449 1934 0 0 0 2 4 this 3 2131  7 low_bit 1 1934  
370 0 0 4 450 2067 0 0 0 1 4 this 3 2130  
371 0 0 6 451 1935 0 0 0 2 4 this 3 2131  5 other 1 2131  
372 0 0 4 452 2067 0 0 0 1 4 this 3 2130  
373 0 0 4 453 2067 0 0 0 2 4 this 3 2131  3 out 1 2079  
374 0 0 4 454 2067 0 0 0 3 4 this 3 2131  3 out 1 2079  12 spaces_every 5 1934  
375 0 0 4 455 2067 0 0 0 3 4 this 3 2131  3 out 1 2079  12 spaces_every 5 1934  
376 0 0 4 456 2067 0 0 0 3 4 this 3 2131  3 out 1 2079  12 indent_level 5 1934  
377 0 0 6 457 1935 0 0 0 2 4 this 3 2131  5 other 1 2131  
378 0 0 6 458 1935 0 0 0 2 4 this 3 2131  5 other 1 2131  
379 0 0 6 459 1935 0 0 0 2 4 this 3 2131  5 other 1 2131  
380 0 0 6 460 1934 0 0 0 2 4 this 3 2131  5 other 1 2131  
381 0 0 7 461 2130 1363 0 0 2 4 this 3 2131  5 other 1 2131  
382 0 0 7 462 2130 1363 0 0 2 4 this 3 2131  5 other 1 2131  
383 0 0 7 463 2130 1363 0 0 2 4 this 3 2131  5 other 1 2131  
384 0 0 7 464 2130 1363 0 0 1 4 this 3 2131  
385 0 0 7 465 2130 1363 0 0 2 4 this 3 2131  5 shift 1 1934  
386 0 0 7 466 2130 1363 0 0 2 4 this 3 2131  5 shift 1 1934  
387 0 0 6 467 2130 0 0 0 2 4 this 3 2130  5 other 1 2131  
388 0 0 6 468 2130 0 0 0 2 4 this 3 2130  5 other 1 2131  
389 0 0 6 469 2130 0 0 0 2 4 this 3 2130  5 other 1 2131  
390 0 0 6 470 2130 0 0 0 2 4 this 3 2130  5 shift 1 1934  
391 0 0 6 471 2130 0 0 0 2 4 this 3 2130  5 shift 1 1934  
392 0 0 4 472 2067 0 0 0 1 4 this 3 2130  
393 0 0 4 473 2067 0 0 0 1 4 this 3 2130  
394 0 0 7 474 2130 1363 0 0 1 4 this 3 2131  
395 0 0 7 475 2130 1363 0 0 1 4 this 3 2131  
396 0 0 7 476 2130 1363 0 0 1 4 this 3 2131  
397 0 0 7 476 2130 1363 0 0 2 4 this 3 2131  5 other 1 2131  
398 0 0 7 476 2130 1363 0 0 2 4 this 3 2131  5 index 1 1934  
399 0 0 7 477 2130 1363 0 0 1 4 this 3 2131  
400 0 0 7 477 2130 1363 0 0 2 4 this 3 2131  5 other 1 2131  
401 0 0 7 477 2130 1363 0 0 2 4 this 3 2131  5 index 1 1934  
402 0 0 6 478 1934 0 0 0 1 4 this 3 2131  
403 0 0 6 479 1935 0 0 0 1 4 this 3 2131  
404 0 0 38 480 1963 0 0 0 1 4 this 3 2131  
405 0 0 7 481 2081 0 0 0 0 
406 0 0 7 487 2134 1423 0 10 /**
 *
 */ 0 
407 0 0 15 487 2134 1423 0 0 1 6 param0 0 2135  
408 0 0 23 487 2134 1423 0 10 /**
 *
 */ 1 10 init_value 1 2137  
409 0 0 23 487 2134 1423 0 10 /**
 *
 */ 1 4 from 1 2138  
410 0 0 7 488 2134 1423 0 69 /**
 * Returns a BitArray with an infinite array of bits, all on.
 */ 0 
411 0 0 7 489 2134 1423 0 53 /**
 * Returns a BitArray whose bits are all off.
 */ 0 
412 0 0 7 490 2134 1423 0 62 /**
 * Returns a BitArray whose lower on_bits bits are on.
 */ 1 7 on_bits 1 1934  
413 0 0 7 491 2134 1423 0 61 /**
 * Returns a BitArray with only the indicated bit on.
 */ 1 5 index 1 1934  
414 0 0 7 492 2134 1423 0 76 /**
 * Returns a BitArray whose size bits, beginning at low_bit, are on.
 */ 2 7 low_bit 1 1934  4 size 1 1934  
415 0 0 6 493 1935 0 0 0 0 
416 0 0 6 494 1934 0 0 0 0 
417 0 0 6 495 1934 0 0 0 0 
418 0 0 6 496 2140 0 0 300 /**
 * Returns the current number of possibly different bits in this array.  There
 * are actually an infinite number of bits, but every bit higher than this bit
 * will have the same value, either 0 or 1 (see get_highest_bits()).
 *
 * This number may grow and/or shrink automatically as needed.
 */ 1 4 this 3 2135  
419 0 0 6 497 1935 0 0 191 /**
 * Returns true if the nth bit is set, false if it is cleared.  It is valid
 * for n to increase beyond get_num_bits(), but the return value
 * get_num_bits() will always be the same.
 */ 2 4 this 3 2135  5 index 1 1934  
420 0 0 4 498 2067 0 0 97 /**
 * Sets the nth bit on.  If n >= get_num_bits(), this automatically extends
 * the array.
 */ 2 4 this 3 2134  5 index 1 1934  
421 0 0 4 499 2067 0 0 98 /**
 * Sets the nth bit off.  If n >= get_num_bits(), this automatically extends
 * the array.
 */ 2 4 this 3 2134  5 index 1 1934  
422 0 0 4 500 2067 0 0 84 /**
 * Sets the nth bit either on or off, according to the indicated bool value.
 */ 3 4 this 3 2134  5 index 1 1934  5 value 1 1935  
423 0 0 6 501 1935 0 0 118 /**
 * Returns true if the infinite set of bits beyond get_num_bits() are all on,
 * or false of they are all off.
 */ 1 4 this 3 2135  
424 0 0 6 502 1935 0 0 71 /**
 * Returns true if the entire bitmask is zero, false otherwise.
 */ 1 4 this 3 2135  
425 0 0 6 503 1935 0 0 70 /**
 * Returns true if the entire bitmask is one, false otherwise.
 */ 1 4 this 3 2135  
426 0 0 6 504 2137 0 0 187 /**
 * Returns a word that represents only the indicated range of bits within this
 * BitArray, shifted to the least-significant position.  size must be <=
 * get_num_bits_per_word().
 */ 3 4 this 3 2135  7 low_bit 1 1934  4 size 1 1934  
427 0 0 4 505 2067 0 0 92 /**
 * Stores the indicated word into the indicated range of bits with this
 * BitArray.
 */ 4 4 this 3 2134  5 value 1 2137  7 low_bit 1 1934  4 size 1 1934  
428 0 0 6 506 1935 0 0 82 /**
 * Returns true if any bit in the indicated range is set, false otherwise.
 */ 3 4 this 3 2135  7 low_bit 1 1934  4 size 1 1934  
429 0 0 6 507 1935 0 0 84 /**
 * Returns true if all bits in the indicated range are set, false otherwise.
 */ 3 4 this 3 2135  7 low_bit 1 1934  4 size 1 1934  
430 0 0 4 508 2067 0 0 47 /**
 * Sets the indicated range of bits on.
 */ 3 4 this 3 2134  7 low_bit 1 1934  4 size 1 1934  
431 0 0 4 509 2067 0 0 48 /**
 * Sets the indicated range of bits off.
 */ 3 4 this 3 2134  7 low_bit 1 1934  4 size 1 1934  
432 0 0 4 510 2067 0 0 64 /**
 * Sets the indicated range of bits to either on or off.
 */ 4 4 this 3 2134  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
433 0 0 6 511 1934 0 0 127 /**
 * Returns the number of bits that are set to 1 in the array.  Returns -1 if
 * there are an infinite number of 1 bits.
 */ 1 4 this 3 2135  
434 0 0 6 512 1934 0 0 127 /**
 * Returns the number of bits that are set to 0 in the array.  Returns -1 if
 * there are an infinite number of 0 bits.
 */ 1 4 this 3 2135  
435 0 0 6 513 1934 0 0 101 /**
 * Returns the index of the lowest 1 bit in the array.  Returns -1 if there
 * are no 1 bits.
 */ 1 4 this 3 2135  
436 0 0 6 514 1934 0 0 101 /**
 * Returns the index of the lowest 0 bit in the array.  Returns -1 if there
 * are no 0 bits.
 */ 1 4 this 3 2135  
437 0 0 6 515 1934 0 0 143 /**
 * Returns the index of the highest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there an infinite number of 1 bits.
 */ 1 4 this 3 2135  
438 0 0 6 516 1934 0 0 143 /**
 * Returns the index of the highest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there an infinite number of 1 bits.
 */ 1 4 this 3 2135  
439 0 0 6 517 1934 0 0 287 /**
 * Returns the index of the next bit in the array, above low_bit, whose value
 * is different that the value of low_bit.  Returns low_bit again if all bits
 * higher than low_bit have the same value.
 *
 * This can be used to quickly iterate through all of the bits in the array.
 */ 2 4 this 3 2135  7 low_bit 1 1934  
440 0 0 6 518 2140 0 0 75 /**
 * Returns the number of possibly-unique words stored in the array.
 */ 1 4 this 3 2135  
441 0 0 7 519 2142 0 0 175 /**
 * Returns the nth word in the array.  It is valid for n to be greater than
 * get_num_words(), but the return value beyond get_num_words() will always be
 * the same.
 */ 2 4 this 3 2135  1 n 1 2140  
442 0 0 4 520 2067 0 0 113 /**
 * Replaces the nth word in the array.  If n >= get_num_words(), this
 * automatically extends the array.
 */ 3 4 this 3 2134  1 n 1 2140  5 value 1 2137  
443 0 0 4 521 2067 0 0 90 /**
 * Inverts all the bits in the BitArray.  This is equivalent to array =
 * ~array.
 */ 1 4 this 3 2134  
444 0 0 6 522 1935 0 0 178 /**
 * Returns true if this BitArray has any "one" bits in common with the other
 * one, false otherwise.
 *
 * This is equivalent to (array & other) != 0, but may be faster.
 */ 2 4 this 3 2135  5 other 1 2135  
445 0 0 4 523 2067 0 0 49 /**
 * Sets all the bits in the BitArray off.
 */ 1 4 this 3 2134  
446 0 0 4 524 2067 0 0 193 /**
 * Writes the BitArray out as a hex number.  For a BitArray, this is always
 * the same as output_hex(); it's too confusing for the output format to
 * change back and forth at runtime.
 */ 2 4 this 3 2135  3 out 1 2079  
447 0 0 4 525 2067 0 0 83 /**
 * Writes the BitArray out as a binary number, with spaces every four bits.
 */ 3 4 this 3 2135  3 out 1 2079  12 spaces_every 5 1934  
448 0 0 4 526 2067 0 0 93 /**
 * Writes the BitArray out as a hexadecimal number, with spaces every four
 * digits.
 */ 3 4 this 3 2135  3 out 1 2079  12 spaces_every 5 1934  
449 0 0 4 527 2067 0 0 99 /**
 * Writes the BitArray out as a binary or a hex number, according to the
 * number of bits.
 */ 3 4 this 3 2135  3 out 1 2079  12 indent_level 5 1934  
450 0 0 6 528 1935 0 0 0 2 4 this 3 2135  5 other 1 2135  
451 0 0 6 529 1935 0 0 0 2 4 this 3 2135  5 other 1 2135  
452 0 0 6 530 1935 0 0 0 2 4 this 3 2135  5 other 1 2135  
453 0 0 6 531 1934 0 0 233 /**
 * Returns a number less than zero if this BitArray sorts before the indicated
 * other BitArray, greater than zero if it sorts after, or 0 if they are
 * equivalent.  This is based on the same ordering defined by operator <.
 */ 2 4 this 3 2135  5 other 1 2135  
454 0 0 7 532 2134 1423 0 0 2 4 this 3 2135  5 other 1 2135  
455 0 0 7 533 2134 1423 0 0 2 4 this 3 2135  5 other 1 2135  
456 0 0 7 534 2134 1423 0 0 2 4 this 3 2135  5 other 1 2135  
457 0 0 7 535 2134 1423 0 0 1 4 this 3 2135  
458 0 0 7 536 2134 1423 0 0 2 4 this 3 2135  5 shift 1 1934  
459 0 0 7 537 2134 1423 0 0 2 4 this 3 2135  5 shift 1 1934  
460 0 0 6 538 2134 0 0 0 2 4 this 3 2134  5 other 1 2135  
461 0 0 6 539 2134 0 0 0 2 4 this 3 2134  5 other 1 2135  
462 0 0 6 540 2134 0 0 0 2 4 this 3 2134  5 other 1 2135  
463 0 0 6 541 2134 0 0 0 2 4 this 3 2134  5 shift 1 1934  
464 0 0 6 542 2134 0 0 0 2 4 this 3 2134  5 shift 1 1934  
465 0 0 38 543 1963 0 0 0 1 4 this 3 2135  
466 0 0 36 544 2067 0 0 0 2 4 this 3 2134  5 state 1 1963  
467 0 0 7 545 2081 0 0 0 0 
468 0 0 7 548 2143 1443 0 259 // The default constructor must do nothing, because we can't guarantee
// ordering of static initializers.  If the constructor tried to initialize
// its value, it  might happen after the value had already been set
// previously by another static initializer! 0 
469 0 0 15 548 2143 1443 0 0 1 6 param0 0 2144  
470 0 0 23 548 2143 1443 0 154 /**
 * Constructs a ButtonHandle with the corresponding index number, which may
 * have been returned by an earlier call to ButtonHandle::get_index().
 */ 1 5 index 1 1934  
471 0 0 23 548 2143 1443 0 341 /**
 * Constructs a ButtonHandle with the corresponding name, which is looked up
 * in the ButtonRegistry.  This exists for the purpose of being able to
 * automatically coerce a string into a ButtonHandle; for most purposes, you
 * should use either the static KeyboardButton/MouseButton getters or
 * ButtonRegistry::register_button().
 */ 1 4 name 1 2083  
472 0 0 6 549 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
473 0 0 6 550 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
474 0 0 6 551 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
475 0 0 6 552 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
476 0 0 6 553 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
477 0 0 6 554 1935 0 0 0 2 4 this 3 2144  5 other 1 2144  
478 0 0 6 555 1934 0 0 209 /**
 * Sorts ButtonHandles arbitrarily (according to <, >, etc.).  Returns a
 * number less than 0 if this type sorts before the other one, greater than
 * zero if it sorts after, 0 if they are equivalent.
 */ 2 4 this 3 2144  5 other 1 2144  
479 0 0 6 556 2140 0 0 54 /**
 * Returns a hash code suitable for phash_map.
 */ 1 4 this 3 2144  
480 0 0 6 557 2083 0 0 42 /**
 * Returns the name of the button.
 */ 1 4 this 3 2144  
481 0 0 6 558 1935 0 0 121 /**
 * Returns true if the button was created with an ASCII equivalent code (e.g.
 * for a standard keyboard button).
 */ 1 4 this 3 2144  
482 0 0 6 559 1981 0 0 109 /**
 * Returns the character code associated with the button, or '\0' if no ASCII
 * code was associated.
 */ 1 4 this 3 2144  
483 0 0 7 560 2143 1443 0 373 /**
 * Returns the alias (alternate name) associated with the button, if any, or
 * ButtonHandle::none() if the button has no alias.
 *
 * Each button is allowed to have one alias, and multiple different buttons
 * can refer to the same alias.  The alias should be the more general name for
 * the button, for instance, shift is an alias for lshift, but not vice-versa.
 */ 1 4 this 3 2144  
484 0 0 6 561 1935 0 0 263 /**
 * Returns true if this ButtonHandle is the same as the other one, or if the
 * other one is an alias for this one.  (Does not return true if this button
 * is an alias for the other one, however.)
 *
 * This is a more general comparison than operator ==.
 */ 2 4 this 3 2144  5 other 1 2144  
485 0 0 6 562 1934 0 0 363 /**
 * Returns the integer index associated with this ButtonHandle.  Each
 * different ButtonHandle will have a different index.  However, you probably
 * shouldn't be using this method; you should just treat the ButtonHandles as
 * opaque classes.  This is provided for the convenience of non-C++ scripting
 * languages to build a hashtable of ButtonHandles.
 */ 1 4 this 3 2144  
486 0 0 4 563 2067 0 0 10 /**
 *
 */ 2 4 this 3 2144  3 out 1 2079  
487 0 0 7 564 2143 1443 0 0 0 
488 0 0 7 573 2081 0 0 0 0 
489 0 0 6 565 1935 0 0 0 1 4 this 3 2144  
490 0 0 15 581 2148 1450 0 0 1 6 param0 0 2146  
491 0 0 7 576 2143 1443 0 150 /**
 * Finds a ButtonHandle in the registry matching the indicated name.  If there
 * is no such ButtonHandle, registers a new one and returns it.
 */ 2 4 this 3 2148  4 name 1 2083  
492 0 0 7 577 2143 1443 0 144 /**
 * Finds a ButtonHandle in the registry matching the indicated name.  If there
 * is no such ButtonHandle, returns ButtonHandle::none().
 */ 2 4 this 3 2148  4 name 1 2083  
493 0 0 7 578 2143 1443 0 169 /**
 * Finds a ButtonHandle in the registry matching the indicated ASCII
 * equivalent character.  If there is no such ButtonHandle, returns
 * ButtonHandle::none().
 */ 2 4 this 3 2146  16 ascii_equivalent 1 1981  
494 0 0 4 579 2067 0 0 10 /**
 *
 */ 2 4 this 3 2146  3 out 1 2079  
495 0 0 6 580 2148 0 0 67 /**
 * Returns the pointer to the global ButtonRegistry object.
 */ 0 
496 0 0 7 592 2149 1459 0 0 0 
497 0 0 15 592 2149 1459 0 0 1 6 param0 0 2150  
498 0 0 6 585 2140 0 0 76 /**
 * Returns the number of buttons that this button mapping specifies.
 */ 1 4 this 3 2150  
499 0 0 7 586 2143 1443 0 76 /**
 * Returns the underlying raw button associated with the nth button.
 */ 2 4 this 3 2150  1 i 1 2140  
500 0 0 7 587 2143 1443 0 166 /**
 * Returns the button that the given button is mapped to, or
 * ButtonHandle::none() if this map does not specify a mapped button for the
 * given raw button.
 */ 2 4 this 3 2150  3 raw 1 2143  
501 0 0 7 587 2143 1443 0 101 /**
 * Returns the nth mapped button, meaning the button that the nth raw button
 * is mapped to.
 */ 2 4 this 3 2150  1 i 1 2140  
502 0 0 7 587 2143 1443 0 166 /**
 * Returns the button that the given button is mapped to, or
 * ButtonHandle::none() if this map does not specify a mapped button for the
 * given raw button.
 */ 2 4 this 3 2150  8 raw_name 1 2083  
503 0 0 6 588 2083 0 0 353 /**
 * If the button map specifies a special name for the button (eg.  if the
 * operating system or keyboard device has a localized name describing the
 * key), returns it, or the empty string otherwise.
 *
 * Note that this is not the same as get_mapped_button().get_name(), which
 * returns the name of the Panda event associated with the button.
 */ 2 4 this 3 2150  3 raw 1 2143  
504 0 0 6 588 2083 0 0 127 /**
 * Returns the label associated with the nth mapped button, meaning the button
 * that the nth raw button is mapped to.
 */ 2 4 this 3 2150  1 i 1 2140  
505 0 0 6 588 2083 0 0 353 /**
 * If the button map specifies a special name for the button (eg.  if the
 * operating system or keyboard device has a localized name describing the
 * key), returns it, or the empty string otherwise.
 *
 * Note that this is not the same as get_mapped_button().get_name(), which
 * returns the name of the Panda event associated with the button.
 */ 2 4 this 3 2150  8 raw_name 1 2083  
506 0 0 4 589 2067 0 0 10 /**
 *
 */ 2 4 this 3 2150  3 out 1 2079  
507 0 0 4 590 2067 0 0 10 /**
 *
 */ 3 4 this 3 2150  3 out 1 2079  12 indent_level 5 1934  
508 0 0 7 591 2081 0 0 0 0 
509 0 0 15 598 2154 1464 0 0 1 6 param0 0 2152  
510 0 0 4 595 2067 0 0 10 /**
 *
 */ 2 4 this 3 2152  3 out 1 2079  
511 0 0 55 596 2154 1464 0 0 1 8 function 1 1963  
512 0 0 7 597 2081 0 0 0 0 
513 0 0 6 601 1934 0 0 47 /**
 * Returns the current reference count.
 */ 1 4 this 3 2155  
514 0 0 4 602 2067 0 0 108 /**
 * Explicitly increments the cache reference count and the normal reference
 * count simultaneously.
 */ 1 4 this 3 2155  
515 0 0 6 603 1935 0 0 202 /**
 * Explicitly decrements the cache reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */ 1 4 this 3 2155  
516 0 0 6 604 1935 0 0 97 /**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */ 1 4 this 3 2155  
517 0 0 7 606 2081 0 0 0 0 
518 0 0 4 608 2067 0 0 10 /**
 *
 */ 2 4 this 3 2157  3 out 1 2079  
519 0 0 4 609 2067 0 0 159 /**
 * You should make this call during the callback if you want to continue the
 * normal function that would have been done in the absence of a callback.
 */ 1 4 this 3 2159  
520 0 0 7 610 2081 0 0 0 0 
521 0 0 23 613 2160 1464 0 10 /**
 *
 */ 1 8 function 5 1963  
522 0 0 15 613 2160 1464 0 0 1 6 param0 0 2161  
523 0 0 4 614 2067 0 0 0 2 4 this 3 2160  4 size 1 2140  
524 0 0 4 617 2067 0 0 126 /**
 * Replaces the function that is called for the callback.  runs.  The
 * parameter should be a Python callable object.
 */ 2 4 this 3 2160  8 function 1 1963  
525 0 0 6 618 1963 0 0 64 /**
 * Returns the function that is called for the callback.
 */ 1 4 this 3 2160  
526 0 0 7 620 2081 0 0 0 0 
527 0 0 7 622 2163 1486 0 10 /**
 *
 */ 0 
528 0 0 15 622 2163 1486 0 0 1 6 param0 0 2164  
529 0 0 6 623 1991 0 0 10 /**
 *
 */ 1 4 this 3 2164  
530 0 0 6 624 1991 0 0 10 /**
 *
 */ 1 4 this 3 2164  
531 0 0 15 634 2168 0 0 10 /**
 *
 */ 1 4 copy 1 2166  
532 0 0 23 634 2168 0 0 10 /**
 *
 */ 1 4 mode 5 1994  
533 0 0 4 635 2067 0 0 1841 /**
 * Changes the mode of the clock.  Normally, the clock is in mode M_normal.
 * In this mode, each call to tick() will set the value returned by
 * get_frame_time() to the current real time; thus, the clock simply reports
 * time advancing.
 *
 * Other possible modes:
 *
 * M_non_real_time - the clock ignores real time completely; at each call to
 * tick(), it pretends that exactly dt seconds have elapsed since the last
 * call to tick().  You may set the value of dt with set_dt() or
 * set_frame_rate().
 *
 * M_limited - the clock will run as fast as it can, as in M_normal, but will
 * not run faster than the rate specified by set_frame_rate().  If the
 * application would run faster than this rate, the clock will slow down the
 * application.
 *
 * M_integer - the clock will run as fast as it can, but the rate will be
 * constrained to be an integer multiple or divisor of the rate specified by
 * set_frame_rate().  The clock will slow down the application a bit to
 * guarantee this.
 *
 * M_integer_limited - a combination of M_limited and M_integer; the clock
 * will not run faster than set_frame_rate(), and if it runs slower, it will
 * run at a integer divisor of that rate.
 *
 * M_forced - the clock forces the application to run at the rate specified by
 * set_frame_rate().  If the application would run faster than this rate, the
 * clock will slow down the application; if the application would run slower
 * than this rate, the clock slows down time so that the application believes
 * it is running at the given rate.
 *
 * M_degrade - the clock runs at real time, but the application is slowed down
 * by a set factor of its frame rate, specified by set_degrade_factor().
 *
 * M_slave - the clock does not advance, but relies on the user to call
 * set_frame_time() and/or set_frame_count() each frame.
 */ 2 4 this 3 2168  4 mode 1 1994  
534 0 0 6 636 1994 0 0 66 /**
 * Returns the current mode of the clock.  See set_mode().
 */ 1 4 this 3 2166  
535 0 0 6 638 1933 0 0 350 /**
 * Returns the time in seconds as of the last time tick() was called
 * (typically, this will be as of the start of the current frame).
 *
 * This is generally the kind of time you want to ask for in most rendering
 * and animation contexts, since it's important that all of the animation for
 * a given frame remains in sync with each other.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
536 0 0 6 639 1933 0 0 434 /**
 * Returns the actual number of seconds elapsed since the ClockObject was
 * created, or since it was last reset.  This is useful for doing real timing
 * measurements, e.g.  for performance statistics.
 *
 * This returns the most precise timer we have for short time intervals, but
 * it may tend to drift over the long haul.  If more accurate timekeeping is
 * needed over a long period of time, use get_long_time() instead.
 */ 1 4 this 3 2166  
537 0 0 6 640 1933 0 0 440 /**
 * Returns the actual number of seconds elapsed since the ClockObject was
 * created, or since it was last reset.
 *
 * This is similar to get_real_time(), except that it uses the most accurate
 * counter we have over a long period of time, and so it is less likely to
 * drift.  However, it may not be very precise for measuring short intervals.
 * On Windows, for instace, this is only accurate to within about 55
 * milliseconds.
 */ 1 4 this 3 2166  
538 0 0 4 641 2067 0 0 75 /**
 * Simultaneously resets both the time and the frame count to zero.
 */ 1 4 this 3 2168  
539 0 0 4 642 2067 0 0 316 /**
 * Resets the clock to the indicated time.  This changes only the real time of
 * the clock as reported by get_real_time(), but does not immediately change
 * the time reported by get_frame_time()--that will change after the next call
 * to tick().  Also see reset(), set_frame_time(), and set_frame_count().
 */ 2 4 this 3 2168  4 time 1 1933  
540 0 0 4 643 2067 0 0 215 /**
 * Changes the time as reported for the current frame to the indicated time.
 * Normally, the way to adjust the frame time is via tick(); this function is
 * provided only for occasional special adjustments.
 */ 3 4 this 3 2168  4 time 1 1933  14 current_thread 5 2169  
541 0 0 4 644 2067 0 0 132 /**
 * Resets the number of frames counted to the indicated number.  Also see
 * reset(), set_real_time(), and set_frame_time().
 */ 3 4 this 3 2168  11 frame_count 1 1934  14 current_thread 5 2169  
542 0 0 6 645 1934 0 0 195 /**
 * Returns the number of times tick() has been called since the ClockObject
 * was created, or since it was last reset.  This is generally the number of
 * frames that have been rendered.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
543 0 0 6 646 1933 0 0 245 /**
 * Returns the average frame rate since the last reset.  This is simply the
 * total number of frames divided by the total elapsed time.  This reports the
 * virtual frame rate if the clock is in (or has been in) M_non_real_time
 * mode.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
544 0 0 6 651 1933 0 0 130 /**
 * Returns the elapsed time for the previous frame: the number of seconds
 * elapsed between the last two calls to tick().
 */ 2 4 this 3 2166  14 current_thread 5 2169  
545 0 0 4 652 2067 0 0 291 /**
 * In non-real-time mode, sets the number of seconds that should appear to
 * elapse between frames.  In forced mode or limited mode, sets our target dt.
 * In normal mode, this has no effect.
 *
 * Also see set_frame_rate(), which is a different way to specify the same
 * quantity.
 */ 2 4 this 3 2168  2 dt 1 1933  
546 0 0 4 653 2067 0 0 291 /**
 * In non-real-time mode, sets the number of frames per second that we should
 * appear to be running.  In forced mode or limited mode, sets our target
 * frame rate.  In normal mode, this has no effect.
 *
 * Also see set_dt(), which is a different way to specify the same quantity.
 */ 2 4 this 3 2168  10 frame_rate 1 1933  
547 0 0 6 655 1933 0 0 106 /**
 * Returns the current maximum allowable time elapsed between any two frames.
 * See set_max_dt().
 */ 1 4 this 3 2166  
548 0 0 4 656 2067 0 0 511 /**
 * Sets a limit on the value returned by get_dt().  If this value is less than
 * zero, no limit is imposed; otherwise, this is the maximum value that will
 * ever be returned by get_dt(), regardless of how much time has actually
 * elapsed between frames.
 *
 * This limit is only imposed in real-time mode; in non-real-time mode, the dt
 * is fixed anyway and max_dt is ignored.
 *
 * This is generally used to guarantee reasonable behavior even in the
 * presence of a very slow or chuggy frame rame.
 */ 2 4 this 3 2168  6 max_dt 1 1933  
549 0 0 6 658 1933 0 0 265 /**
 * In degrade mode, returns the ratio by which the performance is degraded.  A
 * value of 2.0 causes the clock to be slowed down by a factor of two
 * (reducing performance to 1/2 what would be otherwise).
 *
 * This has no effect if mode is not M_degrade.
 */ 1 4 this 3 2166  
550 0 0 4 659 2067 0 0 262 /**
 * In degrade mode, sets the ratio by which the performance is degraded.  A
 * value of 2.0 causes the clock to be slowed down by a factor of two
 * (reducing performance to 1/2 what would be otherwise).
 *
 * This has no effect if mode is not M_degrade.
 */ 2 4 this 3 2168  14 degrade_factor 1 1933  
551 0 0 4 661 2067 0 0 393 /**
 * Specifies the interval of time (in seconds) over which
 * get_average_frame_rate() averages the number of frames per second to
 * compute the frame rate.  Changing this does not necessarily immediately
 * change the result of get_average_frame_rate(), until this interval of time
 * has elapsed again.
 *
 * Setting this to zero disables the computation of get_average_frame_rate().
 */ 2 4 this 3 2168  4 time 1 1933  
552 0 0 6 662 1933 0 0 162 /**
 * Returns the interval of time (in seconds) over which
 * get_average_frame_rate() averages the number of frames per second to
 * compute the frame rate.
 */ 1 4 this 3 2166  
553 0 0 6 664 1933 0 0 214 /**
 * Returns the average frame rate in number of frames per second over the last
 * get_average_frame_rate_interval() seconds.  This measures the virtual frame
 * rate if the clock is in M_non_real_time mode.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
554 0 0 6 665 1933 0 0 105 /**
 * Returns the maximum frame duration over the last
 * get_average_frame_rate_interval() seconds.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
555 0 0 6 666 1933 0 0 539 /**
 * Returns the standard deviation of the frame times of the frames rendered
 * over the past get_average_frame_rate_interval() seconds.  This number gives
 * an estimate of the chugginess of the frame rate; if it is large, there is a
 * large variation in the frame rate; if is small, all of the frames are
 * consistent in length.
 *
 * A large value might also represent just a recent change in frame rate, for
 * instance, because the camera has just rotated from looking at a simple
 * scene to looking at a more complex scene.
 */ 2 4 this 3 2166  14 current_thread 5 2169  
556 0 0 4 669 2067 0 0 285 /**
 * Instructs the clock that a new frame has just begun.  In normal, real-time
 * mode, get_frame_time() will henceforth report the time as of this instant
 * as the current start-of-frame time.  In non-real-time mode,
 * get_frame_time() will be incremented by the value of dt.
 */ 2 4 this 3 2168  14 current_thread 5 2169  
557 0 0 4 670 2067 0 0 417 /**
 * Resets the frame time to the current real time.  This is similar to tick(),
 * except that it does not advance the frame counter and does not affect dt.
 * This is intended to be used in the middle of a particularly long frame to
 * compensate for the time that has already elapsed.
 *
 * In non-real-time mode, this function has no effect (because in this mode
 * all frames take the same length of time).
 */ 2 4 this 3 2168  14 current_thread 5 2169  
558 0 0 6 671 1935 0 0 322 /**
 * Returns true if a clock error was detected since the last time
 * check_errors() was called.  A clock error means that something happened, an
 * OS or BIOS bug, for instance, that makes the current value of the clock
 * somewhat suspect, and an application may wish to resynchronize with any
 * external clocks.
 */ 2 4 this 3 2168  14 current_thread 1 2169  
559 0 0 7 672 2168 0 0 159 /**
 * Returns a pointer to the global ClockObject.  This is the ClockObject that
 * most code should use for handling scene graph rendering and animation.
 */ 0 
560 0 0 7 673 2081 0 0 0 0 
561 0 0 4 681 2067 0 0 62 /**
 * @see CachedTypedWritableReferenceCount::cache_ref()
 */ 1 4 this 3 2171  
562 0 0 6 682 1935 0 0 64 /**
 * @see CachedTypedWritableReferenceCount::cache_unref()
 */ 1 4 this 3 2171  
563 0 0 7 683 2081 0 0 0 0 
564 0 0 7 690 2173 1532 0 48 /**
 * Initializes an empty datagram buffer.
 */ 0 
565 0 0 7 690 2173 1532 0 54 /**
 * Initializes the buffer with the given data.
 */ 1 4 data 1 2000  
566 0 0 4 691 2067 0 0 38 /**
 * Clears the internal buffer.
 */ 1 4 this 3 2173  
567 0 0 6 686 1967 0 0 0 1 4 this 3 2173  
568 0 0 6 688 1960 0 0 0 1 4 this 3 2173  
569 0 0 7 700 2174 0 0 10 /**
 *
 */ 0 
570 0 0 6 701 1935 0 0 99 /**
 * Opens the indicated filename for reading.  Returns true on success, false
 * on failure.
 */ 2 4 this 3 2174  4 file 1 2175  
571 0 0 6 701 1935 0 0 99 /**
 * Opens the indicated filename for reading.  Returns true on success, false
 * on failure.
 */ 2 4 this 3 2174  8 filename 1 2102  
572 0 0 6 701 1935 0 0 230 /**
 * Starts reading from the indicated stream.  Returns true on success, false
 * on failure.  The DatagramInputFile does not take ownership of the stream;
 * you are responsible for closing or deleting it when you are done.
 */ 3 4 this 3 2174  2 in 1 2178  8 filename 5 2102  
573 0 0 6 702 2178 0 0 61 /**
 * Returns the istream represented by the input file.
 */ 1 4 this 3 2174  
574 0 0 4 703 2067 0 0 98 /**
 * Closes the file.  This is also implicitly done when the DatagramInputFile
 * destructs.
 */ 1 4 this 3 2174  
575 0 0 7 706 2180 0 0 10 /**
 *
 */ 0 
576 0 0 6 707 1935 0 0 102 /**
 * Opens the indicated filename for writing.  Returns true if successful,
 * false on failure.
 */ 2 4 this 3 2180  4 file 1 2175  
577 0 0 6 707 1935 0 0 99 /**
 * Opens the indicated filename for writing.  Returns true on success, false
 * on failure.
 */ 2 4 this 3 2180  8 filename 1 2102  
578 0 0 6 707 1935 0 0 229 /**
 * Starts writing to the indicated stream.  Returns true on success, false on
 * failure.  The DatagramOutputFile does not take ownership of the stream; you
 * are responsible for closing or deleting it when you are done.
 */ 3 4 this 3 2180  3 out 1 2079  8 filename 5 2102  
579 0 0 4 708 2067 0 0 99 /**
 * Closes the file.  This is also implicitly done when the DatagramOutputFile
 * destructs.
 */ 1 4 this 3 2180  
580 0 0 6 709 1935 0 0 256 /**
 * Writes a sequence of bytes to the beginning of the datagram file.  This may
 * be called any number of times after the file has been opened and before the
 * first datagram is written.  It may not be called once the first datagram is
 * written.
 */ 2 4 this 3 2180  6 header 1 2083  
581 0 0 6 709 1935 0 0 256 /**
 * Writes a sequence of bytes to the beginning of the datagram file.  This may
 * be called any number of times after the file has been opened and before the
 * first datagram is written.  It may not be called once the first datagram is
 * written.
 */ 2 4 this 3 2180  6 header 1 2000  
582 0 0 7 714 2181 1596 0 0 0 
583 0 0 15 714 2181 1596 0 0 1 6 param0 0 2182  
584 0 0 55 714 2181 1596 0 0 1 10 init_value 1 1963  
585 0 0 7 715 2181 1596 0 0 0 
586 0 0 7 716 2181 1596 0 0 0 
587 0 0 7 717 2181 1596 0 0 1 7 on_bits 1 1934  
588 0 0 7 718 2181 1596 0 0 1 5 index 1 1934  
589 0 0 7 719 2181 1596 0 0 2 7 low_bit 1 1934  4 size 1 1934  
590 0 0 6 720 1935 0 0 0 0 
591 0 0 6 721 1934 0 0 0 0 
592 0 0 6 722 1934 0 0 0 1 4 this 3 2182  
593 0 0 6 723 1935 0 0 0 2 4 this 3 2182  5 index 1 1934  
594 0 0 4 724 2067 0 0 0 2 4 this 3 2181  5 index 1 1934  
595 0 0 4 725 2067 0 0 0 2 4 this 3 2181  5 index 1 1934  
596 0 0 4 726 2067 0 0 0 3 4 this 3 2181  5 index 1 1934  5 value 1 1935  
597 0 0 6 727 1935 0 0 0 1 4 this 3 2182  
598 0 0 6 728 1935 0 0 0 1 4 this 3 2182  
599 0 0 6 729 2133 0 0 0 3 4 this 3 2182  7 low_bit 1 1934  4 size 1 1934  
600 0 0 4 730 2067 0 0 0 4 4 this 3 2181  5 value 1 2133  7 low_bit 1 1934  4 size 1 1934  
601 0 0 6 731 1935 0 0 0 3 4 this 3 2182  7 low_bit 1 1934  4 size 1 1934  
602 0 0 6 732 1935 0 0 0 3 4 this 3 2182  7 low_bit 1 1934  4 size 1 1934  
603 0 0 4 733 2067 0 0 0 3 4 this 3 2181  7 low_bit 1 1934  4 size 1 1934  
604 0 0 4 734 2067 0 0 0 3 4 this 3 2181  7 low_bit 1 1934  4 size 1 1934  
605 0 0 4 735 2067 0 0 0 4 4 this 3 2181  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
606 0 0 6 736 1934 0 0 0 1 4 this 3 2182  
607 0 0 6 737 1934 0 0 0 1 4 this 3 2182  
608 0 0 6 738 1934 0 0 0 1 4 this 3 2182  
609 0 0 6 739 1934 0 0 0 1 4 this 3 2182  
610 0 0 6 740 1934 0 0 0 1 4 this 3 2182  
611 0 0 6 741 1934 0 0 0 1 4 this 3 2182  
612 0 0 6 742 1934 0 0 0 2 4 this 3 2182  7 low_bit 1 1934  
613 0 0 4 743 2067 0 0 0 1 4 this 3 2181  
614 0 0 6 744 1935 0 0 0 2 4 this 3 2182  5 other 1 2182  
615 0 0 4 745 2067 0 0 0 1 4 this 3 2181  
616 0 0 4 746 2067 0 0 0 2 4 this 3 2182  3 out 1 2079  
617 0 0 4 747 2067 0 0 0 3 4 this 3 2182  3 out 1 2079  12 spaces_every 5 1934  
618 0 0 4 748 2067 0 0 0 3 4 this 3 2182  3 out 1 2079  12 spaces_every 5 1934  
619 0 0 4 749 2067 0 0 0 3 4 this 3 2182  3 out 1 2079  12 indent_level 5 1934  
620 0 0 6 750 1935 0 0 0 2 4 this 3 2182  5 other 1 2182  
621 0 0 6 751 1935 0 0 0 2 4 this 3 2182  5 other 1 2182  
622 0 0 6 752 1935 0 0 0 2 4 this 3 2182  5 other 1 2182  
623 0 0 6 753 1934 0 0 0 2 4 this 3 2182  5 other 1 2182  
624 0 0 7 754 2181 1596 0 0 2 4 this 3 2182  5 other 1 2182  
625 0 0 7 755 2181 1596 0 0 2 4 this 3 2182  5 other 1 2182  
626 0 0 7 756 2181 1596 0 0 2 4 this 3 2182  5 other 1 2182  
627 0 0 7 757 2181 1596 0 0 1 4 this 3 2182  
628 0 0 7 758 2181 1596 0 0 2 4 this 3 2182  5 shift 1 1934  
629 0 0 7 759 2181 1596 0 0 2 4 this 3 2182  5 shift 1 1934  
630 0 0 6 760 2181 0 0 0 2 4 this 3 2181  5 other 1 2182  
631 0 0 6 761 2181 0 0 0 2 4 this 3 2181  5 other 1 2182  
632 0 0 6 762 2181 0 0 0 2 4 this 3 2181  5 other 1 2182  
633 0 0 6 763 2181 0 0 0 2 4 this 3 2181  5 shift 1 1934  
634 0 0 6 764 2181 0 0 0 2 4 this 3 2181  5 shift 1 1934  
635 0 0 38 765 1963 0 0 0 1 4 this 3 2182  
636 0 0 7 766 2081 0 0 0 0 
637 0 0 7 770 2184 1650 0 0 0 
638 0 0 15 770 2184 1650 0 0 1 6 param0 0 2185  
639 0 0 55 770 2184 1650 0 0 1 10 init_value 1 1963  
640 0 0 7 771 2184 1650 0 0 0 
641 0 0 7 772 2184 1650 0 0 0 
642 0 0 7 773 2184 1650 0 0 1 7 on_bits 1 1934  
643 0 0 7 774 2184 1650 0 0 1 5 index 1 1934  
644 0 0 7 775 2184 1650 0 0 2 7 low_bit 1 1934  4 size 1 1934  
645 0 0 6 776 1935 0 0 0 0 
646 0 0 6 777 1934 0 0 0 0 
647 0 0 6 778 1934 0 0 0 1 4 this 3 2185  
648 0 0 6 779 1935 0 0 0 2 4 this 3 2185  5 index 1 1934  
649 0 0 4 780 2067 0 0 0 2 4 this 3 2184  5 index 1 1934  
650 0 0 4 781 2067 0 0 0 2 4 this 3 2184  5 index 1 1934  
651 0 0 4 782 2067 0 0 0 3 4 this 3 2184  5 index 1 1934  5 value 1 1935  
652 0 0 6 783 1935 0 0 0 1 4 this 3 2185  
653 0 0 6 784 1935 0 0 0 1 4 this 3 2185  
654 0 0 6 785 2133 0 0 0 3 4 this 3 2185  7 low_bit 1 1934  4 size 1 1934  
655 0 0 4 786 2067 0 0 0 4 4 this 3 2184  5 value 1 2133  7 low_bit 1 1934  4 size 1 1934  
656 0 0 6 787 1935 0 0 0 3 4 this 3 2185  7 low_bit 1 1934  4 size 1 1934  
657 0 0 6 788 1935 0 0 0 3 4 this 3 2185  7 low_bit 1 1934  4 size 1 1934  
658 0 0 4 789 2067 0 0 0 3 4 this 3 2184  7 low_bit 1 1934  4 size 1 1934  
659 0 0 4 790 2067 0 0 0 3 4 this 3 2184  7 low_bit 1 1934  4 size 1 1934  
660 0 0 4 791 2067 0 0 0 4 4 this 3 2184  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
661 0 0 6 792 1934 0 0 0 1 4 this 3 2185  
662 0 0 6 793 1934 0 0 0 1 4 this 3 2185  
663 0 0 6 794 1934 0 0 0 1 4 this 3 2185  
664 0 0 6 795 1934 0 0 0 1 4 this 3 2185  
665 0 0 6 796 1934 0 0 0 1 4 this 3 2185  
666 0 0 6 797 1934 0 0 0 1 4 this 3 2185  
667 0 0 6 798 1934 0 0 0 2 4 this 3 2185  7 low_bit 1 1934  
668 0 0 4 799 2067 0 0 0 1 4 this 3 2184  
669 0 0 6 800 1935 0 0 0 2 4 this 3 2185  5 other 1 2185  
670 0 0 4 801 2067 0 0 0 1 4 this 3 2184  
671 0 0 4 802 2067 0 0 0 2 4 this 3 2185  3 out 1 2079  
672 0 0 4 803 2067 0 0 0 3 4 this 3 2185  3 out 1 2079  12 spaces_every 5 1934  
673 0 0 4 804 2067 0 0 0 3 4 this 3 2185  3 out 1 2079  12 spaces_every 5 1934  
674 0 0 4 805 2067 0 0 0 3 4 this 3 2185  3 out 1 2079  12 indent_level 5 1934  
675 0 0 6 806 1935 0 0 0 2 4 this 3 2185  5 other 1 2185  
676 0 0 6 807 1935 0 0 0 2 4 this 3 2185  5 other 1 2185  
677 0 0 6 808 1935 0 0 0 2 4 this 3 2185  5 other 1 2185  
678 0 0 6 809 1934 0 0 0 2 4 this 3 2185  5 other 1 2185  
679 0 0 7 810 2184 1650 0 0 2 4 this 3 2185  5 other 1 2185  
680 0 0 7 811 2184 1650 0 0 2 4 this 3 2185  5 other 1 2185  
681 0 0 7 812 2184 1650 0 0 2 4 this 3 2185  5 other 1 2185  
682 0 0 7 813 2184 1650 0 0 1 4 this 3 2185  
683 0 0 7 814 2184 1650 0 0 2 4 this 3 2185  5 shift 1 1934  
684 0 0 7 815 2184 1650 0 0 2 4 this 3 2185  5 shift 1 1934  
685 0 0 6 816 2184 0 0 0 2 4 this 3 2184  5 other 1 2185  
686 0 0 6 817 2184 0 0 0 2 4 this 3 2184  5 other 1 2185  
687 0 0 6 818 2184 0 0 0 2 4 this 3 2184  5 other 1 2185  
688 0 0 6 819 2184 0 0 0 2 4 this 3 2184  5 shift 1 1934  
689 0 0 6 820 2184 0 0 0 2 4 this 3 2184  5 shift 1 1934  
690 0 0 38 821 1963 0 0 0 1 4 this 3 2185  
691 0 0 7 822 2081 0 0 0 0 
692 0 0 7 858 2187 1683 0 0 0 
693 0 0 15 858 2187 1683 0 0 1 6 param0 0 2188  
694 0 0 7 827 2143 1443 0 0 0 
695 0 0 7 828 2143 1443 0 0 0 
696 0 0 7 829 2143 1443 0 0 0 
697 0 0 7 830 2143 1443 0 0 0 
698 0 0 7 831 2143 1443 0 0 0 
699 0 0 7 832 2143 1443 0 0 0 
700 0 0 7 833 2143 1443 0 0 0 
701 0 0 7 834 2143 1443 0 0 0 
702 0 0 7 835 2143 1443 0 0 0 
703 0 0 7 836 2143 1443 0 0 0 
704 0 0 7 837 2143 1443 0 0 0 
705 0 0 7 838 2143 1443 0 0 0 
706 0 0 7 839 2143 1443 0 0 0 
707 0 0 7 840 2143 1443 0 0 0 
708 0 0 7 841 2143 1443 0 0 0 
709 0 0 7 842 2143 1443 0 0 0 
710 0 0 7 843 2143 1443 0 0 0 
711 0 0 7 844 2143 1443 0 0 0 
712 0 0 7 845 2143 1443 0 0 0 
713 0 0 7 846 2143 1443 0 0 0 
714 0 0 7 847 2143 1443 0 0 0 
715 0 0 7 848 2143 1443 0 0 0 
716 0 0 7 849 2143 1443 0 0 0 
717 0 0 7 850 2143 1443 0 0 0 
718 0 0 7 851 2143 1443 0 0 0 
719 0 0 7 852 2143 1443 0 74 // Flight stick buttons, takes zero-based index.  First is always trigger. 0 
720 0 0 7 853 2143 1443 0 169 /**
 * Returns the ButtonHandle associated with the particular numbered joystick
 * button (zero-based), if there is one, or ButtonHandle::none() if there is
 * not.
 */ 1 13 button_number 1 1934  
721 0 0 7 854 2143 1443 0 0 0 
722 0 0 7 855 2143 1443 0 0 0 
723 0 0 7 856 2143 1443 0 0 0 
724 0 0 7 857 2143 1443 0 0 0 
725 0 0 7 913 2190 1737 0 0 0 
726 0 0 15 913 2190 1737 0 0 1 6 param0 0 2191  
727 0 0 7 861 2143 1443 0 144 /**
 * Returns the ButtonHandle associated with the particular ASCII character, if
 * there is one, or ButtonHandle::none() if there is not.
 */ 1 16 ascii_equivalent 1 1981  
728 0 0 7 862 2143 1443 0 0 0 
729 0 0 7 863 2143 1443 0 0 0 
730 0 0 7 864 2143 1443 0 0 0 
731 0 0 7 865 2143 1443 0 0 0 
732 0 0 7 866 2143 1443 0 0 0 
733 0 0 7 867 2143 1443 0 0 0 
734 0 0 7 868 2143 1443 0 0 0 
735 0 0 7 869 2143 1443 0 0 0 
736 0 0 7 870 2143 1443 0 0 0 
737 0 0 7 871 2143 1443 0 0 0 
738 0 0 7 872 2143 1443 0 0 0 
739 0 0 7 873 2143 1443 0 0 0 
740 0 0 7 874 2143 1443 0 0 0 
741 0 0 7 875 2143 1443 0 0 0 
742 0 0 7 876 2143 1443 0 0 0 
743 0 0 7 877 2143 1443 0 0 0 
744 0 0 7 878 2143 1443 0 0 0 
745 0 0 7 879 2143 1443 0 59 // PC keyboards don't have these four buttons, but Macs do. 0 
746 0 0 7 880 2143 1443 0 0 0 
747 0 0 7 881 2143 1443 0 0 0 
748 0 0 7 882 2143 1443 0 0 0 
749 0 0 7 883 2143 1443 0 0 0 
750 0 0 7 884 2143 1443 0 0 0 
751 0 0 7 885 2143 1443 0 0 0 
752 0 0 7 886 2143 1443 0 0 0 
753 0 0 7 887 2143 1443 0 0 0 
754 0 0 7 888 2143 1443 0 0 0 
755 0 0 7 889 2143 1443 0 0 0 
756 0 0 7 890 2143 1443 0 0 0 
757 0 0 7 891 2143 1443 0 0 0 
758 0 0 7 892 2143 1443 0 27 // delete is a C++ keyword. 0 
759 0 0 7 893 2143 1443 0 27 // delete is a C++ keyword. 0 
760 0 0 7 894 2143 1443 0 0 0 
761 0 0 7 895 2143 1443 0 0 0 
762 0 0 7 896 2143 1443 0 0 0 
763 0 0 7 897 2143 1443 0 0 0 
764 0 0 7 898 2143 1443 0 0 0 
765 0 0 7 899 2143 1443 0 0 0 
766 0 0 7 900 2143 1443 0 0 0 
767 0 0 7 901 2143 1443 0 0 0 
768 0 0 7 902 2143 1443 0 0 0 
769 0 0 7 903 2143 1443 0 0 0 
770 0 0 7 904 2143 1443 0 0 0 
771 0 0 7 905 2143 1443 0 0 0 
772 0 0 7 906 2143 1443 0 0 0 
773 0 0 7 907 2143 1443 0 0 0 
774 0 0 7 908 2143 1443 0 0 0 
775 0 0 7 909 2143 1443 0 0 0 
776 0 0 7 910 2143 1443 0 0 0 
777 0 0 7 911 2143 1443 0 0 0 
778 0 0 7 912 2143 1443 0 0 0 
779 0 0 7 920 2193 1743 0 10 /**
 *
 */ 0 
780 0 0 15 920 2193 1743 0 10 /**
 *
 */ 1 4 copy 1 2194  
781 0 0 6 922 2193 0 0 0 2 4 this 3 2193  4 copy 1 2194  
782 0 0 6 923 1935 0 0 0 2 4 this 3 2194  5 other 1 2194  
783 0 0 6 924 1935 0 0 0 2 4 this 3 2194  5 other 1 2194  
784 0 0 6 925 1935 0 0 0 2 4 this 3 2194  5 other 1 2194  
785 0 0 7 926 2193 1743 0 0 2 4 this 3 2194  5 other 1 2194  
786 0 0 7 927 2193 1743 0 0 2 4 this 3 2194  5 other 1 2194  
787 0 0 6 928 2193 0 0 0 2 4 this 3 2193  5 other 1 2194  
788 0 0 6 929 2193 0 0 0 2 4 this 3 2193  5 other 1 2194  
789 0 0 4 930 2067 0 0 374 /**
 * Sets the list of buttons to watch to be the same as that of the other
 * ModifierButtons object.  This makes the lists pointer equivalent (until one
 * or the other is later modified).
 *
 * This will preserve the state of any button that was on the original list
 * and is also on the new lists.  Any other buttons will get reset to the
 * default state of "up".
 */ 2 4 this 3 2193  5 other 1 2194  
790 0 0 6 931 1935 0 0 232 /**
 * Returns true if the set of buttons indicated as down by this
 * ModifierButtons object is the same set of buttons indicated as down by the
 * other ModifierButtons object.  The buttons indicated as up are not
 * relevant.
 */ 2 4 this 3 2194  5 other 1 2194  
791 0 0 6 932 1935 0 0 248 /**
 * Adds the indicated button to the set of buttons that will be monitored for
 * upness and downness.  Returns true if the button was added, false if it was
 * already being monitored or if too many buttons are currently being
 * monitored.
 */ 2 4 this 3 2193  6 button 1 2143  
792 0 0 6 933 1935 0 0 109 /**
 * Returns true if the indicated button is in the set of buttons being
 * monitored, false otherwise.
 */ 2 4 this 3 2194  6 button 1 2143  
793 0 0 6 934 1935 0 0 313 /**
 * Removes the indicated button from the set of buttons being monitored.
 * Returns true if the button was removed, false if it was not being monitored
 * in the first place.
 *
 * Unlike the other methods, you cannot remove a button by removing its alias;
 * you have to remove exactly the button itself.
 */ 2 4 this 3 2193  6 button 1 2143  
794 0 0 6 935 1934 0 0 143 /**
 * Returns the number of buttons that the ModifierButtons object is monitoring
 * (e.g.  the number of buttons passed to add_button()).
 */ 1 4 this 3 2194  
795 0 0 7 936 2143 1443 0 185 /**
 * Returns the nth button that the ModifierButtons object is monitoring (the
 * nth button passed to add_button()).  This must be in the range 0 <= index <
 * get_num_buttons().
 */ 2 4 this 3 2194  5 index 1 1934  
796 0 0 6 939 1935 0 0 305 /**
 * Records that a particular button has been pressed.  If the given button is
 * one of the buttons that is currently being monitored, this will update the
 * internal state appropriately; otherwise, it will do nothing.  Returns true
 * if the button is one that was monitored, or false otherwise.
 */ 2 4 this 3 2193  6 button 1 2143  
797 0 0 6 940 1935 0 0 306 /**
 * Records that a particular button has been released.  If the given button is
 * one of the buttons that is currently being monitored, this will update the
 * internal state appropriately; otherwise, it will do nothing.  Returns true
 * if the button is one that was monitored, or false otherwise.
 */ 2 4 this 3 2193  6 button 1 2143  
798 0 0 4 941 2067 0 0 66 /**
 * Marks all monitored buttons as being in the "up" state.
 */ 1 4 this 3 2193  
799 0 0 6 942 1935 0 0 157 /**
 * Returns true if the indicated button is known to be down, or false if it is
 * known to be up or if it is not in the set of buttons being tracked.
 */ 2 4 this 3 2194  6 button 1 2143  
800 0 0 6 942 1935 0 0 105 /**
 * Returns true if the indicated button is known to be down, or false if it is
 * known to be up.
 */ 2 4 this 3 2194  5 index 1 1934  
801 0 0 6 943 1935 0 0 109 /**
 * Returns true if any of the tracked button are known to be down, or false if
 * all of them are up.
 */ 1 4 this 3 2194  
802 0 0 6 944 2083 0 0 150 /**
 * Returns a string which can be used to prefix any button name or event name
 * with the unique set of modifier buttons currently being held.
 */ 1 4 this 3 2194  
803 0 0 4 945 2067 0 0 69 /**
 * Writes a one-line summary of the buttons known to be down.
 */ 2 4 this 3 2194  3 out 1 2079  
804 0 0 4 946 2067 0 0 123 /**
 * Writes a multi-line summary including all of the buttons being monitored
 * and which ones are known to be down.
 */ 2 4 this 3 2194  3 out 1 2079  
805 0 0 7 959 2196 1779 0 0 0 
806 0 0 15 959 2196 1779 0 0 1 6 param0 0 2197  
807 0 0 7 948 2143 1443 0 166 /**
 * Returns the ButtonHandle associated with the particular numbered mouse
 * button (zero-based), if there is one, or ButtonHandle::none() if there is
 * not.
 */ 1 13 button_number 1 1934  
808 0 0 7 949 2143 1443 0 75 /**
 * Returns the ButtonHandle associated with the first mouse button.
 */ 0 
809 0 0 7 950 2143 1443 0 76 /**
 * Returns the ButtonHandle associated with the second mouse button.
 */ 0 
810 0 0 7 951 2143 1443 0 75 /**
 * Returns the ButtonHandle associated with the third mouse button.
 */ 0 
811 0 0 7 952 2143 1443 0 76 /**
 * Returns the ButtonHandle associated with the fourth mouse button.
 */ 0 
812 0 0 7 953 2143 1443 0 75 /**
 * Returns the ButtonHandle associated with the fifth mouse button.
 */ 0 
813 0 0 7 954 2143 1443 0 98 /**
 * Returns the ButtonHandle generated when the mouse wheel is rolled one notch
 * upwards.
 */ 0 
814 0 0 7 955 2143 1443 0 100 /**
 * Returns the ButtonHandle generated when the mouse wheel is rolled one notch
 * downwards.
 */ 0 
815 0 0 7 956 2143 1443 0 148 /**
 * Returns the ButtonHandle generated when the mouse is scrolled to the left.
 * Usually, you'll only find the horizontal scroll on laptops.
 */ 0 
816 0 0 7 957 2143 1443 0 149 /**
 * Returns the ButtonHandle generated when the mouse is scrolled to the right.
 * Usually, you'll only find the horizontal scroll on laptops.
 */ 0 
817 0 0 6 958 1935 0 0 117 /**
 * Returns true if the indicated ButtonHandle is a mouse button, false if it
 * is some other kind of button.
 */ 1 6 button 1 2143  
818 0 0 7 975 2199 1787 0 0 0 
819 0 0 15 975 2199 1787 0 0 1 6 param0 0 2200  
820 0 0 6 963 1933 0 0 10 /**
 *
 */ 1 4 this 3 2200  
821 0 0 6 964 1933 0 0 10 /**
 *
 */ 1 4 this 3 2200  
822 0 0 6 965 1935 0 0 157 /**
 * If this returns false, the pointer is not currently present in the window
 * and the values returned by get_x() and get_y() may not be meaningful.
 */ 1 4 this 3 2200  
823 0 0 6 979 1934 0 0 47 /**
 * Returns the current reference count.
 */ 1 4 this 3 2202  
824 0 0 4 980 2067 0 0 349 /**
 * Explicitly increments the reference count.
 *
 * This function is const, even though it changes the object, because
 * generally fiddling with an object's reference count isn't considered part
 * of fiddling with the object.  An object might be const in other ways, but
 * we still need to accurately count the number of references to it.
 */ 1 4 this 3 2202  
825 0 0 6 981 1935 0 0 201 /**
 * Explicitly decrements the node reference count and the normal reference
 * count simultaneously.
 *
 * The return value is true if the new reference count is nonzero, false if it
 * is zero.
 */ 1 4 this 3 2202  
826 0 0 6 982 1935 0 0 97 /**
 * Does some easy checks to make sure that the reference count isn't
 * completely bogus.
 */ 1 4 this 3 2202  
827 0 0 6 984 1934 0 0 293 /**
 * Returns the union of the values defined in the Referenced enum that
 * represents the various things that appear to be holding a pointer to this
 * object.
 *
 * If R_node is included, at least one node is holding a pointer; if R_cache
 * is included, at least one cache element is.
 */ 1 4 this 3 2202  
828 0 0 7 985 2081 0 0 0 0 
829 0 0 7 987 2204 1848 0 10 /**
 *
 */ 0 
830 0 0 23 987 2204 1848 0 10 /**
 *
 */ 1 4 from 1 2135  
831 0 0 15 987 2204 1848 0 0 1 6 param0 0 2138  
832 0 0 7 988 2204 1848 0 72 /**
 * Returns a SparseArray with an infinite array of bits, all on.
 */ 0 
833 0 0 7 989 2204 1848 0 56 /**
 * Returns a SparseArray whose bits are all off.
 */ 0 
834 0 0 7 990 2204 1848 0 65 /**
 * Returns a SparseArray whose lower on_bits bits are on.
 */ 1 7 on_bits 1 1934  
835 0 0 7 991 2204 1848 0 64 /**
 * Returns a SparseArray with only the indicated bit on.
 */ 1 5 index 1 1934  
836 0 0 7 992 2204 1848 0 79 /**
 * Returns a SparseArray whose size bits, beginning at low_bit, are on.
 */ 2 7 low_bit 1 1934  4 size 1 1934  
837 0 0 6 993 1935 0 0 393 /**
 * Returns true if there is a maximum number of bits that may be stored in
 * this structure, false otherwise.  If this returns true, the number may be
 * queried in get_max_num_bits().
 *
 * This method always returns false.  The SparseArray has no maximum number of
 * bits.  This method is defined so generic programming algorithms can use
 * BitMask or SparseArray interchangeably.
 */ 0 
838 0 0 6 994 1934 0 0 428 /**
 * If get_max_num_bits() returned true, this method may be called to return
 * the maximum number of bits that may be stored in this structure.  It is an
 * error to call this if get_max_num_bits() return false.
 *
 * It is always an error to call this method.  The SparseArray has no maximum
 * number of bits.  This method is defined so generic programming algorithms
 * can use BitMask or SparseArray interchangeably.
 */ 0 
839 0 0 6 995 1934 0 0 300 /**
 * Returns the current number of possibly different bits in this array.  There
 * are actually an infinite number of bits, but every bit higher than this bit
 * will have the same value, either 0 or 1 (see get_highest_bits()).
 *
 * This number may grow and/or shrink automatically as needed.
 */ 1 4 this 3 2138  
840 0 0 6 996 1935 0 0 191 /**
 * Returns true if the nth bit is set, false if it is cleared.  It is valid
 * for n to increase beyond get_num_bits(), but the return value
 * get_num_bits() will always be the same.
 */ 2 4 this 3 2138  5 index 1 1934  
841 0 0 4 997 2067 0 0 97 /**
 * Sets the nth bit on.  If n >= get_num_bits(), this automatically extends
 * the array.
 */ 2 4 this 3 2204  5 index 1 1934  
842 0 0 4 998 2067 0 0 98 /**
 * Sets the nth bit off.  If n >= get_num_bits(), this automatically extends
 * the array.
 */ 2 4 this 3 2204  5 index 1 1934  
843 0 0 4 999 2067 0 0 84 /**
 * Sets the nth bit either on or off, according to the indicated bool value.
 */ 3 4 this 3 2204  5 index 1 1934  5 value 1 1935  
844 0 0 6 1000 1935 0 0 118 /**
 * Returns true if the infinite set of bits beyond get_num_bits() are all on,
 * or false of they are all off.
 */ 1 4 this 3 2138  
845 0 0 6 1001 1935 0 0 71 /**
 * Returns true if the entire bitmask is zero, false otherwise.
 */ 1 4 this 3 2138  
846 0 0 6 1002 1935 0 0 70 /**
 * Returns true if the entire bitmask is one, false otherwise.
 */ 1 4 this 3 2138  
847 0 0 6 1003 1935 0 0 82 /**
 * Returns true if any bit in the indicated range is set, false otherwise.
 */ 3 4 this 3 2138  7 low_bit 1 1934  4 size 1 1934  
848 0 0 6 1004 1935 0 0 84 /**
 * Returns true if all bits in the indicated range are set, false otherwise.
 */ 3 4 this 3 2138  7 low_bit 1 1934  4 size 1 1934  
849 0 0 4 1005 2067 0 0 47 /**
 * Sets the indicated range of bits on.
 */ 3 4 this 3 2204  7 low_bit 1 1934  4 size 1 1934  
850 0 0 4 1006 2067 0 0 48 /**
 * Sets the indicated range of bits off.
 */ 3 4 this 3 2204  7 low_bit 1 1934  4 size 1 1934  
851 0 0 4 1007 2067 0 0 64 /**
 * Sets the indicated range of bits to either on or off.
 */ 4 4 this 3 2204  5 value 1 1935  7 low_bit 1 1934  4 size 1 1934  
852 0 0 6 1008 1934 0 0 127 /**
 * Returns the number of bits that are set to 1 in the array.  Returns -1 if
 * there are an infinite number of 1 bits.
 */ 1 4 this 3 2138  
853 0 0 6 1009 1934 0 0 127 /**
 * Returns the number of bits that are set to 0 in the array.  Returns -1 if
 * there are an infinite number of 0 bits.
 */ 1 4 this 3 2138  
854 0 0 6 1010 1934 0 0 146 /**
 * Returns the index of the lowest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there are an infinite number of 1 bits.
 */ 1 4 this 3 2138  
855 0 0 6 1011 1934 0 0 146 /**
 * Returns the index of the lowest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there are an infinite number of 1 bits.
 */ 1 4 this 3 2138  
856 0 0 6 1012 1934 0 0 143 /**
 * Returns the index of the highest 1 bit in the array.  Returns -1 if there
 * are no 1 bits or if there an infinite number of 1 bits.
 */ 1 4 this 3 2138  
857 0 0 6 1013 1934 0 0 143 /**
 * Returns the index of the highest 0 bit in the array.  Returns -1 if there
 * are no 0 bits or if there an infinite number of 1 bits.
 */ 1 4 this 3 2138  
858 0 0 6 1014 1934 0 0 287 /**
 * Returns the index of the next bit in the array, above low_bit, whose value
 * is different that the value of low_bit.  Returns low_bit again if all bits
 * higher than low_bit have the same value.
 *
 * This can be used to quickly iterate through all of the bits in the array.
 */ 2 4 this 3 2138  7 low_bit 1 1934  
859 0 0 4 1015 2067 0 0 93 /**
 * Inverts all the bits in the SparseArray.  This is equivalent to array =
 * ~array.
 */ 1 4 this 3 2204  
860 0 0 6 1016 1935 0 0 181 /**
 * Returns true if this SparseArray has any "one" bits in common with the
 * other one, false otherwise.
 *
 * This is equivalent to (array & other) != 0, but may be faster.
 */ 2 4 this 3 2138  5 other 1 2138  
861 0 0 4 1017 2067 0 0 52 /**
 * Sets all the bits in the SparseArray off.
 */ 1 4 this 3 2204  
862 0 0 4 1018 2067 0 0 10 /**
 *
 */ 2 4 this 3 2138  3 out 1 2079  
863 0 0 6 1019 1935 0 0 0 2 4 this 3 2138  5 other 1 2138  
864 0 0 6 1020 1935 0 0 0 2 4 this 3 2138  5 other 1 2138  
865 0 0 6 1021 1935 0 0 0 2 4 this 3 2138  5 other 1 2138  
866 0 0 6 1022 1934 0 0 242 /**
 * Returns a number less than zero if this SparseArray sorts before the
 * indicated other SparseArray, greater than zero if it sorts after, or 0 if
 * they are equivalent.  This is based on the same ordering defined by
 * operator <.
 */ 2 4 this 3 2138  5 other 1 2138  
867 0 0 7 1023 2204 1848 0 0 2 4 this 3 2138  5 other 1 2138  
868 0 0 7 1024 2204 1848 0 0 2 4 this 3 2138  5 other 1 2138  
869 0 0 7 1025 2204 1848 0 0 2 4 this 3 2138  5 other 1 2138  
870 0 0 7 1026 2204 1848 0 0 1 4 this 3 2138  
871 0 0 7 1027 2204 1848 0 0 2 4 this 3 2138  5 shift 1 1934  
872 0 0 7 1028 2204 1848 0 0 2 4 this 3 2138  5 shift 1 1934  
873 0 0 6 1029 2204 0 0 0 2 4 this 3 2204  5 other 1 2138  
874 0 0 6 1030 2204 0 0 0 2 4 this 3 2204  5 other 1 2138  
875 0 0 6 1031 2204 0 0 0 2 4 this 3 2204  5 other 1 2138  
876 0 0 6 1032 2204 0 0 0 2 4 this 3 2204  5 shift 1 1934  
877 0 0 6 1033 2204 0 0 0 2 4 this 3 2204  5 shift 1 1934  
878 0 0 6 1034 1935 0 0 330 /**
 * If this is true, the SparseArray is actually defined as a list of subranges
 * of integers that are *not* in the set.  If this is false (the default),
 * then the subranges define the integers that *are* in the set.  This affects
 * the interpretation of the values returned by iterating through
 * get_num_subranges().
 */ 1 4 this 3 2138  
879 0 0 6 1035 2140 0 0 237 /**
 * Returns the number of separate subranges stored in the SparseArray.  You
 * can use this limit to iterate through the subranges, calling
 * get_subrange_begin() and get_subrange_end() for each one.
 *
 * Also see is_inverse().
 */ 1 4 this 3 2138  
880 0 0 6 1036 1934 0 0 94 /**
 * Returns the first numeric element in the nth subrange.
 *
 * Also see is_inverse().
 */ 2 4 this 3 2138  1 n 1 2140  
881 0 0 6 1037 1934 0 0 104 /**
 * Returns the last numeric element, plus one, in the nth subrange.
 *
 * Also see is_inverse().
 */ 2 4 this 3 2138  1 n 1 2140  
882 0 0 38 1038 1963 0 0 0 1 4 this 3 2138  
883 0 0 36 1039 2067 0 0 0 2 4 this 3 2204  5 state 1 1963  
884 0 0 7 1040 2081 0 0 0 0 
885 0 0 7 1043 2081 0 0 52 /**
 * Returns the type of the underlying value.
 */ 1 4 this 3 2205  
886 0 0 4 1044 2067 0 0 0 2 4 this 3 2205  3 out 1 2079  
887 0 0 7 1045 2081 0 0 0 0 
888 0 0 23 1047 2209 1065 0 10 /**
 *
 */ 1 5 value 1 2207  
889 0 0 7 1048 2022 0 0 55 /**
 * Retrieves the value stored in the parameter.
 */ 1 4 this 3 2210  
890 0 0 7 1051 2081 0 0 0 0 
891 0 0 23 1053 2212 1065 0 0 1 5 value 1 2083  
892 0 0 4 1054 2067 0 0 0 2 4 this 3 2212  5 value 1 2083  
893 0 0 6 1055 2083 0 0 0 1 4 this 3 2213  
894 0 0 7 1059 2081 0 0 0 0 
895 0 0 23 1062 2215 1065 0 0 1 5 value 1 2083  
896 0 0 4 1063 2067 0 0 0 2 4 this 3 2215  5 value 1 2083  
897 0 0 6 1064 2083 0 0 0 1 4 this 3 2216  
898 0 0 7 1066 2081 0 0 0 0 
899 0 0 23 1069 2221 1065 0 0 1 5 value 1 2218  
900 0 0 4 1070 2067 0 0 0 2 4 this 3 2221  5 value 1 2218  
901 0 0 6 1071 2218 0 0 0 1 4 this 3 2222  
902 0 0 7 1073 2081 0 0 0 0 
903 0 0 23 1076 2227 1065 0 0 1 5 value 1 2224  
904 0 0 4 1077 2067 0 0 0 2 4 this 3 2227  5 value 1 2224  
905 0 0 6 1078 2224 0 0 0 1 4 this 3 2228  
906 0 0 7 1080 2081 0 0 0 0 
907 0 0 23 1083 2233 1065 0 0 1 5 value 1 2230  
908 0 0 4 1084 2067 0 0 0 2 4 this 3 2233  5 value 1 2230  
909 0 0 6 1085 2230 0 0 0 1 4 this 3 2234  
910 0 0 7 1087 2081 0 0 0 0 
911 0 0 23 1090 2239 1065 0 0 1 5 value 1 2236  
912 0 0 4 1091 2067 0 0 0 2 4 this 3 2239  5 value 1 2236  
913 0 0 6 1092 2236 0 0 0 1 4 this 3 2240  
914 0 0 7 1094 2081 0 0 0 0 
915 0 0 23 1097 2245 1065 0 0 1 5 value 1 2242  
916 0 0 4 1098 2067 0 0 0 2 4 this 3 2245  5 value 1 2242  
917 0 0 6 1099 2242 0 0 0 1 4 this 3 2246  
918 0 0 7 1101 2081 0 0 0 0 
919 0 0 23 1104 2251 1065 0 0 1 5 value 1 2248  
920 0 0 4 1105 2067 0 0 0 2 4 this 3 2251  5 value 1 2248  
921 0 0 6 1106 2248 0 0 0 1 4 this 3 2252  
922 0 0 7 1108 2081 0 0 0 0 
923 0 0 23 1111 2257 1065 0 0 1 5 value 1 2254  
924 0 0 4 1112 2067 0 0 0 2 4 this 3 2257  5 value 1 2254  
925 0 0 6 1113 2254 0 0 0 1 4 this 3 2258  
926 0 0 7 1115 2081 0 0 0 0 
927 0 0 23 1118 2263 1065 0 0 1 5 value 1 2260  
928 0 0 4 1119 2067 0 0 0 2 4 this 3 2263  5 value 1 2260  
929 0 0 6 1120 2260 0 0 0 1 4 this 3 2264  
930 0 0 7 1122 2081 0 0 0 0 
931 0 0 23 1125 2269 1065 0 0 1 5 value 1 2266  
932 0 0 4 1126 2067 0 0 0 2 4 this 3 2269  5 value 1 2266  
933 0 0 6 1127 2266 0 0 0 1 4 this 3 2270  
934 0 0 7 1129 2081 0 0 0 0 
935 0 0 23 1132 2275 1065 0 0 1 5 value 1 2272  
936 0 0 4 1133 2067 0 0 0 2 4 this 3 2275  5 value 1 2272  
937 0 0 6 1134 2272 0 0 0 1 4 this 3 2276  
938 0 0 7 1136 2081 0 0 0 0 
939 0 0 23 1139 2281 1065 0 0 1 5 value 1 2278  
940 0 0 4 1140 2067 0 0 0 2 4 this 3 2281  5 value 1 2278  
941 0 0 6 1141 2278 0 0 0 1 4 this 3 2282  
942 0 0 7 1143 2081 0 0 0 0 
943 0 0 23 1146 2287 1065 0 0 1 5 value 1 2284  
944 0 0 4 1147 2067 0 0 0 2 4 this 3 2287  5 value 1 2284  
945 0 0 6 1148 2284 0 0 0 1 4 this 3 2288  
946 0 0 7 1150 2081 0 0 0 0 
947 0 0 23 1153 2293 1065 0 0 1 5 value 1 2290  
948 0 0 4 1154 2067 0 0 0 2 4 this 3 2293  5 value 1 2290  
949 0 0 6 1155 2290 0 0 0 1 4 this 3 2294  
950 0 0 7 1157 2081 0 0 0 0 
951 0 0 7 1165 2081 0 0 0 0 
952 0 0 15 1168 2298 1918 0 0 1 6 param0 0 2296  
953 0 0 7 1168 2298 1918 0 56 /**
 * Create a free id pool in the range [min:max].
 */ 2 3 min 5 2128  3 max 5 2128  
954 0 0 6 1170 2128 0 0 134 /**
 * Returns an id between _min and _max (that were passed to the constructor).
 * IndexEnd is returned if no ids are available.
 */ 1 4 this 3 2298  
955 0 0 4 1171 2067 0 0 518 /**
 * This may be called to mark a particular id as having already been allocated
 * (for instance, by a prior pass).  The specified id is removed from the
 * available pool.
 *
 * Because of the limitations of this algorithm, this is most efficient when
 * it is called before the first call to allocate(), and when all the calls to
 * initial_reserve_id() are made in descending order by id.  However, this is
 * a performance warning only; if performance is not an issue, any id may be
 * reserved at any time.
 */ 2 4 this 3 2298  2 id 1 2128  
956 0 0 4 1172 2067 0 0 112 /**
 * Free an allocated index (index must be between _min and _max that were
 * passed to the constructor).
 */ 2 4 this 3 2298  5 index 1 2128  
957 0 0 6 1173 2299 0 0 117 /**
 * return the decimal fraction of the pool that is used.  The range is 0 to
 * 1.0 (e.g.  75% would be 0.75).
 */ 1 4 this 3 2296  
958 0 0 4 1174 2067 0 0 42 /**
 * ...intended for debugging only.
 */ 2 4 this 3 2296  3 out 1 2079  
959 0 0 4 1175 2067 0 0 42 /**
 * ...intended for debugging only.
 */ 2 4 this 3 2296  3 out 1 2079  
960 0 0 6 676 1996 0 0 0 1 3 str 1 2083  
961 0 0 6 677 2083 0 0 0 1 2 cs 1 1996  
962 0 0 6 678 2301 0 0 0 0 
963 0 0 6 679 2301 0 0 0 0 
964 0 0 6 915 2303 0 0 602 /**
 * A convenience function for loading explicit prc files from a disk file or
 * from within a multifile (via the virtual file system).  Save the return
 * value and pass it to unload_prc_file() if you ever want to unload this file
 * later.
 *
 * The filename is first searched along the default prc search path, and then
 * also along the model path, for convenience.
 *
 * This function is defined in putil instead of in dtool with the read of the
 * prc stuff, so that it can take advantage of the virtual file system (which
 * is defined in express), and the model path (which is in putil).
 */ 1 8 filename 1 2102  
965 0 0 6 916 2303 0 0 396 /**
 * Another convenience function to load a prc file from an explicit string,
 * which represents the contents of the prc file.
 *
 * The first parameter is an arbitrary name to assign to this in-memory prc
 * file.  Supply a filename if the data was read from a file, or use any other
 * name that is meaningful to you.  The name is only used when the set of
 * loaded prc files is listed.
 */ 2 4 name 1 2083  4 data 1 2083  
966 0 0 6 917 1935 0 0 0 1 4 page 1 2303  
967 0 0 4 918 2067 0 0 0 1 4 hash 1 2305  
968 0 0 6 1176 1963 0 0 203 /**
 * This wrapper is defined as a global function to suit pickle's needs.
 *
 * This hooks into the native pickle and cPickle modules, but it cannot
 * properly handle self-referential BAM objects.
 */ 2 10 this_class 1 1963  4 data 1 2000  
969 0 0 6 1177 1963 0 0 398 /**
 * This wrapper is defined as a global function to suit pickle's needs.
 *
 * This is similar to py_decode_TypedWritable_from_bam_stream, but it provides
 * additional support for the missing persistent-state object needed to
 * properly support self-referential BAM objects written to the pickle stream.
 * This hooks into the pickle and cPickle modules implemented in
 * direct/src/stdpy.
 */ 3 9 unpickler 1 1963  10 this_class 1 1963  4 data 1 2000  
380
1927 29 ConstPointerToArray< ushort > 0 141313 29 ConstPointerToArray< ushort > 29 ConstPointerToArray< ushort > 0 0 0 1 975 989 0 13 976 977 978 979 980 981 982 983 984 985 986 987 988 0 0 1 0 1928 0 0 0 0 0

1928 28 PointerToArrayBase< ushort > 0 26625 28 PointerToArrayBase< ushort > 28 PointerToArrayBase< ushort > 0 0 0 0 972 0 2 973 974 0 0 1 0 1929 0 0 0 0 0

1929 49 PointerToBase< ReferenceCountedVector< ushort > > 0 43009 49 PointerToBase< ReferenceCountedVector< ushort > > 49 PointerToBase< ReferenceCountedVector< ushort > > 0 0 0 0 0 0 2 970 971 0 0 1 0 1930 0 0 0 0 0

1930 13 PointerToVoid 0 2048 13 PointerToVoid 13 PointerToVoid 0 0 0 0 0 0 0 0 0 0 0 0 373
/**
 * This is the non-template part of the base class for PointerTo and
 * ConstPointerTo.  It is necessary so we can keep a pointer to a non-template
 * class within the ReferenceCount object, to implement weak reference
 * pointers--we need to have something to clean up when the ReferenceCount
 * object destructs.
 *
 * This is the base class for PointerToBase<T>.
 */

1931 24 PointerToArray< ushort > 0 141313 24 PointerToArray< ushort > 24 PointerToArray< ushort > 0 0 0 1 990 1011 0 20 991 992 993 994 995 996 997 998 999 1000 1001 1002 1003 1004 1005 1006 1007 1008 1009 1010 0 0 1 0 1928 0 0 0 0 0

1932 13 AnimInterface 0 26625 13 AnimInterface 13 AnimInterface 0 0 0 0 1012 9 2307 2308 2309 2310 2311 2312 2313 2314 2315 17 1013 1014 1015 1016 1017 1018 1019 1020 1021 1022 1023 1024 1025 1026 1027 1028 1029 0 0 0 0 0 226
/**
 * This is the fundamental interface for things that have a play/loop/stop
 * type interface for frame-based animation, such as animated characters.
 * This is the base class for AnimControl and other, similar classes.
 */

1933 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

1934 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

1935 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

1936 16 AutoTextureScale 0 532481 16 AutoTextureScale 16 AutoTextureScale 0 0 0 0 0 0 0 0 0 0 5 8 ATS_none 8 ATS_none 0
0 8 ATS_down 8 ATS_down 0
1 6 ATS_up 6 ATS_up 0
2 7 ATS_pad 7 ATS_pad 0
3 15 ATS_unspecified 15 ATS_unspecified 0
4 0 0

1937 9 UpdateSeq 0 141313 9 UpdateSeq 9 UpdateSeq 0 0 0 1 1030 1050 1 2316 19 1031 1032 1033 1034 1035 1036 1037 1038 1039 1040 1041 1042 1043 1044 1045 1046 1047 1048 1049 0 0 0 0 0 761
/**
 * This is a sequence number that increments monotonically.  It can be used to
 * track cache updates, or serve as a kind of timestamp for any changing
 * properties.
 *
 * A special class is used instead of simply an int, so we can elegantly
 * handle such things as wraparound and special cases.  There are two special
 * cases.  Firstly, a sequence number is 'initial' when it is first created.
 * This sequence is older than any other sequence number.  Secondly, a
 * sequence number may be explicitly set to 'old'.  This is older than any
 * other sequence number except 'initial'.  Finally, we have the explicit
 * number 'fresh', which is newer than any other sequence number.  All other
 * sequences are numeric and are monotonically increasing.
 */

1938 7 Integer 0 2367488 21 AtomicAdjust::Integer 21 AtomicAdjust::Integer 1939 0 1940 0 0 0 0 0 0 0 0 0 0

1939 12 AtomicAdjust 0 1024 12 AtomicAdjust 12 AtomicAdjust 0 0 0 0 0 0 0 0 0 0 0 0 0

1940 8 long int 0 8210 8 long int 8 long int 0 1 0 0 0 0 0 0 0 0 0 0 0

1941 13 TypedWritable 0 75777 13 TypedWritable 13 TypedWritable 0 0 0 0 0 0 8 1051 1052 1053 1054 1055 1056 1057 1058 0 0 1 0 1942 0 0 0 0 135
/**
 * Base class for objects that can be written to and read from Bam files.
 *
 * See also TypedObject for detailed instructions.
 */

1942 11 TypedObject 0 2048 11 TypedObject 11 TypedObject 0 0 0 0 0 0 0 0 0 0 0 0 2508
/**
 * This is an abstract class that all classes which use TypeHandle, and also
 * provide virtual functions to support polymorphism, should inherit from.
 * Each derived class should define get_type(), which should return the
 * specific type of the derived class.  Inheriting from this automatically
 * provides support for is_of_type() and is_exact_type().
 *
 * All classes that inherit directly or indirectly from TypedObject should
 * redefine get_type() and force_init_type(), as shown below.  Some classes
 * that do not inherit from TypedObject may still declare TypeHandles for
 * themselves by defining methods called get_class_type() and init_type().
 * Classes such as these may serve as base classes, but the dynamic type
 * identification system will be limited.  Classes that do not inherit from
 * TypedObject need not define the virtual functions get_type() and
 * force_init_type() (or any other virtual functions).
 *
 * There is a specific layout for defining the overrides from this class.
 * Keeping the definitions formatted just like these examples will allow
 * someone in the future to use a sed (or similar) script to make global
 * changes, if necessary.  Avoid rearranging the braces or the order of the
 * functions unless you're ready to change them in every file all at once.
 *
 * What follows are some examples that can be used in new classes that you
 * create.
 *
 * @par In the class definition (.h file):
 * @code
 * public:
 *   static TypeHandle get_class_type() {
 *     return _type_handle;
 *   }
 *   static void init_type() {
 *     <<<BaseClassOne>>>::init_type();
 *     <<<BaseClassTwo>>>::init_type();
 *     <<<BaseClassN>>>::init_type();
 *     register_type(_type_handle, "<<<ThisClassStringName>>>",
 *                   <<<BaseClassOne>>>::get_class_type(),
 *                   <<<BaseClassTwo>>>::get_class_type(),
 *                   <<<BaseClassN>>>::get_class_type());
 *   }
 *   virtual TypeHandle get_type() const {
 *     return get_class_type();
 *   }
 *   virtual TypeHandle force_init_type() {init_type(); return get_class_type();}
 *
 * private:
 *   static TypeHandle _type_handle;
 * @endcode
 *
 * @par In the class .cxx file:
 * @code
 * TypeHandle <<<ThisClassStringName>>>::_type_handle;
 * @endcode
 *
 * @par In the class config_<<<PackageName>>>.cxx file:
 * @code
 * ConfigureFn(config_<<<PackageName>>>) {
 *   <<<ClassOne>>>::init_type();
 *   <<<ClassTwo>>>::init_type();
 *   <<<ClassN>>>::init_type();
 * }
 * @endcode
 */

1943 27 TypedWritableReferenceCount 0 141313 27 TypedWritableReferenceCount 27 TypedWritableReferenceCount 0 0 0 0 1065 0 2 1063 1064 0 0 2 3 1941 1059 1060 3 1944 1061 1062 0 0 414
/**
 * A base class for things which need to inherit from both TypedWritable and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedWritables and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1944 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

1945 14 BamCacheRecord 0 26625 14 BamCacheRecord 14 BamCacheRecord 0 0 0 0 1070 5 2317 2318 2319 2320 2321 19 1071 1072 1073 1074 1075 1076 1077 1078 1079 1080 1081 1082 1083 1084 1085 1086 1087 1088 1089 0 0 2 3 1943 1066 1067 3 1946 1068 1069 0 0 231
/**
 * An instance of this class is written to the front of a Bam or Txo file to
 * make the file a cached instance of some other loadable resource.  This
 * record contains information needed to test the validity of the cache.
 */

1946 14 LinkedListNode 0 1050624 14 LinkedListNode 14 LinkedListNode 0 0 0 0 0 0 0 0 0 0 0 0 468
/**
 * This just stores the pointers to implement a doubly-linked list of some
 * kind of object.  There are occasions when a hand-rolled linked list is more
 * appropriate than an STL container.
 *
 * Typically, each node of the linked list, as well as the root of the list,
 * will inherit from this class.
 *
 * Note that this class is not inherently thread-safe; derived classes are
 * responsible for protecting any calls into it within mutexes, if necessary.
 */

1947 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 1948 0 0 0 0 0 0 0 0 0 0

1948 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

1949 6 time_t 0 2105344 6 time_t 6 time_t 0 0 1940 0 0 0 0 0 0 0 0 0 0

1950 15 TypedWritable * 0 8576 15 TypedWritable * 15 TypedWritable * 0 0 1941 0 0 0 0 0 0 0 0 0 0

1951 8 BamCache 0 26625 8 BamCache 8 BamCache 0 0 0 1 1090 1091 9 2322 2323 2324 2325 2326 2327 2328 2329 2330 26 1092 1093 1094 1095 1096 1097 1098 1099 1100 1101 1102 1103 1104 1105 1106 1107 1108 1109 1110 1111 1112 1113 1114 1115 1116 1117 0 0 0 0 0 615
/**
 * This class maintains a cache of Bam and/or Txo objects generated from model
 * files and texture images (as well as possibly other kinds of loadable
 * objects that can be stored in bam file format).
 *
 * This class also maintains a persistent index that lists all of the cached
 * objects (see BamCacheIndex). We go through some considerable effort to make
 * sure this index gets saved correctly to disk, even in the presence of
 * multiple different processes writing to the same index, and without relying
 * too heavily on low-level os-provided file locks (which work poorly with C++
 * iostreams).
 */

1952 8 BamEnums 0 141313 8 BamEnums 8 BamEnums 0 0 0 1 1118 1119 0 0 0 0 0 0 3 1953 1954 1955 104
/**
 * This class exists just to provide scoping for the enums shared by BamReader
 * and BamWriter.
 */

1953 9 BamEndian 0 794624 19 BamEnums::BamEndian 19 BamEnums::BamEndian 1952 0 0 0 0 0 0 0 0 0 3 12 BE_bigendian 22 BamEnums::BE_bigendian 0
0 15 BE_littleendian 25 BamEnums::BE_littleendian 0
1 9 BE_native 19 BamEnums::BE_native 0
1 0 365
/**
   * This defines an enumerated type used to represent the endianness of
   * certain numeric values stored in a Bam file.  It really has only two
   * possible values, either BE_bigendian or BE_littleendian; but through a
   * preprocessor trick we also add BE_native, which is the same numerically
   * as whichever value the hardware supports natively.
   */

1954 13 BamObjectCode 0 794624 23 BamEnums::BamObjectCode 23 BamEnums::BamObjectCode 1952 0 0 0 0 0 0 0 0 0 5 8 BOC_push 18 BamEnums::BOC_push 107
// Indicates an object definition, and will always be eventually paired
// with a BOC_pop (which does not).
0 7 BOC_pop 17 BamEnums::BOC_pop 0
1 11 BOC_adjunct 21 BamEnums::BOC_adjunct 105
// Includes an object definition but does not push the level; it is
// associated with the current level.
2 10 BOC_remove 20 BamEnums::BOC_remove 65
// Lists object IDs that have been deallocated on the sender end.
3 13 BOC_file_data 23 BamEnums::BOC_file_data 137
// May appear at any level and indicates the following datagram contains
// auxiliary file data that may be referenced by a later object.
4 0 107
/**
   * This is the code written along with each object.  It is used to control
   * object scoping.
   */

1955 14 BamTextureMode 0 794624 24 BamEnums::BamTextureMode 24 BamEnums::BamTextureMode 1952 0 0 0 0 0 0 0 0 0 5 13 BTM_unchanged 23 BamEnums::BTM_unchanged 0
0 12 BTM_fullpath 22 BamEnums::BTM_fullpath 0
1 12 BTM_relative 22 BamEnums::BTM_relative 0
2 12 BTM_basename 22 BamEnums::BTM_basename 0
3 11 BTM_rawdata 21 BamEnums::BTM_rawdata 0
4 0 73
// This enum is used to control how textures are written to a bam stream.

1956 13 LoaderOptions 0 141313 13 LoaderOptions 13 LoaderOptions 0 0 0 1 1120 1130 4 2331 2332 2333 2334 9 1121 1122 1123 1124 1125 1126 1127 1128 1129 0 0 0 0 2 1957 1958 65
/**
 * Specifies parameters that may be passed to the loader.
 */

1957 11 LoaderFlags 0 794624 26 LoaderOptions::LoaderFlags 26 LoaderOptions::LoaderFlags 1956 0 0 0 0 0 0 0 0 0 10 9 LF_search 24 LoaderOptions::LF_search 0
1 16 LF_report_errors 31 LoaderOptions::LF_report_errors 0
2 19 LF_convert_skeleton 34 LoaderOptions::LF_convert_skeleton 0
4 19 LF_convert_channels 34 LoaderOptions::LF_convert_channels 0
8 15 LF_convert_anim 30 LoaderOptions::LF_convert_anim 22
// skeleton + channels
12 16 LF_no_disk_cache 31 LoaderOptions::LF_no_disk_cache 20
// disallow BamCache
16 15 LF_no_ram_cache 30 LoaderOptions::LF_no_ram_cache 21
// disallow ModelPool
32 11 LF_no_cache 26 LoaderOptions::LF_no_cache 19
// no_disk + no_ram
48 13 LF_cache_only 28 LoaderOptions::LF_cache_only 23
// fail if not in cache
64 17 LF_allow_instance 32 LoaderOptions::LF_allow_instance 35
// returned pointer might be shared
128 0 33
// Flags for loading model files.

1958 12 TextureFlags 0 794624 27 LoaderOptions::TextureFlags 27 LoaderOptions::TextureFlags 1956 0 0 0 0 0 0 0 0 0 8 10 TF_preload 25 LoaderOptions::TF_preload 30
// Texture will have RAM image
4 17 TF_preload_simple 32 LoaderOptions::TF_preload_simple 37
// Texture will have simple RAM image
8 11 TF_allow_1d 26 LoaderOptions::TF_allow_1d 40
// If texture is Nx1, make a 1-d texture
16 19 TF_generate_mipmaps 34 LoaderOptions::TF_generate_mipmaps 30
// Consider generating mipmaps
32 12 TF_multiview 27 LoaderOptions::TF_multiview 36
// Load a multiview texture in pages
64 10 TF_integer 25 LoaderOptions::TF_integer 35
// Load as an integer (RGB) texture
128 8 TF_float 23 LoaderOptions::TF_float 43
// Load as a floating-point (depth) texture
256 20 TF_allow_compression 35 LoaderOptions::TF_allow_compression 33
// Consider compressing RAM image
512 0 35
// Flags for loading texture files.

1959 9 BamReader 0 26625 9 BamReader 9 BamReader 0 0 0 1 1131 1132 6 2335 2336 2337 2338 2339 2340 20 1133 1134 1135 1136 1137 1138 1139 1140 1141 1142 1143 1144 1145 1146 1147 1148 1149 1150 1151 1152 0 0 1 0 1952 0 0 0 0 1311
/**
 * This is the fundamental interface for extracting binary objects from a Bam
 * file, as generated by a BamWriter.
 *
 * A Bam file can be thought of as a linear collection of objects.  Each
 * object is an instance of a class that inherits, directly or indirectly,
 * from TypedWritable.  The objects may include pointers to other objects
 * within the Bam file; the BamReader automatically manages these (with help
 * from code within each class) and restores the pointers correctly.
 *
 * This is the abstract interface and does not specifically deal with disk
 * files, but rather with a DatagramGenerator of some kind, which is simply a
 * linear source of Datagrams.  It is probably from a disk file, but it might
 * conceivably be streamed directly from a network or some such nonsense.
 *
 * Bam files are most often used to store scene graphs or subgraphs, and by
 * convention they are given filenames ending in the extension ".bam" when
 * they are used for this purpose.  However, a Bam file may store any
 * arbitrary list of TypedWritable objects; in this more general usage, they
 * are given filenames ending in ".boo" to differentiate them from the more
 * common scene graph files.
 *
 * See also BamFile, which defines a higher-level interface to read and write
 * Bam files on disk.
 */

1960 19 DatagramGenerator * 0 8576 19 DatagramGenerator * 19 DatagramGenerator * 0 0 1961 0 0 0 0 0 0 0 0 0 0

1961 17 DatagramGenerator 0 2048 17 DatagramGenerator 17 DatagramGenerator 0 0 0 0 0 0 0 0 0 0 0 0 125
/**
 * This class defines the abstract interace to any source of datagrams,
 * whether it be from a file or from the net.
 */

1962 19 LoaderOptions const 0 8832 19 LoaderOptions const 19 LoaderOptions const 0 0 1956 0 0 0 0 0 0 0 0 0 0

1963 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 1964 0 0 0 0 0 0 0 0 0 0

1964 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 1965 0 0 0 0 0 0 0 0 0 0

1965 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

1966 9 BamWriter 0 26625 9 BamWriter 9 BamWriter 0 0 0 1 1153 1154 7 2341 2342 2343 2344 2345 2346 2347 16 1155 1156 1157 1158 1159 1160 1161 1162 1163 1164 1165 1166 1167 1168 1169 1170 0 0 1 0 1952 0 0 0 0 1376
/**
 * This is the fundamental interface for writing binary objects to a Bam file,
 * to be extracted later by a BamReader.
 *
 * A Bam file can be thought of as a linear collection of objects.  Each
 * object is an instance of a class that inherits, directly or indirectly,
 * from TypedWritable.  The objects may include pointers to other objects; the
 * BamWriter automatically manages these (with help from code within each
 * class) and writes all referenced objects to the file in such a way that the
 * pointers may be correctly restored later.
 *
 * This is the abstract interface and does not specifically deal with disk
 * files, but rather with a DatagramSink of some kind, which simply accepts a
 * linear stream of Datagrams.  It is probably written to a disk file, but it
 * might conceivably be streamed directly to a network or some such nonsense.
 *
 * Bam files are most often used to store scene graphs or subgraphs, and by
 * convention they are given filenames ending in the extension ".bam" when
 * they are used for this purpose.  However, a Bam file may store any
 * arbitrary list of TypedWritable objects; in this more general usage, they
 * are given filenames ending in ".boo" to differentiate them from the more
 * common scene graph files.
 *
 * See also BamFile, which defines a higher-level interface to read and write
 * Bam files on disk.
 */

1967 14 DatagramSink * 0 8576 14 DatagramSink * 14 DatagramSink * 0 0 1968 0 0 0 0 0 0 0 0 0 0

1968 12 DatagramSink 0 2048 12 DatagramSink 12 DatagramSink 0 0 0 0 0 0 0 0 0 0 0 0 135
/**
 * This class defines the abstract interface to sending datagrams to any
 * target, whether it be into a file or across the net
 */

1969 23 BitMask< uint16_t, 16 > 0 141313 23 BitMask< uint16_t, 16 > 23 BitMask< uint16_t, 16 > 0 0 0 1 1172 1235 0 62 1173 1174 1175 1176 1177 1178 1179 1180 1181 1182 1183 1184 1185 1186 1187 1188 1189 1190 1191 1192 1193 1194 1195 1196 1197 1198 1199 1200 1201 1202 1203 1204 1205 1206 1207 1208 1209 1210 1211 1212 1213 1214 1215 1216 1217 1218 1219 1220 1221 1222 1223 1224 1225 1226 1227 1228 1229 1230 1231 1232 1233 1234 0 0 0 0 0 0

1970 9 BitMask16 0 2105345 9 BitMask16 9 BitMask16 0 0 1969 0 0 0 0 0 0 0 0 0 0

1971 23 BitMask< uint32_t, 32 > 0 141313 23 BitMask< uint32_t, 32 > 23 BitMask< uint32_t, 32 > 0 0 0 1 1236 1299 0 62 1237 1238 1239 1240 1241 1242 1243 1244 1245 1246 1247 1248 1249 1250 1251 1252 1253 1254 1255 1256 1257 1258 1259 1260 1261 1262 1263 1264 1265 1266 1267 1268 1269 1270 1271 1272 1273 1274 1275 1276 1277 1278 1279 1280 1281 1282 1283 1284 1285 1286 1287 1288 1289 1290 1291 1292 1293 1294 1295 1296 1297 1298 0 0 0 0 0 0

1972 9 BitMask32 0 2105345 9 BitMask32 9 BitMask32 0 0 1971 0 0 0 0 0 0 0 0 0 0

1973 23 BitMask< uint64_t, 64 > 0 141313 23 BitMask< uint64_t, 64 > 23 BitMask< uint64_t, 64 > 0 0 0 1 1300 1363 0 62 1301 1302 1303 1304 1305 1306 1307 1308 1309 1310 1311 1312 1313 1314 1315 1316 1317 1318 1319 1320 1321 1322 1323 1324 1325 1326 1327 1328 1329 1330 1331 1332 1333 1334 1335 1336 1337 1338 1339 1340 1341 1342 1343 1344 1345 1346 1347 1348 1349 1350 1351 1352 1353 1354 1355 1356 1357 1358 1359 1360 1361 1362 0 0 0 0 0 0

1974 9 BitMask64 0 2105345 9 BitMask64 9 BitMask64 0 0 1973 0 0 0 0 0 0 0 0 0 0

1975 13 BitMaskNative 0 1 13 BitMaskNative 13 BitMaskNative 0 0 0 0 0 0 0 0 0 0 0 0 0

1976 8 BitArray 0 141313 8 BitArray 8 BitArray 0 0 0 1 1364 1423 0 58 1365 1366 1367 1368 1369 1370 1371 1372 1373 1374 1375 1376 1377 1378 1379 1380 1381 1382 1383 1384 1385 1386 1387 1388 1389 1390 1391 1392 1393 1394 1395 1396 1397 1398 1399 1400 1401 1402 1403 1404 1405 1406 1407 1408 1409 1410 1411 1412 1413 1414 1415 1416 1417 1418 1419 1420 1421 1422 0 0 0 0 1 1977 210
/**
 * A dynamic array with an unlimited number of bits.
 *
 * This is similar to a BitMask, except it appears to contain an infinite
 * number of bits.  You can use it very much as you would use a BitMask.
 */

1977 0 0 794624 10 BitArray:: 0 1976 0 0 0 0 0 0 0 0 0 1 17 num_bits_per_word 27 BitArray::num_bits_per_word 0
64 0 0

1978 12 ButtonHandle 0 16918529 12 ButtonHandle 12 ButtonHandle 0 0 0 1 1424 1443 4 2348 2349 2350 2351 17 1425 1426 1427 1428 1429 1430 1431 1432 1433 1434 1435 1436 1437 1438 1439 1440 1442 0 1 1441 0 0 0 162
/**
 * A ButtonHandle represents a single button from any device, including
 * keyboard buttons and mouse buttons (but see KeyboardButton and
 * MouseButton).
 */

1979 6 string 0 2105344 11 std::string 11 std::string 0 0 1980 0 0 0 0 0 0 0 0 0 0

1980 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

1981 4 char 0 8194 4 char 4 char 0 5 0 0 0 0 0 0 0 0 0 0 0

1982 14 ButtonRegistry 0 141313 14 ButtonRegistry 14 ButtonRegistry 0 0 0 1 1449 1450 0 5 1444 1445 1446 1447 1448 0 0 0 0 0 188
/**
 * The ButtonRegistry class maintains all the assigned ButtonHandles in a
 * given system.  There should be only one ButtonRegistry class during the
 * lifetime of the application.
 */

1983 9 ButtonMap 0 141313 9 ButtonMap 9 ButtonMap 0 0 0 1 1458 1459 0 7 1451 1452 1453 1454 1455 1456 1457 0 0 1 0 1984 0 0 0 0 328
/**
 * This class represents a map containing all of the buttons of a (keyboard)
 * device, though it can also be used as a generic mapping between
 * ButtonHandles.  It maps an underlying 'raw' button to a 'virtual' button,
 * which may optionally be associated with an appropriate platform-specific
 * name for the button.
 */

1984 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1985 14 CallbackObject 0 141313 14 CallbackObject 14 CallbackObject 0 0 0 1 1463 1464 0 3 1460 1461 1462 0 0 1 0 1984 0 0 0 0 276
/**
 * This is a generic object that can be assigned to a callback at various
 * points in the rendering process.  This is actually a base class for a
 * handful of specialized callback object types.  You can also subclass it
 * yourself to make your own callback handler.
 */

1986 33 CachedTypedWritableReferenceCount 0 43009 33 CachedTypedWritableReferenceCount 33 CachedTypedWritableReferenceCount 0 0 0 0 0 1 2352 5 1465 1466 1467 1468 1469 0 0 1 0 1943 0 0 0 0 722
/**
 * This is a special extension to ReferenceCount that includes dual reference
 * counts: the standard reference count number, which includes all references
 * to the object, and a separate number (the cache reference count) that
 * counts the number of references to the object just within its cache alone.
 * When get_ref_count() == get_cache_ref_count(), the object is not referenced
 * outside the cache.
 *
 * The cache refs must be explicitly maintained; there is no PointerTo<> class
 * to maintain the cache reference counts automatically.  The cache reference
 * count is automatically included in the overall reference count: calling
 * cache_ref() and cache_unref() automatically calls ref() and unref().
 */

1987 12 CallbackData 0 141313 12 CallbackData 12 CallbackData 0 0 0 0 1473 0 3 1470 1471 1472 0 0 1 0 1942 0 0 0 0 348
/**
 * This is a generic data block that is passed along to a CallbackObject when
 * a callback is made.  It contains data specific to the particular callback
 * type in question.
 *
 * This is actually an abstract base class and contains no data.
 * Specializations of this class will contain the actual data relevant to each
 * callback type.
 */

1988 20 PythonCallbackObject 0 75777 20 PythonCallbackObject 20 PythonCallbackObject 0 0 0 1 1474 1464 1 2353 6 1475 1476 1477 1478 1479 1480 0 0 1 0 1985 0 0 0 0 157
/**
 * This is a specialization on CallbackObject to allow a callback to directly
 * call an arbitrary Python function.  Powerful!  But use with caution.
 */

1989 7 TimeVal 0 141313 7 TimeVal 7 TimeVal 0 0 0 1 1481 1486 1 2354 2 1482 1483 0 0 0 0 0 0

1990 9 ulong [2] 0 4202496 9 ulong [2] 9 ulong [2] 0 0 1991 2 0 0 0 0 0 0 0 0 0 0

1991 5 ulong 0 2105344 5 ulong 5 ulong 0 0 1992 0 0 0 0 0 0 0 0 0 0

1992 17 unsigned long int 0 8214 17 unsigned long int 17 unsigned long int 0 1 0 0 0 0 0 0 0 0 0 0 0

1993 11 ClockObject 0 75777 11 ClockObject 11 ClockObject 0 0 0 1 1487 0 11 2355 2356 2357 2358 2359 2360 2361 2362 2363 2364 2365 28 1488 1489 1490 1491 1492 1493 1494 1495 1496 1497 1498 1499 1500 1501 1502 1503 1504 1505 1506 1507 1508 1509 1510 1511 1512 1513 1514 1515 0 0 1 0 1944 0 0 0 1 1994 1000
/**
 * A ClockObject keeps track of elapsed real time and discrete time.  In
 * normal mode, get_frame_time() returns the time as of the last time tick()
 * was called.  This is the "discrete" time, and is usually used to get the
 * time as of, for instance, the beginning of the current frame.
 *
 * In other modes, as set by set_mode() or the clock-mode config variable,
 * get_frame_time() may return other values to simulate different timing
 * effects, for instance to perform non-real-time animation.  See set_mode().
 *
 * In all modes, get_real_time() always returns the elapsed real time in
 * seconds since the ClockObject was constructed, or since it was last reset.
 *
 * You can create your own ClockObject whenever you want to have your own
 * local timer.  There is also a default, global ClockObject intended to
 * represent global time for the application; this is normally set up to tick
 * every frame so that its get_frame_time() will return the time for the
 * current frame.
 */

1994 4 Mode 0 794624 17 ClockObject::Mode 17 ClockObject::Mode 1993 0 0 0 0 0 0 0 0 0 8 8 M_normal 21 ClockObject::M_normal 0
0 15 M_non_real_time 28 ClockObject::M_non_real_time 0
1 8 M_forced 21 ClockObject::M_forced 0
2 9 M_degrade 22 ClockObject::M_degrade 0
3 7 M_slave 20 ClockObject::M_slave 0
4 9 M_limited 22 ClockObject::M_limited 0
5 9 M_integer 22 ClockObject::M_integer 0
6 17 M_integer_limited 30 ClockObject::M_integer_limited 0
7 0 0

1995 11 CollideMask 0 2105345 11 CollideMask 11 CollideMask 0 0 1972 0 0 0 0 0 0 0 0 0 0

1996 10 ColorSpace 0 532481 10 ColorSpace 10 ColorSpace 0 0 0 0 0 0 0 0 0 0 4 14 CS_unspecified 14 CS_unspecified 105
// This value is not a color space, but is used to indicate that a color
// space has not been specified.
0 9 CS_linear 9 CS_linear 389
// CS_linear is not a color space per se, but represents the working color
// space of graphics APIs, which is linearized.  Since the conversion from
// sRGB to linear is defined, one could posit that it has the ITU-R BT.709
// primaries, but this isn't meaningful as modern graphics APIs do not
// perform color management.  All colors in Panda3D are linear unless
// otherwise specified.
1 7 CS_sRGB 7 CS_sRGB 107
// This is the standard, gamma-2.2-corrected sRGB color space, as used by
// the majority of image formats.
2 8 CS_scRGB 8 CS_scRGB 110
// This is a 16-bit encoded linear color space capable of encoding color
// values in the -0.5...7.4999 range.
3 0 0

1997 17 CopyOnWriteObject 0 141313 17 CopyOnWriteObject 17 CopyOnWriteObject 0 0 0 0 1523 0 3 1520 1521 1522 0 0 1 0 1986 0 0 0 0 169
/**
 * This base class provides basic reference counting, but also can be used
 * with a CopyOnWritePointer to provide get_read_pointer() and
 * get_write_pointer().
 */

1998 14 DatagramBuffer 0 141313 14 DatagramBuffer 14 DatagramBuffer 0 0 0 1 1528 1532 1 2366 1 1529 0 0 2 3 1968 1524 1525 3 1961 1526 1527 0 0 355
/**
 * This class can be used to write a series of datagrams into a memory buffer.
 * It acts as both a datagram sink and generator; you can fill it up with
 * datagrams and then read as many datagrams from it.
 *
 * This uses the same format as DatagramInputFile and DatagramOutputFile,
 * meaning that Datagram sizes are always stored little-endian.
 */

1999 18 vector_uchar const 0 8832 18 vector_uchar const 18 vector_uchar const 0 0 2000 0 0 0 0 0 0 0 0 0 0

2000 12 vector_uchar 0 2105344 12 vector_uchar 12 vector_uchar 0 0 2001 0 0 0 0 0 0 0 0 0 0

2001 24 pvector< unsigned char > 0 2048 24 pvector< unsigned char > 24 pvector< unsigned char > 0 0 0 0 0 0 0 0 0 0 0 0 0

2002 17 DatagramInputFile 0 75777 17 DatagramInputFile 17 DatagramInputFile 0 0 0 1 1533 0 0 4 1534 1535 1536 1537 0 0 1 0 1961 0 0 0 0 130
/**
 * This class can be used to read a binary file that consists of an arbitrary
 * header followed by a number of datagrams.
 */

2003 18 DatagramOutputFile 0 75777 18 DatagramOutputFile 18 DatagramOutputFile 0 0 0 1 1538 0 1 2367 3 1539 1540 1541 0 0 1 0 1968 0 0 0 0 131
/**
 * This class can be used to write a binary file that consists of an arbitrary
 * header followed by a number of datagrams.
 */

2004 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

2005 30 DoubleBitMask< BitMaskNative > 0 141313 30 DoubleBitMask< BitMaskNative > 30 DoubleBitMask< BitMaskNative > 0 0 0 1 1543 1596 0 52 1544 1545 1546 1547 1548 1549 1550 1551 1552 1553 1554 1555 1556 1557 1558 1559 1560 1561 1562 1563 1564 1565 1566 1567 1568 1569 1570 1571 1572 1573 1574 1575 1576 1577 1578 1579 1580 1581 1582 1583 1584 1585 1586 1587 1588 1589 1590 1591 1592 1593 1594 1595 0 0 0 0 0 0

2006 19 DoubleBitMaskNative 0 2105345 19 DoubleBitMaskNative 19 DoubleBitMaskNative 0 0 2005 0 0 0 0 0 0 0 0 0 0

2007 36 DoubleBitMask< DoubleBitMaskNative > 0 141313 36 DoubleBitMask< DoubleBitMaskNative > 36 DoubleBitMask< DoubleBitMaskNative > 0 0 0 1 1597 1650 0 52 1598 1599 1600 1601 1602 1603 1604 1605 1606 1607 1608 1609 1610 1611 1612 1613 1614 1615 1616 1617 1618 1619 1620 1621 1622 1623 1624 1625 1626 1627 1628 1629 1630 1631 1632 1633 1634 1635 1636 1637 1638 1639 1640 1641 1642 1643 1644 1645 1646 1647 1648 1649 0 0 0 0 0 0

2008 17 QuadBitMaskNative 0 2105345 17 QuadBitMaskNative 17 QuadBitMaskNative 0 0 2007 0 0 0 0 0 0 0 0 0 0

2009 8 DrawMask 0 2105345 8 DrawMask 8 DrawMask 0 0 1972 0 0 0 0 0 0 0 0 0 0

2010 13 GamepadButton 0 141313 13 GamepadButton 13 GamepadButton 0 0 0 1 1682 1683 0 31 1651 1652 1653 1654 1655 1656 1657 1658 1659 1660 1661 1662 1663 1664 1665 1666 1667 1668 1669 1670 1671 1672 1673 1674 1675 1676 1677 1678 1679 1680 1681 0 0 0 0 0 155
/**
 * This class is just used as a convenient namespace for grouping all of these
 * handy functions that return buttons which map to gamepad buttons.
 */

2011 14 KeyboardButton 0 141313 14 KeyboardButton 14 KeyboardButton 0 0 0 1 1736 1737 0 52 1684 1685 1686 1687 1688 1689 1690 1691 1692 1693 1694 1695 1696 1697 1698 1699 1700 1701 1702 1703 1704 1705 1706 1707 1708 1709 1710 1711 1712 1713 1714 1715 1716 1717 1718 1719 1720 1721 1722 1723 1724 1725 1726 1727 1728 1729 1730 1731 1732 1733 1734 1735 0 0 0 0 0 162
/**
 * This class is just used as a convenient namespace for grouping all of these
 * handy functions that return buttons which map to standard keyboard keys.
 */

2012 15 ModifierButtons 0 26625 15 ModifierButtons 15 ModifierButtons 0 0 0 1 1742 1743 1 2368 23 1744 1745 1746 1747 1748 1749 1750 1751 1752 1753 1754 1755 1756 1757 1758 1759 1760 1761 1762 1763 1764 1765 1766 1 2391 0 0 0 0 135
/**
 * This class monitors the state of a number of individual buttons and tracks
 * whether each button is known to be down or up.
 */

2013 11 MouseButton 0 141313 11 MouseButton 11 MouseButton 0 0 0 1 1778 1779 0 11 1767 1768 1769 1770 1771 1772 1773 1774 1775 1776 1777 0 0 0 0 0 162
/**
 * This class is just used as a convenient namespace for grouping all of these
 * handy functions that return buttons which map to standard mouse buttons.
 */

2014 11 PointerType 0 8921089 11 PointerType 11 PointerType 0 0 0 0 0 0 0 0 0 0 5 7 unknown 7 unknown 0
0 5 mouse 5 mouse 0
1 6 finger 6 finger 0
2 6 stylus 6 stylus 0
3 6 eraser 6 eraser 0
4 0 48
/**
 * Contains the types of pointer device.
 */

2015 11 PointerData 0 141313 11 PointerData 11 PointerData 0 0 0 1 1786 1787 6 2369 2370 2371 2372 2373 2374 3 1780 1781 1782 0 0 0 0 0 124
/**
 * Holds the data that might be generated by a 2-d pointer input device, such
 * as the mouse in the GraphicsWindow.
 */

2016 9 MouseData 0 2105345 9 MouseData 9 MouseData 0 0 2015 0 0 0 0 0 0 0 0 0 0

2017 24 NodeCachedReferenceCount 0 43009 24 NodeCachedReferenceCount 24 NodeCachedReferenceCount 0 0 0 0 0 0 6 1788 1789 1790 1791 1792 1793 0 0 1 0 1986 0 0 0 1 2018 1495
/**
 * This class further specializes CachedTypedWritableReferenceCount to also
 * add a node_ref_count, for the purposes of counting the number of times the
 * object is referenced by a "node", presumably a PandaNode.
 *
 * This essentially combines the functionality of NodeReferenceCount and
 * CachedTypedWritableReferenceCount, so that a derivative of this object
 * actually has three counters: the standard reference count, the "cache"
 * reference count, and the "node" reference count.  Rather than multiply
 * inheriting from the two reference count classes, we inherit only from
 * CachedTypedWritableReferenceCount and simply duplicate the functionality of
 * NodeReferenceCount, to avoid all of the problems associated with multiple
 * inheritance.
 *
 * The intended design is to use this as a base class for RenderState and
 * TransformState, both of which are held by PandaNodes, and also have caches
 * which are independently maintained.  By keeping track of how many nodes
 * hold a pointer to a particular object, we can classify each object into
 * node-referenced, cache-referenced, or other, which is primarily useful for
 * PStats reporting.
 *
 * As with CachedTypedWritableReferenceCount's cache_ref() and cache_unref(),
 * the new methods node_ref() and node_unref() automatically increment and
 * decrement the primary reference count as well.  In this case, however,
 * there does exist a NodePointerTo<> class to maintain the node_ref counters
 * automatically.
 */

2018 10 Referenced 0 794624 36 NodeCachedReferenceCount::Referenced 36 NodeCachedReferenceCount::Referenced 2017 0 0 0 0 0 0 0 0 0 2 6 R_node 32 NodeCachedReferenceCount::R_node 0
1 7 R_cache 33 NodeCachedReferenceCount::R_cache 0
2 0 0

2019 11 SparseArray 0 141313 11 SparseArray 11 SparseArray 0 0 0 1 1794 1848 0 53 1795 1796 1797 1798 1799 1800 1801 1802 1803 1804 1805 1806 1807 1808 1809 1810 1811 1812 1813 1814 1815 1816 1817 1818 1819 1820 1821 1822 1823 1824 1825 1826 1827 1828 1829 1830 1831 1832 1833 1834 1835 1836 1837 1838 1839 1840 1841 1842 1843 1844 1845 1846 1847 0 0 0 0 0 736
/**
 * This class records a set of integers, where each integer is either present
 * or not present in the set.
 *
 * It is similar in principle and in interface to a BitArray (which can be
 * thought of as a set of integers, one integer corresponding to each
 * different bit position), but the SparseArray is implemented as a list of
 * min/max subrange lists, rather than as a bitmask.
 *
 * This makes it particularly efficient for storing sets which consist of
 * large sections of consecutively included or consecutively excluded
 * elements, with arbitrarily large integers, but particularly inefficient for
 * doing boolean operations such as & or |.
 *
 * Also, unlike BitArray, the SparseArray can store negative integers.
 */

2020 14 ParamValueBase 0 75777 14 ParamValueBase 14 ParamValueBase 0 0 0 0 1065 0 3 1849 1850 1851 0 0 1 0 1943 0 0 0 0 141
/**
 * A non-template base class of ParamValue (below), which serves mainly to
 * define the placeholder for the virtual output function.
 */

2021 18 ParamTypedRefCount 0 75777 18 ParamTypedRefCount 18 ParamTypedRefCount 0 0 0 1 1852 1065 1 2375 2 1853 1854 0 0 1 0 2020 0 0 0 0 143
/**
 * A class object for storing specifically objects of type
 * TypedReferenceCount, which is different than TypedWritableReferenceCount.
 */

2022 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 1984 0 0 0 0 0 0 0 0 0 0

2023 20 ParamValue< string > 0 75777 25 ParamValue< std::string > 25 ParamValue< std::string > 0 0 0 1 1855 1065 1 2376 3 1856 1857 1858 0 0 1 0 2020 0 0 0 0 0

2024 10 Type const 0 8832 10 Type const 10 Type const 0 0 0 0 0 0 0 0 0 0 0 0 0

2025 11 ParamString 0 2105345 11 ParamString 11 ParamString 0 0 2023 0 0 0 0 0 0 0 0 0 0

2026 21 ParamValue< wstring > 0 75777 26 ParamValue< std::wstring > 26 ParamValue< std::wstring > 0 0 0 1 1859 1065 1 2377 3 1860 1861 1862 0 0 1 0 2020 0 0 0 0 0

2027 12 ParamWstring 0 2105345 12 ParamWstring 12 ParamWstring 0 0 2026 0 0 0 0 0 0 0 0 0 0

2028 24 ParamValue< LVecBase2d > 0 75777 24 ParamValue< LVecBase2d > 24 ParamValue< LVecBase2d > 0 0 0 1 1863 1065 1 2378 3 1864 1865 1866 0 0 1 0 2020 0 0 0 0 0

2029 14 ParamVecBase2d 0 2105345 14 ParamVecBase2d 14 ParamVecBase2d 0 0 2028 0 0 0 0 0 0 0 0 0 0

2030 24 ParamValue< LVecBase2f > 0 75777 24 ParamValue< LVecBase2f > 24 ParamValue< LVecBase2f > 0 0 0 1 1867 1065 1 2379 3 1868 1869 1870 0 0 1 0 2020 0 0 0 0 0

2031 14 ParamVecBase2f 0 2105345 14 ParamVecBase2f 14 ParamVecBase2f 0 0 2030 0 0 0 0 0 0 0 0 0 0

2032 24 ParamValue< LVecBase2i > 0 75777 24 ParamValue< LVecBase2i > 24 ParamValue< LVecBase2i > 0 0 0 1 1871 1065 1 2380 3 1872 1873 1874 0 0 1 0 2020 0 0 0 0 0

2033 14 ParamVecBase2i 0 2105345 14 ParamVecBase2i 14 ParamVecBase2i 0 0 2032 0 0 0 0 0 0 0 0 0 0

2034 24 ParamValue< LVecBase3d > 0 75777 24 ParamValue< LVecBase3d > 24 ParamValue< LVecBase3d > 0 0 0 1 1875 1065 1 2381 3 1876 1877 1878 0 0 1 0 2020 0 0 0 0 0

2035 14 ParamVecBase3d 0 2105345 14 ParamVecBase3d 14 ParamVecBase3d 0 0 2034 0 0 0 0 0 0 0 0 0 0

2036 24 ParamValue< LVecBase3f > 0 75777 24 ParamValue< LVecBase3f > 24 ParamValue< LVecBase3f > 0 0 0 1 1879 1065 1 2382 3 1880 1881 1882 0 0 1 0 2020 0 0 0 0 0

2037 14 ParamVecBase3f 0 2105345 14 ParamVecBase3f 14 ParamVecBase3f 0 0 2036 0 0 0 0 0 0 0 0 0 0

2038 24 ParamValue< LVecBase3i > 0 75777 24 ParamValue< LVecBase3i > 24 ParamValue< LVecBase3i > 0 0 0 1 1883 1065 1 2383 3 1884 1885 1886 0 0 1 0 2020 0 0 0 0 0

2039 14 ParamVecBase3i 0 2105345 14 ParamVecBase3i 14 ParamVecBase3i 0 0 2038 0 0 0 0 0 0 0 0 0 0

2040 24 ParamValue< LVecBase4d > 0 75777 24 ParamValue< LVecBase4d > 24 ParamValue< LVecBase4d > 0 0 0 1 1887 1065 1 2384 3 1888 1889 1890 0 0 1 0 2020 0 0 0 0 0

2041 14 ParamVecBase4d 0 2105345 14 ParamVecBase4d 14 ParamVecBase4d 0 0 2040 0 0 0 0 0 0 0 0 0 0

2042 24 ParamValue< LVecBase4f > 0 75777 24 ParamValue< LVecBase4f > 24 ParamValue< LVecBase4f > 0 0 0 1 1891 1065 1 2385 3 1892 1893 1894 0 0 1 0 2020 0 0 0 0 0

2043 14 ParamVecBase4f 0 2105345 14 ParamVecBase4f 14 ParamVecBase4f 0 0 2042 0 0 0 0 0 0 0 0 0 0

2044 24 ParamValue< LVecBase4i > 0 75777 24 ParamValue< LVecBase4i > 24 ParamValue< LVecBase4i > 0 0 0 1 1895 1065 1 2386 3 1896 1897 1898 0 0 1 0 2020 0 0 0 0 0

2045 14 ParamVecBase4i 0 2105345 14 ParamVecBase4i 14 ParamVecBase4i 0 0 2044 0 0 0 0 0 0 0 0 0 0

2046 23 ParamValue< LMatrix3d > 0 75777 23 ParamValue< LMatrix3d > 23 ParamValue< LMatrix3d > 0 0 0 1 1899 1065 1 2387 3 1900 1901 1902 0 0 1 0 2020 0 0 0 0 0

2047 13 ParamMatrix3d 0 2105345 13 ParamMatrix3d 13 ParamMatrix3d 0 0 2046 0 0 0 0 0 0 0 0 0 0

2048 23 ParamValue< LMatrix3f > 0 75777 23 ParamValue< LMatrix3f > 23 ParamValue< LMatrix3f > 0 0 0 1 1903 1065 1 2388 3 1904 1905 1906 0 0 1 0 2020 0 0 0 0 0

2049 13 ParamMatrix3f 0 2105345 13 ParamMatrix3f 13 ParamMatrix3f 0 0 2048 0 0 0 0 0 0 0 0 0 0

2050 23 ParamValue< LMatrix4d > 0 75777 23 ParamValue< LMatrix4d > 23 ParamValue< LMatrix4d > 0 0 0 1 1907 1065 1 2389 3 1908 1909 1910 0 0 1 0 2020 0 0 0 0 0

2051 13 ParamMatrix4d 0 2105345 13 ParamMatrix4d 13 ParamMatrix4d 0 0 2050 0 0 0 0 0 0 0 0 0 0

2052 23 ParamValue< LMatrix4f > 0 75777 23 ParamValue< LMatrix4f > 23 ParamValue< LMatrix4f > 0 0 0 1 1911 1065 1 2390 3 1912 1913 1914 0 0 1 0 2020 0 0 0 0 0

2053 13 ParamMatrix4f 0 2105345 13 ParamMatrix4f 13 ParamMatrix4f 0 0 2052 0 0 0 0 0 0 0 0 0 0

2054 13 ParamVecBase2 0 2105345 13 ParamVecBase2 13 ParamVecBase2 0 0 2031 0 0 0 0 0 0 0 0 0 0

2055 13 ParamVecBase3 0 2105345 13 ParamVecBase3 13 ParamVecBase3 0 0 2037 0 0 0 0 0 0 0 0 0 0

2056 13 ParamVecBase4 0 2105345 13 ParamVecBase4 13 ParamVecBase4 0 0 2043 0 0 0 0 0 0 0 0 0 0

2057 12 ParamMatrix3 0 2105345 12 ParamMatrix3 12 ParamMatrix3 0 0 2049 0 0 0 0 0 0 0 0 0 0

2058 12 ParamMatrix4 0 2105345 12 ParamMatrix4 12 ParamMatrix4 0 0 2053 0 0 0 0 0 0 0 0 0 0

2059 20 WritableConfigurable 0 141313 20 WritableConfigurable 20 WritableConfigurable 0 0 0 0 1916 0 1 1915 0 0 1 0 1941 0 0 0 0 379
/**
 * Defined as a fix to allow creating Configurable and Writable objects.
 * Otherwise the compiler gets confused since both TypedWritable and
 * Configurable inherit from TypedObject.
 *
 * An object that has data or parameters that are set less frequently (at
 * least occasionally) than every frame.  We can cache the configuration info
 * by by using the "dirty" flag.
 */

2060 17 UniqueIdAllocator 0 26625 17 UniqueIdAllocator 17 UniqueIdAllocator 0 0 0 1 1917 1918 0 6 1919 1920 1921 1922 1923 1924 0 0 0 0 0 812
/**
 * Manage a set of ID values from min to max inclusive.  The ID numbers that
 * are freed will be allocated (reused) in the same order.  I.e.  the oldest
 * ID numbers will be allocated.
 *
 * This implementation will use 4 bytes per id number, plus a few bytes of
 * management data.  e.g.  10,000 ID numbers will use 40KB.
 *
 * Also be advised that ID -1 and -2 are used internally by the allocator.  If
 * allocate returns IndexEnd (-1) then the allocator is out of free ID
 * numbers.
 *
 * There are other implementations that can better leverage runs of used or
 * unused IDs or use bit arrays for the IDs.  But, it takes extra work to
 * track the age of freed IDs, which is required for what we wanted.  If you
 * would like to kick around other implementation ideas, please contact
 * Schuyler.
 */

2061 10 PortalMask 0 2105345 10 PortalMask 10 PortalMask 0 0 1972 0 0 0 0 0 0 0 0 0 0

2062 37 ConstPointerToArray< ushort > const * 0 8576 37 ConstPointerToArray< ushort > const * 37 ConstPointerToArray< ushort > const * 0 0 2063 0 0 0 0 0 0 0 0 0 0

2063 35 ConstPointerToArray< ushort > const 0 8832 35 ConstPointerToArray< ushort > const 35 ConstPointerToArray< ushort > const 0 0 1927 0 0 0 0 0 0 0 0 0 0

2064 31 ConstPointerToArray< ushort > * 0 8576 31 ConstPointerToArray< ushort > * 31 ConstPointerToArray< ushort > * 0 0 1927 0 0 0 0 0 0 0 0 0 0

2065 32 PointerToArray< ushort > const * 0 8576 32 PointerToArray< ushort > const * 32 PointerToArray< ushort > const * 0 0 2066 0 0 0 0 0 0 0 0 0 0

2066 30 PointerToArray< ushort > const 0 8832 30 PointerToArray< ushort > const 30 PointerToArray< ushort > const 0 0 1931 0 0 0 0 0 0 0 0 0 0

2067 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

2068 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

2069 6 ushort 0 2105344 6 ushort 6 ushort 0 0 2070 0 0 0 0 0 0 0 0 0 0

2070 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

2071 11 Py_buffer * 0 8576 11 Py_buffer * 11 Py_buffer * 0 0 2072 0 0 0 0 0 0 0 0 0 0

2072 9 Py_buffer 0 2105344 9 Py_buffer 9 Py_buffer 0 0 2073 0 0 0 0 0 0 0 0 0 0

2073 10 bufferinfo 0 1024 10 bufferinfo 10 bufferinfo 0 0 0 0 0 0 0 0 0 0 0 0 0

2074 36 PointerToArrayBase< ushort > const * 0 8576 36 PointerToArrayBase< ushort > const * 36 PointerToArrayBase< ushort > const * 0 0 2075 0 0 0 0 0 0 0 0 0 0

2075 34 PointerToArrayBase< ushort > const 0 8832 34 PointerToArrayBase< ushort > const 34 PointerToArrayBase< ushort > const 0 0 1928 0 0 0 0 0 0 0 0 0 0

2076 51 PointerToBase< ReferenceCountedVector< ushort > > * 0 8576 51 PointerToBase< ReferenceCountedVector< ushort > > * 51 PointerToBase< ReferenceCountedVector< ushort > > * 0 0 1929 0 0 0 0 0 0 0 0 0 0

2077 57 PointerToBase< ReferenceCountedVector< ushort > > const * 0 8576 57 PointerToBase< ReferenceCountedVector< ushort > > const * 57 PointerToBase< ReferenceCountedVector< ushort > > const * 0 0 2078 0 0 0 0 0 0 0 0 0 0

2078 55 PointerToBase< ReferenceCountedVector< ushort > > const 0 8832 55 PointerToBase< ReferenceCountedVector< ushort > > const 55 PointerToBase< ReferenceCountedVector< ushort > > const 0 0 1929 0 0 0 0 0 0 0 0 0 0

2079 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 2004 0 0 0 0 0 0 0 0 0 0

2080 26 PointerToArray< ushort > * 0 8576 26 PointerToArray< ushort > * 26 PointerToArray< ushort > * 0 0 1931 0 0 0 0 0 0 0 0 0 0

2081 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 2082 0 0 0 0 0 0 0 0 0 0

2082 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

2083 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

2084 15 AnimInterface * 0 8576 15 AnimInterface * 15 AnimInterface * 0 0 1932 0 0 0 0 0 0 0 0 0 0

2085 21 AnimInterface const * 0 8576 21 AnimInterface const * 21 AnimInterface const * 0 0 2086 0 0 0 0 0 0 0 0 0 0

2086 19 AnimInterface const 0 8832 19 AnimInterface const 19 AnimInterface const 0 0 1932 0 0 0 0 0 0 0 0 0 0

2087 11 UpdateSeq * 0 8576 11 UpdateSeq * 11 UpdateSeq * 0 0 1937 0 0 0 0 0 0 0 0 0 0

2088 17 UpdateSeq const * 0 8576 17 UpdateSeq const * 17 UpdateSeq const * 0 0 2089 0 0 0 0 0 0 0 0 0 0

2089 15 UpdateSeq const 0 8832 15 UpdateSeq const 15 UpdateSeq const 0 0 1937 0 0 0 0 0 0 0 0 0 0

2090 18 DatagramIterator * 0 8576 18 DatagramIterator * 18 DatagramIterator * 0 0 2091 0 0 0 0 0 0 0 0 0 0

2091 16 DatagramIterator 0 2048 16 DatagramIterator 16 DatagramIterator 0 0 0 0 0 0 0 0 0 0 0 0 0

2092 11 BamReader * 0 8576 11 BamReader * 11 BamReader * 0 0 1959 0 0 0 0 0 0 0 0 0 0

2093 21 TypedWritable const * 0 8576 21 TypedWritable const * 21 TypedWritable const * 0 0 2094 0 0 0 0 0 0 0 0 0 0

2094 19 TypedWritable const 0 8832 19 TypedWritable const 19 TypedWritable const 0 0 1941 0 0 0 0 0 0 0 0 0 0

2095 14 vector_uchar * 0 8576 14 vector_uchar * 14 vector_uchar * 0 0 2000 0 0 0 0 0 0 0 0 0 0

2096 11 BamWriter * 0 8576 11 BamWriter * 11 BamWriter * 0 0 1966 0 0 0 0 0 0 0 0 0 0

2097 29 TypedWritableReferenceCount * 0 8576 29 TypedWritableReferenceCount * 29 TypedWritableReferenceCount * 0 0 1943 0 0 0 0 0 0 0 0 0 0

2098 16 ReferenceCount * 0 8576 16 ReferenceCount * 16 ReferenceCount * 0 0 1944 0 0 0 0 0 0 0 0 0 0

2099 22 BamCacheRecord const * 0 8576 22 BamCacheRecord const * 22 BamCacheRecord const * 0 0 2100 0 0 0 0 0 0 0 0 0 0

2100 20 BamCacheRecord const 0 8832 20 BamCacheRecord const 20 BamCacheRecord const 0 0 1945 0 0 0 0 0 0 0 0 0 0

2101 16 BamCacheRecord * 0 8576 16 BamCacheRecord * 16 BamCacheRecord * 0 0 1945 0 0 0 0 0 0 0 0 0 0

2102 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 1947 0 0 0 0 0 0 0 0 0 0

2103 19 VirtualFile const * 0 8576 19 VirtualFile const * 19 VirtualFile const * 0 0 2104 0 0 0 0 0 0 0 0 0 0

2104 17 VirtualFile const 0 8832 17 VirtualFile const 17 VirtualFile const 0 0 2105 0 0 0 0 0 0 0 0 0 0

2105 11 VirtualFile 0 2048 11 VirtualFile 11 VirtualFile 0 0 0 0 0 0 0 0 0 0 0 0 0

2106 10 BamCache * 0 8576 10 BamCache * 10 BamCache * 0 0 1951 0 0 0 0 0 0 0 0 0 0

2107 16 BamCache const * 0 8576 16 BamCache const * 16 BamCache const * 0 0 2108 0 0 0 0 0 0 0 0 0 0

2108 14 BamCache const 0 8832 14 BamCache const 14 BamCache const 0 0 1951 0 0 0 0 0 0 0 0 0 0

2109 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 1948 0 0 0 0 0 0 0 0 0 0

2110 10 BamEnums * 0 8576 10 BamEnums * 10 BamEnums * 0 0 1952 0 0 0 0 0 0 0 0 0 0

2111 16 BamEnums const * 0 8576 16 BamEnums const * 16 BamEnums const * 0 0 2112 0 0 0 0 0 0 0 0 0 0

2112 14 BamEnums const 0 8832 14 BamEnums const 14 BamEnums const 0 0 1952 0 0 0 0 0 0 0 0 0 0

2113 21 LoaderOptions const * 0 8576 21 LoaderOptions const * 21 LoaderOptions const * 0 0 1962 0 0 0 0 0 0 0 0 0 0

2114 15 LoaderOptions * 0 8576 15 LoaderOptions * 15 LoaderOptions * 0 0 1956 0 0 0 0 0 0 0 0 0 0

2115 9 AuxData * 0 8576 20 BamReader::AuxData * 20 BamReader::AuxData * 0 0 2116 0 0 0 0 0 0 0 0 0 0

2116 7 AuxData 0 1312768 18 BamReader::AuxData 18 BamReader::AuxData 1959 0 0 0 0 0 0 0 0 0 0 0 187
// Inherit from this class to piggyback additional temporary data on the
// bamReader (via set_aux_data() and get_aux_data()) for any particular
// objects during the bam reading process.

2117 17 BamReader const * 0 8576 17 BamReader const * 17 BamReader const * 0 0 2118 0 0 0 0 0 0 0 0 0 0

2118 15 BamReader const 0 8832 15 BamReader const 15 BamReader const 0 0 1959 0 0 0 0 0 0 0 0 0 0

2119 17 BamWriter const * 0 8576 17 BamWriter const * 17 BamWriter const * 0 0 2120 0 0 0 0 0 0 0 0 0 0

2120 15 BamWriter const 0 8832 15 BamWriter const 15 BamWriter const 0 0 1966 0 0 0 0 0 0 0 0 0 0

2121 25 BitMask< uint16_t, 16 > * 0 8576 25 BitMask< uint16_t, 16 > * 25 BitMask< uint16_t, 16 > * 0 0 1969 0 0 0 0 0 0 0 0 0 0

2122 31 BitMask< uint16_t, 16 > const * 0 8576 31 BitMask< uint16_t, 16 > const * 31 BitMask< uint16_t, 16 > const * 0 0 2123 0 0 0 0 0 0 0 0 0 0

2123 29 BitMask< uint16_t, 16 > const 0 8832 29 BitMask< uint16_t, 16 > const 29 BitMask< uint16_t, 16 > const 0 0 1969 0 0 0 0 0 0 0 0 0 0

2124 8 uint16_t 0 2105344 8 uint16_t 8 uint16_t 0 0 2070 0 0 0 0 0 0 0 0 0 0

2125 25 BitMask< uint32_t, 32 > * 0 8576 25 BitMask< uint32_t, 32 > * 25 BitMask< uint32_t, 32 > * 0 0 1971 0 0 0 0 0 0 0 0 0 0

2126 31 BitMask< uint32_t, 32 > const * 0 8576 31 BitMask< uint32_t, 32 > const * 31 BitMask< uint32_t, 32 > const * 0 0 2127 0 0 0 0 0 0 0 0 0 0

2127 29 BitMask< uint32_t, 32 > const 0 8832 29 BitMask< uint32_t, 32 > const 29 BitMask< uint32_t, 32 > const 0 0 1971 0 0 0 0 0 0 0 0 0 0

2128 8 uint32_t 0 2105344 8 uint32_t 8 uint32_t 0 0 2129 0 0 0 0 0 0 0 0 0 0

2129 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

2130 25 BitMask< uint64_t, 64 > * 0 8576 25 BitMask< uint64_t, 64 > * 25 BitMask< uint64_t, 64 > * 0 0 1973 0 0 0 0 0 0 0 0 0 0

2131 31 BitMask< uint64_t, 64 > const * 0 8576 31 BitMask< uint64_t, 64 > const * 31 BitMask< uint64_t, 64 > const * 0 0 2132 0 0 0 0 0 0 0 0 0 0

2132 29 BitMask< uint64_t, 64 > const 0 8832 29 BitMask< uint64_t, 64 > const 29 BitMask< uint64_t, 64 > const 0 0 1973 0 0 0 0 0 0 0 0 0 0

2133 8 uint64_t 0 2105344 8 uint64_t 8 uint64_t 0 0 2068 0 0 0 0 0 0 0 0 0 0

2134 10 BitArray * 0 8576 10 BitArray * 10 BitArray * 0 0 1976 0 0 0 0 0 0 0 0 0 0

2135 16 BitArray const * 0 8576 16 BitArray const * 16 BitArray const * 0 0 2136 0 0 0 0 0 0 0 0 0 0

2136 14 BitArray const 0 8832 14 BitArray const 14 BitArray const 0 0 1976 0 0 0 0 0 0 0 0 0 0

2137 8 WordType 0 2367488 18 BitArray::WordType 18 BitArray::WordType 1976 0 2133 0 0 0 0 0 0 0 0 0 0

2138 19 SparseArray const * 0 8576 19 SparseArray const * 19 SparseArray const * 0 0 2139 0 0 0 0 0 0 0 0 0 0

2139 17 SparseArray const 0 8832 17 SparseArray const 17 SparseArray const 0 0 2019 0 0 0 0 0 0 0 0 0 0

2140 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 2068 0 0 0 0 0 0 0 0 0 0

2141 8 MaskType 0 2367488 18 BitArray::MaskType 18 BitArray::MaskType 1976 0 1975 0 0 0 0 0 0 0 0 0 0

2142 10 MaskType * 0 8576 20 BitArray::MaskType * 20 BitArray::MaskType * 0 0 2141 0 0 0 0 0 0 0 0 0 0

2143 14 ButtonHandle * 0 8576 14 ButtonHandle * 14 ButtonHandle * 0 0 1978 0 0 0 0 0 0 0 0 0 0

2144 20 ButtonHandle const * 0 8576 20 ButtonHandle const * 20 ButtonHandle const * 0 0 2145 0 0 0 0 0 0 0 0 0 0

2145 18 ButtonHandle const 0 8832 18 ButtonHandle const 18 ButtonHandle const 0 0 1978 0 0 0 0 0 0 0 0 0 0

2146 22 ButtonRegistry const * 0 8576 22 ButtonRegistry const * 22 ButtonRegistry const * 0 0 2147 0 0 0 0 0 0 0 0 0 0

2147 20 ButtonRegistry const 0 8832 20 ButtonRegistry const 20 ButtonRegistry const 0 0 1982 0 0 0 0 0 0 0 0 0 0

2148 16 ButtonRegistry * 0 8576 16 ButtonRegistry * 16 ButtonRegistry * 0 0 1982 0 0 0 0 0 0 0 0 0 0

2149 11 ButtonMap * 0 8576 11 ButtonMap * 11 ButtonMap * 0 0 1983 0 0 0 0 0 0 0 0 0 0

2150 17 ButtonMap const * 0 8576 17 ButtonMap const * 17 ButtonMap const * 0 0 2151 0 0 0 0 0 0 0 0 0 0

2151 15 ButtonMap const 0 8832 15 ButtonMap const 15 ButtonMap const 0 0 1983 0 0 0 0 0 0 0 0 0 0

2152 22 CallbackObject const * 0 8576 22 CallbackObject const * 22 CallbackObject const * 0 0 2153 0 0 0 0 0 0 0 0 0 0

2153 20 CallbackObject const 0 8832 20 CallbackObject const 20 CallbackObject const 0 0 1985 0 0 0 0 0 0 0 0 0 0

2154 16 CallbackObject * 0 8576 16 CallbackObject * 16 CallbackObject * 0 0 1985 0 0 0 0 0 0 0 0 0 0

2155 41 CachedTypedWritableReferenceCount const * 0 8576 41 CachedTypedWritableReferenceCount const * 41 CachedTypedWritableReferenceCount const * 0 0 2156 0 0 0 0 0 0 0 0 0 0

2156 39 CachedTypedWritableReferenceCount const 0 8832 39 CachedTypedWritableReferenceCount const 39 CachedTypedWritableReferenceCount const 0 0 1986 0 0 0 0 0 0 0 0 0 0

2157 20 CallbackData const * 0 8576 20 CallbackData const * 20 CallbackData const * 0 0 2158 0 0 0 0 0 0 0 0 0 0

2158 18 CallbackData const 0 8832 18 CallbackData const 18 CallbackData const 0 0 1987 0 0 0 0 0 0 0 0 0 0

2159 14 CallbackData * 0 8576 14 CallbackData * 14 CallbackData * 0 0 1987 0 0 0 0 0 0 0 0 0 0

2160 22 PythonCallbackObject * 0 8576 22 PythonCallbackObject * 22 PythonCallbackObject * 0 0 1988 0 0 0 0 0 0 0 0 0 0

2161 28 PythonCallbackObject const * 0 8576 28 PythonCallbackObject const * 28 PythonCallbackObject const * 0 0 2162 0 0 0 0 0 0 0 0 0 0

2162 26 PythonCallbackObject const 0 8832 26 PythonCallbackObject const 26 PythonCallbackObject const 0 0 1988 0 0 0 0 0 0 0 0 0 0

2163 9 TimeVal * 0 8576 9 TimeVal * 9 TimeVal * 0 0 1989 0 0 0 0 0 0 0 0 0 0

2164 15 TimeVal const * 0 8576 15 TimeVal const * 15 TimeVal const * 0 0 2165 0 0 0 0 0 0 0 0 0 0

2165 13 TimeVal const 0 8832 13 TimeVal const 13 TimeVal const 0 0 1989 0 0 0 0 0 0 0 0 0 0

2166 19 ClockObject const * 0 8576 19 ClockObject const * 19 ClockObject const * 0 0 2167 0 0 0 0 0 0 0 0 0 0

2167 17 ClockObject const 0 8832 17 ClockObject const 17 ClockObject const 0 0 1993 0 0 0 0 0 0 0 0 0 0

2168 13 ClockObject * 0 8576 13 ClockObject * 13 ClockObject * 0 0 1993 0 0 0 0 0 0 0 0 0 0

2169 8 Thread * 0 8576 8 Thread * 8 Thread * 0 0 2170 0 0 0 0 0 0 0 0 0 0

2170 6 Thread 0 2048 6 Thread 6 Thread 0 0 0 0 0 0 0 0 0 0 0 0 396
/**
 * A thread; that is, a lightweight process.  This is an abstract base class;
 * to use it, you must subclass from it and redefine thread_main().
 *
 * The thread itself will keep a reference count on the Thread object while it
 * is running; when the thread returns from its root function, the Thread
 * object will automatically be destructed if no other pointers are
 * referencing it.
 */

2171 25 CopyOnWriteObject const * 0 8576 25 CopyOnWriteObject const * 25 CopyOnWriteObject const * 0 0 2172 0 0 0 0 0 0 0 0 0 0

2172 23 CopyOnWriteObject const 0 8832 23 CopyOnWriteObject const 23 CopyOnWriteObject const 0 0 1997 0 0 0 0 0 0 0 0 0 0

2173 16 DatagramBuffer * 0 8576 16 DatagramBuffer * 16 DatagramBuffer * 0 0 1998 0 0 0 0 0 0 0 0 0 0

2174 19 DatagramInputFile * 0 8576 19 DatagramInputFile * 19 DatagramInputFile * 0 0 2002 0 0 0 0 0 0 0 0 0 0

2175 21 FileReference const * 0 8576 21 FileReference const * 21 FileReference const * 0 0 2176 0 0 0 0 0 0 0 0 0 0

2176 19 FileReference const 0 8832 19 FileReference const 19 FileReference const 0 0 2177 0 0 0 0 0 0 0 0 0 0

2177 13 FileReference 0 2048 13 FileReference 13 FileReference 0 0 0 0 0 0 0 0 0 0 0 0 149
/**
 * Keeps a reference-counted pointer to a file on disk.  As long as the
 * FileReference is held, someone presumably has a use for this file.
 */

2178 9 istream * 0 8576 14 std::istream * 14 std::istream * 0 0 2179 0 0 0 0 0 0 0 0 0 0

2179 7 istream 0 2048 12 std::istream 12 std::istream 0 0 0 0 0 0 0 0 0 0 0 0 0

2180 20 DatagramOutputFile * 0 8576 20 DatagramOutputFile * 20 DatagramOutputFile * 0 0 2003 0 0 0 0 0 0 0 0 0 0

2181 32 DoubleBitMask< BitMaskNative > * 0 8576 32 DoubleBitMask< BitMaskNative > * 32 DoubleBitMask< BitMaskNative > * 0 0 2005 0 0 0 0 0 0 0 0 0 0

2182 38 DoubleBitMask< BitMaskNative > const * 0 8576 38 DoubleBitMask< BitMaskNative > const * 38 DoubleBitMask< BitMaskNative > const * 0 0 2183 0 0 0 0 0 0 0 0 0 0

2183 36 DoubleBitMask< BitMaskNative > const 0 8832 36 DoubleBitMask< BitMaskNative > const 36 DoubleBitMask< BitMaskNative > const 0 0 2005 0 0 0 0 0 0 0 0 0 0

2184 38 DoubleBitMask< DoubleBitMaskNative > * 0 8576 38 DoubleBitMask< DoubleBitMaskNative > * 38 DoubleBitMask< DoubleBitMaskNative > * 0 0 2007 0 0 0 0 0 0 0 0 0 0

2185 44 DoubleBitMask< DoubleBitMaskNative > const * 0 8576 44 DoubleBitMask< DoubleBitMaskNative > const * 44 DoubleBitMask< DoubleBitMaskNative > const * 0 0 2186 0 0 0 0 0 0 0 0 0 0

2186 42 DoubleBitMask< DoubleBitMaskNative > const 0 8832 42 DoubleBitMask< DoubleBitMaskNative > const 42 DoubleBitMask< DoubleBitMaskNative > const 0 0 2007 0 0 0 0 0 0 0 0 0 0

2187 15 GamepadButton * 0 8576 15 GamepadButton * 15 GamepadButton * 0 0 2010 0 0 0 0 0 0 0 0 0 0

2188 21 GamepadButton const * 0 8576 21 GamepadButton const * 21 GamepadButton const * 0 0 2189 0 0 0 0 0 0 0 0 0 0

2189 19 GamepadButton const 0 8832 19 GamepadButton const 19 GamepadButton const 0 0 2010 0 0 0 0 0 0 0 0 0 0

2190 16 KeyboardButton * 0 8576 16 KeyboardButton * 16 KeyboardButton * 0 0 2011 0 0 0 0 0 0 0 0 0 0

2191 22 KeyboardButton const * 0 8576 22 KeyboardButton const * 22 KeyboardButton const * 0 0 2192 0 0 0 0 0 0 0 0 0 0

2192 20 KeyboardButton const 0 8832 20 KeyboardButton const 20 KeyboardButton const 0 0 2011 0 0 0 0 0 0 0 0 0 0

2193 17 ModifierButtons * 0 8576 17 ModifierButtons * 17 ModifierButtons * 0 0 2012 0 0 0 0 0 0 0 0 0 0

2194 23 ModifierButtons const * 0 8576 23 ModifierButtons const * 23 ModifierButtons const * 0 0 2195 0 0 0 0 0 0 0 0 0 0

2195 21 ModifierButtons const 0 8832 21 ModifierButtons const 21 ModifierButtons const 0 0 2012 0 0 0 0 0 0 0 0 0 0

2196 13 MouseButton * 0 8576 13 MouseButton * 13 MouseButton * 0 0 2013 0 0 0 0 0 0 0 0 0 0

2197 19 MouseButton const * 0 8576 19 MouseButton const * 19 MouseButton const * 0 0 2198 0 0 0 0 0 0 0 0 0 0

2198 17 MouseButton const 0 8832 17 MouseButton const 17 MouseButton const 0 0 2013 0 0 0 0 0 0 0 0 0 0

2199 13 PointerData * 0 8576 13 PointerData * 13 PointerData * 0 0 2015 0 0 0 0 0 0 0 0 0 0

2200 19 PointerData const * 0 8576 19 PointerData const * 19 PointerData const * 0 0 2201 0 0 0 0 0 0 0 0 0 0

2201 17 PointerData const 0 8832 17 PointerData const 17 PointerData const 0 0 2015 0 0 0 0 0 0 0 0 0 0

2202 32 NodeCachedReferenceCount const * 0 8576 32 NodeCachedReferenceCount const * 32 NodeCachedReferenceCount const * 0 0 2203 0 0 0 0 0 0 0 0 0 0

2203 30 NodeCachedReferenceCount const 0 8832 30 NodeCachedReferenceCount const 30 NodeCachedReferenceCount const 0 0 2017 0 0 0 0 0 0 0 0 0 0

2204 13 SparseArray * 0 8576 13 SparseArray * 13 SparseArray * 0 0 2019 0 0 0 0 0 0 0 0 0 0

2205 22 ParamValueBase const * 0 8576 22 ParamValueBase const * 22 ParamValueBase const * 0 0 2206 0 0 0 0 0 0 0 0 0 0

2206 20 ParamValueBase const 0 8832 20 ParamValueBase const 20 ParamValueBase const 0 0 2020 0 0 0 0 0 0 0 0 0 0

2207 27 TypedReferenceCount const * 0 8576 27 TypedReferenceCount const * 27 TypedReferenceCount const * 0 0 2208 0 0 0 0 0 0 0 0 0 0

2208 25 TypedReferenceCount const 0 8832 25 TypedReferenceCount const 25 TypedReferenceCount const 0 0 1984 0 0 0 0 0 0 0 0 0 0

2209 20 ParamTypedRefCount * 0 8576 20 ParamTypedRefCount * 20 ParamTypedRefCount * 0 0 2021 0 0 0 0 0 0 0 0 0 0

2210 26 ParamTypedRefCount const * 0 8576 26 ParamTypedRefCount const * 26 ParamTypedRefCount const * 0 0 2211 0 0 0 0 0 0 0 0 0 0

2211 24 ParamTypedRefCount const 0 8832 24 ParamTypedRefCount const 24 ParamTypedRefCount const 0 0 2021 0 0 0 0 0 0 0 0 0 0

2212 22 ParamValue< string > * 0 8576 27 ParamValue< std::string > * 27 ParamValue< std::string > * 0 0 2023 0 0 0 0 0 0 0 0 0 0

2213 28 ParamValue< string > const * 0 8576 33 ParamValue< std::string > const * 33 ParamValue< std::string > const * 0 0 2214 0 0 0 0 0 0 0 0 0 0

2214 26 ParamValue< string > const 0 8832 31 ParamValue< std::string > const 31 ParamValue< std::string > const 0 0 2023 0 0 0 0 0 0 0 0 0 0

2215 23 ParamValue< wstring > * 0 8576 28 ParamValue< std::wstring > * 28 ParamValue< std::wstring > * 0 0 2026 0 0 0 0 0 0 0 0 0 0

2216 29 ParamValue< wstring > const * 0 8576 34 ParamValue< std::wstring > const * 34 ParamValue< std::wstring > const * 0 0 2217 0 0 0 0 0 0 0 0 0 0

2217 27 ParamValue< wstring > const 0 8832 32 ParamValue< std::wstring > const 32 ParamValue< std::wstring > const 0 0 2026 0 0 0 0 0 0 0 0 0 0

2218 18 LVecBase2d const * 0 8576 18 LVecBase2d const * 18 LVecBase2d const * 0 0 2219 0 0 0 0 0 0 0 0 0 0

2219 16 LVecBase2d const 0 8832 16 LVecBase2d const 16 LVecBase2d const 0 0 2220 0 0 0 0 0 0 0 0 0 0

2220 10 LVecBase2d 0 2048 10 LVecBase2d 10 LVecBase2d 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

2221 26 ParamValue< LVecBase2d > * 0 8576 26 ParamValue< LVecBase2d > * 26 ParamValue< LVecBase2d > * 0 0 2028 0 0 0 0 0 0 0 0 0 0

2222 32 ParamValue< LVecBase2d > const * 0 8576 32 ParamValue< LVecBase2d > const * 32 ParamValue< LVecBase2d > const * 0 0 2223 0 0 0 0 0 0 0 0 0 0

2223 30 ParamValue< LVecBase2d > const 0 8832 30 ParamValue< LVecBase2d > const 30 ParamValue< LVecBase2d > const 0 0 2028 0 0 0 0 0 0 0 0 0 0

2224 18 LVecBase2f const * 0 8576 18 LVecBase2f const * 18 LVecBase2f const * 0 0 2225 0 0 0 0 0 0 0 0 0 0

2225 16 LVecBase2f const 0 8832 16 LVecBase2f const 16 LVecBase2f const 0 0 2226 0 0 0 0 0 0 0 0 0 0

2226 10 LVecBase2f 0 2048 10 LVecBase2f 10 LVecBase2f 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

2227 26 ParamValue< LVecBase2f > * 0 8576 26 ParamValue< LVecBase2f > * 26 ParamValue< LVecBase2f > * 0 0 2030 0 0 0 0 0 0 0 0 0 0

2228 32 ParamValue< LVecBase2f > const * 0 8576 32 ParamValue< LVecBase2f > const * 32 ParamValue< LVecBase2f > const * 0 0 2229 0 0 0 0 0 0 0 0 0 0

2229 30 ParamValue< LVecBase2f > const 0 8832 30 ParamValue< LVecBase2f > const 30 ParamValue< LVecBase2f > const 0 0 2030 0 0 0 0 0 0 0 0 0 0

2230 18 LVecBase2i const * 0 8576 18 LVecBase2i const * 18 LVecBase2i const * 0 0 2231 0 0 0 0 0 0 0 0 0 0

2231 16 LVecBase2i const 0 8832 16 LVecBase2i const 16 LVecBase2i const 0 0 2232 0 0 0 0 0 0 0 0 0 0

2232 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

2233 26 ParamValue< LVecBase2i > * 0 8576 26 ParamValue< LVecBase2i > * 26 ParamValue< LVecBase2i > * 0 0 2032 0 0 0 0 0 0 0 0 0 0

2234 32 ParamValue< LVecBase2i > const * 0 8576 32 ParamValue< LVecBase2i > const * 32 ParamValue< LVecBase2i > const * 0 0 2235 0 0 0 0 0 0 0 0 0 0

2235 30 ParamValue< LVecBase2i > const 0 8832 30 ParamValue< LVecBase2i > const 30 ParamValue< LVecBase2i > const 0 0 2032 0 0 0 0 0 0 0 0 0 0

2236 18 LVecBase3d const * 0 8576 18 LVecBase3d const * 18 LVecBase3d const * 0 0 2237 0 0 0 0 0 0 0 0 0 0

2237 16 LVecBase3d const 0 8832 16 LVecBase3d const 16 LVecBase3d const 0 0 2238 0 0 0 0 0 0 0 0 0 0

2238 10 LVecBase3d 0 2048 10 LVecBase3d 10 LVecBase3d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2239 26 ParamValue< LVecBase3d > * 0 8576 26 ParamValue< LVecBase3d > * 26 ParamValue< LVecBase3d > * 0 0 2034 0 0 0 0 0 0 0 0 0 0

2240 32 ParamValue< LVecBase3d > const * 0 8576 32 ParamValue< LVecBase3d > const * 32 ParamValue< LVecBase3d > const * 0 0 2241 0 0 0 0 0 0 0 0 0 0

2241 30 ParamValue< LVecBase3d > const 0 8832 30 ParamValue< LVecBase3d > const 30 ParamValue< LVecBase3d > const 0 0 2034 0 0 0 0 0 0 0 0 0 0

2242 18 LVecBase3f const * 0 8576 18 LVecBase3f const * 18 LVecBase3f const * 0 0 2243 0 0 0 0 0 0 0 0 0 0

2243 16 LVecBase3f const 0 8832 16 LVecBase3f const 16 LVecBase3f const 0 0 2244 0 0 0 0 0 0 0 0 0 0

2244 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2245 26 ParamValue< LVecBase3f > * 0 8576 26 ParamValue< LVecBase3f > * 26 ParamValue< LVecBase3f > * 0 0 2036 0 0 0 0 0 0 0 0 0 0

2246 32 ParamValue< LVecBase3f > const * 0 8576 32 ParamValue< LVecBase3f > const * 32 ParamValue< LVecBase3f > const * 0 0 2247 0 0 0 0 0 0 0 0 0 0

2247 30 ParamValue< LVecBase3f > const 0 8832 30 ParamValue< LVecBase3f > const 30 ParamValue< LVecBase3f > const 0 0 2036 0 0 0 0 0 0 0 0 0 0

2248 18 LVecBase3i const * 0 8576 18 LVecBase3i const * 18 LVecBase3i const * 0 0 2249 0 0 0 0 0 0 0 0 0 0

2249 16 LVecBase3i const 0 8832 16 LVecBase3i const 16 LVecBase3i const 0 0 2250 0 0 0 0 0 0 0 0 0 0

2250 10 LVecBase3i 0 2048 10 LVecBase3i 10 LVecBase3i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2251 26 ParamValue< LVecBase3i > * 0 8576 26 ParamValue< LVecBase3i > * 26 ParamValue< LVecBase3i > * 0 0 2038 0 0 0 0 0 0 0 0 0 0

2252 32 ParamValue< LVecBase3i > const * 0 8576 32 ParamValue< LVecBase3i > const * 32 ParamValue< LVecBase3i > const * 0 0 2253 0 0 0 0 0 0 0 0 0 0

2253 30 ParamValue< LVecBase3i > const 0 8832 30 ParamValue< LVecBase3i > const 30 ParamValue< LVecBase3i > const 0 0 2038 0 0 0 0 0 0 0 0 0 0

2254 18 LVecBase4d const * 0 8576 18 LVecBase4d const * 18 LVecBase4d const * 0 0 2255 0 0 0 0 0 0 0 0 0 0

2255 16 LVecBase4d const 0 8832 16 LVecBase4d const 16 LVecBase4d const 0 0 2256 0 0 0 0 0 0 0 0 0 0

2256 10 LVecBase4d 0 2048 10 LVecBase4d 10 LVecBase4d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2257 26 ParamValue< LVecBase4d > * 0 8576 26 ParamValue< LVecBase4d > * 26 ParamValue< LVecBase4d > * 0 0 2040 0 0 0 0 0 0 0 0 0 0

2258 32 ParamValue< LVecBase4d > const * 0 8576 32 ParamValue< LVecBase4d > const * 32 ParamValue< LVecBase4d > const * 0 0 2259 0 0 0 0 0 0 0 0 0 0

2259 30 ParamValue< LVecBase4d > const 0 8832 30 ParamValue< LVecBase4d > const 30 ParamValue< LVecBase4d > const 0 0 2040 0 0 0 0 0 0 0 0 0 0

2260 18 LVecBase4f const * 0 8576 18 LVecBase4f const * 18 LVecBase4f const * 0 0 2261 0 0 0 0 0 0 0 0 0 0

2261 16 LVecBase4f const 0 8832 16 LVecBase4f const 16 LVecBase4f const 0 0 2262 0 0 0 0 0 0 0 0 0 0

2262 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2263 26 ParamValue< LVecBase4f > * 0 8576 26 ParamValue< LVecBase4f > * 26 ParamValue< LVecBase4f > * 0 0 2042 0 0 0 0 0 0 0 0 0 0

2264 32 ParamValue< LVecBase4f > const * 0 8576 32 ParamValue< LVecBase4f > const * 32 ParamValue< LVecBase4f > const * 0 0 2265 0 0 0 0 0 0 0 0 0 0

2265 30 ParamValue< LVecBase4f > const 0 8832 30 ParamValue< LVecBase4f > const 30 ParamValue< LVecBase4f > const 0 0 2042 0 0 0 0 0 0 0 0 0 0

2266 18 LVecBase4i const * 0 8576 18 LVecBase4i const * 18 LVecBase4i const * 0 0 2267 0 0 0 0 0 0 0 0 0 0

2267 16 LVecBase4i const 0 8832 16 LVecBase4i const 16 LVecBase4i const 0 0 2268 0 0 0 0 0 0 0 0 0 0

2268 10 LVecBase4i 0 2048 10 LVecBase4i 10 LVecBase4i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

2269 26 ParamValue< LVecBase4i > * 0 8576 26 ParamValue< LVecBase4i > * 26 ParamValue< LVecBase4i > * 0 0 2044 0 0 0 0 0 0 0 0 0 0

2270 32 ParamValue< LVecBase4i > const * 0 8576 32 ParamValue< LVecBase4i > const * 32 ParamValue< LVecBase4i > const * 0 0 2271 0 0 0 0 0 0 0 0 0 0

2271 30 ParamValue< LVecBase4i > const 0 8832 30 ParamValue< LVecBase4i > const 30 ParamValue< LVecBase4i > const 0 0 2044 0 0 0 0 0 0 0 0 0 0

2272 17 LMatrix3d const * 0 8576 17 LMatrix3d const * 17 LMatrix3d const * 0 0 2273 0 0 0 0 0 0 0 0 0 0

2273 15 LMatrix3d const 0 8832 15 LMatrix3d const 15 LMatrix3d const 0 0 2274 0 0 0 0 0 0 0 0 0 0

2274 9 LMatrix3d 0 2048 9 LMatrix3d 9 LMatrix3d 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * This is a 3-by-3 transform matrix.  It typically will represent either a
 * rotation-and-scale (no translation) matrix in 3-d, or a full affine matrix
 * (rotation, scale, translation) in 2-d, e.g.  for a texture matrix.
 */

2275 25 ParamValue< LMatrix3d > * 0 8576 25 ParamValue< LMatrix3d > * 25 ParamValue< LMatrix3d > * 0 0 2046 0 0 0 0 0 0 0 0 0 0

2276 31 ParamValue< LMatrix3d > const * 0 8576 31 ParamValue< LMatrix3d > const * 31 ParamValue< LMatrix3d > const * 0 0 2277 0 0 0 0 0 0 0 0 0 0

2277 29 ParamValue< LMatrix3d > const 0 8832 29 ParamValue< LMatrix3d > const 29 ParamValue< LMatrix3d > const 0 0 2046 0 0 0 0 0 0 0 0 0 0

2278 17 LMatrix3f const * 0 8576 17 LMatrix3f const * 17 LMatrix3f const * 0 0 2279 0 0 0 0 0 0 0 0 0 0

2279 15 LMatrix3f const 0 8832 15 LMatrix3f const 15 LMatrix3f const 0 0 2280 0 0 0 0 0 0 0 0 0 0

2280 9 LMatrix3f 0 2048 9 LMatrix3f 9 LMatrix3f 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * This is a 3-by-3 transform matrix.  It typically will represent either a
 * rotation-and-scale (no translation) matrix in 3-d, or a full affine matrix
 * (rotation, scale, translation) in 2-d, e.g.  for a texture matrix.
 */

2281 25 ParamValue< LMatrix3f > * 0 8576 25 ParamValue< LMatrix3f > * 25 ParamValue< LMatrix3f > * 0 0 2048 0 0 0 0 0 0 0 0 0 0

2282 31 ParamValue< LMatrix3f > const * 0 8576 31 ParamValue< LMatrix3f > const * 31 ParamValue< LMatrix3f > const * 0 0 2283 0 0 0 0 0 0 0 0 0 0

2283 29 ParamValue< LMatrix3f > const 0 8832 29 ParamValue< LMatrix3f > const 29 ParamValue< LMatrix3f > const 0 0 2048 0 0 0 0 0 0 0 0 0 0

2284 17 LMatrix4d const * 0 8576 17 LMatrix4d const * 17 LMatrix4d const * 0 0 2285 0 0 0 0 0 0 0 0 0 0

2285 15 LMatrix4d const 0 8832 15 LMatrix4d const 15 LMatrix4d const 0 0 2286 0 0 0 0 0 0 0 0 0 0

2286 9 LMatrix4d 0 2048 9 LMatrix4d 9 LMatrix4d 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

2287 25 ParamValue< LMatrix4d > * 0 8576 25 ParamValue< LMatrix4d > * 25 ParamValue< LMatrix4d > * 0 0 2050 0 0 0 0 0 0 0 0 0 0

2288 31 ParamValue< LMatrix4d > const * 0 8576 31 ParamValue< LMatrix4d > const * 31 ParamValue< LMatrix4d > const * 0 0 2289 0 0 0 0 0 0 0 0 0 0

2289 29 ParamValue< LMatrix4d > const 0 8832 29 ParamValue< LMatrix4d > const 29 ParamValue< LMatrix4d > const 0 0 2050 0 0 0 0 0 0 0 0 0 0

2290 17 LMatrix4f const * 0 8576 17 LMatrix4f const * 17 LMatrix4f const * 0 0 2291 0 0 0 0 0 0 0 0 0 0

2291 15 LMatrix4f const 0 8832 15 LMatrix4f const 15 LMatrix4f const 0 0 2292 0 0 0 0 0 0 0 0 0 0

2292 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

2293 25 ParamValue< LMatrix4f > * 0 8576 25 ParamValue< LMatrix4f > * 25 ParamValue< LMatrix4f > * 0 0 2052 0 0 0 0 0 0 0 0 0 0

2294 31 ParamValue< LMatrix4f > const * 0 8576 31 ParamValue< LMatrix4f > const * 31 ParamValue< LMatrix4f > const * 0 0 2295 0 0 0 0 0 0 0 0 0 0

2295 29 ParamValue< LMatrix4f > const 0 8832 29 ParamValue< LMatrix4f > const 29 ParamValue< LMatrix4f > const 0 0 2052 0 0 0 0 0 0 0 0 0 0

2296 25 UniqueIdAllocator const * 0 8576 25 UniqueIdAllocator const * 25 UniqueIdAllocator const * 0 0 2297 0 0 0 0 0 0 0 0 0 0

2297 23 UniqueIdAllocator const 0 8832 23 UniqueIdAllocator const 23 UniqueIdAllocator const 0 0 2060 0 0 0 0 0 0 0 0 0 0

2298 19 UniqueIdAllocator * 0 8576 19 UniqueIdAllocator * 19 UniqueIdAllocator * 0 0 2060 0 0 0 0 0 0 0 0 0 0

2299 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 2300 0 0 0 0 0 0 0 0 0 0

2300 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

2301 26 ConfigVariableSearchPath * 0 8576 26 ConfigVariableSearchPath * 26 ConfigVariableSearchPath * 0 0 2302 0 0 0 0 0 0 0 0 0 0

2302 24 ConfigVariableSearchPath 0 2048 24 ConfigVariableSearchPath 24 ConfigVariableSearchPath 0 0 0 0 0 0 0 0 0 0 0 0 731
/**
 * This is similar to a ConfigVariableList, but it returns its list as a
 * DSearchPath, as a list of directories.
 *
 * You may locally append directories to the end of the search path with the
 * methods here, or prepend them to the beginning.  Use these methods to make
 * adjustments to the path; do not attempt to directly modify the const
 * DSearchPath object returned by get_value().
 *
 * Unlike other ConfigVariable types, local changes (made by calling
 * append_directory() and prepend_directory()) are specific to this particular
 * instance of the ConfigVariableSearchPath.  A separate instance of the same
 * variable, created by using the same name to the constructor, will not
 * reflect the local changes.
 */

2303 12 ConfigPage * 0 8576 12 ConfigPage * 12 ConfigPage * 0 0 2304 0 0 0 0 0 0 0 0 0 0

2304 10 ConfigPage 0 2048 10 ConfigPage 10 ConfigPage 0 0 0 0 0 0 0 0 0 0 0 0 262
/**
 * A page of ConfigDeclarations that may be loaded or unloaded.  Typically
 * this represents a single .prc file that is read from disk at runtime, but
 * it may also represent a list of declarations built up by application code
 * and explicitly loaded.
 */

2305 9 HashVal * 0 8576 9 HashVal * 9 HashVal * 0 0 2306 0 0 0 0 0 0 0 0 0 0

2306 7 HashVal 0 2048 7 HashVal 7 HashVal 0 0 0 0 0 0 0 0 0 0 0 0 0

0
84
2307 9 play_rate 0 6 1933 1019 1018 0 0 0 0 0 0 24 AnimInterface::play_rate 0

2308 10 frame_rate 0 2 1933 1020 0 0 0 0 0 0 0 25 AnimInterface::frame_rate 0

2309 10 num_frames 0 2 1934 1021 0 0 0 0 0 0 0 25 AnimInterface::num_frames 0

2310 5 frame 0 2 1934 1022 0 0 0 0 0 0 0 20 AnimInterface::frame 0

2311 10 next_frame 0 2 1934 1023 0 0 0 0 0 0 0 25 AnimInterface::next_frame 0

2312 4 frac 0 2 1933 1024 0 0 0 0 0 0 0 19 AnimInterface::frac 0

2313 10 full_frame 0 2 1934 1025 0 0 0 0 0 0 0 25 AnimInterface::full_frame 0

2314 11 full_fframe 0 2 1933 1026 0 0 0 0 0 0 0 26 AnimInterface::full_fframe 0

2315 7 playing 0 2 1935 1027 0 0 0 0 0 0 0 22 AnimInterface::playing 0

2316 3 seq 0 2 1938 1048 0 0 0 0 0 0 0 14 UpdateSeq::seq 0

2317 15 source_pathname 0 2 1947 1073 0 0 0 0 0 0 0 31 BamCacheRecord::source_pathname 0

2318 14 cache_filename 0 2 1947 1074 0 0 0 0 0 0 0 30 BamCacheRecord::cache_filename 0

2319 16 source_timestamp 0 2 1949 1075 0 0 0 0 0 0 0 32 BamCacheRecord::source_timestamp 0

2320 13 recorded_time 0 2 1949 1076 0 0 0 0 0 0 0 29 BamCacheRecord::recorded_time 0

2321 4 data 0 30 1950 1084 1086 1082 1083 0 0 0 0 20 BamCacheRecord::data 0

2322 6 active 0 6 1935 1093 1092 0 0 0 0 0 0 16 BamCache::active 0

2323 12 cache_models 0 6 1935 1095 1094 0 0 0 0 0 0 22 BamCache::cache_models 0

2324 14 cache_textures 0 6 1935 1097 1096 0 0 0 0 0 0 24 BamCache::cache_textures 0

2325 25 cache_compressed_textures 0 6 1935 1099 1098 0 0 0 0 0 0 35 BamCache::cache_compressed_textures 0

2326 22 cache_compiled_shaders 0 6 1935 1101 1100 0 0 0 0 0 0 32 BamCache::cache_compiled_shaders 0

2327 4 root 0 6 1948 1103 1102 0 0 0 0 0 0 14 BamCache::root 0

2328 10 flush_time 0 6 1934 1105 1104 0 0 0 0 0 0 20 BamCache::flush_time 0

2329 16 cache_max_kbytes 0 6 1934 1107 1106 0 0 0 0 0 0 26 BamCache::cache_max_kbytes 0

2330 9 read_only 0 6 1935 1109 1108 0 0 0 0 0 0 19 BamCache::read_only 0

2331 5 flags 0 6 1934 1122 1121 0 0 0 0 0 0 20 LoaderOptions::flags 0

2332 13 texture_flags 0 6 1934 1124 1123 0 0 0 0 0 0 28 LoaderOptions::texture_flags 0

2333 17 texture_num_views 0 6 1934 1126 1125 0 0 0 0 0 0 32 LoaderOptions::texture_num_views 0

2334 18 auto_texture_scale 0 6 1936 1128 1127 0 0 0 0 0 0 33 LoaderOptions::auto_texture_scale 0

2335 6 source 0 6 1960 1134 1133 0 0 0 0 0 0 17 BamReader::source 0

2336 8 filename 0 2 1947 1138 0 0 0 0 0 0 0 19 BamReader::filename 0

2337 14 loader_options 0 6 1962 1139 1140 0 0 0 0 0 0 25 BamReader::loader_options 0

2338 12 file_version 0 2 1963 1151 0 0 0 0 0 0 0 23 BamReader::file_version 0

2339 11 file_endian 0 2 1953 1147 0 0 0 0 0 0 0 22 BamReader::file_endian 0

2340 20 file_stdfloat_double 0 2 1935 1148 0 0 0 0 0 0 0 31 BamReader::file_stdfloat_double 0

2341 6 target 0 6 1967 1156 1155 0 0 0 0 0 0 17 BamWriter::target 0

2342 8 filename 0 2 1947 1158 0 0 0 0 0 0 0 19 BamWriter::filename 0

2343 12 file_version 0 2 1963 1171 0 0 0 0 0 0 0 23 BamWriter::file_version 0

2344 11 file_endian 0 2 1953 1165 0 0 0 0 0 0 0 22 BamWriter::file_endian 0

2345 20 file_stdfloat_double 0 2 1935 1166 0 0 0 0 0 0 0 31 BamWriter::file_stdfloat_double 0

2346 17 file_texture_mode 0 6 1955 1167 1168 0 0 0 0 0 0 28 BamWriter::file_texture_mode 0

2347 9 root_node 0 6 1950 1169 1170 0 0 0 0 0 0 20 BamWriter::root_node 0

2348 5 index 0 2 1934 1438 0 0 0 0 0 0 0 19 ButtonHandle::index 0

2349 4 name 0 2 1979 1433 0 0 0 0 0 0 0 18 ButtonHandle::name 0

2350 16 ascii_equivalent 0 10 1981 1435 0 1434 0 0 0 0 0 30 ButtonHandle::ascii_equivalent 0

2351 5 alias 0 2 1978 1436 0 0 0 0 0 0 0 19 ButtonHandle::alias 0

2352 15 cache_ref_count 0 2 1934 1465 0 0 0 0 0 0 0 50 CachedTypedWritableReferenceCount::cache_ref_count 0

2353 8 function 0 6 1963 1479 1478 0 0 0 0 0 0 30 PythonCallbackObject::function 0

2354 2 tv 0 6 1990 1484 1485 0 0 0 0 0 0 11 TimeVal::tv 0

2355 4 mode 0 6 1994 1489 1488 0 0 0 0 0 0 17 ClockObject::mode 0

2356 10 frame_time 0 6 1933 1490 1495 0 0 0 0 0 0 23 ClockObject::frame_time 0

2357 9 real_time 0 6 1933 1491 1494 0 0 0 0 0 0 22 ClockObject::real_time 0

2358 9 long_time 0 2 1933 1492 0 0 0 0 0 0 0 22 ClockObject::long_time 0

2359 11 frame_count 0 6 1934 1497 1496 0 0 0 0 0 0 24 ClockObject::frame_count 0

2360 2 dt 0 6 1933 1499 1500 0 0 0 0 0 0 15 ClockObject::dt 0

2361 6 max_dt 0 6 1933 1502 1503 0 0 0 0 0 0 19 ClockObject::max_dt 0

2362 14 degrade_factor 0 6 1933 1504 1505 0 0 0 0 0 0 27 ClockObject::degrade_factor 0

2363 27 average_frame_rate_interval 0 6 1933 1507 1506 0 0 0 0 0 0 40 ClockObject::average_frame_rate_interval 0

2364 18 average_frame_rate 0 2 1933 1508 0 0 0 0 0 0 0 31 ClockObject::average_frame_rate 0

2365 18 max_frame_duration 0 2 1933 1509 0 0 0 0 0 0 0 31 ClockObject::max_frame_duration 0

2366 4 data 0 6 1999 1530 1531 0 0 0 0 0 0 20 DatagramBuffer::data 0

2367 6 stream 0 2 2004 1542 0 0 0 0 0 0 0 26 DatagramOutputFile::stream 0

2368 7 buttons 0 66 1978 1758 0 0 0 0 1757 0 0 24 ModifierButtons::buttons 0

2369 1 x 0 2 1933 1780 0 0 0 0 0 0 0 14 PointerData::x 0

2370 1 y 0 2 1933 1781 0 0 0 0 0 0 0 14 PointerData::y 0

2371 4 type 0 2 2014 1783 0 0 0 0 0 0 0 17 PointerData::type 0

2372 2 id 0 2 1934 1784 0 0 0 0 0 0 0 15 PointerData::id 0

2373 9 in_window 0 2 1935 1782 0 0 0 0 0 0 0 22 PointerData::in_window 0

2374 8 pressure 0 2 1933 1785 0 0 0 0 0 0 0 21 PointerData::pressure 0

2375 5 value 0 2 2022 1853 0 0 0 0 0 0 0 25 ParamTypedRefCount::value 0

2376 5 value 0 6 2024 1857 1856 0 0 0 0 0 0 27 ParamValue< string >::value 0

2377 5 value 0 6 2024 1861 1860 0 0 0 0 0 0 28 ParamValue< wstring >::value 0

2378 5 value 0 6 2024 1865 1864 0 0 0 0 0 0 31 ParamValue< LVecBase2d >::value 0

2379 5 value 0 6 2024 1869 1868 0 0 0 0 0 0 31 ParamValue< LVecBase2f >::value 0

2380 5 value 0 6 2024 1873 1872 0 0 0 0 0 0 31 ParamValue< LVecBase2i >::value 0

2381 5 value 0 6 2024 1877 1876 0 0 0 0 0 0 31 ParamValue< LVecBase3d >::value 0

2382 5 value 0 6 2024 1881 1880 0 0 0 0 0 0 31 ParamValue< LVecBase3f >::value 0

2383 5 value 0 6 2024 1885 1884 0 0 0 0 0 0 31 ParamValue< LVecBase3i >::value 0

2384 5 value 0 6 2024 1889 1888 0 0 0 0 0 0 31 ParamValue< LVecBase4d >::value 0

2385 5 value 0 6 2024 1893 1892 0 0 0 0 0 0 31 ParamValue< LVecBase4f >::value 0

2386 5 value 0 6 2024 1897 1896 0 0 0 0 0 0 31 ParamValue< LVecBase4i >::value 0

2387 5 value 0 6 2024 1901 1900 0 0 0 0 0 0 30 ParamValue< LMatrix3d >::value 0

2388 5 value 0 6 2024 1905 1904 0 0 0 0 0 0 30 ParamValue< LMatrix3f >::value 0

2389 5 value 0 6 2024 1909 1908 0 0 0 0 0 0 30 ParamValue< LMatrix4d >::value 0

2390 5 value 0 6 2024 1913 1912 0 0 0 0 0 0 30 ParamValue< LMatrix4f >::value 0

1
2391 11 get_buttons 0 1757 1758 28 ModifierButtons::get_buttons 0

