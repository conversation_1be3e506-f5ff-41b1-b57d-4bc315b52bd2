'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_SGIX_pbuffer'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_SGIX_pbuffer',error_checker=_errors._error_checker)
GLX_ACCUM_BUFFER_BIT_SGIX=_C('GLX_ACCUM_BUFFER_BIT_SGIX',0x00000080)
GLX_AUX_BUFFERS_BIT_SGIX=_C('GLX_AUX_BUFFERS_BIT_SGIX',0x00000010)
GLX_BACK_LEFT_BUFFER_BIT_SGIX=_C('GLX_BACK_LEFT_BUFFER_BIT_SGIX',0x00000004)
GLX_BACK_RIGHT_BUFFER_BIT_SGIX=_C('GLX_BACK_RIGHT_BUFFER_BIT_SGIX',0x00000008)
GLX_BUFFER_CLOBBER_MASK_SGIX=_C('GLX_BUFFER_CLOBBER_MASK_SGIX',0x08000000)
GLX_DAMAGED_SGIX=_C('GLX_DAMAGED_SGIX',0x8020)
GLX_DEPTH_BUFFER_BIT_SGIX=_C('GLX_DEPTH_BUFFER_BIT_SGIX',0x00000020)
GLX_EVENT_MASK_SGIX=_C('GLX_EVENT_MASK_SGIX',0x801F)
GLX_FRONT_LEFT_BUFFER_BIT_SGIX=_C('GLX_FRONT_LEFT_BUFFER_BIT_SGIX',0x00000001)
GLX_FRONT_RIGHT_BUFFER_BIT_SGIX=_C('GLX_FRONT_RIGHT_BUFFER_BIT_SGIX',0x00000002)
GLX_HEIGHT_SGIX=_C('GLX_HEIGHT_SGIX',0x801E)
GLX_LARGEST_PBUFFER_SGIX=_C('GLX_LARGEST_PBUFFER_SGIX',0x801C)
GLX_MAX_PBUFFER_HEIGHT_SGIX=_C('GLX_MAX_PBUFFER_HEIGHT_SGIX',0x8017)
GLX_MAX_PBUFFER_PIXELS_SGIX=_C('GLX_MAX_PBUFFER_PIXELS_SGIX',0x8018)
GLX_MAX_PBUFFER_WIDTH_SGIX=_C('GLX_MAX_PBUFFER_WIDTH_SGIX',0x8016)
GLX_OPTIMAL_PBUFFER_HEIGHT_SGIX=_C('GLX_OPTIMAL_PBUFFER_HEIGHT_SGIX',0x801A)
GLX_OPTIMAL_PBUFFER_WIDTH_SGIX=_C('GLX_OPTIMAL_PBUFFER_WIDTH_SGIX',0x8019)
GLX_PBUFFER_BIT_SGIX=_C('GLX_PBUFFER_BIT_SGIX',0x00000004)
GLX_PBUFFER_SGIX=_C('GLX_PBUFFER_SGIX',0x8023)
GLX_PRESERVED_CONTENTS_SGIX=_C('GLX_PRESERVED_CONTENTS_SGIX',0x801B)
GLX_SAMPLE_BUFFERS_BIT_SGIX=_C('GLX_SAMPLE_BUFFERS_BIT_SGIX',0x00000100)
GLX_SAVED_SGIX=_C('GLX_SAVED_SGIX',0x8021)
GLX_STENCIL_BUFFER_BIT_SGIX=_C('GLX_STENCIL_BUFFER_BIT_SGIX',0x00000040)
GLX_WIDTH_SGIX=_C('GLX_WIDTH_SGIX',0x801D)
GLX_WINDOW_SGIX=_C('GLX_WINDOW_SGIX',0x8022)
@_f
@_p.types(_cs.GLXPbufferSGIX,ctypes.POINTER(_cs.Display),_cs.GLXFBConfigSGIX,_cs.c_uint,_cs.c_uint,ctypes.POINTER(_cs.c_int))
def glXCreateGLXPbufferSGIX(dpy,config,width,height,attrib_list):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXPbufferSGIX)
def glXDestroyGLXPbufferSGIX(dpy,pbuf):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,ctypes.POINTER(_cs.c_ulong))
def glXGetSelectedEventSGIX(dpy,drawable,mask):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXPbufferSGIX,_cs.c_int,ctypes.POINTER(_cs.c_uint))
def glXQueryGLXPbufferSGIX(dpy,pbuf,attribute,value):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.c_ulong)
def glXSelectEventSGIX(dpy,drawable,mask):pass
