// qbluetoothtransferreply.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QBluetoothTransferReply : public QObject
{
%TypeHeaderCode
#include <qbluetoothtransferreply.h>
%End

public:
    enum TransferError
    {
        NoE<PERSON>r,
        <PERSON><PERSON><PERSON><PERSON>,
        FileNotFoundError,
        HostNotFoundError,
        UserCanceledTransferError,
%If (Qt_5_3_0 -)
        IODeviceNotReadableError,
%End
%If (Qt_5_3_0 -)
        ResourceBusyError,
%End
%If (Qt_5_4_0 -)
        SessionError,
%End
    };

    virtual ~QBluetoothTransferReply();
    virtual bool isFinished() const = 0;
    virtual bool isRunning() const = 0;
    QBluetoothTransferManager *manager() const;
    virtual QBluetoothTransferReply::TransferError error() const = 0;
    virtual QString errorString() const = 0;
    QBluetoothTransferRequest request() const;

public slots:
    void abort();

signals:
    void finished(QBluetoothTransferReply *);
    void transferProgress(qint64 bytesTransferred, qint64 bytesTotal);
%If (Qt_5_4_0 -)
    void error(QBluetoothTransferReply::TransferError lastError);
%End

protected:
    explicit QBluetoothTransferReply(QObject *parent /TransferThis/ = 0);
    void setManager(QBluetoothTransferManager *manager);
    void setRequest(const QBluetoothTransferRequest &request);
};

%End
