'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_INTEL_swap_event'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_INTEL_swap_event',error_checker=_errors._error_checker)
GLX_BUFFER_SWAP_COMPLETE_INTEL_MASK=_C('GLX_BUFFER_SWAP_COMPLETE_INTEL_MASK',0x04000000)
GLX_COPY_COMPLETE_INTEL=_C('GLX_COPY_COMPLETE_INTEL',0x8181)
GLX_EXCHANGE_COMPLETE_INTEL=_C('GLX_EXCHANGE_COMPLETE_INTEL',0x8180)
GLX_FLIP_COMPLETE_INTEL=_C('GLX_FLIP_COMPLETE_INTEL',0x8182)

