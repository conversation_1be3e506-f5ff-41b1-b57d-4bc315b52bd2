'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_NV_multigpu_context'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_NV_multigpu_context',error_checker=_errors._error_checker)
WGL_CONTEXT_MULTIGPU_ATTRIB_AFR_NV=_C('WGL_CONTEXT_MULTIGPU_ATTRIB_AFR_NV',0x20AC)
WGL_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV=_C('WGL_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV',0x20AD)
WGL_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV=_C('WGL_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV',0x20AE)
WGL_CONTEXT_MULTIGPU_ATTRIB_NV=_C('WGL_CONTEXT_MULTIGPU_ATTRIB_NV',0x20AA)
WGL_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV=_C('WGL_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV',0x20AB)

