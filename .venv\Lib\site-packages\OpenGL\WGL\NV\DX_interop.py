'''OpenGL extension NV.DX_interop

This module customises the behaviour of the 
OpenGL.raw.WGL.NV.DX_interop to provide a more 
Python-friendly API

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/NV/DX_interop.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.NV.DX_interop import *
from OpenGL.raw.WGL.NV.DX_interop import _EXTENSION_NAME

def glInitDxInteropNV():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION