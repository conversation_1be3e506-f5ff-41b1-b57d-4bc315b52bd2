1730992849
3 3
19 libp3particlesystem 4 BxOc 15 panda3d.physics 
325
354 9 make_copy 0 6 679 30 BaseParticleEmitter::make_copy 0 1 1 0
70
virtual BaseParticleEmitter *BaseParticleEmitter::make_copy(void) = 0;

355 8 generate 0 4 679 29 BaseParticleEmitter::generate 0 1 2 37
/**
 * parent generation function
 */
64
void BaseParticleEmitter::generate(LPoint3 &pos, LVector3 &vel);

356 17 set_emission_type 0 4 679 38 BaseParticleEmitter::set_emission_type 0 1 3 35
/**
 * emission type assignment
 */
89
inline void BaseParticleEmitter::set_emission_type(BaseParticleEmitter::emissionType et);

357 13 set_amplitude 0 4 679 34 BaseParticleEmitter::set_amplitude 0 1 4 31
/**
 * amplitude assignment
 */
62
inline void BaseParticleEmitter::set_amplitude(PN_stdfloat a);

358 20 set_amplitude_spread 0 4 679 41 BaseParticleEmitter::set_amplitude_spread 0 1 5 38
/**
 * amplitude spread assignment
 */
70
inline void BaseParticleEmitter::set_amplitude_spread(PN_stdfloat as);

359 16 set_offset_force 0 4 679 37 BaseParticleEmitter::set_offset_force 0 1 6 83
// this is a constant force applied to all particles

/**
 * user-defined force
 */
70
inline void BaseParticleEmitter::set_offset_force(LVector3 const &of);

360 26 set_explicit_launch_vector 0 4 679 47 BaseParticleEmitter::set_explicit_launch_vector 0 1 7 110
// this is a constant force applied to all particles

/**
 * assignment of explicit emission launch vector
 */
81
inline void BaseParticleEmitter::set_explicit_launch_vector(LVector3 const &elv);

361 18 set_radiate_origin 0 4 679 39 BaseParticleEmitter::set_radiate_origin 0 1 8 54
/**
 * assignment of radiate emission origin point
 */
71
inline void BaseParticleEmitter::set_radiate_origin(LPoint3 const &ro);

362 17 get_emission_type 0 4 679 38 BaseParticleEmitter::get_emission_type 0 1 9 30
/**
 * emission type query
 */
92
inline BaseParticleEmitter::emissionType BaseParticleEmitter::get_emission_type(void) const;

363 13 get_amplitude 0 4 679 34 BaseParticleEmitter::get_amplitude 0 1 10 26
/**
 * amplitude query
 */
66
inline PN_stdfloat BaseParticleEmitter::get_amplitude(void) const;

364 20 get_amplitude_spread 0 4 679 41 BaseParticleEmitter::get_amplitude_spread 0 1 11 33
/**
 * amplitude spread query
 */
73
inline PN_stdfloat BaseParticleEmitter::get_amplitude_spread(void) const;

365 16 get_offset_force 0 4 679 37 BaseParticleEmitter::get_offset_force 0 1 12 29
/**
 * user-defined force
 */
66
inline LVector3 BaseParticleEmitter::get_offset_force(void) const;

366 26 get_explicit_launch_vector 0 4 679 47 BaseParticleEmitter::get_explicit_launch_vector 0 1 13 52
/**
 * query for explicit emission launch vector
 */
76
inline LVector3 BaseParticleEmitter::get_explicit_launch_vector(void) const;

367 18 get_radiate_origin 0 4 679 39 BaseParticleEmitter::get_radiate_origin 0 1 14 52
/**
 * query for explicit emission launch vector
 */
67
inline LPoint3 BaseParticleEmitter::get_radiate_origin(void) const;

368 6 output 0 6 679 27 BaseParticleEmitter::output 0 1 15 67
/**
 * Write a string representation of this instance to <out>.
 */
66
virtual void BaseParticleEmitter::output(std::ostream &out) const;

369 5 write 0 6 679 26 BaseParticleEmitter::write 0 1 16 67
/**
 * Write a string representation of this instance to <out>.
 */
81
virtual void BaseParticleEmitter::write(std::ostream &out, int indent = 0) const;

370 11 RingEmitter 0 260 682 24 RingEmitter::RingEmitter 0 2 17 18 51
/**
 * constructor
 */

/**
 * copy constructor
 */
82
RingEmitter::RingEmitter(void);
RingEmitter::RingEmitter(RingEmitter const &copy);

371 10 set_radius 0 4 682 23 RingEmitter::set_radius 0 1 19 0
51
inline void RingEmitter::set_radius(PN_stdfloat r);

372 9 set_angle 0 4 682 22 RingEmitter::set_angle 0 1 20 0
54
inline void RingEmitter::set_angle(PN_stdfloat angle);

373 17 set_radius_spread 0 4 682 30 RingEmitter::set_radius_spread 0 1 21 0
63
inline void RingEmitter::set_radius_spread(PN_stdfloat spread);

374 20 set_uniform_emission 0 4 682 33 RingEmitter::set_uniform_emission 0 1 22 0
68
inline void RingEmitter::set_uniform_emission(int uniform_emission);

375 10 get_radius 0 4 682 23 RingEmitter::get_radius 0 1 23 0
55
inline PN_stdfloat RingEmitter::get_radius(void) const;

376 9 get_angle 0 4 682 22 RingEmitter::get_angle 0 1 24 0
54
inline PN_stdfloat RingEmitter::get_angle(void) const;

377 17 get_radius_spread 0 4 682 30 RingEmitter::get_radius_spread 0 1 25 0
62
inline PN_stdfloat RingEmitter::get_radius_spread(void) const;

378 20 get_uniform_emission 0 4 682 33 RingEmitter::get_uniform_emission 0 1 26 0
57
inline int RingEmitter::get_uniform_emission(void) const;

379 10 ArcEmitter 0 260 683 22 ArcEmitter::ArcEmitter 0 2 27 28 51
/**
 * constructor
 */

/**
 * copy constructor
 */
77
ArcEmitter::ArcEmitter(void);
ArcEmitter::ArcEmitter(ArcEmitter const &copy);

380 15 set_start_angle 0 4 683 27 ArcEmitter::set_start_angle 0 1 29 0
59
inline void ArcEmitter::set_start_angle(PN_stdfloat angle);

381 13 set_end_angle 0 4 683 25 ArcEmitter::set_end_angle 0 1 30 0
57
inline void ArcEmitter::set_end_angle(PN_stdfloat angle);

382 7 set_arc 0 4 683 19 ArcEmitter::set_arc 0 1 31 0
78
inline void ArcEmitter::set_arc(PN_stdfloat startAngle, PN_stdfloat endAngle);

383 15 get_start_angle 0 4 683 27 ArcEmitter::get_start_angle 0 1 32 0
53
inline PN_stdfloat ArcEmitter::get_start_angle(void);

384 13 get_end_angle 0 4 683 25 ArcEmitter::get_end_angle 0 1 33 0
51
inline PN_stdfloat ArcEmitter::get_end_angle(void);

385 17 set_lifespan_base 0 4 684 38 BaseParticleFactory::set_lifespan_base 0 1 34 17
/**
 * public
 */
67
inline void BaseParticleFactory::set_lifespan_base(PN_stdfloat lb);

386 19 set_lifespan_spread 0 4 684 40 BaseParticleFactory::set_lifespan_spread 0 1 35 17
/**
 * public
 */
69
inline void BaseParticleFactory::set_lifespan_spread(PN_stdfloat ls);

387 13 set_mass_base 0 4 684 34 BaseParticleFactory::set_mass_base 0 1 36 17
/**
 * public
 */
63
inline void BaseParticleFactory::set_mass_base(PN_stdfloat mb);

388 15 set_mass_spread 0 4 684 36 BaseParticleFactory::set_mass_spread 0 1 37 17
/**
 * public
 */
65
inline void BaseParticleFactory::set_mass_spread(PN_stdfloat ms);

389 26 set_terminal_velocity_base 0 4 684 47 BaseParticleFactory::set_terminal_velocity_base 0 1 38 17
/**
 * public
 */
77
inline void BaseParticleFactory::set_terminal_velocity_base(PN_stdfloat tvb);

390 28 set_terminal_velocity_spread 0 4 684 49 BaseParticleFactory::set_terminal_velocity_spread 0 1 39 17
/**
 * public
 */
79
inline void BaseParticleFactory::set_terminal_velocity_spread(PN_stdfloat tvs);

391 17 get_lifespan_base 0 4 684 38 BaseParticleFactory::get_lifespan_base 0 1 40 17
/**
 * public
 */
70
inline PN_stdfloat BaseParticleFactory::get_lifespan_base(void) const;

392 19 get_lifespan_spread 0 4 684 40 BaseParticleFactory::get_lifespan_spread 0 1 41 17
/**
 * public
 */
72
inline PN_stdfloat BaseParticleFactory::get_lifespan_spread(void) const;

393 13 get_mass_base 0 4 684 34 BaseParticleFactory::get_mass_base 0 1 42 17
/**
 * public
 */
66
inline PN_stdfloat BaseParticleFactory::get_mass_base(void) const;

394 15 get_mass_spread 0 4 684 36 BaseParticleFactory::get_mass_spread 0 1 43 17
/**
 * public
 */
68
inline PN_stdfloat BaseParticleFactory::get_mass_spread(void) const;

395 26 get_terminal_velocity_base 0 4 684 47 BaseParticleFactory::get_terminal_velocity_base 0 1 44 17
/**
 * public
 */
79
inline PN_stdfloat BaseParticleFactory::get_terminal_velocity_base(void) const;

396 28 get_terminal_velocity_spread 0 4 684 49 BaseParticleFactory::get_terminal_velocity_spread 0 1 45 17
/**
 * public
 */
81
inline PN_stdfloat BaseParticleFactory::get_terminal_velocity_spread(void) const;

397 14 alloc_particle 0 6 684 35 BaseParticleFactory::alloc_particle 0 1 46 0
74
virtual BaseParticle *BaseParticleFactory::alloc_particle(void) const = 0;

398 17 populate_particle 0 4 684 38 BaseParticleFactory::populate_particle 0 1 47 17
/**
 * public
 */
62
void BaseParticleFactory::populate_particle(BaseParticle *bp);

399 6 output 0 6 684 27 BaseParticleFactory::output 0 1 48 67
/**
 * Write a string representation of this instance to <out>.
 */
66
virtual void BaseParticleFactory::output(std::ostream &out) const;

400 5 write 0 6 684 26 BaseParticleFactory::write 0 1 49 67
/**
 * Write a string representation of this instance to <out>.
 */
81
virtual void BaseParticleFactory::write(std::ostream &out, int indent = 0) const;

401 15 get_render_node 0 4 685 37 BaseParticleRenderer::get_render_node 0 1 50 37
/**
 * Query the geomnode pointer
 */
67
inline GeomNode *BaseParticleRenderer::get_render_node(void) const;

402 20 get_render_node_path 0 4 685 42 BaseParticleRenderer::get_render_node_path 0 1 51 37
/**
 * Query the geomnode pointer
 */
71
inline NodePath BaseParticleRenderer::get_render_node_path(void) const;

403 14 set_alpha_mode 0 4 685 36 BaseParticleRenderer::set_alpha_mode 0 1 52 8
/**

 */
101
inline void BaseParticleRenderer::set_alpha_mode(BaseParticleRenderer::ParticleRendererAlphaMode am);

404 14 get_alpha_mode 0 4 685 36 BaseParticleRenderer::get_alpha_mode 0 1 53 8
/**

 */
104
inline BaseParticleRenderer::ParticleRendererAlphaMode BaseParticleRenderer::get_alpha_mode(void) const;

405 14 set_user_alpha 0 4 685 36 BaseParticleRenderer::set_user_alpha 0 1 54 43
/**
 * sets alpha for "user" alpha mode
 */
65
inline void BaseParticleRenderer::set_user_alpha(PN_stdfloat ua);

406 14 get_user_alpha 0 4 685 36 BaseParticleRenderer::get_user_alpha 0 1 55 43
/**
 * gets alpha for "user" alpha mode
 */
68
inline PN_stdfloat BaseParticleRenderer::get_user_alpha(void) const;

407 20 set_color_blend_mode 0 4 685 42 BaseParticleRenderer::set_color_blend_mode 0 1 56 56
/**
 * sets the ColorBlendAttrib on the _render_node
 */
200
inline void BaseParticleRenderer::set_color_blend_mode(ColorBlendAttrib::Mode bm, ColorBlendAttrib::Operand oa = ::ColorBlendAttrib::O_zero, ColorBlendAttrib::Operand ob = ::ColorBlendAttrib::O_zero);

408 16 set_ignore_scale 0 4 685 38 BaseParticleRenderer::set_ignore_scale 0 1 57 411
/**
 * Sets the "ignore scale" flag.  When this is true, particles will be drawn
 * as if they had no scale, regardless of whatever scale might be inherited
 * from above the render node in the scene graph.
 *
 * This flag is mainly useful to support legacy code that was written for a
 * very early version of Panda, whose sprite particle renderer had a bug that
 * incorrectly ignored the inherited scale.
 */
63
void BaseParticleRenderer::set_ignore_scale(bool ignore_scale);

409 16 get_ignore_scale 0 4 685 38 BaseParticleRenderer::get_ignore_scale 0 1 58 68
/**
 * Returns the "ignore scale" flag.  See set_ignore_scale().
 */
63
inline bool BaseParticleRenderer::get_ignore_scale(void) const;

410 6 output 0 6 685 28 BaseParticleRenderer::output 0 1 59 67
/**
 * Write a string representation of this instance to <out>.
 */
67
virtual void BaseParticleRenderer::output(std::ostream &out) const;

411 5 write 0 6 685 27 BaseParticleRenderer::write 0 1 60 67
/**
 * Write a string representation of this instance to <out>.
 */
82
virtual void BaseParticleRenderer::write(std::ostream &out, int indent = 0) const;

412 10 BoxEmitter 0 260 688 22 BoxEmitter::BoxEmitter 0 2 61 62 51
/**
 * constructor
 */

/**
 * copy constructor
 */
77
BoxEmitter::BoxEmitter(void);
BoxEmitter::BoxEmitter(BoxEmitter const &copy);

413 13 set_min_bound 0 4 688 25 BoxEmitter::set_min_bound 0 1 63 30
/**
 * boundary assignment
 */
59
inline void BoxEmitter::set_min_bound(LPoint3 const &vmin);

414 13 set_max_bound 0 4 688 25 BoxEmitter::set_max_bound 0 1 64 30
/**
 * boundary assignment
 */
59
inline void BoxEmitter::set_max_bound(LPoint3 const &vmax);

415 13 get_min_bound 0 4 688 25 BoxEmitter::get_min_bound 0 1 65 28
/**
 * boundary accessor
 */
53
inline LPoint3 BoxEmitter::get_min_bound(void) const;

416 13 get_max_bound 0 4 688 25 BoxEmitter::get_max_bound 0 1 66 28
/**
 * boundary accessor
 */
53
inline LPoint3 BoxEmitter::get_max_bound(void) const;

417 11 get_color_a 0 4 689 47 ColorInterpolationFunctionConstant::get_color_a 0 1 67 0
74
inline LColor ColorInterpolationFunctionConstant::get_color_a(void) const;

418 11 set_color_a 0 4 689 47 ColorInterpolationFunctionConstant::set_color_a 0 1 68 0
77
inline void ColorInterpolationFunctionConstant::set_color_a(LColor const &c);

419 14 get_class_type 0 4 689 50 ColorInterpolationFunctionConstant::get_class_type 0 1 69 0
75
static TypeHandle ColorInterpolationFunctionConstant::get_class_type(void);

420 35 ~ColorInterpolationFunctionConstant 0 516 689 71 ColorInterpolationFunctionConstant::~ColorInterpolationFunctionConstant 0 0 0
78
ColorInterpolationFunctionConstant::~ColorInterpolationFunctionConstant(void);

421 11 get_color_b 0 4 691 45 ColorInterpolationFunctionLinear::get_color_b 0 1 70 0
72
inline LColor ColorInterpolationFunctionLinear::get_color_b(void) const;

422 11 set_color_b 0 4 691 45 ColorInterpolationFunctionLinear::set_color_b 0 1 71 0
75
inline void ColorInterpolationFunctionLinear::set_color_b(LColor const &c);

423 14 get_class_type 0 4 691 48 ColorInterpolationFunctionLinear::get_class_type 0 1 72 0
73
static TypeHandle ColorInterpolationFunctionLinear::get_class_type(void);

424 33 ~ColorInterpolationFunctionLinear 0 516 691 67 ColorInterpolationFunctionLinear::~ColorInterpolationFunctionLinear 0 0 0
74
ColorInterpolationFunctionLinear::~ColorInterpolationFunctionLinear(void);

425 11 get_width_a 0 4 692 47 ColorInterpolationFunctionStepwave::get_width_a 0 1 73 0
79
inline PN_stdfloat ColorInterpolationFunctionStepwave::get_width_a(void) const;

426 11 get_width_b 0 4 692 47 ColorInterpolationFunctionStepwave::get_width_b 0 1 74 0
79
inline PN_stdfloat ColorInterpolationFunctionStepwave::get_width_b(void) const;

427 11 set_width_a 0 4 692 47 ColorInterpolationFunctionStepwave::set_width_a 0 1 75 0
81
inline void ColorInterpolationFunctionStepwave::set_width_a(PN_stdfloat const w);

428 11 set_width_b 0 4 692 47 ColorInterpolationFunctionStepwave::set_width_b 0 1 76 0
81
inline void ColorInterpolationFunctionStepwave::set_width_b(PN_stdfloat const w);

429 14 get_class_type 0 4 692 50 ColorInterpolationFunctionStepwave::get_class_type 0 1 77 0
75
static TypeHandle ColorInterpolationFunctionStepwave::get_class_type(void);

430 35 ~ColorInterpolationFunctionStepwave 0 516 692 71 ColorInterpolationFunctionStepwave::~ColorInterpolationFunctionStepwave 0 0 0
78
ColorInterpolationFunctionStepwave::~ColorInterpolationFunctionStepwave(void);

431 10 get_period 0 4 693 46 ColorInterpolationFunctionSinusoid::get_period 0 1 78 0
78
inline PN_stdfloat ColorInterpolationFunctionSinusoid::get_period(void) const;

432 10 set_period 0 4 693 46 ColorInterpolationFunctionSinusoid::set_period 0 1 79 0
80
inline void ColorInterpolationFunctionSinusoid::set_period(PN_stdfloat const p);

433 14 get_class_type 0 4 693 50 ColorInterpolationFunctionSinusoid::get_class_type 0 1 80 0
75
static TypeHandle ColorInterpolationFunctionSinusoid::get_class_type(void);

434 35 ~ColorInterpolationFunctionSinusoid 0 516 693 71 ColorInterpolationFunctionSinusoid::~ColorInterpolationFunctionSinusoid 0 0 0
78
ColorInterpolationFunctionSinusoid::~ColorInterpolationFunctionSinusoid(void);

435 25 ColorInterpolationSegment 0 260 694 52 ColorInterpolationSegment::ColorInterpolationSegment 0 1 81 0
89
ColorInterpolationSegment::ColorInterpolationSegment(ColorInterpolationSegment const &s);

436 12 get_function 0 4 694 39 ColorInterpolationSegment::get_function 0 1 82 59
// INLINE ColorInterpolationFunction* get_function() const;
80
inline TypedReferenceCount *ColorInterpolationSegment::get_function(void) const;

437 14 get_time_begin 0 4 694 41 ColorInterpolationSegment::get_time_begin 0 1 83 0
73
inline PN_stdfloat ColorInterpolationSegment::get_time_begin(void) const;

438 12 get_time_end 0 4 694 39 ColorInterpolationSegment::get_time_end 0 1 84 0
71
inline PN_stdfloat ColorInterpolationSegment::get_time_end(void) const;

439 12 is_modulated 0 4 694 39 ColorInterpolationSegment::is_modulated 0 1 85 0
64
inline bool ColorInterpolationSegment::is_modulated(void) const;

440 6 get_id 0 4 694 33 ColorInterpolationSegment::get_id 0 1 86 0
57
inline int ColorInterpolationSegment::get_id(void) const;

441 10 is_enabled 0 4 694 37 ColorInterpolationSegment::is_enabled 0 1 87 0
62
inline bool ColorInterpolationSegment::is_enabled(void) const;

442 12 set_function 0 4 694 39 ColorInterpolationSegment::set_function 0 1 88 0
90
inline void ColorInterpolationSegment::set_function(ColorInterpolationFunction *function);

443 14 set_time_begin 0 4 694 41 ColorInterpolationSegment::set_time_begin 0 1 89 0
78
inline void ColorInterpolationSegment::set_time_begin(PN_stdfloat const time);

444 12 set_time_end 0 4 694 39 ColorInterpolationSegment::set_time_end 0 1 90 0
76
inline void ColorInterpolationSegment::set_time_end(PN_stdfloat const time);

445 16 set_is_modulated 0 4 694 43 ColorInterpolationSegment::set_is_modulated 0 1 91 0
73
inline void ColorInterpolationSegment::set_is_modulated(bool const flag);

446 11 set_enabled 0 4 694 38 ColorInterpolationSegment::set_enabled 0 1 92 0
71
inline void ColorInterpolationSegment::set_enabled(bool const enabled);

447 25 ColorInterpolationManager 0 260 695 52 ColorInterpolationManager::ColorInterpolationManager 0 3 93 94 95 0
223
ColorInterpolationManager::ColorInterpolationManager(void);
ColorInterpolationManager::ColorInterpolationManager(LColor const &c);
ColorInterpolationManager::ColorInterpolationManager(ColorInterpolationManager const &copy);

448 12 add_constant 0 4 695 39 ColorInterpolationManager::add_constant 0 1 96 0
196
int ColorInterpolationManager::add_constant(PN_stdfloat const time_begin = 0.0, PN_stdfloat const time_end = 1.0, LColor const &color = LColor(1.0, 1.0, 1.0, 1.0), bool const is_modulated = true);

449 10 add_linear 0 4 695 37 ColorInterpolationManager::add_linear 0 1 97 0
248
int ColorInterpolationManager::add_linear(PN_stdfloat const time_begin = 0.0, PN_stdfloat const time_end = 1.0, LColor const &color_a = LColor(1.0, 0.0, 0.0, 1.0), LColor const &color_b = LColor(0.0, 1.0, 0.0, 1.0), bool const is_modulated = true);

450 12 add_stepwave 0 4 695 39 ColorInterpolationManager::add_stepwave 0 1 98 0
316
int ColorInterpolationManager::add_stepwave(PN_stdfloat const time_begin = 0.0, PN_stdfloat const time_end = 1.0, LColor const &color_a = LColor(1.0, 0.0, 0.0, 1.0), LColor const &color_b = LColor(0.0, 1.0, 0.0, 1.0), PN_stdfloat const width_a = 0.5, PN_stdfloat const width_b = 0.5, bool const is_modulated = true);

451 12 add_sinusoid 0 4 695 39 ColorInterpolationManager::add_sinusoid 0 1 99 0
282
int ColorInterpolationManager::add_sinusoid(PN_stdfloat const time_begin = 0.0, PN_stdfloat const time_end = 1.0, LColor const &color_a = LColor(1.0, 0.0, 0.0, 1.0), LColor const &color_b = LColor(0.0, 1.0, 0.0, 1.0), PN_stdfloat const period = 1.0, bool const is_modulated = true);

452 17 set_default_color 0 4 695 44 ColorInterpolationManager::set_default_color 0 1 100 0
74
inline void ColorInterpolationManager::set_default_color(LColor const &c);

453 11 get_segment 0 4 695 38 ColorInterpolationManager::get_segment 0 1 101 0
91
inline ColorInterpolationSegment *ColorInterpolationManager::get_segment(int const seg_id);

454 19 get_segment_id_list 0 4 695 46 ColorInterpolationManager::get_segment_id_list 0 1 102 0
72
inline std::string ColorInterpolationManager::get_segment_id_list(void);

455 13 clear_segment 0 4 695 40 ColorInterpolationManager::clear_segment 0 1 103 0
64
void ColorInterpolationManager::clear_segment(int const seg_id);

456 16 clear_to_initial 0 4 695 43 ColorInterpolationManager::clear_to_initial 0 1 104 0
55
void ColorInterpolationManager::clear_to_initial(void);

457 11 DiscEmitter 0 260 696 24 DiscEmitter::DiscEmitter 0 2 105 106 51
/**
 * constructor
 */

/**
 * copy constructor
 */
82
DiscEmitter::DiscEmitter(void);
DiscEmitter::DiscEmitter(DiscEmitter const &copy);

458 10 set_radius 0 4 696 23 DiscEmitter::set_radius 0 1 107 0
51
inline void DiscEmitter::set_radius(PN_stdfloat r);

459 15 set_outer_angle 0 4 696 28 DiscEmitter::set_outer_angle 0 1 108 0
62
inline void DiscEmitter::set_outer_angle(PN_stdfloat o_angle);

460 15 set_inner_angle 0 4 696 28 DiscEmitter::set_inner_angle 0 1 109 0
62
inline void DiscEmitter::set_inner_angle(PN_stdfloat i_angle);

461 19 set_outer_magnitude 0 4 696 32 DiscEmitter::set_outer_magnitude 0 1 110 0
64
inline void DiscEmitter::set_outer_magnitude(PN_stdfloat o_mag);

462 19 set_inner_magnitude 0 4 696 32 DiscEmitter::set_inner_magnitude 0 1 111 0
64
inline void DiscEmitter::set_inner_magnitude(PN_stdfloat i_mag);

463 17 set_cubic_lerping 0 4 696 30 DiscEmitter::set_cubic_lerping 0 1 112 0
55
inline void DiscEmitter::set_cubic_lerping(bool clerp);

464 10 get_radius 0 4 696 23 DiscEmitter::get_radius 0 1 113 0
55
inline PN_stdfloat DiscEmitter::get_radius(void) const;

465 15 get_outer_angle 0 4 696 28 DiscEmitter::get_outer_angle 0 1 114 0
60
inline PN_stdfloat DiscEmitter::get_outer_angle(void) const;

466 15 get_inner_angle 0 4 696 28 DiscEmitter::get_inner_angle 0 1 115 0
60
inline PN_stdfloat DiscEmitter::get_inner_angle(void) const;

467 19 get_outer_magnitude 0 4 696 32 DiscEmitter::get_outer_magnitude 0 1 116 0
64
inline PN_stdfloat DiscEmitter::get_outer_magnitude(void) const;

468 19 get_inner_magnitude 0 4 696 32 DiscEmitter::get_inner_magnitude 0 1 117 0
64
inline PN_stdfloat DiscEmitter::get_inner_magnitude(void) const;

469 17 get_cubic_lerping 0 4 696 30 DiscEmitter::get_cubic_lerping 0 1 118 0
55
inline bool DiscEmitter::get_cubic_lerping(void) const;

470 20 GeomParticleRenderer 0 260 697 42 GeomParticleRenderer::GeomParticleRenderer 0 2 119 120 0
254
explicit GeomParticleRenderer::GeomParticleRenderer(BaseParticleRenderer::ParticleRendererAlphaMode am = ::BaseParticleRenderer::PR_ALPHA_NONE, PandaNode *geom_node = nullptr);
GeomParticleRenderer::GeomParticleRenderer(GeomParticleRenderer const &copy);

471 13 set_geom_node 0 4 697 35 GeomParticleRenderer::set_geom_node 0 1 121 0
65
inline void GeomParticleRenderer::set_geom_node(PandaNode *node);

472 13 get_geom_node 0 4 697 35 GeomParticleRenderer::get_geom_node 0 1 122 8
/**

 */
60
inline PandaNode *GeomParticleRenderer::get_geom_node(void);

473 31 get_color_interpolation_manager 0 4 697 53 GeomParticleRenderer::get_color_interpolation_manager 0 1 123 8
/**

 */
100
inline ColorInterpolationManager *GeomParticleRenderer::get_color_interpolation_manager(void) const;

474 16 set_x_scale_flag 0 4 697 38 GeomParticleRenderer::set_x_scale_flag 0 1 124 8
/**

 */
73
inline void GeomParticleRenderer::set_x_scale_flag(bool animate_x_ratio);

475 16 set_y_scale_flag 0 4 697 38 GeomParticleRenderer::set_y_scale_flag 0 1 125 8
/**

 */
73
inline void GeomParticleRenderer::set_y_scale_flag(bool animate_y_ratio);

476 16 set_z_scale_flag 0 4 697 38 GeomParticleRenderer::set_z_scale_flag 0 1 126 8
/**

 */
73
inline void GeomParticleRenderer::set_z_scale_flag(bool animate_z_ratio);

477 19 set_initial_x_scale 0 4 697 41 GeomParticleRenderer::set_initial_x_scale 0 1 127 8
/**

 */
83
inline void GeomParticleRenderer::set_initial_x_scale(PN_stdfloat initial_x_scale);

478 17 set_final_x_scale 0 4 697 39 GeomParticleRenderer::set_final_x_scale 0 1 128 8
/**

 */
79
inline void GeomParticleRenderer::set_final_x_scale(PN_stdfloat final_x_scale);

479 19 set_initial_y_scale 0 4 697 41 GeomParticleRenderer::set_initial_y_scale 0 1 129 8
/**

 */
83
inline void GeomParticleRenderer::set_initial_y_scale(PN_stdfloat initial_y_scale);

480 17 set_final_y_scale 0 4 697 39 GeomParticleRenderer::set_final_y_scale 0 1 130 8
/**

 */
79
inline void GeomParticleRenderer::set_final_y_scale(PN_stdfloat final_y_scale);

481 19 set_initial_z_scale 0 4 697 41 GeomParticleRenderer::set_initial_z_scale 0 1 131 8
/**

 */
83
inline void GeomParticleRenderer::set_initial_z_scale(PN_stdfloat initial_z_scale);

482 17 set_final_z_scale 0 4 697 39 GeomParticleRenderer::set_final_z_scale 0 1 132 8
/**

 */
79
inline void GeomParticleRenderer::set_final_z_scale(PN_stdfloat final_z_scale);

483 16 get_x_scale_flag 0 4 697 38 GeomParticleRenderer::get_x_scale_flag 0 1 133 8
/**

 */
63
inline bool GeomParticleRenderer::get_x_scale_flag(void) const;

484 16 get_y_scale_flag 0 4 697 38 GeomParticleRenderer::get_y_scale_flag 0 1 134 8
/**

 */
63
inline bool GeomParticleRenderer::get_y_scale_flag(void) const;

485 16 get_z_scale_flag 0 4 697 38 GeomParticleRenderer::get_z_scale_flag 0 1 135 8
/**

 */
63
inline bool GeomParticleRenderer::get_z_scale_flag(void) const;

486 19 get_initial_x_scale 0 4 697 41 GeomParticleRenderer::get_initial_x_scale 0 1 136 8
/**

 */
73
inline PN_stdfloat GeomParticleRenderer::get_initial_x_scale(void) const;

487 17 get_final_x_scale 0 4 697 39 GeomParticleRenderer::get_final_x_scale 0 1 137 8
/**

 */
71
inline PN_stdfloat GeomParticleRenderer::get_final_x_scale(void) const;

488 19 get_initial_y_scale 0 4 697 41 GeomParticleRenderer::get_initial_y_scale 0 1 138 8
/**

 */
73
inline PN_stdfloat GeomParticleRenderer::get_initial_y_scale(void) const;

489 17 get_final_y_scale 0 4 697 39 GeomParticleRenderer::get_final_y_scale 0 1 139 8
/**

 */
71
inline PN_stdfloat GeomParticleRenderer::get_final_y_scale(void) const;

490 19 get_initial_z_scale 0 4 697 41 GeomParticleRenderer::get_initial_z_scale 0 1 140 8
/**

 */
73
inline PN_stdfloat GeomParticleRenderer::get_initial_z_scale(void) const;

491 17 get_final_z_scale 0 4 697 39 GeomParticleRenderer::get_final_z_scale 0 1 141 8
/**

 */
71
inline PN_stdfloat GeomParticleRenderer::get_final_z_scale(void) const;

492 11 LineEmitter 0 260 698 24 LineEmitter::LineEmitter 0 2 142 143 46
/**
 * constructor
 */

/**
 * constructor
 */
82
LineEmitter::LineEmitter(void);
LineEmitter::LineEmitter(LineEmitter const &copy);

493 13 set_endpoint1 0 4 698 26 LineEmitter::set_endpoint1 0 1 144 30
/**
 * endpoint assignment
 */
61
inline void LineEmitter::set_endpoint1(LPoint3 const &point);

494 13 set_endpoint2 0 4 698 26 LineEmitter::set_endpoint2 0 1 145 30
/**
 * endpoint assignment
 */
61
inline void LineEmitter::set_endpoint2(LPoint3 const &point);

495 13 get_endpoint1 0 4 698 26 LineEmitter::get_endpoint1 0 1 146 28
/**
 * endpoint accessor
 */
54
inline LPoint3 LineEmitter::get_endpoint1(void) const;

496 13 get_endpoint2 0 4 698 26 LineEmitter::get_endpoint2 0 1 147 28
/**
 * endpoint accessor
 */
54
inline LPoint3 LineEmitter::get_endpoint2(void) const;

497 20 LineParticleRenderer 0 260 699 42 LineParticleRenderer::LineParticleRenderer 0 3 148 149 150 0
280
LineParticleRenderer::LineParticleRenderer(void);
LineParticleRenderer::LineParticleRenderer(LineParticleRenderer const &copy);
explicit LineParticleRenderer::LineParticleRenderer(LColor const &head, LColor const &tail, BaseParticleRenderer::ParticleRendererAlphaMode alpha_mode);

498 14 set_head_color 0 4 699 36 LineParticleRenderer::set_head_color 0 1 151 8
/**

 */
66
inline void LineParticleRenderer::set_head_color(LColor const &c);

499 14 set_tail_color 0 4 699 36 LineParticleRenderer::set_tail_color 0 1 152 8
/**

 */
66
inline void LineParticleRenderer::set_tail_color(LColor const &c);

500 14 get_head_color 0 4 699 36 LineParticleRenderer::get_head_color 0 1 153 8
/**

 */
70
inline LColor const &LineParticleRenderer::get_head_color(void) const;

501 14 get_tail_color 0 4 699 36 LineParticleRenderer::get_tail_color 0 1 154 8
/**

 */
70
inline LColor const &LineParticleRenderer::get_tail_color(void) const;

502 21 set_line_scale_factor 0 4 699 43 LineParticleRenderer::set_line_scale_factor 0 1 155 19
/**
 * accessor
 */
72
inline void LineParticleRenderer::set_line_scale_factor(PN_stdfloat sf);

503 21 get_line_scale_factor 0 4 699 43 LineParticleRenderer::get_line_scale_factor 0 1 156 19
/**
 * accessor
 */
75
inline PN_stdfloat LineParticleRenderer::get_line_scale_factor(void) const;

504 14 ParticleSystem 0 260 700 30 ParticleSystem::ParticleSystem 0 2 157 158 61
/**
 * Default Constructor.
 */

/**
 * Copy Constructor.
 */
119
explicit ParticleSystem::ParticleSystem(int pool_size = 0);
ParticleSystem::ParticleSystem(ParticleSystem const &copy);

505 13 set_pool_size 0 4 700 29 ParticleSystem::set_pool_size 0 1 159 26
// accessqueries

/**

 */
52
inline void ParticleSystem::set_pool_size(int size);

506 14 set_birth_rate 0 4 700 30 ParticleSystem::set_birth_rate 0 1 160 8
/**

 */
63
inline void ParticleSystem::set_birth_rate(PN_stdfloat new_br);

507 19 set_soft_birth_rate 0 4 700 35 ParticleSystem::set_soft_birth_rate 0 1 161 8
/**

 */
68
inline void ParticleSystem::set_soft_birth_rate(PN_stdfloat new_br);

508 15 set_litter_size 0 4 700 31 ParticleSystem::set_litter_size 0 1 162 8
/**

 */
56
inline void ParticleSystem::set_litter_size(int new_ls);

509 17 set_litter_spread 0 4 700 33 ParticleSystem::set_litter_spread 0 1 163 8
/**

 */
58
inline void ParticleSystem::set_litter_spread(int new_ls);

510 23 set_local_velocity_flag 0 4 700 39 ParticleSystem::set_local_velocity_flag 0 1 164 8
/**

 */
61
inline void ParticleSystem::set_local_velocity_flag(bool lv);

511 27 set_system_grows_older_flag 0 4 700 43 ParticleSystem::set_system_grows_older_flag 0 1 165 8
/**

 */
66
inline void ParticleSystem::set_system_grows_older_flag(bool sgo);

512 19 set_system_lifespan 0 4 700 35 ParticleSystem::set_system_lifespan 0 1 166 8
/**

 */
64
inline void ParticleSystem::set_system_lifespan(PN_stdfloat sl);

513 14 set_system_age 0 4 700 30 ParticleSystem::set_system_age 0 1 167 8
/**

 */
60
inline void ParticleSystem::set_system_age(PN_stdfloat age);

514 22 set_active_system_flag 0 4 700 38 ParticleSystem::set_active_system_flag 0 1 168 8
/**

 */
59
inline void ParticleSystem::set_active_system_flag(bool a);

515 23 set_spawn_on_death_flag 0 4 700 39 ParticleSystem::set_spawn_on_death_flag 0 1 169 8
/**

 */
62
inline void ParticleSystem::set_spawn_on_death_flag(bool sod);

516 21 set_spawn_render_node 0 4 700 37 ParticleSystem::set_spawn_render_node 0 1 170 8
/**

 */
67
inline void ParticleSystem::set_spawn_render_node(PandaNode *node);

517 26 set_spawn_render_node_path 0 4 700 42 ParticleSystem::set_spawn_render_node_path 0 1 171 8
/**

 */
77
inline void ParticleSystem::set_spawn_render_node_path(NodePath const &node);

518 24 set_template_system_flag 0 4 700 40 ParticleSystem::set_template_system_flag 0 1 172 8
/**

 */
63
inline void ParticleSystem::set_template_system_flag(bool tsf);

519 17 set_render_parent 0 4 700 33 ParticleSystem::set_render_parent 0 2 173 174 18
/**

 */

/**

 */
132
inline void ParticleSystem::set_render_parent(PandaNode *node);
inline void ParticleSystem::set_render_parent(NodePath const &node);

520 12 set_renderer 0 4 700 28 ParticleSystem::set_renderer 0 1 175 8
/**

 */
66
inline void ParticleSystem::set_renderer(BaseParticleRenderer *r);

521 11 set_emitter 0 4 700 27 ParticleSystem::set_emitter 0 1 176 8
/**

 */
64
inline void ParticleSystem::set_emitter(BaseParticleEmitter *e);

522 11 set_factory 0 4 700 27 ParticleSystem::set_factory 0 1 177 8
/**

 */
64
inline void ParticleSystem::set_factory(BaseParticleFactory *f);

523 11 set_floor_z 0 4 700 27 ParticleSystem::set_floor_z 0 1 178 8
/**

 */
55
inline void ParticleSystem::set_floor_z(PN_stdfloat z);

524 13 clear_floor_z 0 4 700 29 ParticleSystem::clear_floor_z 0 1 179 8
/**

 */
48
inline void ParticleSystem::clear_floor_z(void);

525 13 get_pool_size 0 4 700 29 ParticleSystem::get_pool_size 0 1 180 8
/**

 */
53
inline int ParticleSystem::get_pool_size(void) const;

526 14 get_birth_rate 0 4 700 30 ParticleSystem::get_birth_rate 0 1 181 8
/**

 */
62
inline PN_stdfloat ParticleSystem::get_birth_rate(void) const;

527 19 get_soft_birth_rate 0 4 700 35 ParticleSystem::get_soft_birth_rate 0 1 182 8
/**

 */
67
inline PN_stdfloat ParticleSystem::get_soft_birth_rate(void) const;

528 15 get_litter_size 0 4 700 31 ParticleSystem::get_litter_size 0 1 183 8
/**

 */
55
inline int ParticleSystem::get_litter_size(void) const;

529 17 get_litter_spread 0 4 700 33 ParticleSystem::get_litter_spread 0 1 184 8
/**

 */
57
inline int ParticleSystem::get_litter_spread(void) const;

530 23 get_local_velocity_flag 0 4 700 39 ParticleSystem::get_local_velocity_flag 0 1 185 8
/**

 */
64
inline bool ParticleSystem::get_local_velocity_flag(void) const;

531 27 get_system_grows_older_flag 0 4 700 43 ParticleSystem::get_system_grows_older_flag 0 1 186 8
/**

 */
68
inline bool ParticleSystem::get_system_grows_older_flag(void) const;

532 19 get_system_lifespan 0 4 700 35 ParticleSystem::get_system_lifespan 0 1 187 8
/**

 */
67
inline PN_stdfloat ParticleSystem::get_system_lifespan(void) const;

533 14 get_system_age 0 4 700 30 ParticleSystem::get_system_age 0 1 188 8
/**

 */
62
inline PN_stdfloat ParticleSystem::get_system_age(void) const;

534 22 get_active_system_flag 0 4 700 38 ParticleSystem::get_active_system_flag 0 1 189 8
/**

 */
63
inline bool ParticleSystem::get_active_system_flag(void) const;

535 23 get_spawn_on_death_flag 0 4 700 39 ParticleSystem::get_spawn_on_death_flag 0 1 190 8
/**

 */
64
inline bool ParticleSystem::get_spawn_on_death_flag(void) const;

536 21 get_spawn_render_node 0 4 700 37 ParticleSystem::get_spawn_render_node 0 1 191 8
/**

 */
68
inline PandaNode *ParticleSystem::get_spawn_render_node(void) const;

537 26 get_spawn_render_node_path 0 4 700 42 ParticleSystem::get_spawn_render_node_path 0 1 192 8
/**

 */
71
inline NodePath ParticleSystem::get_spawn_render_node_path(void) const;

538 22 get_i_was_spawned_flag 0 4 700 38 ParticleSystem::get_i_was_spawned_flag 0 1 193 8
/**

 */
63
inline bool ParticleSystem::get_i_was_spawned_flag(void) const;

539 20 get_living_particles 0 4 700 36 ParticleSystem::get_living_particles 0 1 194 8
/**

 */
60
inline int ParticleSystem::get_living_particles(void) const;

540 17 get_render_parent 0 4 700 33 ParticleSystem::get_render_parent 0 1 195 8
/**

 */
62
inline NodePath ParticleSystem::get_render_parent(void) const;

541 12 get_renderer 0 4 700 28 ParticleSystem::get_renderer 0 1 196 8
/**

 */
70
inline BaseParticleRenderer *ParticleSystem::get_renderer(void) const;

542 11 get_emitter 0 4 700 27 ParticleSystem::get_emitter 0 1 197 8
/**

 */
68
inline BaseParticleEmitter *ParticleSystem::get_emitter(void) const;

543 11 get_factory 0 4 700 27 ParticleSystem::get_factory 0 1 198 8
/**

 */
68
inline BaseParticleFactory *ParticleSystem::get_factory(void) const;

544 11 get_floor_z 0 4 700 27 ParticleSystem::get_floor_z 0 1 199 8
/**

 */
59
inline PN_stdfloat ParticleSystem::get_floor_z(void) const;

545 20 get_tics_since_birth 0 4 700 36 ParticleSystem::get_tics_since_birth 0 1 200 7
/**

*/
68
inline PN_stdfloat ParticleSystem::get_tics_since_birth(void) const;

546 18 add_spawn_template 0 4 700 34 ParticleSystem::add_spawn_template 0 1 201 8
/**

 */
67
inline void ParticleSystem::add_spawn_template(ParticleSystem *ps);

547 21 clear_spawn_templates 0 4 700 37 ParticleSystem::clear_spawn_templates 0 1 202 8
/**

 */
56
inline void ParticleSystem::clear_spawn_templates(void);

548 6 render 0 4 700 22 ParticleSystem::render 0 1 203 134
/**
 * Populates an attached GeomNode structure with the particle geometry for
 * rendering.  This is a wrapper for accessability.
 */
41
inline void ParticleSystem::render(void);

549 12 induce_labor 0 4 700 28 ParticleSystem::induce_labor 0 1 204 93
/**
 * Forces the birth of a particle litter this frame by resetting
 * _tics_since_birth
 */
47
inline void ParticleSystem::induce_labor(void);

550 16 clear_to_initial 0 4 700 32 ParticleSystem::clear_to_initial 0 1 205 104
/**
 * Resets the system to its start state by resizing to 0, then resizing back
 * to current size.
 */
51
inline void ParticleSystem::clear_to_initial(void);

551 9 soft_stop 0 4 700 25 ParticleSystem::soft_stop 0 1 206 71
/**
 * Causes system to use birth rate set by set_soft_birth_rate()
 */
60
inline void ParticleSystem::soft_stop(PN_stdfloat br = 0.0);

552 10 soft_start 0 4 700 26 ParticleSystem::soft_start 0 2 207 208 330
/**
 * Causes system to use birth rate set by set_birth_rate()
 */

/**
 * Causes system to use birth rate set by set_birth_rate(), with the system's
 * first birth being delayed by the value of first_birth_delay. Note that a
 * negative delay is perfectly valid, causing the first birth to happen
 * sooner rather than later.
 */
148
inline void ParticleSystem::soft_start(PN_stdfloat br = 0.0);
inline void ParticleSystem::soft_start(PN_stdfloat br, PN_stdfloat first_birth_delay);

553 6 update 0 4 700 22 ParticleSystem::update 0 1 209 0
44
void ParticleSystem::update(PN_stdfloat dt);

554 24 write_free_particle_fifo 0 6 700 40 ParticleSystem::write_free_particle_fifo 0 1 210 67
/**
 * Write a string representation of this instance to <out>.
 */
95
virtual void ParticleSystem::write_free_particle_fifo(std::ostream &out, int indent = 0) const;

555 21 write_spawn_templates 0 6 700 37 ParticleSystem::write_spawn_templates 0 1 211 67
/**
 * Write a string representation of this instance to <out>.
 */
92
virtual void ParticleSystem::write_spawn_templates(std::ostream &out, int indent = 0) const;

556 14 get_class_type 0 4 700 30 ParticleSystem::get_class_type 0 1 212 0
55
static TypeHandle ParticleSystem::get_class_type(void);

557 12 PointEmitter 0 260 702 26 PointEmitter::PointEmitter 0 2 213 214 51
/**
 * constructor
 */

/**
 * copy constructor
 */
87
PointEmitter::PointEmitter(void);
PointEmitter::PointEmitter(PointEmitter const &copy);

558 12 set_location 0 4 702 26 PointEmitter::set_location 0 1 215 24
/**
 * point setting
 */
57
inline void PointEmitter::set_location(LPoint3 const &p);

559 12 get_location 0 4 702 26 PointEmitter::get_location 0 1 216 0
54
inline LPoint3 PointEmitter::get_location(void) const;

560 20 PointParticleFactory 0 260 703 42 PointParticleFactory::PointParticleFactory 0 2 217 218 59
/**
 * default constructor
 */

/**
 * copy constructor
 */
127
PointParticleFactory::PointParticleFactory(void);
PointParticleFactory::PointParticleFactory(PointParticleFactory const &copy);

561 21 PointParticleRenderer 0 260 704 44 PointParticleRenderer::PointParticleRenderer 0 2 219 220 59
/**
 * special constructor
 */

/**
 * Copy constructor
 */
533
PointParticleRenderer::PointParticleRenderer(PointParticleRenderer const &copy);
explicit PointParticleRenderer::PointParticleRenderer(BaseParticleRenderer::ParticleRendererAlphaMode ad = ::BaseParticleRenderer::PR_ALPHA_NONE, PN_stdfloat point_size = 1.0, PointParticleRenderer::PointParticleBlendType bt = ::PointParticleRenderer::PP_ONE_COLOR, BaseParticleRenderer::ParticleRendererBlendMethod bm = ::BaseParticleRenderer::PP_NO_BLEND, LColor const &sc = LColor(1.0, 1.0, 1.0, 1.0), LColor const &ec = LColor(1.0, 1.0, 1.0, 1.0));

562 14 set_point_size 0 4 704 37 PointParticleRenderer::set_point_size 0 1 221 8
/**

 */
74
inline void PointParticleRenderer::set_point_size(PN_stdfloat point_size);

563 15 set_start_color 0 4 704 38 PointParticleRenderer::set_start_color 0 1 222 8
/**

 */
69
inline void PointParticleRenderer::set_start_color(LColor const &sc);

564 13 set_end_color 0 4 704 36 PointParticleRenderer::set_end_color 0 1 223 8
/**

 */
67
inline void PointParticleRenderer::set_end_color(LColor const &ec);

565 14 set_blend_type 0 4 704 37 PointParticleRenderer::set_blend_type 0 1 224 8
/**

 */
100
inline void PointParticleRenderer::set_blend_type(PointParticleRenderer::PointParticleBlendType bt);

566 16 set_blend_method 0 4 704 39 PointParticleRenderer::set_blend_method 0 1 225 8
/**

 */
106
inline void PointParticleRenderer::set_blend_method(BaseParticleRenderer::ParticleRendererBlendMethod bm);

567 14 get_point_size 0 4 704 37 PointParticleRenderer::get_point_size 0 1 226 8
/**

 */
69
inline PN_stdfloat PointParticleRenderer::get_point_size(void) const;

568 15 get_start_color 0 4 704 38 PointParticleRenderer::get_start_color 0 1 227 8
/**

 */
72
inline LColor const &PointParticleRenderer::get_start_color(void) const;

569 13 get_end_color 0 4 704 36 PointParticleRenderer::get_end_color 0 1 228 8
/**

 */
70
inline LColor const &PointParticleRenderer::get_end_color(void) const;

570 14 get_blend_type 0 4 704 37 PointParticleRenderer::get_blend_type 0 1 229 8
/**

 */
103
inline PointParticleRenderer::PointParticleBlendType PointParticleRenderer::get_blend_type(void) const;

571 16 get_blend_method 0 4 704 39 PointParticleRenderer::get_blend_method 0 1 230 8
/**

 */
109
inline BaseParticleRenderer::ParticleRendererBlendMethod PointParticleRenderer::get_blend_method(void) const;

572 16 RectangleEmitter 0 260 706 34 RectangleEmitter::RectangleEmitter 0 2 231 232 51
/**
 * constructor
 */

/**
 * copy constructor
 */
107
RectangleEmitter::RectangleEmitter(void);
RectangleEmitter::RectangleEmitter(RectangleEmitter const &copy);

573 13 set_min_bound 0 4 706 31 RectangleEmitter::set_min_bound 0 1 233 23
/**
 * boundary set
 */
65
inline void RectangleEmitter::set_min_bound(LPoint2 const &vmin);

574 13 set_max_bound 0 4 706 31 RectangleEmitter::set_max_bound 0 1 234 23
/**
 * boundary set
 */
65
inline void RectangleEmitter::set_max_bound(LPoint2 const &vmax);

575 13 get_min_bound 0 4 706 31 RectangleEmitter::get_min_bound 0 1 235 23
/**
 * boundary get
 */
59
inline LPoint2 RectangleEmitter::get_min_bound(void) const;

576 13 get_max_bound 0 4 706 31 RectangleEmitter::get_max_bound 0 1 236 23
/**
 * boundary get
 */
59
inline LPoint2 RectangleEmitter::get_max_bound(void) const;

577 23 SparkleParticleRenderer 0 260 707 48 SparkleParticleRenderer::SparkleParticleRenderer 0 3 237 238 239 83
/**
 * Default Constructor
 */

/**
 * Constructor
 */

/**
 * Copy Constructor
 */
417
SparkleParticleRenderer::SparkleParticleRenderer(void);
SparkleParticleRenderer::SparkleParticleRenderer(SparkleParticleRenderer const &copy);
explicit SparkleParticleRenderer::SparkleParticleRenderer(LColor const &center, LColor const &edge, PN_stdfloat birth_radius, PN_stdfloat death_radius, SparkleParticleRenderer::SparkleParticleLifeScale life_scale, BaseParticleRenderer::ParticleRendererAlphaMode alpha_mode);

578 16 set_center_color 0 4 707 41 SparkleParticleRenderer::set_center_color 0 1 240 8
/**

 */
71
inline void SparkleParticleRenderer::set_center_color(LColor const &c);

579 14 set_edge_color 0 4 707 39 SparkleParticleRenderer::set_edge_color 0 1 241 8
/**

 */
69
inline void SparkleParticleRenderer::set_edge_color(LColor const &c);

580 16 set_birth_radius 0 4 707 41 SparkleParticleRenderer::set_birth_radius 0 1 242 8
/**

 */
74
inline void SparkleParticleRenderer::set_birth_radius(PN_stdfloat radius);

581 16 set_death_radius 0 4 707 41 SparkleParticleRenderer::set_death_radius 0 1 243 8
/**

 */
74
inline void SparkleParticleRenderer::set_death_radius(PN_stdfloat radius);

582 14 set_life_scale 0 4 707 39 SparkleParticleRenderer::set_life_scale 0 1 244 8
/**

 */
104
inline void SparkleParticleRenderer::set_life_scale(SparkleParticleRenderer::SparkleParticleLifeScale );

583 16 get_center_color 0 4 707 41 SparkleParticleRenderer::get_center_color 0 1 245 8
/**

 */
75
inline LColor const &SparkleParticleRenderer::get_center_color(void) const;

584 14 get_edge_color 0 4 707 39 SparkleParticleRenderer::get_edge_color 0 1 246 8
/**

 */
73
inline LColor const &SparkleParticleRenderer::get_edge_color(void) const;

585 16 get_birth_radius 0 4 707 41 SparkleParticleRenderer::get_birth_radius 0 1 247 8
/**

 */
73
inline PN_stdfloat SparkleParticleRenderer::get_birth_radius(void) const;

586 16 get_death_radius 0 4 707 41 SparkleParticleRenderer::get_death_radius 0 1 248 8
/**

 */
73
inline PN_stdfloat SparkleParticleRenderer::get_death_radius(void) const;

587 14 get_life_scale 0 4 707 39 SparkleParticleRenderer::get_life_scale 0 1 249 8
/**

 */
109
inline SparkleParticleRenderer::SparkleParticleLifeScale SparkleParticleRenderer::get_life_scale(void) const;

588 20 SphereSurfaceEmitter 0 260 709 42 SphereSurfaceEmitter::SphereSurfaceEmitter 0 2 250 251 51
/**
 * constructor
 */

/**
 * copy constructor
 */
127
SphereSurfaceEmitter::SphereSurfaceEmitter(void);
SphereSurfaceEmitter::SphereSurfaceEmitter(SphereSurfaceEmitter const &copy);

589 10 set_radius 0 4 709 32 SphereSurfaceEmitter::set_radius 0 1 252 0
60
inline void SphereSurfaceEmitter::set_radius(PN_stdfloat r);

590 10 get_radius 0 4 709 32 SphereSurfaceEmitter::get_radius 0 1 253 0
64
inline PN_stdfloat SphereSurfaceEmitter::get_radius(void) const;

591 19 SphereVolumeEmitter 0 260 710 40 SphereVolumeEmitter::SphereVolumeEmitter 0 2 254 255 51
/**
 * constructor
 */

/**
 * copy constructor
 */
122
SphereVolumeEmitter::SphereVolumeEmitter(void);
SphereVolumeEmitter::SphereVolumeEmitter(SphereVolumeEmitter const &copy);

592 10 set_radius 0 4 710 31 SphereVolumeEmitter::set_radius 0 1 256 0
59
inline void SphereVolumeEmitter::set_radius(PN_stdfloat r);

593 10 get_radius 0 4 710 31 SphereVolumeEmitter::get_radius 0 1 257 0
63
inline PN_stdfloat SphereVolumeEmitter::get_radius(void) const;

594 15 set_source_info 0 4 711 27 SpriteAnim::set_source_info 0 2 259 260 0
142
void SpriteAnim::set_source_info(std::string const &tex);
void SpriteAnim::set_source_info(std::string const &model, std::string const &node);

595 15 get_source_type 0 4 711 27 SpriteAnim::get_source_type 0 1 261 0
63
SpriteAnim::SourceType SpriteAnim::get_source_type(void) const;

596 14 get_tex_source 0 4 711 26 SpriteAnim::get_tex_source 0 1 262 0
51
std::string SpriteAnim::get_tex_source(void) const;

597 16 get_model_source 0 4 711 28 SpriteAnim::get_model_source 0 1 263 0
53
std::string SpriteAnim::get_model_source(void) const;

598 15 get_node_source 0 4 711 27 SpriteAnim::get_node_source 0 1 264 0
52
std::string SpriteAnim::get_node_source(void) const;

599 14 get_num_frames 0 4 711 26 SpriteAnim::get_num_frames 0 1 265 0
43
int SpriteAnim::get_num_frames(void) const;

600 10 SpriteAnim 0 260 711 22 SpriteAnim::SpriteAnim 0 1 258 0
60
inline SpriteAnim::SpriteAnim(SpriteAnim const &) = default;

601 11 ~SpriteAnim 0 516 711 23 SpriteAnim::~SpriteAnim 0 0 0
30
SpriteAnim::~SpriteAnim(void);

602 22 SpriteParticleRenderer 0 260 713 46 SpriteParticleRenderer::SpriteParticleRenderer 0 2 266 267 51
/**
 * constructor
 */

/**
 * copy constructor
 */
164
explicit SpriteParticleRenderer::SpriteParticleRenderer(Texture *tex = nullptr);
SpriteParticleRenderer::SpriteParticleRenderer(SpriteParticleRenderer const &copy);

603 13 set_from_node 0 4 713 37 SpriteParticleRenderer::set_from_node 0 2 268 269 1389
/**
 * If the source type is important, use this one.
 *
 * model and node should lead to node_path like this: node_path =
 * loader.loadModel(model).find(node)
 *
 * This will remove all previously add textures and resize the renderer to
 * match the new geometry.
 */

/**
 * Sets the properties on this renderer from the geometry referenced by the
 * indicated NodePath.  This should be a reference to a GeomNode or a
 * SequenceNode; it extracts out the texture and UV range from the node.
 *
 * This will remove all previously added textures and animations.  It will
 * also resize the renderer to match this new geometry.
 *
 * If node_path refers to a GeomNode(or has one beneath it) the texture, its
 * size, and UV data will be extracted from that.
 *
 * If node_path references a SequenceNode(or has one beneath it) with multiple
 * GeomNodes beneath it, the size data will correspond only to the first
 * GeomNode found with a valid texture, while the texture and UV information
 * will be stored for each individual node.
 *
 * If size_from_texels is true, the particle size is based on the number of
 * texels in the source image; otherwise, it is based on the size of the first
 * polygon found in the node.
 *
 * model and node are the two items used to construct node_path.  If the
 * source type is important, use set_from_node(NodePath,string,string,bool)
 * instead.
 */
254
void SpriteParticleRenderer::set_from_node(NodePath const &node_path, bool size_from_texels = false);
void SpriteParticleRenderer::set_from_node(NodePath const &node_path, std::string const &model, std::string const &node, bool size_from_texels = false);

604 13 add_from_node 0 4 713 37 SpriteParticleRenderer::add_from_node 0 2 270 271 778
/**
 * This will allow the renderer to randomly choose from more than one texture
 * or sequence at particle birth.
 *
 * If the source type is important, use this one.
 *
 * model and node should lead to node_path like this: node_path =
 * loader.loadModel(model).find(node)
 *
 * If resize is true, or if there are no textures currently on the renderer,
 * it will force the renderer to use the size information from this node from
 * now on.  (Default is false)
 */

/**
 * This will allow the renderer to randomly choose from more than one texture
 * or sequence at particle birth.
 *
 * If resize is true, or if there are no textures currently on the renderer,
 * it will force the renderer to use the size information from this node from
 * now on.  (Default is false)
 */
296
void SpriteParticleRenderer::add_from_node(NodePath const &node_path, bool size_from_texels = false, bool resize = false);
void SpriteParticleRenderer::add_from_node(NodePath const &node_path, std::string const &model, std::string const &node, bool size_from_texels = false, bool resize = false);

605 11 set_texture 0 4 713 35 SpriteParticleRenderer::set_texture 0 1 272 292
/**
 * Sets the renderer up to render the entire texture image.  The scale of each
 * particle is based on the size of the texture in each dimension, modified by
 * texels_per_unit.
 *
 * Used to set the size of the particles.  Will clear all previously loaded
 * textures and animations.
 */
97
inline void SpriteParticleRenderer::set_texture(Texture *tex, PN_stdfloat texels_per_unit = 1.0);

606 11 add_texture 0 4 713 35 SpriteParticleRenderer::add_texture 0 1 273 198
/**
 * Adds texture to image pool, effectively creating a single frame animation
 * that can be selected at particle birth.  This should only be called after a
 * previous call to set_texture().
 */
118
inline void SpriteParticleRenderer::add_texture(Texture *tex, PN_stdfloat texels_per_unit = 1.0, bool resize = false);

607 16 remove_animation 0 4 713 40 SpriteParticleRenderer::remove_animation 0 1 274 62
/**
 * Removes an animation texture set from the renderer.
 */
66
inline void SpriteParticleRenderer::remove_animation(int const n);

608 9 set_ll_uv 0 4 713 33 SpriteParticleRenderer::set_ll_uv 0 2 275 276 428
/**
 * Sets the UV coordinate of the lower-left corner of all the sprites
 * generated by this renderer.  Normally this is (0, 0), but it might be set
 * to something else to use only a portion of the texture.
 */

/**
 * Sets the UV coordinate of the lower-left corner of all the sprites
 * generated by this renderer.  Normally this is (0, 0), but it might be set
 * to something else to use only a portion of the texture.
 */
174
inline void SpriteParticleRenderer::set_ll_uv(LTexCoord const &ll_uv);
inline void SpriteParticleRenderer::set_ll_uv(LTexCoord const &ll_uv, int const anim, int const frame);

609 9 set_ur_uv 0 4 713 33 SpriteParticleRenderer::set_ur_uv 0 2 277 278 430
/**
 * Sets the UV coordinate of the upper-right corner of all the sprites
 * generated by this renderer.  Normally this is (1, 1), but it might be set
 * to something else to use only a portion of the texture.
 */

/**
 * Sets the UV coordinate of the upper-right corner of all the sprites
 * generated by this renderer.  Normally this is (1, 1), but it might be set
 * to something else to use only a portion of the texture.
 */
174
inline void SpriteParticleRenderer::set_ur_uv(LTexCoord const &ur_uv);
inline void SpriteParticleRenderer::set_ur_uv(LTexCoord const &ur_uv, int const anim, int const frame);

610 8 set_size 0 4 713 32 SpriteParticleRenderer::set_size 0 1 279 57
/**
 * Sets the size of each particle in world units.
 */
84
inline void SpriteParticleRenderer::set_size(PN_stdfloat width, PN_stdfloat height);

611 9 set_color 0 4 713 33 SpriteParticleRenderer::set_color 0 1 280 8
/**

 */
67
inline void SpriteParticleRenderer::set_color(LColor const &color);

612 16 set_x_scale_flag 0 4 713 40 SpriteParticleRenderer::set_x_scale_flag 0 1 281 8
/**

 */
75
inline void SpriteParticleRenderer::set_x_scale_flag(bool animate_x_ratio);

613 16 set_y_scale_flag 0 4 713 40 SpriteParticleRenderer::set_y_scale_flag 0 1 282 8
/**

 */
75
inline void SpriteParticleRenderer::set_y_scale_flag(bool animate_y_ratio);

614 19 set_anim_angle_flag 0 4 713 43 SpriteParticleRenderer::set_anim_angle_flag 0 1 283 8
/**

 */
76
inline void SpriteParticleRenderer::set_anim_angle_flag(bool animate_theta);

615 19 set_initial_x_scale 0 4 713 43 SpriteParticleRenderer::set_initial_x_scale 0 1 284 8
/**

 */
85
inline void SpriteParticleRenderer::set_initial_x_scale(PN_stdfloat initial_x_scale);

616 17 set_final_x_scale 0 4 713 41 SpriteParticleRenderer::set_final_x_scale 0 1 285 8
/**

 */
81
inline void SpriteParticleRenderer::set_final_x_scale(PN_stdfloat final_x_scale);

617 19 set_initial_y_scale 0 4 713 43 SpriteParticleRenderer::set_initial_y_scale 0 1 286 8
/**

 */
85
inline void SpriteParticleRenderer::set_initial_y_scale(PN_stdfloat initial_y_scale);

618 17 set_final_y_scale 0 4 713 41 SpriteParticleRenderer::set_final_y_scale 0 1 287 8
/**

 */
81
inline void SpriteParticleRenderer::set_final_y_scale(PN_stdfloat final_y_scale);

619 21 set_nonanimated_theta 0 4 713 45 SpriteParticleRenderer::set_nonanimated_theta 0 1 288 8
/**

 */
77
inline void SpriteParticleRenderer::set_nonanimated_theta(PN_stdfloat theta);

620 22 set_alpha_blend_method 0 4 713 46 SpriteParticleRenderer::set_alpha_blend_method 0 1 289 8
/**

 */
113
inline void SpriteParticleRenderer::set_alpha_blend_method(BaseParticleRenderer::ParticleRendererBlendMethod bm);

621 17 set_alpha_disable 0 4 713 41 SpriteParticleRenderer::set_alpha_disable 0 1 290 8
/**

 */
63
inline void SpriteParticleRenderer::set_alpha_disable(bool ad);

622 25 set_animate_frames_enable 0 4 713 49 SpriteParticleRenderer::set_animate_frames_enable 0 1 291 8
/**

 */
71
inline void SpriteParticleRenderer::set_animate_frames_enable(bool an);

623 23 set_animate_frames_rate 0 4 713 47 SpriteParticleRenderer::set_animate_frames_rate 0 1 292 8
/**

 */
75
inline void SpriteParticleRenderer::set_animate_frames_rate(PN_stdfloat r);

624 24 set_animate_frames_index 0 4 713 48 SpriteParticleRenderer::set_animate_frames_index 0 1 293 8
/**

 */
68
inline void SpriteParticleRenderer::set_animate_frames_index(int i);

625 11 get_texture 0 4 713 35 SpriteParticleRenderer::get_texture 0 2 294 295 18
/**

 */

/**

 */
156
inline Texture *SpriteParticleRenderer::get_texture(void) const;
inline Texture *SpriteParticleRenderer::get_texture(int const anim, int const frame) const;

626 13 get_num_anims 0 4 713 37 SpriteParticleRenderer::get_num_anims 0 1 296 0
61
inline int SpriteParticleRenderer::get_num_anims(void) const;

627 8 get_anim 0 4 713 32 SpriteParticleRenderer::get_anim 0 1 297 0
71
inline SpriteAnim *SpriteParticleRenderer::get_anim(int const n) const;

628 13 get_last_anim 0 4 713 37 SpriteParticleRenderer::get_last_anim 0 1 298 0
69
inline SpriteAnim *SpriteParticleRenderer::get_last_anim(void) const;

629 31 get_color_interpolation_manager 0 4 713 55 SpriteParticleRenderer::get_color_interpolation_manager 0 1 299 8
/**

 */
102
inline ColorInterpolationManager *SpriteParticleRenderer::get_color_interpolation_manager(void) const;

630 9 get_ll_uv 0 4 713 33 SpriteParticleRenderer::get_ll_uv 0 2 300 301 160
/**
 * Returns the UV coordinate of the lower-left corner; see set_ll_uv().
 */

/**
 * Returns the UV coordinate of the lower-left corner; see set_ll_uv().
 */
154
inline LTexCoord SpriteParticleRenderer::get_ll_uv(void) const;
inline LTexCoord SpriteParticleRenderer::get_ll_uv(int const anim, int const frame) const;

631 9 get_ur_uv 0 4 713 33 SpriteParticleRenderer::get_ur_uv 0 2 302 303 161
/**
 * Returns the UV coordinate of the lower-left corner; see set_ur_uv().
 */

/**
 * Returns the UV coordinate of the upper-right corner; see set_ur_uv().
 */
154
inline LTexCoord SpriteParticleRenderer::get_ur_uv(void) const;
inline LTexCoord SpriteParticleRenderer::get_ur_uv(int const anim, int const frame) const;

632 9 get_width 0 4 713 33 SpriteParticleRenderer::get_width 0 1 304 61
/**
 * Returns the width of each particle in world units.
 */
65
inline PN_stdfloat SpriteParticleRenderer::get_width(void) const;

633 10 get_height 0 4 713 34 SpriteParticleRenderer::get_height 0 1 305 62
/**
 * Returns the height of each particle in world units.
 */
66
inline PN_stdfloat SpriteParticleRenderer::get_height(void) const;

634 9 get_color 0 4 713 33 SpriteParticleRenderer::get_color 0 1 306 8
/**

 */
60
inline LColor SpriteParticleRenderer::get_color(void) const;

635 16 get_x_scale_flag 0 4 713 40 SpriteParticleRenderer::get_x_scale_flag 0 1 307 8
/**

 */
65
inline bool SpriteParticleRenderer::get_x_scale_flag(void) const;

636 16 get_y_scale_flag 0 4 713 40 SpriteParticleRenderer::get_y_scale_flag 0 1 308 8
/**

 */
65
inline bool SpriteParticleRenderer::get_y_scale_flag(void) const;

637 19 get_anim_angle_flag 0 4 713 43 SpriteParticleRenderer::get_anim_angle_flag 0 1 309 8
/**

 */
68
inline bool SpriteParticleRenderer::get_anim_angle_flag(void) const;

638 19 get_initial_x_scale 0 4 713 43 SpriteParticleRenderer::get_initial_x_scale 0 1 310 8
/**

 */
75
inline PN_stdfloat SpriteParticleRenderer::get_initial_x_scale(void) const;

639 17 get_final_x_scale 0 4 713 41 SpriteParticleRenderer::get_final_x_scale 0 1 311 8
/**

 */
73
inline PN_stdfloat SpriteParticleRenderer::get_final_x_scale(void) const;

640 19 get_initial_y_scale 0 4 713 43 SpriteParticleRenderer::get_initial_y_scale 0 1 312 8
/**

 */
75
inline PN_stdfloat SpriteParticleRenderer::get_initial_y_scale(void) const;

641 17 get_final_y_scale 0 4 713 41 SpriteParticleRenderer::get_final_y_scale 0 1 313 8
/**

 */
73
inline PN_stdfloat SpriteParticleRenderer::get_final_y_scale(void) const;

642 21 get_nonanimated_theta 0 4 713 45 SpriteParticleRenderer::get_nonanimated_theta 0 1 314 8
/**

 */
77
inline PN_stdfloat SpriteParticleRenderer::get_nonanimated_theta(void) const;

643 22 get_alpha_blend_method 0 4 713 46 SpriteParticleRenderer::get_alpha_blend_method 0 1 315 8
/**

 */
116
inline BaseParticleRenderer::ParticleRendererBlendMethod SpriteParticleRenderer::get_alpha_blend_method(void) const;

644 17 get_alpha_disable 0 4 713 41 SpriteParticleRenderer::get_alpha_disable 0 1 316 8
/**

 */
66
inline bool SpriteParticleRenderer::get_alpha_disable(void) const;

645 25 get_animate_frames_enable 0 4 713 49 SpriteParticleRenderer::get_animate_frames_enable 0 1 317 8
/**

 */
74
inline bool SpriteParticleRenderer::get_animate_frames_enable(void) const;

646 23 get_animate_frames_rate 0 4 713 47 SpriteParticleRenderer::get_animate_frames_rate 0 1 318 8
/**

 */
79
inline PN_stdfloat SpriteParticleRenderer::get_animate_frames_rate(void) const;

647 24 get_animate_frames_index 0 4 713 48 SpriteParticleRenderer::get_animate_frames_index 0 1 319 8
/**

 */
72
inline int SpriteParticleRenderer::get_animate_frames_index(void) const;

648 18 TangentRingEmitter 0 260 714 38 TangentRingEmitter::TangentRingEmitter 0 2 320 321 51
/**
 * constructor
 */

/**
 * copy constructor
 */
117
TangentRingEmitter::TangentRingEmitter(void);
TangentRingEmitter::TangentRingEmitter(TangentRingEmitter const &copy);

649 10 set_radius 0 4 714 30 TangentRingEmitter::set_radius 0 1 322 8
/**

 */
58
inline void TangentRingEmitter::set_radius(PN_stdfloat r);

650 17 set_radius_spread 0 4 714 37 TangentRingEmitter::set_radius_spread 0 1 323 8
/**

 */
70
inline void TangentRingEmitter::set_radius_spread(PN_stdfloat spread);

651 10 get_radius 0 4 714 30 TangentRingEmitter::get_radius 0 1 324 8
/**

 */
62
inline PN_stdfloat TangentRingEmitter::get_radius(void) const;

652 17 get_radius_spread 0 4 714 37 TangentRingEmitter::get_radius_spread 0 1 325 8
/**

 */
69
inline PN_stdfloat TangentRingEmitter::get_radius_spread(void) const;

653 20 ZSpinParticleFactory 0 260 715 42 ZSpinParticleFactory::ZSpinParticleFactory 0 2 326 327 51
/**
 * constructor
 */

/**
 * copy constructor
 */
127
ZSpinParticleFactory::ZSpinParticleFactory(void);
ZSpinParticleFactory::ZSpinParticleFactory(ZSpinParticleFactory const &copy);

654 17 set_initial_angle 0 4 715 39 ZSpinParticleFactory::set_initial_angle 0 1 328 8
/**

 */
71
inline void ZSpinParticleFactory::set_initial_angle(PN_stdfloat angle);

655 15 set_final_angle 0 4 715 37 ZSpinParticleFactory::set_final_angle 0 1 329 8
/**

 */
69
inline void ZSpinParticleFactory::set_final_angle(PN_stdfloat angle);

656 24 set_initial_angle_spread 0 4 715 46 ZSpinParticleFactory::set_initial_angle_spread 0 1 330 8
/**

 */
79
inline void ZSpinParticleFactory::set_initial_angle_spread(PN_stdfloat spread);

657 22 set_final_angle_spread 0 4 715 44 ZSpinParticleFactory::set_final_angle_spread 0 1 331 8
/**

 */
77
inline void ZSpinParticleFactory::set_final_angle_spread(PN_stdfloat spread);

658 17 get_initial_angle 0 4 715 39 ZSpinParticleFactory::get_initial_angle 0 1 332 8
/**

 */
71
inline PN_stdfloat ZSpinParticleFactory::get_initial_angle(void) const;

659 15 get_final_angle 0 4 715 37 ZSpinParticleFactory::get_final_angle 0 1 333 8
/**

 */
69
inline PN_stdfloat ZSpinParticleFactory::get_final_angle(void) const;

660 24 get_initial_angle_spread 0 4 715 46 ZSpinParticleFactory::get_initial_angle_spread 0 1 334 8
/**

 */
78
inline PN_stdfloat ZSpinParticleFactory::get_initial_angle_spread(void) const;

661 22 get_final_angle_spread 0 4 715 44 ZSpinParticleFactory::get_final_angle_spread 0 1 335 8
/**

 */
76
inline PN_stdfloat ZSpinParticleFactory::get_final_angle_spread(void) const;

662 20 set_angular_velocity 0 4 715 42 ZSpinParticleFactory::set_angular_velocity 0 1 336 0
70
inline void ZSpinParticleFactory::set_angular_velocity(PN_stdfloat v);

663 20 get_angular_velocity 0 4 715 42 ZSpinParticleFactory::get_angular_velocity 0 1 337 8
/**

 */
74
inline PN_stdfloat ZSpinParticleFactory::get_angular_velocity(void) const;

664 27 set_angular_velocity_spread 0 4 715 49 ZSpinParticleFactory::set_angular_velocity_spread 0 1 338 0
82
inline void ZSpinParticleFactory::set_angular_velocity_spread(PN_stdfloat spread);

665 27 get_angular_velocity_spread 0 4 715 49 ZSpinParticleFactory::get_angular_velocity_spread 0 1 339 0
81
inline PN_stdfloat ZSpinParticleFactory::get_angular_velocity_spread(void) const;

666 23 enable_angular_velocity 0 4 715 45 ZSpinParticleFactory::enable_angular_velocity 0 1 340 0
73
inline void ZSpinParticleFactory::enable_angular_velocity(bool bEnabled);

667 28 get_angular_velocity_enabled 0 4 715 50 ZSpinParticleFactory::get_angular_velocity_enabled 0 1 341 0
75
inline bool ZSpinParticleFactory::get_angular_velocity_enabled(void) const;

668 21 ParticleSystemManager 0 260 716 44 ParticleSystemManager::ParticleSystemManager 0 2 342 343 30
/**
 * default constructor
 */
173
explicit ParticleSystemManager::ParticleSystemManager(int every_nth_frame = 1);
inline ParticleSystemManager::ParticleSystemManager(ParticleSystemManager const &) = default;

669 22 ~ParticleSystemManager 0 518 716 45 ParticleSystemManager::~ParticleSystemManager 0 0 21
/**
 * Destructor
 */
60
virtual ParticleSystemManager::~ParticleSystemManager(void);

670 18 set_frame_stepping 0 4 716 41 ParticleSystemManager::set_frame_stepping 0 1 344 0
75
inline void ParticleSystemManager::set_frame_stepping(int every_nth_frame);

671 18 get_frame_stepping 0 4 716 41 ParticleSystemManager::get_frame_stepping 0 1 345 0
65
inline int ParticleSystemManager::get_frame_stepping(void) const;

672 21 attach_particlesystem 0 4 716 44 ParticleSystemManager::attach_particlesystem 0 1 346 0
77
inline void ParticleSystemManager::attach_particlesystem(ParticleSystem *ps);

673 21 remove_particlesystem 0 4 716 44 ParticleSystemManager::remove_particlesystem 0 1 347 49
/**
 * removes a ps from the maintenance list
 */
70
void ParticleSystemManager::remove_particlesystem(ParticleSystem *ps);

674 5 clear 0 4 716 28 ParticleSystemManager::clear 0 1 348 0
47
inline void ParticleSystemManager::clear(void);

675 12 do_particles 0 4 716 35 ParticleSystemManager::do_particles 0 2 349 350 449
/**
 * does an update and render for each ps in the list.  this is probably the
 * one you want to use.  Rendering is the expensive operation, and particles
 * REALLY should at least be updated every frame, so nth_frame stepping
 * applies only to rendering.
 */

/**
 * does an update and an optional render for a specific ps.  Since rendering
 * is the expensive operation, multiple updates could be applied before
 * calling the final render.
 */
158
void ParticleSystemManager::do_particles(PN_stdfloat dt);
void ParticleSystemManager::do_particles(PN_stdfloat dt, ParticleSystem *ps, bool do_render = true);

676 6 output 0 6 716 29 ParticleSystemManager::output 0 1 351 67
/**
 * Write a string representation of this instance to <out>.
 */
68
virtual void ParticleSystemManager::output(std::ostream &out) const;

677 13 write_ps_list 0 6 716 36 ParticleSystemManager::write_ps_list 0 1 352 67
/**
 * Write a string representation of this instance to <out>.
 */
91
virtual void ParticleSystemManager::write_ps_list(std::ostream &out, int indent = 0) const;

678 5 write 0 6 716 28 ParticleSystemManager::write 0 1 353 67
/**
 * Write a string representation of this instance to <out>.
 */
83
virtual void ParticleSystemManager::write(std::ostream &out, int indent = 0) const;

353
1 0 0 7 4 717 0 0 0 1 4 this 3 717  
2 0 0 4 5 724 0 0 37 /**
 * parent generation function
 */ 3 4 this 3 717  3 pos 1 718  3 vel 1 721  
3 0 0 4 6 724 0 0 35 /**
 * emission type assignment
 */ 2 4 this 3 717  2 et 1 681  
4 0 0 4 7 724 0 0 31 /**
 * amplitude assignment
 */ 2 4 this 3 717  1 a 1 725  
5 0 0 4 8 724 0 0 38 /**
 * amplitude spread assignment
 */ 2 4 this 3 717  2 as 1 725  
6 0 0 4 9 724 0 0 52 // this is a constant force applied to all particles 2 4 this 3 717  2 of 1 727  
7 0 0 4 10 724 0 0 56 /**
 * assignment of explicit emission launch vector
 */ 2 4 this 3 717  3 elv 1 727  
8 0 0 4 11 724 0 0 54 /**
 * assignment of radiate emission origin point
 */ 2 4 this 3 717  2 ro 1 729  
9 0 0 6 12 681 0 0 30 /**
 * emission type query
 */ 1 4 this 3 731  
10 0 0 6 13 725 0 0 26 /**
 * amplitude query
 */ 1 4 this 3 731  
11 0 0 6 14 725 0 0 33 /**
 * amplitude spread query
 */ 1 4 this 3 731  
12 0 0 7 15 721 0 0 29 /**
 * user-defined force
 */ 1 4 this 3 731  
13 0 0 7 16 721 0 0 52 /**
 * query for explicit emission launch vector
 */ 1 4 this 3 731  
14 0 0 7 17 718 0 0 52 /**
 * query for explicit emission launch vector
 */ 1 4 this 3 731  
15 0 0 4 18 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 731  3 out 1 733  
16 0 0 4 19 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 731  3 out 1 733  6 indent 5 735  
17 0 0 7 21 736 0 0 22 /**
 * constructor
 */ 0 
18 0 0 15 21 736 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 737  
19 0 0 4 22 724 0 0 0 2 4 this 3 736  1 r 1 725  
20 0 0 4 23 724 0 0 0 2 4 this 3 736  5 angle 1 725  
21 0 0 4 24 724 0 0 0 2 4 this 3 736  6 spread 1 725  
22 0 0 4 25 724 0 0 0 2 4 this 3 736  16 uniform_emission 1 735  
23 0 0 6 26 725 0 0 0 1 4 this 3 737  
24 0 0 6 27 725 0 0 0 1 4 this 3 737  
25 0 0 6 28 725 0 0 0 1 4 this 3 737  
26 0 0 6 29 735 0 0 0 1 4 this 3 737  
27 0 0 7 31 739 0 0 22 /**
 * constructor
 */ 0 
28 0 0 15 31 739 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 740  
29 0 0 4 32 724 0 0 0 2 4 this 3 739  5 angle 1 725  
30 0 0 4 33 724 0 0 0 2 4 this 3 739  5 angle 1 725  
31 0 0 4 34 724 0 0 0 3 4 this 3 739  10 startAngle 1 725  8 endAngle 1 725  
32 0 0 6 35 725 0 0 0 1 4 this 3 739  
33 0 0 6 36 725 0 0 0 1 4 this 3 739  
34 0 0 4 38 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  2 lb 1 725  
35 0 0 4 39 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  2 ls 1 725  
36 0 0 4 40 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  2 mb 1 725  
37 0 0 4 41 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  2 ms 1 725  
38 0 0 4 42 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  3 tvb 1 725  
39 0 0 4 43 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  3 tvs 1 725  
40 0 0 6 44 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
41 0 0 6 45 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
42 0 0 6 46 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
43 0 0 6 47 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
44 0 0 6 48 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
45 0 0 6 49 725 0 0 17 /**
 * public
 */ 1 4 this 3 743  
46 0 0 6 50 745 0 0 0 1 4 this 3 743  
47 0 0 4 51 724 0 0 17 /**
 * public
 */ 2 4 this 3 742  2 bp 1 745  
48 0 0 4 52 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 743  3 out 1 733  
49 0 0 4 53 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 743  3 out 1 733  6 indent 5 735  
50 0 0 7 57 750 0 0 37 /**
 * Query the geomnode pointer
 */ 1 4 this 3 748  
51 0 0 7 58 752 0 0 37 /**
 * Query the geomnode pointer
 */ 1 4 this 3 748  
52 0 0 4 59 724 0 0 8 /**

 */ 2 4 this 3 753  2 am 1 686  
53 0 0 6 60 686 0 0 8 /**

 */ 1 4 this 3 748  
54 0 0 4 61 724 0 0 43 /**
 * sets alpha for "user" alpha mode
 */ 2 4 this 3 753  2 ua 1 725  
55 0 0 6 62 725 0 0 43 /**
 * gets alpha for "user" alpha mode
 */ 1 4 this 3 748  
56 0 0 4 63 724 0 0 56 /**
 * sets the ColorBlendAttrib on the _render_node
 */ 4 4 this 3 753  2 bm 1 754  2 oa 5 756  2 ob 5 756  
57 0 0 4 64 724 0 0 411 /**
 * Sets the "ignore scale" flag.  When this is true, particles will be drawn
 * as if they had no scale, regardless of whatever scale might be inherited
 * from above the render node in the scene graph.
 *
 * This flag is mainly useful to support legacy code that was written for a
 * very early version of Panda, whose sprite particle renderer had a bug that
 * incorrectly ignored the inherited scale.
 */ 2 4 this 3 753  12 ignore_scale 1 757  
58 0 0 6 65 757 0 0 68 /**
 * Returns the "ignore scale" flag.  See set_ignore_scale().
 */ 1 4 this 3 748  
59 0 0 4 66 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 748  3 out 1 733  
60 0 0 4 67 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 748  3 out 1 733  6 indent 5 735  
61 0 0 7 69 758 0 0 22 /**
 * constructor
 */ 0 
62 0 0 15 69 758 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 759  
63 0 0 4 70 724 0 0 30 /**
 * boundary assignment
 */ 2 4 this 3 758  4 vmin 1 729  
64 0 0 4 71 724 0 0 30 /**
 * boundary assignment
 */ 2 4 this 3 758  4 vmax 1 729  
65 0 0 7 72 718 0 0 28 /**
 * boundary accessor
 */ 1 4 this 3 759  
66 0 0 7 73 718 0 0 28 /**
 * boundary accessor
 */ 1 4 this 3 759  
67 0 0 7 76 766 0 0 0 1 4 this 3 764  
68 0 0 4 77 724 0 0 0 2 4 this 3 767  1 c 1 768  
69 0 0 7 78 771 0 0 0 0 
70 0 0 7 81 766 0 0 0 1 4 this 3 772  
71 0 0 4 82 724 0 0 0 2 4 this 3 774  1 c 1 768  
72 0 0 7 83 771 0 0 0 0 
73 0 0 6 86 725 0 0 0 1 4 this 3 775  
74 0 0 6 87 725 0 0 0 1 4 this 3 775  
75 0 0 4 88 724 0 0 0 2 4 this 3 777  1 w 1 778  
76 0 0 4 89 724 0 0 0 2 4 this 3 777  1 w 1 778  
77 0 0 7 90 771 0 0 0 0 
78 0 0 6 93 725 0 0 0 1 4 this 3 779  
79 0 0 4 94 724 0 0 0 2 4 this 3 781  1 p 1 778  
80 0 0 7 95 771 0 0 0 0 
81 0 0 15 98 784 0 0 0 1 1 s 1 782  
82 0 0 7 99 786 0 0 59 // INLINE ColorInterpolationFunction* get_function() const; 1 4 this 3 782  
83 0 0 6 100 725 0 0 0 1 4 this 3 782  
84 0 0 6 101 725 0 0 0 1 4 this 3 782  
85 0 0 6 102 757 0 0 0 1 4 this 3 782  
86 0 0 6 103 735 0 0 0 1 4 this 3 782  
87 0 0 6 104 757 0 0 0 1 4 this 3 782  
88 0 0 4 105 724 0 0 0 2 4 this 3 784  8 function 1 787  
89 0 0 4 106 724 0 0 0 2 4 this 3 784  4 time 1 778  
90 0 0 4 107 724 0 0 0 2 4 this 3 784  4 time 1 778  
91 0 0 4 108 724 0 0 0 2 4 this 3 784  4 flag 1 788  
92 0 0 4 109 724 0 0 0 2 4 this 3 784  7 enabled 1 788  
93 0 0 7 111 789 0 0 0 0 
94 0 0 15 111 789 0 0 0 1 4 copy 1 790  
95 0 0 23 111 789 0 0 0 1 1 c 1 768  
96 0 0 6 112 735 0 0 0 5 4 this 3 789  10 time_begin 5 778  8 time_end 5 778  5 color 5 768  12 is_modulated 5 788  
97 0 0 6 113 735 0 0 0 6 4 this 3 789  10 time_begin 5 778  8 time_end 5 778  7 color_a 5 768  7 color_b 5 768  12 is_modulated 5 788  
98 0 0 6 114 735 0 0 0 8 4 this 3 789  10 time_begin 5 778  8 time_end 5 778  7 color_a 5 768  7 color_b 5 768  7 width_a 5 778  7 width_b 5 778  12 is_modulated 5 788  
99 0 0 6 115 735 0 0 0 7 4 this 3 789  10 time_begin 5 778  8 time_end 5 778  7 color_a 5 768  7 color_b 5 768  6 period 5 778  12 is_modulated 5 788  
100 0 0 4 116 724 0 0 0 2 4 this 3 789  1 c 1 768  
101 0 0 7 117 784 0 0 0 2 4 this 3 789  6 seg_id 1 792  
102 0 0 6 118 793 0 0 0 1 4 this 3 789  
103 0 0 4 119 724 0 0 0 2 4 this 3 789  6 seg_id 1 792  
104 0 0 4 120 724 0 0 0 1 4 this 3 789  
105 0 0 7 122 794 0 0 22 /**
 * constructor
 */ 0 
106 0 0 15 122 794 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 795  
107 0 0 4 123 724 0 0 0 2 4 this 3 794  1 r 1 725  
108 0 0 4 124 724 0 0 0 2 4 this 3 794  7 o_angle 1 725  
109 0 0 4 125 724 0 0 0 2 4 this 3 794  7 i_angle 1 725  
110 0 0 4 126 724 0 0 0 2 4 this 3 794  5 o_mag 1 725  
111 0 0 4 127 724 0 0 0 2 4 this 3 794  5 i_mag 1 725  
112 0 0 4 128 724 0 0 0 2 4 this 3 794  5 clerp 1 757  
113 0 0 6 129 725 0 0 0 1 4 this 3 795  
114 0 0 6 130 725 0 0 0 1 4 this 3 795  
115 0 0 6 131 725 0 0 0 1 4 this 3 795  
116 0 0 6 132 725 0 0 0 1 4 this 3 795  
117 0 0 6 133 725 0 0 0 1 4 this 3 795  
118 0 0 6 134 757 0 0 0 1 4 this 3 795  
119 0 0 7 136 799 0 0 0 2 2 am 5 686  9 geom_node 5 797  
120 0 0 15 136 799 0 0 0 1 4 copy 1 800  
121 0 0 4 137 724 0 0 0 2 4 this 3 799  4 node 1 797  
122 0 0 7 138 797 0 0 8 /**

 */ 1 4 this 3 799  
123 0 0 7 139 789 0 0 8 /**

 */ 1 4 this 3 800  
124 0 0 4 140 724 0 0 8 /**

 */ 2 4 this 3 799  15 animate_x_ratio 1 757  
125 0 0 4 141 724 0 0 8 /**

 */ 2 4 this 3 799  15 animate_y_ratio 1 757  
126 0 0 4 142 724 0 0 8 /**

 */ 2 4 this 3 799  15 animate_z_ratio 1 757  
127 0 0 4 143 724 0 0 8 /**

 */ 2 4 this 3 799  15 initial_x_scale 1 725  
128 0 0 4 144 724 0 0 8 /**

 */ 2 4 this 3 799  13 final_x_scale 1 725  
129 0 0 4 145 724 0 0 8 /**

 */ 2 4 this 3 799  15 initial_y_scale 1 725  
130 0 0 4 146 724 0 0 8 /**

 */ 2 4 this 3 799  13 final_y_scale 1 725  
131 0 0 4 147 724 0 0 8 /**

 */ 2 4 this 3 799  15 initial_z_scale 1 725  
132 0 0 4 148 724 0 0 8 /**

 */ 2 4 this 3 799  13 final_z_scale 1 725  
133 0 0 6 149 757 0 0 8 /**

 */ 1 4 this 3 800  
134 0 0 6 150 757 0 0 8 /**

 */ 1 4 this 3 800  
135 0 0 6 151 757 0 0 8 /**

 */ 1 4 this 3 800  
136 0 0 6 152 725 0 0 8 /**

 */ 1 4 this 3 800  
137 0 0 6 153 725 0 0 8 /**

 */ 1 4 this 3 800  
138 0 0 6 154 725 0 0 8 /**

 */ 1 4 this 3 800  
139 0 0 6 155 725 0 0 8 /**

 */ 1 4 this 3 800  
140 0 0 6 156 725 0 0 8 /**

 */ 1 4 this 3 800  
141 0 0 6 157 725 0 0 8 /**

 */ 1 4 this 3 800  
142 0 0 7 159 802 0 0 22 /**
 * constructor
 */ 0 
143 0 0 15 159 802 0 0 22 /**
 * constructor
 */ 1 4 copy 1 803  
144 0 0 4 160 724 0 0 30 /**
 * endpoint assignment
 */ 2 4 this 3 802  5 point 1 729  
145 0 0 4 161 724 0 0 30 /**
 * endpoint assignment
 */ 2 4 this 3 802  5 point 1 729  
146 0 0 7 162 718 0 0 28 /**
 * endpoint accessor
 */ 1 4 this 3 803  
147 0 0 7 163 718 0 0 28 /**
 * endpoint accessor
 */ 1 4 this 3 803  
148 0 0 7 165 805 0 0 0 0 
149 0 0 7 165 805 0 0 0 3 4 head 1 768  4 tail 1 768  10 alpha_mode 1 686  
150 0 0 15 165 805 0 0 0 1 4 copy 1 806  
151 0 0 4 166 724 0 0 8 /**

 */ 2 4 this 3 805  1 c 1 768  
152 0 0 4 167 724 0 0 8 /**

 */ 2 4 this 3 805  1 c 1 768  
153 0 0 6 168 768 0 0 8 /**

 */ 1 4 this 3 806  
154 0 0 6 169 768 0 0 8 /**

 */ 1 4 this 3 806  
155 0 0 4 170 724 0 0 19 /**
 * accessor
 */ 2 4 this 3 805  2 sf 1 725  
156 0 0 6 171 725 0 0 19 /**
 * accessor
 */ 1 4 this 3 806  
157 0 0 15 174 810 0 0 28 /**
 * Copy Constructor.
 */ 1 4 copy 1 808  
158 0 0 7 174 810 0 0 31 /**
 * Default Constructor.
 */ 1 9 pool_size 5 735  
159 0 0 4 175 724 0 0 16 // accessqueries 2 4 this 3 810  4 size 1 735  
160 0 0 4 176 724 0 0 8 /**

 */ 2 4 this 3 810  6 new_br 1 725  
161 0 0 4 177 724 0 0 8 /**

 */ 2 4 this 3 810  6 new_br 1 725  
162 0 0 4 178 724 0 0 8 /**

 */ 2 4 this 3 810  6 new_ls 1 735  
163 0 0 4 179 724 0 0 8 /**

 */ 2 4 this 3 810  6 new_ls 1 735  
164 0 0 4 180 724 0 0 8 /**

 */ 2 4 this 3 810  2 lv 1 757  
165 0 0 4 181 724 0 0 8 /**

 */ 2 4 this 3 810  3 sgo 1 757  
166 0 0 4 182 724 0 0 8 /**

 */ 2 4 this 3 810  2 sl 1 725  
167 0 0 4 183 724 0 0 8 /**

 */ 2 4 this 3 810  3 age 1 725  
168 0 0 4 184 724 0 0 8 /**

 */ 2 4 this 3 810  1 a 1 757  
169 0 0 4 185 724 0 0 8 /**

 */ 2 4 this 3 810  3 sod 1 757  
170 0 0 4 186 724 0 0 8 /**

 */ 2 4 this 3 810  4 node 1 797  
171 0 0 4 187 724 0 0 8 /**

 */ 2 4 this 3 810  4 node 1 811  
172 0 0 4 188 724 0 0 8 /**

 */ 2 4 this 3 810  3 tsf 1 757  
173 0 0 4 189 724 0 0 8 /**

 */ 2 4 this 3 810  4 node 1 811  
174 0 0 4 189 724 0 0 8 /**

 */ 2 4 this 3 810  4 node 1 797  
175 0 0 4 190 724 0 0 8 /**

 */ 2 4 this 3 810  1 r 1 753  
176 0 0 4 191 724 0 0 8 /**

 */ 2 4 this 3 810  1 e 1 717  
177 0 0 4 192 724 0 0 8 /**

 */ 2 4 this 3 810  1 f 1 742  
178 0 0 4 193 724 0 0 8 /**

 */ 2 4 this 3 810  1 z 1 725  
179 0 0 4 194 724 0 0 8 /**

 */ 1 4 this 3 810  
180 0 0 6 195 735 0 0 8 /**

 */ 1 4 this 3 808  
181 0 0 6 196 725 0 0 8 /**

 */ 1 4 this 3 808  
182 0 0 6 197 725 0 0 8 /**

 */ 1 4 this 3 808  
183 0 0 6 198 735 0 0 8 /**

 */ 1 4 this 3 808  
184 0 0 6 199 735 0 0 8 /**

 */ 1 4 this 3 808  
185 0 0 6 200 757 0 0 8 /**

 */ 1 4 this 3 808  
186 0 0 6 201 757 0 0 8 /**

 */ 1 4 this 3 808  
187 0 0 6 202 725 0 0 8 /**

 */ 1 4 this 3 808  
188 0 0 6 203 725 0 0 8 /**

 */ 1 4 this 3 808  
189 0 0 6 204 757 0 0 8 /**

 */ 1 4 this 3 808  
190 0 0 6 205 757 0 0 8 /**

 */ 1 4 this 3 808  
191 0 0 7 206 797 0 0 8 /**

 */ 1 4 this 3 808  
192 0 0 7 207 752 0 0 8 /**

 */ 1 4 this 3 808  
193 0 0 6 208 757 0 0 8 /**

 */ 1 4 this 3 808  
194 0 0 6 209 735 0 0 8 /**

 */ 1 4 this 3 808  
195 0 0 7 210 752 0 0 8 /**

 */ 1 4 this 3 808  
196 0 0 7 211 753 0 0 8 /**

 */ 1 4 this 3 808  
197 0 0 7 212 717 0 0 8 /**

 */ 1 4 this 3 808  
198 0 0 7 213 742 0 0 8 /**

 */ 1 4 this 3 808  
199 0 0 6 214 725 0 0 8 /**

 */ 1 4 this 3 808  
200 0 0 6 215 725 0 0 7 /**

*/ 1 4 this 3 808  
201 0 0 4 216 724 0 0 8 /**

 */ 2 4 this 3 810  2 ps 1 810  
202 0 0 4 217 724 0 0 8 /**

 */ 1 4 this 3 810  
203 0 0 4 218 724 0 0 134 /**
 * Populates an attached GeomNode structure with the particle geometry for
 * rendering.  This is a wrapper for accessability.
 */ 1 4 this 3 810  
204 0 0 4 219 724 0 0 93 /**
 * Forces the birth of a particle litter this frame by resetting
 * _tics_since_birth
 */ 1 4 this 3 810  
205 0 0 4 220 724 0 0 104 /**
 * Resets the system to its start state by resizing to 0, then resizing back
 * to current size.
 */ 1 4 this 3 810  
206 0 0 4 221 724 0 0 71 /**
 * Causes system to use birth rate set by set_soft_birth_rate()
 */ 2 4 this 3 810  2 br 5 725  
207 0 0 4 222 724 0 0 66 /**
 * Causes system to use birth rate set by set_birth_rate()
 */ 2 4 this 3 810  2 br 5 725  
208 0 0 4 222 724 0 0 262 /**
 * Causes system to use birth rate set by set_birth_rate(), with the system's
 * first birth being delayed by the value of first_birth_delay. Note that a
 * negative delay is perfectly valid, causing the first birth to happen
 * sooner rather than later.
 */ 3 4 this 3 810  2 br 1 725  17 first_birth_delay 1 725  
209 0 0 4 223 724 0 0 0 2 4 this 3 810  2 dt 1 725  
210 0 0 4 224 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 808  3 out 1 733  6 indent 5 735  
211 0 0 4 225 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 808  3 out 1 733  6 indent 5 735  
212 0 0 7 226 771 0 0 0 0 
213 0 0 7 228 813 0 0 22 /**
 * constructor
 */ 0 
214 0 0 15 228 813 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 814  
215 0 0 4 229 724 0 0 24 /**
 * point setting
 */ 2 4 this 3 813  1 p 1 729  
216 0 0 7 230 718 0 0 0 1 4 this 3 814  
217 0 0 7 232 816 0 0 30 /**
 * default constructor
 */ 0 
218 0 0 15 232 816 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 817  
219 0 0 7 235 819 0 0 30 /**
 * special constructor
 */ 6 2 ad 5 686  10 point_size 5 725  2 bt 5 705  2 bm 5 687  2 sc 5 768  2 ec 5 768  
220 0 0 15 235 819 0 0 27 /**
 * Copy constructor
 */ 1 4 copy 1 820  
221 0 0 4 236 724 0 0 8 /**

 */ 2 4 this 3 819  10 point_size 1 725  
222 0 0 4 237 724 0 0 8 /**

 */ 2 4 this 3 819  2 sc 1 768  
223 0 0 4 238 724 0 0 8 /**

 */ 2 4 this 3 819  2 ec 1 768  
224 0 0 4 239 724 0 0 8 /**

 */ 2 4 this 3 819  2 bt 1 705  
225 0 0 4 240 724 0 0 8 /**

 */ 2 4 this 3 819  2 bm 1 687  
226 0 0 6 241 725 0 0 8 /**

 */ 1 4 this 3 820  
227 0 0 6 242 768 0 0 8 /**

 */ 1 4 this 3 820  
228 0 0 6 243 768 0 0 8 /**

 */ 1 4 this 3 820  
229 0 0 6 244 705 0 0 8 /**

 */ 1 4 this 3 820  
230 0 0 6 245 687 0 0 8 /**

 */ 1 4 this 3 820  
231 0 0 7 247 822 0 0 22 /**
 * constructor
 */ 0 
232 0 0 15 247 822 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 823  
233 0 0 4 248 724 0 0 23 /**
 * boundary set
 */ 2 4 this 3 822  4 vmin 1 825  
234 0 0 4 249 724 0 0 23 /**
 * boundary set
 */ 2 4 this 3 822  4 vmax 1 825  
235 0 0 7 250 829 0 0 23 /**
 * boundary get
 */ 1 4 this 3 823  
236 0 0 7 251 829 0 0 23 /**
 * boundary get
 */ 1 4 this 3 823  
237 0 0 7 254 830 0 0 30 /**
 * Default Constructor
 */ 0 
238 0 0 7 254 830 0 0 0 6 6 center 1 768  4 edge 1 768  12 birth_radius 1 725  12 death_radius 1 725  10 life_scale 1 708  10 alpha_mode 1 686  
239 0 0 15 254 830 0 0 27 /**
 * Copy Constructor
 */ 1 4 copy 1 831  
240 0 0 4 255 724 0 0 8 /**

 */ 2 4 this 3 830  1 c 1 768  
241 0 0 4 256 724 0 0 8 /**

 */ 2 4 this 3 830  1 c 1 768  
242 0 0 4 257 724 0 0 8 /**

 */ 2 4 this 3 830  6 radius 1 725  
243 0 0 4 258 724 0 0 8 /**

 */ 2 4 this 3 830  6 radius 1 725  
244 0 0 4 259 724 0 0 8 /**

 */ 2 4 this 3 830  6 param0 0 708  
245 0 0 6 260 768 0 0 8 /**

 */ 1 4 this 3 831  
246 0 0 6 261 768 0 0 8 /**

 */ 1 4 this 3 831  
247 0 0 6 262 725 0 0 8 /**

 */ 1 4 this 3 831  
248 0 0 6 263 725 0 0 8 /**

 */ 1 4 this 3 831  
249 0 0 6 264 708 0 0 8 /**

 */ 1 4 this 3 831  
250 0 0 7 266 833 0 0 22 /**
 * constructor
 */ 0 
251 0 0 15 266 833 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 834  
252 0 0 4 267 724 0 0 0 2 4 this 3 833  1 r 1 725  
253 0 0 6 268 725 0 0 0 1 4 this 3 834  
254 0 0 7 270 836 0 0 22 /**
 * constructor
 */ 0 
255 0 0 15 270 836 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 837  
256 0 0 4 271 724 0 0 0 2 4 this 3 836  1 r 1 725  
257 0 0 6 272 725 0 0 0 1 4 this 3 837  
258 0 0 15 281 841 601 0 0 1 6 param0 0 839  
259 0 0 4 275 724 0 0 0 2 4 this 3 841  3 tex 1 793  
260 0 0 4 275 724 0 0 0 3 4 this 3 841  5 model 1 793  4 node 1 793  
261 0 0 6 276 712 0 0 0 1 4 this 3 839  
262 0 0 6 277 793 0 0 0 1 4 this 3 839  
263 0 0 6 278 793 0 0 0 1 4 this 3 839  
264 0 0 6 279 793 0 0 0 1 4 this 3 839  
265 0 0 6 280 735 0 0 0 1 4 this 3 839  
266 0 0 15 284 844 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 842  
267 0 0 7 284 844 0 0 22 /**
 * constructor
 */ 1 3 tex 5 845  
268 0 0 4 285 724 0 0 1118 /**
 * Sets the properties on this renderer from the geometry referenced by the
 * indicated NodePath.  This should be a reference to a GeomNode or a
 * SequenceNode; it extracts out the texture and UV range from the node.
 *
 * This will remove all previously added textures and animations.  It will
 * also resize the renderer to match this new geometry.
 *
 * If node_path refers to a GeomNode(or has one beneath it) the texture, its
 * size, and UV data will be extracted from that.
 *
 * If node_path references a SequenceNode(or has one beneath it) with multiple
 * GeomNodes beneath it, the size data will correspond only to the first
 * GeomNode found with a valid texture, while the texture and UV information
 * will be stored for each individual node.
 *
 * If size_from_texels is true, the particle size is based on the number of
 * texels in the source image; otherwise, it is based on the size of the first
 * polygon found in the node.
 *
 * model and node are the two items used to construct node_path.  If the
 * source type is important, use set_from_node(NodePath,string,string,bool)
 * instead.
 */ 3 4 this 3 844  9 node_path 1 811  16 size_from_texels 5 757  
269 0 0 4 285 724 0 0 269 /**
 * If the source type is important, use this one.
 *
 * model and node should lead to node_path like this: node_path =
 * loader.loadModel(model).find(node)
 *
 * This will remove all previously add textures and resize the renderer to
 * match the new geometry.
 */ 5 4 this 3 844  9 node_path 1 811  5 model 1 793  4 node 1 793  16 size_from_texels 5 757  
270 0 0 4 286 724 0 0 308 /**
 * This will allow the renderer to randomly choose from more than one texture
 * or sequence at particle birth.
 *
 * If resize is true, or if there are no textures currently on the renderer,
 * it will force the renderer to use the size information from this node from
 * now on.  (Default is false)
 */ 4 4 this 3 844  9 node_path 1 811  16 size_from_texels 5 757  6 resize 5 757  
271 0 0 4 286 724 0 0 468 /**
 * This will allow the renderer to randomly choose from more than one texture
 * or sequence at particle birth.
 *
 * If the source type is important, use this one.
 *
 * model and node should lead to node_path like this: node_path =
 * loader.loadModel(model).find(node)
 *
 * If resize is true, or if there are no textures currently on the renderer,
 * it will force the renderer to use the size information from this node from
 * now on.  (Default is false)
 */ 6 4 this 3 844  9 node_path 1 811  5 model 1 793  4 node 1 793  16 size_from_texels 5 757  6 resize 5 757  
272 0 0 4 287 724 0 0 292 /**
 * Sets the renderer up to render the entire texture image.  The scale of each
 * particle is based on the size of the texture in each dimension, modified by
 * texels_per_unit.
 *
 * Used to set the size of the particles.  Will clear all previously loaded
 * textures and animations.
 */ 3 4 this 3 844  3 tex 1 845  15 texels_per_unit 5 725  
273 0 0 4 288 724 0 0 198 /**
 * Adds texture to image pool, effectively creating a single frame animation
 * that can be selected at particle birth.  This should only be called after a
 * previous call to set_texture().
 */ 4 4 this 3 844  3 tex 1 845  15 texels_per_unit 5 725  6 resize 5 757  
274 0 0 4 289 724 0 0 62 /**
 * Removes an animation texture set from the renderer.
 */ 2 4 this 3 844  1 n 1 792  
275 0 0 4 290 724 0 0 213 /**
 * Sets the UV coordinate of the lower-left corner of all the sprites
 * generated by this renderer.  Normally this is (0, 0), but it might be set
 * to something else to use only a portion of the texture.
 */ 2 4 this 3 844  5 ll_uv 1 847  
276 0 0 4 290 724 0 0 213 /**
 * Sets the UV coordinate of the lower-left corner of all the sprites
 * generated by this renderer.  Normally this is (0, 0), but it might be set
 * to something else to use only a portion of the texture.
 */ 4 4 this 3 844  5 ll_uv 1 847  4 anim 1 792  5 frame 1 792  
277 0 0 4 291 724 0 0 214 /**
 * Sets the UV coordinate of the upper-right corner of all the sprites
 * generated by this renderer.  Normally this is (1, 1), but it might be set
 * to something else to use only a portion of the texture.
 */ 2 4 this 3 844  5 ur_uv 1 847  
278 0 0 4 291 724 0 0 214 /**
 * Sets the UV coordinate of the upper-right corner of all the sprites
 * generated by this renderer.  Normally this is (1, 1), but it might be set
 * to something else to use only a portion of the texture.
 */ 4 4 this 3 844  5 ur_uv 1 847  4 anim 1 792  5 frame 1 792  
279 0 0 4 292 724 0 0 57 /**
 * Sets the size of each particle in world units.
 */ 3 4 this 3 844  5 width 1 725  6 height 1 725  
280 0 0 4 293 724 0 0 8 /**

 */ 2 4 this 3 844  5 color 1 768  
281 0 0 4 294 724 0 0 8 /**

 */ 2 4 this 3 844  15 animate_x_ratio 1 757  
282 0 0 4 295 724 0 0 8 /**

 */ 2 4 this 3 844  15 animate_y_ratio 1 757  
283 0 0 4 296 724 0 0 8 /**

 */ 2 4 this 3 844  13 animate_theta 1 757  
284 0 0 4 297 724 0 0 8 /**

 */ 2 4 this 3 844  15 initial_x_scale 1 725  
285 0 0 4 298 724 0 0 8 /**

 */ 2 4 this 3 844  13 final_x_scale 1 725  
286 0 0 4 299 724 0 0 8 /**

 */ 2 4 this 3 844  15 initial_y_scale 1 725  
287 0 0 4 300 724 0 0 8 /**

 */ 2 4 this 3 844  13 final_y_scale 1 725  
288 0 0 4 301 724 0 0 8 /**

 */ 2 4 this 3 844  5 theta 1 725  
289 0 0 4 302 724 0 0 8 /**

 */ 2 4 this 3 844  2 bm 1 687  
290 0 0 4 303 724 0 0 8 /**

 */ 2 4 this 3 844  2 ad 1 757  
291 0 0 4 304 724 0 0 8 /**

 */ 2 4 this 3 844  2 an 1 757  
292 0 0 4 305 724 0 0 8 /**

 */ 2 4 this 3 844  1 r 1 725  
293 0 0 4 306 724 0 0 8 /**

 */ 2 4 this 3 844  1 i 1 735  
294 0 0 7 307 845 0 0 8 /**

 */ 1 4 this 3 842  
295 0 0 7 307 845 0 0 8 /**

 */ 3 4 this 3 842  4 anim 1 792  5 frame 1 792  
296 0 0 6 308 735 0 0 0 1 4 this 3 842  
297 0 0 7 309 841 601 0 0 2 4 this 3 842  1 n 1 792  
298 0 0 7 311 841 601 0 0 1 4 this 3 842  
299 0 0 7 312 789 0 0 8 /**

 */ 1 4 this 3 842  
300 0 0 7 313 851 0 0 79 /**
 * Returns the UV coordinate of the lower-left corner; see set_ll_uv().
 */ 1 4 this 3 842  
301 0 0 7 313 851 0 0 79 /**
 * Returns the UV coordinate of the lower-left corner; see set_ll_uv().
 */ 3 4 this 3 842  4 anim 1 792  5 frame 1 792  
302 0 0 7 314 851 0 0 79 /**
 * Returns the UV coordinate of the lower-left corner; see set_ur_uv().
 */ 1 4 this 3 842  
303 0 0 7 314 851 0 0 80 /**
 * Returns the UV coordinate of the upper-right corner; see set_ur_uv().
 */ 3 4 this 3 842  4 anim 1 792  5 frame 1 792  
304 0 0 6 315 725 0 0 61 /**
 * Returns the width of each particle in world units.
 */ 1 4 this 3 842  
305 0 0 6 316 725 0 0 62 /**
 * Returns the height of each particle in world units.
 */ 1 4 this 3 842  
306 0 0 7 317 766 0 0 8 /**

 */ 1 4 this 3 842  
307 0 0 6 318 757 0 0 8 /**

 */ 1 4 this 3 842  
308 0 0 6 319 757 0 0 8 /**

 */ 1 4 this 3 842  
309 0 0 6 320 757 0 0 8 /**

 */ 1 4 this 3 842  
310 0 0 6 321 725 0 0 8 /**

 */ 1 4 this 3 842  
311 0 0 6 322 725 0 0 8 /**

 */ 1 4 this 3 842  
312 0 0 6 323 725 0 0 8 /**

 */ 1 4 this 3 842  
313 0 0 6 324 725 0 0 8 /**

 */ 1 4 this 3 842  
314 0 0 6 325 725 0 0 8 /**

 */ 1 4 this 3 842  
315 0 0 6 326 687 0 0 8 /**

 */ 1 4 this 3 842  
316 0 0 6 327 757 0 0 8 /**

 */ 1 4 this 3 842  
317 0 0 6 328 757 0 0 8 /**

 */ 1 4 this 3 842  
318 0 0 6 329 725 0 0 8 /**

 */ 1 4 this 3 842  
319 0 0 6 330 735 0 0 8 /**

 */ 1 4 this 3 842  
320 0 0 7 332 852 0 0 22 /**
 * constructor
 */ 0 
321 0 0 15 332 852 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 853  
322 0 0 4 333 724 0 0 8 /**

 */ 2 4 this 3 852  1 r 1 725  
323 0 0 4 334 724 0 0 8 /**

 */ 2 4 this 3 852  6 spread 1 725  
324 0 0 6 335 725 0 0 8 /**

 */ 1 4 this 3 853  
325 0 0 6 336 725 0 0 8 /**

 */ 1 4 this 3 853  
326 0 0 7 338 855 0 0 22 /**
 * constructor
 */ 0 
327 0 0 15 338 855 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 856  
328 0 0 4 339 724 0 0 8 /**

 */ 2 4 this 3 855  5 angle 1 725  
329 0 0 4 340 724 0 0 8 /**

 */ 2 4 this 3 855  5 angle 1 725  
330 0 0 4 341 724 0 0 8 /**

 */ 2 4 this 3 855  6 spread 1 725  
331 0 0 4 342 724 0 0 8 /**

 */ 2 4 this 3 855  6 spread 1 725  
332 0 0 6 343 725 0 0 8 /**

 */ 1 4 this 3 856  
333 0 0 6 344 725 0 0 8 /**

 */ 1 4 this 3 856  
334 0 0 6 345 725 0 0 8 /**

 */ 1 4 this 3 856  
335 0 0 6 346 725 0 0 8 /**

 */ 1 4 this 3 856  
336 0 0 4 347 724 0 0 0 2 4 this 3 855  1 v 1 725  
337 0 0 6 348 725 0 0 8 /**

 */ 1 4 this 3 856  
338 0 0 4 349 724 0 0 0 2 4 this 3 855  6 spread 1 725  
339 0 0 6 350 725 0 0 0 1 4 this 3 856  
340 0 0 4 351 724 0 0 0 2 4 this 3 855  8 bEnabled 1 757  
341 0 0 6 352 757 0 0 0 1 4 this 3 856  
342 0 0 15 354 860 669 0 0 1 6 param0 0 858  
343 0 0 7 354 860 669 0 30 /**
 * default constructor
 */ 1 15 every_nth_frame 5 735  
344 0 0 4 356 724 0 0 0 2 4 this 3 860  15 every_nth_frame 1 735  
345 0 0 6 357 735 0 0 0 1 4 this 3 858  
346 0 0 4 358 724 0 0 0 2 4 this 3 860  2 ps 1 810  
347 0 0 4 359 724 0 0 49 /**
 * removes a ps from the maintenance list
 */ 2 4 this 3 860  2 ps 1 810  
348 0 0 4 360 724 0 0 0 1 4 this 3 860  
349 0 0 4 361 724 0 0 262 /**
 * does an update and render for each ps in the list.  this is probably the
 * one you want to use.  Rendering is the expensive operation, and particles
 * REALLY should at least be updated every frame, so nth_frame stepping
 * applies only to rendering.
 */ 2 4 this 3 860  2 dt 1 725  
350 0 0 4 361 724 0 0 185 /**
 * does an update and an optional render for a specific ps.  Since rendering
 * is the expensive operation, multiple updates could be applied before
 * calling the final render.
 */ 4 4 this 3 860  2 dt 1 725  2 ps 1 810  9 do_render 5 757  
351 0 0 4 362 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 858  3 out 1 733  
352 0 0 4 363 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 858  3 out 1 733  6 indent 5 735  
353 0 0 4 364 724 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 858  3 out 1 733  6 indent 5 735  
182
679 19 BaseParticleEmitter 0 75777 19 BaseParticleEmitter 19 BaseParticleEmitter 0 0 0 0 0 0 16 354 355 356 357 358 359 360 361 362 363 364 365 366 367 368 369 0 0 1 0 680 0 0 0 1 681 0

680 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

681 12 emissionType 0 794624 33 BaseParticleEmitter::emissionType 33 BaseParticleEmitter::emissionType 679 0 0 0 0 0 0 0 0 0 3 11 ET_EXPLICIT 32 BaseParticleEmitter::ET_EXPLICIT 62
// all particles are emitted in parallel along the same vector
0 10 ET_RADIATE 31 BaseParticleEmitter::ET_RADIATE 49
// all particles radiate away from a single point
1 9 ET_CUSTOM 30 BaseParticleEmitter::ET_CUSTOM 70
// particle launch vectors are dependent on particular derived emitter
2 0 0

682 11 RingEmitter 0 75777 11 RingEmitter 11 RingEmitter 0 0 0 1 370 0 0 8 371 372 373 374 375 376 377 378 0 0 1 0 679 0 0 0 0 75
/**
 * Describes a planar ring region in which particles are generated.
 */

683 10 ArcEmitter 0 75777 10 ArcEmitter 10 ArcEmitter 0 0 0 1 379 0 0 5 380 381 382 383 384 0 0 1 0 682 0 0 0 0 75
/**
 * Describes a planar ring region in which particles are generated.
 */

684 19 BaseParticleFactory 0 75777 19 BaseParticleFactory 19 BaseParticleFactory 0 0 0 0 0 0 16 385 386 387 388 389 390 391 392 393 394 395 396 397 398 399 400 0 0 1 0 680 0 0 0 0 57
/**
 * Pure Virtual base class for creating particles
 */

685 20 BaseParticleRenderer 0 75777 20 BaseParticleRenderer 20 BaseParticleRenderer 0 0 0 0 0 0 11 401 402 403 404 405 406 407 408 409 410 411 0 0 1 0 680 0 0 0 2 686 687 52
/**
 * Pure virtual particle renderer base class
 */

686 25 ParticleRendererAlphaMode 0 794624 47 BaseParticleRenderer::ParticleRendererAlphaMode 47 BaseParticleRenderer::ParticleRendererAlphaMode 685 0 0 0 0 0 0 0 0 0 6 13 PR_ALPHA_NONE 35 BaseParticleRenderer::PR_ALPHA_NONE 0
0 12 PR_ALPHA_OUT 34 BaseParticleRenderer::PR_ALPHA_OUT 0
1 11 PR_ALPHA_IN 33 BaseParticleRenderer::PR_ALPHA_IN 0
2 15 PR_ALPHA_IN_OUT 37 BaseParticleRenderer::PR_ALPHA_IN_OUT 0
3 13 PR_ALPHA_USER 35 BaseParticleRenderer::PR_ALPHA_USER 0
4 22 PR_NOT_INITIALIZED_YET 44 BaseParticleRenderer::PR_NOT_INITIALIZED_YET 0
5 0 0

687 27 ParticleRendererBlendMethod 0 794624 49 BaseParticleRenderer::ParticleRendererBlendMethod 49 BaseParticleRenderer::ParticleRendererBlendMethod 685 0 0 0 0 0 0 0 0 0 3 11 PP_NO_BLEND 33 BaseParticleRenderer::PP_NO_BLEND 0
0 15 PP_BLEND_LINEAR 37 BaseParticleRenderer::PP_BLEND_LINEAR 0
1 14 PP_BLEND_CUBIC 36 BaseParticleRenderer::PP_BLEND_CUBIC 0
2 0 0

688 10 BoxEmitter 0 75777 10 BoxEmitter 10 BoxEmitter 0 0 0 1 412 0 0 4 413 414 415 416 0 0 1 0 679 0 0 0 0 78
/**
 * Describes a voluminous box region in which particles are generated.
 */

689 34 ColorInterpolationFunctionConstant 0 141313 34 ColorInterpolationFunctionConstant 34 ColorInterpolationFunctionConstant 0 0 0 0 420 0 3 417 418 419 0 0 1 0 690 0 0 0 0 0

690 26 ColorInterpolationFunction 0 1050624 26 ColorInterpolationFunction 26 ColorInterpolationFunction 0 0 0 0 0 0 0 0 0 0 0 0 0

691 32 ColorInterpolationFunctionLinear 0 141313 32 ColorInterpolationFunctionLinear 32 ColorInterpolationFunctionLinear 0 0 0 0 424 0 3 421 422 423 0 0 1 0 689 0 0 0 0 0

692 34 ColorInterpolationFunctionStepwave 0 141313 34 ColorInterpolationFunctionStepwave 34 ColorInterpolationFunctionStepwave 0 0 0 0 430 0 5 425 426 427 428 429 0 0 1 0 691 0 0 0 0 0

693 34 ColorInterpolationFunctionSinusoid 0 141313 34 ColorInterpolationFunctionSinusoid 34 ColorInterpolationFunctionSinusoid 0 0 0 0 434 0 3 431 432 433 0 0 1 0 691 0 0 0 0 0

694 25 ColorInterpolationSegment 0 75777 25 ColorInterpolationSegment 25 ColorInterpolationSegment 0 0 0 1 435 0 0 11 436 437 438 439 440 441 442 443 444 445 446 0 0 1 0 680 0 0 0 0 0

695 25 ColorInterpolationManager 0 75777 25 ColorInterpolationManager 25 ColorInterpolationManager 0 0 0 1 447 0 0 9 448 449 450 451 452 453 454 455 456 0 0 1 0 680 0 0 0 0 0

696 11 DiscEmitter 0 75777 11 DiscEmitter 11 DiscEmitter 0 0 0 1 457 0 0 12 458 459 460 461 462 463 464 465 466 467 468 469 0 0 1 0 679 0 0 0 0 76
/**
 * Describes a planar disc region from which particles are generated
 */

697 20 GeomParticleRenderer 0 75777 20 GeomParticleRenderer 20 GeomParticleRenderer 0 0 0 1 470 0 0 21 471 472 473 474 475 476 477 478 479 480 481 482 483 484 485 486 487 488 489 490 491 0 0 1 0 685 0 0 0 0 0

698 11 LineEmitter 0 75777 11 LineEmitter 11 LineEmitter 0 0 0 1 492 0 0 4 493 494 495 496 0 0 1 0 679 0 0 0 0 70
/**
 * Describes a linear region in which particles are generated.
 */

699 20 LineParticleRenderer 0 75777 20 LineParticleRenderer 20 LineParticleRenderer 0 0 0 1 497 0 0 6 498 499 500 501 502 503 0 0 1 0 685 0 0 0 0 0

700 14 ParticleSystem 0 75777 14 ParticleSystem 14 ParticleSystem 0 0 0 1 504 0 0 52 505 506 507 508 509 510 511 512 513 514 515 516 517 518 519 520 521 522 523 524 525 526 527 528 529 530 531 532 533 534 535 536 537 538 539 540 541 542 543 544 545 546 547 548 549 550 551 552 553 554 555 556 0 0 1 0 701 0 0 0 0 50
/**
 * Contains and manages a particle system.
 */

701 8 Physical 0 2048 8 Physical 8 Physical 0 0 0 0 0 0 0 0 0 0 0 0 126
/**
 * Defines a set of physically modeled attributes.  If you want physics
 * applied to your class, derive it from this.
 */

702 12 PointEmitter 0 75777 12 PointEmitter 12 PointEmitter 0 0 0 1 557 0 0 2 558 559 0 0 1 0 679 0 0 0 0 75
/**
 * Describes a planar ring region in which particles are generated.
 */

703 20 PointParticleFactory 0 75777 20 PointParticleFactory 20 PointParticleFactory 0 0 0 1 560 0 0 0 0 0 1 0 684 0 0 0 0 0

704 21 PointParticleRenderer 0 75777 21 PointParticleRenderer 21 PointParticleRenderer 0 0 0 1 561 0 0 10 562 563 564 565 566 567 568 569 570 571 0 0 1 0 685 0 0 0 1 705 0

705 22 PointParticleBlendType 0 794624 45 PointParticleRenderer::PointParticleBlendType 45 PointParticleRenderer::PointParticleBlendType 704 0 0 0 0 0 0 0 0 0 3 12 PP_ONE_COLOR 35 PointParticleRenderer::PP_ONE_COLOR 0
0 13 PP_BLEND_LIFE 36 PointParticleRenderer::PP_BLEND_LIFE 0
1 12 PP_BLEND_VEL 35 PointParticleRenderer::PP_BLEND_VEL 0
2 0 0

706 16 RectangleEmitter 0 75777 16 RectangleEmitter 16 RectangleEmitter 0 0 0 1 572 0 0 4 573 574 575 576 0 0 1 0 679 0 0 0 0 77
/**
 * Describes a planar square region in which particles are generated.
 */

707 23 SparkleParticleRenderer 0 75777 23 SparkleParticleRenderer 23 SparkleParticleRenderer 0 0 0 1 577 0 0 10 578 579 580 581 582 583 584 585 586 587 0 0 1 0 685 0 0 0 1 708 33
/**
 * pretty sparkly things.
 */

708 24 SparkleParticleLifeScale 0 794624 49 SparkleParticleRenderer::SparkleParticleLifeScale 49 SparkleParticleRenderer::SparkleParticleLifeScale 707 0 0 0 0 0 0 0 0 0 2 11 SP_NO_SCALE 36 SparkleParticleRenderer::SP_NO_SCALE 0
0 8 SP_SCALE 33 SparkleParticleRenderer::SP_SCALE 0
1 0 0

709 20 SphereSurfaceEmitter 0 75777 20 SphereSurfaceEmitter 20 SphereSurfaceEmitter 0 0 0 1 588 0 0 2 589 590 0 0 1 0 679 0 0 0 0 69
/**
 * Describes a curved space in which particles are generated.
 */

710 19 SphereVolumeEmitter 0 75777 19 SphereVolumeEmitter 19 SphereVolumeEmitter 0 0 0 1 591 0 0 2 592 593 0 0 1 0 679 0 0 0 0 84
/**
 * Describes a voluminous spherical region in which particles are generated.
 */

711 10 SpriteAnim 0 141313 10 SpriteAnim 10 SpriteAnim 0 0 0 1 600 601 0 6 594 595 596 597 598 599 0 0 1 0 680 0 0 0 1 712 130
/**
 * Helper class used by SpriteParticleRenderer to keep track of its textures
 * and their respective UVs and source types.
 */

712 10 SourceType 0 794624 22 SpriteAnim::SourceType 22 SpriteAnim::SourceType 711 0 0 0 0 0 0 0 0 0 2 10 ST_texture 22 SpriteAnim::ST_texture 0
0 12 ST_from_node 24 SpriteAnim::ST_from_node 0
1 0 0

713 22 SpriteParticleRenderer 0 75777 22 SpriteParticleRenderer 22 SpriteParticleRenderer 0 0 0 1 602 0 0 45 603 604 605 606 607 608 609 610 611 612 613 614 615 616 617 618 619 620 621 622 623 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 646 647 1 861 0 1 0 685 0 0 0 0 73
/**
 * Renders a particle system with high-speed nasty trick sprites.
 */

714 18 TangentRingEmitter 0 75777 18 TangentRingEmitter 18 TangentRingEmitter 0 0 0 1 648 0 0 4 649 650 651 652 0 0 1 0 679 0 0 0 0 132
/**
 * Describes a planar ring region in which tangent particles are generated,
 * and particles fly off tangential to the ring.
 */

715 20 ZSpinParticleFactory 0 75777 20 ZSpinParticleFactory 20 ZSpinParticleFactory 0 0 0 1 653 0 0 14 654 655 656 657 658 659 660 661 662 663 664 665 666 667 0 0 1 0 684 0 0 0 0 10
/**
 *
 */

716 21 ParticleSystemManager 0 26625 21 ParticleSystemManager 21 ParticleSystemManager 0 0 0 1 668 669 0 9 670 671 672 673 674 675 676 677 678 0 0 0 0 0 185
/**
 * Manages a set of individual ParticleSystem objects, so that each individual
 * one doesn't have to be updated and rendered every frame See Also :
 * particleSystemManager.cxx
 */

717 21 BaseParticleEmitter * 0 8576 21 BaseParticleEmitter * 21 BaseParticleEmitter * 0 0 679 0 0 0 0 0 0 0 0 0 0

718 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 719 0 0 0 0 0 0 0 0 0 0

719 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 720 0 0 0 0 0 0 0 0 0 0

720 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

721 10 LVector3 * 0 8576 10 LVector3 * 10 LVector3 * 0 0 722 0 0 0 0 0 0 0 0 0 0

722 8 LVector3 0 2105344 8 LVector3 8 LVector3 0 0 723 0 0 0 0 0 0 0 0 0 0

723 9 LVector3f 0 2048 9 LVector3f 9 LVector3f 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

724 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

725 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 726 0 0 0 0 0 0 0 0 0 0

726 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

727 16 LVector3 const * 0 8576 16 LVector3 const * 16 LVector3 const * 0 0 728 0 0 0 0 0 0 0 0 0 0

728 14 LVector3 const 0 8832 14 LVector3 const 14 LVector3 const 0 0 722 0 0 0 0 0 0 0 0 0 0

729 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 730 0 0 0 0 0 0 0 0 0 0

730 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 719 0 0 0 0 0 0 0 0 0 0

731 27 BaseParticleEmitter const * 0 8576 27 BaseParticleEmitter const * 27 BaseParticleEmitter const * 0 0 732 0 0 0 0 0 0 0 0 0 0

732 25 BaseParticleEmitter const 0 8832 25 BaseParticleEmitter const 25 BaseParticleEmitter const 0 0 679 0 0 0 0 0 0 0 0 0 0

733 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 734 0 0 0 0 0 0 0 0 0 0

734 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

735 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

736 13 RingEmitter * 0 8576 13 RingEmitter * 13 RingEmitter * 0 0 682 0 0 0 0 0 0 0 0 0 0

737 19 RingEmitter const * 0 8576 19 RingEmitter const * 19 RingEmitter const * 0 0 738 0 0 0 0 0 0 0 0 0 0

738 17 RingEmitter const 0 8832 17 RingEmitter const 17 RingEmitter const 0 0 682 0 0 0 0 0 0 0 0 0 0

739 12 ArcEmitter * 0 8576 12 ArcEmitter * 12 ArcEmitter * 0 0 683 0 0 0 0 0 0 0 0 0 0

740 18 ArcEmitter const * 0 8576 18 ArcEmitter const * 18 ArcEmitter const * 0 0 741 0 0 0 0 0 0 0 0 0 0

741 16 ArcEmitter const 0 8832 16 ArcEmitter const 16 ArcEmitter const 0 0 683 0 0 0 0 0 0 0 0 0 0

742 21 BaseParticleFactory * 0 8576 21 BaseParticleFactory * 21 BaseParticleFactory * 0 0 684 0 0 0 0 0 0 0 0 0 0

743 27 BaseParticleFactory const * 0 8576 27 BaseParticleFactory const * 27 BaseParticleFactory const * 0 0 744 0 0 0 0 0 0 0 0 0 0

744 25 BaseParticleFactory const 0 8832 25 BaseParticleFactory const 25 BaseParticleFactory const 0 0 684 0 0 0 0 0 0 0 0 0 0

745 14 BaseParticle * 0 8576 14 BaseParticle * 14 BaseParticle * 0 0 746 0 0 0 0 0 0 0 0 0 0

746 12 BaseParticle 0 1050624 12 BaseParticle 12 BaseParticle 0 0 0 0 0 0 0 0 0 0 0 0 76
/**
 * An individual, physically-modelable particle abstract base class.
 */

747 8 GeomNode 0 2048 8 GeomNode 8 GeomNode 0 0 0 0 0 0 0 0 0 0 0 0 208
/**
 * A node that holds Geom objects, renderable pieces of geometry.  This is the
 * primary kind of leaf node in the scene graph; almost all visible objects
 * will be contained in a GeomNode somewhere.
 */

748 28 BaseParticleRenderer const * 0 8576 28 BaseParticleRenderer const * 28 BaseParticleRenderer const * 0 0 749 0 0 0 0 0 0 0 0 0 0

749 26 BaseParticleRenderer const 0 8832 26 BaseParticleRenderer const 26 BaseParticleRenderer const 0 0 685 0 0 0 0 0 0 0 0 0 0

750 10 GeomNode * 0 8576 10 GeomNode * 10 GeomNode * 0 0 747 0 0 0 0 0 0 0 0 0 0

751 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

752 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 751 0 0 0 0 0 0 0 0 0 0

753 22 BaseParticleRenderer * 0 8576 22 BaseParticleRenderer * 22 BaseParticleRenderer * 0 0 685 0 0 0 0 0 0 0 0 0 0

754 4 Mode 0 794624 22 ColorBlendAttrib::Mode 22 ColorBlendAttrib::Mode 755 0 0 0 0 0 0 0 0 0 6 6 M_none 24 ColorBlendAttrib::M_none 23
// Blending is disabled
0 5 M_add 23 ColorBlendAttrib::M_add 41
// incoming color * A + fbuffer color * B
1 10 M_subtract 28 ColorBlendAttrib::M_subtract 41
// incoming color * A - fbuffer color * B
2 14 M_inv_subtract 32 ColorBlendAttrib::M_inv_subtract 41
// fbuffer color * B - incoming color * A
3 5 M_min 23 ColorBlendAttrib::M_min 37
// min(incoming color, fbuffer color)
4 5 M_max 23 ColorBlendAttrib::M_max 37
// max(incoming color, fbuffer color)
5 0 0

755 16 ColorBlendAttrib 0 2048 16 ColorBlendAttrib 16 ColorBlendAttrib 0 0 0 0 0 0 0 0 0 0 0 0 159
/**
 * This specifies how colors are blended into the frame buffer, for special
 * effects.  This overrides transparency if transparency is also specified.
 */

756 7 Operand 0 794624 25 ColorBlendAttrib::Operand 25 ColorBlendAttrib::Operand 755 0 0 0 0 0 0 0 0 0 23 6 O_zero 24 ColorBlendAttrib::O_zero 0
0 5 O_one 23 ColorBlendAttrib::O_one 0
1 16 O_incoming_color 34 ColorBlendAttrib::O_incoming_color 0
2 26 O_one_minus_incoming_color 44 ColorBlendAttrib::O_one_minus_incoming_color 0
3 15 O_fbuffer_color 33 ColorBlendAttrib::O_fbuffer_color 0
4 25 O_one_minus_fbuffer_color 43 ColorBlendAttrib::O_one_minus_fbuffer_color 0
5 16 O_incoming_alpha 34 ColorBlendAttrib::O_incoming_alpha 0
6 26 O_one_minus_incoming_alpha 44 ColorBlendAttrib::O_one_minus_incoming_alpha 0
7 15 O_fbuffer_alpha 33 ColorBlendAttrib::O_fbuffer_alpha 0
8 25 O_one_minus_fbuffer_alpha 43 ColorBlendAttrib::O_one_minus_fbuffer_alpha 0
9 16 O_constant_color 34 ColorBlendAttrib::O_constant_color 0
10 26 O_one_minus_constant_color 44 ColorBlendAttrib::O_one_minus_constant_color 0
11 16 O_constant_alpha 34 ColorBlendAttrib::O_constant_alpha 0
12 26 O_one_minus_constant_alpha 44 ColorBlendAttrib::O_one_minus_constant_alpha 0
13 25 O_incoming_color_saturate 43 ColorBlendAttrib::O_incoming_color_saturate 27
// valid only for operand a
14 17 O_incoming1_color 35 ColorBlendAttrib::O_incoming1_color 135
// The following are used for dual-source blending, where the fragment
// shader outputs a second color that will be used for blending.
15 27 O_one_minus_incoming1_color 45 ColorBlendAttrib::O_one_minus_incoming1_color 0
16 17 O_incoming1_alpha 35 ColorBlendAttrib::O_incoming1_alpha 0
17 27 O_one_minus_incoming1_alpha 45 ColorBlendAttrib::O_one_minus_incoming1_alpha 0
18 13 O_color_scale 31 ColorBlendAttrib::O_color_scale 52
// These modes are being considered for deprecation.
19 23 O_one_minus_color_scale 41 ColorBlendAttrib::O_one_minus_color_scale 0
20 13 O_alpha_scale 31 ColorBlendAttrib::O_alpha_scale 0
21 23 O_one_minus_alpha_scale 41 ColorBlendAttrib::O_one_minus_alpha_scale 0
22 0 0

757 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

758 12 BoxEmitter * 0 8576 12 BoxEmitter * 12 BoxEmitter * 0 0 688 0 0 0 0 0 0 0 0 0 0

759 18 BoxEmitter const * 0 8576 18 BoxEmitter const * 18 BoxEmitter const * 0 0 760 0 0 0 0 0 0 0 0 0 0

760 16 BoxEmitter const 0 8832 16 BoxEmitter const 16 BoxEmitter const 0 0 688 0 0 0 0 0 0 0 0 0 0

761 6 LColor 0 2105344 6 LColor 6 LColor 0 0 762 0 0 0 0 0 0 0 0 0 0

762 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 763 0 0 0 0 0 0 0 0 0 0

763 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

764 42 ColorInterpolationFunctionConstant const * 0 8576 42 ColorInterpolationFunctionConstant const * 42 ColorInterpolationFunctionConstant const * 0 0 765 0 0 0 0 0 0 0 0 0 0

765 40 ColorInterpolationFunctionConstant const 0 8832 40 ColorInterpolationFunctionConstant const 40 ColorInterpolationFunctionConstant const 0 0 689 0 0 0 0 0 0 0 0 0 0

766 8 LColor * 0 8576 8 LColor * 8 LColor * 0 0 761 0 0 0 0 0 0 0 0 0 0

767 36 ColorInterpolationFunctionConstant * 0 8576 36 ColorInterpolationFunctionConstant * 36 ColorInterpolationFunctionConstant * 0 0 689 0 0 0 0 0 0 0 0 0 0

768 14 LColor const * 0 8576 14 LColor const * 14 LColor const * 0 0 769 0 0 0 0 0 0 0 0 0 0

769 12 LColor const 0 8832 12 LColor const 12 LColor const 0 0 761 0 0 0 0 0 0 0 0 0 0

770 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

771 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 770 0 0 0 0 0 0 0 0 0 0

772 40 ColorInterpolationFunctionLinear const * 0 8576 40 ColorInterpolationFunctionLinear const * 40 ColorInterpolationFunctionLinear const * 0 0 773 0 0 0 0 0 0 0 0 0 0

773 38 ColorInterpolationFunctionLinear const 0 8832 38 ColorInterpolationFunctionLinear const 38 ColorInterpolationFunctionLinear const 0 0 691 0 0 0 0 0 0 0 0 0 0

774 34 ColorInterpolationFunctionLinear * 0 8576 34 ColorInterpolationFunctionLinear * 34 ColorInterpolationFunctionLinear * 0 0 691 0 0 0 0 0 0 0 0 0 0

775 42 ColorInterpolationFunctionStepwave const * 0 8576 42 ColorInterpolationFunctionStepwave const * 42 ColorInterpolationFunctionStepwave const * 0 0 776 0 0 0 0 0 0 0 0 0 0

776 40 ColorInterpolationFunctionStepwave const 0 8832 40 ColorInterpolationFunctionStepwave const 40 ColorInterpolationFunctionStepwave const 0 0 692 0 0 0 0 0 0 0 0 0 0

777 36 ColorInterpolationFunctionStepwave * 0 8576 36 ColorInterpolationFunctionStepwave * 36 ColorInterpolationFunctionStepwave * 0 0 692 0 0 0 0 0 0 0 0 0 0

778 17 PN_stdfloat const 0 8832 17 PN_stdfloat const 17 PN_stdfloat const 0 0 725 0 0 0 0 0 0 0 0 0 0

779 42 ColorInterpolationFunctionSinusoid const * 0 8576 42 ColorInterpolationFunctionSinusoid const * 42 ColorInterpolationFunctionSinusoid const * 0 0 780 0 0 0 0 0 0 0 0 0 0

780 40 ColorInterpolationFunctionSinusoid const 0 8832 40 ColorInterpolationFunctionSinusoid const 40 ColorInterpolationFunctionSinusoid const 0 0 693 0 0 0 0 0 0 0 0 0 0

781 36 ColorInterpolationFunctionSinusoid * 0 8576 36 ColorInterpolationFunctionSinusoid * 36 ColorInterpolationFunctionSinusoid * 0 0 693 0 0 0 0 0 0 0 0 0 0

782 33 ColorInterpolationSegment const * 0 8576 33 ColorInterpolationSegment const * 33 ColorInterpolationSegment const * 0 0 783 0 0 0 0 0 0 0 0 0 0

783 31 ColorInterpolationSegment const 0 8832 31 ColorInterpolationSegment const 31 ColorInterpolationSegment const 0 0 694 0 0 0 0 0 0 0 0 0 0

784 27 ColorInterpolationSegment * 0 8576 27 ColorInterpolationSegment * 27 ColorInterpolationSegment * 0 0 694 0 0 0 0 0 0 0 0 0 0

785 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

786 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 785 0 0 0 0 0 0 0 0 0 0

787 28 ColorInterpolationFunction * 0 8576 28 ColorInterpolationFunction * 28 ColorInterpolationFunction * 0 0 690 0 0 0 0 0 0 0 0 0 0

788 10 bool const 0 8832 10 bool const 10 bool const 0 0 757 0 0 0 0 0 0 0 0 0 0

789 27 ColorInterpolationManager * 0 8576 27 ColorInterpolationManager * 27 ColorInterpolationManager * 0 0 695 0 0 0 0 0 0 0 0 0 0

790 33 ColorInterpolationManager const * 0 8576 33 ColorInterpolationManager const * 33 ColorInterpolationManager const * 0 0 791 0 0 0 0 0 0 0 0 0 0

791 31 ColorInterpolationManager const 0 8832 31 ColorInterpolationManager const 31 ColorInterpolationManager const 0 0 695 0 0 0 0 0 0 0 0 0 0

792 9 int const 0 8832 9 int const 9 int const 0 0 735 0 0 0 0 0 0 0 0 0 0

793 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

794 13 DiscEmitter * 0 8576 13 DiscEmitter * 13 DiscEmitter * 0 0 696 0 0 0 0 0 0 0 0 0 0

795 19 DiscEmitter const * 0 8576 19 DiscEmitter const * 19 DiscEmitter const * 0 0 796 0 0 0 0 0 0 0 0 0 0

796 17 DiscEmitter const 0 8832 17 DiscEmitter const 17 DiscEmitter const 0 0 696 0 0 0 0 0 0 0 0 0 0

797 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 798 0 0 0 0 0 0 0 0 0 0

798 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

799 22 GeomParticleRenderer * 0 8576 22 GeomParticleRenderer * 22 GeomParticleRenderer * 0 0 697 0 0 0 0 0 0 0 0 0 0

800 28 GeomParticleRenderer const * 0 8576 28 GeomParticleRenderer const * 28 GeomParticleRenderer const * 0 0 801 0 0 0 0 0 0 0 0 0 0

801 26 GeomParticleRenderer const 0 8832 26 GeomParticleRenderer const 26 GeomParticleRenderer const 0 0 697 0 0 0 0 0 0 0 0 0 0

802 13 LineEmitter * 0 8576 13 LineEmitter * 13 LineEmitter * 0 0 698 0 0 0 0 0 0 0 0 0 0

803 19 LineEmitter const * 0 8576 19 LineEmitter const * 19 LineEmitter const * 0 0 804 0 0 0 0 0 0 0 0 0 0

804 17 LineEmitter const 0 8832 17 LineEmitter const 17 LineEmitter const 0 0 698 0 0 0 0 0 0 0 0 0 0

805 22 LineParticleRenderer * 0 8576 22 LineParticleRenderer * 22 LineParticleRenderer * 0 0 699 0 0 0 0 0 0 0 0 0 0

806 28 LineParticleRenderer const * 0 8576 28 LineParticleRenderer const * 28 LineParticleRenderer const * 0 0 807 0 0 0 0 0 0 0 0 0 0

807 26 LineParticleRenderer const 0 8832 26 LineParticleRenderer const 26 LineParticleRenderer const 0 0 699 0 0 0 0 0 0 0 0 0 0

808 22 ParticleSystem const * 0 8576 22 ParticleSystem const * 22 ParticleSystem const * 0 0 809 0 0 0 0 0 0 0 0 0 0

809 20 ParticleSystem const 0 8832 20 ParticleSystem const 20 ParticleSystem const 0 0 700 0 0 0 0 0 0 0 0 0 0

810 16 ParticleSystem * 0 8576 16 ParticleSystem * 16 ParticleSystem * 0 0 700 0 0 0 0 0 0 0 0 0 0

811 16 NodePath const * 0 8576 16 NodePath const * 16 NodePath const * 0 0 812 0 0 0 0 0 0 0 0 0 0

812 14 NodePath const 0 8832 14 NodePath const 14 NodePath const 0 0 751 0 0 0 0 0 0 0 0 0 0

813 14 PointEmitter * 0 8576 14 PointEmitter * 14 PointEmitter * 0 0 702 0 0 0 0 0 0 0 0 0 0

814 20 PointEmitter const * 0 8576 20 PointEmitter const * 20 PointEmitter const * 0 0 815 0 0 0 0 0 0 0 0 0 0

815 18 PointEmitter const 0 8832 18 PointEmitter const 18 PointEmitter const 0 0 702 0 0 0 0 0 0 0 0 0 0

816 22 PointParticleFactory * 0 8576 22 PointParticleFactory * 22 PointParticleFactory * 0 0 703 0 0 0 0 0 0 0 0 0 0

817 28 PointParticleFactory const * 0 8576 28 PointParticleFactory const * 28 PointParticleFactory const * 0 0 818 0 0 0 0 0 0 0 0 0 0

818 26 PointParticleFactory const 0 8832 26 PointParticleFactory const 26 PointParticleFactory const 0 0 703 0 0 0 0 0 0 0 0 0 0

819 23 PointParticleRenderer * 0 8576 23 PointParticleRenderer * 23 PointParticleRenderer * 0 0 704 0 0 0 0 0 0 0 0 0 0

820 29 PointParticleRenderer const * 0 8576 29 PointParticleRenderer const * 29 PointParticleRenderer const * 0 0 821 0 0 0 0 0 0 0 0 0 0

821 27 PointParticleRenderer const 0 8832 27 PointParticleRenderer const 27 PointParticleRenderer const 0 0 704 0 0 0 0 0 0 0 0 0 0

822 18 RectangleEmitter * 0 8576 18 RectangleEmitter * 18 RectangleEmitter * 0 0 706 0 0 0 0 0 0 0 0 0 0

823 24 RectangleEmitter const * 0 8576 24 RectangleEmitter const * 24 RectangleEmitter const * 0 0 824 0 0 0 0 0 0 0 0 0 0

824 22 RectangleEmitter const 0 8832 22 RectangleEmitter const 22 RectangleEmitter const 0 0 706 0 0 0 0 0 0 0 0 0 0

825 15 LPoint2 const * 0 8576 15 LPoint2 const * 15 LPoint2 const * 0 0 826 0 0 0 0 0 0 0 0 0 0

826 13 LPoint2 const 0 8832 13 LPoint2 const 13 LPoint2 const 0 0 827 0 0 0 0 0 0 0 0 0 0

827 7 LPoint2 0 2105344 7 LPoint2 7 LPoint2 0 0 828 0 0 0 0 0 0 0 0 0 0

828 8 LPoint2f 0 2048 8 LPoint2f 8 LPoint2f 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

829 9 LPoint2 * 0 8576 9 LPoint2 * 9 LPoint2 * 0 0 827 0 0 0 0 0 0 0 0 0 0

830 25 SparkleParticleRenderer * 0 8576 25 SparkleParticleRenderer * 25 SparkleParticleRenderer * 0 0 707 0 0 0 0 0 0 0 0 0 0

831 31 SparkleParticleRenderer const * 0 8576 31 SparkleParticleRenderer const * 31 SparkleParticleRenderer const * 0 0 832 0 0 0 0 0 0 0 0 0 0

832 29 SparkleParticleRenderer const 0 8832 29 SparkleParticleRenderer const 29 SparkleParticleRenderer const 0 0 707 0 0 0 0 0 0 0 0 0 0

833 22 SphereSurfaceEmitter * 0 8576 22 SphereSurfaceEmitter * 22 SphereSurfaceEmitter * 0 0 709 0 0 0 0 0 0 0 0 0 0

834 28 SphereSurfaceEmitter const * 0 8576 28 SphereSurfaceEmitter const * 28 SphereSurfaceEmitter const * 0 0 835 0 0 0 0 0 0 0 0 0 0

835 26 SphereSurfaceEmitter const 0 8832 26 SphereSurfaceEmitter const 26 SphereSurfaceEmitter const 0 0 709 0 0 0 0 0 0 0 0 0 0

836 21 SphereVolumeEmitter * 0 8576 21 SphereVolumeEmitter * 21 SphereVolumeEmitter * 0 0 710 0 0 0 0 0 0 0 0 0 0

837 27 SphereVolumeEmitter const * 0 8576 27 SphereVolumeEmitter const * 27 SphereVolumeEmitter const * 0 0 838 0 0 0 0 0 0 0 0 0 0

838 25 SphereVolumeEmitter const 0 8832 25 SphereVolumeEmitter const 25 SphereVolumeEmitter const 0 0 710 0 0 0 0 0 0 0 0 0 0

839 18 SpriteAnim const * 0 8576 18 SpriteAnim const * 18 SpriteAnim const * 0 0 840 0 0 0 0 0 0 0 0 0 0

840 16 SpriteAnim const 0 8832 16 SpriteAnim const 16 SpriteAnim const 0 0 711 0 0 0 0 0 0 0 0 0 0

841 12 SpriteAnim * 0 8576 12 SpriteAnim * 12 SpriteAnim * 0 0 711 0 0 0 0 0 0 0 0 0 0

842 30 SpriteParticleRenderer const * 0 8576 30 SpriteParticleRenderer const * 30 SpriteParticleRenderer const * 0 0 843 0 0 0 0 0 0 0 0 0 0

843 28 SpriteParticleRenderer const 0 8832 28 SpriteParticleRenderer const 28 SpriteParticleRenderer const 0 0 713 0 0 0 0 0 0 0 0 0 0

844 24 SpriteParticleRenderer * 0 8576 24 SpriteParticleRenderer * 24 SpriteParticleRenderer * 0 0 713 0 0 0 0 0 0 0 0 0 0

845 9 Texture * 0 8576 9 Texture * 9 Texture * 0 0 846 0 0 0 0 0 0 0 0 0 0

846 7 Texture 0 2048 7 Texture 7 Texture 0 0 0 0 0 0 0 0 0 0 0 0 688
/**
 * Represents a texture object, which is typically a single 2-d image but may
 * also represent a 1-d or 3-d texture image, or the six 2-d faces of a cube
 * map texture.
 *
 * A texture's image data might be stored in system RAM (see get_ram_image())
 * or its image may be represented in texture memory on one or more
 * GraphicsStateGuardians (see prepare()), or both.  The typical usage pattern
 * is that a texture is loaded from an image file on disk, which copies its
 * image data into system RAM; then the first time the texture is rendered its
 * image data is copied to texture memory (actually, to the graphics API), and
 * the system RAM image is automatically freed.
 */

847 17 LTexCoord const * 0 8576 17 LTexCoord const * 17 LTexCoord const * 0 0 848 0 0 0 0 0 0 0 0 0 0

848 15 LTexCoord const 0 8832 15 LTexCoord const 15 LTexCoord const 0 0 849 0 0 0 0 0 0 0 0 0 0

849 9 LTexCoord 0 2105344 9 LTexCoord 9 LTexCoord 0 0 850 0 0 0 0 0 0 0 0 0 0

850 10 LTexCoordf 0 2105344 10 LTexCoordf 10 LTexCoordf 0 0 828 0 0 0 0 0 0 0 0 0 0

851 11 LTexCoord * 0 8576 11 LTexCoord * 11 LTexCoord * 0 0 849 0 0 0 0 0 0 0 0 0 0

852 20 TangentRingEmitter * 0 8576 20 TangentRingEmitter * 20 TangentRingEmitter * 0 0 714 0 0 0 0 0 0 0 0 0 0

853 26 TangentRingEmitter const * 0 8576 26 TangentRingEmitter const * 26 TangentRingEmitter const * 0 0 854 0 0 0 0 0 0 0 0 0 0

854 24 TangentRingEmitter const 0 8832 24 TangentRingEmitter const 24 TangentRingEmitter const 0 0 714 0 0 0 0 0 0 0 0 0 0

855 22 ZSpinParticleFactory * 0 8576 22 ZSpinParticleFactory * 22 ZSpinParticleFactory * 0 0 715 0 0 0 0 0 0 0 0 0 0

856 28 ZSpinParticleFactory const * 0 8576 28 ZSpinParticleFactory const * 28 ZSpinParticleFactory const * 0 0 857 0 0 0 0 0 0 0 0 0 0

857 26 ZSpinParticleFactory const 0 8832 26 ZSpinParticleFactory const 26 ZSpinParticleFactory const 0 0 715 0 0 0 0 0 0 0 0 0 0

858 29 ParticleSystemManager const * 0 8576 29 ParticleSystemManager const * 29 ParticleSystemManager const * 0 0 859 0 0 0 0 0 0 0 0 0 0

859 27 ParticleSystemManager const 0 8832 27 ParticleSystemManager const 27 ParticleSystemManager const 0 0 716 0 0 0 0 0 0 0 0 0 0

860 23 ParticleSystemManager * 0 8576 23 ParticleSystemManager * 23 ParticleSystemManager * 0 0 716 0 0 0 0 0 0 0 0 0 0

0
0
1
861 9 get_anims 0 626 627 33 SpriteParticleRenderer::get_anims 0

