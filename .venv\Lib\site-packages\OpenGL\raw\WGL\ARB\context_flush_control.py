'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_ARB_context_flush_control'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_ARB_context_flush_control',error_checker=_errors._error_checker)
WGL_CONTEXT_RELEASE_BEHAVIOR_ARB=_C('WGL_CONTEXT_RELEASE_BEHAVIOR_ARB',0x2097)
WGL_CONTEXT_RELEASE_BEHAVIOR_FLUSH_ARB=_C('WGL_CONTEXT_RELEASE_BEHAVIOR_FLUSH_ARB',0x2098)
WGL_CONTEXT_RELEASE_BEHAVIOR_NONE_ARB=_C('WGL_CONTEXT_RELEASE_BEHAVIOR_NONE_ARB',0)

