'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_copy_image'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_copy_image',error_checker=_errors._error_checker)

@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXContext,_cs.GLuint,_cs.GLenum,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLXContext,_cs.<PERSON><PERSON><PERSON><PERSON>,_cs.<PERSON>,_cs.<PERSON>L<PERSON>,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLsizei,_cs.GLsizei,_cs.GLsizei)
def glXCopyImageSubDataNV(dpy,srcCtx,srcName,srcTarget,srcLevel,srcX,srcY,srcZ,dstCtx,dstName,dstTarget,dstLevel,dstX,dstY,dstZ,width,height,depth):pass
