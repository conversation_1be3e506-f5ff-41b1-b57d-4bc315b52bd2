'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_SGIX_video_resize'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_SGIX_video_resize',error_checker=_errors._error_checker)
GLX_SYNC_FRAME_SGIX=_C('GLX_SYNC_FRAME_SGIX',0x00000000)
GLX_SYNC_SWAP_SGIX=_C('GLX_SYNC_SWAP_SGIX',0x00000001)
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,_cs.Window)
def glXBindChannelToWindowSGIX(display,screen,channel,window):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int,_cs.c_int)
def glXChannelRectSGIX(display,screen,channel,x,y,w,h):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,_cs.GLenum)
def glXChannelRectSyncSGIX(display,screen,channel,synctype):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int))
def glXQueryChannelDeltasSGIX(display,screen,channel,x,y,w,h):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int))
def glXQueryChannelRectSGIX(display,screen,channel,dx,dy,dw,dh):pass
