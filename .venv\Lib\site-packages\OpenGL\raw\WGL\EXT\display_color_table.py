'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_EXT_display_color_table'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_EXT_display_color_table',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.<PERSON><PERSON><PERSON>lean,_cs.GLushort)
def wglBindDisplayColorTableEXT(id):pass
@_f
@_p.types(_cs.GLboolean,_cs.GLushort)
def wglCreateDisplayColorTableEXT(id):pass
@_f
@_p.types(_cs.VOID,_cs.GLushort)
def wglDestroyDisplayColorTableEXT(id):pass
@_f
@_p.types(_cs.<PERSON><PERSON><PERSON>lean,arrays.GLushortArray,_cs.<PERSON><PERSON>uint)
def wglLoadDisplayColorTableEXT(table,length):pass
