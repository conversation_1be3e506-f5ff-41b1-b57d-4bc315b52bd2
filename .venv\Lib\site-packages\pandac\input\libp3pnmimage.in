1730992849
3 3
13 libp3pnmimage 4 BCNh 12 panda3d.core 
305
371 5 pixel 0 260 676 12 pixel::pixel 0 4 1 2 3 4 0
138
pixel::pixel(void) = default;
pixel::pixel(gray fill);
pixel::pixel(gray r, gray g, gray b);
inline pixel::pixel(pixel const &) = default;

372 11 operator [] 0 4 676 18 pixel::operator [] 0 2 5 6 0
70
gray pixel::operator [](int i) const;
gray &pixel::operator [](int i);

373 10 operator + 0 4 676 17 pixel::operator + 0 1 7 0
50
pixel pixel::operator +(pixel const &other) const;

374 10 operator - 0 4 676 17 pixel::operator - 0 1 8 0
50
pixel pixel::operator -(pixel const &other) const;

375 10 operator * 0 4 676 17 pixel::operator * 0 1 9 0
49
pixel pixel::operator *(double const mult) const;

376 11 operator += 0 4 676 18 pixel::operator += 0 1 10 0
44
void pixel::operator +=(pixel const &other);

377 11 operator -= 0 4 676 18 pixel::operator -= 0 1 11 0
44
void pixel::operator -=(pixel const &other);

378 11 operator *= 0 4 676 18 pixel::operator *= 0 1 12 0
43
void pixel::operator *=(double const mult);

379 11 operator == 0 4 676 18 pixel::operator == 0 1 13 0
44
bool pixel::operator ==(pixel const &other);

380 11 operator != 0 4 676 18 pixel::operator != 0 1 14 0
44
bool pixel::operator !=(pixel const &other);

381 10 operator < 0 4 676 17 pixel::operator < 0 1 15 0
49
bool pixel::operator <(pixel const &other) const;

382 4 size 0 4 676 11 pixel::size 0 1 16 0
29
static int pixel::size(void);

383 6 output 0 4 676 13 pixel::output 0 1 17 0
38
void pixel::output(std::ostream &out);

384 5 get_b 0 20 676 12 pixel::get_b 0 0 25
getter for gray pixel::b;
30
gray pixel::get_b(void) const;

385 5 set_b 0 36 676 12 pixel::set_b 0 0 25
setter for gray pixel::b;
30
void pixel::set_b(gray value);

386 5 get_g 0 20 676 12 pixel::get_g 0 0 25
getter for gray pixel::g;
30
gray pixel::get_g(void) const;

387 5 set_g 0 36 676 12 pixel::set_g 0 0 25
setter for gray pixel::g;
30
void pixel::set_g(gray value);

388 5 get_r 0 20 676 12 pixel::get_r 0 0 25
getter for gray pixel::r;
30
gray pixel::get_r(void) const;

389 5 set_r 0 36 676 12 pixel::set_r 0 0 25
setter for gray pixel::r;
30
void pixel::set_r(gray value);

390 6 ~pixel 0 516 676 13 pixel::~pixel 0 0 0
20
pixel::~pixel(void);

391 17 decode_sRGB_float 0 1 0 17 decode_sRGB_float 0 2 363 364 0
93
inline float decode_sRGB_float(unsigned char val);
inline float decode_sRGB_float(float val);

392 17 decode_sRGB_uchar 0 1 0 17 decode_sRGB_uchar 0 2 365 366 0
109
inline unsigned char decode_sRGB_uchar(unsigned char val);
inline unsigned char decode_sRGB_uchar(float val);

393 17 encode_sRGB_float 0 1 0 17 encode_sRGB_float 0 2 367 368 0
93
inline float encode_sRGB_float(unsigned char val);
inline float encode_sRGB_float(float val);

394 17 encode_sRGB_uchar 0 1 0 17 encode_sRGB_uchar 0 2 369 370 0
109
inline unsigned char encode_sRGB_uchar(unsigned char val);
inline unsigned char encode_sRGB_uchar(float val);

395 8 get_name 0 6 680 21 PNMFileType::get_name 0 1 18 0
58
virtual std::string PNMFileType::get_name(void) const = 0;

396 18 get_num_extensions 0 6 680 31 PNMFileType::get_num_extensions 0 1 19 117
/**
 * Returns the number of different possible filename extensions associated
 * with this particular file type.
 */
56
virtual int PNMFileType::get_num_extensions(void) const;

397 13 get_extension 0 6 680 26 PNMFileType::get_extension 0 1 20 123
/**
 * Returns the nth possible filename extension associated with this particular
 * file type, without a leading dot.
 */
60
virtual std::string PNMFileType::get_extension(int n) const;

398 23 get_suggested_extension 0 6 680 36 PNMFileType::get_suggested_extension 0 1 21 159
/**
 * Returns a suitable filename extension (without a leading dot) to suggest
 * for files of this type, or empty string if no suggestions are available.
 */
69
virtual std::string PNMFileType::get_suggested_extension(void) const;

399 14 get_class_type 0 4 680 27 PNMFileType::get_class_type 0 1 22 0
52
static TypeHandle PNMFileType::get_class_type(void);

400 20 ~PNMFileTypeRegistry 0 516 684 41 PNMFileTypeRegistry::~PNMFileTypeRegistry 0 0 10
/**
 *
 */
48
PNMFileTypeRegistry::~PNMFileTypeRegistry(void);

401 13 get_num_types 0 4 684 34 PNMFileTypeRegistry::get_num_types 0 1 24 56
/**
 * Returns the total number of types registered.
 */
51
int PNMFileTypeRegistry::get_num_types(void) const;

402 8 get_type 0 4 684 29 PNMFileTypeRegistry::get_type 0 1 25 43
/**
 * Returns the nth type registered.
 */
56
PNMFileType *PNMFileTypeRegistry::get_type(int n) const;

403 23 get_type_from_extension 0 4 684 44 PNMFileTypeRegistry::get_type_from_extension 0 1 26 203
/**
 * Tries to determine what the PNMFileType is likely to be for a particular
 * image file based on its extension.  Returns a suitable PNMFileType pointer,
 * or NULL if no type can be determined.
 */
93
PNMFileType *PNMFileTypeRegistry::get_type_from_extension(std::string const &filename) const;

404 26 get_type_from_magic_number 0 4 684 47 PNMFileTypeRegistry::get_type_from_magic_number 0 1 27 249
/**
 * Tries to determine what the PNMFileType is likely to be for a particular
 * image file based on its magic number, the first two bytes read from the
 * file.  Returns a suitable PNMFileType pointer, or NULL if no type can be
 * determined.
 */
100
PNMFileType *PNMFileTypeRegistry::get_type_from_magic_number(std::string const &magic_number) const;

405 18 get_type_by_handle 0 4 684 39 PNMFileTypeRegistry::get_type_by_handle 0 1 28 172
/**
 * Returns the PNMFileType instance stored in the registry for the given
 * TypeHandle, e.g.  as retrieved by a previous call to get_type() on the type
 * instance.
 */
78
PNMFileType *PNMFileTypeRegistry::get_type_by_handle(TypeHandle handle) const;

406 5 write 0 4 684 26 PNMFileTypeRegistry::write 0 1 29 103
/**
 * Writes a list of supported image file types to the indicated output stream,
 * one per line.
 */
79
void PNMFileTypeRegistry::write(std::ostream &out, int indent_level = 0) const;

407 14 get_global_ptr 0 4 684 35 PNMFileTypeRegistry::get_global_ptr 0 1 30 70
/**
 * Returns a pointer to the global PNMFileTypeRegistry object.
 */
70
static PNMFileTypeRegistry *PNMFileTypeRegistry::get_global_ptr(void);

408 19 PNMFileTypeRegistry 0 260 684 40 PNMFileTypeRegistry::PNMFileTypeRegistry 0 1 23 10
/**
 *
 */
87
inline PNMFileTypeRegistry::PNMFileTypeRegistry(PNMFileTypeRegistry const &) = default;

409 14 PNMImageHeader 0 260 686 30 PNMImageHeader::PNMImageHeader 0 2 31 32 22
/**
 *
 */

/**
 *
 */
111
inline PNMImageHeader::PNMImageHeader(void);
inline PNMImageHeader::PNMImageHeader(PNMImageHeader const &copy);

410 10 operator = 0 4 686 26 PNMImageHeader::operator = 0 1 33 0
67
inline void PNMImageHeader::operator =(PNMImageHeader const &copy);

411 15 ~PNMImageHeader 0 516 686 31 PNMImageHeader::~PNMImageHeader 0 0 10
/**
 *
 */
45
inline PNMImageHeader::~PNMImageHeader(void);

412 14 get_color_type 0 4 686 30 PNMImageHeader::get_color_type 0 1 34 147
/**
 * Returns the image type of the image, as an enumerated value.  This is
 * really just the number of channels cast to the enumerated type.
 */
76
inline PNMImageHeader::ColorType PNMImageHeader::get_color_type(void) const;

413 16 get_num_channels 0 4 686 32 PNMImageHeader::get_num_channels 0 1 35 55
/**
 * Returns the number of channels in the image.
 */
56
inline int PNMImageHeader::get_num_channels(void) const;

414 12 is_grayscale 0 4 686 28 PNMImageHeader::is_grayscale 0 2 36 37 416
/**
 * This static variant of is_grayscale() returns true if the indicated image
 * type represents a grayscale image, false otherwise.
 */

/**
 * Returns false if the image is a full-color image, and has red, green, and
 * blue components; true if it is a grayscale image and has only a gray
 * component.  (The gray color is actually stored in the blue channel, and the
 * red and green channels are ignored.)
 */
140
static inline bool PNMImageHeader::is_grayscale(PNMImageHeader::ColorType color_type);
inline bool PNMImageHeader::is_grayscale(void) const;

415 9 has_alpha 0 4 686 25 PNMImageHeader::has_alpha 0 2 38 39 339
/**
 * This static variant of has_alpha() returns true if the indicated image type
 * includes an alpha channel, false otherwise.
 */

/**
 * Returns true if the image includes an alpha channel, false otherwise.
 * Unlike is_grayscale(), if this returns false it is an error to call any of
 * the functions accessing the alpha channel.
 */
134
static inline bool PNMImageHeader::has_alpha(PNMImageHeader::ColorType color_type);
inline bool PNMImageHeader::has_alpha(void) const;

416 10 get_maxval 0 4 686 26 PNMImageHeader::get_maxval 0 1 40 183
/**
 * Returns the maximum channel value allowable for any pixel in this image;
 * for instance, 255 for a typical 8-bit-per-channel image.  A pixel with this
 * value is full on.
 */
53
inline xelval PNMImageHeader::get_maxval(void) const;

417 15 get_color_space 0 4 686 31 PNMImageHeader::get_color_space 0 1 41 97
/**
 * Returns the color space that the image is encoded in, or CS_unspecified if
 * unknown.
 */
62
inline ColorSpace PNMImageHeader::get_color_space(void) const;

418 10 get_x_size 0 4 686 26 PNMImageHeader::get_x_size 0 1 42 121
/**
 * Returns the number of pixels in the X direction.  This is one more than the
 * largest allowable X coordinate.
 */
50
inline int PNMImageHeader::get_x_size(void) const;

419 10 get_y_size 0 4 686 26 PNMImageHeader::get_y_size 0 1 43 121
/**
 * Returns the number of pixels in the Y direction.  This is one more than the
 * largest allowable Y coordinate.
 */
50
inline int PNMImageHeader::get_y_size(void) const;

420 8 get_size 0 4 686 24 PNMImageHeader::get_size 0 1 44 119
/**
 * Returns the number of pixels in each direction.  This is one more than the
 * largest allowable coordinates.
 */
55
inline LVecBase2i PNMImageHeader::get_size(void) const;

421 11 get_comment 0 4 686 27 PNMImageHeader::get_comment 0 1 45 47
/**
 * Gets the user comment from the file.
 */
59
inline std::string PNMImageHeader::get_comment(void) const;

422 11 set_comment 0 4 686 27 PNMImageHeader::set_comment 0 1 46 62
/**
 * Writes a user comment string to the image (header).
 */
68
inline void PNMImageHeader::set_comment(std::string const &comment);

423 8 has_type 0 4 686 24 PNMImageHeader::has_type 0 1 47 85
/**
 * Returns true if the PNMImageHeader knows what type it is, false otherwise.
 */
49
inline bool PNMImageHeader::has_type(void) const;

424 8 get_type 0 4 686 24 PNMImageHeader::get_type 0 1 48 130
/**
 * If the file type is known (e.g.  has_type() returns true), returns its
 * PNMFileType pointer; otherwise, returns NULL.
 */
57
inline PNMFileType *PNMImageHeader::get_type(void) const;

425 8 set_type 0 4 686 24 PNMImageHeader::set_type 0 1 49 294
/**
 * Sets the file type of this PNMImage.  This will be the default type used
 * when an image is read, if the type cannot be determined by magic number or
 * inferred by extension, or the type used when the image is written, if the
 * type cannot be inferred from the filename extension.
 */
56
inline void PNMImageHeader::set_type(PNMFileType *type);

426 11 read_header 0 4 686 27 PNMImageHeader::read_header 0 2 50 51 619
/**
 * Opens up the image file and tries to read its header information to
 * determine its size, number of channels, etc.  If successful, updates the
 * header information and returns true; otherwise, returns false.
 */

/**
 * Reads the image header information only from the indicated stream.
 *
 * The filename is advisory only, and may be used to suggest a type if it has
 * a known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of file it is (and a
 * non-NULL type will override any magic number test or filename extension
 * lookup).
 *
 * Returns true if successful, false on error.
 */
277
bool PNMImageHeader::read_header(Filename const &filename, PNMFileType *type = nullptr, bool report_unknown_type = true);
bool PNMImageHeader::read_header(std::istream &data, std::string const &filename = string(), PNMFileType *type = nullptr, bool report_unknown_type = true);

427 11 make_reader 0 4 686 27 PNMImageHeader::make_reader 0 2 52 53 1285
/**
 * Returns a newly-allocated PNMReader of the suitable type for reading from
 * the indicated image filename, or NULL if the filename cannot be read for
 * some reason.  The filename "-" always stands for standard input.  If type
 * is specified, it is a suggestion for the file type to use.
 *
 * The PNMReader should be deleted when it is no longer needed.
 */

/**
 * Returns a newly-allocated PNMReader of the suitable type for reading from
 * the already-opened image file, or NULL if the file cannot be read for some
 * reason.
 *
 * owns_file should be set true if the PNMReader is to be considered the owner
 * of the stream pointer (in which case the stream will be deleted on
 * completion, whether successful or not), or false if it should not delete
 * it.
 *
 * The filename parameter is optional here, since the file has already been
 * opened; it is only used to examine the extension and attempt to guess the
 * file type.
 *
 * If magic_number is nonempty, it is assumed to represent the first few bytes
 * that have already been read from the file.  Some file types may have
 * difficulty if this is more than two bytes.
 *
 * If type is non-NULL, it is a suggestion for the file type to use.
 *
 * The PNMReader should be deleted when it is no longer needed.
 */
360
PNMReader *PNMImageHeader::make_reader(Filename const &filename, PNMFileType *type = nullptr, bool report_unknown_type = true) const;
PNMReader *PNMImageHeader::make_reader(std::istream *file, bool owns_file = true, Filename const &filename = Filename(), std::string magic_number = string(), PNMFileType *type = nullptr, bool report_unknown_type = true) const;

428 11 make_writer 0 4 686 27 PNMImageHeader::make_writer 0 2 54 55 1100
/**
 * Returns a newly-allocated PNMWriter of the suitable type for writing an
 * image to the indicated filename, or NULL if the filename cannot be written
 * for some reason.  The filename "-" always stands for standard output.  If
 * type is specified, it is a suggestion for the file type to use.
 *
 * The PNMWriter should be deleted when it is no longer needed.
 */

/**
 * Returns a newly-allocated PNMWriter of the suitable type for writing to the
 * already-opened image file, or NULL if the file cannot be written for some
 * reason.
 *
 * owns_file should be set true if the PNMWriter is to be considered the owner
 * of the stream pointer (in which case the stream will be deleted on
 * completion, whether successful or not), or false if it should not delete
 * it.
 *
 * The filename parameter is optional here, since the file has already been
 * opened; it is only used to examine the extension and attempt to guess the
 * intended file type.
 *
 * If type is non-NULL, it is a suggestion for the file type to use.
 *
 * The PNMWriter should be deleted when it is no longer needed.
 */
257
PNMWriter *PNMImageHeader::make_writer(Filename const &filename, PNMFileType *type = nullptr) const;
PNMWriter *PNMImageHeader::make_writer(std::ostream *file, bool owns_file = true, Filename const &filename = Filename(), PNMFileType *type = nullptr) const;

429 17 read_magic_number 0 4 686 33 PNMImageHeader::read_magic_number 0 0 296
/**
 * Ensures that the first n bytes of the file are read into magic_number.  If
 * magic_number is initially nonempty, assumes these represent the first few
 * bytes already extracted.  Returns true if successful, false if an end of
 * file or error occurred before num_bytes could be read.
 */
108
static bool PNMImageHeader::read_magic_number(std::istream *file, std::string &magic_number, int num_bytes);

430 6 output 0 4 686 22 PNMImageHeader::output 0 1 56 10
/**
 *
 */
53
void PNMImageHeader::output(std::ostream &out) const;

431 9 PixelSpec 0 260 692 36 PNMImageHeader::PixelSpec::PixelSpec 0 7 57 58 59 60 61 62 63 70
/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
549
inline PNMImageHeader::PixelSpec::PixelSpec(xelval gray_value);
inline PNMImageHeader::PixelSpec::PixelSpec(xelval gray_value, xelval alpha);
inline PNMImageHeader::PixelSpec::PixelSpec(xelval red, xelval green, xelval blue);
inline PNMImageHeader::PixelSpec::PixelSpec(xelval red, xelval green, xelval blue, xelval alpha);
inline PNMImageHeader::PixelSpec::PixelSpec(xel const &rgb);
inline PNMImageHeader::PixelSpec::PixelSpec(xel const &rgb, xelval alpha);
inline PNMImageHeader::PixelSpec::PixelSpec(PNMImageHeader::PixelSpec const &) = default;

432 10 operator < 0 4 692 37 PNMImageHeader::PixelSpec::operator < 0 1 64 0
96
inline bool PNMImageHeader::PixelSpec::operator <(PNMImageHeader::PixelSpec const &other) const;

433 11 operator == 0 4 692 38 PNMImageHeader::PixelSpec::operator == 0 1 65 0
97
inline bool PNMImageHeader::PixelSpec::operator ==(PNMImageHeader::PixelSpec const &other) const;

434 11 operator != 0 4 692 38 PNMImageHeader::PixelSpec::operator != 0 1 66 0
97
inline bool PNMImageHeader::PixelSpec::operator !=(PNMImageHeader::PixelSpec const &other) const;

435 10 compare_to 0 4 692 37 PNMImageHeader::PixelSpec::compare_to 0 1 67 10
/**
 *
 */
95
inline int PNMImageHeader::PixelSpec::compare_to(PNMImageHeader::PixelSpec const &other) const;

436 7 get_red 0 4 692 34 PNMImageHeader::PixelSpec::get_red 0 1 68 10
/**
 *
 */
61
inline xelval PNMImageHeader::PixelSpec::get_red(void) const;

437 9 get_green 0 4 692 36 PNMImageHeader::PixelSpec::get_green 0 1 69 10
/**
 *
 */
63
inline xelval PNMImageHeader::PixelSpec::get_green(void) const;

438 8 get_blue 0 4 692 35 PNMImageHeader::PixelSpec::get_blue 0 1 70 10
/**
 *
 */
62
inline xelval PNMImageHeader::PixelSpec::get_blue(void) const;

439 9 get_alpha 0 4 692 36 PNMImageHeader::PixelSpec::get_alpha 0 1 71 10
/**
 *
 */
63
inline xelval PNMImageHeader::PixelSpec::get_alpha(void) const;

440 7 set_red 0 4 692 34 PNMImageHeader::PixelSpec::set_red 0 1 72 10
/**
 *
 */
59
inline void PNMImageHeader::PixelSpec::set_red(xelval red);

441 9 set_green 0 4 692 36 PNMImageHeader::PixelSpec::set_green 0 1 73 10
/**
 *
 */
63
inline void PNMImageHeader::PixelSpec::set_green(xelval green);

442 8 set_blue 0 4 692 35 PNMImageHeader::PixelSpec::set_blue 0 1 74 10
/**
 *
 */
61
inline void PNMImageHeader::PixelSpec::set_blue(xelval blue);

443 9 set_alpha 0 4 692 36 PNMImageHeader::PixelSpec::set_alpha 0 1 75 10
/**
 *
 */
63
inline void PNMImageHeader::PixelSpec::set_alpha(xelval alpha);

444 11 operator [] 0 4 692 38 PNMImageHeader::PixelSpec::operator [] 0 1 76 0
66
inline xelval PNMImageHeader::PixelSpec::operator [](int n) const;

445 4 size 0 4 692 31 PNMImageHeader::PixelSpec::size 0 1 77 135
/**
 * Specifies the number of components in the PixelSpec; this is always 4,
 * regardless of the type of image it was taken from.
 */
56
static inline int PNMImageHeader::PixelSpec::size(void);

446 6 output 0 4 692 33 PNMImageHeader::PixelSpec::output 0 1 78 10
/**
 *
 */
64
void PNMImageHeader::PixelSpec::output(std::ostream &out) const;

447 10 ~PixelSpec 0 516 692 37 PNMImageHeader::PixelSpec::~PixelSpec 0 0 0
44
PNMImageHeader::PixelSpec::~PixelSpec(void);

448 14 PixelSpecCount 0 260 693 46 PNMImageHeader::PixelSpecCount::PixelSpecCount 0 1 79 0
104
inline PNMImageHeader::PixelSpecCount::PixelSpecCount(PNMImageHeader::PixelSpecCount const &) = default;

449 15 ~PixelSpecCount 0 516 693 47 PNMImageHeader::PixelSpecCount::~PixelSpecCount 0 0 0
54
PNMImageHeader::PixelSpecCount::~PixelSpecCount(void);

450 9 Histogram 0 260 700 36 PNMImageHeader::Histogram::Histogram 0 2 80 81 10
/**
 *
 */
140
inline PNMImageHeader::Histogram::Histogram(void);
inline PNMImageHeader::Histogram::Histogram(PNMImageHeader::Histogram const &) = default;

451 14 get_num_pixels 0 4 700 41 PNMImageHeader::Histogram::get_num_pixels 0 1 82 70
/**
 * Returns the number of unique pixel colors in the histogram.
 */
65
inline int PNMImageHeader::Histogram::get_num_pixels(void) const;

452 9 get_pixel 0 4 700 36 PNMImageHeader::Histogram::get_pixel 0 1 83 130
/**
 * Returns the nth unique pixel color in the histogram.  These are ordered by
 * default from most common to least common.
 */
90
inline PNMImageHeader::PixelSpec const &PNMImageHeader::Histogram::get_pixel(int n) const;

453 9 get_count 0 4 700 36 PNMImageHeader::Histogram::get_count 0 2 84 85 200
/**
 * Returns the number of occurrences in the image of the nth unique pixel
 * color in the histogram.
 */

/**
 * Returns the number of occurrences in the image of the indicated pixel
 * color.
 */
156
inline int PNMImageHeader::Histogram::get_count(int n) const;
inline int PNMImageHeader::Histogram::get_count(PNMImageHeader::PixelSpec const &pixel) const;

454 5 write 0 4 700 32 PNMImageHeader::Histogram::write 0 1 86 10
/**
 *
 */
63
void PNMImageHeader::Histogram::write(std::ostream &out) const;

455 10 ~Histogram 0 516 700 37 PNMImageHeader::Histogram::~Histogram 0 0 0
44
PNMImageHeader::Histogram::~Histogram(void);

456 7 PfmFile 0 260 701 16 PfmFile::PfmFile 0 2 87 88 22
/**
 *
 */

/**
 *
 */
62
PfmFile::PfmFile(void);
PfmFile::PfmFile(PfmFile const &copy);

457 10 operator = 0 4 701 19 PfmFile::operator = 0 1 89 0
46
void PfmFile::operator =(PfmFile const &copy);

458 5 clear 0 4 701 14 PfmFile::clear 0 2 90 91 252
/**
 * Eliminates all data in the file.
 */

/**
 * Resets to an empty table with a specific size.  The case of num_channels ==
 * 0 is allowed only in the case that x_size and y_size are also == 0; and
 * this makes an empty (and invalid) PfmFile.
 */
89
void PfmFile::clear(void);
void PfmFile::clear(int x_size, int y_size, int num_channels);

459 4 read 0 4 701 13 PfmFile::read 0 3 92 93 94 628
/**
 * Reads the PFM data from the indicated file, returning true on success,
 * false on failure.
 *
 * This can also handle reading a standard image file supported by PNMImage;
 * it will be quietly converted to a floating-point type.
 */

/**
 * Reads the PFM data from the indicated stream, returning true on success,
 * false on failure.
 *
 * This can also handle reading a standard image file supported by PNMImage;
 * it will be quietly converted to a floating-point type.
 */

/**
 * Reads the PFM data using the indicated PNMReader.
 *
 * The PNMReader is always deleted upon completion, whether successful or not.
 */
161
bool PfmFile::read(Filename const &fullpath);
bool PfmFile::read(std::istream &in, Filename const &fullpath = Filename());
bool PfmFile::read(PNMReader *reader);

460 5 write 0 4 701 14 PfmFile::write 0 3 95 96 97 563
/**
 * Writes the PFM data to the indicated file, returning true on success, false
 * on failure.
 *
 * If the type implied by the filename extension supports floating-point, the
 * data will be written directly; otherwise, the floating-point data will be
 * quietly converted to the appropriate integer type.
 */

/**
 * Writes the PFM data to the indicated stream, returning true on success,
 * false on failure.
 */

/**
 * Writes the PFM data using the indicated PNMWriter.
 *
 * The PNMWriter is always deleted upon completion, whether successful or not.
 */
165
bool PfmFile::write(Filename const &fullpath);
bool PfmFile::write(std::ostream &out, Filename const &fullpath = Filename());
bool PfmFile::write(PNMWriter *writer);

461 4 load 0 4 701 13 PfmFile::load 0 1 98 110
/**
 * Fills the PfmFile with the data from the indicated PNMImage, converted to
 * floating-point values.
 */
45
bool PfmFile::load(PNMImage const &pnmimage);

462 5 store 0 4 701 14 PfmFile::store 0 1 99 79
/**
 * Copies the data to the indicated PNMImage, converting to RGB values.
 */
46
bool PfmFile::store(PNMImage &pnmimage) const;

463 10 store_mask 0 4 701 19 PfmFile::store_mask 0 2 100 101 606
/**
 * Stores 1 or 0 values into the indicated PNMImage, according to has_point()
 * for each pixel.  Each valid point gets a 1 value; each nonexistent point
 * gets a 0 value.
 */

/**
 * Stores 1 or 0 values into the indicated PNMImage, according to has_point()
 * for each pixel.  Each valid point gets a 1 value; each nonexistent point
 * gets a 0 value.
 *
 * This flavor of store_mask also checks whether the valid points are within
 * the specified min/max range.  Any valid points without the condition
 * min_point[c] <= value[c] <= max_point[c], for any c, are stored with a 0 in
 * the mask.
 */
161
bool PfmFile::store_mask(PNMImage &pnmimage) const;
bool PfmFile::store_mask(PNMImage &pnmimage, LVecBase4f const &min_point, LVecBase4f const &max_point) const;

464 8 is_valid 0 4 701 17 PfmFile::is_valid 0 1 102 10
/**
 *
 */
42
inline bool PfmFile::is_valid(void) const;

465 9 get_scale 0 4 701 18 PfmFile::get_scale 0 1 103 81
/**
 * The "scale" is reported in the pfm header and is probably meaningless.
 */
49
inline PN_float32 PfmFile::get_scale(void) const;

466 9 set_scale 0 4 701 18 PfmFile::set_scale 0 1 104 81
/**
 * The "scale" is reported in the pfm header and is probably meaningless.
 */
49
inline void PfmFile::set_scale(PN_float32 scale);

467 9 has_point 0 4 701 18 PfmFile::has_point 0 1 105 205
/**
 * Returns true if there is a valid point at x, y.  This always returns true
 * unless a "no data" value has been set, in which case it returns false if
 * the point at x, y is the "no data" value.
 */
51
inline bool PfmFile::has_point(int x, int y) const;

468 11 get_channel 0 4 701 20 PfmFile::get_channel 0 1 106 77
/**
 * Returns the cth channel of the point value at the indicated point.
 */
66
inline PN_float32 PfmFile::get_channel(int x, int y, int c) const;

469 11 set_channel 0 4 701 20 PfmFile::set_channel 0 1 107 78
/**
 * Replaces the cth channel of the point value at the indicated point.
 */
72
inline void PfmFile::set_channel(int x, int y, int c, PN_float32 value);

470 10 get_point1 0 4 701 19 PfmFile::get_point1 0 1 108 70
/**
 * Returns the 1-component point value at the indicated point.
 */
58
inline PN_float32 PfmFile::get_point1(int x, int y) const;

471 10 set_point1 0 4 701 19 PfmFile::set_point1 0 1 109 71
/**
 * Replaces the 1-component point value at the indicated point.
 */
64
inline void PfmFile::set_point1(int x, int y, PN_float32 point);

472 10 get_point2 0 4 701 19 PfmFile::get_point2 0 1 110 137
/**
 * Returns the 2-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */
63
inline LPoint2f const &PfmFile::get_point2(int x, int y) const;

473 10 set_point2 0 4 701 19 PfmFile::set_point2 0 2 111 112 278
/**
 * Replaces the 2-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */

/**
 * Replaces the 2-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */
143
inline void PfmFile::set_point2(int x, int y, LVecBase2f const &point);
inline void PfmFile::set_point2(int x, int y, LVecBase2d const &point);

474 13 modify_point2 0 4 701 22 PfmFile::modify_point2 0 1 113 79
/**
 * Returns a modifiable 2-component point value at the indicated point.
 */
54
inline LPoint2f &PfmFile::modify_point2(int x, int y);

475 9 get_point 0 4 701 18 PfmFile::get_point 0 1 114 137
/**
 * Returns the 3-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */
62
inline LPoint3f const &PfmFile::get_point(int x, int y) const;

476 9 set_point 0 4 701 18 PfmFile::set_point 0 2 115 116 278
/**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */

/**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */
141
inline void PfmFile::set_point(int x, int y, LVecBase3f const &point);
inline void PfmFile::set_point(int x, int y, LVecBase3d const &point);

477 12 modify_point 0 4 701 21 PfmFile::modify_point 0 1 117 79
/**
 * Returns a modifiable 3-component point value at the indicated point.
 */
53
inline LPoint3f &PfmFile::modify_point(int x, int y);

478 10 get_point3 0 4 701 19 PfmFile::get_point3 0 1 118 137
/**
 * Returns the 3-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */
63
inline LPoint3f const &PfmFile::get_point3(int x, int y) const;

479 10 set_point3 0 4 701 19 PfmFile::set_point3 0 2 119 120 278
/**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */

/**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */
143
inline void PfmFile::set_point3(int x, int y, LVecBase3f const &point);
inline void PfmFile::set_point3(int x, int y, LVecBase3d const &point);

480 13 modify_point3 0 4 701 22 PfmFile::modify_point3 0 1 121 79
/**
 * Returns a modifiable 3-component point value at the indicated point.
 */
54
inline LPoint3f &PfmFile::modify_point3(int x, int y);

481 10 get_point4 0 4 701 19 PfmFile::get_point4 0 1 122 137
/**
 * Returns the 4-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */
63
inline LPoint4f const &PfmFile::get_point4(int x, int y) const;

482 10 set_point4 0 4 701 19 PfmFile::set_point4 0 2 123 124 278
/**
 * Replaces the 4-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */

/**
 * Replaces the 4-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */
143
inline void PfmFile::set_point4(int x, int y, LVecBase4f const &point);
inline void PfmFile::set_point4(int x, int y, LVecBase4d const &point);

483 13 modify_point4 0 4 701 22 PfmFile::modify_point4 0 1 125 79
/**
 * Returns a modifiable 4-component point value at the indicated point.
 */
54
inline LPoint4f &PfmFile::modify_point4(int x, int y);

484 4 fill 0 4 701 13 PfmFile::fill 0 4 126 127 128 129 222
/**
 * Fills the table with all of the same value.
 */

/**
 * Fills the table with all of the same value.
 */

/**
 * Fills the table with all of the same value.
 */

/**
 * Fills the table with all of the same value.
 */
187
inline void PfmFile::fill(PN_float32 value);
inline void PfmFile::fill(LPoint2f const &value);
inline void PfmFile::fill(LPoint3f const &value);
void PfmFile::fill(LPoint4f const &value);

485 8 fill_nan 0 4 701 17 PfmFile::fill_nan 0 1 130 40
/**
 * Fills the table with all NaN.
 */
29
void PfmFile::fill_nan(void);

486 18 fill_no_data_value 0 4 701 27 PfmFile::fill_no_data_value 0 1 131 86
/**
 * Fills the table with the current no_data value, so that the table is empty.
 */
39
void PfmFile::fill_no_data_value(void);

487 12 fill_channel 0 4 701 21 PfmFile::fill_channel 0 1 132 107
/**
 * Fills the indicated channel with all of the same value, leaving the other
 * channels unchanged.
 */
58
void PfmFile::fill_channel(int channel, PN_float32 value);

488 16 fill_channel_nan 0 4 701 25 PfmFile::fill_channel_nan 0 1 133 86
/**
 * Fills the indicated channel with NaN, leaving the other channels unchanged.
 */
44
void PfmFile::fill_channel_nan(int channel);

489 19 fill_channel_masked 0 4 701 28 PfmFile::fill_channel_masked 0 1 134 152
/**
 * Fills the indicated channel with all of the same value, but only where the
 * table already has a data point.  Leaves empty points unchanged.
 */
65
void PfmFile::fill_channel_masked(int channel, PN_float32 value);

490 23 fill_channel_masked_nan 0 4 701 32 PfmFile::fill_channel_masked_nan 0 1 135 134
/**
 * Fills the indicated channel with NaN, but only where the table already has
 * a data point.  Leaves empty points unchanged.
 */
51
void PfmFile::fill_channel_masked_nan(int channel);

491 18 calc_average_point 0 4 701 27 PfmFile::calc_average_point 0 1 136 296
/**
 * Computes the unweighted average point of all points within the box centered
 * at (x, y) with the indicated Manhattan-distance radius.  Missing points are
 * assigned the value of their nearest neighbor.  Returns true if successful,
 * or false if the point value cannot be determined.
 */
104
bool PfmFile::calc_average_point(LPoint3f &result, PN_float32 x, PN_float32 y, PN_float32 radius) const;

492 19 calc_bilinear_point 0 4 701 28 PfmFile::calc_bilinear_point 0 1 137 190
/**
 * Computes the weighted average of the four nearest points to the floating-
 * point index (x, y).  Returns true if the point has any contributors, false
 * if the point is unknown.
 */
86
bool PfmFile::calc_bilinear_point(LPoint3f &result, PN_float32 x, PN_float32 y) const;

493 12 calc_min_max 0 4 701 21 PfmFile::calc_min_max 0 1 138 246
/**
 * Calculates the minimum and maximum x, y, and z depth component values,
 * representing the bounding box of depth values, and places them in the
 * indicated vectors.  Returns true if successful, false if the mesh contains
 * no points.
 */
81
bool PfmFile::calc_min_max(LVecBase3f &min_points, LVecBase3f &max_points) const;

494 13 calc_autocrop 0 4 701 22 PfmFile::calc_autocrop 0 2 139 140 754
/**
 * Computes the minimum range of x and y across the PFM file that include all
 * points.  If there are no points with no_data_value in the grid--that is,
 * all points are included--then this will return (0, get_x_size(), 0,
 * get_y_size()).
 */

/**
 * Computes the minimum range of x and y across the PFM file that include all
 * points.  If there are no points with no_data_value in the grid--that is,
 * all points are included--then this will return (0, get_x_size(), 0,
 * get_y_size()).
 */

/**
 * Computes the minimum range of x and y across the PFM file that include all
 * points.  If there are no points with no_data_value in the grid--that is,
 * all points are included--then this will return (0, get_x_size(), 0,
 * get_y_size()).
 */
208
bool PfmFile::calc_autocrop(int &x_begin, int &x_end, int &y_begin, int &y_end) const;
inline bool PfmFile::calc_autocrop(LVecBase4f &range) const;
inline bool PfmFile::calc_autocrop(LVecBase4d &range) const;

495 12 is_row_empty 0 4 701 21 PfmFile::is_row_empty 0 1 141 160
/**
 * Returns true if all of the points on row y, in the range [x_begin, x_end),
 * are the no_data value, or false if any one of these points has a value.
 */
64
bool PfmFile::is_row_empty(int y, int x_begin, int x_end) const;

496 15 is_column_empty 0 4 701 24 PfmFile::is_column_empty 0 1 142 155
/**
 * Returns true if all of the points on column x, from [y_begin, y_end), are
 * the no_data value, or false if any one of these points has a value.
 */
67
bool PfmFile::is_column_empty(int x, int y_begin, int y_end) const;

497 16 set_zero_special 0 4 701 25 PfmFile::set_zero_special 0 1 143 209
/**
 * Sets the zero_special flag.  When this flag is true, values of (0, 0, 0) in
 * the pfm file are treated as a special case, and are not processed.
 *
 * This is a special case of set_no_data_value().
 */
57
inline void PfmFile::set_zero_special(bool zero_special);

498 17 set_no_data_chan4 0 4 701 26 PfmFile::set_no_data_chan4 0 1 144 259
/**
 * Sets the no_data_chan4 flag.  When this flag is true, and the pfm file has
 * 4 channels, then a negative value in the fourth channel indicates no data.
 * When it is false, all points are valid.
 *
 * This is a special case of set_no_data_value().
 */
51
inline void PfmFile::set_no_data_chan4(bool chan4);

499 15 set_no_data_nan 0 4 701 24 PfmFile::set_no_data_nan 0 1 145 272
/**
 * Sets the no_data_nan flag.  When num_channels is nonzero, then a NaN value
 * in any of the first num_channels channels indicates no data for that point.
 * If num_channels is zero, then all points are valid.
 *
 * This is a special case of set_no_data_value().
 */
48
void PfmFile::set_no_data_nan(int num_channels);

500 17 set_no_data_value 0 4 701 26 PfmFile::set_no_data_value 0 2 146 147 182
/**
 * Sets the special value that means "no data" when it appears in the pfm
 * file.
 */

/**
 * Sets the special value that means "no data" when it appears in the pfm
 * file.
 */
134
void PfmFile::set_no_data_value(LPoint4f const &no_data_value);
inline void PfmFile::set_no_data_value(LPoint4d const &no_data_value);

501 21 set_no_data_threshold 0 4 701 30 PfmFile::set_no_data_threshold 0 2 148 149 254
/**
 * Sets the special threshold value.  Points that are below this value in all
 * components are considered "no value".
 */

/**
 * Sets the special threshold value.  Points that are below this value in all
 * components are considered "no value".
 */
142
void PfmFile::set_no_data_threshold(LPoint4f const &no_data_value);
inline void PfmFile::set_no_data_threshold(LPoint4d const &no_data_value);

502 19 clear_no_data_value 0 4 701 28 PfmFile::clear_no_data_value 0 1 150 136
/**
 * Removes the special value that means "no data" when it appears in the pfm
 * file.  All points will thus be considered valid.
 */
47
inline void PfmFile::clear_no_data_value(void);

503 17 has_no_data_value 0 4 701 26 PfmFile::has_no_data_value 0 1 151 92
/**
 * Returns whether a "no data" value has been established by
 * set_no_data_value().
 */
51
inline bool PfmFile::has_no_data_value(void) const;

504 21 has_no_data_threshold 0 4 701 30 PfmFile::has_no_data_threshold 0 1 152 106
/**
 * Returns whether a "no data" threshold value has been established by
 * set_no_data_threshold().
 */
55
inline bool PfmFile::has_no_data_threshold(void) const;

505 17 get_no_data_value 0 4 701 26 PfmFile::get_no_data_value 0 1 153 95
/**
 * If has_no_data_value() returns true, this returns the particular "no data"
 * value.
 */
62
inline LPoint4f const &PfmFile::get_no_data_value(void) const;

506 6 resize 0 4 701 15 PfmFile::resize 0 1 154 173
/**
 * Applies a simple filter to resample the pfm file in-place to the indicated
 * size.  Don't confuse this with applying a scale to all of the points via
 * xform().
 */
53
void PfmFile::resize(int new_x_size, int new_y_size);

507 15 box_filter_from 0 4 701 24 PfmFile::box_filter_from 0 1 155 260
/**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */
65
void PfmFile::box_filter_from(float radius, PfmFile const &copy);

508 20 gaussian_filter_from 0 4 701 29 PfmFile::gaussian_filter_from 0 1 156 260
/**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */
70
void PfmFile::gaussian_filter_from(float radius, PfmFile const &copy);

509 17 quick_filter_from 0 4 701 26 PfmFile::quick_filter_from 0 1 157 257
/**
 * Resizes from the given image, with a fixed radius of 0.5. This is a very
 * specialized and simple algorithm that doesn't handle dropping below the
 * Nyquist rate very well, but is quite a bit faster than the more general
 * box_filter(), above.
 */
53
void PfmFile::quick_filter_from(PfmFile const &copy);

510 12 reverse_rows 0 4 701 21 PfmFile::reverse_rows 0 1 158 61
/**
 * Performs an in-place reversal of the row (y) data.
 */
33
void PfmFile::reverse_rows(void);

511 4 flip 0 4 701 13 PfmFile::flip 0 1 159 369
/**
 * Reverses, transposes, and/or rotates the table in-place according to the
 * specified parameters.  If flip_x is true, the x axis is reversed; if flip_y
 * is true, the y axis is reversed.  Then, if transpose is true, the x and y
 * axes are exchanged.  These parameters can be used to select any combination
 * of 90-degree or 180-degree rotations and flips.
 */
61
void PfmFile::flip(bool flip_x, bool flip_y, bool transpose);

512 5 xform 0 4 701 14 PfmFile::xform 0 2 160 161 148
/**
 * Applies the indicated transform matrix to all points in-place.
 */

/**
 * Applies the indicated transform matrix to all points in-place.
 */
104
void PfmFile::xform(LMatrix4f const &transform);
inline void PfmFile::xform(LMatrix4d const &transform);

513 15 forward_distort 0 4 701 24 PfmFile::forward_distort 0 1 162 659
/**
 * Applies the distortion indicated in the supplied dist map to the current
 * map.  The dist map is understood to be a mapping of points in the range
 * 0..1 in the first two dimensions.
 *
 * The operation can be expressed symbolically as:
 *
 * this(u, v) = this(dist(u, v))
 *
 * If scale_factor is not 1, it should be a value > 1, and it specifies the
 * factor to upscale the working table while processing, to reduce artifacts
 * from integer truncation.
 *
 * By convention, the y axis is inverted in the distortion map relative to the
 * coordinates here.  A y value of 0 in the distortion map corresponds with a
 * v value of 1 in this file.
 */
82
void PfmFile::forward_distort(PfmFile const &dist, PN_float32 scale_factor = 1.0);

514 15 reverse_distort 0 4 701 24 PfmFile::reverse_distort 0 1 163 659
/**
 * Applies the distortion indicated in the supplied dist map to the current
 * map.  The dist map is understood to be a mapping of points in the range
 * 0..1 in the first two dimensions.
 *
 * The operation can be expressed symbolically as:
 *
 * this(u, v) = dist(this(u, v))
 *
 * If scale_factor is not 1, it should be a value > 1, and it specifies the
 * factor to upscale the working table while processing, to reduce artifacts
 * from integer truncation.
 *
 * By convention, the y axis in inverted in the distortion map relative to the
 * coordinates here.  A y value of 0 in the distortion map corresponds with a
 * v value of 1 in this file.
 */
82
void PfmFile::reverse_distort(PfmFile const &dist, PN_float32 scale_factor = 1.0);

515 12 apply_1d_lut 0 4 701 21 PfmFile::apply_1d_lut 0 1 164 211
/**
 * Assumes that lut is an X by 1, 1-component PfmFile whose X axis maps points
 * to target points.  For each point in this pfm file, computes: p(u,
 * v)[channel] = lut(p(u, v)[channel] * x_scale, 0)[0]
 */
86
void PfmFile::apply_1d_lut(int channel, PfmFile const &lut, PN_float32 x_scale = 1.0);

516 5 merge 0 4 701 14 PfmFile::merge 0 1 165 199
/**
 * Wherever there is missing data in this PfmFile (that is, wherever
 * has_point() returns false), copy data from the other PfmFile, which must be
 * exactly the same dimensions as this one.
 */
42
void PfmFile::merge(PfmFile const &other);

517 10 apply_mask 0 4 701 19 PfmFile::apply_mask 0 1 166 274
/**
 * Wherever there is missing data in the other PfmFile, set this the
 * corresponding point in this PfmFile to missing as well, so that this
 * PfmFile has only points where both files have points.
 *
 * The point is set to "missing" by setting it the no_data_value.
 */
47
void PfmFile::apply_mask(PfmFile const &other);

518 12 copy_channel 0 4 701 21 PfmFile::copy_channel 0 1 167 159
/**
 * Copies just the specified channel values from the indicated PfmFile (which
 * could be same as this PfmFile) into the specified channel of this one.
 */
83
void PfmFile::copy_channel(int to_channel, PfmFile const &other, int from_channel);

519 19 copy_channel_masked 0 4 701 28 PfmFile::copy_channel_masked 0 1 168 130
/**
 * Copies just the specified channel values from the indicated PfmFile, but
 * only where the other file has a data point.
 */
90
void PfmFile::copy_channel_masked(int to_channel, PfmFile const &other, int from_channel);

520 10 apply_crop 0 4 701 19 PfmFile::apply_crop 0 1 169 148
/**
 * Reduces the PFM file to the cells in the rectangle bounded by (x_begin,
 * x_end, y_begin, y_end), where the _end cells are not included.
 */
73
void PfmFile::apply_crop(int x_begin, int x_end, int y_begin, int y_end);

521 18 clear_to_texcoords 0 4 701 27 PfmFile::clear_to_texcoords 0 1 170 159
/**
 * Replaces this PfmFile with a new PfmFile of size x_size x y_size x 3,
 * containing the x y 0 values in the range 0 .. 1 according to the x y index.
 */
57
void PfmFile::clear_to_texcoords(int x_size, int y_size);

522 9 pull_spot 0 4 701 18 PfmFile::pull_spot 0 1 171 262
/**
 * Applies delta * t to the point values within radius (xr, yr) distance of
 * (xc, yc).  The t value is scaled from 1.0 at the center to 0.0 at radius
 * (xr, yr), and this scale follows the specified exponent.  Returns the
 * number of points affected.
 */
102
int PfmFile::pull_spot(LPoint4f const &delta, float xc, float yc, float xr, float yr, float exponent);

523 17 calc_tight_bounds 0 4 701 26 PfmFile::calc_tight_bounds 0 1 172 209
/**
 * Calculates the minimum and maximum vertices of all points within the table.
 * Assumes the table contains 3-D points.
 *
 * The return value is true if any points in the table, or false if none are.
 */
80
bool PfmFile::calc_tight_bounds(LPoint3f &min_point, LPoint3f &max_point) const;

524 21 compute_planar_bounds 0 4 701 30 PfmFile::compute_planar_bounds 0 2 173 174 1290
/**
 * Computes the minmax bounding volume of the points in 3-D space, assuming
 * the points represent a mostly-planar surface.
 *
 * This algorithm works by sampling the (square) sample_radius pixels at the
 * four point_dist corners around the center (cx - pd, cx + pd) and so on, to
 * approximate the plane of the surface.  Then all of the points are projected
 * into that plane and the bounding volume of the entire mesh within that
 * plane is determined.  If points_only is true, the bounding volume of only
 * those four points is determined.
 *
 * center, point_dist and sample_radius are in UV space, i.e.  in the range
 * 0..1.
 */

/**
 * Computes the minmax bounding volume of the points in 3-D space, assuming
 * the points represent a mostly-planar surface.
 *
 * This algorithm works by sampling the (square) sample_radius pixels at the
 * four point_dist corners around the center (cx - pd, cx + pd) and so on, to
 * approximate the plane of the surface.  Then all of the points are projected
 * into that plane and the bounding volume of the entire mesh within that
 * plane is determined.  If points_only is true, the bounding volume of only
 * those four points is determined.
 *
 * center, point_dist and sample_radius are in UV space, i.e.  in the range
 * 0..1.
 */
328
PointerTo< BoundingHexahedron > PfmFile::compute_planar_bounds(LPoint2f const &center, PN_float32 point_dist, PN_float32 sample_radius, bool points_only) const;
inline PointerTo< BoundingHexahedron > PfmFile::compute_planar_bounds(LPoint2d const &center, PN_float32 point_dist, PN_float32 sample_radius, bool points_only) const;

525 20 compute_sample_point 0 4 701 29 PfmFile::compute_sample_point 0 1 175 188
/**
 * Computes the average of all the point within sample_radius (manhattan
 * distance) and the indicated point.
 *
 * The point coordinates are given in UV space, in the range 0..1.
 */
113
void PfmFile::compute_sample_point(LPoint3f &result, PN_float32 x, PN_float32 y, PN_float32 sample_radius) const;

526 14 copy_sub_image 0 4 701 23 PfmFile::copy_sub_image 0 1 176 344
/**
 * Copies a rectangular area of another image into a rectangular area of this
 * image.  Both images must already have been initialized.  The upper-left
 * corner of the region in both images is specified, and the size of the area;
 * if the size is omitted, it defaults to the entire other image, or the
 * largest piece that will fit.
 */
132
void PfmFile::copy_sub_image(PfmFile const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1);

527 13 add_sub_image 0 4 701 22 PfmFile::add_sub_image 0 1 177 155
/**
 * Behaves like copy_sub_image(), except the copy pixels are added to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 */
156
void PfmFile::add_sub_image(PfmFile const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

528 14 mult_sub_image 0 4 701 23 PfmFile::mult_sub_image 0 1 178 160
/**
 * Behaves like copy_sub_image(), except the copy pixels are multiplied to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 */
157
void PfmFile::mult_sub_image(PfmFile const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

529 16 divide_sub_image 0 4 701 25 PfmFile::divide_sub_image 0 1 179 216
/**
 * Behaves like copy_sub_image(), except the copy pixels are divided into the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * dest(x, y) = dest(x, y) / (copy(x, y) * pixel_scale).
 */
159
void PfmFile::divide_sub_image(PfmFile const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

530 11 operator *= 0 4 701 20 PfmFile::operator *= 0 1 180 0
44
void PfmFile::operator *=(float multiplier);

531 18 indirect_1d_lookup 0 4 701 27 PfmFile::indirect_1d_lookup 0 1 181 566
/**
 * index_image is a WxH 1-channel image, while pixel_values is an Nx1
 * image with any number of channels.  Typically pixel_values will be
 * a 256x1 image.
 *
 * Fills the PfmFile with a new image the same width and height as
 * index_image, with the same number of channels as pixel_values.
 *
 * Each pixel of the new image is computed with the formula:
 *
 * new_image(x, y) = pixel_values(index_image(x, y)[channel], 0)
 *
 * At present, no interpolation is performed; the nearest value in
 * pixel_values is discovered.  This may change in the future.
 */
103
void PfmFile::indirect_1d_lookup(PfmFile const &index_image, int channel, PfmFile const &pixel_values);

532 13 gamma_correct 0 4 701 22 PfmFile::gamma_correct 0 1 182 217
/**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * RGB channels, converts it to an image with a gamma curve of to_gamma in the
 * RGB channels.  Does not affect the alpha channel.
 */
69
inline void PfmFile::gamma_correct(float from_gamma, float to_gamma);

533 19 gamma_correct_alpha 0 4 701 28 PfmFile::gamma_correct_alpha 0 1 183 218
/**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * alpha channel, converts it to an image with a gamma curve of to_gamma in
 * the alpha channel.  Does not affect the RGB channels.
 */
75
inline void PfmFile::gamma_correct_alpha(float from_gamma, float to_gamma);

534 14 apply_exponent 0 4 701 23 PfmFile::apply_exponent 0 4 184 185 186 187 733
/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */
337
inline void PfmFile::apply_exponent(float gray_exponent);
inline void PfmFile::apply_exponent(float gray_exponent, float alpha_exponent);
inline void PfmFile::apply_exponent(float c0_exponent, float c1_exponent, float c2_exponent);
void PfmFile::apply_exponent(float c0_exponent, float c1_exponent, float c2_exponent, float c3_exponent);

535 6 output 0 4 701 15 PfmFile::output 0 1 188 10
/**
 *
 */
46
void PfmFile::output(std::ostream &out) const;

536 10 get_points 0 4 701 19 PfmFile::get_points 0 1 189 0
42
PyObject *PfmFile::get_points(void) const;

537 13 __getbuffer__ 0 4 701 22 PfmFile::__getbuffer__ 0 1 190 0
77
int PfmFile::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

538 8 ~PfmFile 0 516 701 17 PfmFile::~PfmFile 0 0 0
24
PfmFile::~PfmFile(void);

539 16 make_transparent 0 4 705 26 PNMBrush::make_transparent 0 1 191 161
/**
 * Returns a new brush that does not paint anything.  Can be used as either a
 * pen or a fill brush to make borderless or unfilled shapes, respectively.
 */
62
static PointerTo< PNMBrush > PNMBrush::make_transparent(void);

540 10 make_pixel 0 4 705 20 PNMBrush::make_pixel 0 1 192 136
/**
 * Returns a new brush that paints a single pixel of the indicated color on a
 * border, or paints a solid color in an interior.
 */
125
static PointerTo< PNMBrush > PNMBrush::make_pixel(LColorf const &color, PNMBrush::BrushEffect effect = ::PNMBrush::BE_blend);

541 9 make_spot 0 4 705 19 PNMBrush::make_spot 0 1 193 153
/**
 * Returns a new brush that paints a spot of the indicated color and radius.
 * If fuzzy is true, the spot is fuzzy; otherwise, it is hard-edged.
 */
150
static PointerTo< PNMBrush > PNMBrush::make_spot(LColorf const &color, float radius, bool fuzzy, PNMBrush::BrushEffect effect = ::PNMBrush::BE_blend);

542 10 make_image 0 4 705 20 PNMBrush::make_image 0 1 194 244
/**
 * Returns a new brush that paints with the indicated image.  xc and yc
 * indicate the pixel in the center of the brush.
 *
 * The brush makes a copy of the image; it is safe to deallocate or modify the
 * image after making this call.
 */
146
static PointerTo< PNMBrush > PNMBrush::make_image(PNMImage const &image, float xc, float yc, PNMBrush::BrushEffect effect = ::PNMBrush::BE_blend);

543 8 PNMImage 0 260 708 18 PNMImage::PNMImage 0 4 195 196 197 198 46
/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
335
inline PNMImage::PNMImage(void);
explicit PNMImage::PNMImage(Filename const &filename, PNMFileType *type = nullptr);
inline explicit PNMImage::PNMImage(int x_size, int y_size, int num_channels = 3, xelval maxval = 255, PNMFileType *type = nullptr, ColorSpace color_space = ::CS_linear);
inline PNMImage::PNMImage(PNMImage const &copy);

544 10 operator = 0 4 708 20 PNMImage::operator = 0 1 199 0
55
inline void PNMImage::operator =(PNMImage const &copy);

545 9 ~PNMImage 0 516 708 19 PNMImage::~PNMImage 0 0 10
/**
 *
 */
33
inline PNMImage::~PNMImage(void);

546 9 clamp_val 0 4 708 19 PNMImage::clamp_val 0 1 200 65
/**
 * A handy function to clamp values to [0..get_maxval()].
 */
57
inline xelval PNMImage::clamp_val(int input_value) const;

547 6 to_val 0 4 708 16 PNMImage::to_val 0 2 201 202 288
/**
 * A handy function to scale non-alpha values from [0..1] to
 * [0..get_maxval()].  Do not use this for alpha values, see to_alpha_val.
 */

/**
 * A handy function to scale non-alpha values from [0..1] to
 * [0..get_maxval()].  Do not use this for alpha values, see to_alpha_val.
 */
122
inline xel PNMImage::to_val(LRGBColorf const &input_value) const;
inline xelval PNMImage::to_val(float input_value) const;

548 12 to_alpha_val 0 4 708 22 PNMImage::to_alpha_val 0 1 203 83
/**
 * A handy function to scale alpha values from [0..1] to [0..get_maxval()].
 */
62
inline xelval PNMImage::to_alpha_val(float input_value) const;

549 8 from_val 0 4 708 18 PNMImage::from_val 0 2 204 205 292
/**
 * A handy function to scale non-alpha values from [0..get_maxval()] to
 * [0..1].  Do not use this for alpha values, see from_alpha_val.
 */

/**
 * A handy function to scale non-alpha values from [0..get_maxval()] to
 * [0..1].  Do not use this for alpha values, see from_alpha_val.
 */
126
inline LRGBColorf PNMImage::from_val(xel const &input_value) const;
inline float PNMImage::from_val(xelval input_value) const;

550 14 from_alpha_val 0 4 708 24 PNMImage::from_alpha_val 0 1 206 83
/**
 * A handy function to scale alpha values from [0..get_maxval()] to [0..1].
 */
64
inline float PNMImage::from_alpha_val(xelval input_value) const;

551 5 clear 0 4 708 15 PNMImage::clear 0 2 207 208 226
/**
 * Frees all memory allocated for the image, and clears all its parameters
 * (size, color, type, etc).
 */

/**
 * This flavor of clear() reinitializes the image to an empty (black) image
 * with the given dimensions.
 */
183
void PNMImage::clear(void);
void PNMImage::clear(int x_size, int y_size, int num_channels = 3, xelval maxval = 255, PNMFileType *type = nullptr, ColorSpace color_space = ::CS_linear);

552 9 copy_from 0 4 708 19 PNMImage::copy_from 0 1 209 61
/**
 * Makes this image become a copy of the other image.
 */
47
void PNMImage::copy_from(PNMImage const &copy);

553 12 copy_channel 0 4 708 22 PNMImage::copy_channel 0 2 210 211 227
/**
 * Copies a channel from one image into another.  Images must be the same size
 */

/**
 * Copies just a single channel from the source image into a single channel of
 * this image, leaving the remaining channels alone.
 */
242
void PNMImage::copy_channel(PNMImage const &copy, int src_channel, int dest_channel);
void PNMImage::copy_channel(PNMImage const &copy, int xto, int yto, int cto, int xfrom = 0, int yfrom = 0, int cfrom = 0, int x_size = -1, int y_size = -1);

554 17 copy_channel_bits 0 4 708 27 PNMImage::copy_channel_bits 0 1 212 247
/**
 * Copies some subset of the bits of the specified channel from one image into
 * some subset of the bits of the specified channel in another image.  Images
 * must be the same size.
 *
 * If right_shift is negative, it means a left shift.
 */
124
void PNMImage::copy_channel_bits(PNMImage const &copy, int src_channel, int dest_channel, xelval src_mask, int right_shift);

555 16 copy_header_from 0 4 708 26 PNMImage::copy_header_from 0 1 213 186
/**
 * Copies just the header information into this image.  This will blow away
 * any image data stored in the image.  The new image data will be allocated,
 * but left unitialized.
 */
62
void PNMImage::copy_header_from(PNMImageHeader const &header);

556 9 take_from 0 4 708 19 PNMImage::take_from 0 1 214 92
/**
 * Move the contents of the other image into this one, and empty the other
 * image.
 */
41
void PNMImage::take_from(PNMImage &orig);

557 4 fill 0 4 708 14 PNMImage::fill 0 2 215 216 173
/**
 * Sets the entire image (except the alpha channel) to the given color.
 */

/**
 * Sets the entire image (except the alpha channel) to the given grayscale
 * level.
 */
109
inline void PNMImage::fill(float red, float green, float blue);
inline void PNMImage::fill(float gray = 0.0);

558 8 fill_val 0 4 708 18 PNMImage::fill_val 0 2 217 218 173
/**
 * Sets the entire image (except the alpha channel) to the given grayscale
 * level.
 */

/**
 * Sets the entire image (except the alpha channel) to the given color.
 */
112
void PNMImage::fill_val(xelval red, xelval green, xelval blue);
inline void PNMImage::fill_val(xelval gray = 0);

559 10 alpha_fill 0 4 708 20 PNMImage::alpha_fill 0 1 219 60
/**
 * Sets the entire alpha channel to the given level.
 */
52
inline void PNMImage::alpha_fill(float alpha = 0.0);

560 14 alpha_fill_val 0 4 708 24 PNMImage::alpha_fill_val 0 1 220 60
/**
 * Sets the entire alpha channel to the given level.
 */
48
void PNMImage::alpha_fill_val(xelval alpha = 0);

561 13 set_read_size 0 4 708 23 PNMImage::set_read_size 0 1 221 513
/**
 * Specifies the size to we'd like to scale the image upon reading it.  This
 * will affect the next call to read().  This is usually used to reduce the
 * image size, e.g.  for a thumbnail.
 *
 * If the file type reader supports it (e.g.  JPEG), then this will scale the
 * image during the read operation, consequently reducing memory and CPU
 * utilization.  If the file type reader does not support it, this will load
 * the image normally, and them perform a linear scale after it has been
 * loaded.
 */
60
inline void PNMImage::set_read_size(int x_size, int y_size);

562 15 clear_read_size 0 4 708 25 PNMImage::clear_read_size 0 1 222 67
/**
 * Undoes the effect of a previous call to set_read_size().
 */
44
inline void PNMImage::clear_read_size(void);

563 13 has_read_size 0 4 708 23 PNMImage::has_read_size 0 1 223 59
/**
 * Returns true if set_read_size() has been called.
 */
48
inline bool PNMImage::has_read_size(void) const;

564 15 get_read_x_size 0 4 708 25 PNMImage::get_read_x_size 0 1 224 139
/**
 * Returns the requested x_size of the image if set_read_size() has been
 * called, or the image x_size otherwise (if it is known).
 */
49
inline int PNMImage::get_read_x_size(void) const;

565 15 get_read_y_size 0 4 708 25 PNMImage::get_read_y_size 0 1 225 139
/**
 * Returns the requested y_size of the image if set_read_size() has been
 * called, or the image y_size otherwise (if it is known).
 */
49
inline int PNMImage::get_read_y_size(void) const;

566 15 get_color_space 0 4 708 25 PNMImage::get_color_space 0 1 226 65
/**
 * Returns the color space in which the image is encoded.
 */
56
inline ColorSpace PNMImage::get_color_space(void) const;

567 4 read 0 4 708 14 PNMImage::read 0 3 227 228 229 930
/**
 * Reads the indicated image filename.  If type is non-NULL, it is a
 * suggestion for the type of file it is.  Returns true if successful, false
 * on error.
 */

/**
 * Reads the image data from the indicated stream.
 *
 * The filename is advisory only, and may be used to suggest a type if it has
 * a known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of file it is (and a
 * non-NULL type will override any magic number test or filename extension
 * lookup).
 *
 * Returns true if successful, false on error.
 */

/**
 * This flavor of read() uses an already-existing PNMReader to read the image
 * file.  You can get a reader via the PNMImageHeader::make_reader() methods.
 * This is a good way to examine the header of a file (for instance, to
 * determine its size) before actually reading the entire image.
 *
 * The PNMReader is always deleted upon completion, whether successful or not.
 */
291
bool PNMImage::read(Filename const &filename, PNMFileType *type = nullptr, bool report_unknown_type = true);
bool PNMImage::read(std::istream &data, std::string const &filename = string(), PNMFileType *type = nullptr, bool report_unknown_type = true);
bool PNMImage::read(PNMReader *reader);

568 5 write 0 4 708 15 PNMImage::write 0 3 230 231 232 628
/**
 * Writes the image to the indicated filename.  If type is non-NULL, it is a
 * suggestion for the type of image file to write.
 */

/**
 * Writes the image to the indicated ostream.
 *
 * The filename is advisory only, and may be used suggest a type if it has a
 * known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of image file to
 * write.
 */

/**
 * This flavor of write() uses an already-existing PNMWriter to write the
 * image file.  You can get a writer via the PNMImageHeader::make_writer()
 * methods.
 *
 * The PNMWriter is always deleted upon completion, whether successful or not.
 */
246
bool PNMImage::write(Filename const &filename, PNMFileType *type = nullptr) const;
bool PNMImage::write(std::ostream &data, std::string const &filename = string(), PNMFileType *type = nullptr) const;
bool PNMImage::write(PNMWriter *writer) const;

569 8 is_valid 0 4 708 18 PNMImage::is_valid 0 1 233 218
/**
 * Returns true if the image has been read in or correctly initialized with a
 * height and width.  If this returns false, virtually all member functions
 * except clear() and read() are invalid function calls.
 */
43
inline bool PNMImage::is_valid(void) const;

570 16 set_num_channels 0 4 708 26 PNMImage::set_num_channels 0 1 234 257
/**
 * Changes the number of channels associated with the image.  The new number
 * of channels must be an integer in the range 1 through 4, inclusive.  This
 * will allocate and/or deallocate memory as necessary to accommodate; see
 * set_color_type().
 */
57
inline void PNMImage::set_num_channels(int num_channels);

571 14 set_color_type 0 4 708 24 PNMImage::set_color_type 0 1 235 319
/**
 * Translates the image to or from grayscale, color, or four-color mode.
 * Grayscale images are converted to full-color images with R, G, B set to the
 * original gray level; color images are converted to grayscale according to
 * the value of Bright().  The alpha channel, if added, is initialized to
 * zero.
 */
68
void PNMImage::set_color_type(PNMImageHeader::ColorType color_type);

572 15 set_color_space 0 4 708 25 PNMImage::set_color_space 0 1 236 634
/**
 * Converts the colors in the image to the indicated color space.  This may be
 * a lossy operation, in particular when going from sRGB to linear.  The alpha
 * channel remains untouched.
 *
 * Note that, because functions like get_xel() and set_xel() work on
 * linearized floating-point values, this conversion won't affect those values
 * (aside from some minor discrepancies due to storage precision).  It does
 * affect the values used by get_xel_val() and set_xel_val(), though, since
 * those operate on encoded colors.
 *
 * Some color spaces, particularly scRGB, may enforce the use of a particular
 * maxval setting.
 */
55
void PNMImage::set_color_space(ColorSpace color_space);

573 9 add_alpha 0 4 708 19 PNMImage::add_alpha 0 1 237 126
/**
 * Adds an alpha channel to the image, if it does not already have one.  The
 * alpha channel is initialized to zeros.
 */
38
inline void PNMImage::add_alpha(void);

574 12 remove_alpha 0 4 708 22 PNMImage::remove_alpha 0 1 238 59
/**
 * Removes the image's alpha channel, if it exists.
 */
41
inline void PNMImage::remove_alpha(void);

575 14 make_grayscale 0 4 708 24 PNMImage::make_grayscale 0 2 239 240 337
/**
 * Converts the image from RGB to grayscale.  Any alpha channel, if present,
 * is left undisturbed.
 */

/**
 * Converts the image from RGB to grayscale.  Any alpha channel, if present,
 * is left undisturbed.  The optional rc, gc, bc values represent the relative
 * weights to apply to each channel to convert it to grayscale.
 */
104
inline void PNMImage::make_grayscale(void);
void PNMImage::make_grayscale(float rc, float gc, float bc);

576 8 make_rgb 0 4 708 18 PNMImage::make_rgb 0 1 241 108
/**
 * Converts the image from grayscale to RGB.  Any alpha channel, if present,
 * is left undisturbed.
 */
37
inline void PNMImage::make_rgb(void);

577 17 premultiply_alpha 0 4 708 27 PNMImage::premultiply_alpha 0 1 242 234
/**
 * Converts an image in-place to its "premultiplied" form, where, for every
 * pixel in the image, the red, green, and blue components are multiplied by
 * that pixel's alpha value.
 *
 * This does not modify any alpha values.
 */
39
void PNMImage::premultiply_alpha(void);

578 19 unpremultiply_alpha 0 4 708 29 PNMImage::unpremultiply_alpha 0 1 243 273
/**
 * Converts an image in-place to its "straight alpha" form (presumably from a
 * "premultiplied" form), where, for every pixel in the image, the red, green,
 * and blue components are divided by that pixel's alpha value.
 *
 * This does not modify any alpha values.
 */
41
void PNMImage::unpremultiply_alpha(void);

579 12 reverse_rows 0 4 708 22 PNMImage::reverse_rows 0 1 244 61
/**
 * Performs an in-place reversal of the row (y) data.
 */
34
void PNMImage::reverse_rows(void);

580 4 flip 0 4 708 14 PNMImage::flip 0 1 245 369
/**
 * Reverses, transposes, and/or rotates the image in-place according to the
 * specified parameters.  If flip_x is true, the x axis is reversed; if flip_y
 * is true, the y axis is reversed.  Then, if transpose is true, the x and y
 * axes are exchanged.  These parameters can be used to select any combination
 * of 90-degree or 180-degree rotations and flips.
 */
62
void PNMImage::flip(bool flip_x, bool flip_y, bool transpose);

581 10 set_maxval 0 4 708 20 PNMImage::set_maxval 0 1 246 54
/**
 * Rescales the image to the indicated maxval.
 */
41
void PNMImage::set_maxval(xelval maxval);

582 11 get_xel_val 0 4 708 21 PNMImage::get_xel_val 0 2 247 248 206
/**
 * Returns the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval.
 */

/**
 * Returns the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval.
 */
102
inline xel &PNMImage::get_xel_val(int x, int y);
inline xel PNMImage::get_xel_val(int x, int y) const;

583 11 set_xel_val 0 4 708 21 PNMImage::set_xel_val 0 3 249 250 251 740
/**
 * Changes the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval, encoded in the configured color space.  See set_xel if you
 * instead have a linearized and normalized floating-point value.
 */

/**
 * Changes the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval, encoded in the configured color space.  See set_xel if you
 * instead have a linearized and normalized floating-point value.
 */

/**
 * Changes all three color components at the indicated pixel to the same
 * value.  The value is in the range component is in the range 0..maxval,
 * encoded in the configured color space.  See set_xel if you instead have a
 * linearized and normalized floating-point value.
 */
207
inline void PNMImage::set_xel_val(int x, int y, xel const &value);
inline void PNMImage::set_xel_val(int x, int y, xelval r, xelval g, xelval b);
inline void PNMImage::set_xel_val(int x, int y, xelval gray);

584 11 get_red_val 0 4 708 21 PNMImage::get_red_val 0 1 252 158
/**
 * Returns the red component color at the indicated pixel.  The value returned
 * is in the range 0..maxval and encoded in the configured color space.
 */
56
inline xelval PNMImage::get_red_val(int x, int y) const;

585 13 get_green_val 0 4 708 23 PNMImage::get_green_val 0 1 253 163
/**
 * Returns the green component color at the indicated pixel.  The value
 * returned is in the range 0..maxval and encoded in the configured color
 * space.
 */
58
inline xelval PNMImage::get_green_val(int x, int y) const;

586 12 get_blue_val 0 4 708 22 PNMImage::get_blue_val 0 1 254 162
/**
 * Returns the blue component color at the indicated pixel.  The value
 * returned is in the range 0..maxval and encoded in the configured color
 * space.
 */
57
inline xelval PNMImage::get_blue_val(int x, int y) const;

587 12 get_gray_val 0 4 708 22 PNMImage::get_gray_val 0 1 255 342
/**
 * Returns the gray component color at the indicated pixel.  This only has a
 * meaningful value for grayscale images; for other image types, this returns
 * the value of the blue channel only.  However, also see the get_bright()
 * function.  The value returned is in the range 0..maxval and encoded in the
 * configured color space.
 */
57
inline xelval PNMImage::get_gray_val(int x, int y) const;

588 13 get_alpha_val 0 4 708 23 PNMImage::get_alpha_val 0 1 256 196
/**
 * Returns the alpha component color at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value returned is in the
 * range 0..maxval and always linear.
 */
58
inline xelval PNMImage::get_alpha_val(int x, int y) const;

589 11 set_red_val 0 4 708 21 PNMImage::set_red_val 0 1 257 249
/**
 * Sets the red component color only at the indicated pixel.  The value given
 * should be in the range 0..maxval, encoded in the configured color space.
 * See set_red if you instead have a linearized and normalized floating-point
 * value.
 */
58
inline void PNMImage::set_red_val(int x, int y, xelval r);

590 13 set_green_val 0 4 708 23 PNMImage::set_green_val 0 1 258 254
/**
 * Sets the green component color only at the indicated pixel.  The value
 * given should be in the range 0..maxval, encoded in the configured color
 * space.  See set_green if you instead have a linearized and normalized
 * floating-point value.
 */
60
inline void PNMImage::set_green_val(int x, int y, xelval g);

591 12 set_blue_val 0 4 708 22 PNMImage::set_blue_val 0 1 259 251
/**
 * Sets the blue component color only at the indicated pixel.  The value given
 * should be in the range 0..maxval, encoded in the configured color space.
 * See set_blue if you instead have a linearized and normalized floating-point
 * value.
 */
59
inline void PNMImage::set_blue_val(int x, int y, xelval b);

592 12 set_gray_val 0 4 708 22 PNMImage::set_gray_val 0 1 260 530
/**
 * Sets the gray component color at the indicated pixel.  This is only
 * meaningful for grayscale images; for other image types, this simply sets
 * the blue component color.  However, also see set_xel_val(), which can set
 * all the component colors to the same grayscale level, and hence works
 * correctly both for grayscale and color images.  The value given should be
 * in the range 0..maxval, encoded in the configured color space.  See
 * set_gray if you instead have a linearized normalized floating-point value.
 */
62
inline void PNMImage::set_gray_val(int x, int y, xelval gray);

593 13 set_alpha_val 0 4 708 23 PNMImage::set_alpha_val 0 1 261 282
/**
 * Sets the alpha component color only at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value given should be in the
 * range 0..maxval.
 *
 * This value is always linearly encoded, even if the image is set to the sRGB
 * color space.
 */
60
inline void PNMImage::set_alpha_val(int x, int y, xelval a);

594 15 get_channel_val 0 4 708 25 PNMImage::get_channel_val 0 1 262 326
/**
 * Returns the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than accessing the component
 * values directly by named methods.  The value returned is in the range
 * 0..maxval.
 */
66
xelval PNMImage::get_channel_val(int x, int y, int channel) const;

595 15 set_channel_val 0 4 708 25 PNMImage::set_channel_val 0 1 263 325
/**
 * Sets the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than setting the component
 * values directly by named methods.  The value given should be in the range
 * 0..maxval.
 */
72
void PNMImage::set_channel_val(int x, int y, int channel, xelval value);

596 11 get_channel 0 4 708 21 PNMImage::get_channel 0 1 264 329
/**
 * Returns the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than accessing the component
 * values directly by named methods.  The value returned is a float in the
 * range 0..1.
 */
61
float PNMImage::get_channel(int x, int y, int channel) const;

597 11 set_channel 0 4 708 21 PNMImage::set_channel 0 1 265 328
/**
 * Sets the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than setting the component
 * values directly by named methods.  The value given should be a float in the
 * range 0..1.
 */
67
void PNMImage::set_channel(int x, int y, int channel, float value);

598 9 get_pixel 0 4 708 19 PNMImage::get_pixel 0 1 266 100
/**
 * Returns the (r, g, b, a) pixel value at the indicated pixel, using a
 * PixelSpec object.
 */
66
PNMImageHeader::PixelSpec PNMImage::get_pixel(int x, int y) const;

599 9 set_pixel 0 4 708 19 PNMImage::set_pixel 0 1 267 97
/**
 * Sets the (r, g, b, a) pixel value at the indicated pixel, using a PixelSpec
 * object.
 */
79
void PNMImage::set_pixel(int x, int y, PNMImageHeader::PixelSpec const &pixel);

600 7 get_xel 0 4 708 17 PNMImage::get_xel 0 1 268 116
/**
 * Returns the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */
56
inline LRGBColorf PNMImage::get_xel(int x, int y) const;

601 7 set_xel 0 4 708 17 PNMImage::set_xel 0 3 269 270 271 378
/**
 * Changes the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */

/**
 * Changes the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */

/**
 * Changes all three color components at the indicated pixel to the same
 * value.  The value is a linearized float in the range 0..1.
 */
198
inline void PNMImage::set_xel(int x, int y, LRGBColorf const &value);
inline void PNMImage::set_xel(int x, int y, float r, float g, float b);
inline void PNMImage::set_xel(int x, int y, float gray);

602 9 get_xel_a 0 4 708 19 PNMImage::get_xel_a 0 1 272 117
/**
 * Returns the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */
55
inline LColorf PNMImage::get_xel_a(int x, int y) const;

603 9 set_xel_a 0 4 708 19 PNMImage::set_xel_a 0 2 273 274 236
/**
 * Changes the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */

/**
 * Changes the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */
151
inline void PNMImage::set_xel_a(int x, int y, LColorf const &value);
inline void PNMImage::set_xel_a(int x, int y, float r, float g, float b, float a);

604 7 get_red 0 4 708 17 PNMImage::get_red 0 1 275 130
/**
 * Returns the red component color at the indicated pixel.  The value returned
 * is a linearized float in the range 0..1.
 */
51
inline float PNMImage::get_red(int x, int y) const;

605 9 get_green 0 4 708 19 PNMImage::get_green 0 1 276 132
/**
 * Returns the green component color at the indicated pixel.  The value
 * returned is a linearized float in the range 0..1.
 */
53
inline float PNMImage::get_green(int x, int y) const;

606 8 get_blue 0 4 708 18 PNMImage::get_blue 0 1 277 131
/**
 * Returns the blue component color at the indicated pixel.  The value
 * returned is a linearized float in the range 0..1.
 */
52
inline float PNMImage::get_blue(int x, int y) const;

607 8 get_gray 0 4 708 18 PNMImage::get_gray 0 1 278 311
/**
 * Returns the gray component color at the indicated pixel.  This only has a
 * meaningful value for grayscale images; for other image types, this returns
 * the value of the blue channel only.  However, also see the get_bright()
 * function.  The value returned is a linearized float in the range 0..1.
 */
52
inline float PNMImage::get_gray(int x, int y) const;

608 9 get_alpha 0 4 708 19 PNMImage::get_alpha 0 1 279 181
/**
 * Returns the alpha component color at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value returned is a float in
 * the range 0..1.
 */
53
inline float PNMImage::get_alpha(int x, int y) const;

609 7 set_red 0 4 708 17 PNMImage::set_red 0 1 280 136
/**
 * Sets the red component color only at the indicated pixel.  The value given
 * should be a linearized float in the range 0..1.
 */
53
inline void PNMImage::set_red(int x, int y, float r);

610 9 set_green 0 4 708 19 PNMImage::set_green 0 1 281 138
/**
 * Sets the green component color only at the indicated pixel.  The value
 * given should be a linearized float in the range 0..1.
 */
55
inline void PNMImage::set_green(int x, int y, float g);

611 8 set_blue 0 4 708 18 PNMImage::set_blue 0 1 282 137
/**
 * Sets the blue component color only at the indicated pixel.  The value given
 * should be a linearized float in the range 0..1.
 */
54
inline void PNMImage::set_blue(int x, int y, float b);

612 8 set_gray 0 4 708 18 PNMImage::set_gray 0 1 283 418
/**
 * Sets the gray component color at the indicated pixel.  This is only
 * meaningful for grayscale images; for other image types, this simply sets
 * the blue component color.  However, also see set_xel(), which can set all
 * the component colors to the same grayscale level, and hence works correctly
 * both for grayscale and color images.  The value given should be a
 * linearized float in the range 0..1.
 */
57
inline void PNMImage::set_gray(int x, int y, float gray);

613 9 set_alpha 0 4 708 19 PNMImage::set_alpha 0 1 284 179
/**
 * Sets the alpha component color only at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value given should be in the
 * range 0..1.
 */
55
inline void PNMImage::set_alpha(int x, int y, float a);

614 10 get_bright 0 4 708 20 PNMImage::get_bright 0 3 285 286 287 660
/**
 * Returns the linear brightness of the given xel, as a linearized float in
 * the range 0..1.  This flavor of get_bright() returns the correct grayscale
 * brightness level for both full-color and grayscale images.
 */

/**
 * This flavor of get_bright() works correctly only for color images.  It
 * returns a single brightness value for the RGB color at the indicated pixel,
 * based on the supplied weights for each component.
 */

/**
 * This flavor of get_bright() works correctly only for four-channel images.
 * It returns a single brightness value for the RGBA color at the indicated
 * pixel, based on the supplied weights for each component.
 */
234
inline float PNMImage::get_bright(int x, int y) const;
inline float PNMImage::get_bright(int x, int y, float rc, float gc, float bc) const;
inline float PNMImage::get_bright(int x, int y, float rc, float gc, float bc, float ac) const;

615 5 blend 0 4 708 15 PNMImage::blend 0 2 288 289 554
/**
 * Smoothly blends the indicated pixel value in with whatever was already in
 * the image, based on the given alpha value.  An alpha of 1.0 is fully opaque
 * and completely replaces whatever was there previously; alpha of 0.0 is
 * fully transparent and does nothing.
 */

/**
 * Smoothly blends the indicated pixel value in with whatever was already in
 * the image, based on the given alpha value.  An alpha of 1.0 is fully opaque
 * and completely replaces whatever was there previously; alpha of 0.0 is
 * fully transparent and does nothing.
 */
154
inline void PNMImage::blend(int x, int y, LRGBColorf const &val, float alpha);
void PNMImage::blend(int x, int y, float r, float g, float b, float alpha);

616 14 copy_sub_image 0 4 708 24 PNMImage::copy_sub_image 0 1 290 344
/**
 * Copies a rectangular area of another image into a rectangular area of this
 * image.  Both images must already have been initialized.  The upper-left
 * corner of the region in both images is specified, and the size of the area;
 * if the size is omitted, it defaults to the entire other image, or the
 * largest piece that will fit.
 */
134
void PNMImage::copy_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1);

617 15 blend_sub_image 0 4 708 25 PNMImage::blend_sub_image 0 1 291 438
/**
 * Behaves like copy_sub_image(), except the alpha channel of the copy is used
 * to blend the copy into the destination image, instead of overwriting pixels
 * unconditionally.
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each *alpha*
 * value of the source image before applying it to the target image.
 *
 * If pixel_scale is 1.0 and the copy has no alpha channel, this degenerates
 * into copy_sub_image().
 */
160
void PNMImage::blend_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

618 13 add_sub_image 0 4 708 23 PNMImage::add_sub_image 0 1 292 228
/**
 * Behaves like copy_sub_image(), except the copy pixels are added to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * Unlike blend_sub_image(), the alpha channel is not treated specially.
 */
158
void PNMImage::add_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

619 14 mult_sub_image 0 4 708 24 PNMImage::mult_sub_image 0 1 293 233
/**
 * Behaves like copy_sub_image(), except the copy pixels are multiplied to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * Unlike blend_sub_image(), the alpha channel is not treated specially.
 */
159
void PNMImage::mult_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

620 16 darken_sub_image 0 4 708 26 PNMImage::darken_sub_image 0 1 294 437
/**
 * Behaves like copy_sub_image(), but the resulting color will be the darker
 * of the source and destination colors at each pixel (and at each R, G, B, A
 * component value).
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each pixel value
 * of the source image before applying it to the target image.  The scale is
 * applied with the center at 1.0: scaling the pixel value smaller brings it
 * closer to 1.0.
 */
161
void PNMImage::darken_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

621 17 lighten_sub_image 0 4 708 27 PNMImage::lighten_sub_image 0 1 295 329
/**
 * Behaves like copy_sub_image(), but the resulting color will be the lighter
 * of the source and destination colors at each pixel (and at each R, G, B, A
 * component value).
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each pixel value
 * of the source image before applying it to the target image.
 */
162
void PNMImage::lighten_sub_image(PNMImage const &copy, int xto, int yto, int xfrom = 0, int yfrom = 0, int x_size = -1, int y_size = -1, float pixel_scale = 1.0);

622 9 threshold 0 4 708 19 PNMImage::threshold 0 1 296 620
/**
 * Selectively copies each pixel from either one source or another source,
 * depending on the pixel value of the indicated channel of select_image.
 *
 * For each pixel (x, y):
 *
 * s = select_image.get_channel(x, y, channel). Set this image's (x, y) to:
 *
 * lt.get_xel(x, y) if s < threshold, or
 *
 * ge.get_xel(x, y) if s >= threshold
 *
 * Any of select_image, lt, or ge may be the same PNMImge object as this
 * image, or the same as each other; or they may all be different.  All images
 * must be the same size.  As a special case, lt and ge may both be 1x1 images
 * instead of the source image size.
 */
125
void PNMImage::threshold(PNMImage const &select_image, int channel, float threshold, PNMImage const &lt, PNMImage const &ge);

623 20 fill_distance_inside 0 4 708 30 PNMImage::fill_distance_inside 0 1 297 790
/**
 * Replaces this image with a grayscale image whose gray channel represents
 * the linear Manhattan distance from the nearest dark pixel in the given mask
 * image, up to the specified radius value (which also becomes the new
 * maxval).  radius may range from 0 to maxmaxval; smaller values will compute
 * faster.  A dark pixel is defined as one whose pixel value is < threshold.
 *
 * If shrink_from_border is true, then the mask image is considered to be
 * surrounded by a border of dark pixels; otherwise, the border isn't
 * considered.
 *
 * This can be used, in conjunction with threshold, to shrink a mask image
 * inwards by a certain number of pixels.
 *
 * The mask image may be the same image as this one, in which case it is
 * destructively modified by this process.
 */
112
void PNMImage::fill_distance_inside(PNMImage const &mask, float threshold, int radius, bool shrink_from_border);

624 21 fill_distance_outside 0 4 708 31 PNMImage::fill_distance_outside 0 1 298 630
/**
 * Replaces this image with a grayscale image whose gray channel represents
 * the linear Manhattan distance from the nearest white pixel in the given
 * mask image, up to the specified radius value (which also becomes the new
 * maxval).  radius may range from 0 to maxmaxval; smaller values will compute
 * faster.  A white pixel is defined as one whose pixel value is >= threshold.
 *
 * This can be used, in conjunction with threshold, to grow a mask image
 * outwards by a certain number of pixels.
 *
 * The mask image may be the same image as this one, in which case it is
 * destructively modified by this process.
 */
88
void PNMImage::fill_distance_outside(PNMImage const &mask, float threshold, int radius);

625 18 indirect_1d_lookup 0 4 708 28 PNMImage::indirect_1d_lookup 0 1 299 557
/**
 * index_image is a WxH grayscale image, while pixel_values is an Nx1 color
 * (or grayscale) image.  Typically pixel_values will be a 256x1 image.
 *
 * Fills the PNMImage with a new image the same width and height as
 * index_image, with the same number of channels as pixel_values.
 *
 * Each pixel of the new image is computed with the formula:
 *
 * new_image(x, y) = pixel_values(index_image(x, y)[channel], 0)
 *
 * At present, no interpolation is performed; the nearest value in
 * pixel_values is discovered.  This may change in the future.
 */
106
void PNMImage::indirect_1d_lookup(PNMImage const &index_image, int channel, PNMImage const &pixel_values);

626 7 rescale 0 4 708 17 PNMImage::rescale 0 1 300 272
/**
 * Rescales the RGB channel values so that any values in the original image
 * between min_val and max_val are expanded to the range 0 .. 1.  Values below
 * min_val are set to 0, and values above max_val are set to 1. Does not
 * affect the alpha channel, if any.
 */
53
void PNMImage::rescale(float min_val, float max_val);

627 11 render_spot 0 4 708 21 PNMImage::render_spot 0 1 301 450
/**
 * Renders a solid-color circle, with a fuzzy edge, into the center of the
 * PNMImage.  If the PNMImage is non-square, this actually renders an ellipse.
 *
 * The min_radius and max_radius are in the scale 0..1, where 1.0 means the
 * full width of the image.  If min_radius == max_radius, the edge is sharp
 * (but still antialiased); otherwise, the pixels between min_radius and
 * max_radius are smoothly blended between fg and bg colors.
 */
101
void PNMImage::render_spot(LColorf const &fg, LColorf const &bg, float min_radius, float max_radius);

628 13 expand_border 0 4 708 23 PNMImage::expand_border 0 1 302 198
/**
 * Expands the image by the indicated number of pixels on each edge.  The new
 * pixels are set to the indicated color.
 *
 * If any of the values is negative, this actually crops the image.
 */
93
void PNMImage::expand_border(int left, int right, int bottom, int top, LColorf const &color);

629 10 box_filter 0 4 708 20 PNMImage::box_filter 0 1 303 154
/**
 * This flavor of box_filter() will apply the filter over the entire image
 * without resizing or copying; the effect is that of a blur operation.
 */
53
inline void PNMImage::box_filter(float radius = 1.0);

630 15 gaussian_filter 0 4 708 25 PNMImage::gaussian_filter 0 1 304 159
/**
 * This flavor of gaussian_filter() will apply the filter over the entire
 * image without resizing or copying; the effect is that of a blur operation.
 */
58
inline void PNMImage::gaussian_filter(float radius = 1.0);

631 23 unfiltered_stretch_from 0 4 708 33 PNMImage::unfiltered_stretch_from 0 1 305 99
/**
 * Resizes from the indicated image into this one by performing a nearest-
 * point sample.
 */
61
void PNMImage::unfiltered_stretch_from(PNMImage const &copy);

632 15 box_filter_from 0 4 708 25 PNMImage::box_filter_from 0 1 306 260
/**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */
67
void PNMImage::box_filter_from(float radius, PNMImage const &copy);

633 20 gaussian_filter_from 0 4 708 30 PNMImage::gaussian_filter_from 0 1 307 260
/**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */
72
void PNMImage::gaussian_filter_from(float radius, PNMImage const &copy);

634 17 quick_filter_from 0 4 708 27 PNMImage::quick_filter_from 0 1 308 415
/**
 * Resizes from the given image, with a fixed radius of 0.5. This is a very
 * specialized and simple algorithm that doesn't handle dropping below the
 * Nyquist rate very well, but is quite a bit faster than the more general
 * box_filter(), above.  If borders are specified, they will further restrict
 * the size of the resulting image.  There's no point in using
 * quick_box_filter() on a single image.
 */
89
void PNMImage::quick_filter_from(PNMImage const &copy, int xborder = 0, int yborder = 0);

635 14 make_histogram 0 4 708 24 PNMImage::make_histogram 0 1 309 64
/**
 * Computes a histogram of the colors used in the image.
 */
63
void PNMImage::make_histogram(PNMImageHeader::Histogram &hist);

636 8 quantize 0 4 708 18 PNMImage::quantize 0 1 310 284
/**
 * Reduces the number of unique colors in the image to (at most) the given
 * count.  Fewer colors than requested may be left in the image after this
 * operation, but never more.
 *
 * At present, this is only supported on images without an alpha channel.
 *
 * @since 1.10.5
 */
48
void PNMImage::quantize(std::size_t max_colors);

637 17 perlin_noise_fill 0 4 708 27 PNMImage::perlin_noise_fill 0 2 311 312 361
/**
 * Fills the image with a grayscale perlin noise pattern based on the
 * indicated parameters.  Uses set_xel to set the grayscale values.  The sx
 * and sy parameters are in multiples of the size of this image.  See also the
 * PerlinNoise2 class in mathutil.
 */

/**
 * Variant of perlin_noise_fill that uses an existing StackedPerlinNoise2
 * object.
 */
166
void PNMImage::perlin_noise_fill(float sx, float sy, int table_size = 256, unsigned long int seed = 0);
void PNMImage::perlin_noise_fill(StackedPerlinNoise2 &perlin);

638 14 remix_channels 0 4 708 24 PNMImage::remix_channels 0 1 313 126
/**
 * Transforms every pixel using the operation (Ro,Go,Bo) =
 * conv.xform_point(Ri,Gi,Bi); Input must be a color image.
 */
52
void PNMImage::remix_channels(LMatrix4 const &conv);

639 13 gamma_correct 0 4 708 23 PNMImage::gamma_correct 0 1 314 217
/**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * RGB channels, converts it to an image with a gamma curve of to_gamma in the
 * RGB channels.  Does not affect the alpha channel.
 */
70
inline void PNMImage::gamma_correct(float from_gamma, float to_gamma);

640 19 gamma_correct_alpha 0 4 708 29 PNMImage::gamma_correct_alpha 0 1 315 218
/**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * alpha channel, converts it to an image with a gamma curve of to_gamma in
 * the alpha channel.  Does not affect the RGB channels.
 */
76
inline void PNMImage::gamma_correct_alpha(float from_gamma, float to_gamma);

641 14 apply_exponent 0 4 708 24 PNMImage::apply_exponent 0 4 316 317 318 319 868
/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */

/**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */
356
inline void PNMImage::apply_exponent(float gray_exponent);
inline void PNMImage::apply_exponent(float gray_exponent, float alpha_exponent);
inline void PNMImage::apply_exponent(float red_exponent, float green_exponent, float blue_exponent);
void PNMImage::apply_exponent(float red_exponent, float green_exponent, float blue_exponent, float alpha_exponent);

642 15 get_average_xel 0 4 708 25 PNMImage::get_average_xel 0 1 320 71
/**
 * Returns the average color of all of the pixels in the image.
 */
49
LRGBColorf PNMImage::get_average_xel(void) const;

643 17 get_average_xel_a 0 4 708 27 PNMImage::get_average_xel_a 0 1 321 103
/**
 * Returns the average color of all of the pixels in the image, including the
 * alpha channel.
 */
48
LColorf PNMImage::get_average_xel_a(void) const;

644 16 get_average_gray 0 4 708 26 PNMImage::get_average_gray 0 1 322 85
/**
 * Returns the average grayscale component of all of the pixels in the image.
 */
45
float PNMImage::get_average_gray(void) const;

645 16 do_fill_distance 0 4 708 26 PNMImage::do_fill_distance 0 1 323 116
/**
 * Recursively fills in the minimum distance measured from a certain set of
 * points into the gray channel.
 */
55
void PNMImage::do_fill_distance(int xi, int yi, int d);

646 4 size 0 4 709 19 PNMImage::Row::size 0 1 342 47
/**
 * Get the number of pixels in the row.
 */
51
inline std::size_t PNMImage::Row::size(void) const;

647 11 operator [] 0 4 709 26 PNMImage::Row::operator [] 0 1 343 0
55
inline LColorf PNMImage::Row::operator [](int x) const;

648 11 __setitem__ 0 4 709 26 PNMImage::Row::__setitem__ 0 1 344 127
/**
 * Set the pixel at the given column in the row.  If the image has no alpha
 * channel, the alpha component is ignored.
 */
64
inline void PNMImage::Row::__setitem__(int x, LColorf const &v);

649 11 get_xel_val 0 4 709 26 PNMImage::Row::get_xel_val 0 1 345 58
/**
 * Fetch the pixel at the given column in the row.
 */
46
inline xel &PNMImage::Row::get_xel_val(int x);

650 11 set_xel_val 0 4 709 26 PNMImage::Row::set_xel_val 0 1 346 56
/**
 * Set the pixel at the given column in the row.
 */
60
inline void PNMImage::Row::set_xel_val(int x, xel const &v);

651 13 get_alpha_val 0 4 709 28 PNMImage::Row::get_alpha_val 0 1 347 64
/**
 * Fetch the alpha value at the given column in the row.
 */
56
inline xelval PNMImage::Row::get_alpha_val(int x) const;

652 13 set_alpha_val 0 4 709 28 PNMImage::Row::set_alpha_val 0 1 348 62
/**
 * Set the alpha value at the given column in the row.
 */
58
inline void PNMImage::Row::set_alpha_val(int x, xelval v);

653 3 Row 0 260 709 18 PNMImage::Row::Row 0 1 341 8
/**

 */
59
inline PNMImage::Row::Row(PNMImage::Row const &) = default;

654 4 size 0 4 710 20 PNMImage::CRow::size 0 1 350 47
/**
 * Get the number of pixels in the row.
 */
52
inline std::size_t PNMImage::CRow::size(void) const;

655 11 operator [] 0 4 710 27 PNMImage::CRow::operator [] 0 1 351 0
56
inline LColorf PNMImage::CRow::operator [](int x) const;

656 11 get_xel_val 0 4 710 27 PNMImage::CRow::get_xel_val 0 1 352 58
/**
 * Fetch the pixel at the given column in the row.
 */
52
inline xel PNMImage::CRow::get_xel_val(int x) const;

657 13 get_alpha_val 0 4 710 29 PNMImage::CRow::get_alpha_val 0 1 353 64
/**
 * Fetch the alpha value at the given column in the row.
 */
57
inline xelval PNMImage::CRow::get_alpha_val(int x) const;

658 4 CRow 0 260 710 20 PNMImage::CRow::CRow 0 1 349 8
/**

 */
62
inline PNMImage::CRow::CRow(PNMImage::CRow const &) = default;

659 11 operator [] 0 4 708 21 PNMImage::operator [] 0 2 324 325 0
108
inline PNMImage::Row PNMImage::operator [](int y);
inline PNMImage::CRow PNMImage::operator [](int y) const;

660 10 operator ~ 0 68 708 20 PNMImage::operator ~ 0 1 326 0
42
PNMImage PNMImage::operator ~(void) const;

661 10 operator + 0 4 708 20 PNMImage::operator + 0 2 327 328 0
132
inline PNMImage PNMImage::operator +(PNMImage const &other) const;
inline PNMImage PNMImage::operator +(LColorf const &other) const;

662 10 operator - 0 4 708 20 PNMImage::operator - 0 2 329 330 0
132
inline PNMImage PNMImage::operator -(PNMImage const &other) const;
inline PNMImage PNMImage::operator -(LColorf const &other) const;

663 10 operator * 0 4 708 20 PNMImage::operator * 0 3 331 332 333 0
194
inline PNMImage PNMImage::operator *(PNMImage const &other) const;
inline PNMImage PNMImage::operator *(float multiplier) const;
inline PNMImage PNMImage::operator *(LColorf const &other) const;

664 11 operator += 0 4 708 21 PNMImage::operator += 0 2 334 335 0
100
void PNMImage::operator +=(PNMImage const &other);
void PNMImage::operator +=(LColorf const &other);

665 11 operator -= 0 4 708 21 PNMImage::operator -= 0 2 336 337 0
100
void PNMImage::operator -=(PNMImage const &other);
void PNMImage::operator -=(LColorf const &other);

666 11 operator *= 0 4 708 21 PNMImage::operator *= 0 3 338 339 340 0
146
void PNMImage::operator *=(PNMImage const &other);
void PNMImage::operator *=(float multiplier);
void PNMImage::operator *=(LColorf const &other);

667 10 PNMPainter 0 260 711 22 PNMPainter::PNMPainter 0 2 354 355 532
/**
 * The constructor stores a pointer to the PNMImage you pass it, but it does
 * not take ownership of the object; you are responsible for ensuring that the
 * PNMImage does not destruct during the lifetime of the PNMPainter object.
 *
 * The xo, yo coordinates specify an optional offset for fill coordinates.  If
 * you are painting with a pattern fill, these specify the virtual coordinates
 * of the upper-left corner of the image, which can allow you to adjust the
 * pattern to line up with nested images, if necessary.
 */
134
explicit PNMPainter::PNMPainter(PNMImage &image, int xo = 0, int yo = 0);
inline PNMPainter::PNMPainter(PNMPainter const &) = default;

668 11 ~PNMPainter 0 516 711 23 PNMPainter::~PNMPainter 0 0 10
/**
 *
 */
37
inline PNMPainter::~PNMPainter(void);

669 7 set_pen 0 4 711 19 PNMPainter::set_pen 0 1 356 330
/**
 * Specifies a PNMBrush that will be used for drawing lines and edges.  If the
 * brush is a bitmap brush, its image will be smeared pixelwise along the
 * line.
 *
 * Unlike the PNMImage passed to the constructor, the PNMPainter will take
 * ownership of the pen.  It is not necessary to keep a separate pointer to
 * it.
 */
47
inline void PNMPainter::set_pen(PNMBrush *pen);

670 7 get_pen 0 4 711 19 PNMPainter::get_pen 0 1 357 51
/**
 * Returns the current pen.  See set_pen().
 */
49
inline PNMBrush *PNMPainter::get_pen(void) const;

671 8 set_fill 0 4 711 20 PNMPainter::set_fill 0 1 358 343
/**
 * Specifies a PNMBrush that will be used for filling in the interiors of
 * objects.  If the brush is a bitmap brush, its image will be tiled
 * throughout the space.
 *
 * Unlike the PNMImage passed to the constructor, the PNMPainter will take
 * ownership of the fill brush.  It is not necessary to keep a separate
 * pointer to it.
 */
49
inline void PNMPainter::set_fill(PNMBrush *fill);

672 8 get_fill 0 4 711 20 PNMPainter::get_fill 0 1 359 59
/**
 * Returns the current fill brush.  See set_fill().
 */
50
inline PNMBrush *PNMPainter::get_fill(void) const;

673 10 draw_point 0 4 711 22 PNMPainter::draw_point 0 1 360 77
/**
 * Draws an antialiased point on the PNMImage, using the current pen.
 */
53
inline void PNMPainter::draw_point(float x, float y);

674 9 draw_line 0 4 711 21 PNMPainter::draw_line 0 1 361 76
/**
 * Draws an antialiased line on the PNMImage, using the current pen.
 */
67
void PNMPainter::draw_line(float xa, float ya, float xb, float yb);

675 14 draw_rectangle 0 4 711 26 PNMPainter::draw_rectangle 0 1 362 211
/**
 * Draws a filled rectangule on the PNMImage, using the current pen for the
 * outline, and the current fill brush for the interior.
 *
 * The two coordinates specify any two diagonally opposite corners.
 */
72
void PNMPainter::draw_rectangle(float xa, float ya, float xb, float yb);

370
1 0 0 7 2 713 390 0 0 0 
2 0 0 23 2 713 390 0 0 1 4 fill 1 677  
3 0 0 23 2 713 390 0 0 3 1 r 1 677  1 g 1 677  1 b 1 677  
4 0 0 15 2 713 390 0 0 1 6 param0 0 714  
5 0 0 4 3 716 0 0 0 3 4 this 3 713  1 i 1 688  10 assign_val 1 677  
6 0 0 6 3 677 0 0 0 2 4 this 3 714  1 i 1 688  
7 0 0 7 4 713 390 0 0 2 4 this 3 714  5 other 1 714  
8 0 0 7 5 713 390 0 0 2 4 this 3 714  5 other 1 714  
9 0 0 7 6 713 390 0 0 2 4 this 3 714  4 mult 1 717  
10 0 0 6 7 713 0 0 0 2 4 this 3 713  5 other 1 714  
11 0 0 6 8 713 0 0 0 2 4 this 3 713  5 other 1 714  
12 0 0 6 9 713 0 0 0 2 4 this 3 713  4 mult 1 717  
13 0 0 6 10 702 0 0 0 2 4 this 3 713  5 other 1 714  
14 0 0 6 11 702 0 0 0 2 4 this 3 713  5 other 1 714  
15 0 0 6 12 702 0 0 0 2 4 this 3 714  5 other 1 714  
16 0 0 6 13 688 0 0 0 0 
17 0 0 4 14 716 0 0 0 2 4 this 3 713  3 out 1 719  
18 0 0 6 34 723 0 0 0 1 4 this 3 721  
19 0 0 6 35 688 0 0 117 /**
 * Returns the number of different possible filename extensions associated
 * with this particular file type.
 */ 1 4 this 3 721  
20 0 0 6 36 723 0 0 123 /**
 * Returns the nth possible filename extension associated with this particular
 * file type, without a leading dot.
 */ 2 4 this 3 721  1 n 1 688  
21 0 0 6 38 723 0 0 159 /**
 * Returns a suitable filename extension (without a leading dot) to suggest
 * for files of this type, or empty string if no suggestions are available.
 */ 1 4 this 3 721  
22 0 0 7 44 725 0 0 0 0 
23 0 0 15 57 728 400 0 0 1 6 param0 0 726  
24 0 0 6 47 688 0 0 56 /**
 * Returns the total number of types registered.
 */ 1 4 this 3 726  
25 0 0 6 48 685 0 0 43 /**
 * Returns the nth type registered.
 */ 2 4 this 3 726  1 n 1 688  
26 0 0 6 52 685 0 0 203 /**
 * Tries to determine what the PNMFileType is likely to be for a particular
 * image file based on its extension.  Returns a suitable PNMFileType pointer,
 * or NULL if no type can be determined.
 */ 2 4 this 3 726  8 filename 1 723  
27 0 0 6 53 685 0 0 249 /**
 * Tries to determine what the PNMFileType is likely to be for a particular
 * image file based on its magic number, the first two bytes read from the
 * file.  Returns a suitable PNMFileType pointer, or NULL if no type can be
 * determined.
 */ 2 4 this 3 726  12 magic_number 1 723  
28 0 0 6 54 685 0 0 172 /**
 * Returns the PNMFileType instance stored in the registry for the given
 * TypeHandle, e.g.  as retrieved by a previous call to get_type() on the type
 * instance.
 */ 2 4 this 3 726  6 handle 1 725  
29 0 0 4 55 716 0 0 103 /**
 * Writes a list of supported image file types to the indicated output stream,
 * one per line.
 */ 3 4 this 3 726  3 out 1 719  12 indent_level 5 688  
30 0 0 6 56 728 0 0 70 /**
 * Returns a pointer to the global PNMFileTypeRegistry object.
 */ 0 
31 0 0 7 59 729 411 0 10 /**
 *
 */ 0 
32 0 0 15 59 729 411 0 10 /**
 *
 */ 1 4 copy 1 730  
33 0 0 6 60 729 0 0 0 2 4 this 3 729  4 copy 1 730  
34 0 0 6 63 687 0 0 147 /**
 * Returns the image type of the image, as an enumerated value.  This is
 * really just the number of channels cast to the enumerated type.
 */ 1 4 this 3 730  
35 0 0 6 64 688 0 0 55 /**
 * Returns the number of channels in the image.
 */ 1 4 this 3 730  
36 0 0 6 67 702 0 0 275 /**
 * Returns false if the image is a full-color image, and has red, green, and
 * blue components; true if it is a grayscale image and has only a gray
 * component.  (The gray color is actually stored in the blue channel, and the
 * red and green channels are ignored.)
 */ 1 4 this 3 730  
37 0 0 6 67 702 0 0 139 /**
 * This static variant of is_grayscale() returns true if the indicated image
 * type represents a grayscale image, false otherwise.
 */ 1 10 color_type 1 687  
38 0 0 6 68 702 0 0 204 /**
 * Returns true if the image includes an alpha channel, false otherwise.
 * Unlike is_grayscale(), if this returns false it is an error to call any of
 * the functions accessing the alpha channel.
 */ 1 4 this 3 730  
39 0 0 6 68 702 0 0 133 /**
 * This static variant of has_alpha() returns true if the indicated image type
 * includes an alpha channel, false otherwise.
 */ 1 10 color_type 1 687  
40 0 0 6 69 689 0 0 183 /**
 * Returns the maximum channel value allowable for any pixel in this image;
 * for instance, 255 for a typical 8-bit-per-channel image.  A pixel with this
 * value is full on.
 */ 1 4 this 3 730  
41 0 0 6 70 690 0 0 97 /**
 * Returns the color space that the image is encoded in, or CS_unspecified if
 * unknown.
 */ 1 4 this 3 730  
42 0 0 6 75 688 0 0 121 /**
 * Returns the number of pixels in the X direction.  This is one more than the
 * largest allowable X coordinate.
 */ 1 4 this 3 730  
43 0 0 6 76 688 0 0 121 /**
 * Returns the number of pixels in the Y direction.  This is one more than the
 * largest allowable Y coordinate.
 */ 1 4 this 3 730  
44 0 0 7 77 732 0 0 119 /**
 * Returns the number of pixels in each direction.  This is one more than the
 * largest allowable coordinates.
 */ 1 4 this 3 730  
45 0 0 6 80 723 0 0 47 /**
 * Gets the user comment from the file.
 */ 1 4 this 3 730  
46 0 0 4 81 716 0 0 62 /**
 * Writes a user comment string to the image (header).
 */ 2 4 this 3 729  7 comment 1 723  
47 0 0 6 83 702 0 0 85 /**
 * Returns true if the PNMImageHeader knows what type it is, false otherwise.
 */ 1 4 this 3 730  
48 0 0 6 84 685 0 0 130 /**
 * If the file type is known (e.g.  has_type() returns true), returns its
 * PNMFileType pointer; otherwise, returns NULL.
 */ 1 4 this 3 730  
49 0 0 4 85 716 0 0 294 /**
 * Sets the file type of this PNMImage.  This will be the default type used
 * when an image is read, if the type cannot be determined by magic number or
 * inferred by extension, or the type used when the image is written, if the
 * type cannot be inferred from the filename extension.
 */ 2 4 this 3 729  4 type 1 685  
50 0 0 6 87 702 0 0 220 /**
 * Opens up the image file and tries to read its header information to
 * determine its size, number of channels, etc.  If successful, updates the
 * header information and returns true; otherwise, returns false.
 */ 4 4 this 3 729  8 filename 1 733  4 type 5 685  19 report_unknown_type 5 702  
51 0 0 6 87 702 0 0 397 /**
 * Reads the image header information only from the indicated stream.
 *
 * The filename is advisory only, and may be used to suggest a type if it has
 * a known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of file it is (and a
 * non-NULL type will override any magic number test or filename extension
 * lookup).
 *
 * Returns true if successful, false on error.
 */ 5 4 this 3 729  4 data 1 736  8 filename 5 723  4 type 5 685  19 report_unknown_type 5 702  
52 0 0 6 88 738 0 0 366 /**
 * Returns a newly-allocated PNMReader of the suitable type for reading from
 * the indicated image filename, or NULL if the filename cannot be read for
 * some reason.  The filename "-" always stands for standard input.  If type
 * is specified, it is a suggestion for the file type to use.
 *
 * The PNMReader should be deleted when it is no longer needed.
 */ 4 4 this 3 730  8 filename 1 733  4 type 5 685  19 report_unknown_type 5 702  
53 0 0 6 88 738 0 0 917 /**
 * Returns a newly-allocated PNMReader of the suitable type for reading from
 * the already-opened image file, or NULL if the file cannot be read for some
 * reason.
 *
 * owns_file should be set true if the PNMReader is to be considered the owner
 * of the stream pointer (in which case the stream will be deleted on
 * completion, whether successful or not), or false if it should not delete
 * it.
 *
 * The filename parameter is optional here, since the file has already been
 * opened; it is only used to examine the extension and attempt to guess the
 * file type.
 *
 * If magic_number is nonempty, it is assumed to represent the first few bytes
 * that have already been read from the file.  Some file types may have
 * difficulty if this is more than two bytes.
 *
 * If type is non-NULL, it is a suggestion for the file type to use.
 *
 * The PNMReader should be deleted when it is no longer needed.
 */ 7 4 this 3 730  4 file 1 736  9 owns_file 5 702  8 filename 5 733  12 magic_number 5 723  4 type 5 685  19 report_unknown_type 5 702  
54 0 0 6 89 740 0 0 371 /**
 * Returns a newly-allocated PNMWriter of the suitable type for writing an
 * image to the indicated filename, or NULL if the filename cannot be written
 * for some reason.  The filename "-" always stands for standard output.  If
 * type is specified, it is a suggestion for the file type to use.
 *
 * The PNMWriter should be deleted when it is no longer needed.
 */ 3 4 this 3 730  8 filename 1 733  4 type 5 685  
55 0 0 6 89 740 0 0 727 /**
 * Returns a newly-allocated PNMWriter of the suitable type for writing to the
 * already-opened image file, or NULL if the file cannot be written for some
 * reason.
 *
 * owns_file should be set true if the PNMWriter is to be considered the owner
 * of the stream pointer (in which case the stream will be deleted on
 * completion, whether successful or not), or false if it should not delete
 * it.
 *
 * The filename parameter is optional here, since the file has already been
 * opened; it is only used to examine the extension and attempt to guess the
 * intended file type.
 *
 * If type is non-NULL, it is a suggestion for the file type to use.
 *
 * The PNMWriter should be deleted when it is no longer needed.
 */ 5 4 this 3 730  4 file 1 719  9 owns_file 5 702  8 filename 5 733  4 type 5 685  
56 0 0 4 91 716 0 0 10 /**
 *
 */ 2 4 this 3 730  3 out 1 719  
57 0 0 15 93 744 447 0 0 1 6 param0 0 742  
58 0 0 23 93 744 447 0 10 /**
 *
 */ 1 3 rgb 1 745  
59 0 0 23 93 744 447 0 10 /**
 *
 */ 2 3 rgb 1 745  5 alpha 1 689  
60 0 0 23 93 744 447 0 10 /**
 *
 */ 1 10 gray_value 1 689  
61 0 0 23 93 744 447 0 10 /**
 *
 */ 2 10 gray_value 1 689  5 alpha 1 689  
62 0 0 23 93 744 447 0 10 /**
 *
 */ 3 3 red 1 689  5 green 1 689  4 blue 1 689  
63 0 0 23 93 744 447 0 10 /**
 *
 */ 4 3 red 1 689  5 green 1 689  4 blue 1 689  5 alpha 1 689  
64 0 0 6 94 702 0 0 0 2 4 this 3 742  5 other 1 742  
65 0 0 6 95 702 0 0 0 2 4 this 3 742  5 other 1 742  
66 0 0 6 96 702 0 0 0 2 4 this 3 742  5 other 1 742  
67 0 0 6 97 688 0 0 10 /**
 *
 */ 2 4 this 3 742  5 other 1 742  
68 0 0 6 98 689 0 0 10 /**
 *
 */ 1 4 this 3 742  
69 0 0 6 99 689 0 0 10 /**
 *
 */ 1 4 this 3 742  
70 0 0 6 100 689 0 0 10 /**
 *
 */ 1 4 this 3 742  
71 0 0 6 101 689 0 0 10 /**
 *
 */ 1 4 this 3 742  
72 0 0 4 102 716 0 0 10 /**
 *
 */ 2 4 this 3 744  3 red 1 689  
73 0 0 4 103 716 0 0 10 /**
 *
 */ 2 4 this 3 744  5 green 1 689  
74 0 0 4 104 716 0 0 10 /**
 *
 */ 2 4 this 3 744  4 blue 1 689  
75 0 0 4 105 716 0 0 10 /**
 *
 */ 2 4 this 3 744  5 alpha 1 689  
76 0 0 6 106 689 0 0 0 2 4 this 3 742  1 n 1 688  
77 0 0 6 107 688 0 0 135 /**
 * Specifies the number of components in the PixelSpec; this is always 4,
 * regardless of the type of image it was taken from.
 */ 0 
78 0 0 4 108 716 0 0 10 /**
 *
 */ 2 4 this 3 742  3 out 1 719  
79 0 0 15 111 749 449 0 0 1 6 param0 0 747  
80 0 0 7 120 750 455 0 10 /**
 *
 */ 0 
81 0 0 15 120 750 455 0 0 1 6 param0 0 751  
82 0 0 6 121 688 0 0 70 /**
 * Returns the number of unique pixel colors in the histogram.
 */ 1 4 this 3 751  
83 0 0 6 122 742 0 0 130 /**
 * Returns the nth unique pixel color in the histogram.  These are ordered by
 * default from most common to least common.
 */ 2 4 this 3 751  1 n 1 688  
84 0 0 6 123 688 0 0 90 /**
 * Returns the number of occurrences in the image of the indicated pixel
 * color.
 */ 2 4 this 3 751  5 pixel 1 742  
85 0 0 6 123 688 0 0 108 /**
 * Returns the number of occurrences in the image of the nth unique pixel
 * color in the histogram.
 */ 2 4 this 3 751  1 n 1 688  
86 0 0 4 125 716 0 0 10 /**
 *
 */ 2 4 this 3 751  3 out 1 719  
87 0 0 7 128 753 538 0 10 /**
 *
 */ 0 
88 0 0 15 128 753 538 0 10 /**
 *
 */ 1 4 copy 1 754  
89 0 0 6 129 753 0 0 0 2 4 this 3 753  4 copy 1 754  
90 0 0 4 130 716 0 0 43 /**
 * Eliminates all data in the file.
 */ 1 4 this 3 753  
91 0 0 4 130 716 0 0 207 /**
 * Resets to an empty table with a specific size.  The case of num_channels ==
 * 0 is allowed only in the case that x_size and y_size are also == 0; and
 * this makes an empty (and invalid) PfmFile.
 */ 4 4 this 3 753  6 x_size 1 688  6 y_size 1 688  12 num_channels 1 688  
92 0 0 6 131 702 0 0 240 /**
 * Reads the PFM data from the indicated file, returning true on success,
 * false on failure.
 *
 * This can also handle reading a standard image file supported by PNMImage;
 * it will be quietly converted to a floating-point type.
 */ 2 4 this 3 753  8 fullpath 1 733  
93 0 0 6 131 702 0 0 142 /**
 * Reads the PFM data using the indicated PNMReader.
 *
 * The PNMReader is always deleted upon completion, whether successful or not.
 */ 2 4 this 3 753  6 reader 1 738  
94 0 0 6 131 702 0 0 242 /**
 * Reads the PFM data from the indicated stream, returning true on success,
 * false on failure.
 *
 * This can also handle reading a standard image file supported by PNMImage;
 * it will be quietly converted to a floating-point type.
 */ 3 4 this 3 753  2 in 1 736  8 fullpath 5 733  
95 0 0 6 132 702 0 0 313 /**
 * Writes the PFM data to the indicated file, returning true on success, false
 * on failure.
 *
 * If the type implied by the filename extension supports floating-point, the
 * data will be written directly; otherwise, the floating-point data will be
 * quietly converted to the appropriate integer type.
 */ 2 4 this 3 753  8 fullpath 1 733  
96 0 0 6 132 702 0 0 143 /**
 * Writes the PFM data using the indicated PNMWriter.
 *
 * The PNMWriter is always deleted upon completion, whether successful or not.
 */ 2 4 this 3 753  6 writer 1 740  
97 0 0 6 132 702 0 0 103 /**
 * Writes the PFM data to the indicated stream, returning true on success,
 * false on failure.
 */ 3 4 this 3 753  3 out 1 719  8 fullpath 5 733  
98 0 0 6 133 702 0 0 110 /**
 * Fills the PfmFile with the data from the indicated PNMImage, converted to
 * floating-point values.
 */ 2 4 this 3 753  8 pnmimage 1 756  
99 0 0 6 134 702 0 0 79 /**
 * Copies the data to the indicated PNMImage, converting to RGB values.
 */ 2 4 this 3 754  8 pnmimage 1 758  
100 0 0 6 135 702 0 0 180 /**
 * Stores 1 or 0 values into the indicated PNMImage, according to has_point()
 * for each pixel.  Each valid point gets a 1 value; each nonexistent point
 * gets a 0 value.
 */ 2 4 this 3 754  8 pnmimage 1 758  
101 0 0 6 135 702 0 0 424 /**
 * Stores 1 or 0 values into the indicated PNMImage, according to has_point()
 * for each pixel.  Each valid point gets a 1 value; each nonexistent point
 * gets a 0 value.
 *
 * This flavor of store_mask also checks whether the valid points are within
 * the specified min/max range.  Any valid points without the condition
 * min_point[c] <= value[c] <= max_point[c], for any c, are stored with a 0 in
 * the mask.
 */ 4 4 this 3 754  8 pnmimage 1 758  9 min_point 1 759  9 max_point 1 759  
102 0 0 6 136 702 0 0 10 /**
 *
 */ 1 4 this 3 754  
103 0 0 6 139 703 0 0 81 /**
 * The "scale" is reported in the pfm header and is probably meaningless.
 */ 1 4 this 3 754  
104 0 0 4 140 716 0 0 81 /**
 * The "scale" is reported in the pfm header and is probably meaningless.
 */ 2 4 this 3 753  5 scale 1 703  
105 0 0 6 144 702 0 0 205 /**
 * Returns true if there is a valid point at x, y.  This always returns true
 * unless a "no data" value has been set, in which case it returns false if
 * the point at x, y is the "no data" value.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
106 0 0 6 145 703 0 0 77 /**
 * Returns the cth channel of the point value at the indicated point.
 */ 4 4 this 3 754  1 x 1 688  1 y 1 688  1 c 1 688  
107 0 0 4 146 716 0 0 78 /**
 * Replaces the cth channel of the point value at the indicated point.
 */ 5 4 this 3 753  1 x 1 688  1 y 1 688  1 c 1 688  5 value 1 703  
108 0 0 6 147 703 0 0 70 /**
 * Returns the 1-component point value at the indicated point.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
109 0 0 4 148 716 0 0 71 /**
 * Replaces the 1-component point value at the indicated point.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 703  
110 0 0 6 149 762 0 0 137 /**
 * Returns the 2-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
111 0 0 4 150 716 0 0 138 /**
 * Replaces the 2-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 765  
112 0 0 4 150 716 0 0 138 /**
 * Replaces the 2-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 768  
113 0 0 6 151 771 0 0 79 /**
 * Returns a modifiable 2-component point value at the indicated point.
 */ 3 4 this 3 753  1 x 1 688  1 y 1 688  
114 0 0 6 152 772 0 0 137 /**
 * Returns the 3-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
115 0 0 4 153 716 0 0 138 /**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 775  
116 0 0 4 153 716 0 0 138 /**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 778  
117 0 0 6 154 781 0 0 79 /**
 * Returns a modifiable 3-component point value at the indicated point.
 */ 3 4 this 3 753  1 x 1 688  1 y 1 688  
118 0 0 6 155 772 0 0 137 /**
 * Returns the 3-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
119 0 0 4 156 716 0 0 138 /**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 775  
120 0 0 4 156 716 0 0 138 /**
 * Replaces the 3-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 778  
121 0 0 6 157 781 0 0 79 /**
 * Returns a modifiable 3-component point value at the indicated point.
 */ 3 4 this 3 753  1 x 1 688  1 y 1 688  
122 0 0 6 158 782 0 0 137 /**
 * Returns the 4-component point value at the indicated point.  In a 1-channel
 * image, the channel value is in the x component.
 */ 3 4 this 3 754  1 x 1 688  1 y 1 688  
123 0 0 4 159 716 0 0 138 /**
 * Replaces the 4-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 785  
124 0 0 4 159 716 0 0 138 /**
 * Replaces the 4-component point value at the indicated point.  In a
 * 1-channel image, the channel value is in the x component.
 */ 4 4 this 3 753  1 x 1 688  1 y 1 688  5 point 1 759  
125 0 0 6 160 788 0 0 79 /**
 * Returns a modifiable 4-component point value at the indicated point.
 */ 3 4 this 3 753  1 x 1 688  1 y 1 688  
126 0 0 4 161 716 0 0 54 /**
 * Fills the table with all of the same value.
 */ 2 4 this 3 753  5 value 1 762  
127 0 0 4 161 716 0 0 54 /**
 * Fills the table with all of the same value.
 */ 2 4 this 3 753  5 value 1 772  
128 0 0 4 161 716 0 0 54 /**
 * Fills the table with all of the same value.
 */ 2 4 this 3 753  5 value 1 782  
129 0 0 4 161 716 0 0 54 /**
 * Fills the table with all of the same value.
 */ 2 4 this 3 753  5 value 1 703  
130 0 0 4 162 716 0 0 40 /**
 * Fills the table with all NaN.
 */ 1 4 this 3 753  
131 0 0 4 163 716 0 0 86 /**
 * Fills the table with the current no_data value, so that the table is empty.
 */ 1 4 this 3 753  
132 0 0 4 164 716 0 0 107 /**
 * Fills the indicated channel with all of the same value, leaving the other
 * channels unchanged.
 */ 3 4 this 3 753  7 channel 1 688  5 value 1 703  
133 0 0 4 165 716 0 0 86 /**
 * Fills the indicated channel with NaN, leaving the other channels unchanged.
 */ 2 4 this 3 753  7 channel 1 688  
134 0 0 4 166 716 0 0 152 /**
 * Fills the indicated channel with all of the same value, but only where the
 * table already has a data point.  Leaves empty points unchanged.
 */ 3 4 this 3 753  7 channel 1 688  5 value 1 703  
135 0 0 4 167 716 0 0 134 /**
 * Fills the indicated channel with NaN, but only where the table already has
 * a data point.  Leaves empty points unchanged.
 */ 2 4 this 3 753  7 channel 1 688  
136 0 0 6 168 702 0 0 296 /**
 * Computes the unweighted average point of all points within the box centered
 * at (x, y) with the indicated Manhattan-distance radius.  Missing points are
 * assigned the value of their nearest neighbor.  Returns true if successful,
 * or false if the point value cannot be determined.
 */ 5 4 this 3 754  6 result 1 781  1 x 1 703  1 y 1 703  6 radius 1 703  
137 0 0 6 169 702 0 0 190 /**
 * Computes the weighted average of the four nearest points to the floating-
 * point index (x, y).  Returns true if the point has any contributors, false
 * if the point is unknown.
 */ 4 4 this 3 754  6 result 1 781  1 x 1 703  1 y 1 703  
138 0 0 6 170 702 0 0 246 /**
 * Calculates the minimum and maximum x, y, and z depth component values,
 * representing the bounding box of depth values, and places them in the
 * indicated vectors.  Returns true if successful, false if the mesh contains
 * no points.
 */ 3 4 this 3 754  10 min_points 1 789  10 max_points 1 789  
139 0 0 6 171 702 0 0 250 /**
 * Computes the minimum range of x and y across the PFM file that include all
 * points.  If there are no points with no_data_value in the grid--that is,
 * all points are included--then this will return (0, get_x_size(), 0,
 * get_y_size()).
 */ 2 4 this 3 754  5 range 1 790  
140 0 0 6 171 702 0 0 250 /**
 * Computes the minimum range of x and y across the PFM file that include all
 * points.  If there are no points with no_data_value in the grid--that is,
 * all points are included--then this will return (0, get_x_size(), 0,
 * get_y_size()).
 */ 2 4 this 3 754  5 range 1 791  
141 0 0 6 172 702 0 0 160 /**
 * Returns true if all of the points on row y, in the range [x_begin, x_end),
 * are the no_data value, or false if any one of these points has a value.
 */ 4 4 this 3 754  1 y 1 688  7 x_begin 1 688  5 x_end 1 688  
142 0 0 6 173 702 0 0 155 /**
 * Returns true if all of the points on column x, from [y_begin, y_end), are
 * the no_data value, or false if any one of these points has a value.
 */ 4 4 this 3 754  1 x 1 688  7 y_begin 1 688  5 y_end 1 688  
143 0 0 4 174 716 0 0 209 /**
 * Sets the zero_special flag.  When this flag is true, values of (0, 0, 0) in
 * the pfm file are treated as a special case, and are not processed.
 *
 * This is a special case of set_no_data_value().
 */ 2 4 this 3 753  12 zero_special 1 702  
144 0 0 4 175 716 0 0 259 /**
 * Sets the no_data_chan4 flag.  When this flag is true, and the pfm file has
 * 4 channels, then a negative value in the fourth channel indicates no data.
 * When it is false, all points are valid.
 *
 * This is a special case of set_no_data_value().
 */ 2 4 this 3 753  5 chan4 1 702  
145 0 0 4 176 716 0 0 272 /**
 * Sets the no_data_nan flag.  When num_channels is nonzero, then a NaN value
 * in any of the first num_channels channels indicates no data for that point.
 * If num_channels is zero, then all points are valid.
 *
 * This is a special case of set_no_data_value().
 */ 2 4 this 3 753  12 num_channels 1 688  
146 0 0 4 177 716 0 0 90 /**
 * Sets the special value that means "no data" when it appears in the pfm
 * file.
 */ 2 4 this 3 753  13 no_data_value 1 792  
147 0 0 4 177 716 0 0 90 /**
 * Sets the special value that means "no data" when it appears in the pfm
 * file.
 */ 2 4 this 3 753  13 no_data_value 1 782  
148 0 0 4 178 716 0 0 126 /**
 * Sets the special threshold value.  Points that are below this value in all
 * components are considered "no value".
 */ 2 4 this 3 753  13 no_data_value 1 792  
149 0 0 4 178 716 0 0 126 /**
 * Sets the special threshold value.  Points that are below this value in all
 * components are considered "no value".
 */ 2 4 this 3 753  13 no_data_value 1 782  
150 0 0 4 179 716 0 0 136 /**
 * Removes the special value that means "no data" when it appears in the pfm
 * file.  All points will thus be considered valid.
 */ 1 4 this 3 753  
151 0 0 6 180 702 0 0 92 /**
 * Returns whether a "no data" value has been established by
 * set_no_data_value().
 */ 1 4 this 3 754  
152 0 0 6 181 702 0 0 106 /**
 * Returns whether a "no data" threshold value has been established by
 * set_no_data_threshold().
 */ 1 4 this 3 754  
153 0 0 6 182 782 0 0 95 /**
 * If has_no_data_value() returns true, this returns the particular "no data"
 * value.
 */ 1 4 this 3 754  
154 0 0 4 183 716 0 0 173 /**
 * Applies a simple filter to resample the pfm file in-place to the indicated
 * size.  Don't confuse this with applying a scale to all of the points via
 * xform().
 */ 3 4 this 3 753  10 new_x_size 1 688  10 new_y_size 1 688  
155 0 0 4 184 716 0 0 260 /**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */ 3 4 this 3 753  6 radius 1 704  4 copy 1 754  
156 0 0 4 185 716 0 0 260 /**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */ 3 4 this 3 753  6 radius 1 704  4 copy 1 754  
157 0 0 4 186 716 0 0 257 /**
 * Resizes from the given image, with a fixed radius of 0.5. This is a very
 * specialized and simple algorithm that doesn't handle dropping below the
 * Nyquist rate very well, but is quite a bit faster than the more general
 * box_filter(), above.
 */ 2 4 this 3 753  4 copy 1 754  
158 0 0 4 187 716 0 0 61 /**
 * Performs an in-place reversal of the row (y) data.
 */ 1 4 this 3 753  
159 0 0 4 188 716 0 0 369 /**
 * Reverses, transposes, and/or rotates the table in-place according to the
 * specified parameters.  If flip_x is true, the x axis is reversed; if flip_y
 * is true, the y axis is reversed.  Then, if transpose is true, the x and y
 * axes are exchanged.  These parameters can be used to select any combination
 * of 90-degree or 180-degree rotations and flips.
 */ 4 4 this 3 753  6 flip_x 1 702  6 flip_y 1 702  9 transpose 1 702  
160 0 0 4 189 716 0 0 73 /**
 * Applies the indicated transform matrix to all points in-place.
 */ 2 4 this 3 753  9 transform 1 795  
161 0 0 4 189 716 0 0 73 /**
 * Applies the indicated transform matrix to all points in-place.
 */ 2 4 this 3 753  9 transform 1 798  
162 0 0 4 190 716 0 0 659 /**
 * Applies the distortion indicated in the supplied dist map to the current
 * map.  The dist map is understood to be a mapping of points in the range
 * 0..1 in the first two dimensions.
 *
 * The operation can be expressed symbolically as:
 *
 * this(u, v) = this(dist(u, v))
 *
 * If scale_factor is not 1, it should be a value > 1, and it specifies the
 * factor to upscale the working table while processing, to reduce artifacts
 * from integer truncation.
 *
 * By convention, the y axis is inverted in the distortion map relative to the
 * coordinates here.  A y value of 0 in the distortion map corresponds with a
 * v value of 1 in this file.
 */ 3 4 this 3 753  4 dist 1 754  12 scale_factor 5 703  
163 0 0 4 191 716 0 0 659 /**
 * Applies the distortion indicated in the supplied dist map to the current
 * map.  The dist map is understood to be a mapping of points in the range
 * 0..1 in the first two dimensions.
 *
 * The operation can be expressed symbolically as:
 *
 * this(u, v) = dist(this(u, v))
 *
 * If scale_factor is not 1, it should be a value > 1, and it specifies the
 * factor to upscale the working table while processing, to reduce artifacts
 * from integer truncation.
 *
 * By convention, the y axis in inverted in the distortion map relative to the
 * coordinates here.  A y value of 0 in the distortion map corresponds with a
 * v value of 1 in this file.
 */ 3 4 this 3 753  4 dist 1 754  12 scale_factor 5 703  
164 0 0 4 192 716 0 0 211 /**
 * Assumes that lut is an X by 1, 1-component PfmFile whose X axis maps points
 * to target points.  For each point in this pfm file, computes: p(u,
 * v)[channel] = lut(p(u, v)[channel] * x_scale, 0)[0]
 */ 4 4 this 3 753  7 channel 1 688  3 lut 1 754  7 x_scale 5 703  
165 0 0 4 193 716 0 0 199 /**
 * Wherever there is missing data in this PfmFile (that is, wherever
 * has_point() returns false), copy data from the other PfmFile, which must be
 * exactly the same dimensions as this one.
 */ 2 4 this 3 753  5 other 1 754  
166 0 0 4 194 716 0 0 274 /**
 * Wherever there is missing data in the other PfmFile, set this the
 * corresponding point in this PfmFile to missing as well, so that this
 * PfmFile has only points where both files have points.
 *
 * The point is set to "missing" by setting it the no_data_value.
 */ 2 4 this 3 753  5 other 1 754  
167 0 0 4 195 716 0 0 159 /**
 * Copies just the specified channel values from the indicated PfmFile (which
 * could be same as this PfmFile) into the specified channel of this one.
 */ 4 4 this 3 753  10 to_channel 1 688  5 other 1 754  12 from_channel 1 688  
168 0 0 4 196 716 0 0 130 /**
 * Copies just the specified channel values from the indicated PfmFile, but
 * only where the other file has a data point.
 */ 4 4 this 3 753  10 to_channel 1 688  5 other 1 754  12 from_channel 1 688  
169 0 0 4 197 716 0 0 148 /**
 * Reduces the PFM file to the cells in the rectangle bounded by (x_begin,
 * x_end, y_begin, y_end), where the _end cells are not included.
 */ 5 4 this 3 753  7 x_begin 1 688  5 x_end 1 688  7 y_begin 1 688  5 y_end 1 688  
170 0 0 4 198 716 0 0 159 /**
 * Replaces this PfmFile with a new PfmFile of size x_size x y_size x 3,
 * containing the x y 0 values in the range 0 .. 1 according to the x y index.
 */ 3 4 this 3 753  6 x_size 1 688  6 y_size 1 688  
171 0 0 6 199 688 0 0 262 /**
 * Applies delta * t to the point values within radius (xr, yr) distance of
 * (xc, yc).  The t value is scaled from 1.0 at the center to 0.0 at radius
 * (xr, yr), and this scale follows the specified exponent.  Returns the
 * number of points affected.
 */ 7 4 this 3 753  5 delta 1 782  2 xc 1 704  2 yc 1 704  2 xr 1 704  2 yr 1 704  8 exponent 1 704  
172 0 0 6 200 702 0 0 209 /**
 * Calculates the minimum and maximum vertices of all points within the table.
 * Assumes the table contains 3-D points.
 *
 * The return value is true if any points in the table, or false if none are.
 */ 3 4 this 3 754  9 min_point 1 781  9 max_point 1 781  
173 0 0 7 201 805 0 0 644 /**
 * Computes the minmax bounding volume of the points in 3-D space, assuming
 * the points represent a mostly-planar surface.
 *
 * This algorithm works by sampling the (square) sample_radius pixels at the
 * four point_dist corners around the center (cx - pd, cx + pd) and so on, to
 * approximate the plane of the surface.  Then all of the points are projected
 * into that plane and the bounding volume of the entire mesh within that
 * plane is determined.  If points_only is true, the bounding volume of only
 * those four points is determined.
 *
 * center, point_dist and sample_radius are in UV space, i.e.  in the range
 * 0..1.
 */ 5 4 this 3 754  6 center 1 802  10 point_dist 1 703  13 sample_radius 1 703  11 points_only 1 702  
174 0 0 7 201 805 0 0 644 /**
 * Computes the minmax bounding volume of the points in 3-D space, assuming
 * the points represent a mostly-planar surface.
 *
 * This algorithm works by sampling the (square) sample_radius pixels at the
 * four point_dist corners around the center (cx - pd, cx + pd) and so on, to
 * approximate the plane of the surface.  Then all of the points are projected
 * into that plane and the bounding volume of the entire mesh within that
 * plane is determined.  If points_only is true, the bounding volume of only
 * those four points is determined.
 *
 * center, point_dist and sample_radius are in UV space, i.e.  in the range
 * 0..1.
 */ 5 4 this 3 754  6 center 1 762  10 point_dist 1 703  13 sample_radius 1 703  11 points_only 1 702  
175 0 0 4 202 716 0 0 188 /**
 * Computes the average of all the point within sample_radius (manhattan
 * distance) and the indicated point.
 *
 * The point coordinates are given in UV space, in the range 0..1.
 */ 5 4 this 3 754  6 result 1 781  1 x 1 703  1 y 1 703  13 sample_radius 1 703  
176 0 0 4 203 716 0 0 344 /**
 * Copies a rectangular area of another image into a rectangular area of this
 * image.  Both images must already have been initialized.  The upper-left
 * corner of the region in both images is specified, and the size of the area;
 * if the size is omitted, it defaults to the entire other image, or the
 * largest piece that will fit.
 */ 8 4 this 3 753  4 copy 1 754  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  
177 0 0 4 204 716 0 0 155 /**
 * Behaves like copy_sub_image(), except the copy pixels are added to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 */ 9 4 this 3 753  4 copy 1 754  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
178 0 0 4 205 716 0 0 160 /**
 * Behaves like copy_sub_image(), except the copy pixels are multiplied to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 */ 9 4 this 3 753  4 copy 1 754  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
179 0 0 4 206 716 0 0 216 /**
 * Behaves like copy_sub_image(), except the copy pixels are divided into the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * dest(x, y) = dest(x, y) / (copy(x, y) * pixel_scale).
 */ 9 4 this 3 753  4 copy 1 754  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
180 0 0 6 207 753 0 0 0 2 4 this 3 753  10 multiplier 1 704  
181 0 0 4 208 716 0 0 566 /**
 * index_image is a WxH 1-channel image, while pixel_values is an Nx1
 * image with any number of channels.  Typically pixel_values will be
 * a 256x1 image.
 *
 * Fills the PfmFile with a new image the same width and height as
 * index_image, with the same number of channels as pixel_values.
 *
 * Each pixel of the new image is computed with the formula:
 *
 * new_image(x, y) = pixel_values(index_image(x, y)[channel], 0)
 *
 * At present, no interpolation is performed; the nearest value in
 * pixel_values is discovered.  This may change in the future.
 */ 4 4 this 3 753  11 index_image 1 754  7 channel 1 688  12 pixel_values 1 754  
182 0 0 4 209 716 0 0 217 /**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * RGB channels, converts it to an image with a gamma curve of to_gamma in the
 * RGB channels.  Does not affect the alpha channel.
 */ 3 4 this 3 753  10 from_gamma 1 704  8 to_gamma 1 704  
183 0 0 4 210 716 0 0 218 /**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * alpha channel, converts it to an image with a gamma curve of to_gamma in
 * the alpha channel.  Does not affect the RGB channels.
 */ 3 4 this 3 753  10 from_gamma 1 704  8 to_gamma 1 704  
184 0 0 4 211 716 0 0 148 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */ 2 4 this 3 753  13 gray_exponent 1 704  
185 0 0 4 211 716 0 0 148 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */ 3 4 this 3 753  13 gray_exponent 1 704  14 alpha_exponent 1 704  
186 0 0 4 211 716 0 0 283 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */ 4 4 this 3 753  11 c0_exponent 1 704  11 c1_exponent 1 704  11 c2_exponent 1 704  
187 0 0 4 211 716 0 0 148 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */ 5 4 this 3 753  11 c0_exponent 1 704  11 c1_exponent 1 704  11 c2_exponent 1 704  11 c3_exponent 1 704  
188 0 0 4 212 716 0 0 10 /**
 *
 */ 2 4 this 3 754  3 out 1 719  
189 0 0 38 213 806 0 0 0 1 4 this 3 754  
190 0 0 38 214 688 0 0 0 3 4 this 3 754  4 view 1 809  5 flags 1 688  
191 0 0 7 219 712 0 0 161 /**
 * Returns a new brush that does not paint anything.  Can be used as either a
 * pen or a fill brush to make borderless or unfilled shapes, respectively.
 */ 0 
192 0 0 7 220 712 0 0 136 /**
 * Returns a new brush that paints a single pixel of the indicated color on a
 * border, or paints a solid color in an interior.
 */ 2 5 color 1 812  6 effect 5 707  
193 0 0 7 221 712 0 0 153 /**
 * Returns a new brush that paints a spot of the indicated color and radius.
 * If fuzzy is true, the spot is fuzzy; otherwise, it is hard-edged.
 */ 4 5 color 1 812  6 radius 1 704  5 fuzzy 1 702  6 effect 5 707  
194 0 0 7 222 712 0 0 244 /**
 * Returns a new brush that paints with the indicated image.  xc and yc
 * indicate the pixel in the center of the brush.
 *
 * The brush makes a copy of the image; it is safe to deallocate or modify the
 * image after making this call.
 */ 4 5 image 1 756  2 xc 1 704  2 yc 1 704  6 effect 5 707  
195 0 0 7 224 758 545 0 10 /**
 *
 */ 0 
196 0 0 7 224 758 545 0 10 /**
 *
 */ 2 8 filename 1 733  4 type 5 685  
197 0 0 15 224 758 545 0 10 /**
 *
 */ 1 4 copy 1 756  
198 0 0 7 224 758 545 0 10 /**
 *
 */ 6 6 x_size 1 688  6 y_size 1 688  12 num_channels 5 688  6 maxval 5 689  4 type 5 685  11 color_space 5 690  
199 0 0 6 225 758 0 0 0 2 4 this 3 758  4 copy 1 756  
200 0 0 6 227 689 0 0 65 /**
 * A handy function to clamp values to [0..get_maxval()].
 */ 2 4 this 3 756  11 input_value 1 688  
201 0 0 7 228 818 0 0 143 /**
 * A handy function to scale non-alpha values from [0..1] to
 * [0..get_maxval()].  Do not use this for alpha values, see to_alpha_val.
 */ 2 4 this 3 756  11 input_value 1 815  
202 0 0 6 228 689 0 0 143 /**
 * A handy function to scale non-alpha values from [0..1] to
 * [0..get_maxval()].  Do not use this for alpha values, see to_alpha_val.
 */ 2 4 this 3 756  11 input_value 1 704  
203 0 0 6 229 689 0 0 83 /**
 * A handy function to scale alpha values from [0..1] to [0..get_maxval()].
 */ 2 4 this 3 756  11 input_value 1 704  
204 0 0 7 230 819 0 0 145 /**
 * A handy function to scale non-alpha values from [0..get_maxval()] to
 * [0..1].  Do not use this for alpha values, see from_alpha_val.
 */ 2 4 this 3 756  11 input_value 1 745  
205 0 0 6 230 704 0 0 145 /**
 * A handy function to scale non-alpha values from [0..get_maxval()] to
 * [0..1].  Do not use this for alpha values, see from_alpha_val.
 */ 2 4 this 3 756  11 input_value 1 689  
206 0 0 6 231 704 0 0 83 /**
 * A handy function to scale alpha values from [0..get_maxval()] to [0..1].
 */ 2 4 this 3 756  11 input_value 1 689  
207 0 0 4 232 716 0 0 111 /**
 * Frees all memory allocated for the image, and clears all its parameters
 * (size, color, type, etc).
 */ 1 4 this 3 758  
208 0 0 4 232 716 0 0 113 /**
 * This flavor of clear() reinitializes the image to an empty (black) image
 * with the given dimensions.
 */ 7 4 this 3 758  6 x_size 1 688  6 y_size 1 688  12 num_channels 5 688  6 maxval 5 689  4 type 5 685  11 color_space 5 690  
209 0 0 4 233 716 0 0 61 /**
 * Makes this image become a copy of the other image.
 */ 2 4 this 3 758  4 copy 1 756  
210 0 0 4 234 716 0 0 86 /**
 * Copies a channel from one image into another.  Images must be the same size
 */ 4 4 this 3 758  4 copy 1 756  11 src_channel 1 688  12 dest_channel 1 688  
211 0 0 4 234 716 0 0 139 /**
 * Copies just a single channel from the source image into a single channel of
 * this image, leaving the remaining channels alone.
 */ 10 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  3 cto 1 688  5 xfrom 5 688  5 yfrom 5 688  5 cfrom 5 688  6 x_size 5 688  6 y_size 5 688  
212 0 0 4 235 716 0 0 247 /**
 * Copies some subset of the bits of the specified channel from one image into
 * some subset of the bits of the specified channel in another image.  Images
 * must be the same size.
 *
 * If right_shift is negative, it means a left shift.
 */ 6 4 this 3 758  4 copy 1 756  11 src_channel 1 688  12 dest_channel 1 688  8 src_mask 1 689  11 right_shift 1 688  
213 0 0 4 236 716 0 0 186 /**
 * Copies just the header information into this image.  This will blow away
 * any image data stored in the image.  The new image data will be allocated,
 * but left unitialized.
 */ 2 4 this 3 758  6 header 1 730  
214 0 0 4 237 716 0 0 92 /**
 * Move the contents of the other image into this one, and empty the other
 * image.
 */ 2 4 this 3 758  4 orig 1 758  
215 0 0 4 238 716 0 0 92 /**
 * Sets the entire image (except the alpha channel) to the given grayscale
 * level.
 */ 2 4 this 3 758  4 gray 5 704  
216 0 0 4 238 716 0 0 79 /**
 * Sets the entire image (except the alpha channel) to the given color.
 */ 4 4 this 3 758  3 red 1 704  5 green 1 704  4 blue 1 704  
217 0 0 4 239 716 0 0 92 /**
 * Sets the entire image (except the alpha channel) to the given grayscale
 * level.
 */ 2 4 this 3 758  4 gray 5 689  
218 0 0 4 239 716 0 0 79 /**
 * Sets the entire image (except the alpha channel) to the given color.
 */ 4 4 this 3 758  3 red 1 689  5 green 1 689  4 blue 1 689  
219 0 0 4 240 716 0 0 60 /**
 * Sets the entire alpha channel to the given level.
 */ 2 4 this 3 758  5 alpha 5 704  
220 0 0 4 241 716 0 0 60 /**
 * Sets the entire alpha channel to the given level.
 */ 2 4 this 3 758  5 alpha 5 689  
221 0 0 4 242 716 0 0 513 /**
 * Specifies the size to we'd like to scale the image upon reading it.  This
 * will affect the next call to read().  This is usually used to reduce the
 * image size, e.g.  for a thumbnail.
 *
 * If the file type reader supports it (e.g.  JPEG), then this will scale the
 * image during the read operation, consequently reducing memory and CPU
 * utilization.  If the file type reader does not support it, this will load
 * the image normally, and them perform a linear scale after it has been
 * loaded.
 */ 3 4 this 3 758  6 x_size 1 688  6 y_size 1 688  
222 0 0 4 243 716 0 0 67 /**
 * Undoes the effect of a previous call to set_read_size().
 */ 1 4 this 3 758  
223 0 0 6 244 702 0 0 59 /**
 * Returns true if set_read_size() has been called.
 */ 1 4 this 3 756  
224 0 0 6 245 688 0 0 139 /**
 * Returns the requested x_size of the image if set_read_size() has been
 * called, or the image x_size otherwise (if it is known).
 */ 1 4 this 3 756  
225 0 0 6 246 688 0 0 139 /**
 * Returns the requested y_size of the image if set_read_size() has been
 * called, or the image y_size otherwise (if it is known).
 */ 1 4 this 3 756  
226 0 0 6 247 690 0 0 65 /**
 * Returns the color space in which the image is encoded.
 */ 1 4 this 3 756  
227 0 0 6 248 702 0 0 166 /**
 * Reads the indicated image filename.  If type is non-NULL, it is a
 * suggestion for the type of file it is.  Returns true if successful, false
 * on error.
 */ 4 4 this 3 758  8 filename 1 733  4 type 5 685  19 report_unknown_type 5 702  
228 0 0 6 248 702 0 0 382 /**
 * This flavor of read() uses an already-existing PNMReader to read the image
 * file.  You can get a reader via the PNMImageHeader::make_reader() methods.
 * This is a good way to examine the header of a file (for instance, to
 * determine its size) before actually reading the entire image.
 *
 * The PNMReader is always deleted upon completion, whether successful or not.
 */ 2 4 this 3 758  6 reader 1 738  
229 0 0 6 248 702 0 0 378 /**
 * Reads the image data from the indicated stream.
 *
 * The filename is advisory only, and may be used to suggest a type if it has
 * a known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of file it is (and a
 * non-NULL type will override any magic number test or filename extension
 * lookup).
 *
 * Returns true if successful, false on error.
 */ 5 4 this 3 758  4 data 1 736  8 filename 5 723  4 type 5 685  19 report_unknown_type 5 702  
230 0 0 6 249 702 0 0 135 /**
 * Writes the image to the indicated filename.  If type is non-NULL, it is a
 * suggestion for the type of image file to write.
 */ 3 4 this 3 756  8 filename 1 733  4 type 5 685  
231 0 0 6 249 702 0 0 250 /**
 * This flavor of write() uses an already-existing PNMWriter to write the
 * image file.  You can get a writer via the PNMImageHeader::make_writer()
 * methods.
 *
 * The PNMWriter is always deleted upon completion, whether successful or not.
 */ 2 4 this 3 756  6 writer 1 740  
232 0 0 6 249 702 0 0 239 /**
 * Writes the image to the indicated ostream.
 *
 * The filename is advisory only, and may be used suggest a type if it has a
 * known extension.
 *
 * If type is non-NULL, it is a suggestion for the type of image file to
 * write.
 */ 4 4 this 3 756  4 data 1 719  8 filename 5 723  4 type 5 685  
233 0 0 6 250 702 0 0 218 /**
 * Returns true if the image has been read in or correctly initialized with a
 * height and width.  If this returns false, virtually all member functions
 * except clear() and read() are invalid function calls.
 */ 1 4 this 3 756  
234 0 0 4 251 716 0 0 257 /**
 * Changes the number of channels associated with the image.  The new number
 * of channels must be an integer in the range 1 through 4, inclusive.  This
 * will allocate and/or deallocate memory as necessary to accommodate; see
 * set_color_type().
 */ 2 4 this 3 758  12 num_channels 1 688  
235 0 0 4 252 716 0 0 319 /**
 * Translates the image to or from grayscale, color, or four-color mode.
 * Grayscale images are converted to full-color images with R, G, B set to the
 * original gray level; color images are converted to grayscale according to
 * the value of Bright().  The alpha channel, if added, is initialized to
 * zero.
 */ 2 4 this 3 758  10 color_type 1 687  
236 0 0 4 253 716 0 0 634 /**
 * Converts the colors in the image to the indicated color space.  This may be
 * a lossy operation, in particular when going from sRGB to linear.  The alpha
 * channel remains untouched.
 *
 * Note that, because functions like get_xel() and set_xel() work on
 * linearized floating-point values, this conversion won't affect those values
 * (aside from some minor discrepancies due to storage precision).  It does
 * affect the values used by get_xel_val() and set_xel_val(), though, since
 * those operate on encoded colors.
 *
 * Some color spaces, particularly scRGB, may enforce the use of a particular
 * maxval setting.
 */ 2 4 this 3 758  11 color_space 1 690  
237 0 0 4 254 716 0 0 126 /**
 * Adds an alpha channel to the image, if it does not already have one.  The
 * alpha channel is initialized to zeros.
 */ 1 4 this 3 758  
238 0 0 4 255 716 0 0 59 /**
 * Removes the image's alpha channel, if it exists.
 */ 1 4 this 3 758  
239 0 0 4 256 716 0 0 108 /**
 * Converts the image from RGB to grayscale.  Any alpha channel, if present,
 * is left undisturbed.
 */ 1 4 this 3 758  
240 0 0 4 256 716 0 0 227 /**
 * Converts the image from RGB to grayscale.  Any alpha channel, if present,
 * is left undisturbed.  The optional rc, gc, bc values represent the relative
 * weights to apply to each channel to convert it to grayscale.
 */ 4 4 this 3 758  2 rc 1 704  2 gc 1 704  2 bc 1 704  
241 0 0 4 257 716 0 0 108 /**
 * Converts the image from grayscale to RGB.  Any alpha channel, if present,
 * is left undisturbed.
 */ 1 4 this 3 758  
242 0 0 4 258 716 0 0 234 /**
 * Converts an image in-place to its "premultiplied" form, where, for every
 * pixel in the image, the red, green, and blue components are multiplied by
 * that pixel's alpha value.
 *
 * This does not modify any alpha values.
 */ 1 4 this 3 758  
243 0 0 4 259 716 0 0 273 /**
 * Converts an image in-place to its "straight alpha" form (presumably from a
 * "premultiplied" form), where, for every pixel in the image, the red, green,
 * and blue components are divided by that pixel's alpha value.
 *
 * This does not modify any alpha values.
 */ 1 4 this 3 758  
244 0 0 4 260 716 0 0 61 /**
 * Performs an in-place reversal of the row (y) data.
 */ 1 4 this 3 758  
245 0 0 4 261 716 0 0 369 /**
 * Reverses, transposes, and/or rotates the image in-place according to the
 * specified parameters.  If flip_x is true, the x axis is reversed; if flip_y
 * is true, the y axis is reversed.  Then, if transpose is true, the x and y
 * axes are exchanged.  These parameters can be used to select any combination
 * of 90-degree or 180-degree rotations and flips.
 */ 4 4 this 3 758  6 flip_x 1 702  6 flip_y 1 702  9 transpose 1 702  
246 0 0 4 262 716 0 0 54 /**
 * Rescales the image to the indicated maxval.
 */ 2 4 this 3 758  6 maxval 1 689  
247 0 0 6 263 818 0 0 102 /**
 * Returns the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval.
 */ 3 4 this 3 758  1 x 1 688  1 y 1 688  
248 0 0 7 263 818 0 0 102 /**
 * Returns the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
249 0 0 4 264 716 0 0 227 /**
 * Changes the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval, encoded in the configured color space.  See set_xel if you
 * instead have a linearized and normalized floating-point value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  5 value 1 745  
250 0 0 4 264 716 0 0 282 /**
 * Changes all three color components at the indicated pixel to the same
 * value.  The value is in the range component is in the range 0..maxval,
 * encoded in the configured color space.  See set_xel if you instead have a
 * linearized and normalized floating-point value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  4 gray 1 689  
251 0 0 4 264 716 0 0 227 /**
 * Changes the RGB color at the indicated pixel.  Each component is in the
 * range 0..maxval, encoded in the configured color space.  See set_xel if you
 * instead have a linearized and normalized floating-point value.
 */ 6 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 689  1 g 1 689  1 b 1 689  
252 0 0 6 265 689 0 0 158 /**
 * Returns the red component color at the indicated pixel.  The value returned
 * is in the range 0..maxval and encoded in the configured color space.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
253 0 0 6 266 689 0 0 163 /**
 * Returns the green component color at the indicated pixel.  The value
 * returned is in the range 0..maxval and encoded in the configured color
 * space.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
254 0 0 6 267 689 0 0 162 /**
 * Returns the blue component color at the indicated pixel.  The value
 * returned is in the range 0..maxval and encoded in the configured color
 * space.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
255 0 0 6 268 689 0 0 342 /**
 * Returns the gray component color at the indicated pixel.  This only has a
 * meaningful value for grayscale images; for other image types, this returns
 * the value of the blue channel only.  However, also see the get_bright()
 * function.  The value returned is in the range 0..maxval and encoded in the
 * configured color space.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
256 0 0 6 269 689 0 0 196 /**
 * Returns the alpha component color at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value returned is in the
 * range 0..maxval and always linear.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
257 0 0 4 270 716 0 0 249 /**
 * Sets the red component color only at the indicated pixel.  The value given
 * should be in the range 0..maxval, encoded in the configured color space.
 * See set_red if you instead have a linearized and normalized floating-point
 * value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 689  
258 0 0 4 271 716 0 0 254 /**
 * Sets the green component color only at the indicated pixel.  The value
 * given should be in the range 0..maxval, encoded in the configured color
 * space.  See set_green if you instead have a linearized and normalized
 * floating-point value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 g 1 689  
259 0 0 4 272 716 0 0 251 /**
 * Sets the blue component color only at the indicated pixel.  The value given
 * should be in the range 0..maxval, encoded in the configured color space.
 * See set_blue if you instead have a linearized and normalized floating-point
 * value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 b 1 689  
260 0 0 4 273 716 0 0 530 /**
 * Sets the gray component color at the indicated pixel.  This is only
 * meaningful for grayscale images; for other image types, this simply sets
 * the blue component color.  However, also see set_xel_val(), which can set
 * all the component colors to the same grayscale level, and hence works
 * correctly both for grayscale and color images.  The value given should be
 * in the range 0..maxval, encoded in the configured color space.  See
 * set_gray if you instead have a linearized normalized floating-point value.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  4 gray 1 689  
261 0 0 4 274 716 0 0 282 /**
 * Sets the alpha component color only at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value given should be in the
 * range 0..maxval.
 *
 * This value is always linearly encoded, even if the image is set to the sRGB
 * color space.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 a 1 689  
262 0 0 6 275 689 0 0 326 /**
 * Returns the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than accessing the component
 * values directly by named methods.  The value returned is in the range
 * 0..maxval.
 */ 4 4 this 3 756  1 x 1 688  1 y 1 688  7 channel 1 688  
263 0 0 4 276 716 0 0 325 /**
 * Sets the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than setting the component
 * values directly by named methods.  The value given should be in the range
 * 0..maxval.
 */ 5 4 this 3 758  1 x 1 688  1 y 1 688  7 channel 1 688  5 value 1 689  
264 0 0 6 277 704 0 0 329 /**
 * Returns the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than accessing the component
 * values directly by named methods.  The value returned is a float in the
 * range 0..1.
 */ 4 4 this 3 756  1 x 1 688  1 y 1 688  7 channel 1 688  
265 0 0 4 278 716 0 0 328 /**
 * Sets the nth component color at the indicated pixel.  The channel index
 * should be in the range 0..(get_num_channels()-1).  The channels are ordered
 * B, G, R, A.  This is slightly less optimal than setting the component
 * values directly by named methods.  The value given should be a float in the
 * range 0..1.
 */ 5 4 this 3 758  1 x 1 688  1 y 1 688  7 channel 1 688  5 value 1 704  
266 0 0 7 279 744 447 0 100 /**
 * Returns the (r, g, b, a) pixel value at the indicated pixel, using a
 * PixelSpec object.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
267 0 0 4 280 716 0 0 97 /**
 * Sets the (r, g, b, a) pixel value at the indicated pixel, using a PixelSpec
 * object.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  5 pixel 1 742  
268 0 0 7 281 819 0 0 116 /**
 * Returns the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
269 0 0 4 282 716 0 0 116 /**
 * Changes the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  5 value 1 815  
270 0 0 4 282 716 0 0 142 /**
 * Changes all three color components at the indicated pixel to the same
 * value.  The value is a linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  4 gray 1 704  
271 0 0 4 282 716 0 0 116 /**
 * Changes the RGB color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 6 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 704  1 g 1 704  1 b 1 704  
272 0 0 7 283 820 0 0 117 /**
 * Returns the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
273 0 0 4 284 716 0 0 117 /**
 * Changes the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  5 value 1 812  
274 0 0 4 284 716 0 0 117 /**
 * Changes the RGBA color at the indicated pixel.  Each component is a
 * linearized float in the range 0..1.
 */ 7 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 704  1 g 1 704  1 b 1 704  1 a 1 704  
275 0 0 6 285 704 0 0 130 /**
 * Returns the red component color at the indicated pixel.  The value returned
 * is a linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
276 0 0 6 286 704 0 0 132 /**
 * Returns the green component color at the indicated pixel.  The value
 * returned is a linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
277 0 0 6 287 704 0 0 131 /**
 * Returns the blue component color at the indicated pixel.  The value
 * returned is a linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
278 0 0 6 288 704 0 0 311 /**
 * Returns the gray component color at the indicated pixel.  This only has a
 * meaningful value for grayscale images; for other image types, this returns
 * the value of the blue channel only.  However, also see the get_bright()
 * function.  The value returned is a linearized float in the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
279 0 0 6 289 704 0 0 181 /**
 * Returns the alpha component color at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value returned is a float in
 * the range 0..1.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
280 0 0 4 290 716 0 0 136 /**
 * Sets the red component color only at the indicated pixel.  The value given
 * should be a linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 704  
281 0 0 4 291 716 0 0 138 /**
 * Sets the green component color only at the indicated pixel.  The value
 * given should be a linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 g 1 704  
282 0 0 4 292 716 0 0 137 /**
 * Sets the blue component color only at the indicated pixel.  The value given
 * should be a linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 b 1 704  
283 0 0 4 293 716 0 0 418 /**
 * Sets the gray component color at the indicated pixel.  This is only
 * meaningful for grayscale images; for other image types, this simply sets
 * the blue component color.  However, also see set_xel(), which can set all
 * the component colors to the same grayscale level, and hence works correctly
 * both for grayscale and color images.  The value given should be a
 * linearized float in the range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  4 gray 1 704  
284 0 0 4 294 716 0 0 179 /**
 * Sets the alpha component color only at the indicated pixel.  It is an error
 * to call this unless has_alpha() is true.  The value given should be in the
 * range 0..1.
 */ 4 4 this 3 758  1 x 1 688  1 y 1 688  1 a 1 704  
285 0 0 6 295 704 0 0 223 /**
 * Returns the linear brightness of the given xel, as a linearized float in
 * the range 0..1.  This flavor of get_bright() returns the correct grayscale
 * brightness level for both full-color and grayscale images.
 */ 3 4 this 3 756  1 x 1 688  1 y 1 688  
286 0 0 6 295 704 0 0 213 /**
 * This flavor of get_bright() works correctly only for color images.  It
 * returns a single brightness value for the RGB color at the indicated pixel,
 * based on the supplied weights for each component.
 */ 6 4 this 3 756  1 x 1 688  1 y 1 688  2 rc 1 704  2 gc 1 704  2 bc 1 704  
287 0 0 6 295 704 0 0 220 /**
 * This flavor of get_bright() works correctly only for four-channel images.
 * It returns a single brightness value for the RGBA color at the indicated
 * pixel, based on the supplied weights for each component.
 */ 7 4 this 3 756  1 x 1 688  1 y 1 688  2 rc 1 704  2 gc 1 704  2 bc 1 704  2 ac 1 704  
288 0 0 4 296 716 0 0 276 /**
 * Smoothly blends the indicated pixel value in with whatever was already in
 * the image, based on the given alpha value.  An alpha of 1.0 is fully opaque
 * and completely replaces whatever was there previously; alpha of 0.0 is
 * fully transparent and does nothing.
 */ 5 4 this 3 758  1 x 1 688  1 y 1 688  3 val 1 815  5 alpha 1 704  
289 0 0 4 296 716 0 0 276 /**
 * Smoothly blends the indicated pixel value in with whatever was already in
 * the image, based on the given alpha value.  An alpha of 1.0 is fully opaque
 * and completely replaces whatever was there previously; alpha of 0.0 is
 * fully transparent and does nothing.
 */ 7 4 this 3 758  1 x 1 688  1 y 1 688  1 r 1 704  1 g 1 704  1 b 1 704  5 alpha 1 704  
290 0 0 4 297 716 0 0 344 /**
 * Copies a rectangular area of another image into a rectangular area of this
 * image.  Both images must already have been initialized.  The upper-left
 * corner of the region in both images is specified, and the size of the area;
 * if the size is omitted, it defaults to the entire other image, or the
 * largest piece that will fit.
 */ 8 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  
291 0 0 4 298 716 0 0 438 /**
 * Behaves like copy_sub_image(), except the alpha channel of the copy is used
 * to blend the copy into the destination image, instead of overwriting pixels
 * unconditionally.
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each *alpha*
 * value of the source image before applying it to the target image.
 *
 * If pixel_scale is 1.0 and the copy has no alpha channel, this degenerates
 * into copy_sub_image().
 */ 9 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
292 0 0 4 299 716 0 0 228 /**
 * Behaves like copy_sub_image(), except the copy pixels are added to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * Unlike blend_sub_image(), the alpha channel is not treated specially.
 */ 9 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
293 0 0 4 300 716 0 0 233 /**
 * Behaves like copy_sub_image(), except the copy pixels are multiplied to the
 * pixels of the destination, after scaling by the specified pixel_scale.
 * Unlike blend_sub_image(), the alpha channel is not treated specially.
 */ 9 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
294 0 0 4 301 716 0 0 437 /**
 * Behaves like copy_sub_image(), but the resulting color will be the darker
 * of the source and destination colors at each pixel (and at each R, G, B, A
 * component value).
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each pixel value
 * of the source image before applying it to the target image.  The scale is
 * applied with the center at 1.0: scaling the pixel value smaller brings it
 * closer to 1.0.
 */ 9 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
295 0 0 4 302 716 0 0 329 /**
 * Behaves like copy_sub_image(), but the resulting color will be the lighter
 * of the source and destination colors at each pixel (and at each R, G, B, A
 * component value).
 *
 * If pixel_scale is not 1.0, it specifies an amount to scale each pixel value
 * of the source image before applying it to the target image.
 */ 9 4 this 3 758  4 copy 1 756  3 xto 1 688  3 yto 1 688  5 xfrom 5 688  5 yfrom 5 688  6 x_size 5 688  6 y_size 5 688  11 pixel_scale 5 704  
296 0 0 4 303 716 0 0 620 /**
 * Selectively copies each pixel from either one source or another source,
 * depending on the pixel value of the indicated channel of select_image.
 *
 * For each pixel (x, y):
 *
 * s = select_image.get_channel(x, y, channel). Set this image's (x, y) to:
 *
 * lt.get_xel(x, y) if s < threshold, or
 *
 * ge.get_xel(x, y) if s >= threshold
 *
 * Any of select_image, lt, or ge may be the same PNMImge object as this
 * image, or the same as each other; or they may all be different.  All images
 * must be the same size.  As a special case, lt and ge may both be 1x1 images
 * instead of the source image size.
 */ 6 4 this 3 758  12 select_image 1 756  7 channel 1 688  9 threshold 1 704  2 lt 1 756  2 ge 1 756  
297 0 0 4 304 716 0 0 790 /**
 * Replaces this image with a grayscale image whose gray channel represents
 * the linear Manhattan distance from the nearest dark pixel in the given mask
 * image, up to the specified radius value (which also becomes the new
 * maxval).  radius may range from 0 to maxmaxval; smaller values will compute
 * faster.  A dark pixel is defined as one whose pixel value is < threshold.
 *
 * If shrink_from_border is true, then the mask image is considered to be
 * surrounded by a border of dark pixels; otherwise, the border isn't
 * considered.
 *
 * This can be used, in conjunction with threshold, to shrink a mask image
 * inwards by a certain number of pixels.
 *
 * The mask image may be the same image as this one, in which case it is
 * destructively modified by this process.
 */ 5 4 this 3 758  4 mask 1 756  9 threshold 1 704  6 radius 1 688  18 shrink_from_border 1 702  
298 0 0 4 305 716 0 0 630 /**
 * Replaces this image with a grayscale image whose gray channel represents
 * the linear Manhattan distance from the nearest white pixel in the given
 * mask image, up to the specified radius value (which also becomes the new
 * maxval).  radius may range from 0 to maxmaxval; smaller values will compute
 * faster.  A white pixel is defined as one whose pixel value is >= threshold.
 *
 * This can be used, in conjunction with threshold, to grow a mask image
 * outwards by a certain number of pixels.
 *
 * The mask image may be the same image as this one, in which case it is
 * destructively modified by this process.
 */ 4 4 this 3 758  4 mask 1 756  9 threshold 1 704  6 radius 1 688  
299 0 0 4 306 716 0 0 557 /**
 * index_image is a WxH grayscale image, while pixel_values is an Nx1 color
 * (or grayscale) image.  Typically pixel_values will be a 256x1 image.
 *
 * Fills the PNMImage with a new image the same width and height as
 * index_image, with the same number of channels as pixel_values.
 *
 * Each pixel of the new image is computed with the formula:
 *
 * new_image(x, y) = pixel_values(index_image(x, y)[channel], 0)
 *
 * At present, no interpolation is performed; the nearest value in
 * pixel_values is discovered.  This may change in the future.
 */ 4 4 this 3 758  11 index_image 1 756  7 channel 1 688  12 pixel_values 1 756  
300 0 0 4 307 716 0 0 272 /**
 * Rescales the RGB channel values so that any values in the original image
 * between min_val and max_val are expanded to the range 0 .. 1.  Values below
 * min_val are set to 0, and values above max_val are set to 1. Does not
 * affect the alpha channel, if any.
 */ 3 4 this 3 758  7 min_val 1 704  7 max_val 1 704  
301 0 0 4 308 716 0 0 450 /**
 * Renders a solid-color circle, with a fuzzy edge, into the center of the
 * PNMImage.  If the PNMImage is non-square, this actually renders an ellipse.
 *
 * The min_radius and max_radius are in the scale 0..1, where 1.0 means the
 * full width of the image.  If min_radius == max_radius, the edge is sharp
 * (but still antialiased); otherwise, the pixels between min_radius and
 * max_radius are smoothly blended between fg and bg colors.
 */ 5 4 this 3 758  2 fg 1 812  2 bg 1 812  10 min_radius 1 704  10 max_radius 1 704  
302 0 0 4 309 716 0 0 198 /**
 * Expands the image by the indicated number of pixels on each edge.  The new
 * pixels are set to the indicated color.
 *
 * If any of the values is negative, this actually crops the image.
 */ 6 4 this 3 758  4 left 1 688  5 right 1 688  6 bottom 1 688  3 top 1 688  5 color 1 812  
303 0 0 4 310 716 0 0 154 /**
 * This flavor of box_filter() will apply the filter over the entire image
 * without resizing or copying; the effect is that of a blur operation.
 */ 2 4 this 3 758  6 radius 5 704  
304 0 0 4 311 716 0 0 159 /**
 * This flavor of gaussian_filter() will apply the filter over the entire
 * image without resizing or copying; the effect is that of a blur operation.
 */ 2 4 this 3 758  6 radius 5 704  
305 0 0 4 312 716 0 0 99 /**
 * Resizes from the indicated image into this one by performing a nearest-
 * point sample.
 */ 2 4 this 3 758  4 copy 1 756  
306 0 0 4 313 716 0 0 260 /**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */ 3 4 this 3 758  6 radius 1 704  4 copy 1 756  
307 0 0 4 314 716 0 0 260 /**
 * Makes a resized copy of the indicated image into this one using the
 * indicated filter.  The image to be copied is squashed and stretched to
 * match the dimensions of the current image, applying the appropriate filter
 * to perform the stretching.
 */ 3 4 this 3 758  6 radius 1 704  4 copy 1 756  
308 0 0 4 315 716 0 0 415 /**
 * Resizes from the given image, with a fixed radius of 0.5. This is a very
 * specialized and simple algorithm that doesn't handle dropping below the
 * Nyquist rate very well, but is quite a bit faster than the more general
 * box_filter(), above.  If borders are specified, they will further restrict
 * the size of the resulting image.  There's no point in using
 * quick_box_filter() on a single image.
 */ 4 4 this 3 758  4 copy 1 756  7 xborder 5 688  7 yborder 5 688  
309 0 0 4 316 716 0 0 64 /**
 * Computes a histogram of the colors used in the image.
 */ 2 4 this 3 758  4 hist 1 750  
310 0 0 4 317 716 0 0 284 /**
 * Reduces the number of unique colors in the image to (at most) the given
 * count.  Fewer colors than requested may be left in the image after this
 * operation, but never more.
 *
 * At present, this is only supported on images without an alpha channel.
 *
 * @since 1.10.5
 */ 2 4 this 3 758  10 max_colors 1 821  
311 0 0 4 318 716 0 0 92 /**
 * Variant of perlin_noise_fill that uses an existing StackedPerlinNoise2
 * object.
 */ 2 4 this 3 758  6 perlin 1 823  
312 0 0 4 318 716 0 0 267 /**
 * Fills the image with a grayscale perlin noise pattern based on the
 * indicated parameters.  Uses set_xel to set the grayscale values.  The sx
 * and sy parameters are in multiples of the size of this image.  See also the
 * PerlinNoise2 class in mathutil.
 */ 5 4 this 3 758  2 sx 1 704  2 sy 1 704  10 table_size 5 688  4 seed 5 825  
313 0 0 4 319 716 0 0 126 /**
 * Transforms every pixel using the operation (Ro,Go,Bo) =
 * conv.xform_point(Ri,Gi,Bi); Input must be a color image.
 */ 2 4 this 3 758  4 conv 1 826  
314 0 0 4 320 716 0 0 217 /**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * RGB channels, converts it to an image with a gamma curve of to_gamma in the
 * RGB channels.  Does not affect the alpha channel.
 */ 3 4 this 3 758  10 from_gamma 1 704  8 to_gamma 1 704  
315 0 0 4 321 716 0 0 218 /**
 * Assuming the image was constructed with a gamma curve of from_gamma in the
 * alpha channel, converts it to an image with a gamma curve of to_gamma in
 * the alpha channel.  Does not affect the RGB channels.
 */ 3 4 this 3 758  10 from_gamma 1 704  8 to_gamma 1 704  
316 0 0 4 322 716 0 0 148 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */ 2 4 this 3 758  13 gray_exponent 1 704  
317 0 0 4 322 716 0 0 148 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.
 */ 3 4 this 3 758  13 gray_exponent 1 704  14 alpha_exponent 1 704  
318 0 0 4 322 716 0 0 283 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */ 4 4 this 3 758  12 red_exponent 1 704  14 green_exponent 1 704  13 blue_exponent 1 704  
319 0 0 4 322 716 0 0 283 /**
 * Adjusts each channel of the image by raising the corresponding component
 * value to the indicated exponent, such that L' = L ^ exponent.  For a
 * grayscale image, the blue_exponent value is used for the grayscale value,
 * and red_exponent and green_exponent are unused.
 */ 5 4 this 3 758  12 red_exponent 1 704  14 green_exponent 1 704  13 blue_exponent 1 704  14 alpha_exponent 1 704  
320 0 0 7 323 819 0 0 71 /**
 * Returns the average color of all of the pixels in the image.
 */ 1 4 this 3 756  
321 0 0 7 324 820 0 0 103 /**
 * Returns the average color of all of the pixels in the image, including the
 * alpha channel.
 */ 1 4 this 3 756  
322 0 0 6 325 704 0 0 85 /**
 * Returns the average grayscale component of all of the pixels in the image.
 */ 1 4 this 3 756  
323 0 0 4 326 716 0 0 116 /**
 * Recursively fills in the minimum distance measured from a certain set of
 * points into the gray channel.
 */ 4 4 this 3 758  2 xi 1 688  2 yi 1 688  1 d 1 688  
324 0 0 7 342 829 0 0 0 2 4 this 3 758  1 y 1 688  
325 0 0 7 342 830 0 0 0 2 4 this 3 756  1 y 1 688  
326 0 0 7 343 758 545 0 0 1 4 this 3 756  
327 0 0 7 344 758 545 0 0 2 4 this 3 756  5 other 1 812  
328 0 0 7 344 758 545 0 0 2 4 this 3 756  5 other 1 756  
329 0 0 7 345 758 545 0 0 2 4 this 3 756  5 other 1 812  
330 0 0 7 345 758 545 0 0 2 4 this 3 756  5 other 1 756  
331 0 0 7 346 758 545 0 0 2 4 this 3 756  5 other 1 812  
332 0 0 7 346 758 545 0 0 2 4 this 3 756  5 other 1 756  
333 0 0 7 346 758 545 0 0 2 4 this 3 756  10 multiplier 1 704  
334 0 0 6 347 758 0 0 0 2 4 this 3 758  5 other 1 812  
335 0 0 6 347 758 0 0 0 2 4 this 3 758  5 other 1 756  
336 0 0 6 348 758 0 0 0 2 4 this 3 758  5 other 1 812  
337 0 0 6 348 758 0 0 0 2 4 this 3 758  5 other 1 756  
338 0 0 6 349 758 0 0 0 2 4 this 3 758  5 other 1 812  
339 0 0 6 349 758 0 0 0 2 4 this 3 758  5 other 1 756  
340 0 0 6 349 758 0 0 0 2 4 this 3 758  10 multiplier 1 704  
341 0 0 15 335 829 0 0 0 1 6 param0 0 831  
342 0 0 6 328 821 0 0 47 /**
 * Get the number of pixels in the row.
 */ 1 4 this 3 831  
343 0 0 7 329 820 0 0 0 2 4 this 3 831  1 x 1 688  
344 0 0 4 330 716 0 0 127 /**
 * Set the pixel at the given column in the row.  If the image has no alpha
 * channel, the alpha component is ignored.
 */ 3 4 this 3 829  1 x 1 688  1 v 1 812  
345 0 0 6 331 818 0 0 58 /**
 * Fetch the pixel at the given column in the row.
 */ 2 4 this 3 829  1 x 1 688  
346 0 0 4 332 716 0 0 56 /**
 * Set the pixel at the given column in the row.
 */ 3 4 this 3 829  1 x 1 688  1 v 1 745  
347 0 0 6 333 689 0 0 64 /**
 * Fetch the alpha value at the given column in the row.
 */ 2 4 this 3 831  1 x 1 688  
348 0 0 4 334 716 0 0 62 /**
 * Set the alpha value at the given column in the row.
 */ 3 4 this 3 829  1 x 1 688  1 v 1 689  
349 0 0 15 341 830 0 0 0 1 6 param0 0 833  
350 0 0 6 337 821 0 0 47 /**
 * Get the number of pixels in the row.
 */ 1 4 this 3 833  
351 0 0 7 338 820 0 0 0 2 4 this 3 833  1 x 1 688  
352 0 0 7 339 818 0 0 58 /**
 * Fetch the pixel at the given column in the row.
 */ 2 4 this 3 833  1 x 1 688  
353 0 0 6 340 689 0 0 64 /**
 * Fetch the alpha value at the given column in the row.
 */ 2 4 this 3 833  1 x 1 688  
354 0 0 7 351 835 668 0 532 /**
 * The constructor stores a pointer to the PNMImage you pass it, but it does
 * not take ownership of the object; you are responsible for ensuring that the
 * PNMImage does not destruct during the lifetime of the PNMPainter object.
 *
 * The xo, yo coordinates specify an optional offset for fill coordinates.  If
 * you are painting with a pattern fill, these specify the virtual coordinates
 * of the upper-left corner of the image, which can allow you to adjust the
 * pattern to line up with nested images, if necessary.
 */ 3 5 image 1 758  2 xo 5 688  2 yo 5 688  
355 0 0 15 351 835 668 0 0 1 6 param0 0 836  
356 0 0 4 353 716 0 0 330 /**
 * Specifies a PNMBrush that will be used for drawing lines and edges.  If the
 * brush is a bitmap brush, its image will be smeared pixelwise along the
 * line.
 *
 * Unlike the PNMImage passed to the constructor, the PNMPainter will take
 * ownership of the pen.  It is not necessary to keep a separate pointer to
 * it.
 */ 2 4 this 3 835  3 pen 1 712  
357 0 0 7 354 712 0 0 51 /**
 * Returns the current pen.  See set_pen().
 */ 1 4 this 3 836  
358 0 0 4 355 716 0 0 343 /**
 * Specifies a PNMBrush that will be used for filling in the interiors of
 * objects.  If the brush is a bitmap brush, its image will be tiled
 * throughout the space.
 *
 * Unlike the PNMImage passed to the constructor, the PNMPainter will take
 * ownership of the fill brush.  It is not necessary to keep a separate
 * pointer to it.
 */ 2 4 this 3 835  4 fill 1 712  
359 0 0 7 356 712 0 0 59 /**
 * Returns the current fill brush.  See set_fill().
 */ 1 4 this 3 836  
360 0 0 4 360 716 0 0 77 /**
 * Draws an antialiased point on the PNMImage, using the current pen.
 */ 3 4 this 3 835  1 x 1 704  1 y 1 704  
361 0 0 4 361 716 0 0 76 /**
 * Draws an antialiased line on the PNMImage, using the current pen.
 */ 5 4 this 3 835  2 xa 1 704  2 ya 1 704  2 xb 1 704  2 yb 1 704  
362 0 0 4 362 716 0 0 211 /**
 * Draws a filled rectangule on the PNMImage, using the current pen for the
 * outline, and the current fill brush for the interior.
 *
 * The two coordinates specify any two diagonally opposite corners.
 */ 5 4 this 3 835  2 xa 1 704  2 ya 1 704  2 xb 1 704  2 yb 1 704  
363 0 0 6 28 704 0 0 0 1 3 val 1 704  
364 0 0 6 28 704 0 0 0 1 3 val 1 838  
365 0 0 6 29 838 0 0 0 1 3 val 1 704  
366 0 0 6 29 838 0 0 0 1 3 val 1 838  
367 0 0 6 30 704 0 0 0 1 3 val 1 704  
368 0 0 6 30 704 0 0 0 1 3 val 1 838  
369 0 0 6 31 838 0 0 0 1 3 val 1 704  
370 0 0 6 31 838 0 0 0 1 3 val 1 838  
163
676 5 pixel 0 140289 5 pixel 5 pixel 0 0 0 1 371 390 3 839 840 841 12 372 373 374 375 376 377 378 379 380 381 382 383 0 0 0 0 0 0

677 4 gray 0 2105344 4 gray 4 gray 0 0 678 0 0 0 0 0 0 0 0 0 0

678 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

679 3 xel 0 2105345 3 xel 3 xel 0 0 676 0 0 0 0 0 0 0 0 0 0

680 11 PNMFileType 0 75777 11 PNMFileType 11 PNMFileType 0 0 0 0 0 3 842 843 844 5 395 396 397 398 399 1 856 0 1 0 681 0 0 0 0 126
/**
 * This is the base class of a family of classes that represent particular
 * image file types that PNMImage supports.
 */

681 13 TypedWritable 0 2048 13 TypedWritable 13 TypedWritable 0 0 0 0 0 0 0 0 0 0 0 0 135
/**
 * Base class for objects that can be written to and read from Bam files.
 *
 * See also TypedObject for detailed instructions.
 */

682 6 string 0 2105344 11 std::string 11 std::string 0 0 683 0 0 0 0 0 0 0 0 0 0

683 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

684 19 PNMFileTypeRegistry 0 26625 19 PNMFileTypeRegistry 19 PNMFileTypeRegistry 0 0 0 1 408 400 1 845 7 401 402 403 404 405 406 407 1 857 0 0 0 0 82
/**
 * This class maintains the set of all known PNMFileTypes in the universe.
 */

685 13 PNMFileType * 0 8576 13 PNMFileType * 13 PNMFileType * 0 0 680 0 0 0 0 0 0 0 0 0 0

686 14 PNMImageHeader 0 26625 14 PNMImageHeader 14 PNMImageHeader 0 0 0 1 409 411 6 846 847 848 849 850 851 20 410 412 413 414 415 416 417 418 419 420 421 422 423 424 425 426 427 428 429 430 0 0 0 0 7 687 692 693 694 696 698 700 350
/**
 * This is the base class of PNMImage, PNMReader, and PNMWriter.  It
 * encapsulates all the information associated with an image that describes
 * its size, number of channels, etc; that is, all the information about the
 * image except the image data itself.  It's the sort of information you
 * typically read from the image file's header.
 */

687 9 ColorType 0 794624 25 PNMImageHeader::ColorType 25 PNMImageHeader::ColorType 686 0 0 0 0 0 0 0 0 0 5 10 CT_invalid 26 PNMImageHeader::CT_invalid 0
0 12 CT_grayscale 28 PNMImageHeader::CT_grayscale 0
1 14 CT_two_channel 30 PNMImageHeader::CT_two_channel 0
2 8 CT_color 24 PNMImageHeader::CT_color 0
3 15 CT_four_channel 31 PNMImageHeader::CT_four_channel 0
4 0 198
// This enumerated type indicates the number of channels in the image, and
// also implies an image type.  You can treat it either as an integer number
// of channels or as an enumerated image type.

688 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

689 6 xelval 0 2105344 6 xelval 6 xelval 0 0 677 0 0 0 0 0 0 0 0 0 0

690 10 ColorSpace 0 532480 10 ColorSpace 10 ColorSpace 0 0 0 0 0 0 0 0 0 0 4 14 CS_unspecified 14 CS_unspecified 105
// This value is not a color space, but is used to indicate that a color
// space has not been specified.
0 9 CS_linear 9 CS_linear 389
// CS_linear is not a color space per se, but represents the working color
// space of graphics APIs, which is linearized.  Since the conversion from
// sRGB to linear is defined, one could posit that it has the ITU-R BT.709
// primaries, but this isn't meaningful as modern graphics APIs do not
// perform color management.  All colors in Panda3D are linear unless
// otherwise specified.
1 7 CS_sRGB 7 CS_sRGB 107
// This is the standard, gamma-2.2-corrected sRGB color space, as used by
// the majority of image formats.
2 8 CS_scRGB 8 CS_scRGB 110
// This is a 16-bit encoded linear color space capable of encoding color
// values in the -0.5...7.4999 range.
3 0 0

691 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

692 9 PixelSpec 0 403457 25 PNMImageHeader::PixelSpec 25 PNMImageHeader::PixelSpec 686 0 0 1 431 447 0 15 432 433 434 435 436 437 438 439 440 441 442 443 444 445 446 0 0 0 0 0 179
// Contains a single pixel specification used in compute_histogram() and
// make_histogram().  Note that pixels are stored by integer value, not by
// floating-point scaled value.

693 14 PixelSpecCount 0 403457 30 PNMImageHeader::PixelSpecCount 30 PNMImageHeader::PixelSpecCount 686 0 0 1 448 449 0 0 0 0 0 0 0 93
// Associates a pixel specification with an appearance count, for use in
// Histogram, below.

694 7 HistMap 0 2367488 23 PNMImageHeader::HistMap 23 PNMImageHeader::HistMap 686 0 695 0 0 0 0 0 0 0 0 0 0

695 21 map< PixelSpec, int > 0 2048 37 map< PNMImageHeader::PixelSpec, int > 37 map< PNMImageHeader::PixelSpec, int > 0 0 0 0 0 0 0 0 0 0 0 0 0

696 10 PixelCount 0 2367488 26 PNMImageHeader::PixelCount 26 PNMImageHeader::PixelCount 686 0 697 0 0 0 0 0 0 0 0 0 0

697 25 pvector< PixelSpecCount > 0 2048 41 pvector< PNMImageHeader::PixelSpecCount > 41 pvector< PNMImageHeader::PixelSpecCount > 0 0 0 0 0 0 0 0 0 0 0 0 0

698 7 Palette 0 2367488 23 PNMImageHeader::Palette 23 PNMImageHeader::Palette 686 0 699 0 0 0 0 0 0 0 0 0 0

699 20 pvector< PixelSpec > 0 2048 36 pvector< PNMImageHeader::PixelSpec > 36 pvector< PNMImageHeader::PixelSpec > 0 0 0 0 0 0 0 0 0 0 0 0 0

700 9 Histogram 0 403457 25 PNMImageHeader::Histogram 25 PNMImageHeader::Histogram 686 0 0 1 450 455 0 4 451 452 453 454 1 858 0 0 0 0 65
// Used to return a pixel histogram in PNMImage::get_histogram().

701 7 PfmFile 0 141313 7 PfmFile 7 PfmFile 0 0 0 1 456 538 2 852 853 81 457 458 459 460 461 462 463 464 465 466 467 468 469 470 471 472 473 474 475 476 477 478 479 480 481 482 483 484 485 486 487 488 489 490 491 492 493 494 495 496 497 498 499 500 501 502 503 504 505 506 507 508 509 510 511 512 513 514 515 516 517 518 519 520 521 522 523 524 525 526 527 528 529 530 531 532 533 534 535 536 537 0 0 1 0 686 0 0 0 0 155
/**
 * Defines a pfm file, a 2-d table of floating-point numbers, either
 * 3-component or 1-component, or with a special extension, 2- or 4-component.
 */

702 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

703 10 PN_float32 0 2105344 10 PN_float32 10 PN_float32 0 0 704 0 0 0 0 0 0 0 0 0 0

704 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

705 8 PNMBrush 0 75777 8 PNMBrush 8 PNMBrush 0 0 0 0 0 0 4 539 540 541 542 0 0 1 0 706 0 0 0 1 707 517
/**
 * This class is used to control the shape and color of the drawing operations
 * performed by a PNMPainter object.
 *
 * Normally, you don't create a PNMBrush directly; instead, use one of the
 * static PNMBrush::make_*() methods provided here.
 *
 * A PNMBrush is used to draw the border of a polygon or rectangle, as well as
 * for filling its interior.  When it is used to draw a border, the brush is
 * "smeared" over the border; when it is used to fill the interior, it is
 * tiled through the interior.
 */

706 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

707 11 BrushEffect 0 794624 21 PNMBrush::BrushEffect 21 PNMBrush::BrushEffect 705 0 0 0 0 0 0 0 0 0 4 6 BE_set 16 PNMBrush::BE_set 0
0 8 BE_blend 18 PNMBrush::BE_blend 0
1 9 BE_darken 19 PNMBrush::BE_darken 0
2 10 BE_lighten 20 PNMBrush::BE_lighten 0
3 0 0

708 8 PNMImage 0 26625 8 PNMImage 8 PNMImage 0 0 0 1 543 545 0 109 544 546 547 548 549 550 551 552 553 554 555 556 557 558 559 560 561 562 563 564 565 566 567 568 569 570 571 572 573 574 575 576 577 578 579 580 581 582 583 584 585 586 587 588 589 590 591 592 593 594 595 596 597 598 599 600 601 602 603 604 605 606 607 608 609 610 611 612 613 614 615 616 617 618 619 620 621 622 623 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 659 660 661 662 663 664 665 666 0 0 1 0 686 0 0 0 2 709 710 1516
/**
 * The name of this class derives from the fact that we originally implemented
 * it as a layer on top of the "pnm library", based on netpbm, which was built
 * to implement pbm, pgm, and pbm files, and is the underlying support of a
 * number of public-domain image file converters.  Nowadays we are no longer
 * derived directly from the pnm library, mainly to allow support of C++
 * iostreams instead of the C stdio FILE interface.
 *
 * Conceptually, a PNMImage is a two-dimensional array of xels, which are the
 * PNM-defined generic pixel type.  Each xel may have a red, green, and blue
 * component, or (if the image is grayscale) a gray component.  The image may
 * be read in, the individual xels manipulated, and written out again, or a
 * black image may be constructed from scratch.
 *
 * A PNMImage has a color space and a maxval, the combination of which defines
 * how a floating-point linear color value is encoded as an integer value in
 * memory.  The functions ending in _val operate on encoded colors, whereas
 * the regular ones work with linear floating-point values.  All operations
 * are color space correct unless otherwise specified.
 *
 * The image is of size XSize() by YSize() xels, numbered from top to bottom,
 * left to right, beginning at zero.
 *
 * Files can be specified by filename, or by an iostream pointer.  The
 * filename "-" refers to stdin or stdout.
 *
 * This class is not inherently thread-safe; use it from a single thread or
 * protect access using a mutex.
 */

709 3 Row 0 272385 13 PNMImage::Row 13 PNMImage::Row 708 0 0 1 653 0 0 7 646 647 648 649 650 651 652 0 0 0 0 0 96
// Provides an accessor for reading or writing the contents of one row of
// the image in-place.

710 4 CRow 0 272385 14 PNMImage::CRow 14 PNMImage::CRow 708 0 0 1 658 0 0 4 654 655 656 657 0 0 0 0 0 86
// Provides an accessor for reading the contents of one row of the image in-
// place.

711 10 PNMPainter 0 26625 10 PNMPainter 10 PNMPainter 0 0 0 1 667 668 2 854 855 7 669 670 671 672 673 674 675 0 0 0 0 0 334
/**
 * This class provides a number of convenient methods for painting drawings
 * directly into a PNMImage.
 *
 * It stores a pointer to the PNMImage you pass it, but it does not take
 * ownership of the object; you are responsible for ensuring that the PNMImage
 * does not destruct during the lifetime of the PNMPainter object.
 */

712 10 PNMBrush * 0 8576 10 PNMBrush * 10 PNMBrush * 0 0 705 0 0 0 0 0 0 0 0 0 0

713 7 pixel * 0 8576 7 pixel * 7 pixel * 0 0 676 0 0 0 0 0 0 0 0 0 0

714 13 pixel const * 0 8576 13 pixel const * 13 pixel const * 0 0 715 0 0 0 0 0 0 0 0 0 0

715 11 pixel const 0 8832 11 pixel const 11 pixel const 0 0 676 0 0 0 0 0 0 0 0 0 0

716 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

717 12 double const 0 8832 12 double const 12 double const 0 0 718 0 0 0 0 0 0 0 0 0 0

718 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

719 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 720 0 0 0 0 0 0 0 0 0 0

720 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

721 19 PNMFileType const * 0 8576 19 PNMFileType const * 19 PNMFileType const * 0 0 722 0 0 0 0 0 0 0 0 0 0

722 17 PNMFileType const 0 8832 17 PNMFileType const 17 PNMFileType const 0 0 680 0 0 0 0 0 0 0 0 0 0

723 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

724 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

725 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 724 0 0 0 0 0 0 0 0 0 0

726 27 PNMFileTypeRegistry const * 0 8576 27 PNMFileTypeRegistry const * 27 PNMFileTypeRegistry const * 0 0 727 0 0 0 0 0 0 0 0 0 0

727 25 PNMFileTypeRegistry const 0 8832 25 PNMFileTypeRegistry const 25 PNMFileTypeRegistry const 0 0 684 0 0 0 0 0 0 0 0 0 0

728 21 PNMFileTypeRegistry * 0 8576 21 PNMFileTypeRegistry * 21 PNMFileTypeRegistry * 0 0 684 0 0 0 0 0 0 0 0 0 0

729 16 PNMImageHeader * 0 8576 16 PNMImageHeader * 16 PNMImageHeader * 0 0 686 0 0 0 0 0 0 0 0 0 0

730 22 PNMImageHeader const * 0 8576 22 PNMImageHeader const * 22 PNMImageHeader const * 0 0 731 0 0 0 0 0 0 0 0 0 0

731 20 PNMImageHeader const 0 8832 20 PNMImageHeader const 20 PNMImageHeader const 0 0 686 0 0 0 0 0 0 0 0 0 0

732 12 LVecBase2i * 0 8576 12 LVecBase2i * 12 LVecBase2i * 0 0 691 0 0 0 0 0 0 0 0 0 0

733 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 734 0 0 0 0 0 0 0 0 0 0

734 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 735 0 0 0 0 0 0 0 0 0 0

735 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

736 9 istream * 0 8576 14 std::istream * 14 std::istream * 0 0 737 0 0 0 0 0 0 0 0 0 0

737 7 istream 0 2048 12 std::istream 12 std::istream 0 0 0 0 0 0 0 0 0 0 0 0 0

738 11 PNMReader * 0 8576 11 PNMReader * 11 PNMReader * 0 0 739 0 0 0 0 0 0 0 0 0 0

739 9 PNMReader 0 1050624 9 PNMReader 9 PNMReader 0 0 0 0 0 0 0 0 0 0 0 0 226
/**
 * This is an abstract base class that defines the interface for reading image
 * files of various types.  Any particular image file type that can be read
 * must define a class that inherits from PNMReader to read it.
 */

740 11 PNMWriter * 0 8576 11 PNMWriter * 11 PNMWriter * 0 0 741 0 0 0 0 0 0 0 0 0 0

741 9 PNMWriter 0 1050624 9 PNMWriter 9 PNMWriter 0 0 0 0 0 0 0 0 0 0 0 0 230
/**
 * This is an abstract base class that defines the interface for writing image
 * files of various types.  Any particular image file type that can be written
 * must define a class that inherits from PNMWriter to write it.
 */

742 17 PixelSpec const * 0 8576 33 PNMImageHeader::PixelSpec const * 33 PNMImageHeader::PixelSpec const * 0 0 743 0 0 0 0 0 0 0 0 0 0

743 15 PixelSpec const 0 8832 31 PNMImageHeader::PixelSpec const 31 PNMImageHeader::PixelSpec const 0 0 692 0 0 0 0 0 0 0 0 0 0

744 11 PixelSpec * 0 8576 27 PNMImageHeader::PixelSpec * 27 PNMImageHeader::PixelSpec * 0 0 692 0 0 0 0 0 0 0 0 0 0

745 11 xel const * 0 8576 11 xel const * 11 xel const * 0 0 746 0 0 0 0 0 0 0 0 0 0

746 9 xel const 0 8832 9 xel const 9 xel const 0 0 679 0 0 0 0 0 0 0 0 0 0

747 22 PixelSpecCount const * 0 8576 38 PNMImageHeader::PixelSpecCount const * 38 PNMImageHeader::PixelSpecCount const * 0 0 748 0 0 0 0 0 0 0 0 0 0

748 20 PixelSpecCount const 0 8832 36 PNMImageHeader::PixelSpecCount const 36 PNMImageHeader::PixelSpecCount const 0 0 693 0 0 0 0 0 0 0 0 0 0

749 16 PixelSpecCount * 0 8576 32 PNMImageHeader::PixelSpecCount * 32 PNMImageHeader::PixelSpecCount * 0 0 693 0 0 0 0 0 0 0 0 0 0

750 11 Histogram * 0 8576 27 PNMImageHeader::Histogram * 27 PNMImageHeader::Histogram * 0 0 700 0 0 0 0 0 0 0 0 0 0

751 17 Histogram const * 0 8576 33 PNMImageHeader::Histogram const * 33 PNMImageHeader::Histogram const * 0 0 752 0 0 0 0 0 0 0 0 0 0

752 15 Histogram const 0 8832 31 PNMImageHeader::Histogram const 31 PNMImageHeader::Histogram const 0 0 700 0 0 0 0 0 0 0 0 0 0

753 9 PfmFile * 0 8576 9 PfmFile * 9 PfmFile * 0 0 701 0 0 0 0 0 0 0 0 0 0

754 15 PfmFile const * 0 8576 15 PfmFile const * 15 PfmFile const * 0 0 755 0 0 0 0 0 0 0 0 0 0

755 13 PfmFile const 0 8832 13 PfmFile const 13 PfmFile const 0 0 701 0 0 0 0 0 0 0 0 0 0

756 16 PNMImage const * 0 8576 16 PNMImage const * 16 PNMImage const * 0 0 757 0 0 0 0 0 0 0 0 0 0

757 14 PNMImage const 0 8832 14 PNMImage const 14 PNMImage const 0 0 708 0 0 0 0 0 0 0 0 0 0

758 10 PNMImage * 0 8576 10 PNMImage * 10 PNMImage * 0 0 708 0 0 0 0 0 0 0 0 0 0

759 18 LVecBase4f const * 0 8576 18 LVecBase4f const * 18 LVecBase4f const * 0 0 760 0 0 0 0 0 0 0 0 0 0

760 16 LVecBase4f const 0 8832 16 LVecBase4f const 16 LVecBase4f const 0 0 761 0 0 0 0 0 0 0 0 0 0

761 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

762 16 LPoint2f const * 0 8576 16 LPoint2f const * 16 LPoint2f const * 0 0 763 0 0 0 0 0 0 0 0 0 0

763 14 LPoint2f const 0 8832 14 LPoint2f const 14 LPoint2f const 0 0 764 0 0 0 0 0 0 0 0 0 0

764 8 LPoint2f 0 2048 8 LPoint2f 8 LPoint2f 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

765 18 LVecBase2d const * 0 8576 18 LVecBase2d const * 18 LVecBase2d const * 0 0 766 0 0 0 0 0 0 0 0 0 0

766 16 LVecBase2d const 0 8832 16 LVecBase2d const 16 LVecBase2d const 0 0 767 0 0 0 0 0 0 0 0 0 0

767 10 LVecBase2d 0 2048 10 LVecBase2d 10 LVecBase2d 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

768 18 LVecBase2f const * 0 8576 18 LVecBase2f const * 18 LVecBase2f const * 0 0 769 0 0 0 0 0 0 0 0 0 0

769 16 LVecBase2f const 0 8832 16 LVecBase2f const 16 LVecBase2f const 0 0 770 0 0 0 0 0 0 0 0 0 0

770 10 LVecBase2f 0 2048 10 LVecBase2f 10 LVecBase2f 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

771 10 LPoint2f * 0 8576 10 LPoint2f * 10 LPoint2f * 0 0 764 0 0 0 0 0 0 0 0 0 0

772 16 LPoint3f const * 0 8576 16 LPoint3f const * 16 LPoint3f const * 0 0 773 0 0 0 0 0 0 0 0 0 0

773 14 LPoint3f const 0 8832 14 LPoint3f const 14 LPoint3f const 0 0 774 0 0 0 0 0 0 0 0 0 0

774 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

775 18 LVecBase3d const * 0 8576 18 LVecBase3d const * 18 LVecBase3d const * 0 0 776 0 0 0 0 0 0 0 0 0 0

776 16 LVecBase3d const 0 8832 16 LVecBase3d const 16 LVecBase3d const 0 0 777 0 0 0 0 0 0 0 0 0 0

777 10 LVecBase3d 0 2048 10 LVecBase3d 10 LVecBase3d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

778 18 LVecBase3f const * 0 8576 18 LVecBase3f const * 18 LVecBase3f const * 0 0 779 0 0 0 0 0 0 0 0 0 0

779 16 LVecBase3f const 0 8832 16 LVecBase3f const 16 LVecBase3f const 0 0 780 0 0 0 0 0 0 0 0 0 0

780 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

781 10 LPoint3f * 0 8576 10 LPoint3f * 10 LPoint3f * 0 0 774 0 0 0 0 0 0 0 0 0 0

782 16 LPoint4f const * 0 8576 16 LPoint4f const * 16 LPoint4f const * 0 0 783 0 0 0 0 0 0 0 0 0 0

783 14 LPoint4f const 0 8832 14 LPoint4f const 14 LPoint4f const 0 0 784 0 0 0 0 0 0 0 0 0 0

784 8 LPoint4f 0 2048 8 LPoint4f 8 LPoint4f 0 0 0 0 0 0 0 0 0 0 0 0 51
/**
 * This is a four-component point in space.
 */

785 18 LVecBase4d const * 0 8576 18 LVecBase4d const * 18 LVecBase4d const * 0 0 786 0 0 0 0 0 0 0 0 0 0

786 16 LVecBase4d const 0 8832 16 LVecBase4d const 16 LVecBase4d const 0 0 787 0 0 0 0 0 0 0 0 0 0

787 10 LVecBase4d 0 2048 10 LVecBase4d 10 LVecBase4d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

788 10 LPoint4f * 0 8576 10 LPoint4f * 10 LPoint4f * 0 0 784 0 0 0 0 0 0 0 0 0 0

789 12 LVecBase3f * 0 8576 12 LVecBase3f * 12 LVecBase3f * 0 0 780 0 0 0 0 0 0 0 0 0 0

790 12 LVecBase4d * 0 8576 12 LVecBase4d * 12 LVecBase4d * 0 0 787 0 0 0 0 0 0 0 0 0 0

791 12 LVecBase4f * 0 8576 12 LVecBase4f * 12 LVecBase4f * 0 0 761 0 0 0 0 0 0 0 0 0 0

792 16 LPoint4d const * 0 8576 16 LPoint4d const * 16 LPoint4d const * 0 0 793 0 0 0 0 0 0 0 0 0 0

793 14 LPoint4d const 0 8832 14 LPoint4d const 14 LPoint4d const 0 0 794 0 0 0 0 0 0 0 0 0 0

794 8 LPoint4d 0 2048 8 LPoint4d 8 LPoint4d 0 0 0 0 0 0 0 0 0 0 0 0 51
/**
 * This is a four-component point in space.
 */

795 17 LMatrix4d const * 0 8576 17 LMatrix4d const * 17 LMatrix4d const * 0 0 796 0 0 0 0 0 0 0 0 0 0

796 15 LMatrix4d const 0 8832 15 LMatrix4d const 15 LMatrix4d const 0 0 797 0 0 0 0 0 0 0 0 0 0

797 9 LMatrix4d 0 2048 9 LMatrix4d 9 LMatrix4d 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

798 17 LMatrix4f const * 0 8576 17 LMatrix4f const * 17 LMatrix4f const * 0 0 799 0 0 0 0 0 0 0 0 0 0

799 15 LMatrix4f const 0 8832 15 LMatrix4f const 15 LMatrix4f const 0 0 800 0 0 0 0 0 0 0 0 0 0

800 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

801 18 BoundingHexahedron 0 2048 18 BoundingHexahedron 18 BoundingHexahedron 0 0 0 0 0 0 0 0 0 0 0 0 300
/**
 * This defines a bounding convex hexahedron.  It is typically used to
 * represent a frustum, but may represent any enclosing convex hexahedron,
 * including simple boxes.  However, if all you want is an axis-aligned
 * bounding box, you may be better off with the simpler BoundingBox class.
 */

802 16 LPoint2d const * 0 8576 16 LPoint2d const * 16 LPoint2d const * 0 0 803 0 0 0 0 0 0 0 0 0 0

803 14 LPoint2d const 0 8832 14 LPoint2d const 14 LPoint2d const 0 0 804 0 0 0 0 0 0 0 0 0 0

804 8 LPoint2d 0 2048 8 LPoint2d 8 LPoint2d 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

805 20 BoundingHexahedron * 0 8576 20 BoundingHexahedron * 20 BoundingHexahedron * 0 0 801 0 0 0 0 0 0 0 0 0 0

806 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 807 0 0 0 0 0 0 0 0 0 0

807 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 808 0 0 0 0 0 0 0 0 0 0

808 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

809 11 Py_buffer * 0 8576 11 Py_buffer * 11 Py_buffer * 0 0 810 0 0 0 0 0 0 0 0 0 0

810 9 Py_buffer 0 2105344 9 Py_buffer 9 Py_buffer 0 0 811 0 0 0 0 0 0 0 0 0 0

811 10 bufferinfo 0 1024 10 bufferinfo 10 bufferinfo 0 0 0 0 0 0 0 0 0 0 0 0 0

812 15 LColorf const * 0 8576 15 LColorf const * 15 LColorf const * 0 0 813 0 0 0 0 0 0 0 0 0 0

813 13 LColorf const 0 8832 13 LColorf const 13 LColorf const 0 0 814 0 0 0 0 0 0 0 0 0 0

814 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 761 0 0 0 0 0 0 0 0 0 0

815 18 LRGBColorf const * 0 8576 18 LRGBColorf const * 18 LRGBColorf const * 0 0 816 0 0 0 0 0 0 0 0 0 0

816 16 LRGBColorf const 0 8832 16 LRGBColorf const 16 LRGBColorf const 0 0 817 0 0 0 0 0 0 0 0 0 0

817 10 LRGBColorf 0 2105344 10 LRGBColorf 10 LRGBColorf 0 0 780 0 0 0 0 0 0 0 0 0 0

818 5 xel * 0 8576 5 xel * 5 xel * 0 0 679 0 0 0 0 0 0 0 0 0 0

819 12 LRGBColorf * 0 8576 12 LRGBColorf * 12 LRGBColorf * 0 0 817 0 0 0 0 0 0 0 0 0 0

820 9 LColorf * 0 8576 9 LColorf * 9 LColorf * 0 0 814 0 0 0 0 0 0 0 0 0 0

821 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 822 0 0 0 0 0 0 0 0 0 0

822 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

823 21 StackedPerlinNoise2 * 0 8576 21 StackedPerlinNoise2 * 21 StackedPerlinNoise2 * 0 0 824 0 0 0 0 0 0 0 0 0 0

824 19 StackedPerlinNoise2 0 2048 19 StackedPerlinNoise2 19 StackedPerlinNoise2 0 0 0 0 0 0 0 0 0 0 0 0 147
/**
 * Implements a multi-layer PerlinNoise, with one or more high-frequency noise
 * functions added to a lower-frequency base noise function.
 */

825 17 unsigned long int 0 8214 17 unsigned long int 17 unsigned long int 0 1 0 0 0 0 0 0 0 0 0 0 0

826 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 827 0 0 0 0 0 0 0 0 0 0

827 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 828 0 0 0 0 0 0 0 0 0 0

828 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 800 0 0 0 0 0 0 0 0 0 0

829 5 Row * 0 8576 15 PNMImage::Row * 15 PNMImage::Row * 0 0 709 0 0 0 0 0 0 0 0 0 0

830 6 CRow * 0 8576 16 PNMImage::CRow * 16 PNMImage::CRow * 0 0 710 0 0 0 0 0 0 0 0 0 0

831 11 Row const * 0 8576 21 PNMImage::Row const * 21 PNMImage::Row const * 0 0 832 0 0 0 0 0 0 0 0 0 0

832 9 Row const 0 8832 19 PNMImage::Row const 19 PNMImage::Row const 0 0 709 0 0 0 0 0 0 0 0 0 0

833 12 CRow const * 0 8576 22 PNMImage::CRow const * 22 PNMImage::CRow const * 0 0 834 0 0 0 0 0 0 0 0 0 0

834 10 CRow const 0 8832 20 PNMImage::CRow const 20 PNMImage::CRow const 0 0 710 0 0 0 0 0 0 0 0 0 0

835 12 PNMPainter * 0 8576 12 PNMPainter * 12 PNMPainter * 0 0 711 0 0 0 0 0 0 0 0 0 0

836 18 PNMPainter const * 0 8576 18 PNMPainter const * 18 PNMPainter const * 0 0 837 0 0 0 0 0 0 0 0 0 0

837 16 PNMPainter const 0 8832 16 PNMPainter const 16 PNMPainter const 0 0 711 0 0 0 0 0 0 0 0 0 0

838 13 unsigned char 0 8198 13 unsigned char 13 unsigned char 0 5 0 0 0 0 0 0 0 0 0 0 0

0
17
839 1 b 0 6 677 384 385 0 0 0 0 0 0 8 pixel::b 0

840 1 g 0 6 677 386 387 0 0 0 0 0 0 8 pixel::g 0

841 1 r 0 6 677 388 389 0 0 0 0 0 0 8 pixel::r 0

842 4 name 0 2 682 395 0 0 0 0 0 0 0 17 PNMFileType::name 0

843 10 extensions 0 66 682 397 0 0 0 0 396 0 0 23 PNMFileType::extensions 0

844 19 suggested_extension 0 2 682 398 0 0 0 0 0 0 0 32 PNMFileType::suggested_extension 0

845 5 types 0 66 685 402 0 0 0 0 401 0 0 26 PNMFileTypeRegistry::types 0

846 12 num_channels 0 2 688 413 0 0 0 0 0 0 0 28 PNMImageHeader::num_channels 0

847 6 maxval 0 2 689 416 0 0 0 0 0 0 0 22 PNMImageHeader::maxval 0

848 11 color_space 0 2 690 417 0 0 0 0 0 0 0 27 PNMImageHeader::color_space 0

849 4 size 0 2 691 420 0 0 0 0 0 0 0 20 PNMImageHeader::size 0

850 7 comment 0 6 682 421 422 0 0 0 0 0 0 23 PNMImageHeader::comment 0

851 4 type 0 10 685 424 0 423 0 0 0 0 0 20 PNMImageHeader::type 0

852 5 valid 0 2 702 464 0 0 0 0 0 0 0 14 PfmFile::valid 0

853 5 scale 0 6 703 465 466 0 0 0 0 0 0 14 PfmFile::scale 0

854 3 pen 0 6 712 670 669 0 0 0 0 0 0 15 PNMPainter::pen 0

855 4 fill 0 6 712 672 671 0 0 0 0 0 0 16 PNMPainter::fill 0

3
856 14 get_extensions 0 396 397 27 PNMFileType::get_extensions 0

857 9 get_types 0 401 402 30 PNMFileTypeRegistry::get_types 0

858 10 get_pixels 0 451 452 37 PNMImageHeader::Histogram::get_pixels 0

