1730992849
3 3
16 libp3pgraphnodes 4 Bgt0 12 panda3d.core 
196
173 15 upcast_to_Light 0 12 369 26 LightNode::upcast_to_Light 0 1 4 30
upcast from LightNode to Light
40
Light *LightNode::upcast_to_Light(void);

174 21 downcast_to_LightNode 0 12 370 28 Light::downcast_to_LightNode 0 0 32
downcast from Light to LightNode
46
LightNode *Light::downcast_to_LightNode(void);

175 19 upcast_to_PandaNode 0 12 369 30 LightNode::upcast_to_PandaNode 0 1 5 34
upcast from LightNode to PandaNode
48
PandaNode *LightNode::upcast_to_PandaNode(void);

176 21 downcast_to_LightNode 0 12 371 32 PandaNode::downcast_to_LightNode 0 0 36
downcast from PandaNode to LightNode
50
LightNode *PandaNode::downcast_to_LightNode(void);

177 6 output 0 6 369 17 LightNode::output 0 1 1 100
// We have to explicitly publish these because they resolve the multiple
// inheritance.

/**
 *
 */
56
virtual void LightNode::output(std::ostream &out) const;

178 5 write 0 6 369 16 LightNode::write 0 1 2 10
/**
 *
 */
77
virtual void LightNode::write(std::ostream &out, int indent_level = 0) const;

179 14 get_class_type 0 4 369 25 LightNode::get_class_type 0 1 3 0
50
static TypeHandle LightNode::get_class_type(void);

180 10 ~LightNode 0 516 369 21 LightNode::~LightNode 0 0 0
28
LightNode::~LightNode(void);

181 12 AmbientLight 0 260 372 26 AmbientLight::AmbientLight 0 1 6 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
61
explicit AmbientLight::AmbientLight(std::string const &name);

182 14 get_class_type 0 4 372 28 AmbientLight::get_class_type 0 1 7 0
53
static TypeHandle AmbientLight::get_class_type(void);

183 13 ~AmbientLight 0 516 372 27 AmbientLight::~AmbientLight 0 0 0
34
AmbientLight::~AmbientLight(void);

184 12 CallbackNode 0 260 373 26 CallbackNode::CallbackNode 0 1 8 22
/**
 *
 */

/**
 *
 */
61
explicit CallbackNode::CallbackNode(std::string const &name);

185 17 set_cull_callback 0 4 373 31 CallbackNode::set_cull_callback 0 1 9 1131
/**
 * Sets the CallbackObject that will be notified when this node is visited
 * during the cull traversal.  This callback will be made during the cull
 * thread.
 *
 * The cull traversal is responsible for determining which nodes are visible
 * and within the view frustum, and for accumulating state and transform, and
 * generally building up the list of CullableObjects that are to be eventually
 * passed to the draw traversal for rendering.
 *
 * At the time the cull traversal callback is made, the node has been
 * determined to be visible and it has passed the bounding-volume test, so it
 * lies within the view frustum.
 *
 * The callback is passed an instance of a NodeCullCallbackData, which
 * contains pointers to the CullTraverser and CullTraverserData--enough data
 * to examine the current node and its place within the scene graph.  The
 * callback *replaces* the normal cull behavior, so if your callback does
 * nothing, the cull traversal will not continue below this node.  If you wish
 * the cull traversal to continue to visit this node and below, you must call
 * cbdata->upcall() from your callback.
 */
68
inline void CallbackNode::set_cull_callback(CallbackObject *object);

186 19 clear_cull_callback 0 4 373 33 CallbackNode::clear_cull_callback 0 1 10 78
/**
 * Removes the callback set by an earlier call to set_cull_callback().
 */
52
inline void CallbackNode::clear_cull_callback(void);

187 17 get_cull_callback 0 4 373 31 CallbackNode::get_cull_callback 0 1 11 65
/**
 * Returns the CallbackObject set by set_cull_callback().
 */
67
inline CallbackObject *CallbackNode::get_cull_callback(void) const;

188 17 set_draw_callback 0 4 373 31 CallbackNode::set_draw_callback 0 1 12 973
/**
 * Sets the CallbackObject that will be notified when this node is visited
 * during the draw traversal.  This callback will be made during the draw
 * thread.
 *
 * The draw traversal is responsible for actually issuing the commands to the
 * graphics engine to draw primitives.  Its job is to walk through the list of
 * CullableObjects build up by the cull traversal, as quickly as possible,
 * issuing the appropriate commands to draw each one.
 *
 * At the time the draw traversal callback is made, the graphics state has
 * been loaded with the correct modelview transform and render state, and the
 * primitives (if any) in this node are ready to be drawn.
 *
 * The callback is passed an instance of a GeomDrawCallbackData, which
 * contains pointers to the current state and transform, as well as the
 * current GSG.  There is a Geom pointer as well, but it will always be NULL
 * to this callback, since the CallbackNode does not itself contain any Geoms.
 */
68
inline void CallbackNode::set_draw_callback(CallbackObject *object);

189 19 clear_draw_callback 0 4 373 33 CallbackNode::clear_draw_callback 0 1 13 78
/**
 * Removes the callback set by an earlier call to set_draw_callback().
 */
52
inline void CallbackNode::clear_draw_callback(void);

190 17 get_draw_callback 0 4 373 31 CallbackNode::get_draw_callback 0 1 14 65
/**
 * Returns the CallbackObject set by set_draw_callback().
 */
67
inline CallbackObject *CallbackNode::get_draw_callback(void) const;

191 14 get_class_type 0 4 373 28 CallbackNode::get_class_type 0 1 15 0
53
static TypeHandle CallbackNode::get_class_type(void);

192 13 ~CallbackNode 0 516 373 27 CallbackNode::~CallbackNode 0 0 0
34
CallbackNode::~CallbackNode(void);

193 11 ComputeNode 0 260 376 24 ComputeNode::ComputeNode 0 1 16 134
/**
 * Creates a ComputeNode with the given name.  Use add_dispatch and  also
 * assign a shader using a ShaderAttrib.
 */

/**
 *
 */
59
explicit ComputeNode::ComputeNode(std::string const &name);

194 12 add_dispatch 0 4 376 25 ComputeNode::add_dispatch 0 2 17 18 388
/**
 * Adds a dispatch command with the given number of work groups in the X, Y,
 * and Z dimensions.  Any of these values may be set to 1 if the respective
 * dimension should not be used.
 */

/**
 * Adds a dispatch command with the given number of work groups in the X, Y,
 * and Z dimensions.  Any of these values may be set to 1 if the respective
 * dimension should not be used.
 */
161
inline void ComputeNode::add_dispatch(LVecBase3i const &num_groups);
inline void ComputeNode::add_dispatch(int num_groups_x, int num_groups_y, int num_groups_z);

195 18 get_num_dispatches 0 4 376 31 ComputeNode::get_num_dispatches 0 1 19 83
/**
 * Returns the number of times add_dispatch has been called on this object.
 */
63
inline std::size_t ComputeNode::get_num_dispatches(void) const;

196 12 get_dispatch 0 4 376 25 ComputeNode::get_dispatch 0 1 20 84
/**
 * Returns the group counts of the nth dispatch associated with this object.
 */
72
inline LVecBase3i const &ComputeNode::get_dispatch(std::size_t i) const;

197 12 set_dispatch 0 4 376 25 ComputeNode::set_dispatch 0 1 21 81
/**
 * Sets the group counts of the nth dispatch associated with this object.
 */
83
inline void ComputeNode::set_dispatch(std::size_t i, LVecBase3i const &num_groups);

198 15 insert_dispatch 0 4 376 28 ComputeNode::insert_dispatch 0 1 22 253
/**
 * Inserts a dispatch command with the given number of work groups in the X,
 * Y, and Z dimensions at the given position in the list of dispatch commands.
 * Any of these values may be set to 1 if the respective dimension should not
 * be used.
 */
86
inline void ComputeNode::insert_dispatch(std::size_t i, LVecBase3i const &num_groups);

199 15 remove_dispatch 0 4 376 28 ComputeNode::remove_dispatch 0 1 23 57
/**
 * Erases the given dispatch index from the list.
 */
56
inline void ComputeNode::remove_dispatch(std::size_t i);

200 16 clear_dispatches 0 4 376 29 ComputeNode::clear_dispatches 0 1 24 41
/**
 * Removes all dispatch commands.
 */
48
inline void ComputeNode::clear_dispatches(void);

201 14 get_class_type 0 4 376 27 ComputeNode::get_class_type 0 1 25 0
52
static TypeHandle ComputeNode::get_class_type(void);

202 12 ~ComputeNode 0 516 376 25 ComputeNode::~ComputeNode 0 0 0
32
ComputeNode::~ComputeNode(void);

203 15 upcast_to_Light 0 12 380 30 LightLensNode::upcast_to_Light 0 1 37 34
upcast from LightLensNode to Light
44
Light *LightLensNode::upcast_to_Light(void);

204 25 downcast_to_LightLensNode 0 12 370 32 Light::downcast_to_LightLensNode 0 0 36
downcast from Light to LightLensNode
54
LightLensNode *Light::downcast_to_LightLensNode(void);

205 16 upcast_to_Camera 0 12 380 31 LightLensNode::upcast_to_Camera 0 1 38 35
upcast from LightLensNode to Camera
46
Camera *LightLensNode::upcast_to_Camera(void);

206 25 downcast_to_LightLensNode 0 12 381 33 Camera::downcast_to_LightLensNode 0 0 37
downcast from Camera to LightLensNode
55
LightLensNode *Camera::downcast_to_LightLensNode(void);

207 14 ~LightLensNode 0 518 380 29 LightLensNode::~LightLensNode 0 0 10
/**
 *
 */
44
virtual LightLensNode::~LightLensNode(void);

208 18 has_specular_color 0 4 380 33 LightLensNode::has_specular_color 0 1 26 141
/**
 * Returns true if this light defines a specular color, false if the specular
 * color is derived automatically from the light color.
 */
58
inline bool LightLensNode::has_specular_color(void) const;

209 16 is_shadow_caster 0 4 380 31 LightLensNode::is_shadow_caster 0 1 27 75
/**
 * Returns whether this light is configured to cast shadows or not.
 */
56
inline bool LightLensNode::is_shadow_caster(void) const;

210 17 set_shadow_caster 0 4 380 32 LightLensNode::set_shadow_caster 0 2 28 29 609
/**
 * Sets the flag indicating whether this light should cast shadows or not.
 * This is the variant without buffer size, meaning that the current buffer
 * size will be kept (512x512 is the default). Note that enabling shadows will
 * require the shader generator to be enabled on the scene.
 */

/**
 * Sets the flag indicating whether this light should cast shadows or not.
 * The xsize and ysize parameters specify the size of the shadow buffer that
 * will be set up, the sort parameter specifies the sort.  Note that enabling
 * shadows will require the shader generator to be enabled on the scene.
 */
155
void LightLensNode::set_shadow_caster(bool caster);
void LightLensNode::set_shadow_caster(bool caster, int buffer_xsize, int buffer_ysize, int sort = -10);

211 22 get_shadow_buffer_sort 0 4 380 37 LightLensNode::get_shadow_buffer_sort 0 1 30 85
/**
 * Returns the sort of the shadow buffer to be created for this light source.
 */
61
inline int LightLensNode::get_shadow_buffer_sort(void) const;

212 22 get_shadow_buffer_size 0 4 380 37 LightLensNode::get_shadow_buffer_size 0 1 31 85
/**
 * Returns the size of the shadow buffer to be created for this light source.
 */
68
inline LVecBase2i LightLensNode::get_shadow_buffer_size(void) const;

213 22 set_shadow_buffer_size 0 4 380 37 LightLensNode::set_shadow_buffer_size 0 1 32 82
/**
 * Sets the size of the shadow buffer to be created for this light source.
 */
74
inline void LightLensNode::set_shadow_buffer_size(LVecBase2i const &size);

214 17 get_shadow_buffer 0 4 380 32 LightLensNode::get_shadow_buffer 0 1 33 214
/**
 * Returns the buffer that has been constructed for a given GSG, or NULL if no
 * such buffer has (yet) been constructed.  This should be used for debugging
 * only, you will not need to call this normally.
 */
92
inline GraphicsOutputBase *LightLensNode::get_shadow_buffer(GraphicsStateGuardianBase *gsg);

215 6 output 0 6 380 21 LightLensNode::output 0 1 34 100
// We have to explicitly publish these because they resolve the multiple
// inheritance.

/**
 *
 */
60
virtual void LightLensNode::output(std::ostream &out) const;

216 5 write 0 6 380 20 LightLensNode::write 0 1 35 10
/**
 *
 */
81
virtual void LightLensNode::write(std::ostream &out, int indent_level = 0) const;

217 14 get_class_type 0 4 380 29 LightLensNode::get_class_type 0 1 36 0
54
static TypeHandle LightLensNode::get_class_type(void);

218 16 DirectionalLight 0 260 384 34 DirectionalLight::DirectionalLight 0 1 39 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
69
explicit DirectionalLight::DirectionalLight(std::string const &name);

219 18 set_specular_color 0 4 384 36 DirectionalLight::set_specular_color 0 1 40 72
/**
 * Sets the color of specular highlights generated by the light.
 */
70
inline void DirectionalLight::set_specular_color(LColor const &color);

220 20 clear_specular_color 0 4 384 38 DirectionalLight::clear_specular_color 0 1 41 115
/**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */
57
inline void DirectionalLight::clear_specular_color(void);

221 18 get_specular_color 0 6 384 36 DirectionalLight::get_specular_color 0 0 120
/**
 * Returns the color of specular highlights generated by the light.  This is
 * usually the same as get_color().
 */
84
virtual inline LColor const &DirectionalLight::get_specular_color(void) const final;

222 9 get_point 0 4 384 27 DirectionalLight::get_point 0 1 42 349
/**
 * Returns the point in space at which the light is located.  This is local to
 * the coordinate space in which the light is assigned.
 *
 * This actually has no bearing on the visual effect of the light, since the
 * light is rendered as if it were infinitely far away.  This is only used to
 * create a visible representation of the light.
 */
62
inline LPoint3 const &DirectionalLight::get_point(void) const;

223 9 set_point 0 4 384 27 DirectionalLight::set_point 0 1 43 65
/**
 * Sets the point in space at which the light is located.
 */
62
inline void DirectionalLight::set_point(LPoint3 const &point);

224 13 get_direction 0 4 384 31 DirectionalLight::get_direction 0 1 44 135
/**
 * Returns the direction in which the light is aimed.  This is local to the
 * coordinate space in which the light is assigned.
 */
67
inline LVector3 const &DirectionalLight::get_direction(void) const;

225 13 set_direction 0 4 384 31 DirectionalLight::set_direction 0 1 45 58
/**
 * Sets the direction in which the light is aimed.
 */
71
inline void DirectionalLight::set_direction(LVector3 const &direction);

226 14 get_class_type 0 4 384 32 DirectionalLight::get_class_type 0 1 46 0
57
static TypeHandle DirectionalLight::get_class_type(void);

227 17 ~DirectionalLight 0 516 384 35 DirectionalLight::~DirectionalLight 0 0 0
42
DirectionalLight::~DirectionalLight(void);

228 7 LODNode 0 260 395 16 LODNode::LODNode 0 1 47 22
/**
 *
 */

/**
 *
 */
58
inline explicit LODNode::LODNode(std::string const &name);

229 16 make_default_lod 0 4 395 25 LODNode::make_default_lod 0 1 48 98
/**
 * Creates a new LODNode of the type specified by the default-lod-type config
 * variable.
 */
79
static PointerTo< LODNode > LODNode::make_default_lod(std::string const &name);

230 10 add_switch 0 4 395 19 LODNode::add_switch 0 1 49 355
/**
 * Adds a switch range to the LODNode.  This implies that the corresponding
 * child node has been parented to the node.
 *
 * The sense of in vs.  out distances is as if the object were coming towards
 * you from far away: it switches "in" at the far distance, and switches "out"
 * at the close distance.  Thus, "in" should be larger than "out".
 */
65
inline void LODNode::add_switch(PN_stdfloat in, PN_stdfloat out);

231 10 set_switch 0 4 395 19 LODNode::set_switch 0 1 50 98
/**
 * Changes the switching range of a particular child of the LODNode.  See
 * add_switch().
 */
76
inline bool LODNode::set_switch(int index, PN_stdfloat in, PN_stdfloat out);

232 14 clear_switches 0 4 395 23 LODNode::clear_switches 0 1 51 146
/**
 * Removes the set of switching ranges for the LODNode, presumably in
 * conjunction with removing all of its children.  See add_switch().
 */
42
inline void LODNode::clear_switches(void);

233 16 get_num_switches 0 4 395 25 LODNode::get_num_switches 0 1 52 184
/**
 * Returns the number of switch ranges added to the LODNode.  This should
 * correspond to the number of children of the node in order for the LODNode
 * to function correctly.
 */
49
inline int LODNode::get_num_switches(void) const;

234 6 get_in 0 4 395 15 LODNode::get_in 0 1 53 136
/**
 * Returns the "in" distance of the indicated switch range.  This should be
 * larger than the "out" distance of the same range.
 */
52
inline PN_stdfloat LODNode::get_in(int index) const;

235 7 get_out 0 4 395 16 LODNode::get_out 0 1 54 137
/**
 * Returns the "out" distance of the indicated switch range.  This should be
 * smaller than the "in" distance of the same range.
 */
53
inline PN_stdfloat LODNode::get_out(int index) const;

236 17 get_lowest_switch 0 4 395 26 LODNode::get_lowest_switch 0 1 55 220
/**
 * Returns the index number of the child with the lowest level of detail; that
 * is, the one that is designed to be seen from the farthest away.  This is
 * usually the first child, but it is not necessarily so.
 */
50
inline int LODNode::get_lowest_switch(void) const;

237 18 get_highest_switch 0 4 395 27 LODNode::get_highest_switch 0 1 56 228
/**
 * Returns the index number of the child with the highest level of detail;
 * that is, the one that is designed to be seen from the closest to the
 * camera.  This is usually the last child, but it is not necessarily so.
 */
51
inline int LODNode::get_highest_switch(void) const;

238 12 force_switch 0 4 395 21 LODNode::force_switch 0 1 57 149
/**
 * Forces the LODNode to show the indicated level instead of the level that
 * would normally be shown based on the distance from the camera.
 */
45
inline void LODNode::force_switch(int index);

239 18 clear_force_switch 0 4 395 27 LODNode::clear_force_switch 0 1 58 133
/**
 * Undoes the effect of a previous call to force_switch() and releases the
 * LODNode to once again display the normal level.
 */
46
inline void LODNode::clear_force_switch(void);

240 13 set_lod_scale 0 4 395 22 LODNode::set_lod_scale 0 1 59 331
// for performance tuning, increasing this value should improve performance
// at the cost of model quality

// for performance tuning, increasing this value should improve performance
// at the cost of model quality

/**
 * Sets the multiplier for lod distances.  A higher value means you'll see
 * farther switchs than normal
 */
54
inline void LODNode::set_lod_scale(PN_stdfloat value);

241 13 get_lod_scale 0 4 395 22 LODNode::get_lod_scale 0 1 60 51
/**
 * Returns the multiplier for lod distances
 */
54
inline PN_stdfloat LODNode::get_lod_scale(void) const;

242 10 set_center 0 4 395 19 LODNode::set_center 0 1 61 172
/**
 * Specifies the center of the LOD.  This is the point that is compared to the
 * camera (in camera space) to determine the particular LOD that should be
 * chosen.
 */
55
inline void LODNode::set_center(LPoint3 const &center);

243 10 get_center 0 4 395 19 LODNode::get_center 0 1 62 170
/**
 * Returns the center of the LOD.  This is the point that is compared to the
 * camera (in camera space) to determine the particular LOD that should be
 * chosen.
 */
54
inline LPoint3 const &LODNode::get_center(void) const;

244 11 show_switch 0 4 395 20 LODNode::show_switch 0 2 63 64 1102
/**
 * This is provided as a debugging aid.  show_switch() will put the LODNode
 * into a special mode where rather than computing and drawing the appropriate
 * level of the LOD, a ring is drawn around the LODNode center indicating the
 * switch distances from the camera for the indicated level, and the geometry
 * of the indicated level is drawn in wireframe.
 *
 * Multiple different levels can be visualized this way at once.  Call
 * hide_switch() or hide_all_switches() to undo this mode and restore the
 * LODNode to its normal behavior.
 */

/**
 * This is provided as a debugging aid.  show_switch() will put the LODNode
 * into a special mode where rather than computing and drawing the appropriate
 * level of the LOD, a ring is drawn around the LODNode center indicating the
 * switch distances from the camera for the indicated level, and the geometry
 * of the indicated level is drawn in wireframe.
 *
 * Multiple different levels can be visualized this way at once.  Call
 * hide_switch() or hide_all_switches() to undo this mode and restore the
 * LODNode to its normal behavior.
 */
96
void LODNode::show_switch(int index);
void LODNode::show_switch(int index, LColor const &color);

245 11 hide_switch 0 4 395 20 LODNode::hide_switch 0 1 65 53
/**
 * Disables a previous call to show_switch().
 */
37
void LODNode::hide_switch(int index);

246 17 show_all_switches 0 4 395 26 LODNode::show_all_switches 0 1 66 52
/**
 * Shows all levels in their default colors.
 */
38
void LODNode::show_all_switches(void);

247 17 hide_all_switches 0 4 395 26 LODNode::hide_all_switches 0 1 67 71
/**
 * Hides all levels, restoring the LODNode to normal operation.
 */
38
void LODNode::hide_all_switches(void);

248 12 is_any_shown 0 4 395 21 LODNode::is_any_shown 0 1 68 159
/**
 * Returns true if any switch has been shown with show_switch(), indicating
 * the LODNode is in debug show mode; or false if it is in the normal mode.
 */
46
inline bool LODNode::is_any_shown(void) const;

249 19 verify_child_bounds 0 4 395 28 LODNode::verify_child_bounds 0 1 69 274
/**
 * Returns true if the bounding volumes for the geometry of each fhild node
 * entirely fits within the switch_in radius for that child, or false
 * otherwise.  It is almost always a mistake for the geometry of an LOD level
 * to be larger than its switch_in radius.
 */
46
bool LODNode::verify_child_bounds(void) const;

250 14 get_class_type 0 4 395 23 LODNode::get_class_type 0 1 70 0
48
static TypeHandle LODNode::get_class_type(void);

251 8 ~LODNode 0 516 395 17 LODNode::~LODNode 0 0 0
24
LODNode::~LODNode(void);

252 11 FadeLODNode 0 260 399 24 FadeLODNode::FadeLODNode 0 1 71 22
/**
 *
 */

/**
 *
 */
59
explicit FadeLODNode::FadeLODNode(std::string const &name);

253 13 set_fade_time 0 4 399 26 FadeLODNode::set_fade_time 0 1 72 55
/**
 * set the time taken to complete an LOD switch
 */
54
inline void FadeLODNode::set_fade_time(PN_stdfloat t);

254 13 get_fade_time 0 4 399 26 FadeLODNode::get_fade_time 0 1 73 55
/**
 * get the time taken to complete an LOD switch
 */
58
inline PN_stdfloat FadeLODNode::get_fade_time(void) const;

255 12 set_fade_bin 0 4 399 25 FadeLODNode::set_fade_bin 0 1 74 124
/**
 * Specifies the cull bin and draw order that is assigned to the fading part
 * of the geometry during a transition.
 */
72
void FadeLODNode::set_fade_bin(std::string const &name, int draw_order);

256 17 get_fade_bin_name 0 4 399 30 FadeLODNode::get_fade_bin_name 0 1 75 107
/**
 * Returns the cull bin that is assigned to the fading part of the geometry
 * during a transition.
 */
69
inline std::string const &FadeLODNode::get_fade_bin_name(void) const;

257 23 get_fade_bin_draw_order 0 4 399 36 FadeLODNode::get_fade_bin_draw_order 0 1 76 135
/**
 * Returns the draw order that is assigned (along with the bin name) to the
 * fading part of the geometry during a transition.
 */
60
inline int FadeLODNode::get_fade_bin_draw_order(void) const;

258 23 set_fade_state_override 0 4 399 36 FadeLODNode::set_fade_state_override 0 1 77 191
/**
 * Specifies the override value that is applied to the state changes necessary
 * to apply the fade effect.  This should be larger than any attrib overrides
 * on the fading geometry.
 */
56
void FadeLODNode::set_fade_state_override(int override);

259 23 get_fade_state_override 0 4 399 36 FadeLODNode::get_fade_state_override 0 1 78 189
/**
 * Returns the override value that is applied to the state changes necessary
 * to apply the fade effect.  This should be larger than any attrib overrides
 * on the fading geometry.
 */
60
inline int FadeLODNode::get_fade_state_override(void) const;

260 14 get_class_type 0 4 399 27 FadeLODNode::get_class_type 0 1 79 0
52
static TypeHandle FadeLODNode::get_class_type(void);

261 12 ~FadeLODNode 0 516 399 25 FadeLODNode::~FadeLODNode 0 0 0
32
FadeLODNode::~FadeLODNode(void);

262 8 get_trav 0 4 403 30 NodeCullCallbackData::get_trav 0 1 80 193
/**
 * Returns the CullTraverser in use at the time of the callback.  This object
 * contains data that does not change during the traversal, such as the
 * DisplayRegion and Camera in use.
 */
65
inline CullTraverser *NodeCullCallbackData::get_trav(void) const;

263 8 get_data 0 4 403 30 NodeCullCallbackData::get_data 0 1 81 222
/**
 * Returns the CullTraverserData in use at the time of the callback.  This
 * object contains data that changes at each node of the traversal, such as
 * the current node and the current net transform to that node.
 */
69
inline CullTraverserData &NodeCullCallbackData::get_data(void) const;

264 14 get_class_type 0 4 403 36 NodeCullCallbackData::get_class_type 0 1 82 0
61
static TypeHandle NodeCullCallbackData::get_class_type(void);

265 10 PointLight 0 260 405 22 PointLight::PointLight 0 1 83 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
57
explicit PointLight::PointLight(std::string const &name);

266 18 set_specular_color 0 4 405 30 PointLight::set_specular_color 0 1 84 72
/**
 * Sets the color of specular highlights generated by the light.
 */
64
inline void PointLight::set_specular_color(LColor const &color);

267 20 clear_specular_color 0 4 405 32 PointLight::clear_specular_color 0 1 85 115
/**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */
51
inline void PointLight::clear_specular_color(void);

268 18 get_specular_color 0 6 405 30 PointLight::get_specular_color 0 0 120
/**
 * Returns the color of specular highlights generated by the light.  This is
 * usually the same as get_color().
 */
78
virtual inline LColor const &PointLight::get_specular_color(void) const final;

269 15 set_attenuation 0 4 405 27 PointLight::set_attenuation 0 1 86 190
/**
 * Sets the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */
70
inline void PointLight::set_attenuation(LVecBase3 const &attenuation);

270 15 get_attenuation 0 6 405 27 PointLight::get_attenuation 0 0 193
/**
 * Returns the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */
78
virtual inline LVecBase3 const &PointLight::get_attenuation(void) const final;

271 16 get_max_distance 0 4 405 28 PointLight::get_max_distance 0 1 87 122
/**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */
60
inline PN_stdfloat PointLight::get_max_distance(void) const;

272 16 set_max_distance 0 4 405 28 PointLight::set_max_distance 0 1 88 161
/**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */
67
inline void PointLight::set_max_distance(PN_stdfloat max_distance);

273 9 get_point 0 4 405 21 PointLight::get_point 0 1 89 160
/**
 * Returns the point in space at which the light is located.  This is local to
 * the coordinate space in which the light is assigned, and is usually 0.
 */
56
inline LPoint3 const &PointLight::get_point(void) const;

274 9 set_point 0 4 405 21 PointLight::set_point 0 1 90 77
/**
 * Sets the point in space at which the light is located.  Usually 0.
 */
56
inline void PointLight::set_point(LPoint3 const &point);

275 14 get_class_type 0 4 405 26 PointLight::get_class_type 0 1 91 0
51
static TypeHandle PointLight::get_class_type(void);

276 11 ~PointLight 0 516 405 23 PointLight::~PointLight 0 0 0
30
PointLight::~PointLight(void);

277 14 RectangleLight 0 260 409 30 RectangleLight::RectangleLight 0 1 92 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
65
explicit RectangleLight::RectangleLight(std::string const &name);

278 16 get_max_distance 0 4 409 32 RectangleLight::get_max_distance 0 1 93 122
/**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */
64
inline PN_stdfloat RectangleLight::get_max_distance(void) const;

279 16 set_max_distance 0 4 409 32 RectangleLight::set_max_distance 0 1 94 161
/**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */
71
inline void RectangleLight::set_max_distance(PN_stdfloat max_distance);

280 14 get_class_type 0 4 409 30 RectangleLight::get_class_type 0 1 95 0
55
static TypeHandle RectangleLight::get_class_type(void);

281 15 ~RectangleLight 0 516 409 31 RectangleLight::~RectangleLight 0 0 0
38
RectangleLight::~RectangleLight(void);

282 18 SelectiveChildNode 0 260 410 38 SelectiveChildNode::SelectiveChildNode 0 1 96 22
/**
 *
 */

/**
 *
 */
80
inline explicit SelectiveChildNode::SelectiveChildNode(std::string const &name);

283 14 get_class_type 0 4 410 34 SelectiveChildNode::get_class_type 0 1 97 0
59
static TypeHandle SelectiveChildNode::get_class_type(void);

284 19 ~SelectiveChildNode 0 516 410 39 SelectiveChildNode::~SelectiveChildNode 0 0 0
46
SelectiveChildNode::~SelectiveChildNode(void);

285 28 upcast_to_SelectiveChildNode 0 12 411 42 SequenceNode::upcast_to_SelectiveChildNode 0 1 102 46
upcast from SequenceNode to SelectiveChildNode
69
SelectiveChildNode *SequenceNode::upcast_to_SelectiveChildNode(void);

286 24 downcast_to_SequenceNode 0 12 410 44 SelectiveChildNode::downcast_to_SequenceNode 0 0 48
downcast from SelectiveChildNode to SequenceNode
65
SequenceNode *SelectiveChildNode::downcast_to_SequenceNode(void);

287 23 upcast_to_AnimInterface 0 12 411 37 SequenceNode::upcast_to_AnimInterface 0 1 103 41
upcast from SequenceNode to AnimInterface
59
AnimInterface *SequenceNode::upcast_to_AnimInterface(void);

288 24 downcast_to_SequenceNode 0 12 412 39 AnimInterface::downcast_to_SequenceNode 0 0 43
downcast from AnimInterface to SequenceNode
60
SequenceNode *AnimInterface::downcast_to_SequenceNode(void);

289 12 SequenceNode 0 260 411 26 SequenceNode::SequenceNode 0 1 98 22
/**
 *
 */

/**
 *
 */
68
inline explicit SequenceNode::SequenceNode(std::string const &name);

290 14 get_num_frames 0 6 411 28 SequenceNode::get_num_frames 0 1 99 278
/**
 * Returns the number of frames in the animation.  This is a property of the
 * animation and may not be directly adjusted by the user (although it may
 * change without warning with certain kinds of animations, since this is a
 * virtual method that may be overridden).
 */
53
virtual int SequenceNode::get_num_frames(void) const;

291 14 set_frame_rate 0 4 411 28 SequenceNode::set_frame_rate 0 1 100 173
/**
 * Changes the advertised frame rate of the SequenceNode.  This can be used in
 * conjunction with get_play_rate() to change the effective frame rate of the
 * node.
 */
60
inline void SequenceNode::set_frame_rate(double frame_rate);

292 14 get_frame_rate 0 4 411 28 SequenceNode::get_frame_rate 0 0 0
55
inline double SequenceNode::get_frame_rate(void) const;

293 14 get_class_type 0 4 411 28 SequenceNode::get_class_type 0 1 101 0
53
static TypeHandle SequenceNode::get_class_type(void);

294 13 ~SequenceNode 0 516 411 27 SequenceNode::~SequenceNode 0 0 0
34
SequenceNode::~SequenceNode(void);

295 15 ShaderGenerator 0 260 414 32 ShaderGenerator::ShaderGenerator 0 2 104 105 413
/**
 * Create a ShaderGenerator.  This has no state, except possibly to cache
 * certain results.  The given parameters contain information about the
 * capabilities of the target for which the shader will be compiled.
 */

/**
 * Create a ShaderGenerator.  This has no state, except possibly to cache
 * certain results.  The parameter that must be passed is the GSG to which the
 * shader generator belongs.
 */
147
ShaderGenerator::ShaderGenerator(GraphicsStateGuardianBase const *gsg);
inline ShaderGenerator::ShaderGenerator(ShaderGenerator const &) = default;

296 17 synthesize_shader 0 6 414 34 ShaderGenerator::synthesize_shader 0 1 106 794
/**
 * This is the routine that implements the next-gen fixed function pipeline by
 * synthesizing a shader.  It also takes care of setting up any buffers needed
 * to produce the requested effects.
 *
 * Currently supports:
 * - flat colors
 * - vertex colors
 * - lighting
 * - normal maps, even multiple
 * - gloss maps, but not multiple
 * - glow maps, but not multiple
 * - materials, but not updates to materials
 * - 2D textures
 * - all texture stage modes, including combine modes
 * - color scale attrib
 * - light ramps (for cartoon shading)
 * - shadow mapping
 * - most texgen modes
 * - texmatrix
 * - 1D/2D/3D textures, cube textures, 2D tex arrays
 * - linear/exp/exp2 fog
 * - animation
 *
 * Potential optimizations
 * - omit attenuation calculations if attenuation off
 *
 */
134
virtual ConstPointerTo< ShaderAttrib > ShaderGenerator::synthesize_shader(RenderState const *rs, GeomVertexAnimationSpec const &anim);

297 24 rehash_generated_shaders 0 4 414 41 ShaderGenerator::rehash_generated_shaders 0 1 107 354
/**
 * Rehashes all the states with generated shaders, removing the ones that are
 * no longer fresh.
 *
 * Call this if certain state has changed in such a way as to require a rerun
 * of the shader generator.  This should be rare because in most cases, the
 * shader generator will automatically regenerate shaders as necessary.
 *
 * @since 1.10.0
 */
53
void ShaderGenerator::rehash_generated_shaders(void);

298 23 clear_generated_shaders 0 4 414 40 ShaderGenerator::clear_generated_shaders 0 1 108 159
/**
 * Removes all previously generated shaders, requiring all shaders to be
 * regenerated.  Does not clear cache of compiled shaders.
 *
 * @since 1.10.0
 */
52
void ShaderGenerator::clear_generated_shaders(void);

299 14 get_class_type 0 4 414 31 ShaderGenerator::get_class_type 0 1 109 0
56
static TypeHandle ShaderGenerator::get_class_type(void);

300 11 SphereLight 0 260 416 24 SphereLight::SphereLight 0 1 110 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
59
explicit SphereLight::SphereLight(std::string const &name);

301 10 get_radius 0 4 416 23 SphereLight::get_radius 0 1 111 44
/**
 * Returns the radius of the sphere.
 */
55
inline PN_stdfloat SphereLight::get_radius(void) const;

302 10 set_radius 0 4 416 23 SphereLight::set_radius 0 1 112 41
/**
 * Sets the radius of the sphere.
 */
56
inline void SphereLight::set_radius(PN_stdfloat radius);

303 14 get_class_type 0 4 416 27 SphereLight::get_class_type 0 1 113 0
52
static TypeHandle SphereLight::get_class_type(void);

304 12 ~SphereLight 0 516 416 25 SphereLight::~SphereLight 0 0 0
32
SphereLight::~SphereLight(void);

305 9 Spotlight 0 260 417 20 Spotlight::Spotlight 0 1 114 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
46
Spotlight::Spotlight(std::string const &name);

306 12 set_exponent 0 4 417 23 Spotlight::set_exponent 0 1 115 398
/**
 * Sets the exponent that controls the amount of light falloff from the center
 * of the spotlight.  The light is attenuated by the cosine of the angle
 * between the direction of the light and the direction of the point being
 * lighted, raised to the power of this exponent.  Thus, higher exponents
 * result in a more focused light source, regardless of the field-of-view of
 * the lens.
 */
58
inline void Spotlight::set_exponent(PN_stdfloat exponent);

307 12 get_exponent 0 6 417 23 Spotlight::get_exponent 0 0 131
/**
 * Returns the exponent that controls the amount of light falloff from the
 * center of the spotlight.  See set_exponent().
 */
69
virtual inline PN_stdfloat Spotlight::get_exponent(void) const final;

308 18 set_specular_color 0 4 417 29 Spotlight::set_specular_color 0 1 116 72
/**
 * Sets the color of specular highlights generated by the light.
 */
63
inline void Spotlight::set_specular_color(LColor const &color);

309 20 clear_specular_color 0 4 417 31 Spotlight::clear_specular_color 0 1 117 115
/**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */
50
inline void Spotlight::clear_specular_color(void);

310 18 get_specular_color 0 6 417 29 Spotlight::get_specular_color 0 0 120
/**
 * Returns the color of specular highlights generated by the light.  This is
 * usually the same as get_color().
 */
77
virtual inline LColor const &Spotlight::get_specular_color(void) const final;

311 15 set_attenuation 0 4 417 26 Spotlight::set_attenuation 0 1 118 190
/**
 * Sets the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */
69
inline void Spotlight::set_attenuation(LVecBase3 const &attenuation);

312 15 get_attenuation 0 6 417 26 Spotlight::get_attenuation 0 0 193
/**
 * Returns the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */
77
virtual inline LVecBase3 const &Spotlight::get_attenuation(void) const final;

313 16 get_max_distance 0 4 417 27 Spotlight::get_max_distance 0 1 119 122
/**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */
59
inline PN_stdfloat Spotlight::get_max_distance(void) const;

314 16 set_max_distance 0 4 417 27 Spotlight::set_max_distance 0 1 120 161
/**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */
66
inline void Spotlight::set_max_distance(PN_stdfloat max_distance);

315 9 make_spot 0 4 417 20 Spotlight::make_spot 0 1 121 619
/**
 * Returns a newly-generated Texture that renders a circular spot image as
 * might be cast from the spotlight.  This may be projected onto target
 * geometry (for instance, via NodePath::project_texture()) instead of
 * actually enabling the light itself, as a cheesy way to make a high-
 * resolution spot appear on the geometry.
 *
 * pixel_width specifies the height and width of the new texture in pixels,
 * full_radius is a value in the range 0..1 that indicates the relative size
 * of the fully bright center spot, and fg and bg are the colors of the
 * interior and exterior of the spot, respectively.
 */
115
static PointerTo< Texture > Spotlight::make_spot(int pixel_width, PN_stdfloat full_radius, LColor &fg, LColor &bg);

316 14 get_class_type 0 4 417 25 Spotlight::get_class_type 0 1 122 0
50
static TypeHandle Spotlight::get_class_type(void);

317 10 ~Spotlight 0 516 417 21 Spotlight::~Spotlight 0 0 0
28
Spotlight::~Spotlight(void);

318 10 SwitchNode 0 260 418 22 SwitchNode::SwitchNode 0 1 123 22
/**
 *
 */

/**
 *
 */
64
inline explicit SwitchNode::SwitchNode(std::string const &name);

319 17 set_visible_child 0 4 418 29 SwitchNode::set_visible_child 0 1 124 90
/**
 * Specifies the particular child of this node, by index, that will be
 * visible.
 */
53
inline void SwitchNode::set_visible_child(int index);

320 17 get_visible_child 0 6 418 29 SwitchNode::get_visible_child 0 1 125 65
/**
 * Returns the index of the child that should be visible.
 */
54
virtual int SwitchNode::get_visible_child(void) const;

321 14 get_class_type 0 4 418 26 SwitchNode::get_class_type 0 1 126 0
51
static TypeHandle SwitchNode::get_class_type(void);

322 11 ~SwitchNode 0 516 418 23 SwitchNode::~SwitchNode 0 0 0
30
SwitchNode::~SwitchNode(void);

323 12 UvScrollNode 0 260 419 26 UvScrollNode::UvScrollNode 0 2 127 128 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
221
inline explicit UvScrollNode::UvScrollNode(std::string const &name, PN_stdfloat u_speed, PN_stdfloat v_speed, PN_stdfloat w_speed, PN_stdfloat r_speed);
inline explicit UvScrollNode::UvScrollNode(std::string const &name);

324 11 set_u_speed 0 4 419 25 UvScrollNode::set_u_speed 0 1 129 10
/**
 *
 */
59
inline void UvScrollNode::set_u_speed(PN_stdfloat u_speed);

325 11 set_v_speed 0 4 419 25 UvScrollNode::set_v_speed 0 1 130 10
/**
 *
 */
59
inline void UvScrollNode::set_v_speed(PN_stdfloat v_speed);

326 11 set_w_speed 0 4 419 25 UvScrollNode::set_w_speed 0 1 131 10
/**
 *
 */
59
inline void UvScrollNode::set_w_speed(PN_stdfloat w_speed);

327 11 set_r_speed 0 4 419 25 UvScrollNode::set_r_speed 0 1 132 10
/**
 *
 */
59
inline void UvScrollNode::set_r_speed(PN_stdfloat r_speed);

328 11 get_u_speed 0 4 419 25 UvScrollNode::get_u_speed 0 1 133 10
/**
 *
 */
57
inline PN_stdfloat UvScrollNode::get_u_speed(void) const;

329 11 get_v_speed 0 4 419 25 UvScrollNode::get_v_speed 0 1 134 10
/**
 *
 */
57
inline PN_stdfloat UvScrollNode::get_v_speed(void) const;

330 11 get_w_speed 0 4 419 25 UvScrollNode::get_w_speed 0 1 135 10
/**
 *
 */
57
inline PN_stdfloat UvScrollNode::get_w_speed(void) const;

331 11 get_r_speed 0 4 419 25 UvScrollNode::get_r_speed 0 1 136 10
/**
 *
 */
57
inline PN_stdfloat UvScrollNode::get_r_speed(void) const;

332 14 get_class_type 0 4 419 28 UvScrollNode::get_class_type 0 1 137 0
53
static TypeHandle UvScrollNode::get_class_type(void);

333 13 ~UvScrollNode 0 516 419 27 UvScrollNode::~UvScrollNode 0 0 0
34
UvScrollNode::~UvScrollNode(void);

334 18 SceneGraphAnalyzer 0 260 420 38 SceneGraphAnalyzer::SceneGraphAnalyzer 0 2 138 139 10
/**
 *
 */
130
SceneGraphAnalyzer::SceneGraphAnalyzer(void);
inline SceneGraphAnalyzer::SceneGraphAnalyzer(SceneGraphAnalyzer const &) = default;

335 19 ~SceneGraphAnalyzer 0 516 420 39 SceneGraphAnalyzer::~SceneGraphAnalyzer 0 0 10
/**
 *
 */
46
SceneGraphAnalyzer::~SceneGraphAnalyzer(void);

336 12 set_lod_mode 0 4 420 32 SceneGraphAnalyzer::set_lod_mode 0 1 140 61
/**
 * Specifies the mode in which LODNodes are analyzed.
 */
83
inline void SceneGraphAnalyzer::set_lod_mode(SceneGraphAnalyzer::LodMode lod_mode);

337 12 get_lod_mode 0 4 420 32 SceneGraphAnalyzer::get_lod_mode 0 1 141 59
/**
 * Returns the mode in which LODNodes are analyzed.
 */
112
inline SceneGraphAnalyzer::LodMode SceneGraphAnalyzer::get_lod_mode(SceneGraphAnalyzer::LodMode lod_mode) const;

338 5 clear 0 4 420 25 SceneGraphAnalyzer::clear 0 1 142 79
/**
 * Resets all of the data in the analyzer in preparation for a new run.
 */
37
void SceneGraphAnalyzer::clear(void);

339 8 add_node 0 4 420 28 SceneGraphAnalyzer::add_node 0 1 143 244
/**
 * Adds a new node to the set of data for analysis.  Normally, this would only
 * be called once, and passed the top of the scene graph, but it's possible to
 * repeatedly pass in subgraphs to get an analysis of all the graphs together.
 */
51
void SceneGraphAnalyzer::add_node(PandaNode *node);

340 5 write 0 4 420 25 SceneGraphAnalyzer::write 0 1 144 44
/**
 * Describes all the data collected.
 */
78
void SceneGraphAnalyzer::write(std::ostream &out, int indent_level = 0) const;

341 13 get_num_nodes 0 4 420 33 SceneGraphAnalyzer::get_num_nodes 0 1 145 10
/**
 *
 */
57
inline int SceneGraphAnalyzer::get_num_nodes(void) const;

342 17 get_num_instances 0 4 420 37 SceneGraphAnalyzer::get_num_instances 0 1 146 10
/**
 *
 */
61
inline int SceneGraphAnalyzer::get_num_instances(void) const;

343 18 get_num_transforms 0 4 420 38 SceneGraphAnalyzer::get_num_transforms 0 1 147 10
/**
 *
 */
62
inline int SceneGraphAnalyzer::get_num_transforms(void) const;

344 26 get_num_nodes_with_attribs 0 4 420 46 SceneGraphAnalyzer::get_num_nodes_with_attribs 0 1 148 10
/**
 *
 */
70
inline int SceneGraphAnalyzer::get_num_nodes_with_attribs(void) const;

345 17 get_num_lod_nodes 0 4 420 37 SceneGraphAnalyzer::get_num_lod_nodes 0 1 149 10
/**
 *
 */
61
inline int SceneGraphAnalyzer::get_num_lod_nodes(void) const;

346 18 get_num_geom_nodes 0 4 420 38 SceneGraphAnalyzer::get_num_geom_nodes 0 1 150 10
/**
 *
 */
62
inline int SceneGraphAnalyzer::get_num_geom_nodes(void) const;

347 13 get_num_geoms 0 4 420 33 SceneGraphAnalyzer::get_num_geoms 0 1 151 10
/**
 *
 */
57
inline int SceneGraphAnalyzer::get_num_geoms(void) const;

348 25 get_num_geom_vertex_datas 0 4 420 45 SceneGraphAnalyzer::get_num_geom_vertex_datas 0 1 152 10
/**
 *
 */
69
inline int SceneGraphAnalyzer::get_num_geom_vertex_datas(void) const;

349 27 get_num_geom_vertex_formats 0 4 420 47 SceneGraphAnalyzer::get_num_geom_vertex_formats 0 1 153 10
/**
 *
 */
71
inline int SceneGraphAnalyzer::get_num_geom_vertex_formats(void) const;

350 20 get_vertex_data_size 0 4 420 40 SceneGraphAnalyzer::get_vertex_data_size 0 1 154 10
/**
 *
 */
72
inline std::size_t SceneGraphAnalyzer::get_vertex_data_size(void) const;

351 16 get_num_vertices 0 4 420 36 SceneGraphAnalyzer::get_num_vertices 0 1 155 10
/**
 *
 */
60
inline int SceneGraphAnalyzer::get_num_vertices(void) const;

352 15 get_num_normals 0 4 420 35 SceneGraphAnalyzer::get_num_normals 0 1 156 10
/**
 *
 */
59
inline int SceneGraphAnalyzer::get_num_normals(void) const;

353 14 get_num_colors 0 4 420 34 SceneGraphAnalyzer::get_num_colors 0 1 157 10
/**
 *
 */
58
inline int SceneGraphAnalyzer::get_num_colors(void) const;

354 17 get_num_texcoords 0 4 420 37 SceneGraphAnalyzer::get_num_texcoords 0 1 158 10
/**
 *
 */
61
inline int SceneGraphAnalyzer::get_num_texcoords(void) const;

355 12 get_num_tris 0 4 420 32 SceneGraphAnalyzer::get_num_tris 0 1 159 10
/**
 *
 */
56
inline int SceneGraphAnalyzer::get_num_tris(void) const;

356 13 get_num_lines 0 4 420 33 SceneGraphAnalyzer::get_num_lines 0 1 160 10
/**
 *
 */
57
inline int SceneGraphAnalyzer::get_num_lines(void) const;

357 14 get_num_points 0 4 420 34 SceneGraphAnalyzer::get_num_points 0 1 161 10
/**
 *
 */
58
inline int SceneGraphAnalyzer::get_num_points(void) const;

358 15 get_num_patches 0 4 420 35 SceneGraphAnalyzer::get_num_patches 0 1 162 10
/**
 *
 */
59
inline int SceneGraphAnalyzer::get_num_patches(void) const;

359 23 get_num_individual_tris 0 4 420 43 SceneGraphAnalyzer::get_num_individual_tris 0 1 163 10
/**
 *
 */
67
inline int SceneGraphAnalyzer::get_num_individual_tris(void) const;

360 17 get_num_tristrips 0 4 420 37 SceneGraphAnalyzer::get_num_tristrips 0 1 164 10
/**
 *
 */
61
inline int SceneGraphAnalyzer::get_num_tristrips(void) const;

361 27 get_num_triangles_in_strips 0 4 420 47 SceneGraphAnalyzer::get_num_triangles_in_strips 0 1 165 10
/**
 *
 */
71
inline int SceneGraphAnalyzer::get_num_triangles_in_strips(void) const;

362 15 get_num_trifans 0 4 420 35 SceneGraphAnalyzer::get_num_trifans 0 1 166 10
/**
 *
 */
59
inline int SceneGraphAnalyzer::get_num_trifans(void) const;

363 25 get_num_triangles_in_fans 0 4 420 45 SceneGraphAnalyzer::get_num_triangles_in_fans 0 1 167 10
/**
 *
 */
69
inline int SceneGraphAnalyzer::get_num_triangles_in_fans(void) const;

364 27 get_num_vertices_in_patches 0 4 420 47 SceneGraphAnalyzer::get_num_vertices_in_patches 0 1 168 10
/**
 *
 */
71
inline int SceneGraphAnalyzer::get_num_vertices_in_patches(void) const;

365 17 get_texture_bytes 0 4 420 37 SceneGraphAnalyzer::get_texture_bytes 0 1 169 10
/**
 *
 */
69
inline std::size_t SceneGraphAnalyzer::get_texture_bytes(void) const;

366 20 get_num_long_normals 0 4 420 40 SceneGraphAnalyzer::get_num_long_normals 0 1 170 10
/**
 *
 */
64
inline int SceneGraphAnalyzer::get_num_long_normals(void) const;

367 21 get_num_short_normals 0 4 420 41 SceneGraphAnalyzer::get_num_short_normals 0 1 171 10
/**
 *
 */
65
inline int SceneGraphAnalyzer::get_num_short_normals(void) const;

368 23 get_total_normal_length 0 4 420 43 SceneGraphAnalyzer::get_total_normal_length 0 1 172 10
/**
 *
 */
75
inline PN_stdfloat SceneGraphAnalyzer::get_total_normal_length(void) const;

172
1 0 0 4 8 426 0 0 88 // We have to explicitly publish these because they resolve the multiple
// inheritance. 2 4 this 3 422  3 out 1 424  
2 0 0 4 9 426 0 0 10 /**
 *
 */ 3 4 this 3 422  3 out 1 424  12 indent_level 5 398  
3 0 0 7 10 428 0 0 0 0 
4 0 0 6 3 430 0 0 0 1 4 this 3 429  
5 0 0 7 6 431 0 0 0 1 4 this 3 429  
6 0 0 7 13 433 183 0 10 /**
 *
 */ 1 4 name 1 432  
7 0 0 7 14 428 0 0 0 0 
8 0 0 7 17 434 192 0 10 /**
 *
 */ 1 4 name 1 432  
9 0 0 4 18 426 0 0 1131 /**
 * Sets the CallbackObject that will be notified when this node is visited
 * during the cull traversal.  This callback will be made during the cull
 * thread.
 *
 * The cull traversal is responsible for determining which nodes are visible
 * and within the view frustum, and for accumulating state and transform, and
 * generally building up the list of CullableObjects that are to be eventually
 * passed to the draw traversal for rendering.
 *
 * At the time the cull traversal callback is made, the node has been
 * determined to be visible and it has passed the bounding-volume test, so it
 * lies within the view frustum.
 *
 * The callback is passed an instance of a NodeCullCallbackData, which
 * contains pointers to the CullTraverser and CullTraverserData--enough data
 * to examine the current node and its place within the scene graph.  The
 * callback *replaces* the normal cull behavior, so if your callback does
 * nothing, the cull traversal will not continue below this node.  If you wish
 * the cull traversal to continue to visit this node and below, you must call
 * cbdata->upcall() from your callback.
 */ 2 4 this 3 434  6 object 1 374  
10 0 0 4 19 426 0 0 78 /**
 * Removes the callback set by an earlier call to set_cull_callback().
 */ 1 4 this 3 434  
11 0 0 7 20 374 0 0 65 /**
 * Returns the CallbackObject set by set_cull_callback().
 */ 1 4 this 3 435  
12 0 0 4 24 426 0 0 973 /**
 * Sets the CallbackObject that will be notified when this node is visited
 * during the draw traversal.  This callback will be made during the draw
 * thread.
 *
 * The draw traversal is responsible for actually issuing the commands to the
 * graphics engine to draw primitives.  Its job is to walk through the list of
 * CullableObjects build up by the cull traversal, as quickly as possible,
 * issuing the appropriate commands to draw each one.
 *
 * At the time the draw traversal callback is made, the graphics state has
 * been loaded with the correct modelview transform and render state, and the
 * primitives (if any) in this node are ready to be drawn.
 *
 * The callback is passed an instance of a GeomDrawCallbackData, which
 * contains pointers to the current state and transform, as well as the
 * current GSG.  There is a Geom pointer as well, but it will always be NULL
 * to this callback, since the CallbackNode does not itself contain any Geoms.
 */ 2 4 this 3 434  6 object 1 374  
13 0 0 4 25 426 0 0 78 /**
 * Removes the callback set by an earlier call to set_draw_callback().
 */ 1 4 this 3 434  
14 0 0 7 26 374 0 0 65 /**
 * Returns the CallbackObject set by set_draw_callback().
 */ 1 4 this 3 435  
15 0 0 7 28 428 0 0 0 0 
16 0 0 7 31 437 202 0 122 /**
 * Creates a ComputeNode with the given name.  Use add_dispatch and  also
 * assign a shader using a ShaderAttrib.
 */ 1 4 name 1 432  
17 0 0 4 32 426 0 0 193 /**
 * Adds a dispatch command with the given number of work groups in the X, Y,
 * and Z dimensions.  Any of these values may be set to 1 if the respective
 * dimension should not be used.
 */ 2 4 this 3 437  10 num_groups 1 438  
18 0 0 4 32 426 0 0 193 /**
 * Adds a dispatch command with the given number of work groups in the X, Y,
 * and Z dimensions.  Any of these values may be set to 1 if the respective
 * dimension should not be used.
 */ 4 4 this 3 437  12 num_groups_x 1 398  12 num_groups_y 1 398  12 num_groups_z 1 398  
19 0 0 6 33 441 0 0 83 /**
 * Returns the number of times add_dispatch has been called on this object.
 */ 1 4 this 3 439  
20 0 0 6 34 438 0 0 84 /**
 * Returns the group counts of the nth dispatch associated with this object.
 */ 2 4 this 3 439  1 i 1 441  
21 0 0 4 35 426 0 0 81 /**
 * Sets the group counts of the nth dispatch associated with this object.
 */ 3 4 this 3 437  1 i 1 441  10 num_groups 1 438  
22 0 0 4 36 426 0 0 253 /**
 * Inserts a dispatch command with the given number of work groups in the X,
 * Y, and Z dimensions at the given position in the list of dispatch commands.
 * Any of these values may be set to 1 if the respective dimension should not
 * be used.
 */ 3 4 this 3 437  1 i 1 441  10 num_groups 1 438  
23 0 0 4 37 426 0 0 57 /**
 * Erases the given dispatch index from the list.
 */ 2 4 this 3 437  1 i 1 441  
24 0 0 4 38 426 0 0 41 /**
 * Removes all dispatch commands.
 */ 1 4 this 3 437  
25 0 0 7 43 428 0 0 0 0 
26 0 0 6 53 382 0 0 141 /**
 * Returns true if this light defines a specular color, false if the specular
 * color is derived automatically from the light color.
 */ 1 4 this 3 443  
27 0 0 6 54 382 0 0 75 /**
 * Returns whether this light is configured to cast shadows or not.
 */ 1 4 this 3 443  
28 0 0 4 55 426 0 0 297 /**
 * Sets the flag indicating whether this light should cast shadows or not.
 * This is the variant without buffer size, meaning that the current buffer
 * size will be kept (512x512 is the default). Note that enabling shadows will
 * require the shader generator to be enabled on the scene.
 */ 2 4 this 3 445  6 caster 1 382  
29 0 0 4 55 426 0 0 310 /**
 * Sets the flag indicating whether this light should cast shadows or not.
 * The xsize and ysize parameters specify the size of the shadow buffer that
 * will be set up, the sort parameter specifies the sort.  Note that enabling
 * shadows will require the shader generator to be enabled on the scene.
 */ 5 4 this 3 445  6 caster 1 382  12 buffer_xsize 1 398  12 buffer_ysize 1 398  4 sort 5 398  
30 0 0 6 56 398 0 0 85 /**
 * Returns the sort of the shadow buffer to be created for this light source.
 */ 1 4 this 3 443  
31 0 0 7 57 446 0 0 85 /**
 * Returns the size of the shadow buffer to be created for this light source.
 */ 1 4 this 3 443  
32 0 0 4 58 426 0 0 82 /**
 * Sets the size of the shadow buffer to be created for this light source.
 */ 2 4 this 3 445  4 size 1 447  
33 0 0 7 59 452 0 0 214 /**
 * Returns the buffer that has been constructed for a given GSG, or NULL if no
 * such buffer has (yet) been constructed.  This should be used for debugging
 * only, you will not need to call this normally.
 */ 2 4 this 3 445  3 gsg 1 450  
34 0 0 4 64 426 0 0 88 // We have to explicitly publish these because they resolve the multiple
// inheritance. 2 4 this 3 443  3 out 1 424  
35 0 0 4 65 426 0 0 10 /**
 *
 */ 3 4 this 3 443  3 out 1 424  12 indent_level 5 398  
36 0 0 7 66 428 0 0 0 0 
37 0 0 6 47 430 0 0 0 1 4 this 3 445  
38 0 0 7 50 453 0 0 0 1 4 this 3 445  
39 0 0 7 68 454 227 0 10 /**
 *
 */ 1 4 name 1 432  
40 0 0 4 69 426 0 0 72 /**
 * Sets the color of specular highlights generated by the light.
 */ 2 4 this 3 454  5 color 1 455  
41 0 0 4 70 426 0 0 115 /**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */ 1 4 this 3 454  
42 0 0 6 77 458 0 0 349 /**
 * Returns the point in space at which the light is located.  This is local to
 * the coordinate space in which the light is assigned.
 *
 * This actually has no bearing on the visual effect of the light, since the
 * light is rendered as if it were infinitely far away.  This is only used to
 * create a visible representation of the light.
 */ 1 4 this 3 456  
43 0 0 4 78 426 0 0 65 /**
 * Sets the point in space at which the light is located.
 */ 2 4 this 3 454  5 point 1 458  
44 0 0 6 83 459 0 0 135 /**
 * Returns the direction in which the light is aimed.  This is local to the
 * coordinate space in which the light is assigned.
 */ 1 4 this 3 456  
45 0 0 4 84 426 0 0 58 /**
 * Sets the direction in which the light is aimed.
 */ 2 4 this 3 454  9 direction 1 459  
46 0 0 7 89 428 0 0 0 0 
47 0 0 7 92 460 251 0 10 /**
 *
 */ 1 4 name 1 432  
48 0 0 7 93 460 251 0 98 /**
 * Creates a new LODNode of the type specified by the default-lod-type config
 * variable.
 */ 1 4 name 1 432  
49 0 0 4 94 426 0 0 355 /**
 * Adds a switch range to the LODNode.  This implies that the corresponding
 * child node has been parented to the node.
 *
 * The sense of in vs.  out distances is as if the object were coming towards
 * you from far away: it switches "in" at the far distance, and switches "out"
 * at the close distance.  Thus, "in" should be larger than "out".
 */ 3 4 this 3 460  2 in 1 396  3 out 1 396  
50 0 0 6 95 382 0 0 98 /**
 * Changes the switching range of a particular child of the LODNode.  See
 * add_switch().
 */ 4 4 this 3 460  5 index 1 398  2 in 1 396  3 out 1 396  
51 0 0 4 96 426 0 0 146 /**
 * Removes the set of switching ranges for the LODNode, presumably in
 * conjunction with removing all of its children.  See add_switch().
 */ 1 4 this 3 460  
52 0 0 6 97 398 0 0 184 /**
 * Returns the number of switch ranges added to the LODNode.  This should
 * correspond to the number of children of the node in order for the LODNode
 * to function correctly.
 */ 1 4 this 3 461  
53 0 0 6 98 396 0 0 136 /**
 * Returns the "in" distance of the indicated switch range.  This should be
 * larger than the "out" distance of the same range.
 */ 2 4 this 3 461  5 index 1 398  
54 0 0 6 100 396 0 0 137 /**
 * Returns the "out" distance of the indicated switch range.  This should be
 * smaller than the "in" distance of the same range.
 */ 2 4 this 3 461  5 index 1 398  
55 0 0 6 102 398 0 0 220 /**
 * Returns the index number of the child with the lowest level of detail; that
 * is, the one that is designed to be seen from the farthest away.  This is
 * usually the first child, but it is not necessarily so.
 */ 1 4 this 3 461  
56 0 0 6 103 398 0 0 228 /**
 * Returns the index number of the child with the highest level of detail;
 * that is, the one that is designed to be seen from the closest to the
 * camera.  This is usually the last child, but it is not necessarily so.
 */ 1 4 this 3 461  
57 0 0 4 104 426 0 0 149 /**
 * Forces the LODNode to show the indicated level instead of the level that
 * would normally be shown based on the distance from the camera.
 */ 2 4 this 3 460  5 index 1 398  
58 0 0 4 105 426 0 0 133 /**
 * Undoes the effect of a previous call to force_switch() and releases the
 * LODNode to once again display the normal level.
 */ 1 4 this 3 460  
59 0 0 4 106 426 0 0 113 /**
 * Sets the multiplier for lod distances.  A higher value means you'll see
 * farther switchs than normal
 */ 2 4 this 3 460  5 value 1 396  
60 0 0 6 107 396 0 0 51 /**
 * Returns the multiplier for lod distances
 */ 1 4 this 3 461  
61 0 0 4 108 426 0 0 172 /**
 * Specifies the center of the LOD.  This is the point that is compared to the
 * camera (in camera space) to determine the particular LOD that should be
 * chosen.
 */ 2 4 this 3 460  6 center 1 458  
62 0 0 6 109 458 0 0 170 /**
 * Returns the center of the LOD.  This is the point that is compared to the
 * camera (in camera space) to determine the particular LOD that should be
 * chosen.
 */ 1 4 this 3 461  
63 0 0 4 119 426 0 0 550 /**
 * This is provided as a debugging aid.  show_switch() will put the LODNode
 * into a special mode where rather than computing and drawing the appropriate
 * level of the LOD, a ring is drawn around the LODNode center indicating the
 * switch distances from the camera for the indicated level, and the geometry
 * of the indicated level is drawn in wireframe.
 *
 * Multiple different levels can be visualized this way at once.  Call
 * hide_switch() or hide_all_switches() to undo this mode and restore the
 * LODNode to its normal behavior.
 */ 2 4 this 3 460  5 index 1 398  
64 0 0 4 119 426 0 0 550 /**
 * This is provided as a debugging aid.  show_switch() will put the LODNode
 * into a special mode where rather than computing and drawing the appropriate
 * level of the LOD, a ring is drawn around the LODNode center indicating the
 * switch distances from the camera for the indicated level, and the geometry
 * of the indicated level is drawn in wireframe.
 *
 * Multiple different levels can be visualized this way at once.  Call
 * hide_switch() or hide_all_switches() to undo this mode and restore the
 * LODNode to its normal behavior.
 */ 3 4 this 3 460  5 index 1 398  5 color 1 455  
65 0 0 4 120 426 0 0 53 /**
 * Disables a previous call to show_switch().
 */ 2 4 this 3 460  5 index 1 398  
66 0 0 4 121 426 0 0 52 /**
 * Shows all levels in their default colors.
 */ 1 4 this 3 460  
67 0 0 4 122 426 0 0 71 /**
 * Hides all levels, restoring the LODNode to normal operation.
 */ 1 4 this 3 460  
68 0 0 6 123 382 0 0 159 /**
 * Returns true if any switch has been shown with show_switch(), indicating
 * the LODNode is in debug show mode; or false if it is in the normal mode.
 */ 1 4 this 3 461  
69 0 0 6 124 382 0 0 274 /**
 * Returns true if the bounding volumes for the geometry of each fhild node
 * entirely fits within the switch_in radius for that child, or false
 * otherwise.  It is almost always a mistake for the geometry of an LOD level
 * to be larger than its switch_in radius.
 */ 1 4 this 3 461  
70 0 0 7 125 428 0 0 0 0 
71 0 0 7 128 463 261 0 10 /**
 *
 */ 1 4 name 1 432  
72 0 0 4 129 426 0 0 55 /**
 * set the time taken to complete an LOD switch
 */ 2 4 this 3 463  1 t 1 396  
73 0 0 6 130 396 0 0 55 /**
 * get the time taken to complete an LOD switch
 */ 1 4 this 3 464  
74 0 0 4 132 426 0 0 124 /**
 * Specifies the cull bin and draw order that is assigned to the fading part
 * of the geometry during a transition.
 */ 3 4 this 3 463  4 name 1 432  10 draw_order 1 398  
75 0 0 6 133 432 0 0 107 /**
 * Returns the cull bin that is assigned to the fading part of the geometry
 * during a transition.
 */ 1 4 this 3 464  
76 0 0 6 134 398 0 0 135 /**
 * Returns the draw order that is assigned (along with the bin name) to the
 * fading part of the geometry during a transition.
 */ 1 4 this 3 464  
77 0 0 4 140 426 0 0 191 /**
 * Specifies the override value that is applied to the state changes necessary
 * to apply the fade effect.  This should be larger than any attrib overrides
 * on the fading geometry.
 */ 2 4 this 3 463  8 override 1 398  
78 0 0 6 141 398 0 0 189 /**
 * Returns the override value that is applied to the state changes necessary
 * to apply the fade effect.  This should be larger than any attrib overrides
 * on the fading geometry.
 */ 1 4 this 3 464  
79 0 0 7 143 428 0 0 0 0 
80 0 0 7 147 469 0 0 193 /**
 * Returns the CullTraverser in use at the time of the callback.  This object
 * contains data that does not change during the traversal, such as the
 * DisplayRegion and Camera in use.
 */ 1 4 this 3 467  
81 0 0 6 148 470 0 0 222 /**
 * Returns the CullTraverserData in use at the time of the callback.  This
 * object contains data that changes at each node of the traversal, such as
 * the current node and the current net transform to that node.
 */ 1 4 this 3 467  
82 0 0 7 149 428 0 0 0 0 
83 0 0 7 151 472 276 0 10 /**
 *
 */ 1 4 name 1 432  
84 0 0 4 152 426 0 0 72 /**
 * Sets the color of specular highlights generated by the light.
 */ 2 4 this 3 472  5 color 1 455  
85 0 0 4 153 426 0 0 115 /**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */ 1 4 this 3 472  
86 0 0 4 156 426 0 0 190 /**
 * Sets the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */ 2 4 this 3 472  11 attenuation 1 473  
87 0 0 6 162 396 0 0 122 /**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */ 1 4 this 3 474  
88 0 0 4 163 426 0 0 161 /**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */ 2 4 this 3 472  12 max_distance 1 396  
89 0 0 6 165 458 0 0 160 /**
 * Returns the point in space at which the light is located.  This is local to
 * the coordinate space in which the light is assigned, and is usually 0.
 */ 1 4 this 3 474  
90 0 0 4 166 426 0 0 77 /**
 * Sets the point in space at which the light is located.  Usually 0.
 */ 2 4 this 3 472  5 point 1 458  
91 0 0 7 168 428 0 0 0 0 
92 0 0 7 171 476 281 0 10 /**
 *
 */ 1 4 name 1 432  
93 0 0 6 172 396 0 0 122 /**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */ 1 4 this 3 477  
94 0 0 4 173 426 0 0 161 /**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */ 2 4 this 3 476  12 max_distance 1 396  
95 0 0 7 175 428 0 0 0 0 
96 0 0 7 178 479 284 0 10 /**
 *
 */ 1 4 name 1 432  
97 0 0 7 179 428 0 0 0 0 
98 0 0 7 187 480 294 0 10 /**
 *
 */ 1 4 name 1 432  
99 0 0 6 188 398 0 0 278 /**
 * Returns the number of frames in the animation.  This is a property of the
 * animation and may not be directly adjusted by the user (although it may
 * change without warning with certain kinds of animations, since this is a
 * virtual method that may be overridden).
 */ 1 4 this 3 481  
100 0 0 4 189 426 0 0 173 /**
 * Changes the advertised frame rate of the SequenceNode.  This can be used in
 * conjunction with get_play_rate() to change the effective frame rate of the
 * node.
 */ 2 4 this 3 480  10 frame_rate 1 413  
101 0 0 7 193 428 0 0 0 0 
102 0 0 7 182 479 284 0 0 1 4 this 3 480  
103 0 0 6 185 483 0 0 0 1 4 this 3 480  
104 0 0 23 197 486 0 0 189 /**
 * Create a ShaderGenerator.  This has no state, except possibly to cache
 * certain results.  The parameter that must be passed is the GSG to which the
 * shader generator belongs.
 */ 1 3 gsg 1 484  
105 0 0 15 197 486 0 0 0 1 6 param0 0 487  
106 0 0 7 198 497 0 0 794 /**
 * This is the routine that implements the next-gen fixed function pipeline by
 * synthesizing a shader.  It also takes care of setting up any buffers needed
 * to produce the requested effects.
 *
 * Currently supports:
 * - flat colors
 * - vertex colors
 * - lighting
 * - normal maps, even multiple
 * - gloss maps, but not multiple
 * - glow maps, but not multiple
 * - materials, but not updates to materials
 * - 2D textures
 * - all texture stage modes, including combine modes
 * - color scale attrib
 * - light ramps (for cartoon shading)
 * - shadow mapping
 * - most texgen modes
 * - texmatrix
 * - 1D/2D/3D textures, cube textures, 2D tex arrays
 * - linear/exp/exp2 fog
 * - animation
 *
 * Potential optimizations
 * - omit attenuation calculations if attenuation off
 *
 */ 3 4 this 3 486  2 rs 1 491  4 anim 1 494  
107 0 0 4 199 426 0 0 354 /**
 * Rehashes all the states with generated shaders, removing the ones that are
 * no longer fresh.
 *
 * Call this if certain state has changed in such a way as to require a rerun
 * of the shader generator.  This should be rare because in most cases, the
 * shader generator will automatically regenerate shaders as necessary.
 *
 * @since 1.10.0
 */ 1 4 this 3 486  
108 0 0 4 200 426 0 0 159 /**
 * Removes all previously generated shaders, requiring all shaders to be
 * regenerated.  Does not clear cache of compiled shaders.
 *
 * @since 1.10.0
 */ 1 4 this 3 486  
109 0 0 7 201 428 0 0 0 0 
110 0 0 7 203 498 304 0 10 /**
 *
 */ 1 4 name 1 432  
111 0 0 6 204 396 0 0 44 /**
 * Returns the radius of the sphere.
 */ 1 4 this 3 499  
112 0 0 4 205 426 0 0 41 /**
 * Sets the radius of the sphere.
 */ 2 4 this 3 498  6 radius 1 396  
113 0 0 7 207 428 0 0 0 0 
114 0 0 23 210 501 317 0 10 /**
 *
 */ 1 4 name 1 432  
115 0 0 4 211 426 0 0 398 /**
 * Sets the exponent that controls the amount of light falloff from the center
 * of the spotlight.  The light is attenuated by the cosine of the angle
 * between the direction of the light and the direction of the point being
 * lighted, raised to the power of this exponent.  Thus, higher exponents
 * result in a more focused light source, regardless of the field-of-view of
 * the lens.
 */ 2 4 this 3 501  8 exponent 1 396  
116 0 0 4 214 426 0 0 72 /**
 * Sets the color of specular highlights generated by the light.
 */ 2 4 this 3 501  5 color 1 455  
117 0 0 4 215 426 0 0 115 /**
 * Clears a custom specular color setting, meaning that the specular color
 * will now come from the color.
 */ 1 4 this 3 501  
118 0 0 4 218 426 0 0 190 /**
 * Sets the terms of the attenuation equation for the light.  These are, in
 * order, the constant, linear, and quadratic terms based on the distance from
 * the point to the vertex.
 */ 2 4 this 3 501  11 attenuation 1 473  
119 0 0 6 221 396 0 0 122 /**
 * Returns the maximum distance at which the light has any effect, as previously
 * specified by set_max_distance.
 */ 1 4 this 3 502  
120 0 0 4 222 426 0 0 161 /**
 * Sets the radius of the light's sphere of influence.  Beyond this distance, the
 * light may be attenuated to zero, if this is supported by the shader.
 */ 2 4 this 3 501  12 max_distance 1 396  
121 0 0 7 224 506 0 0 619 /**
 * Returns a newly-generated Texture that renders a circular spot image as
 * might be cast from the spotlight.  This may be projected onto target
 * geometry (for instance, via NodePath::project_texture()) instead of
 * actually enabling the light itself, as a cheesy way to make a high-
 * resolution spot appear on the geometry.
 *
 * pixel_width specifies the height and width of the new texture in pixels,
 * full_radius is a value in the range 0..1 that indicates the relative size
 * of the fully bright center spot, and fg and bg are the colors of the
 * interior and exterior of the spot, respectively.
 */ 4 11 pixel_width 1 398  11 full_radius 1 396  2 fg 1 505  2 bg 1 505  
122 0 0 7 225 428 0 0 0 0 
123 0 0 7 228 507 322 0 10 /**
 *
 */ 1 4 name 1 432  
124 0 0 4 229 426 0 0 90 /**
 * Specifies the particular child of this node, by index, that will be
 * visible.
 */ 2 4 this 3 507  5 index 1 398  
125 0 0 6 230 398 0 0 65 /**
 * Returns the index of the child that should be visible.
 */ 1 4 this 3 508  
126 0 0 7 232 428 0 0 0 0 
127 0 0 7 235 510 333 0 10 /**
 *
 */ 1 4 name 1 432  
128 0 0 7 235 510 333 0 10 /**
 *
 */ 5 4 name 1 432  7 u_speed 1 396  7 v_speed 1 396  7 w_speed 1 396  7 r_speed 1 396  
129 0 0 4 236 426 0 0 10 /**
 *
 */ 2 4 this 3 510  7 u_speed 1 396  
130 0 0 4 237 426 0 0 10 /**
 *
 */ 2 4 this 3 510  7 v_speed 1 396  
131 0 0 4 238 426 0 0 10 /**
 *
 */ 2 4 this 3 510  7 w_speed 1 396  
132 0 0 4 239 426 0 0 10 /**
 *
 */ 2 4 this 3 510  7 r_speed 1 396  
133 0 0 6 240 396 0 0 10 /**
 *
 */ 1 4 this 3 511  
134 0 0 6 241 396 0 0 10 /**
 *
 */ 1 4 this 3 511  
135 0 0 6 242 396 0 0 10 /**
 *
 */ 1 4 this 3 511  
136 0 0 6 243 396 0 0 10 /**
 *
 */ 1 4 this 3 511  
137 0 0 7 248 428 0 0 0 0 
138 0 0 7 251 513 335 0 10 /**
 *
 */ 0 
139 0 0 15 251 513 335 0 0 1 6 param0 0 514  
140 0 0 4 254 426 0 0 61 /**
 * Specifies the mode in which LODNodes are analyzed.
 */ 2 4 this 3 513  8 lod_mode 1 421  
141 0 0 6 255 421 0 0 59 /**
 * Returns the mode in which LODNodes are analyzed.
 */ 2 4 this 3 514  8 lod_mode 1 421  
142 0 0 4 256 426 0 0 79 /**
 * Resets all of the data in the analyzer in preparation for a new run.
 */ 1 4 this 3 513  
143 0 0 4 257 426 0 0 244 /**
 * Adds a new node to the set of data for analysis.  Normally, this would only
 * be called once, and passed the top of the scene graph, but it's possible to
 * repeatedly pass in subgraphs to get an analysis of all the graphs together.
 */ 2 4 this 3 513  4 node 1 431  
144 0 0 4 258 426 0 0 44 /**
 * Describes all the data collected.
 */ 3 4 this 3 514  3 out 1 424  12 indent_level 5 398  
145 0 0 6 259 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
146 0 0 6 260 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
147 0 0 6 261 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
148 0 0 6 262 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
149 0 0 6 263 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
150 0 0 6 264 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
151 0 0 6 265 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
152 0 0 6 266 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
153 0 0 6 267 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
154 0 0 6 268 441 0 0 10 /**
 *
 */ 1 4 this 3 514  
155 0 0 6 269 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
156 0 0 6 270 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
157 0 0 6 271 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
158 0 0 6 272 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
159 0 0 6 273 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
160 0 0 6 274 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
161 0 0 6 275 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
162 0 0 6 276 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
163 0 0 6 277 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
164 0 0 6 278 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
165 0 0 6 279 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
166 0 0 6 280 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
167 0 0 6 281 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
168 0 0 6 282 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
169 0 0 6 283 441 0 0 10 /**
 *
 */ 1 4 this 3 514  
170 0 0 6 284 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
171 0 0 6 285 398 0 0 10 /**
 *
 */ 1 4 this 3 514  
172 0 0 6 286 396 0 0 10 /**
 *
 */ 1 4 this 3 514  
147
369 9 LightNode 0 141313 9 LightNode 9 LightNode 0 0 0 0 180 0 3 177 178 179 0 0 2 3 370 173 174 3 371 175 176 0 0 161
/**
 * A derivative of Light and of PandaNode.  All kinds of Light except
 * Spotlight (which must inherit from LensNode instead) inherit from this
 * class.
 */

370 5 Light 0 2048 5 Light 5 Light 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * The abstract interface to all kinds of lights.  The actual light objects
 * also inherit from PandaNode, and can therefore be added to the scene graph
 * at some arbitrary point to define the coordinate system of effect.
 */

371 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

372 12 AmbientLight 0 141313 12 AmbientLight 12 AmbientLight 0 0 0 1 181 183 0 1 182 0 0 1 0 369 0 0 0 0 188
/**
 * A light source that seems to illuminate all points in space at once.  This
 * kind of light need not actually be part of the scene graph, since it has no
 * meaningful position.
 */

373 12 CallbackNode 0 141313 12 CallbackNode 12 CallbackNode 0 0 0 1 184 192 2 516 517 7 185 186 187 188 189 190 191 0 0 1 0 371 0 0 0 0 120
/**
 * A special node that can issue arbitrary callbacks to user code, either
 * during the cull or draw traversals.
 */

374 16 CallbackObject * 0 8576 16 CallbackObject * 16 CallbackObject * 0 0 375 0 0 0 0 0 0 0 0 0 0

375 14 CallbackObject 0 2048 14 CallbackObject 14 CallbackObject 0 0 0 0 0 0 0 0 0 0 0 0 276
/**
 * This is a generic object that can be assigned to a callback at various
 * points in the rendering process.  This is actually a base class for a
 * handful of specialized callback object types.  You can also subclass it
 * yourself to make your own callback handler.
 */

376 11 ComputeNode 0 141313 11 ComputeNode 11 ComputeNode 0 0 0 1 193 202 1 518 8 194 195 196 197 198 199 200 201 1 550 0 1 0 371 0 0 0 0 121
/**
 * A special node, the sole purpose of which is to invoke a dispatch operation
 * on the assigned compute shader.
 */

377 16 LVecBase3i const 0 8832 16 LVecBase3i const 16 LVecBase3i const 0 0 378 0 0 0 0 0 0 0 0 0 0

378 10 LVecBase3i 0 2048 10 LVecBase3i 10 LVecBase3i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

379 11 LODNodeType 0 532481 11 LODNodeType 11 LODNodeType 0 0 0 0 0 0 0 0 0 0 2 7 LNT_pop 7 LNT_pop 0
0 8 LNT_fade 8 LNT_fade 0
1 0 0

380 13 LightLensNode 0 26625 13 LightLensNode 13 LightLensNode 0 0 0 0 207 2 519 520 10 208 209 210 211 212 213 214 215 216 217 0 0 2 3 370 203 204 3 381 205 206 0 0 215
/**
 * A derivative of Light and of Camera.  The name might be misleading: it does
 * not directly derive from LensNode, but through the Camera class.  The
 * Camera serves no purpose unless shadows are enabled.
 */

381 6 Camera 0 2048 6 Camera 6 Camera 0 0 0 0 0 0 0 0 0 0 0 0 121
/**
 * A node that can be positioned around in the scene graph to represent a
 * point of view for rendering a scene.
 */

382 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

383 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

384 16 DirectionalLight 0 141313 16 DirectionalLight 16 DirectionalLight 0 0 0 1 218 227 3 521 522 523 7 219 220 222 223 224 225 226 0 0 1 0 380 0 0 0 0 96
/**
 * A light shining from infinitely far away in a particular direction, like
 * sunlight.
 */

385 12 LColor const 0 8832 12 LColor const 12 LColor const 0 0 386 0 0 0 0 0 0 0 0 0 0

386 6 LColor 0 2105344 6 LColor 6 LColor 0 0 387 0 0 0 0 0 0 0 0 0 0

387 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 388 0 0 0 0 0 0 0 0 0 0

388 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

389 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 390 0 0 0 0 0 0 0 0 0 0

390 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 391 0 0 0 0 0 0 0 0 0 0

391 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

392 14 LVector3 const 0 8832 14 LVector3 const 14 LVector3 const 0 0 393 0 0 0 0 0 0 0 0 0 0

393 8 LVector3 0 2105344 8 LVector3 8 LVector3 0 0 394 0 0 0 0 0 0 0 0 0 0

394 9 LVector3f 0 2048 9 LVector3f 9 LVector3f 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

395 7 LODNode 0 141313 7 LODNode 7 LODNode 0 0 0 1 228 251 6 524 525 526 527 528 529 22 229 230 231 232 233 234 235 236 237 238 239 240 241 242 243 244 245 246 247 248 249 250 2 551 552 0 1 0 371 0 0 0 0 190
/**
 * A Level-of-Detail node.  This selects only one of its children for
 * rendering, according to the distance from the camera and the table
 * indicated in the associated LOD object.
 */

396 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 397 0 0 0 0 0 0 0 0 0 0

397 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

398 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

399 11 FadeLODNode 0 141313 11 FadeLODNode 11 FadeLODNode 0 0 0 1 252 261 4 530 531 532 533 8 253 254 255 256 257 258 259 260 0 0 1 0 395 0 0 0 0 61
/**
 * A Level-of-Detail node with alpha based switching.
 */

400 12 string const 0 8832 17 std::string const 17 std::string const 0 0 401 0 0 0 0 0 0 0 0 0 0

401 6 string 0 2105344 11 std::string 11 std::string 0 0 402 0 0 0 0 0 0 0 0 0 0

402 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

403 20 NodeCullCallbackData 0 10241 20 NodeCullCallbackData 20 NodeCullCallbackData 0 0 0 0 0 0 3 262 263 264 0 0 1 0 404 0 0 0 0 114
/**
 * This kind of CallbackData is passed to the CallbackObject added to
 * CallbackNode:set_cull_callback().
 */

404 12 CallbackData 0 2048 12 CallbackData 12 CallbackData 0 0 0 0 0 0 0 0 0 0 0 0 348
/**
 * This is a generic data block that is passed along to a CallbackObject when
 * a callback is made.  It contains data specific to the particular callback
 * type in question.
 *
 * This is actually an abstract base class and contains no data.
 * Specializations of this class will contain the actual data relevant to each
 * callback type.
 */

405 10 PointLight 0 141313 10 PointLight 10 PointLight 0 0 0 1 265 276 4 534 535 536 537 8 266 267 269 271 272 273 274 275 0 0 1 0 380 0 0 0 0 94
/**
 * A light originating from a single point in space, and shining in all
 * directions.
 */

406 15 LVecBase3 const 0 8832 15 LVecBase3 const 15 LVecBase3 const 0 0 407 0 0 0 0 0 0 0 0 0 0

407 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 408 0 0 0 0 0 0 0 0 0 0

408 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

409 14 RectangleLight 0 141313 14 RectangleLight 14 RectangleLight 0 0 0 1 277 281 1 538 3 278 279 280 0 0 1 0 380 0 0 0 0 150
/**
 * This is a type of area light that is an axis aligned rectangle, pointing
 * along the Y axis in the positive direction.
 *
 * @since 1.10.0
 */

410 18 SelectiveChildNode 0 141313 18 SelectiveChildNode 18 SelectiveChildNode 0 0 0 1 282 284 0 1 283 0 0 1 0 371 0 0 0 0 112
/**
 * A base class for nodes like LODNode and SequenceNode that select only one
 * visible child at a time.
 */

411 12 SequenceNode 0 141313 12 SequenceNode 12 SequenceNode 0 0 0 1 289 294 1 539 3 290 291 293 0 0 2 3 410 285 286 3 412 287 288 0 0 118
/**
 * A node that automatically cycles through rendering each one of its children
 * according to its frame rate.
 */

412 13 AnimInterface 0 2048 13 AnimInterface 13 AnimInterface 0 0 0 0 0 0 0 0 0 0 0 0 226
/**
 * This is the fundamental interface for things that have a play/loop/stop
 * type interface for frame-based animation, such as animated characters.
 * This is the base class for AnimControl and other, similar classes.
 */

413 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

414 15 ShaderGenerator 0 75777 15 ShaderGenerator 15 ShaderGenerator 0 0 0 1 295 0 0 4 296 297 298 299 0 0 1 0 415 0 0 0 0 1069
/**
 * The ShaderGenerator is a device that effectively replaces the classic fixed
 * function pipeline with a 'next-gen' fixed function pipeline.  The next-gen
 * fixed function pipeline supports features like normal mapping, gloss
 * mapping, cartoon lighting, and so forth.  It works by automatically
 * generating a shader from a given RenderState.
 *
 * Currently, there is one ShaderGenerator object per GraphicsStateGuardian.
 * It is our intent that in time, people will write classes that derive from
 * ShaderGenerator but which yield slightly different results.
 *
 * The ShaderGenerator owes its existence to the 'Bamboo Team' at Carnegie
 * Mellon's Entertainment Technology Center.  This is a group of students who,
 * as a semester project, decided that next-gen graphics should be accessible
 * to everyone, even if they don't know shader programming.  The group
 * consisted of:
 *
 * Aaron Lo, Programmer Heegun Lee, Programmer Erin Fernandez, Artist/Tester
 * Joe Grubb, Artist/Tester Ivan Ortega, Technical Artist/Tester
 *
 * Thanks to them!
 *
 */

415 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

416 11 SphereLight 0 141313 11 SphereLight 11 SphereLight 0 0 0 1 300 304 1 540 3 301 302 303 0 0 1 0 405 0 0 0 0 174
/**
 * A sphere light is like a point light, except that it represents a sphere
 * with a radius, rather than being an infinitely thin point in space.
 *
 * @since 1.10.0
 */

417 9 Spotlight 0 141313 9 Spotlight 9 Spotlight 0 0 0 1 305 317 4 541 542 543 544 8 306 308 309 311 313 314 315 316 0 0 1 0 380 0 0 0 0 390
/**
 * A light originating from a single point in space, and shining in a
 * particular direction, with a cone-shaped falloff.
 *
 * The Spotlight frustum is defined using a Lens, so it can have any of the
 * properties that a camera lens can have.
 *
 * Note that the class is named Spotlight instead of SpotLight, because
 * "spotlight" is a single English word, instead of two words.
 */

418 10 SwitchNode 0 141313 10 SwitchNode 10 SwitchNode 0 0 0 1 318 322 1 545 3 319 320 321 0 0 1 0 410 0 0 0 0 95
/**
 * A node that renders only one of its children, according to the user's
 * indication.
 */

419 12 UvScrollNode 0 141313 12 UvScrollNode 12 UvScrollNode 0 0 0 1 323 333 4 546 547 548 549 9 324 325 326 327 328 329 330 331 332 0 0 1 0 371 0 0 0 0 83
/**
 * This node is placed at key points within the scene graph to animate uvs.
 */

420 18 SceneGraphAnalyzer 0 26625 18 SceneGraphAnalyzer 18 SceneGraphAnalyzer 0 0 0 1 334 335 0 33 336 337 338 339 340 341 342 343 344 345 346 347 348 349 350 351 352 353 354 355 356 357 358 359 360 361 362 363 364 365 366 367 368 0 0 0 0 1 421 103
/**
 * A handy class that can scrub over a scene graph and collect interesting
 * statistics on it.
 */

421 7 LodMode 0 794624 27 SceneGraphAnalyzer::LodMode 27 SceneGraphAnalyzer::LodMode 420 0 0 0 0 0 0 0 0 0 4 9 LM_lowest 29 SceneGraphAnalyzer::LM_lowest 0
0 10 LM_highest 30 SceneGraphAnalyzer::LM_highest 0
1 6 LM_all 26 SceneGraphAnalyzer::LM_all 0
2 7 LM_none 27 SceneGraphAnalyzer::LM_none 0
3 0 0

422 17 LightNode const * 0 8576 17 LightNode const * 17 LightNode const * 0 0 423 0 0 0 0 0 0 0 0 0 0

423 15 LightNode const 0 8832 15 LightNode const 15 LightNode const 0 0 369 0 0 0 0 0 0 0 0 0 0

424 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 425 0 0 0 0 0 0 0 0 0 0

425 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

426 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

427 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

428 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 427 0 0 0 0 0 0 0 0 0 0

429 11 LightNode * 0 8576 11 LightNode * 11 LightNode * 0 0 369 0 0 0 0 0 0 0 0 0 0

430 7 Light * 0 8576 7 Light * 7 Light * 0 0 370 0 0 0 0 0 0 0 0 0 0

431 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 371 0 0 0 0 0 0 0 0 0 0

432 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

433 14 AmbientLight * 0 8576 14 AmbientLight * 14 AmbientLight * 0 0 372 0 0 0 0 0 0 0 0 0 0

434 14 CallbackNode * 0 8576 14 CallbackNode * 14 CallbackNode * 0 0 373 0 0 0 0 0 0 0 0 0 0

435 20 CallbackNode const * 0 8576 20 CallbackNode const * 20 CallbackNode const * 0 0 436 0 0 0 0 0 0 0 0 0 0

436 18 CallbackNode const 0 8832 18 CallbackNode const 18 CallbackNode const 0 0 373 0 0 0 0 0 0 0 0 0 0

437 13 ComputeNode * 0 8576 13 ComputeNode * 13 ComputeNode * 0 0 376 0 0 0 0 0 0 0 0 0 0

438 18 LVecBase3i const * 0 8576 18 LVecBase3i const * 18 LVecBase3i const * 0 0 377 0 0 0 0 0 0 0 0 0 0

439 19 ComputeNode const * 0 8576 19 ComputeNode const * 19 ComputeNode const * 0 0 440 0 0 0 0 0 0 0 0 0 0

440 17 ComputeNode const 0 8832 17 ComputeNode const 17 ComputeNode const 0 0 376 0 0 0 0 0 0 0 0 0 0

441 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 442 0 0 0 0 0 0 0 0 0 0

442 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

443 21 LightLensNode const * 0 8576 21 LightLensNode const * 21 LightLensNode const * 0 0 444 0 0 0 0 0 0 0 0 0 0

444 19 LightLensNode const 0 8832 19 LightLensNode const 19 LightLensNode const 0 0 380 0 0 0 0 0 0 0 0 0 0

445 15 LightLensNode * 0 8576 15 LightLensNode * 15 LightLensNode * 0 0 380 0 0 0 0 0 0 0 0 0 0

446 12 LVecBase2i * 0 8576 12 LVecBase2i * 12 LVecBase2i * 0 0 383 0 0 0 0 0 0 0 0 0 0

447 18 LVecBase2i const * 0 8576 18 LVecBase2i const * 18 LVecBase2i const * 0 0 448 0 0 0 0 0 0 0 0 0 0

448 16 LVecBase2i const 0 8832 16 LVecBase2i const 16 LVecBase2i const 0 0 383 0 0 0 0 0 0 0 0 0 0

449 18 GraphicsOutputBase 0 2048 18 GraphicsOutputBase 18 GraphicsOutputBase 0 0 0 0 0 0 0 0 0 0 0 0 80
/**
 * An abstract base class for GraphicsOutput, for all the usual reasons.
 */

450 27 GraphicsStateGuardianBase * 0 8576 27 GraphicsStateGuardianBase * 27 GraphicsStateGuardianBase * 0 0 451 0 0 0 0 0 0 0 0 0 0

451 25 GraphicsStateGuardianBase 0 2048 25 GraphicsStateGuardianBase 25 GraphicsStateGuardianBase 0 0 0 0 0 0 0 0 0 0 0 0 607
/**
 * This is a base class for the GraphicsStateGuardian class, which is itself a
 * base class for the various GSG's for different platforms.  This class
 * contains all the function prototypes to support the double-dispatch of GSG
 * to geoms, transitions, etc.  It lives in a separate class in its own
 * package so we can avoid circular build dependency problems.
 *
 * GraphicsStateGuardians are not actually writable to bam files, of course,
 * but they may be passed as event parameters, so they inherit from
 * TypedWritableReferenceCount instead of TypedReferenceCount for that
 * convenience.
 */

452 20 GraphicsOutputBase * 0 8576 20 GraphicsOutputBase * 20 GraphicsOutputBase * 0 0 449 0 0 0 0 0 0 0 0 0 0

453 8 Camera * 0 8576 8 Camera * 8 Camera * 0 0 381 0 0 0 0 0 0 0 0 0 0

454 18 DirectionalLight * 0 8576 18 DirectionalLight * 18 DirectionalLight * 0 0 384 0 0 0 0 0 0 0 0 0 0

455 14 LColor const * 0 8576 14 LColor const * 14 LColor const * 0 0 385 0 0 0 0 0 0 0 0 0 0

456 24 DirectionalLight const * 0 8576 24 DirectionalLight const * 24 DirectionalLight const * 0 0 457 0 0 0 0 0 0 0 0 0 0

457 22 DirectionalLight const 0 8832 22 DirectionalLight const 22 DirectionalLight const 0 0 384 0 0 0 0 0 0 0 0 0 0

458 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 389 0 0 0 0 0 0 0 0 0 0

459 16 LVector3 const * 0 8576 16 LVector3 const * 16 LVector3 const * 0 0 392 0 0 0 0 0 0 0 0 0 0

460 9 LODNode * 0 8576 9 LODNode * 9 LODNode * 0 0 395 0 0 0 0 0 0 0 0 0 0

461 15 LODNode const * 0 8576 15 LODNode const * 15 LODNode const * 0 0 462 0 0 0 0 0 0 0 0 0 0

462 13 LODNode const 0 8832 13 LODNode const 13 LODNode const 0 0 395 0 0 0 0 0 0 0 0 0 0

463 13 FadeLODNode * 0 8576 13 FadeLODNode * 13 FadeLODNode * 0 0 399 0 0 0 0 0 0 0 0 0 0

464 19 FadeLODNode const * 0 8576 19 FadeLODNode const * 19 FadeLODNode const * 0 0 465 0 0 0 0 0 0 0 0 0 0

465 17 FadeLODNode const 0 8832 17 FadeLODNode const 17 FadeLODNode const 0 0 399 0 0 0 0 0 0 0 0 0 0

466 13 CullTraverser 0 2048 13 CullTraverser 13 CullTraverser 0 0 0 0 0 0 0 0 0 0 0 0 279
/**
 * This object performs a depth-first traversal of the scene graph, with
 * optional view-frustum culling, collecting CullState and searching for
 * GeomNodes.  Each renderable Geom encountered is passed along with its
 * associated RenderState to the CullHandler object.
 */

467 28 NodeCullCallbackData const * 0 8576 28 NodeCullCallbackData const * 28 NodeCullCallbackData const * 0 0 468 0 0 0 0 0 0 0 0 0 0

468 26 NodeCullCallbackData const 0 8832 26 NodeCullCallbackData const 26 NodeCullCallbackData const 0 0 403 0 0 0 0 0 0 0 0 0 0

469 15 CullTraverser * 0 8576 15 CullTraverser * 15 CullTraverser * 0 0 466 0 0 0 0 0 0 0 0 0 0

470 19 CullTraverserData * 0 8576 19 CullTraverserData * 19 CullTraverserData * 0 0 471 0 0 0 0 0 0 0 0 0 0

471 17 CullTraverserData 0 2048 17 CullTraverserData 17 CullTraverserData 0 0 0 0 0 0 0 0 0 0 0 0 464
/**
 * This collects together the pieces of data that are accumulated for each
 * node while walking the scene graph during the cull traversal.
 *
 * Having this as a separate object simplifies the parameter list to
 * CullTraverser::r_traverse(), as well as to other functions like
 * PandaNode::cull_callback().  It also makes it easier to add cull
 * parameters, and provides a place to abstract out some of the cull behavior
 * (like view-frustum culling).
 */

472 12 PointLight * 0 8576 12 PointLight * 12 PointLight * 0 0 405 0 0 0 0 0 0 0 0 0 0

473 17 LVecBase3 const * 0 8576 17 LVecBase3 const * 17 LVecBase3 const * 0 0 406 0 0 0 0 0 0 0 0 0 0

474 18 PointLight const * 0 8576 18 PointLight const * 18 PointLight const * 0 0 475 0 0 0 0 0 0 0 0 0 0

475 16 PointLight const 0 8832 16 PointLight const 16 PointLight const 0 0 405 0 0 0 0 0 0 0 0 0 0

476 16 RectangleLight * 0 8576 16 RectangleLight * 16 RectangleLight * 0 0 409 0 0 0 0 0 0 0 0 0 0

477 22 RectangleLight const * 0 8576 22 RectangleLight const * 22 RectangleLight const * 0 0 478 0 0 0 0 0 0 0 0 0 0

478 20 RectangleLight const 0 8832 20 RectangleLight const 20 RectangleLight const 0 0 409 0 0 0 0 0 0 0 0 0 0

479 20 SelectiveChildNode * 0 8576 20 SelectiveChildNode * 20 SelectiveChildNode * 0 0 410 0 0 0 0 0 0 0 0 0 0

480 14 SequenceNode * 0 8576 14 SequenceNode * 14 SequenceNode * 0 0 411 0 0 0 0 0 0 0 0 0 0

481 20 SequenceNode const * 0 8576 20 SequenceNode const * 20 SequenceNode const * 0 0 482 0 0 0 0 0 0 0 0 0 0

482 18 SequenceNode const 0 8832 18 SequenceNode const 18 SequenceNode const 0 0 411 0 0 0 0 0 0 0 0 0 0

483 15 AnimInterface * 0 8576 15 AnimInterface * 15 AnimInterface * 0 0 412 0 0 0 0 0 0 0 0 0 0

484 33 GraphicsStateGuardianBase const * 0 8576 33 GraphicsStateGuardianBase const * 33 GraphicsStateGuardianBase const * 0 0 485 0 0 0 0 0 0 0 0 0 0

485 31 GraphicsStateGuardianBase const 0 8832 31 GraphicsStateGuardianBase const 31 GraphicsStateGuardianBase const 0 0 451 0 0 0 0 0 0 0 0 0 0

486 17 ShaderGenerator * 0 8576 17 ShaderGenerator * 17 ShaderGenerator * 0 0 414 0 0 0 0 0 0 0 0 0 0

487 23 ShaderGenerator const * 0 8576 23 ShaderGenerator const * 23 ShaderGenerator const * 0 0 488 0 0 0 0 0 0 0 0 0 0

488 21 ShaderGenerator const 0 8832 21 ShaderGenerator const 21 ShaderGenerator const 0 0 414 0 0 0 0 0 0 0 0 0 0

489 18 ShaderAttrib const 0 8832 18 ShaderAttrib const 18 ShaderAttrib const 0 0 490 0 0 0 0 0 0 0 0 0 0

490 12 ShaderAttrib 0 2048 12 ShaderAttrib 12 ShaderAttrib 0 0 0 0 0 0 0 0 0 0 0 0 10
/**
 *
 */

491 19 RenderState const * 0 8576 19 RenderState const * 19 RenderState const * 0 0 492 0 0 0 0 0 0 0 0 0 0

492 17 RenderState const 0 8832 17 RenderState const 17 RenderState const 0 0 493 0 0 0 0 0 0 0 0 0 0

493 11 RenderState 0 2048 11 RenderState 11 RenderState 0 0 0 0 0 0 0 0 0 0 0 0 340
/**
 * This represents a unique collection of RenderAttrib objects that correspond
 * to a particular renderable state.
 *
 * You should not attempt to create or modify a RenderState object directly.
 * Instead, call one of the make() functions to create one for you.  And
 * instead of modifying a RenderState object, create a new one.
 */

494 31 GeomVertexAnimationSpec const * 0 8576 31 GeomVertexAnimationSpec const * 31 GeomVertexAnimationSpec const * 0 0 495 0 0 0 0 0 0 0 0 0 0

495 29 GeomVertexAnimationSpec const 0 8832 29 GeomVertexAnimationSpec const 29 GeomVertexAnimationSpec const 0 0 496 0 0 0 0 0 0 0 0 0 0

496 23 GeomVertexAnimationSpec 0 2048 23 GeomVertexAnimationSpec 23 GeomVertexAnimationSpec 0 0 0 0 0 0 0 0 0 0 0 0 550
/**
 * This object describes how the vertex animation, if any, represented in a
 * GeomVertexData is encoded.
 *
 * Vertex animation includes soft-skinned skeleton animation and morphs (blend
 * shapes), and might be performed on the CPU by Panda, or passed down to the
 * graphics backed to be performed on the hardware (depending on the
 * hardware's advertised capabilities).
 *
 * Changing this setting doesn't by itself change the way the animation is
 * actually performed; this just specifies how the vertices are set up to be
 * animated.
 */

497 20 ShaderAttrib const * 0 8576 20 ShaderAttrib const * 20 ShaderAttrib const * 0 0 489 0 0 0 0 0 0 0 0 0 0

498 13 SphereLight * 0 8576 13 SphereLight * 13 SphereLight * 0 0 416 0 0 0 0 0 0 0 0 0 0

499 19 SphereLight const * 0 8576 19 SphereLight const * 19 SphereLight const * 0 0 500 0 0 0 0 0 0 0 0 0 0

500 17 SphereLight const 0 8832 17 SphereLight const 17 SphereLight const 0 0 416 0 0 0 0 0 0 0 0 0 0

501 11 Spotlight * 0 8576 11 Spotlight * 11 Spotlight * 0 0 417 0 0 0 0 0 0 0 0 0 0

502 17 Spotlight const * 0 8576 17 Spotlight const * 17 Spotlight const * 0 0 503 0 0 0 0 0 0 0 0 0 0

503 15 Spotlight const 0 8832 15 Spotlight const 15 Spotlight const 0 0 417 0 0 0 0 0 0 0 0 0 0

504 7 Texture 0 2048 7 Texture 7 Texture 0 0 0 0 0 0 0 0 0 0 0 0 688
/**
 * Represents a texture object, which is typically a single 2-d image but may
 * also represent a 1-d or 3-d texture image, or the six 2-d faces of a cube
 * map texture.
 *
 * A texture's image data might be stored in system RAM (see get_ram_image())
 * or its image may be represented in texture memory on one or more
 * GraphicsStateGuardians (see prepare()), or both.  The typical usage pattern
 * is that a texture is loaded from an image file on disk, which copies its
 * image data into system RAM; then the first time the texture is rendered its
 * image data is copied to texture memory (actually, to the graphics API), and
 * the system RAM image is automatically freed.
 */

505 8 LColor * 0 8576 8 LColor * 8 LColor * 0 0 386 0 0 0 0 0 0 0 0 0 0

506 9 Texture * 0 8576 9 Texture * 9 Texture * 0 0 504 0 0 0 0 0 0 0 0 0 0

507 12 SwitchNode * 0 8576 12 SwitchNode * 12 SwitchNode * 0 0 418 0 0 0 0 0 0 0 0 0 0

508 18 SwitchNode const * 0 8576 18 SwitchNode const * 18 SwitchNode const * 0 0 509 0 0 0 0 0 0 0 0 0 0

509 16 SwitchNode const 0 8832 16 SwitchNode const 16 SwitchNode const 0 0 418 0 0 0 0 0 0 0 0 0 0

510 14 UvScrollNode * 0 8576 14 UvScrollNode * 14 UvScrollNode * 0 0 419 0 0 0 0 0 0 0 0 0 0

511 20 UvScrollNode const * 0 8576 20 UvScrollNode const * 20 UvScrollNode const * 0 0 512 0 0 0 0 0 0 0 0 0 0

512 18 UvScrollNode const 0 8832 18 UvScrollNode const 18 UvScrollNode const 0 0 419 0 0 0 0 0 0 0 0 0 0

513 20 SceneGraphAnalyzer * 0 8576 20 SceneGraphAnalyzer * 20 SceneGraphAnalyzer * 0 0 420 0 0 0 0 0 0 0 0 0 0

514 26 SceneGraphAnalyzer const * 0 8576 26 SceneGraphAnalyzer const * 26 SceneGraphAnalyzer const * 0 0 515 0 0 0 0 0 0 0 0 0 0

515 24 SceneGraphAnalyzer const 0 8832 24 SceneGraphAnalyzer const 24 SceneGraphAnalyzer const 0 0 420 0 0 0 0 0 0 0 0 0 0

0
34
516 13 cull_callback 0 6 374 187 185 0 0 0 0 0 0 27 CallbackNode::cull_callback 0

517 13 draw_callback 0 6 374 190 188 0 0 0 0 0 0 27 CallbackNode::draw_callback 0

518 10 dispatches 0 358 377 196 197 0 0 199 195 198 0 23 ComputeNode::dispatches 0

519 13 shadow_caster 0 2 382 209 0 0 0 0 0 0 0 28 LightLensNode::shadow_caster 0

520 18 shadow_buffer_size 0 6 383 212 213 0 0 0 0 0 0 33 LightLensNode::shadow_buffer_size 0

521 14 specular_color 0 6 385 221 219 0 0 0 0 0 0 32 DirectionalLight::specular_color 0

522 5 point 0 6 389 222 223 0 0 0 0 0 0 23 DirectionalLight::point 0

523 9 direction 0 6 392 224 225 0 0 0 0 0 0 27 DirectionalLight::direction 0

524 3 ins 0 66 396 234 0 0 0 0 233 0 0 12 LODNode::ins 0

525 4 outs 0 66 396 235 0 0 0 0 233 0 0 13 LODNode::outs 0

526 13 lowest_switch 0 2 398 236 0 0 0 0 0 0 0 22 LODNode::lowest_switch 0

527 14 highest_switch 0 2 398 237 0 0 0 0 0 0 0 23 LODNode::highest_switch 0

528 9 lod_scale 0 6 396 241 240 0 0 0 0 0 0 18 LODNode::lod_scale 0

529 6 center 0 6 389 243 242 0 0 0 0 0 0 15 LODNode::center 0

530 9 fade_time 0 6 396 254 253 0 0 0 0 0 0 22 FadeLODNode::fade_time 0

531 13 fade_bin_name 0 2 400 256 0 0 0 0 0 0 0 26 FadeLODNode::fade_bin_name 0

532 19 fade_bin_draw_order 0 2 398 257 0 0 0 0 0 0 0 32 FadeLODNode::fade_bin_draw_order 0

533 19 fade_state_override 0 6 398 259 258 0 0 0 0 0 0 32 FadeLODNode::fade_state_override 0

534 14 specular_color 0 6 385 268 266 0 0 0 0 0 0 26 PointLight::specular_color 0

535 11 attenuation 0 6 406 270 269 0 0 0 0 0 0 23 PointLight::attenuation 0

536 12 max_distance 0 6 396 271 272 0 0 0 0 0 0 24 PointLight::max_distance 0

537 5 point 0 6 389 273 274 0 0 0 0 0 0 17 PointLight::point 0

538 12 max_distance 0 6 396 278 279 0 0 0 0 0 0 28 RectangleLight::max_distance 0

539 10 frame_rate 0 6 413 292 291 0 0 0 0 0 0 24 SequenceNode::frame_rate 0

540 6 radius 0 6 396 301 302 0 0 0 0 0 0 19 SphereLight::radius 0

541 8 exponent 0 6 396 307 306 0 0 0 0 0 0 19 Spotlight::exponent 0

542 14 specular_color 0 6 385 310 308 0 0 0 0 0 0 25 Spotlight::specular_color 0

543 11 attenuation 0 6 406 312 311 0 0 0 0 0 0 22 Spotlight::attenuation 0

544 12 max_distance 0 6 396 313 314 0 0 0 0 0 0 23 Spotlight::max_distance 0

545 13 visible_child 0 6 398 320 319 0 0 0 0 0 0 25 SwitchNode::visible_child 0

546 7 u_speed 0 6 396 328 324 0 0 0 0 0 0 21 UvScrollNode::u_speed 0

547 7 v_speed 0 6 396 329 325 0 0 0 0 0 0 21 UvScrollNode::v_speed 0

548 7 w_speed 0 6 396 330 326 0 0 0 0 0 0 21 UvScrollNode::w_speed 0

549 7 r_speed 0 6 396 331 327 0 0 0 0 0 0 21 UvScrollNode::r_speed 0

3
550 14 get_dispatches 0 195 196 27 ComputeNode::get_dispatches 0

551 7 get_ins 0 233 234 16 LODNode::get_ins 0

552 8 get_outs 0 233 235 17 LODNode::get_outs 0

