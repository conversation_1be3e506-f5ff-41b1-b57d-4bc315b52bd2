'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_AMD_gpu_association'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_AMD_gpu_association',error_checker=_errors._error_checker)
WGL_GPU_CLOCK_AMD=_C('WGL_GPU_CLOCK_AMD',0x21A4)
WGL_GPU_FASTEST_TARGET_GPUS_AMD=_C('WGL_GPU_FASTEST_TARGET_GPUS_AMD',0x21A2)
WGL_GPU_NUM_PIPES_AMD=_C('WGL_GPU_NUM_PIPES_AMD',0x21A5)
WGL_GPU_NUM_RB_AMD=_C('WGL_GPU_NUM_RB_AMD',0x21A7)
WGL_GPU_NUM_SIMD_AMD=_C('WGL_GPU_NUM_SIMD_AMD',0x21A6)
WGL_GPU_NUM_SPI_AMD=_C('WGL_GPU_NUM_SPI_AMD',0x21A8)
WGL_GPU_OPENGL_VERSION_STRING_AMD=_C('WGL_GPU_OPENGL_VERSION_STRING_AMD',0x1F02)
WGL_GPU_RAM_AMD=_C('WGL_GPU_RAM_AMD',0x21A3)
WGL_GPU_RENDERER_STRING_AMD=_C('WGL_GPU_RENDERER_STRING_AMD',0x1F01)
WGL_GPU_VENDOR_AMD=_C('WGL_GPU_VENDOR_AMD',0x1F00)
@_f
@_p.types(_cs.VOID,_cs.HGLRC,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLint,_cs.GLbitfield,_cs.GLenum)
def wglBlitContextFramebufferAMD(dstCtx,srcX0,srcY0,srcX1,srcY1,dstX0,dstY0,dstX1,dstY1,mask,filter):pass
@_f
@_p.types(_cs.HGLRC,_cs.UINT)
def wglCreateAssociatedContextAMD(id):pass
@_f
@_p.types(_cs.HGLRC,_cs.UINT,_cs.HGLRC,ctypes.POINTER(_cs.c_int))
def wglCreateAssociatedContextAttribsAMD(id,hShareContext,attribList):pass
@_f
@_p.types(_cs.BOOL,_cs.HGLRC)
def wglDeleteAssociatedContextAMD(hglrc):pass
@_f
@_p.types(_cs.UINT,_cs.HGLRC)
def wglGetContextGPUIDAMD(hglrc):pass
@_f
@_p.types(_cs.HGLRC,)
def wglGetCurrentAssociatedContextAMD():pass
@_f
@_p.types(_cs.UINT,_cs.UINT,ctypes.POINTER(_cs.UINT))
def wglGetGPUIDsAMD(maxCount,ids):pass
@_f
@_p.types(_cs.INT,_cs.UINT,_cs.INT,_cs.GLenum,_cs.UINT,ctypes.c_void_p)
def wglGetGPUInfoAMD(id,property,dataType,size,data):pass
@_f
@_p.types(_cs.BOOL,_cs.HGLRC)
def wglMakeAssociatedContextCurrentAMD(hglrc):pass
