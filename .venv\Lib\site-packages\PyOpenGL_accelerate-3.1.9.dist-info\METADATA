Metadata-Version: 2.2
Name: PyOpenGL-accelerate
Version: 3.1.9
Summary: Cython-coded accelerators for PyOpenGL
Home-page: http://pyopengl.sourceforge.net
Download-URL: http://sourceforge.net/project/showfiles.php?group_id=5988
Author: <PERSON>
Author-email: "<PERSON>" <<EMAIL>>
License: NOTE:
        
        	THIS SOFTWARE IS NOT FAULT TOLERANT AND SHOULD NOT BE USED IN ANY
        	SITUATION ENDANGERING HUMAN LIFE OR PROPERTY.
        	
        OpenGL-ctypes PyOpenGL-accelerate License
        
        	Copyright (c) 2005-2009, <PERSON> and Contributors
        	All rights reserved.
        	
        	Redistribution and use in source and binary forms, with or without
        	modification, are permitted provided that the following conditions
        	are met:
        	
        		Redistributions of source code must retain the above copyright
        		notice, this list of conditions and the following disclaimer.
        	
        		Redistributions in binary form must reproduce the above
        		copyright notice, this list of conditions and the following
        		disclaimer in the documentation and/or other materials
        		provided with the distribution.
        	
        		The name of <PERSON>, or the name of any Contributor,
        		may not be used to endorse or promote products derived from this 
        		software without specific prior written permission.
        	
        	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
        	``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
        	LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
        	FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
        	COPYRIGHT HOLDERS AND CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
        	INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
        	(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        	SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
        	HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
        	STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
        	ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
        	OF THE POSSIBILITY OF SUCH DAMAGE. 
        
        
        
Project-URL: Homepage, https://mcfletch.github.io/pyopengl/
Project-URL: Download, https://pypi.org/project/PyOpenGL-accelerate/
Project-URL: Source, https://github.com/mcfletch/pyopengl
Project-URL: Documentation, https://mcfletch.github.io/pyopengl/documentation/index.html
Keywords: Graphics,3D,OpenGL,GLU,GLUT,GLE,GLX,EXT,ARB,Mesa,PyOpenGL
Platform: Win32
Platform: Linux
Platform: OS-X
Platform: Posix
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Multimedia :: Graphics :: 3D Rendering
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Intended Audience :: Developers
Description-Content-Type: text/markdown
License-File: license.txt
Dynamic: download-url

# Acceleration code for PyOpenGL

This set of C (Cython) extensions provides acceleration of common operations
for slow points in PyOpenGL 3.x. It is not a requirement for using PyOpenGL
but performance without it will be poor.

## Build Process

Cython is updated frequently to support newer versions of Python. As of
release 3.1.9 we no longer check in the Cython generated code for the
wrapper modules, relying on the build machines to generate the wrappers.

The Github CI Pipeline should generate and release binary builds for most
major platforms (Windows, Linux, Mac), but if you need to build from source,
a `pip install .` **should** work from the PyOpenGL repository.
