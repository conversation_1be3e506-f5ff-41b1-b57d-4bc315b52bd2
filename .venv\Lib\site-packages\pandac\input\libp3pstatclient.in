1730992849
3 3
16 libp3pstatclient 4 __OL 12 panda3d.core 
64
77 15 set_client_name 0 4 141 28 PStatClient::set_client_name 0 1 1 143
/**
 * Sets the name of the client.  This is reported to the PStatsServer, and
 * will presumably be written in the title bar or something.
 */
59
void PStatClient::set_client_name(std::string const &name);

78 15 get_client_name 0 4 141 28 PStatClient::get_client_name 0 1 2 51
/**
 * Retrieves the name of the client as set.
 */
53
std::string PStatClient::get_client_name(void) const;

79 12 set_max_rate 0 4 141 25 PStatClient::set_max_rate 0 1 3 469
/**
 * Controls the number of packets that will be sent to the server.  Normally,
 * one packet is sent per frame, but this can flood the server with more
 * packets than it can handle if the frame rate is especially good (e.g.  if
 * nothing is onscreen at the moment).  Set this parameter to a reasonable
 * number to prevent this from happening.
 *
 * This number specifies the maximum number of packets that will be sent to
 * the server per second, per thread.
 */
44
void PStatClient::set_max_rate(double rate);

80 12 get_max_rate 0 4 141 25 PStatClient::get_max_rate 0 1 4 128
/**
 * Returns the maximum number of packets that will be sent to the server per
 * second, per thread.  See set_max_rate().
 */
45
double PStatClient::get_max_rate(void) const;

81 18 get_num_collectors 0 4 141 31 PStatClient::get_num_collectors 0 1 5 73
/**
 * Returns the total number of collectors the Client knows about.
 */
55
inline int PStatClient::get_num_collectors(void) const;

82 13 get_collector 0 4 141 26 PStatClient::get_collector 0 1 6 37
/**
 * Returns the nth collector.
 */
59
PStatCollector PStatClient::get_collector(int index) const;

83 17 get_collector_def 0 4 141 30 PStatClient::get_collector_def 0 1 7 60
/**
 * Returns the definition body of the nth collector.
 */
74
inline PStatCollectorDef *PStatClient::get_collector_def(int index) const;

84 18 get_collector_name 0 4 141 31 PStatClient::get_collector_name 0 1 8 55
/**
 * Returns the name of the indicated collector.
 */
61
std::string PStatClient::get_collector_name(int index) const;

85 22 get_collector_fullname 0 4 141 35 PStatClient::get_collector_fullname 0 1 9 186
/**
 * Returns the "full name" of the indicated collector.  This will be the
 * concatenation of all of the collector's parents' names (except Frame) and
 * the collector's own name.
 */
65
std::string PStatClient::get_collector_fullname(int index) const;

86 15 get_num_threads 0 4 141 28 PStatClient::get_num_threads 0 1 10 70
/**
 * Returns the total number of threads the Client knows about.
 */
52
inline int PStatClient::get_num_threads(void) const;

87 10 get_thread 0 4 141 23 PStatClient::get_thread 0 1 11 34
/**
 * Returns the nth thread.
 */
53
PStatThread PStatClient::get_thread(int index) const;

88 15 get_thread_name 0 4 141 28 PStatClient::get_thread_name 0 1 12 52
/**
 * Returns the name of the indicated thread.
 */
65
inline std::string PStatClient::get_thread_name(int index) const;

89 20 get_thread_sync_name 0 4 141 33 PStatClient::get_thread_sync_name 0 1 13 57
/**
 * Returns the sync_name of the indicated thread.
 */
70
inline std::string PStatClient::get_thread_sync_name(int index) const;

90 17 get_thread_object 0 4 141 30 PStatClient::get_thread_object 0 1 14 85
/**
 * Returns the Panda Thread object associated with the indicated PStatThread.
 */
75
inline PointerTo< Thread > PStatClient::get_thread_object(int index) const;

91 15 get_main_thread 0 4 141 28 PStatClient::get_main_thread 0 1 15 109
/**
 * Returns a handle to the client's Main thread.  This is the thread that
 * started the application.
 */
53
PStatThread PStatClient::get_main_thread(void) const;

92 18 get_current_thread 0 4 141 31 PStatClient::get_current_thread 0 1 16 159
/**
 * Returns a handle to the currently-executing thread.  This is the thread
 * that PStatCollectors will be counted in if they do not specify otherwise.
 */
56
PStatThread PStatClient::get_current_thread(void) const;

93 13 get_real_time 0 4 141 26 PStatClient::get_real_time 0 1 17 248
/**
 * Returns the time according to to the PStatClient's clock object.  It keeps
 * its own clock, instead of using the global clock object, so the stats won't
 * get mucked up if you put the global clock in non-real-time mode or
 * something.
 */
46
double PStatClient::get_real_time(void) const;

94 14 PStatCollector 0 260 147 30 PStatCollector::PStatCollector 0 3 31 32 33 1708
/**
 * Normally, this constructor is called only from PStatClient.  Use one of the
 * constructors below to create your own Collector.
 */

/**
 * Creates a new PStatCollector, ready to start accumulating data.  The name
 * of the collector uniquely identifies it among the other collectors; if two
 * collectors share the same name then they are really the same collector.
 *
 * The name may also be a compound name, something like "Cull:Sort", which
 * indicates that this is a collector named "Sort", a child of the collector
 * named "Cull". The parent may also be named explicitly by reference in the
 * other flavor of the constructor; see further comments on this for that
 * constructor.
 *
 * If the client pointer is non-null, it specifies a particular client to
 * register the collector with; otherwise, the global client is used.
 */

/**
 * Creates a new PStatCollector, ready to start accumulating data.  The name
 * of the collector uniquely identifies it among the other collectors; if two
 * collectors share the same name then they are really the same collector.
 *
 * The parent is the collector that conceptually includes all of the time
 * measured for this collector.  For instance, a particular character's
 * animation time is owned by the "Animation" collector, which is in turn
 * owned by the "Frame" collector.  It is not strictly necessary that all of
 * the time spent in a particular collector is completely nested within time
 * spent in its parent's collector.  If parent is the empty string, the
 * collector is owned by "Frame".
 *
 * This constructor does not take a client pointer; it always creates the new
 * collector on the same client as its parent.
 */

/**
 *
 */
273
inline explicit PStatCollector::PStatCollector(std::string const &name, PStatClient *client = nullptr);
inline explicit PStatCollector::PStatCollector(PStatCollector const &parent, std::string const &name);
inline PStatCollector::PStatCollector(PStatCollector const &copy);

95 10 operator = 0 4 147 26 PStatCollector::operator = 0 1 34 0
67
inline void PStatCollector::operator =(PStatCollector const &copy);

96 8 is_valid 0 4 147 24 PStatCollector::is_valid 0 1 35 178
/**
 * Returns true if collector is valid and may be used, or false if it was
 * constructed with the default constructor (in which case any attempt to use
 * it will crash).
 */
49
inline bool PStatCollector::is_valid(void) const;

97 8 get_name 0 4 147 24 PStatCollector::get_name 0 1 36 127
/**
 * Returns the local name of this collector.  This is the rightmost part of
 * the fullname, after the rightmost colon.
 */
56
inline std::string PStatCollector::get_name(void) const;

98 12 get_fullname 0 4 147 28 PStatCollector::get_fullname 0 1 37 146
/**
 * Returns the full name of this collector.  This includes the names of all
 * the collector's parents, concatenated together with colons.
 */
60
inline std::string PStatCollector::get_fullname(void) const;

99 6 output 0 4 147 22 PStatCollector::output 0 1 38 10
/**
 *
 */
60
inline void PStatCollector::output(std::ostream &out) const;

100 9 is_active 0 4 147 25 PStatCollector::is_active 0 2 39 40 274
/**
 * Returns true if this particular collector is active on the default thread,
 * and we are currently transmitting PStats data.
 */

/**
 * Returns true if this particular collector is active on the indicated
 * thread, and we are currently transmitting PStats data.
 */
110
inline bool PStatCollector::is_active(void);
inline bool PStatCollector::is_active(PStatThread const &thread);

101 10 is_started 0 4 147 26 PStatCollector::is_started 0 2 41 42 234
/**
 * Returns true if this particular collector has been started on the default
 * thread, or false otherwise.
 */

/**
 * Returns true if this particular collector has been started on the indicated
 * thread, or false otherwise.
 */
112
inline bool PStatCollector::is_started(void);
inline bool PStatCollector::is_started(PStatThread const &thread);

102 5 start 0 4 147 21 PStatCollector::start 0 3 43 44 45 458
/**
 * Starts this particular timer ticking.  This should be called before the
 * code you want to measure.
 */

/**
 * Starts this timer ticking within a particular thread.
 */

/**
 * Marks that the timer should have been started as of the indicated time.
 * This must be a time based on the PStatClient's clock (see
 * PStatClient::get_clock()), and care should be taken that all such calls
 * exhibit a monotonically increasing series of time values.
 */
178
inline void PStatCollector::start(void);
inline void PStatCollector::start(PStatThread const &thread);
inline void PStatCollector::start(PStatThread const &thread, double as_of);

103 4 stop 0 4 147 20 PStatCollector::stop 0 3 46 47 48 428
/**
 * Stops this timer.  This should be called after the code you want to
 * measure.
 */

/**
 * Stops this timer within a particular thread.
 */

/**
 * Marks that the timer should have been stopped as of the indicated time.
 * This must be a time based on the PStatClient's clock (see
 * PStatClient::get_clock()), and care should be taken that all such calls
 * exhibit a monotonically increasing series of time values.
 */
175
inline void PStatCollector::stop(void);
inline void PStatCollector::stop(PStatThread const &thread);
inline void PStatCollector::stop(PStatThread const &thread, double as_of);

104 11 clear_level 0 4 147 27 PStatCollector::clear_level 0 2 49 50 385
/**
 * Removes the level setting associated with this collector for the main
 * thread.  The collector will no longer show up on any level graphs in the
 * main thread.  This implicitly calls flush_level().
 */

/**
 * Removes the level setting associated with this collector for the indicated
 * thread.  The collector will no longer show up on any level graphs in this
 * thread.
 */
114
inline void PStatCollector::clear_level(void);
inline void PStatCollector::clear_level(PStatThread const &thread);

105 9 set_level 0 4 147 25 PStatCollector::set_level 0 2 51 52 267
/**
 * Sets the level setting associated with this collector for the main thread
 * to the indicated value.  This implicitly calls flush_level().
 */

/**
 * Sets the level setting associated with this collector for the indicated
 * thread to the indicated value.
 */
132
inline void PStatCollector::set_level(double level);
inline void PStatCollector::set_level(PStatThread const &thread, double level);

106 9 add_level 0 4 147 25 PStatCollector::add_level 0 2 53 54 630
/**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the main thread.  If the collector did
 * not already have a level setting for the main thread, it is initialized to
 * 0.
 *
 * As an optimization, the data is not immediately set to the PStatClient.  It
 * will be sent the next time flush_level() is called.
 */

/**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the indicated thread.  If the collector
 * did not already have a level setting for this thread, it is initialized to
 * 0.
 */
140
inline void PStatCollector::add_level(double increment);
inline void PStatCollector::add_level(PStatThread const &thread, double increment);

107 9 sub_level 0 4 147 25 PStatCollector::sub_level 0 2 55 56 640
/**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the main thread.  If the
 * collector did not already have a level setting for the main thread, it is
 * initialized to 0.
 *
 * As an optimization, the data is not immediately set to the PStatClient.  It
 * will be sent the next time flush_level() is called.
 */

/**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the indicated thread.  If the
 * collector did not already have a level setting for this thread, it is
 * initialized to 0.
 */
140
inline void PStatCollector::sub_level(double decrement);
inline void PStatCollector::sub_level(PStatThread const &thread, double decrement);

108 13 add_level_now 0 4 147 29 PStatCollector::add_level_now 0 1 57 65
/**
 * Calls add_level() and immediately calls flush_level().
 */
60
inline void PStatCollector::add_level_now(double increment);

109 13 sub_level_now 0 4 147 29 PStatCollector::sub_level_now 0 1 58 65
/**
 * Calls sub_level() and immediately calls flush_level().
 */
60
inline void PStatCollector::sub_level_now(double decrement);

110 11 flush_level 0 4 147 27 PStatCollector::flush_level 0 1 59 95
/**
 * Updates the PStatClient with the recent results from add_level() and
 * sub_level().
 */
46
inline void PStatCollector::flush_level(void);

111 9 get_level 0 4 147 25 PStatCollector::get_level 0 2 60 61 193
/**
 * Returns the current level value of the given collector in the main thread.
 * This implicitly calls flush_level().
 */

/**
 * Returns the current level value of the given collector.
 */
114
inline double PStatCollector::get_level(void);
inline double PStatCollector::get_level(PStatThread const &thread);

112 18 clear_thread_level 0 4 147 34 PStatCollector::clear_thread_level 0 1 62 178
/**
 * Removes the level setting associated with this collector for the current
 * thread.  The collector will no longer show up on any level graphs in the
 * current thread.
 */
53
inline void PStatCollector::clear_thread_level(void);

113 16 set_thread_level 0 4 147 32 PStatCollector::set_thread_level 0 1 63 114
/**
 * Sets the level setting associated with this collector for the current
 * thread to the indicated value.
 */
59
inline void PStatCollector::set_thread_level(double level);

114 16 add_thread_level 0 4 147 32 PStatCollector::add_thread_level 0 1 64 251
/**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the current thread.  If the collector
 * did not already have a level setting for the current thread, it is
 * initialized to 0.
 */
63
inline void PStatCollector::add_thread_level(double increment);

115 16 sub_thread_level 0 4 147 32 PStatCollector::sub_thread_level 0 1 65 256
/**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the current thread.  If the
 * collector did not already have a level setting for the current thread, it
 * is initialized to 0.
 */
63
inline void PStatCollector::sub_thread_level(double decrement);

116 16 get_thread_level 0 4 147 32 PStatCollector::get_thread_level 0 1 66 91
/**
 * Returns the current level value of the given collector in the current
 * thread.
 */
53
inline double PStatCollector::get_thread_level(void);

117 9 get_index 0 4 147 25 PStatCollector::get_index 0 1 67 91
/**
 * Returns the index number of this particular collector within the
 * PStatClient.
 */
49
inline int PStatCollector::get_index(void) const;

118 15 ~PStatCollector 0 516 147 31 PStatCollector::~PStatCollector 0 0 0
38
PStatCollector::~PStatCollector(void);

119 11 PStatThread 0 260 148 24 PStatThread::PStatThread 0 3 68 69 70 446
/**
 * Normally, this constructor is called only from PStatClient.  Use one of the
 * constructors below to create your own Thread.
 */

/**
 * Normally, this constructor is called only from PStatClient.  Use one of the
 * constructors below to create your own Thread.
 */

/**
 * Creates a new named thread.  This will be used to unify tasks that share a
 * common thread, and differentiate tasks that occur in different threads.
 */

/**
 *
 */
202
inline PStatThread::PStatThread(PStatClient *client, int index);
inline PStatThread::PStatThread(Thread *thread, PStatClient *client = nullptr);
inline PStatThread::PStatThread(PStatThread const &copy);

120 10 operator = 0 4 148 23 PStatThread::operator = 0 1 71 0
61
inline void PStatThread::operator =(PStatThread const &copy);

121 9 new_frame 0 4 148 22 PStatThread::new_frame 0 1 72 327
/**
 * This must be called at the start of every "frame", whatever a frame may be
 * deemed to be, to accumulate all the stats that have collected so far for
 * the thread and ship them off to the server.
 *
 * Calling PStatClient::thread_tick() will automatically call this for any
 * threads with the indicated sync name.
 */
34
void PStatThread::new_frame(void);

122 9 add_frame 0 4 148 22 PStatThread::add_frame 0 1 73 118
/**
 * This is a slightly lower-level version of new_frame that also specifies the
 * data to send for this frame.
 */
62
void PStatThread::add_frame(PStatFrameData const &frame_data);

123 10 get_thread 0 4 148 23 PStatThread::get_thread 0 1 74 90
/**
 * Returns the Panda Thread object associated with this particular
 * PStatThread.
 */
44
Thread *PStatThread::get_thread(void) const;

124 9 get_index 0 4 148 22 PStatThread::get_index 0 1 75 85
/**
 * Returns the index number of this particular thread within the PStatClient.
 */
46
inline int PStatThread::get_index(void) const;

125 12 ~PStatThread 0 516 148 25 PStatThread::~PStatThread 0 0 0
32
PStatThread::~PStatThread(void);

126 7 connect 0 4 141 20 PStatClient::connect 0 1 18 125
/**
 * Attempts to establish a connection to the indicated PStatServer.  Returns
 * true if successful, false on failure.
 */
95
static inline bool PStatClient::connect(std::string const &hostname = string(), int port = -1);

127 10 disconnect 0 4 141 23 PStatClient::disconnect 0 1 19 56
/**
 * Closes the connection previously established.
 */
49
static inline void PStatClient::disconnect(void);

128 12 is_connected 0 4 141 25 PStatClient::is_connected 0 1 20 108
/**
 * Returns true if the client believes it is connected to a working
 * PStatServer, false otherwise.
 */
51
static inline bool PStatClient::is_connected(void);

129 18 resume_after_pause 0 4 141 31 PStatClient::resume_after_pause 0 1 21 209
/**
 * Resumes the PStatClient after the simulation has been paused for a while.
 * This allows the stats to continue exactly where it left off, instead of
 * leaving a big gap that would represent a chug.
 */
57
static inline void PStatClient::resume_after_pause(void);

130 9 main_tick 0 4 141 22 PStatClient::main_tick 0 1 22 147
/**
 * A convenience function to call new_frame() on the global PStatClient's main
 * thread, and any other threads with a sync_name of "Main".
 */
41
static void PStatClient::main_tick(void);

131 11 thread_tick 0 4 141 24 PStatClient::thread_tick 0 1 23 100
/**
 * A convenience function to call new_frame() on any threads with the
 * indicated sync_name
 */
67
static void PStatClient::thread_tick(std::string const &sync_name);

132 16 client_main_tick 0 4 141 29 PStatClient::client_main_tick 0 1 24 146
/**
 * A convenience function to call new_frame() on the given PStatClient's main
 * thread, and any other threads with a sync_name of "Main".
 */
41
void PStatClient::client_main_tick(void);

133 18 client_thread_tick 0 4 141 31 PStatClient::client_thread_tick 0 1 25 108
/**
 * A convenience function to call new_frame() on all of the threads with the
 * indicated sync name.
 */
67
void PStatClient::client_thread_tick(std::string const &sync_name);

134 14 client_connect 0 4 141 27 PStatClient::client_connect 0 1 26 53
/**
 * The nonstatic implementation of connect().
 */
65
bool PStatClient::client_connect(std::string hostname, int port);

135 17 client_disconnect 0 4 141 30 PStatClient::client_disconnect 0 1 27 56
/**
 * The nonstatic implementation of disconnect().
 */
42
void PStatClient::client_disconnect(void);

136 19 client_is_connected 0 4 141 32 PStatClient::client_is_connected 0 1 28 58
/**
 * The nonstatic implementation of is_connected().
 */
50
bool PStatClient::client_is_connected(void) const;

137 25 client_resume_after_pause 0 4 141 38 PStatClient::client_resume_after_pause 0 1 29 209
/**
 * Resumes the PStatClient after the simulation has been paused for a while.
 * This allows the stats to continue exactly where it left off, instead of
 * leaving a big gap that would represent a chug.
 */
50
void PStatClient::client_resume_after_pause(void);

138 17 get_global_pstats 0 4 141 30 PStatClient::get_global_pstats 0 1 30 225
/**
 * Returns a pointer to the global PStatClient object.  It's legal to declare
 * your own PStatClient locally, but it's also convenient to have a global one
 * that everyone can register with.  This is the global one.
 */
57
static PStatClient *PStatClient::get_global_pstats(void);

139 21 PStatCollectorForward 0 260 151 44 PStatCollectorForward::PStatCollectorForward 0 1 76 10
/**
 *
 */
79
inline PStatCollectorForward::PStatCollectorForward(PStatCollector const &col);

140 22 ~PStatCollectorForward 0 516 151 45 PStatCollectorForward::~PStatCollectorForward 0 0 0
52
PStatCollectorForward::~PStatCollectorForward(void);

76
1 0 0 4 4 155 0 0 143 /**
 * Sets the name of the client.  This is reported to the PStatsServer, and
 * will presumably be written in the title bar or something.
 */ 2 4 this 3 153  4 name 1 154  
2 0 0 6 5 154 0 0 51 /**
 * Retrieves the name of the client as set.
 */ 1 4 this 3 156  
3 0 0 4 6 155 0 0 469 /**
 * Controls the number of packets that will be sent to the server.  Normally,
 * one packet is sent per frame, but this can flood the server with more
 * packets than it can handle if the frame rate is especially good (e.g.  if
 * nothing is onscreen at the moment).  Set this parameter to a reasonable
 * number to prevent this from happening.
 *
 * This number specifies the maximum number of packets that will be sent to
 * the server per second, per thread.
 */ 2 4 this 3 153  4 rate 1 146  
4 0 0 6 7 146 0 0 128 /**
 * Returns the maximum number of packets that will be sent to the server per
 * second, per thread.  See set_max_rate().
 */ 1 4 this 3 156  
5 0 0 6 8 150 0 0 73 /**
 * Returns the total number of collectors the Client knows about.
 */ 1 4 this 3 156  
6 0 0 7 9 158 118 0 37 /**
 * Returns the nth collector.
 */ 2 4 this 3 156  5 index 1 150  
7 0 0 6 11 159 0 0 60 /**
 * Returns the definition body of the nth collector.
 */ 2 4 this 3 156  5 index 1 150  
8 0 0 6 12 154 0 0 55 /**
 * Returns the name of the indicated collector.
 */ 2 4 this 3 156  5 index 1 150  
9 0 0 6 13 154 0 0 186 /**
 * Returns the "full name" of the indicated collector.  This will be the
 * concatenation of all of the collector's parents' names (except Frame) and
 * the collector's own name.
 */ 2 4 this 3 156  5 index 1 150  
10 0 0 6 14 150 0 0 70 /**
 * Returns the total number of threads the Client knows about.
 */ 1 4 this 3 156  
11 0 0 7 15 161 125 0 34 /**
 * Returns the nth thread.
 */ 2 4 this 3 156  5 index 1 150  
12 0 0 6 17 154 0 0 52 /**
 * Returns the name of the indicated thread.
 */ 2 4 this 3 156  5 index 1 150  
13 0 0 6 18 154 0 0 57 /**
 * Returns the sync_name of the indicated thread.
 */ 2 4 this 3 156  5 index 1 150  
14 0 0 7 19 149 0 0 85 /**
 * Returns the Panda Thread object associated with the indicated PStatThread.
 */ 2 4 this 3 156  5 index 1 150  
15 0 0 7 20 161 125 0 109 /**
 * Returns a handle to the client's Main thread.  This is the thread that
 * started the application.
 */ 1 4 this 3 156  
16 0 0 7 21 161 125 0 159 /**
 * Returns a handle to the currently-executing thread.  This is the thread
 * that PStatCollectors will be counted in if they do not specify otherwise.
 */ 1 4 this 3 156  
17 0 0 6 22 146 0 0 248 /**
 * Returns the time according to to the PStatClient's clock object.  It keeps
 * its own clock, instead of using the global clock object, so the stats won't
 * get mucked up if you put the global clock in non-real-time mode or
 * something.
 */ 1 4 this 3 156  
18 0 0 38 71 162 0 0 125 /**
 * Attempts to establish a connection to the indicated PStatServer.  Returns
 * true if successful, false on failure.
 */ 2 8 hostname 5 154  4 port 5 150  
19 0 0 36 72 155 0 0 56 /**
 * Closes the connection previously established.
 */ 0 
20 0 0 6 73 162 0 0 108 /**
 * Returns true if the client believes it is connected to a working
 * PStatServer, false otherwise.
 */ 0 
21 0 0 4 74 155 0 0 209 /**
 * Resumes the PStatClient after the simulation has been paused for a while.
 * This allows the stats to continue exactly where it left off, instead of
 * leaving a big gap that would represent a chug.
 */ 0 
22 0 0 4 75 155 0 0 147 /**
 * A convenience function to call new_frame() on the global PStatClient's main
 * thread, and any other threads with a sync_name of "Main".
 */ 0 
23 0 0 4 76 155 0 0 100 /**
 * A convenience function to call new_frame() on any threads with the
 * indicated sync_name
 */ 1 9 sync_name 1 154  
24 0 0 4 77 155 0 0 146 /**
 * A convenience function to call new_frame() on the given PStatClient's main
 * thread, and any other threads with a sync_name of "Main".
 */ 1 4 this 3 153  
25 0 0 4 78 155 0 0 108 /**
 * A convenience function to call new_frame() on all of the threads with the
 * indicated sync name.
 */ 2 4 this 3 153  9 sync_name 1 154  
26 0 0 38 79 162 0 0 53 /**
 * The nonstatic implementation of connect().
 */ 3 4 this 3 153  8 hostname 1 154  4 port 1 150  
27 0 0 36 80 155 0 0 56 /**
 * The nonstatic implementation of disconnect().
 */ 1 4 this 3 153  
28 0 0 6 81 162 0 0 58 /**
 * The nonstatic implementation of is_connected().
 */ 1 4 this 3 156  
29 0 0 4 82 155 0 0 209 /**
 * Resumes the PStatClient after the simulation has been paused for a while.
 * This allows the stats to continue exactly where it left off, instead of
 * leaving a big gap that would represent a chug.
 */ 1 4 this 3 153  
30 0 0 6 83 153 0 0 225 /**
 * Returns a pointer to the global PStatClient object.  It's legal to declare
 * your own PStatClient locally, but it's also convenient to have a global one
 * that everyone can register with.  This is the global one.
 */ 0 
31 0 0 15 30 158 118 0 10 /**
 *
 */ 1 4 copy 1 163  
32 0 0 7 30 158 118 0 848 /**
 * Creates a new PStatCollector, ready to start accumulating data.  The name
 * of the collector uniquely identifies it among the other collectors; if two
 * collectors share the same name then they are really the same collector.
 *
 * The parent is the collector that conceptually includes all of the time
 * measured for this collector.  For instance, a particular character's
 * animation time is owned by the "Animation" collector, which is in turn
 * owned by the "Frame" collector.  It is not strictly necessary that all of
 * the time spent in a particular collector is completely nested within time
 * spent in its parent's collector.  If parent is the empty string, the
 * collector is owned by "Frame".
 *
 * This constructor does not take a client pointer; it always creates the new
 * collector on the same client as its parent.
 */ 2 6 parent 1 163  4 name 1 154  
33 0 0 7 30 158 118 0 706 /**
 * Creates a new PStatCollector, ready to start accumulating data.  The name
 * of the collector uniquely identifies it among the other collectors; if two
 * collectors share the same name then they are really the same collector.
 *
 * The name may also be a compound name, something like "Cull:Sort", which
 * indicates that this is a collector named "Sort", a child of the collector
 * named "Cull". The parent may also be named explicitly by reference in the
 * other flavor of the constructor; see further comments on this for that
 * constructor.
 *
 * If the client pointer is non-null, it specifies a particular client to
 * register the collector with; otherwise, the global client is used.
 */ 2 4 name 1 154  6 client 5 153  
34 0 0 6 31 158 0 0 0 2 4 this 3 158  4 copy 1 163  
35 0 0 6 32 162 0 0 178 /**
 * Returns true if collector is valid and may be used, or false if it was
 * constructed with the default constructor (in which case any attempt to use
 * it will crash).
 */ 1 4 this 3 163  
36 0 0 6 33 154 0 0 127 /**
 * Returns the local name of this collector.  This is the rightmost part of
 * the fullname, after the rightmost colon.
 */ 1 4 this 3 163  
37 0 0 6 34 154 0 0 146 /**
 * Returns the full name of this collector.  This includes the names of all
 * the collector's parents, concatenated together with colons.
 */ 1 4 this 3 163  
38 0 0 4 35 155 0 0 10 /**
 *
 */ 2 4 this 3 163  3 out 1 165  
39 0 0 6 36 162 0 0 135 /**
 * Returns true if this particular collector is active on the default thread,
 * and we are currently transmitting PStats data.
 */ 1 4 this 3 158  
40 0 0 6 36 162 0 0 137 /**
 * Returns true if this particular collector is active on the indicated
 * thread, and we are currently transmitting PStats data.
 */ 2 4 this 3 158  6 thread 1 167  
41 0 0 6 37 162 0 0 115 /**
 * Returns true if this particular collector has been started on the default
 * thread, or false otherwise.
 */ 1 4 this 3 158  
42 0 0 6 37 162 0 0 117 /**
 * Returns true if this particular collector has been started on the indicated
 * thread, or false otherwise.
 */ 2 4 this 3 158  6 thread 1 167  
43 0 0 4 38 155 0 0 111 /**
 * Starts this particular timer ticking.  This should be called before the
 * code you want to measure.
 */ 1 4 this 3 158  
44 0 0 4 38 155 0 0 64 /**
 * Starts this timer ticking within a particular thread.
 */ 2 4 this 3 158  6 thread 1 167  
45 0 0 4 38 155 0 0 279 /**
 * Marks that the timer should have been started as of the indicated time.
 * This must be a time based on the PStatClient's clock (see
 * PStatClient::get_clock()), and care should be taken that all such calls
 * exhibit a monotonically increasing series of time values.
 */ 3 4 this 3 158  6 thread 1 167  5 as_of 1 146  
46 0 0 4 39 155 0 0 90 /**
 * Stops this timer.  This should be called after the code you want to
 * measure.
 */ 1 4 this 3 158  
47 0 0 4 39 155 0 0 55 /**
 * Stops this timer within a particular thread.
 */ 2 4 this 3 158  6 thread 1 167  
48 0 0 4 39 155 0 0 279 /**
 * Marks that the timer should have been stopped as of the indicated time.
 * This must be a time based on the PStatClient's clock (see
 * PStatClient::get_clock()), and care should be taken that all such calls
 * exhibit a monotonically increasing series of time values.
 */ 3 4 this 3 158  6 thread 1 167  5 as_of 1 146  
49 0 0 4 40 155 0 0 210 /**
 * Removes the level setting associated with this collector for the main
 * thread.  The collector will no longer show up on any level graphs in the
 * main thread.  This implicitly calls flush_level().
 */ 1 4 this 3 158  
50 0 0 4 40 155 0 0 173 /**
 * Removes the level setting associated with this collector for the indicated
 * thread.  The collector will no longer show up on any level graphs in this
 * thread.
 */ 2 4 this 3 158  6 thread 1 167  
51 0 0 4 41 155 0 0 116 /**
 * Sets the level setting associated with this collector for the indicated
 * thread to the indicated value.
 */ 3 4 this 3 158  6 thread 1 167  5 level 1 146  
52 0 0 4 41 155 0 0 149 /**
 * Sets the level setting associated with this collector for the main thread
 * to the indicated value.  This implicitly calls flush_level().
 */ 2 4 this 3 158  5 level 1 146  
53 0 0 4 42 155 0 0 246 /**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the indicated thread.  If the collector
 * did not already have a level setting for this thread, it is initialized to
 * 0.
 */ 3 4 this 3 158  6 thread 1 167  9 increment 1 146  
54 0 0 4 42 155 0 0 382 /**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the main thread.  If the collector did
 * not already have a level setting for the main thread, it is initialized to
 * 0.
 *
 * As an optimization, the data is not immediately set to the PStatClient.  It
 * will be sent the next time flush_level() is called.
 */ 2 4 this 3 158  9 increment 1 146  
55 0 0 4 43 155 0 0 251 /**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the indicated thread.  If the
 * collector did not already have a level setting for this thread, it is
 * initialized to 0.
 */ 3 4 this 3 158  6 thread 1 167  9 decrement 1 146  
56 0 0 4 43 155 0 0 387 /**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the main thread.  If the
 * collector did not already have a level setting for the main thread, it is
 * initialized to 0.
 *
 * As an optimization, the data is not immediately set to the PStatClient.  It
 * will be sent the next time flush_level() is called.
 */ 2 4 this 3 158  9 decrement 1 146  
57 0 0 4 44 155 0 0 65 /**
 * Calls add_level() and immediately calls flush_level().
 */ 2 4 this 3 158  9 increment 1 146  
58 0 0 4 45 155 0 0 65 /**
 * Calls sub_level() and immediately calls flush_level().
 */ 2 4 this 3 158  9 decrement 1 146  
59 0 0 4 46 155 0 0 95 /**
 * Updates the PStatClient with the recent results from add_level() and
 * sub_level().
 */ 1 4 this 3 158  
60 0 0 6 47 146 0 0 125 /**
 * Returns the current level value of the given collector in the main thread.
 * This implicitly calls flush_level().
 */ 1 4 this 3 158  
61 0 0 6 47 146 0 0 66 /**
 * Returns the current level value of the given collector.
 */ 2 4 this 3 158  6 thread 1 167  
62 0 0 4 48 155 0 0 178 /**
 * Removes the level setting associated with this collector for the current
 * thread.  The collector will no longer show up on any level graphs in the
 * current thread.
 */ 1 4 this 3 158  
63 0 0 4 49 155 0 0 114 /**
 * Sets the level setting associated with this collector for the current
 * thread to the indicated value.
 */ 2 4 this 3 158  5 level 1 146  
64 0 0 4 50 155 0 0 251 /**
 * Adds the indicated increment (which may be negative) to the level setting
 * associated with this collector for the current thread.  If the collector
 * did not already have a level setting for the current thread, it is
 * initialized to 0.
 */ 2 4 this 3 158  9 increment 1 146  
65 0 0 4 51 155 0 0 256 /**
 * Subtracts the indicated decrement (which may be negative) to the level
 * setting associated with this collector for the current thread.  If the
 * collector did not already have a level setting for the current thread, it
 * is initialized to 0.
 */ 2 4 this 3 158  9 decrement 1 146  
66 0 0 6 52 146 0 0 91 /**
 * Returns the current level value of the given collector in the current
 * thread.
 */ 1 4 this 3 158  
67 0 0 6 53 150 0 0 91 /**
 * Returns the index number of this particular collector within the
 * PStatClient.
 */ 1 4 this 3 163  
68 0 0 23 57 161 125 0 135 /**
 * Normally, this constructor is called only from PStatClient.  Use one of the
 * constructors below to create your own Thread.
 */ 2 6 client 1 153  5 index 1 150  
69 0 0 15 57 161 125 0 10 /**
 *
 */ 1 4 copy 1 167  
70 0 0 23 57 161 125 0 160 /**
 * Creates a new named thread.  This will be used to unify tasks that share a
 * common thread, and differentiate tasks that occur in different threads.
 */ 2 6 thread 1 149  6 client 5 153  
71 0 0 6 58 161 0 0 0 2 4 this 3 161  4 copy 1 167  
72 0 0 4 59 155 0 0 327 /**
 * This must be called at the start of every "frame", whatever a frame may be
 * deemed to be, to accumulate all the stats that have collected so far for
 * the thread and ship them off to the server.
 *
 * Calling PStatClient::thread_tick() will automatically call this for any
 * threads with the indicated sync name.
 */ 1 4 this 3 161  
73 0 0 4 60 155 0 0 118 /**
 * This is a slightly lower-level version of new_frame that also specifies the
 * data to send for this frame.
 */ 2 4 this 3 161  10 frame_data 1 169  
74 0 0 7 61 149 0 0 90 /**
 * Returns the Panda Thread object associated with this particular
 * PStatThread.
 */ 1 4 this 3 167  
75 0 0 6 62 150 0 0 85 /**
 * Returns the index number of this particular thread within the PStatClient.
 */ 1 4 this 3 167  
76 0 0 23 86 172 140 0 10 /**
 *
 */ 1 3 col 1 163  
32
141 11 PStatClient 0 75777 11 PStatClient 11 PStatClient 0 0 0 0 0 7 173 174 175 176 179 180 181 30 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 126 127 128 129 130 131 132 133 134 135 136 137 138 2 182 183 0 1 0 142 0 0 0 0 0

142 14 PStatsCallback 0 264192 22 Thread::PStatsCallback 22 Thread::PStatsCallback 143 0 0 0 0 0 0 0 0 0 0 0 89
// This class allows integration with PStats, particularly in the
// SIMPLE_THREADS case.

143 6 Thread 0 2048 6 Thread 6 Thread 0 0 0 0 0 0 0 0 0 0 0 0 396
/**
 * A thread; that is, a lightweight process.  This is an abstract base class;
 * to use it, you must subclass from it and redefine thread_main().
 *
 * The thread itself will keep a reference count on the Thread object while it
 * is running; when the thread returns from its root function, the Thread
 * object will automatically be destructed if no other pointers are
 * referencing it.
 */

144 6 string 0 2105344 11 std::string 11 std::string 0 0 145 0 0 0 0 0 0 0 0 0 0

145 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

146 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

147 14 PStatCollector 0 141313 14 PStatCollector 14 PStatCollector 0 0 0 1 94 118 0 23 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110 111 112 113 114 115 116 117 0 0 0 0 0 900
/**
 * A lightweight class that represents a single element that may be timed
 * and/or counted via stats.
 *
 * Collectors can be used to measure two different kinds of values: elapsed
 * time, and "other".
 *
 * To measure elapsed time, call start() and stop() as appropriate to bracket
 * the section of code you want to time (or use a PStatTimer to do this
 * automatically).
 *
 * To measure anything else, call set_level() and/or add_level() to set the
 * "level" value associated with this collector.  The meaning of the value set
 * for the "level" is entirely up to the user; it may represent the number of
 * triangles rendered or the kilobytes of texture memory consumed, for
 * instance.  The level set will remain fixed across multiple frames until it
 * is reset via another set_level() or adjusted via a call to add_level().  It
 * may also be completely removed via clear_level().
 */

148 11 PStatThread 0 141313 11 PStatThread 11 PStatThread 0 0 0 1 119 125 2 177 178 5 120 121 122 123 124 0 0 0 0 0 145
/**
 * A lightweight class that represents a single thread of execution to PStats.
 * It corresponds one-to-one with Panda's Thread instance.
 */

149 8 Thread * 0 8576 8 Thread * 8 Thread * 0 0 143 0 0 0 0 0 0 0 0 0 0

150 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

151 21 PStatCollectorForward 0 141313 21 PStatCollectorForward 21 PStatCollectorForward 0 0 0 1 139 140 0 0 0 0 1 0 152 0 0 0 0 173
/**
 * This class serves as a cheap forward reference to a PStatCollector, so that
 * classes that are defined before the pstats module may access the
 * PStatCollector.
 */

152 25 PStatCollectorForwardBase 0 2048 25 PStatCollectorForwardBase 25 PStatCollectorForwardBase 0 0 0 0 0 0 0 0 0 0 0 0 282
/**
 * This class serves as a cheap forward reference to a PStatCollector, which
 * is defined in the pstatclient module (and is not directly accessible here
 * in the express module).
 *
 * This is subclassed as PStatCollectorForward, which defines the actual
 * functionality.
 */

153 13 PStatClient * 0 8576 13 PStatClient * 13 PStatClient * 0 0 141 0 0 0 0 0 0 0 0 0 0

154 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

155 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

156 19 PStatClient const * 0 8576 19 PStatClient const * 19 PStatClient const * 0 0 157 0 0 0 0 0 0 0 0 0 0

157 17 PStatClient const 0 8832 17 PStatClient const 17 PStatClient const 0 0 141 0 0 0 0 0 0 0 0 0 0

158 16 PStatCollector * 0 8576 16 PStatCollector * 16 PStatCollector * 0 0 147 0 0 0 0 0 0 0 0 0 0

159 19 PStatCollectorDef * 0 8576 19 PStatCollectorDef * 19 PStatCollectorDef * 0 0 160 0 0 0 0 0 0 0 0 0 0

160 17 PStatCollectorDef 0 1050624 17 PStatCollectorDef 17 PStatCollectorDef 0 0 0 0 0 0 0 0 0 0 0 0 91
/**
 * Defines the details about the Collectors: the name, the suggested color,
 * etc.
 */

161 13 PStatThread * 0 8576 13 PStatThread * 13 PStatThread * 0 0 148 0 0 0 0 0 0 0 0 0 0

162 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

163 22 PStatCollector const * 0 8576 22 PStatCollector const * 22 PStatCollector const * 0 0 164 0 0 0 0 0 0 0 0 0 0

164 20 PStatCollector const 0 8832 20 PStatCollector const 20 PStatCollector const 0 0 147 0 0 0 0 0 0 0 0 0 0

165 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 166 0 0 0 0 0 0 0 0 0 0

166 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

167 19 PStatThread const * 0 8576 19 PStatThread const * 19 PStatThread const * 0 0 168 0 0 0 0 0 0 0 0 0 0

168 17 PStatThread const 0 8832 17 PStatThread const 17 PStatThread const 0 0 148 0 0 0 0 0 0 0 0 0 0

169 22 PStatFrameData const * 0 8576 22 PStatFrameData const * 22 PStatFrameData const * 0 0 170 0 0 0 0 0 0 0 0 0 0

170 20 PStatFrameData const 0 8832 20 PStatFrameData const 20 PStatFrameData const 0 0 171 0 0 0 0 0 0 0 0 0 0

171 14 PStatFrameData 0 1050624 14 PStatFrameData 14 PStatFrameData 0 0 0 0 0 0 0 0 0 0 0 0 215
/**
 * Contains the raw timing and level data for a single frame.  This is a
 * sequence of start/stop events, as well as a table of level values,
 * associated with a number of collectors within a single frame.
 */

172 23 PStatCollectorForward * 0 8576 23 PStatCollectorForward * 23 PStatCollectorForward * 0 0 151 0 0 0 0 0 0 0 0 0 0

0
9
173 11 client_name 0 6 144 78 77 0 0 0 0 0 0 24 PStatClient::client_name 0

174 8 max_rate 0 6 146 80 79 0 0 0 0 0 0 21 PStatClient::max_rate 0

175 10 collectors 0 66 147 82 0 0 0 0 81 0 0 23 PStatClient::collectors 0

176 7 threads 0 66 148 87 0 0 0 0 86 0 0 20 PStatClient::threads 0

177 6 thread 0 2 149 123 0 0 0 0 0 0 0 19 PStatThread::thread 0

178 5 index 0 2 150 124 0 0 0 0 0 0 0 18 PStatThread::index 0

179 11 main_thread 0 2 148 91 0 0 0 0 0 0 0 24 PStatClient::main_thread 0

180 14 current_thread 0 2 148 92 0 0 0 0 0 0 0 27 PStatClient::current_thread 0

181 9 real_time 0 2 146 93 0 0 0 0 0 0 0 22 PStatClient::real_time 0

2
182 14 get_collectors 0 81 82 27 PStatClient::get_collectors 0

183 11 get_threads 0 86 87 24 PStatClient::get_threads 0

