'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_NV_render_depth_texture'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_NV_render_depth_texture',error_checker=_errors._error_checker)
WGL_BIND_TO_TEXTURE_DEPTH_NV=_C('WGL_BIND_TO_TEXTURE_DEPTH_NV',0x20A3)
WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV=_C('WGL_BIND_TO_TEXTURE_RECTANGLE_DEPTH_NV',0x20A4)
WGL_DEPTH_COMPONENT_NV=_C('WGL_DEPTH_COMPONENT_NV',0x20A7)
WGL_DEPTH_TEXTURE_FORMAT_NV=_C('WGL_DEPTH_TEXTURE_FORMAT_NV',0x20A5)
WGL_TEXTURE_DEPTH_COMPONENT_NV=_C('WGL_TEXTURE_DEPTH_COMPONENT_NV',0x20A6)

