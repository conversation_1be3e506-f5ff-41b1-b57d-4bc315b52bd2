'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_MESA_query_renderer'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_MESA_query_renderer',error_checker=_errors._error_checker)
GLX_RENDERER_ACCELERATED_MESA=_C('GLX_RENDERER_ACCELERATED_MESA',0x8186)
GLX_RENDERER_DEVICE_ID_MESA=_C('GLX_RENDERER_DEVICE_ID_MESA',0x8184)
GLX_RENDERER_OPENGL_COMPATIBILITY_PROFILE_VERSION_MESA=_C('GLX_RENDERER_OPENGL_COMPATIBILITY_PROFILE_VERSION_MESA',0x818B)
GLX_RENDERER_OPENGL_CORE_PROFILE_VERSION_MESA=_C('GLX_RENDERER_OPENGL_CORE_PROFILE_VERSION_MESA',0x818A)
GLX_RENDERER_OPENGL_ES2_PROFILE_VERSION_MESA=_C('GLX_RENDERER_OPENGL_ES2_PROFILE_VERSION_MESA',0x818D)
GLX_RENDERER_OPENGL_ES_PROFILE_VERSION_MESA=_C('GLX_RENDERER_OPENGL_ES_PROFILE_VERSION_MESA',0x818C)
GLX_RENDERER_PREFERRED_PROFILE_MESA=_C('GLX_RENDERER_PREFERRED_PROFILE_MESA',0x8189)
GLX_RENDERER_UNIFIED_MEMORY_ARCHITECTURE_MESA=_C('GLX_RENDERER_UNIFIED_MEMORY_ARCHITECTURE_MESA',0x8188)
GLX_RENDERER_VENDOR_ID_MESA=_C('GLX_RENDERER_VENDOR_ID_MESA',0x8183)
GLX_RENDERER_VERSION_MESA=_C('GLX_RENDERER_VERSION_MESA',0x8185)
GLX_RENDERER_VIDEO_MEMORY_MESA=_C('GLX_RENDERER_VIDEO_MEMORY_MESA',0x8187)
@_f
@_p.types(_cs.Bool,_cs.c_int,ctypes.POINTER(_cs.c_uint))
def glXQueryCurrentRendererIntegerMESA(attribute,value):pass
@_f
@_p.types(ctypes.c_char_p,_cs.c_int)
def glXQueryCurrentRendererStringMESA(attribute):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.c_uint))
def glXQueryRendererIntegerMESA(dpy,screen,renderer,attribute,value):pass
@_f
@_p.types(ctypes.c_char_p,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,_cs.c_int)
def glXQueryRendererStringMESA(dpy,screen,renderer,attribute):pass
