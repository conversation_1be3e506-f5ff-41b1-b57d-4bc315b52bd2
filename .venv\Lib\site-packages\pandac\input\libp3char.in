1730992849
3 3
9 libp3char 4 uMZI 12 panda3d.core 
44
48 14 CharacterJoint 0 260 92 30 CharacterJoint::CharacterJoint 0 1 1 57
/**
 * For internal use only.
 */

/**
 *
 */

/**
 *
 */
155
explicit CharacterJoint::CharacterJoint(Character *character, PartBundle *root, PartGroup *parent, std::string const &name, LMatrix4 const &default_value);

49 17 add_net_transform 0 4 92 33 CharacterJoint::add_net_transform 0 1 2 334
/**
 * Adds the indicated node to the list of nodes that will be updated each
 * frame with the joint's net transform from the root.  Returns true if the
 * node is successfully added, false if it had already been added.
 *
 * A CharacterJointEffect for this joint's Character will automatically be
 * added to the specified node.
 */
56
bool CharacterJoint::add_net_transform(PandaNode *node);

50 20 remove_net_transform 0 4 92 36 CharacterJoint::remove_net_transform 0 1 3 333
/**
 * Removes the indicated node from the list of nodes that will be updated each
 * frame with the joint's net transform from the root.  Returns true if the
 * node is successfully removed, false if it was not on the list.
 *
 * If the node has a CharacterJointEffect that matches this joint's Character,
 * it will be cleared.
 */
59
bool CharacterJoint::remove_net_transform(PandaNode *node);

51 17 has_net_transform 0 4 92 33 CharacterJoint::has_net_transform 0 1 4 157
/**
 * Returns true if the node is on the list of nodes that will be updated each
 * frame with the joint's net transform from the root, false otherwise.
 */
62
bool CharacterJoint::has_net_transform(PandaNode *node) const;

52 20 clear_net_transforms 0 4 92 36 CharacterJoint::clear_net_transforms 0 1 5 132
/**
 * Removes all nodes from the list of nodes that will be updated each frame
 * with the joint's net transform from the root.
 */
48
void CharacterJoint::clear_net_transforms(void);

53 18 get_net_transforms 0 4 92 34 CharacterJoint::get_net_transforms 0 1 6 183
/**
 * Returns a list of the net transforms set for this node.  Note that this
 * returns a list of NodePaths, even though the net transforms are actually a
 * list of PandaNodes.
 */
60
NodePathCollection CharacterJoint::get_net_transforms(void);

54 19 add_local_transform 0 4 92 35 CharacterJoint::add_local_transform 0 1 7 539
/**
 * Adds the indicated node to the list of nodes that will be updated each
 * frame with the joint's local transform from its parent.  Returns true if
 * the node is successfully added, false if it had already been added.
 *
 * The Character pointer should be the Character object that owns this joint;
 * this will be used to create a CharacterJointEffect for this node.  If it is
 * NULL, no such effect will be created.
 *
 * A CharacterJointEffect for this joint's Character will automatically be
 * added to the specified node.
 */
58
bool CharacterJoint::add_local_transform(PandaNode *node);

55 22 remove_local_transform 0 4 92 38 CharacterJoint::remove_local_transform 0 1 8 337
/**
 * Removes the indicated node from the list of nodes that will be updated each
 * frame with the joint's local transform from its parent.  Returns true if
 * the node is successfully removed, false if it was not on the list.
 *
 * If the node has a CharacterJointEffect that matches this joint's Character,
 * it will be cleared.
 */
61
bool CharacterJoint::remove_local_transform(PandaNode *node);

56 19 has_local_transform 0 4 92 35 CharacterJoint::has_local_transform 0 1 9 161
/**
 * Returns true if the node is on the list of nodes that will be updated each
 * frame with the joint's local transform from its parent, false otherwise.
 */
64
bool CharacterJoint::has_local_transform(PandaNode *node) const;

57 22 clear_local_transforms 0 4 92 38 CharacterJoint::clear_local_transforms 0 1 10 136
/**
 * Removes all nodes from the list of nodes that will be updated each frame
 * with the joint's local transform from its parent.
 */
50
void CharacterJoint::clear_local_transforms(void);

58 20 get_local_transforms 0 4 92 36 CharacterJoint::get_local_transforms 0 1 11 187
/**
 * Returns a list of the local transforms set for this node.  Note that this
 * returns a list of NodePaths, even though the local transforms are actually
 * a list of PandaNodes.
 */
62
NodePathCollection CharacterJoint::get_local_transforms(void);

59 13 get_transform 0 4 92 29 CharacterJoint::get_transform 0 2 12 13 128
/**
 * Returns the transform matrix of the joint
 */

/**
 * Copies the joint's current transform into the indicated matrix.
 */
128
void CharacterJoint::get_transform(LMatrix4 &transform) const;
inline LMatrix4 const &CharacterJoint::get_transform(void) const;

60 19 get_transform_state 0 4 92 35 CharacterJoint::get_transform_state 0 1 14 0
81
ConstPointerTo< TransformState > CharacterJoint::get_transform_state(void) const;

61 17 get_net_transform 0 4 92 33 CharacterJoint::get_net_transform 0 1 15 139
/**
 * Copies the joint's current net transform (composed from the root of the
 * character joint hierarchy) into the indicated matrix.
 */
66
void CharacterJoint::get_net_transform(LMatrix4 &transform) const;

62 13 get_character 0 4 92 29 CharacterJoint::get_character 0 1 16 54
/**
 * Returns the Character that owns this joint.
 */
53
Character *CharacterJoint::get_character(void) const;

63 14 get_class_type 0 4 92 30 CharacterJoint::get_class_type 0 1 17 0
55
static TypeHandle CharacterJoint::get_class_type(void);

64 15 CharacterSlider 0 260 94 32 CharacterSlider::CharacterSlider 0 2 18 19 69
/**
 * For internal use only.
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
207
explicit CharacterSlider::CharacterSlider(PartGroup *parent, std::string const &name);
explicit CharacterSlider::CharacterSlider(PartGroup *parent, std::string const &name, PN_stdfloat const &default_value);

65 14 get_class_type 0 4 94 31 CharacterSlider::get_class_type 0 1 20 0
56
static TypeHandle CharacterSlider::get_class_type(void);

66 21 CharacterVertexSlider 0 260 96 44 CharacterVertexSlider::CharacterVertexSlider 0 1 21 216
/**
 * Constructs an invalid object; used only by the bam loader.
 */

/**
 * Constructs a new object that converts vertices from the indicated joint's
 * coordinate space, into the other indicated joint's space.
 */
75
CharacterVertexSlider::CharacterVertexSlider(CharacterSlider *char_slider);

67 15 get_char_slider 0 4 96 38 CharacterVertexSlider::get_char_slider 0 1 22 96
/**
 * Returns the CharacterSlider object for which this object returns the slider
 * value.
 */
81
inline CharacterSlider const *CharacterVertexSlider::get_char_slider(void) const;

68 14 get_class_type 0 4 96 37 CharacterVertexSlider::get_class_type 0 1 23 0
62
static TypeHandle CharacterVertexSlider::get_class_type(void);

69 20 JointVertexTransform 0 260 98 42 JointVertexTransform::JointVertexTransform 0 1 24 216
/**
 * Constructs an invalid object; used only by the bam loader.
 */

/**
 * Constructs a new object that converts vertices from the indicated joint's
 * coordinate space, into the other indicated joint's space.
 */
66
JointVertexTransform::JointVertexTransform(CharacterJoint *joint);

70 9 get_joint 0 4 98 31 JointVertexTransform::get_joint 0 1 25 73
/**
 * Returns the joint for which this object returns the transform.
 */
73
inline CharacterJoint const *JointVertexTransform::get_joint(void) const;

71 14 get_class_type 0 4 98 36 JointVertexTransform::get_class_type 0 1 26 0
61
static TypeHandle JointVertexTransform::get_class_type(void);

72 9 Character 0 260 100 20 Character::Character 0 2 27 28 78
/**
 * Use make_copy() or copy_subgraph() to copy a Character.
 */

/**
 *
 */
113
explicit Character::Character(std::string const &name);
inline Character::Character(Character const &) = default;

73 10 get_bundle 0 4 100 21 Character::get_bundle 0 1 29 10
/**
 *
 */
64
inline CharacterJointBundle *Character::get_bundle(int i) const;

74 13 merge_bundles 0 4 100 24 Character::merge_bundles 0 2 30 31 1309
/**
 * Merges old_bundle with new_bundle.  old_bundle must be one of the
 * PartBundles within this node.  At the end of this call, the old_bundle
 * pointer within this node will be replaced with the new_bundle pointer, and
 * all geometry within this node will be updated to reference new_bundle.
 *
 * @deprecated Use the newer version of this method, below.
 */

/**
 * Merges old_bundle_handle->get_bundle() with new_bundle.  old_bundle_handle
 * must be one of the PartBundleHandle within this node.  At the end of this
 * call, the bundle pointer within the old_bundle_handle will be replaced with
 * that within the new_bundle_handle pointer, and all geometry within this
 * node will be updated to reference new_bundle.
 *
 * Normally, this is called when the two bundles have the same, or nearly the
 * same, hierarchies.  In this case, new_bundle will simply be assigned over
 * the old_bundle position.  However, if any joints are present in one bundle
 * or the other, new_bundle will be modified to contain the union of all
 * joints.
 *
 * The geometry below this node is also updated to reference new_bundle,
 * instead of the original old_bundle.
 *
 * This method is intended to unify two different models that share a common
 * skeleton, for instance, different LOD's of the same model.
 */
187
void Character::merge_bundles(PartBundle *old_bundle, PartBundle *other_bundle);
void Character::merge_bundles(PartBundleHandle *old_bundle_handle, PartBundleHandle *other_bundle_handle);

75 17 set_lod_animation 0 4 100 28 Character::set_lod_animation 0 1 32 1171
/**
 * Activates a special mode in which the character animates less frequently as
 * it gets further from the camera.  This is intended as a simple optimization
 * to minimize the effort of computing animation for lots of characters that
 * may not necessarily be very important to animate every frame.
 *
 * If the character is closer to the camera than near_distance, then it is
 * animated its normal rate, every frame.  If the character is exactly
 * far_distance away, it is animated only every delay_factor seconds (which
 * should be a number greater than 0).  If the character is between
 * near_distance and far_distance, its animation rate is linearly interpolated
 * according to its distance between the two.  The interpolation function
 * continues beyond far_distance, so that the character is animated
 * increasingly less frequently as it gets farther away.
 *
 * The distance calculations are made from center, which is a fixed point
 * relative to the character node, to the camera's lod center or cull center
 * node (or to the camera node itself).
 *
 * If multiple cameras are viewing the character in any given frame, the
 * closest one counts.
 */
136
void Character::set_lod_animation(LPoint3 const &center, PN_stdfloat far_distance, PN_stdfloat near_distance, PN_stdfloat delay_factor);

76 19 clear_lod_animation 0 4 100 30 Character::clear_lod_animation 0 1 33 172
/**
 * Undoes the effect of a recent call to set_lod_animation().  Henceforth, the
 * character will animate every frame, regardless of its distance from the
 * camera.
 */
42
void Character::clear_lod_animation(void);

77 10 find_joint 0 4 100 21 Character::find_joint 0 1 34 174
/**
 * Returns a pointer to the joint with the given name, if there is such a
 * joint, or NULL if there is no such joint.  This will not return a pointer
 * to a slider.
 */
69
CharacterJoint *Character::find_joint(std::string const &name) const;

78 11 find_slider 0 4 100 22 Character::find_slider 0 1 35 176
/**
 * Returns a pointer to the slider with the given name, if there is such a
 * slider, or NULL if there is no such slider.  This will not return a pointer
 * to a joint.
 */
71
CharacterSlider *Character::find_slider(std::string const &name) const;

79 11 write_parts 0 4 100 22 Character::write_parts 0 1 36 129
/**
 * Writes a list of the Character's joints and sliders, in their hierchical
 * structure, to the indicated output stream.
 */
53
void Character::write_parts(std::ostream &out) const;

80 17 write_part_values 0 4 100 28 Character::write_part_values 0 1 37 166
/**
 * Writes a list of the Character's joints and sliders, along with each
 * current position, in their hierchical structure, to the indicated output
 * stream.
 */
59
void Character::write_part_values(std::ostream &out) const;

81 13 update_to_now 0 4 100 24 Character::update_to_now 0 1 38 310
/**
 * Advances the character's frame to the current time, and then calls
 * update().  This can be used by show code to force an update of the
 * character's position to the current frame, regardless of whether the
 * character is currently onscreen and animating.
 *
 * @deprecated Call update() instead.
 */
36
void Character::update_to_now(void);

82 6 update 0 4 100 17 Character::update 0 1 39 182
/**
 * Recalculates the Character's joints and vertices for the current frame.
 * Normally this is performed automatically during the render and need not be
 * called explicitly.
 */
29
void Character::update(void);

83 12 force_update 0 4 100 23 Character::force_update 0 1 40 74
/**
 * Recalculates the character even if we think it doesn't need it.
 */
35
void Character::force_update(void);

84 14 get_class_type 0 4 100 25 Character::get_class_type 0 1 41 0
50
static TypeHandle Character::get_class_type(void);

85 20 CharacterJointBundle 0 260 102 42 CharacterJointBundle::CharacterJointBundle 0 1 42 232
/**
 * Normally, you'd use make_copy() or copy_subgraph() to make a copy of this.
 */

/**
 * Normally, there is no need to create a CharacterJointBundle directly.  The
 * Character node will automatically create one for itself.
 */
82
explicit CharacterJointBundle::CharacterJointBundle(std::string const &name = "");

86 8 get_node 0 4 102 30 CharacterJointBundle::get_node 0 1 43 69
/**
 * Returns the nth Character associated with this PartBundle.
 */
62
inline Character *CharacterJointBundle::get_node(int n) const;

87 14 get_class_type 0 4 102 36 CharacterJointBundle::get_class_type 0 1 44 0
61
static TypeHandle CharacterJointBundle::get_class_type(void);

88 4 make 0 4 104 26 CharacterJointEffect::make 0 1 45 251
/**
 * Constructs a new CharacterJointEffect object that references the indicated
 * character.  When a relative get_transform() is called on the node that
 * contains the CharacterJointEffect, it will implicitly call
 * character->update() first.
 */
87
static ConstPointerTo< RenderEffect > CharacterJointEffect::make(Character *character);

89 13 get_character 0 4 104 35 CharacterJointEffect::get_character 0 1 46 158
/**
 * Returns the Character that will get update() called on it when this node's
 * relative transform is queried, or NULL if there is no such character.
 */
78
inline PointerTo< Character > CharacterJointEffect::get_character(void) const;

90 14 get_class_type 0 4 104 36 CharacterJointEffect::get_class_type 0 1 47 0
61
static TypeHandle CharacterJointEffect::get_class_type(void);

91 21 ~CharacterJointEffect 0 516 104 43 CharacterJointEffect::~CharacterJointEffect 0 0 0
50
CharacterJointEffect::~CharacterJointEffect(void);

47
1 0 0 7 3 115 0 0 10 /**
 *
 */ 5 9 character 1 106  4 root 1 107  6 parent 1 108  4 name 1 110  13 default_value 1 111  
2 0 0 6 4 118 0 0 334 /**
 * Adds the indicated node to the list of nodes that will be updated each
 * frame with the joint's net transform from the root.  Returns true if the
 * node is successfully added, false if it had already been added.
 *
 * A CharacterJointEffect for this joint's Character will automatically be
 * added to the specified node.
 */ 2 4 this 3 115  4 node 1 116  
3 0 0 6 5 118 0 0 333 /**
 * Removes the indicated node from the list of nodes that will be updated each
 * frame with the joint's net transform from the root.  Returns true if the
 * node is successfully removed, false if it was not on the list.
 *
 * If the node has a CharacterJointEffect that matches this joint's Character,
 * it will be cleared.
 */ 2 4 this 3 115  4 node 1 116  
4 0 0 6 6 118 0 0 157 /**
 * Returns true if the node is on the list of nodes that will be updated each
 * frame with the joint's net transform from the root, false otherwise.
 */ 2 4 this 3 119  4 node 1 116  
5 0 0 4 7 121 0 0 132 /**
 * Removes all nodes from the list of nodes that will be updated each frame
 * with the joint's net transform from the root.
 */ 1 4 this 3 115  
6 0 0 7 8 123 0 0 183 /**
 * Returns a list of the net transforms set for this node.  Note that this
 * returns a list of NodePaths, even though the net transforms are actually a
 * list of PandaNodes.
 */ 1 4 this 3 115  
7 0 0 6 9 118 0 0 539 /**
 * Adds the indicated node to the list of nodes that will be updated each
 * frame with the joint's local transform from its parent.  Returns true if
 * the node is successfully added, false if it had already been added.
 *
 * The Character pointer should be the Character object that owns this joint;
 * this will be used to create a CharacterJointEffect for this node.  If it is
 * NULL, no such effect will be created.
 *
 * A CharacterJointEffect for this joint's Character will automatically be
 * added to the specified node.
 */ 2 4 this 3 115  4 node 1 116  
8 0 0 6 10 118 0 0 337 /**
 * Removes the indicated node from the list of nodes that will be updated each
 * frame with the joint's local transform from its parent.  Returns true if
 * the node is successfully removed, false if it was not on the list.
 *
 * If the node has a CharacterJointEffect that matches this joint's Character,
 * it will be cleared.
 */ 2 4 this 3 115  4 node 1 116  
9 0 0 6 11 118 0 0 161 /**
 * Returns true if the node is on the list of nodes that will be updated each
 * frame with the joint's local transform from its parent, false otherwise.
 */ 2 4 this 3 119  4 node 1 116  
10 0 0 4 12 121 0 0 136 /**
 * Removes all nodes from the list of nodes that will be updated each frame
 * with the joint's local transform from its parent.
 */ 1 4 this 3 115  
11 0 0 7 13 123 0 0 187 /**
 * Returns a list of the local transforms set for this node.  Note that this
 * returns a list of NodePaths, even though the local transforms are actually
 * a list of PandaNodes.
 */ 1 4 this 3 115  
12 0 0 6 14 111 0 0 52 /**
 * Returns the transform matrix of the joint
 */ 1 4 this 3 119  
13 0 0 4 14 121 0 0 74 /**
 * Copies the joint's current transform into the indicated matrix.
 */ 2 4 this 3 119  9 transform 1 124  
14 0 0 7 15 127 0 0 0 1 4 this 3 119  
15 0 0 4 16 121 0 0 139 /**
 * Copies the joint's current net transform (composed from the root of the
 * character joint hierarchy) into the indicated matrix.
 */ 2 4 this 3 119  9 transform 1 124  
16 0 0 7 17 106 0 0 54 /**
 * Returns the Character that owns this joint.
 */ 1 4 this 3 119  
17 0 0 7 18 129 0 0 0 0 
18 0 0 7 21 130 0 0 10 /**
 *
 */ 2 6 parent 1 108  4 name 1 110  
19 0 0 7 21 130 0 0 10 /**
 *
 */ 3 6 parent 1 108  4 name 1 110  13 default_value 1 131  
20 0 0 7 22 129 0 0 0 0 
21 0 0 23 25 133 0 0 145 /**
 * Constructs a new object that converts vertices from the indicated joint's
 * coordinate space, into the other indicated joint's space.
 */ 1 11 char_slider 1 130  
22 0 0 7 26 137 0 0 96 /**
 * Returns the CharacterSlider object for which this object returns the slider
 * value.
 */ 1 4 this 3 135  
23 0 0 7 27 129 0 0 0 0 
24 0 0 23 30 138 0 0 145 /**
 * Constructs a new object that converts vertices from the indicated joint's
 * coordinate space, into the other indicated joint's space.
 */ 1 5 joint 1 115  
25 0 0 7 31 119 0 0 73 /**
 * Returns the joint for which this object returns the transform.
 */ 1 4 this 3 139  
26 0 0 7 32 129 0 0 0 0 
27 0 0 15 35 106 0 0 0 1 6 param0 0 141  
28 0 0 7 35 106 0 0 10 /**
 *
 */ 1 4 name 1 110  
29 0 0 7 36 144 0 0 10 /**
 *
 */ 2 4 this 3 141  1 i 1 143  
30 0 0 4 37 121 0 0 365 /**
 * Merges old_bundle with new_bundle.  old_bundle must be one of the
 * PartBundles within this node.  At the end of this call, the old_bundle
 * pointer within this node will be replaced with the new_bundle pointer, and
 * all geometry within this node will be updated to reference new_bundle.
 *
 * @deprecated Use the newer version of this method, below.
 */ 3 4 this 3 106  10 old_bundle 1 107  12 other_bundle 1 107  
31 0 0 4 37 121 0 0 942 /**
 * Merges old_bundle_handle->get_bundle() with new_bundle.  old_bundle_handle
 * must be one of the PartBundleHandle within this node.  At the end of this
 * call, the bundle pointer within the old_bundle_handle will be replaced with
 * that within the new_bundle_handle pointer, and all geometry within this
 * node will be updated to reference new_bundle.
 *
 * Normally, this is called when the two bundles have the same, or nearly the
 * same, hierarchies.  In this case, new_bundle will simply be assigned over
 * the old_bundle position.  However, if any joints are present in one bundle
 * or the other, new_bundle will be modified to contain the union of all
 * joints.
 *
 * The geometry below this node is also updated to reference new_bundle,
 * instead of the original old_bundle.
 *
 * This method is intended to unify two different models that share a common
 * skeleton, for instance, different LOD's of the same model.
 */ 3 4 this 3 106  17 old_bundle_handle 1 145  19 other_bundle_handle 1 145  
32 0 0 4 38 121 0 0 1171 /**
 * Activates a special mode in which the character animates less frequently as
 * it gets further from the camera.  This is intended as a simple optimization
 * to minimize the effort of computing animation for lots of characters that
 * may not necessarily be very important to animate every frame.
 *
 * If the character is closer to the camera than near_distance, then it is
 * animated its normal rate, every frame.  If the character is exactly
 * far_distance away, it is animated only every delay_factor seconds (which
 * should be a number greater than 0).  If the character is between
 * near_distance and far_distance, its animation rate is linearly interpolated
 * according to its distance between the two.  The interpolation function
 * continues beyond far_distance, so that the character is animated
 * increasingly less frequently as it gets farther away.
 *
 * The distance calculations are made from center, which is a fixed point
 * relative to the character node, to the camera's lod center or cull center
 * node (or to the camera node itself).
 *
 * If multiple cameras are viewing the character in any given frame, the
 * closest one counts.
 */ 5 4 this 3 106  6 center 1 147  12 far_distance 1 131  13 near_distance 1 131  12 delay_factor 1 131  
33 0 0 4 39 121 0 0 172 /**
 * Undoes the effect of a recent call to set_lod_animation().  Henceforth, the
 * character will animate every frame, regardless of its distance from the
 * camera.
 */ 1 4 this 3 106  
34 0 0 7 40 115 0 0 174 /**
 * Returns a pointer to the joint with the given name, if there is such a
 * joint, or NULL if there is no such joint.  This will not return a pointer
 * to a slider.
 */ 2 4 this 3 141  4 name 1 110  
35 0 0 7 41 130 0 0 176 /**
 * Returns a pointer to the slider with the given name, if there is such a
 * slider, or NULL if there is no such slider.  This will not return a pointer
 * to a joint.
 */ 2 4 this 3 141  4 name 1 110  
36 0 0 4 42 121 0 0 129 /**
 * Writes a list of the Character's joints and sliders, in their hierchical
 * structure, to the indicated output stream.
 */ 2 4 this 3 141  3 out 1 151  
37 0 0 4 43 121 0 0 166 /**
 * Writes a list of the Character's joints and sliders, along with each
 * current position, in their hierchical structure, to the indicated output
 * stream.
 */ 2 4 this 3 141  3 out 1 151  
38 0 0 4 44 121 0 0 310 /**
 * Advances the character's frame to the current time, and then calls
 * update().  This can be used by show code to force an update of the
 * character's position to the current frame, regardless of whether the
 * character is currently onscreen and animating.
 *
 * @deprecated Call update() instead.
 */ 1 4 this 3 106  
39 0 0 4 45 121 0 0 182 /**
 * Recalculates the Character's joints and vertices for the current frame.
 * Normally this is performed automatically during the render and need not be
 * called explicitly.
 */ 1 4 this 3 106  
40 0 0 4 46 121 0 0 74 /**
 * Recalculates the character even if we think it doesn't need it.
 */ 1 4 this 3 106  
41 0 0 7 47 129 0 0 0 0 
42 0 0 7 50 144 0 0 145 /**
 * Normally, there is no need to create a CharacterJointBundle directly.  The
 * Character node will automatically create one for itself.
 */ 1 4 name 5 110  
43 0 0 7 51 106 0 0 69 /**
 * Returns the nth Character associated with this PartBundle.
 */ 2 4 this 3 153  1 n 1 143  
44 0 0 7 52 129 0 0 0 0 
45 0 0 23 55 156 0 0 251 /**
 * Constructs a new CharacterJointEffect object that references the indicated
 * character.  When a relative get_transform() is called on the node that
 * contains the CharacterJointEffect, it will implicitly call
 * character->update() first.
 */ 1 9 character 1 106  
46 0 0 7 56 106 0 0 158 /**
 * Returns the Character that will get update() called on it when this node's
 * relative transform is queried, or NULL if there is no such character.
 */ 1 4 this 3 157  
47 0 0 7 57 129 0 0 0 0 
67
92 14 CharacterJoint 0 75777 14 CharacterJoint 14 CharacterJoint 0 0 0 1 48 0 0 15 49 50 51 52 53 54 55 56 57 58 59 60 61 62 63 0 0 1 0 93 0 0 0 0 111
/**
 * This represents one joint of the character's animation, containing an
 * animating transform matrix.
 */

93 16 MovingPartMatrix 0 2048 16 MovingPartMatrix 16 MovingPartMatrix 0 0 0 0 0 0 0 0 0 0 0 0 84
/**
 * This is a particular kind of MovingPart that accepts a matrix each frame.
 */

94 15 CharacterSlider 0 75777 15 CharacterSlider 15 CharacterSlider 0 0 0 1 64 0 0 1 65 0 0 1 0 95 0 0 0 0 215
/**
 * This is a morph slider within the character.  It's simply a single
 * floating-point value that animates generally between 0 and 1, that controls
 * the effects of one or more morphs within the character.
 */

95 16 MovingPartScalar 0 2048 16 MovingPartScalar 16 MovingPartScalar 0 0 0 0 0 0 0 0 0 0 0 0 84
/**
 * This is a particular kind of MovingPart that accepts a scalar each frame.
 */

96 21 CharacterVertexSlider 0 75777 21 CharacterVertexSlider 21 CharacterVertexSlider 0 0 0 1 66 0 0 2 67 68 0 0 1 0 97 0 0 0 0 137
/**
 * This is a specialization on VertexSlider that returns the slider value
 * associated with a particular CharacterSlider object.
 */

97 12 VertexSlider 0 2048 12 VertexSlider 12 VertexSlider 0 0 0 0 0 0 0 0 0 0 0 0 343
/**
 * This is an abstract base class that retains some slider value, which is a
 * linear value that typically ranges from 0.0 to 1.0, and is used to control
 * the animation of morphs (blend shapes).
 *
 * It is similar to VertexTransform, which keeps a full 4x4 transform matrix,
 * but the VertexSlider only keeps a single float value.
 */

98 20 JointVertexTransform 0 75777 20 JointVertexTransform 20 JointVertexTransform 0 0 0 1 69 0 0 2 70 71 0 0 1 0 99 0 0 0 0 486
/**
 * This is a specialization on VertexTransform that returns the transform
 * necessary to move vertices as if they were assigned to the indicated joint.
 * The geometry itself should be parented to the scene graph at the level of
 * the character's root joint; that is, it should not be parented under a node
 * directly animated by any joints.
 *
 * Multiple combinations of these with different weights are used to implement
 * soft-skinned vertices for an animated character.
 */

99 15 VertexTransform 0 2048 15 VertexTransform 15 VertexTransform 0 0 0 0 0 0 0 0 0 0 0 0 305
/**
 * This is an abstract base class that holds a pointer to some transform,
 * computed in some arbitrary way, that is to be applied to vertices during
 * rendering.  This is used to implement soft-skinned and animated vertices.
 * Derived classes will define how the transform is actually computed.
 */

100 9 Character 0 75777 9 Character 9 Character 0 0 0 1 72 0 0 12 73 74 75 76 77 78 79 80 81 82 83 84 0 0 1 0 101 0 0 0 0 117
/**
 * An animated character, with skeleton-morph animation and either soft-
 * skinned or hard-skinned vertices.
 */

101 14 PartBundleNode 0 2048 14 PartBundleNode 14 PartBundleNode 0 0 0 0 0 0 0 0 0 0 0 0 342
/**
 * This is a node that contains a pointer to an PartBundle.  Like
 * AnimBundleNode, it exists to make it easy to store PartBundles in the scene
 * graph.
 *
 * (Unlike AnimBundleNode, however, PartBundleNode has an additional function:
 * it is also the base class of the Character node type, which adds additional
 * functionality.)
 */

102 20 CharacterJointBundle 0 75777 20 CharacterJointBundle 20 CharacterJointBundle 0 0 0 1 85 0 0 2 86 87 0 0 1 0 103 0 0 0 0 73
/**
 * The collection of all the joints and sliders in the character.
 */

103 10 PartBundle 0 2048 10 PartBundle 10 PartBundle 0 0 0 0 0 0 0 0 0 0 0 0 134
/**
 * This is the root of a MovingPart hierarchy.  It defines the hierarchy of
 * moving parts that make up an animatable object.
 */

104 20 CharacterJointEffect 0 141313 20 CharacterJointEffect 20 CharacterJointEffect 0 0 0 0 91 0 3 88 89 90 0 0 1 0 105 0 0 0 0 340
/**
 * This effect will be added automatically to a node by
 * CharacterJoint::add_net_transform() and
 * CharacterJoint::add_local_transform().
 *
 * The effect binds the node back to the character, so that querying the
 * relative transform of the affected node will automatically force the
 * indicated character to be updated first.
 */

105 12 RenderEffect 0 2048 12 RenderEffect 12 RenderEffect 0 0 0 0 0 0 0 0 0 0 0 0 901
/**
 * This is the base class for a number of special render effects that may be
 * set on scene graph nodes to change the way they render.  This includes
 * BillboardEffect, DecalEffect, etc.
 *
 * RenderEffect represents render properties that must be applied as soon as
 * they are encountered in the scene graph, rather than propagating down to
 * the leaves.  This is different from RenderAttrib, which represents
 * properties like color and texture that don't do anything until they
 * propagate down to a GeomNode.
 *
 * You should not attempt to create or modify a RenderEffect directly;
 * instead, use the make() method of the appropriate kind of effect you want.
 * This will allocate and return a new RenderEffect of the appropriate type,
 * and it may share pointers if possible.  Do not modify the new RenderEffect
 * if you wish to change its properties; instead, create a new one.
 */

106 11 Character * 0 8576 11 Character * 11 Character * 0 0 100 0 0 0 0 0 0 0 0 0 0

107 12 PartBundle * 0 8576 12 PartBundle * 12 PartBundle * 0 0 103 0 0 0 0 0 0 0 0 0 0

108 11 PartGroup * 0 8576 11 PartGroup * 11 PartGroup * 0 0 109 0 0 0 0 0 0 0 0 0 0

109 9 PartGroup 0 2048 9 PartGroup 9 PartGroup 0 0 0 0 0 0 0 0 0 0 0 0 105
/**
 * This is the base class for PartRoot and MovingPart.  It defines a hierarchy
 * of MovingParts.
 */

110 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

111 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 112 0 0 0 0 0 0 0 0 0 0

112 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 113 0 0 0 0 0 0 0 0 0 0

113 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 114 0 0 0 0 0 0 0 0 0 0

114 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

115 16 CharacterJoint * 0 8576 16 CharacterJoint * 16 CharacterJoint * 0 0 92 0 0 0 0 0 0 0 0 0 0

116 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 117 0 0 0 0 0 0 0 0 0 0

117 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

118 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

119 22 CharacterJoint const * 0 8576 22 CharacterJoint const * 22 CharacterJoint const * 0 0 120 0 0 0 0 0 0 0 0 0 0

120 20 CharacterJoint const 0 8832 20 CharacterJoint const 20 CharacterJoint const 0 0 92 0 0 0 0 0 0 0 0 0 0

121 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

122 18 NodePathCollection 0 2048 18 NodePathCollection 18 NodePathCollection 0 0 0 0 0 0 0 0 0 0 0 0 178
/**
 * This is a set of zero or more NodePaths.  It's handy for returning from
 * functions that need to return multiple NodePaths (for instance,
 * NodePaths::get_children).
 */

123 20 NodePathCollection * 0 8576 20 NodePathCollection * 20 NodePathCollection * 0 0 122 0 0 0 0 0 0 0 0 0 0

124 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 113 0 0 0 0 0 0 0 0 0 0

125 20 TransformState const 0 8832 20 TransformState const 20 TransformState const 0 0 126 0 0 0 0 0 0 0 0 0 0

126 14 TransformState 0 16779264 14 TransformState 14 TransformState 0 0 0 0 0 0 0 0 0 0 0 0 734
/**
 * Indicates a coordinate-system transform on vertices.  TransformStates are
 * the primary means for storing transformations on the scene graph.
 *
 * Transforms may be specified in one of two ways: componentwise, with a pos-
 * hpr-scale, or with an arbitrary transform matrix.  If you specify a
 * transform componentwise, it will remember its original components.
 *
 * TransformState objects are managed very much like RenderState objects.
 * They are immutable and reference-counted automatically.
 *
 * You should not attempt to create or modify a TransformState object
 * directly.  Instead, call one of the make() functions to create one for you.
 * And instead of modifying a TransformState object, create a new one.
 */

127 22 TransformState const * 0 8576 22 TransformState const * 22 TransformState const * 0 0 125 0 0 0 0 0 0 0 0 0 0

128 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

129 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 128 0 0 0 0 0 0 0 0 0 0

130 17 CharacterSlider * 0 8576 17 CharacterSlider * 17 CharacterSlider * 0 0 94 0 0 0 0 0 0 0 0 0 0

131 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 132 0 0 0 0 0 0 0 0 0 0

132 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

133 23 CharacterVertexSlider * 0 8576 23 CharacterVertexSlider * 23 CharacterVertexSlider * 0 0 96 0 0 0 0 0 0 0 0 0 0

134 21 CharacterSlider const 0 8832 21 CharacterSlider const 21 CharacterSlider const 0 0 94 0 0 0 0 0 0 0 0 0 0

135 29 CharacterVertexSlider const * 0 8576 29 CharacterVertexSlider const * 29 CharacterVertexSlider const * 0 0 136 0 0 0 0 0 0 0 0 0 0

136 27 CharacterVertexSlider const 0 8832 27 CharacterVertexSlider const 27 CharacterVertexSlider const 0 0 96 0 0 0 0 0 0 0 0 0 0

137 23 CharacterSlider const * 0 8576 23 CharacterSlider const * 23 CharacterSlider const * 0 0 134 0 0 0 0 0 0 0 0 0 0

138 22 JointVertexTransform * 0 8576 22 JointVertexTransform * 22 JointVertexTransform * 0 0 98 0 0 0 0 0 0 0 0 0 0

139 28 JointVertexTransform const * 0 8576 28 JointVertexTransform const * 28 JointVertexTransform const * 0 0 140 0 0 0 0 0 0 0 0 0 0

140 26 JointVertexTransform const 0 8832 26 JointVertexTransform const 26 JointVertexTransform const 0 0 98 0 0 0 0 0 0 0 0 0 0

141 17 Character const * 0 8576 17 Character const * 17 Character const * 0 0 142 0 0 0 0 0 0 0 0 0 0

142 15 Character const 0 8832 15 Character const 15 Character const 0 0 100 0 0 0 0 0 0 0 0 0 0

143 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

144 22 CharacterJointBundle * 0 8576 22 CharacterJointBundle * 22 CharacterJointBundle * 0 0 102 0 0 0 0 0 0 0 0 0 0

145 18 PartBundleHandle * 0 8576 18 PartBundleHandle * 18 PartBundleHandle * 0 0 146 0 0 0 0 0 0 0 0 0 0

146 16 PartBundleHandle 0 2048 16 PartBundleHandle 16 PartBundleHandle 0 0 0 0 0 0 0 0 0 0 0 0 546
/**
 * This is a trivial class returned by PartBundleNode::get_bundle().  Its
 * purpose is to hold the actual PartBundle pointer contained within the
 * PartBundleNode, so that scene graph flatten operations can safely combine
 * or duplicate PartBundles as necessary without affecting high-level bundle
 * operations.
 *
 * The high-level Actor class defined in direct/src/actor, for instance, will
 * store a list of PartBundleHandles instead of on actual PartBundles, so that
 * it will be immune to changes from these flatten operations.
 */

147 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 148 0 0 0 0 0 0 0 0 0 0

148 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 149 0 0 0 0 0 0 0 0 0 0

149 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 150 0 0 0 0 0 0 0 0 0 0

150 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

151 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 152 0 0 0 0 0 0 0 0 0 0

152 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

153 28 CharacterJointBundle const * 0 8576 28 CharacterJointBundle const * 28 CharacterJointBundle const * 0 0 154 0 0 0 0 0 0 0 0 0 0

154 26 CharacterJointBundle const 0 8832 26 CharacterJointBundle const 26 CharacterJointBundle const 0 0 102 0 0 0 0 0 0 0 0 0 0

155 18 RenderEffect const 0 8832 18 RenderEffect const 18 RenderEffect const 0 0 105 0 0 0 0 0 0 0 0 0 0

156 20 RenderEffect const * 0 8576 20 RenderEffect const * 20 RenderEffect const * 0 0 155 0 0 0 0 0 0 0 0 0 0

157 28 CharacterJointEffect const * 0 8576 28 CharacterJointEffect const * 28 CharacterJointEffect const * 0 0 158 0 0 0 0 0 0 0 0 0 0

158 26 CharacterJointEffect const 0 8832 26 CharacterJointEffect const 26 CharacterJointEffect const 0 0 104 0 0 0 0 0 0 0 0 0 0

0
0
0
