{"license": "Modified BSD License", "name": "Panda3D", "metadata_version": "2.1", "generator": "<PERSON><PERSON><PERSON>", "summary": "Panda3D is a framework for 3D rendering and game development for Python and C++ programs.", "extensions": {"python.details": {"project_urls": {"Download": "https://www.panda3d.org/download/", "Source": "https://github.com/panda3d/panda3d", "Tracker": "https://github.com/panda3d/panda3d/issues", "Documentation": "https://www.panda3d.org/manual/", "Funding": "https://opencollective.com/panda3d", "Home": "https://www.panda3d.org/"}, "document_names": {"license": "LICENSE.txt"}, "contacts": [{"role": "author", "name": "Panda3D Team", "email": "<EMAIL>"}]}}, "classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "Intended Audience :: End Users/Desktop", "License :: OSI Approved :: BSD License", "Operating System :: OS Independent", "Programming Language :: C++", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7", "Programming Language :: Python :: 3.8", "Programming Language :: Python :: 3.9", "Programming Language :: Python :: 3.10", "Programming Language :: Python :: 3.11", "Programming Language :: Python :: 3.12", "Programming Language :: Python :: 3.13", "Programming Language :: Python :: Implementation :: CPython", "Topic :: Games/Entertainment", "Topic :: Multimedia", "Topic :: Multimedia :: Graphics", "Topic :: Multimedia :: Graphics :: 3D Rendering", "Topic :: Software Development :: Libraries", "Topic :: Software Development :: Libraries :: Application Frameworks", "Topic :: Software Development :: Libraries :: Python Modules"], "version": "1.10.15"}