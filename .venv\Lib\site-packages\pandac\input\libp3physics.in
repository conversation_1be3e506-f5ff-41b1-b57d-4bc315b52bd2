1730992849
3 3
12 libp3physics 4 1Scs 15 panda3d.physics 
219
242 13 PhysicsObject 0 260 461 28 PhysicsObject::PhysicsObject 0 2 1 2 59
/**
 * Default Constructor
 */

/**
 * copy constructor
 */
92
PhysicsObject::PhysicsObject(void);
PhysicsObject::PhysicsObject(PhysicsObject const &copy);

243 10 operator = 0 4 461 25 PhysicsObject::operator = 0 1 3 10
/**
 *
 */
59
void PhysicsObject::operator =(PhysicsObject const &other);

244 10 set_active 0 4 461 25 PhysicsObject::set_active 0 1 4 34
/**
 * Process Flag assignment
 */
49
inline void PhysicsObject::set_active(bool flag);

245 10 get_active 0 4 461 25 PhysicsObject::get_active 0 1 5 29
/**
 * Process Flag Query
 */
50
inline bool PhysicsObject::get_active(void) const;

246 8 set_mass 0 4 461 23 PhysicsObject::set_mass 0 1 6 48
/**
 * Set the mass in slugs (or kilograms).
 */
50
inline void PhysicsObject::set_mass(PN_stdfloat );

247 8 get_mass 0 4 461 23 PhysicsObject::get_mass 0 1 7 48
/**
 * Get the mass in slugs (or kilograms).
 */
55
inline PN_stdfloat PhysicsObject::get_mass(void) const;

248 12 set_position 0 4 461 27 PhysicsObject::set_position 0 2 8 9 270
// INLINE void set_center_of_mass(const LPoint3 &pos); use set_position.

// INLINE void set_center_of_mass(const LPoint3 &pos); use set_position.

/**
 * Vector position assignment.  This is also used as the center of mass.
 */

/**
 * Piecewise position assignment
 */
146
inline void PhysicsObject::set_position(LPoint3 const &pos);
inline void PhysicsObject::set_position(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

249 12 get_position 0 4 461 27 PhysicsObject::get_position 0 1 10 25
/**
 * Position Query
 */
55
inline LPoint3 PhysicsObject::get_position(void) const;

250 14 reset_position 0 4 461 29 PhysicsObject::reset_position 0 1 11 118
/**
 * use this to place an object in a completely new position, that has nothing
 * to do with its last position.
 */
62
inline void PhysicsObject::reset_position(LPoint3 const &pos);

251 17 set_last_position 0 4 461 32 PhysicsObject::set_last_position 0 1 12 35
/**
 * Last position assignment
 */
65
inline void PhysicsObject::set_last_position(LPoint3 const &pos);

252 17 get_last_position 0 4 461 32 PhysicsObject::get_last_position 0 1 13 96
/**
 * Get the position of the physics object at the start of the most recent
 * do_physics.
 */
60
inline LPoint3 PhysicsObject::get_last_position(void) const;

253 12 set_velocity 0 4 461 27 PhysicsObject::set_velocity 0 2 14 15 79
/**
 * Vector velocity assignment
 */

/**
 * Piecewise velocity assignment
 */
147
inline void PhysicsObject::set_velocity(LVector3 const &vel);
inline void PhysicsObject::set_velocity(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

254 12 get_velocity 0 4 461 27 PhysicsObject::get_velocity 0 1 16 36
/**
 * Velocity Query per second
 */
56
inline LVector3 PhysicsObject::get_velocity(void) const;

255 21 get_implicit_velocity 0 4 461 36 PhysicsObject::get_implicit_velocity 0 1 17 42
/**
 * Velocity Query over the last dt
 */
65
inline LVector3 PhysicsObject::get_implicit_velocity(void) const;

256 10 add_torque 0 4 461 25 PhysicsObject::add_torque 0 1 18 236
// Global instantanious forces

/**
 * Adds an torque force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the angular velocity, add a vector to it and set that
 * value to be the new angular velocity.
 */
63
inline void PhysicsObject::add_torque(LRotation const &torque);

257 11 add_impulse 0 4 461 26 PhysicsObject::add_impulse 0 1 19 189
/**
 * Adds an impulse force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the velocity, add a vector to it and set that value to
 * be the new velocity.
 */
64
inline void PhysicsObject::add_impulse(LVector3 const &impulse);

258 10 add_impact 0 6 461 25 PhysicsObject::add_impact 0 1 20 371
/**
 * Adds an impulse and/or torque (i.e.  an instantanious change in velocity)
 * based on how well the offset and impulse align with the center of mass (aka
 * position). If you wanted to immitate this function you could work out the
 * impulse and torque and call add_impulse and add_torque respectively.
 * offset and force are in global (or parent) coordinates.
 */
107
virtual void PhysicsObject::add_impact(LPoint3 const &offset_from_center_of_mass, LVector3 const &impulse);

259 16 add_local_torque 0 4 461 31 PhysicsObject::add_local_torque 0 1 21 235
// Local instantanious forces

/**
 * Adds an torque force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the angular velocity, add a vector to it and set that
 * value to be the new angular velocity.
 */
69
inline void PhysicsObject::add_local_torque(LRotation const &torque);

260 17 add_local_impulse 0 4 461 32 PhysicsObject::add_local_impulse 0 1 22 189
/**
 * Adds an impulse force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the velocity, add a vector to it and set that value to
 * be the new velocity.
 */
70
inline void PhysicsObject::add_local_impulse(LVector3 const &impulse);

261 16 add_local_impact 0 6 461 31 PhysicsObject::add_local_impact 0 1 23 358
/**
 * Adds an impulse and/or torque (i.e.  an instantanious change in velocity)
 * based on how well the offset and impulse align with the center of mass (aka
 * position). If you wanted to immitate this function you could work out the
 * impulse and torque and call add_impulse and add_torque respectively.
 * offset and force are in local coordinates.
 */
113
virtual void PhysicsObject::add_local_impact(LPoint3 const &offset_from_center_of_mass, LVector3 const &impulse);

262 21 set_terminal_velocity 0 4 461 36 PhysicsObject::set_terminal_velocity 0 1 24 24
/**
 * tv assignment
 */
65
inline void PhysicsObject::set_terminal_velocity(PN_stdfloat tv);

263 21 get_terminal_velocity 0 4 461 36 PhysicsObject::get_terminal_velocity 0 1 25 19
/**
 * tv query
 */
68
inline PN_stdfloat PhysicsObject::get_terminal_velocity(void) const;

264 12 set_oriented 0 4 461 27 PhysicsObject::set_oriented 0 1 26 122
/**
 * Set flag to determine whether this object should do any rotation or
 * orientation calculations.  Optimization.
 */
51
inline void PhysicsObject::set_oriented(bool flag);

265 12 get_oriented 0 4 461 27 PhysicsObject::get_oriented 0 1 27 30
/**
 * See set_oriented().
 */
52
inline bool PhysicsObject::get_oriented(void) const;

266 15 set_orientation 0 4 461 30 PhysicsObject::set_orientation 0 1 28 10
/**
 *
 */
76
inline void PhysicsObject::set_orientation(LOrientation const &orientation);

267 15 get_orientation 0 4 461 30 PhysicsObject::get_orientation 0 1 29 35
/**
 * get current orientation.
 */
63
inline LOrientation PhysicsObject::get_orientation(void) const;

268 17 reset_orientation 0 4 461 32 PhysicsObject::reset_orientation 0 1 30 68
/**
 * set the orientation while clearing the rotation velocity.
 */
78
inline void PhysicsObject::reset_orientation(LOrientation const &orientation);

269 12 set_rotation 0 4 461 27 PhysicsObject::set_rotation 0 1 31 57
/**
 * set rotation as a quaternion delta per second.
 */
67
inline void PhysicsObject::set_rotation(LRotation const &rotation);

270 12 get_rotation 0 4 461 27 PhysicsObject::get_rotation 0 1 32 35
/**
 * get rotation per second.
 */
57
inline LRotation PhysicsObject::get_rotation(void) const;

271 19 get_inertial_tensor 0 6 461 34 PhysicsObject::get_inertial_tensor 0 1 33 95
/**
 * returns a transform matrix that represents the object's willingness to be
 * forced.
 */
64
virtual LMatrix4 PhysicsObject::get_inertial_tensor(void) const;

272 7 get_lcs 0 6 461 22 PhysicsObject::get_lcs 0 1 34 79
/**
 * returns a transform matrix to this object's local coordinate system.
 */
52
virtual LMatrix4 PhysicsObject::get_lcs(void) const;

273 9 make_copy 0 6 461 24 PhysicsObject::make_copy 0 1 35 24
/**
 * dynamic copy.
 */
60
virtual PhysicsObject *PhysicsObject::make_copy(void) const;

274 8 set_name 0 4 461 23 PhysicsObject::set_name 0 1 36 0
54
void PhysicsObject::set_name(std::string const &name);

275 8 get_name 0 4 461 23 PhysicsObject::get_name 0 1 37 0
49
std::string const &PhysicsObject::get_name(void);

276 6 output 0 6 461 21 PhysicsObject::output 0 1 38 67
/**
 * Write a string representation of this instance to <out>.
 */
60
virtual void PhysicsObject::output(std::ostream &out) const;

277 5 write 0 6 461 20 PhysicsObject::write 0 1 39 67
/**
 * Write a string representation of this instance to <out>.
 */
75
virtual void PhysicsObject::write(std::ostream &out, int indent = 0) const;

278 14 get_class_type 0 4 461 29 PhysicsObject::get_class_type 0 1 40 0
54
static TypeHandle PhysicsObject::get_class_type(void);

279 23 PhysicsObjectCollection 0 260 474 48 PhysicsObjectCollection::PhysicsObjectCollection 0 2 41 42 22
/**
 *
 */

/**
 *
 */
142
PhysicsObjectCollection::PhysicsObjectCollection(void);
PhysicsObjectCollection::PhysicsObjectCollection(PhysicsObjectCollection const &copy);

280 10 operator = 0 4 474 35 PhysicsObjectCollection::operator = 0 1 43 0
78
void PhysicsObjectCollection::operator =(PhysicsObjectCollection const &copy);

281 24 ~PhysicsObjectCollection 0 516 474 49 PhysicsObjectCollection::~PhysicsObjectCollection 0 0 10
/**
 *
 */
63
inline PhysicsObjectCollection::~PhysicsObjectCollection(void);

282 18 add_physics_object 0 4 474 43 PhysicsObjectCollection::add_physics_object 0 1 44 54
/**
 * Adds a new PhysicsObject to the collection.
 */
92
void PhysicsObjectCollection::add_physics_object(PointerTo< PhysicsObject > physics_object);

283 21 remove_physics_object 0 4 474 46 PhysicsObjectCollection::remove_physics_object 0 1 45 170
/**
 * Removes the indicated PhysicsObject from the collection.  Returns true if
 * the physics_object was removed, false if it was not a member of the
 * collection.
 */
95
bool PhysicsObjectCollection::remove_physics_object(PointerTo< PhysicsObject > physics_object);

284 24 add_physics_objects_from 0 4 474 49 PhysicsObjectCollection::add_physics_objects_from 0 1 46 235
/**
 * Adds all the PhysicsObjects indicated in the other collection to this
 * collection.  The other physics_objects are simply appended to the end of
 * the physics_objects in this list; duplicates are not automatically removed.
 */
93
void PhysicsObjectCollection::add_physics_objects_from(PhysicsObjectCollection const &other);

285 27 remove_physics_objects_from 0 4 474 52 PhysicsObjectCollection::remove_physics_objects_from 0 1 47 100
/**
 * Removes from this collection all of the PhysicsObjects listed in the other
 * collection.
 */
96
void PhysicsObjectCollection::remove_physics_objects_from(PhysicsObjectCollection const &other);

286 32 remove_duplicate_physics_objects 0 4 474 57 PhysicsObjectCollection::remove_duplicate_physics_objects 0 1 48 207
/**
 * Removes any duplicate entries of the same PhysicsObjects on this
 * collection.  If a PhysicsObject appears multiple times, the first
 * appearance is retained; subsequent appearances are removed.
 */
69
void PhysicsObjectCollection::remove_duplicate_physics_objects(void);

287 18 has_physics_object 0 4 474 43 PhysicsObjectCollection::has_physics_object 0 1 49 102
/**
 * Returns true if the indicated PhysicsObject appears in this collection,
 * false otherwise.
 */
98
bool PhysicsObjectCollection::has_physics_object(PointerTo< PhysicsObject > physics_object) const;

288 5 clear 0 4 474 30 PhysicsObjectCollection::clear 0 1 50 58
/**
 * Removes all PhysicsObjects from the collection.
 */
42
void PhysicsObjectCollection::clear(void);

289 8 is_empty 0 4 474 33 PhysicsObjectCollection::is_empty 0 1 51 93
/**
 * Returns true if there are no PhysicsObjects in the collection, false
 * otherwise.
 */
51
bool PhysicsObjectCollection::is_empty(void) const;

290 23 get_num_physics_objects 0 4 474 48 PhysicsObjectCollection::get_num_physics_objects 0 1 52 66
/**
 * Returns the number of PhysicsObjects in the collection.
 */
65
int PhysicsObjectCollection::get_num_physics_objects(void) const;

291 18 get_physics_object 0 4 474 43 PhysicsObjectCollection::get_physics_object 0 1 53 59
/**
 * Returns the nth PhysicsObject in the collection.
 */
88
PointerTo< PhysicsObject > PhysicsObjectCollection::get_physics_object(int index) const;

292 11 operator [] 0 4 474 36 PhysicsObjectCollection::operator [] 0 1 54 0
81
PointerTo< PhysicsObject > PhysicsObjectCollection::operator [](int index) const;

293 4 size 0 4 474 29 PhysicsObjectCollection::size 0 1 55 124
/**
 * Returns the number of physics objects in the collection.  This is the same
 * thing as get_num_physics_objects().
 */
46
int PhysicsObjectCollection::size(void) const;

294 11 operator += 0 4 474 36 PhysicsObjectCollection::operator += 0 1 56 0
87
inline void PhysicsObjectCollection::operator +=(PhysicsObjectCollection const &other);

295 10 operator + 0 4 474 35 PhysicsObjectCollection::operator + 0 1 57 0
111
inline PhysicsObjectCollection PhysicsObjectCollection::operator +(PhysicsObjectCollection const &other) const;

296 6 output 0 4 474 31 PhysicsObjectCollection::output 0 1 58 112
/**
 * Writes a brief one-line description of the PhysicsObjectCollection to the
 * indicated output stream.
 */
62
void PhysicsObjectCollection::output(std::ostream &out) const;

297 5 write 0 4 474 30 PhysicsObjectCollection::write 0 1 59 117
/**
 * Writes a complete multi-line description of the PhysicsObjectCollection to
 * the indicated output stream.
 */
83
void PhysicsObjectCollection::write(std::ostream &out, int indent_level = 0) const;

298 10 get_active 0 4 475 21 BaseForce::get_active 0 1 60 8
/**

 */
46
inline bool BaseForce::get_active(void) const;

299 10 set_active 0 4 475 21 BaseForce::set_active 0 1 61 8
/**

 */
47
inline void BaseForce::set_active(bool active);

300 9 is_linear 0 6 475 20 BaseForce::is_linear 0 1 62 0
50
virtual bool BaseForce::is_linear(void) const = 0;

301 14 get_force_node 0 4 475 25 BaseForce::get_force_node 0 1 63 8
/**

 */
56
inline ForceNode *BaseForce::get_force_node(void) const;

302 19 get_force_node_path 0 4 475 30 BaseForce::get_force_node_path 0 1 64 8
/**

 */
59
inline NodePath BaseForce::get_force_node_path(void) const;

303 6 output 0 6 475 17 BaseForce::output 0 1 65 67
/**
 * Write a string representation of this instance to <out>.
 */
56
virtual void BaseForce::output(std::ostream &out) const;

304 5 write 0 6 475 16 BaseForce::write 0 1 66 67
/**
 * Write a string representation of this instance to <out>.
 */
77
virtual void BaseForce::write(std::ostream &out, int indent_level = 0) const;

305 14 get_class_type 0 4 475 25 BaseForce::get_class_type 0 1 67 0
50
static TypeHandle BaseForce::get_class_type(void);

306 13 set_amplitude 0 4 476 26 LinearForce::set_amplitude 0 1 68 8
/**

 */
60
inline void LinearForce::set_amplitude(PN_stdfloat const a);

307 18 set_mass_dependent 0 4 476 31 LinearForce::set_mass_dependent 0 1 69 8
/**

 */
52
inline void LinearForce::set_mass_dependent(bool m);

308 13 get_amplitude 0 4 476 26 LinearForce::get_amplitude 0 1 70 8
/**

 */
58
inline PN_stdfloat LinearForce::get_amplitude(void) const;

309 18 get_mass_dependent 0 4 476 31 LinearForce::get_mass_dependent 0 1 71 8
/**

 */
56
inline bool LinearForce::get_mass_dependent(void) const;

310 16 set_vector_masks 0 4 476 29 LinearForce::set_vector_masks 0 1 72 8
/**

 */
66
inline void LinearForce::set_vector_masks(bool x, bool y, bool z);

311 16 get_vector_masks 0 4 476 29 LinearForce::get_vector_masks 0 1 73 8
/**

 */
52
inline LVector3 LinearForce::get_vector_masks(void);

312 10 get_vector 0 4 476 23 LinearForce::get_vector 0 1 74 8
/**

 */
58
LVector3 LinearForce::get_vector(PhysicsObject const *po);

313 9 make_copy 0 6 476 22 LinearForce::make_copy 0 1 75 0
54
virtual LinearForce *LinearForce::make_copy(void) = 0;

314 14 get_class_type 0 4 476 27 LinearForce::get_class_type 0 1 76 0
52
static TypeHandle LinearForce::get_class_type(void);

315 9 make_copy 0 6 477 23 AngularForce::make_copy 0 1 77 0
62
virtual AngularForce *AngularForce::make_copy(void) const = 0;

316 8 get_quat 0 4 477 22 AngularForce::get_quat 0 1 78 23
/**
 * access query
 */
58
LRotation AngularForce::get_quat(PhysicsObject const *po);

317 14 get_class_type 0 4 477 28 AngularForce::get_class_type 0 1 79 0
53
static TypeHandle AngularForce::get_class_type(void);

318 8 Physical 0 260 478 18 Physical::Physical 0 2 80 81 643
/**
 * Default Constructor The idea here is that most physicals will NOT be
 * collections of sets (i.e.  particle systems and whatever else).  Because of
 * this, the default constructor, unless otherwise specified, will
 * automatically allocate and initialize one PhysicalObject.  This makes it
 * easier for high-level work.
 *
 * pre-alloc is ONLY for multiple-object physicals, and if true, fills the
 * physics_object vector with dead nodes, pre-allocating for the speed end of
 * the speed-vs-overhead deal.
 */

/**
 * copy constructor (note- does deep copy of pn's) but does NOT attach itself
 * to its template's physicsmanager.
 */
117
explicit Physical::Physical(int total_objects = 1, bool pre_alloc = false);
Physical::Physical(Physical const &copy);

319 19 get_physics_manager 0 4 478 29 Physical::get_physics_manager 0 1 82 20
// helpers

/**

 */
65
inline PhysicsManager *Physical::get_physics_manager(void) const;

320 17 get_physical_node 0 4 478 27 Physical::get_physical_node 0 1 83 8
/**

 */
61
inline PhysicalNode *Physical::get_physical_node(void) const;

321 22 get_physical_node_path 0 4 478 32 Physical::get_physical_node_path 0 1 84 8
/**

 */
61
inline NodePath Physical::get_physical_node_path(void) const;

322 13 get_phys_body 0 4 478 23 Physical::get_phys_body 0 1 85 8
/**

 */
58
inline PhysicsObject *Physical::get_phys_body(void) const;

323 19 clear_linear_forces 0 4 478 29 Physical::clear_linear_forces 0 1 86 39
/**
 * Erases the linear force list
 */
48
inline void Physical::clear_linear_forces(void);

324 20 clear_angular_forces 0 4 478 30 Physical::clear_angular_forces 0 1 87 40
/**
 * Erases the angular force list
 */
49
inline void Physical::clear_angular_forces(void);

325 21 clear_physics_objects 0 4 478 31 Physical::clear_physics_objects 0 1 88 33
/**
 * Erases the object list
 */
50
inline void Physical::clear_physics_objects(void);

326 16 add_linear_force 0 4 478 26 Physical::add_linear_force 0 1 89 48
/**
 * Adds a linear force to the force list
 */
55
inline void Physical::add_linear_force(LinearForce *f);

327 17 add_angular_force 0 4 478 27 Physical::add_angular_force 0 1 90 50
/**
 * Adds an angular force to the force list
 */
57
inline void Physical::add_angular_force(AngularForce *f);

328 18 add_physics_object 0 4 478 28 Physical::add_physics_object 0 1 91 54
/**
 * Adds an object to the physics object vector
 */
60
inline void Physical::add_physics_object(PhysicsObject *po);

329 19 remove_linear_force 0 4 478 29 Physical::remove_linear_force 0 1 92 53
/**
 * removes a linear force from the force list
 */
58
inline void Physical::remove_linear_force(LinearForce *f);

330 20 remove_angular_force 0 4 478 30 Physical::remove_angular_force 0 1 93 55
/**
 * removes an angular force from the force list
 */
60
inline void Physical::remove_angular_force(AngularForce *f);

331 21 get_num_linear_forces 0 4 478 31 Physical::get_num_linear_forces 0 1 94 8
/**

 */
55
inline int Physical::get_num_linear_forces(void) const;

332 16 get_linear_force 0 4 478 26 Physical::get_linear_force 0 1 95 8
/**

 */
76
inline PointerTo< LinearForce > Physical::get_linear_force(int index) const;

333 22 get_num_angular_forces 0 4 478 32 Physical::get_num_angular_forces 0 1 96 8
/**

 */
56
inline int Physical::get_num_angular_forces(void) const;

334 17 get_angular_force 0 4 478 27 Physical::get_angular_force 0 1 97 8
/**

 */
78
inline PointerTo< AngularForce > Physical::get_angular_force(int index) const;

335 13 set_viscosity 0 4 478 23 Physical::set_viscosity 0 1 98 35
/**
 * Set the local viscosity.
 */
59
inline void Physical::set_viscosity(PN_stdfloat viscosity);

336 13 get_viscosity 0 4 478 23 Physical::get_viscosity 0 1 99 35
/**
 * Get the local viscosity.
 */
55
inline PN_stdfloat Physical::get_viscosity(void) const;

337 11 get_objects 0 4 478 21 Physical::get_objects 0 1 100 8
/**

 */
64
PhysicsObjectCollection const Physical::get_objects(void) const;

338 6 output 0 6 478 16 Physical::output 0 1 101 67
/**
 * Write a string representation of this instance to <out>.
 */
69
virtual void Physical::output(std::ostream &out = ::std::cout) const;

339 21 write_physics_objects 0 6 478 31 Physical::write_physics_objects 0 1 102 67
/**
 * Write a string representation of this instance to <out>.
 */
100
virtual void Physical::write_physics_objects(std::ostream &out = ::std::cout, int indent = 0) const;

340 19 write_linear_forces 0 6 478 29 Physical::write_linear_forces 0 1 103 67
/**
 * Write a string representation of this instance to <out>.
 */
98
virtual void Physical::write_linear_forces(std::ostream &out = ::std::cout, int indent = 0) const;

341 20 write_angular_forces 0 6 478 30 Physical::write_angular_forces 0 1 104 67
/**
 * Write a string representation of this instance to <out>.
 */
99
virtual void Physical::write_angular_forces(std::ostream &out = ::std::cout, int indent = 0) const;

342 5 write 0 6 478 15 Physical::write 0 1 105 67
/**
 * Write a string representation of this instance to <out>.
 */
84
virtual void Physical::write(std::ostream &out = ::std::cout, int indent = 0) const;

343 14 get_class_type 0 4 478 24 Physical::get_class_type 0 1 106 0
49
static TypeHandle Physical::get_class_type(void);

344 12 PhysicalNode 0 260 481 26 PhysicalNode::PhysicalNode 0 1 107 59
/**
 * default constructor
 */

/**
 * copy constructor
 */
61
explicit PhysicalNode::PhysicalNode(std::string const &name);

345 5 clear 0 4 481 19 PhysicalNode::clear 0 1 108 8
/**

 */
38
inline void PhysicalNode::clear(void);

346 12 get_physical 0 4 481 26 PhysicalNode::get_physical 0 1 109 8
/**

 */
69
inline Physical *PhysicalNode::get_physical(std::size_t index) const;

347 17 get_num_physicals 0 4 481 31 PhysicalNode::get_num_physicals 0 1 110 8
/**

 */
63
inline std::size_t PhysicalNode::get_num_physicals(void) const;

348 12 add_physical 0 4 481 26 PhysicalNode::add_physical 0 1 111 164
/**
 * Adds a Physical to this PhysicalNode.  If it is already added to this node,
 * does nothing.  It is an error to add a Physical to multiple PhysicalNodes.
 */
59
inline void PhysicalNode::add_physical(Physical *physical);

349 18 add_physicals_from 0 4 481 32 PhysicalNode::add_physicals_from 0 1 112 27
/**
 * append operation
 */
65
void PhysicalNode::add_physicals_from(PhysicalNode const &other);

350 12 set_physical 0 4 481 26 PhysicalNode::set_physical 0 1 113 28
/**
 * replace operation
 */
71
void PhysicalNode::set_physical(std::size_t index, Physical *physical);

351 15 insert_physical 0 4 481 29 PhysicalNode::insert_physical 0 1 114 27
/**
 * insert operation
 */
74
void PhysicalNode::insert_physical(std::size_t index, Physical *physical);

352 15 remove_physical 0 4 481 29 PhysicalNode::remove_physical 0 2 115 116 56
/**
 * remove operation
 */

/**
 * remove operation
 */
110
void PhysicalNode::remove_physical(Physical *physical);
void PhysicalNode::remove_physical(std::size_t index);

353 14 get_class_type 0 4 481 28 PhysicalNode::get_class_type 0 1 117 0
53
static TypeHandle PhysicalNode::get_class_type(void);

354 9 ActorNode 0 260 484 20 ActorNode::ActorNode 0 2 118 119 52
/**
 * Constructor
 */

/**
 * Copy Constructor.
 */
105
explicit ActorNode::ActorNode(std::string const &name = "");
ActorNode::ActorNode(ActorNode const &copy);

355 18 get_physics_object 0 4 484 29 ActorNode::get_physics_object 0 1 120 0
51
PhysicsObject *ActorNode::get_physics_object(void);

356 18 set_contact_vector 0 4 484 29 ActorNode::set_contact_vector 0 1 121 10
/**
 *
 */
67
void ActorNode::set_contact_vector(LVector3 const &contact_vector);

357 18 get_contact_vector 0 4 484 29 ActorNode::get_contact_vector 0 1 122 10
/**
 *
 */
58
LVector3 const &ActorNode::get_contact_vector(void) const;

358 16 update_transform 0 4 484 27 ActorNode::update_transform 0 1 123 274
// update the parent scene graph node with PhysicsObject information i.e.
// copy from PhysicsObject to PandaNode

/**
 * this sets the transform generated by the contained Physical, moving the
 * node and subsequent geometry.  i.e.  copy from PhysicsObject to PandaNode
 */
39
void ActorNode::update_transform(void);

359 19 set_transform_limit 0 4 484 30 ActorNode::set_transform_limit 0 1 124 0
55
void ActorNode::set_transform_limit(PN_stdfloat limit);

360 14 get_class_type 0 4 484 25 ActorNode::get_class_type 0 1 125 0
50
static TypeHandle ActorNode::get_class_type(void);

361 6 output 0 6 485 22 BaseIntegrator::output 0 1 127 67
/**
 * Write a string representation of this instance to <out>.
 */
61
virtual void BaseIntegrator::output(std::ostream &out) const;

362 33 write_precomputed_linear_matrices 0 6 485 49 BaseIntegrator::write_precomputed_linear_matrices 0 1 128 67
/**
 * Write a string representation of this instance to <out>.
 */
104
virtual void BaseIntegrator::write_precomputed_linear_matrices(std::ostream &out, int indent = 0) const;

363 34 write_precomputed_angular_matrices 0 6 485 50 BaseIntegrator::write_precomputed_angular_matrices 0 1 129 67
/**
 * Write a string representation of this instance to <out>.
 */
105
virtual void BaseIntegrator::write_precomputed_angular_matrices(std::ostream &out, int indent = 0) const;

364 5 write 0 6 485 21 BaseIntegrator::write 0 1 130 67
/**
 * Write a string representation of this instance to <out>.
 */
76
virtual void BaseIntegrator::write(std::ostream &out, int indent = 0) const;

365 14 BaseIntegrator 0 260 485 30 BaseIntegrator::BaseIntegrator 0 1 126 22
/**
 * constructor
 */
72
inline BaseIntegrator::BaseIntegrator(BaseIntegrator const &) = default;

366 22 AngularEulerIntegrator 0 260 488 46 AngularEulerIntegrator::AngularEulerIntegrator 0 1 131 22
/**
 * constructor
 */
53
AngularEulerIntegrator::AngularEulerIntegrator(void);

367 18 AngularVectorForce 0 260 489 38 AngularVectorForce::AngularVectorForce 0 3 132 133 134 75
/**
 * constructor
 */

/**
 * constructor
 */

/**
 * copy constructor
 */
237
explicit AngularVectorForce::AngularVectorForce(LRotation const &quat);
explicit AngularVectorForce::AngularVectorForce(PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);
AngularVectorForce::AngularVectorForce(AngularVectorForce const &copy);

368 8 set_quat 0 4 489 28 AngularVectorForce::set_quat 0 1 135 8
/**

 */
64
inline void AngularVectorForce::set_quat(LRotation const &quat);

369 7 set_hpr 0 4 489 27 AngularVectorForce::set_hpr 0 1 136 8
/**

 */
85
inline void AngularVectorForce::set_hpr(PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);

370 14 get_local_quat 0 4 489 34 AngularVectorForce::get_local_quat 0 1 137 8
/**

 */
64
inline LRotation AngularVectorForce::get_local_quat(void) const;

371 14 get_class_type 0 4 489 34 AngularVectorForce::get_class_type 0 1 138 0
59
static TypeHandle AngularVectorForce::get_class_type(void);

372 9 ForceNode 0 260 490 20 ForceNode::ForceNode 0 1 139 59
/**
 * default constructor
 */

/**
 * copy constructor
 */
55
explicit ForceNode::ForceNode(std::string const &name);

373 5 clear 0 4 490 16 ForceNode::clear 0 1 140 8
/**

 */
35
inline void ForceNode::clear(void);

374 9 get_force 0 4 490 20 ForceNode::get_force 0 1 141 8
/**

 */
64
inline BaseForce *ForceNode::get_force(std::size_t index) const;

375 14 get_num_forces 0 4 490 25 ForceNode::get_num_forces 0 1 142 8
/**

 */
57
inline std::size_t ForceNode::get_num_forces(void) const;

376 9 add_force 0 4 490 20 ForceNode::add_force 0 1 143 8
/**

 */
51
inline void ForceNode::add_force(BaseForce *force);

377 15 add_forces_from 0 4 490 26 ForceNode::add_forces_from 0 1 144 27
/**
 * append operation
 */
56
void ForceNode::add_forces_from(ForceNode const &other);

378 9 set_force 0 4 490 20 ForceNode::set_force 0 1 145 28
/**
 * replace operation
 */
63
void ForceNode::set_force(std::size_t index, BaseForce *force);

379 12 insert_force 0 4 490 23 ForceNode::insert_force 0 1 146 27
/**
 * insert operation
 */
66
void ForceNode::insert_force(std::size_t index, BaseForce *force);

380 12 remove_force 0 4 490 23 ForceNode::remove_force 0 2 147 148 56
/**
 * remove operation
 */

/**
 * remove operation
 */
96
void ForceNode::remove_force(BaseForce *force);
void ForceNode::remove_force(std::size_t index);

381 12 write_forces 0 6 490 23 ForceNode::write_forces 0 1 149 67
/**
 * Write a string representation of this instance to <out>.
 */
78
virtual void ForceNode::write_forces(std::ostream &out, int indent = 0) const;

382 14 get_class_type 0 4 490 25 ForceNode::get_class_type 0 1 150 0
50
static TypeHandle ForceNode::get_class_type(void);

383 18 LinearControlForce 0 260 492 38 LinearControlForce::LinearControlForce 0 2 151 152 58
/**
 * Vector Constructor
 */

/**
 * Copy Constructor
 */
189
explicit LinearControlForce::LinearControlForce(PhysicsObject const *po = 0, PN_stdfloat a = 1.0, bool mass = false);
LinearControlForce::LinearControlForce(LinearControlForce const &copy);

384 20 clear_physics_object 0 4 492 40 LinearControlForce::clear_physics_object 0 1 153 32
/**
 * encapsulating wrapper
 */
59
inline void LinearControlForce::clear_physics_object(void);

385 18 set_physics_object 0 4 492 38 LinearControlForce::set_physics_object 0 1 154 32
/**
 * encapsulating wrapper
 */
76
inline void LinearControlForce::set_physics_object(PhysicsObject const *po);

386 18 get_physics_object 0 4 492 38 LinearControlForce::get_physics_object 0 1 155 42
/**
 * piecewise encapsulating wrapper
 */
90
inline ConstPointerTo< PhysicsObject > LinearControlForce::get_physics_object(void) const;

387 10 set_vector 0 4 492 30 LinearControlForce::set_vector 0 2 156 157 76
/**
 * encapsulating wrapper
 */

/**
 * piecewise encapsulating wrapper
 */
151
inline void LinearControlForce::set_vector(LVector3 const &v);
inline void LinearControlForce::set_vector(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

388 16 get_local_vector 0 4 492 36 LinearControlForce::get_local_vector 0 1 158 10
/**
 *
 */
65
inline LVector3 LinearControlForce::get_local_vector(void) const;

389 14 get_class_type 0 4 492 34 LinearControlForce::get_class_type 0 1 159 0
59
static TypeHandle LinearControlForce::get_class_type(void);

390 25 LinearCylinderVortexForce 0 260 493 52 LinearCylinderVortexForce::LinearCylinderVortexForce 0 2 160 161 58
/**
 * Simple Constructor
 */

/**
 * copy Constructor
 */
269
explicit LinearCylinderVortexForce::LinearCylinderVortexForce(PN_stdfloat radius = 1.0, PN_stdfloat length = 0.0, PN_stdfloat coef = 1.0, PN_stdfloat a = 1.0, bool md = false);
LinearCylinderVortexForce::LinearCylinderVortexForce(LinearCylinderVortexForce const &copy);

391 8 set_coef 0 4 493 35 LinearCylinderVortexForce::set_coef 0 1 162 8
/**

 */
66
inline void LinearCylinderVortexForce::set_coef(PN_stdfloat coef);

392 8 get_coef 0 4 493 35 LinearCylinderVortexForce::get_coef 0 1 163 8
/**

 */
67
inline PN_stdfloat LinearCylinderVortexForce::get_coef(void) const;

393 10 set_radius 0 4 493 37 LinearCylinderVortexForce::set_radius 0 1 164 8
/**

 */
70
inline void LinearCylinderVortexForce::set_radius(PN_stdfloat radius);

394 10 get_radius 0 4 493 37 LinearCylinderVortexForce::get_radius 0 1 165 8
/**

 */
69
inline PN_stdfloat LinearCylinderVortexForce::get_radius(void) const;

395 10 set_length 0 4 493 37 LinearCylinderVortexForce::set_length 0 1 166 8
/**

 */
70
inline void LinearCylinderVortexForce::set_length(PN_stdfloat length);

396 10 get_length 0 4 493 37 LinearCylinderVortexForce::get_length 0 1 167 8
/**

 */
69
inline PN_stdfloat LinearCylinderVortexForce::get_length(void) const;

397 14 get_class_type 0 4 493 41 LinearCylinderVortexForce::get_class_type 0 1 168 0
66
static TypeHandle LinearCylinderVortexForce::get_class_type(void);

398 10 set_radius 0 4 494 31 LinearDistanceForce::set_radius 0 1 169 25
/**
 * set the radius
 */
59
inline void LinearDistanceForce::set_radius(PN_stdfloat r);

399 16 set_falloff_type 0 4 494 37 LinearDistanceForce::set_falloff_type 0 1 170 42
/**
 * falloff_type encapsulating wrap
 */
87
inline void LinearDistanceForce::set_falloff_type(LinearDistanceForce::FalloffType ft);

400 16 set_force_center 0 4 494 37 LinearDistanceForce::set_force_center 0 1 171 31
/**
 * set the force center
 */
68
inline void LinearDistanceForce::set_force_center(LPoint3 const &p);

401 10 get_radius 0 4 494 31 LinearDistanceForce::get_radius 0 1 172 23
/**
 * radius query
 */
63
inline PN_stdfloat LinearDistanceForce::get_radius(void) const;

402 16 get_falloff_type 0 4 494 37 LinearDistanceForce::get_falloff_type 0 1 173 29
/**
 * falloff_type query
 */
90
inline LinearDistanceForce::FalloffType LinearDistanceForce::get_falloff_type(void) const;

403 16 get_force_center 0 4 494 37 LinearDistanceForce::get_force_center 0 1 174 29
/**
 * force_center query
 */
65
inline LPoint3 LinearDistanceForce::get_force_center(void) const;

404 15 get_scalar_term 0 4 494 36 LinearDistanceForce::get_scalar_term 0 1 175 46
/**
 * calculate the term based on falloff
 */
68
inline PN_stdfloat LinearDistanceForce::get_scalar_term(void) const;

405 14 get_class_type 0 4 494 35 LinearDistanceForce::get_class_type 0 1 176 0
60
static TypeHandle LinearDistanceForce::get_class_type(void);

406 21 LinearEulerIntegrator 0 260 497 44 LinearEulerIntegrator::LinearEulerIntegrator 0 1 177 22
/**
 * constructor
 */
51
LinearEulerIntegrator::LinearEulerIntegrator(void);

407 19 LinearFrictionForce 0 260 498 40 LinearFrictionForce::LinearFrictionForce 0 2 178 179 51
/**
 * Constructor
 */

/**
 * copy constructor
 */
186
explicit LinearFrictionForce::LinearFrictionForce(PN_stdfloat coef = 1.0, PN_stdfloat a = 1.0, bool m = false);
LinearFrictionForce::LinearFrictionForce(LinearFrictionForce const &copy);

408 8 set_coef 0 4 498 29 LinearFrictionForce::set_coef 0 1 180 8
/**

 */
60
inline void LinearFrictionForce::set_coef(PN_stdfloat coef);

409 8 get_coef 0 4 498 29 LinearFrictionForce::get_coef 0 1 181 8
/**

 */
61
inline PN_stdfloat LinearFrictionForce::get_coef(void) const;

410 14 get_class_type 0 4 498 35 LinearFrictionForce::get_class_type 0 1 182 0
60
static TypeHandle LinearFrictionForce::get_class_type(void);

411 14 get_class_type 0 4 499 33 LinearRandomForce::get_class_type 0 1 183 0
58
static TypeHandle LinearRandomForce::get_class_type(void);

412 17 LinearJitterForce 0 260 500 36 LinearJitterForce::LinearJitterForce 0 2 184 185 51
/**
 * constructor
 */

/**
 * copy constructor
 */
152
explicit LinearJitterForce::LinearJitterForce(PN_stdfloat a = 1.0, bool m = false);
LinearJitterForce::LinearJitterForce(LinearJitterForce const &copy);

413 14 get_class_type 0 4 500 33 LinearJitterForce::get_class_type 0 1 186 0
58
static TypeHandle LinearJitterForce::get_class_type(void);

414 16 LinearNoiseForce 0 260 501 34 LinearNoiseForce::LinearNoiseForce 0 2 187 188 51
/**
 * constructor
 */

/**
 * copy constructor
 */
147
explicit LinearNoiseForce::LinearNoiseForce(PN_stdfloat a = 1.0, bool m = false);
LinearNoiseForce::LinearNoiseForce(LinearNoiseForce const &copy);

415 14 get_class_type 0 4 501 32 LinearNoiseForce::get_class_type 0 1 189 0
57
static TypeHandle LinearNoiseForce::get_class_type(void);

416 15 LinearSinkForce 0 260 502 32 LinearSinkForce::LinearSinkForce 0 3 190 191 192 89
/**
 * Simple constructor
 */

/**
 * Simple constructor
 */

/**
 * copy constructor
 */
250
explicit LinearSinkForce::LinearSinkForce(LPoint3 const &p, LinearDistanceForce::FalloffType f, PN_stdfloat r, PN_stdfloat a = 1.0, bool m = true);
LinearSinkForce::LinearSinkForce(void);
LinearSinkForce::LinearSinkForce(LinearSinkForce const &copy);

417 14 get_class_type 0 4 502 31 LinearSinkForce::get_class_type 0 1 193 0
56
static TypeHandle LinearSinkForce::get_class_type(void);

418 17 LinearSourceForce 0 260 503 36 LinearSourceForce::LinearSourceForce 0 3 194 195 196 89
/**
 * Simple constructor
 */

/**
 * Simple constructor
 */

/**
 * copy constructor
 */
267
explicit LinearSourceForce::LinearSourceForce(LPoint3 const &p, LinearDistanceForce::FalloffType f, PN_stdfloat r, PN_stdfloat a = 1.0, bool mass = true);
LinearSourceForce::LinearSourceForce(void);
LinearSourceForce::LinearSourceForce(LinearSourceForce const &copy);

419 14 get_class_type 0 4 503 33 LinearSourceForce::get_class_type 0 1 197 0
58
static TypeHandle LinearSourceForce::get_class_type(void);

420 22 LinearUserDefinedForce 0 260 504 46 LinearUserDefinedForce::LinearUserDefinedForce 0 1 198 51
/**
 * constructor
 */

/**
 * copy constructor
 */
229
explicit LinearUserDefinedForce::LinearUserDefinedForce(LVector3 (*proc)(PhysicsObject const *) = nullptr, PN_stdfloat a = 1.0, bool md = false);
LinearUserDefinedForce::LinearUserDefinedForce(LinearUserDefinedForce const &copy);

421 8 set_proc 0 4 504 32 LinearUserDefinedForce::set_proc 0 0 8
/**

 */
86
inline void LinearUserDefinedForce::set_proc(LVector3 (*proc)(PhysicsObject const *));

422 14 get_class_type 0 4 504 38 LinearUserDefinedForce::get_class_type 0 1 199 0
63
static TypeHandle LinearUserDefinedForce::get_class_type(void);

423 17 LinearVectorForce 0 260 505 36 LinearVectorForce::LinearVectorForce 0 3 200 201 202 100
/**
 * Vector Constructor
 */

/**
 * Default/Piecewise constructor
 */

/**
 * Copy Constructor
 */
326
explicit LinearVectorForce::LinearVectorForce(LVector3 const &vec, PN_stdfloat a = 1.0, bool mass = false);
explicit LinearVectorForce::LinearVectorForce(PN_stdfloat x = 0.0, PN_stdfloat y = 0.0, PN_stdfloat z = 0.0, PN_stdfloat a = 1.0, bool mass = false);
LinearVectorForce::LinearVectorForce(LinearVectorForce const &copy);

424 10 set_vector 0 4 505 29 LinearVectorForce::set_vector 0 2 203 204 76
/**
 * encapsulating wrapper
 */

/**
 * piecewise encapsulating wrapper
 */
149
inline void LinearVectorForce::set_vector(LVector3 const &v);
inline void LinearVectorForce::set_vector(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

425 16 get_local_vector 0 4 505 35 LinearVectorForce::get_local_vector 0 1 205 10
/**
 *
 */
64
inline LVector3 LinearVectorForce::get_local_vector(void) const;

426 14 get_class_type 0 4 505 33 LinearVectorForce::get_class_type 0 1 206 0
58
static TypeHandle LinearVectorForce::get_class_type(void);

427 23 PhysicsCollisionHandler 0 260 506 48 PhysicsCollisionHandler::PhysicsCollisionHandler 0 1 207 10
/**
 *
 */
55
PhysicsCollisionHandler::PhysicsCollisionHandler(void);

428 27 set_almost_stationary_speed 0 4 506 52 PhysicsCollisionHandler::set_almost_stationary_speed 0 1 208 48
// These setters and getter are a bit of a hack:
84
inline void PhysicsCollisionHandler::set_almost_stationary_speed(PN_stdfloat speed);

429 27 get_almost_stationary_speed 0 4 506 52 PhysicsCollisionHandler::get_almost_stationary_speed 0 1 209 0
78
inline PN_stdfloat PhysicsCollisionHandler::get_almost_stationary_speed(void);

430 24 set_static_friction_coef 0 4 506 49 PhysicsCollisionHandler::set_static_friction_coef 0 1 210 0
80
inline void PhysicsCollisionHandler::set_static_friction_coef(PN_stdfloat coef);

431 24 get_static_friction_coef 0 4 506 49 PhysicsCollisionHandler::get_static_friction_coef 0 1 211 0
75
inline PN_stdfloat PhysicsCollisionHandler::get_static_friction_coef(void);

432 25 set_dynamic_friction_coef 0 4 506 50 PhysicsCollisionHandler::set_dynamic_friction_coef 0 1 212 0
81
inline void PhysicsCollisionHandler::set_dynamic_friction_coef(PN_stdfloat coef);

433 25 get_dynamic_friction_coef 0 4 506 50 PhysicsCollisionHandler::get_dynamic_friction_coef 0 1 213 0
76
inline PN_stdfloat PhysicsCollisionHandler::get_dynamic_friction_coef(void);

434 14 get_class_type 0 4 506 39 PhysicsCollisionHandler::get_class_type 0 1 214 0
64
static TypeHandle PhysicsCollisionHandler::get_class_type(void);

435 14 PhysicsManager 0 260 508 30 PhysicsManager::PhysicsManager 0 2 215 216 79
/**
 * Default Constructor.  NOTE: EulerIntegrator is the standard default.
 */
110
PhysicsManager::PhysicsManager(void);
inline PhysicsManager::PhysicsManager(PhysicsManager const &) = default;

436 15 ~PhysicsManager 0 518 508 31 PhysicsManager::~PhysicsManager 0 0 28
/**
 * Simple Destructor
 */
46
virtual PhysicsManager::~PhysicsManager(void);

437 24 attach_linear_integrator 0 4 508 40 PhysicsManager::attach_linear_integrator 0 1 217 53
/**
 * Hooks a linear integrator into the manager
 */
74
inline void PhysicsManager::attach_linear_integrator(LinearIntegrator *i);

438 25 attach_angular_integrator 0 4 508 41 PhysicsManager::attach_angular_integrator 0 1 218 55
/**
 * Hooks an angular integrator into the manager
 */
76
inline void PhysicsManager::attach_angular_integrator(AngularIntegrator *i);

439 15 attach_physical 0 4 508 31 PhysicsManager::attach_physical 0 1 219 54
/**
 * Registers a Physical class with the manager
 */
57
inline void PhysicsManager::attach_physical(Physical *p);

440 19 attach_physicalnode 0 4 508 35 PhysicsManager::attach_physicalnode 0 1 220 90
// use attach_physical_node instead.

/**
 * Please call attach_physical_node instead.
 */
65
inline void PhysicsManager::attach_physicalnode(PhysicalNode *p);

441 20 attach_physical_node 0 4 508 36 PhysicsManager::attach_physical_node 0 1 221 90
// use attach_physical_node instead.

/**
 * Registers a physicalnode with the manager
 */
66
inline void PhysicsManager::attach_physical_node(PhysicalNode *p);

442 16 add_linear_force 0 4 508 32 PhysicsManager::add_linear_force 0 1 222 60
/**
 * Adds a global linear force to the physics manager
 */
61
inline void PhysicsManager::add_linear_force(LinearForce *f);

443 17 add_angular_force 0 4 508 33 PhysicsManager::add_angular_force 0 1 223 61
/**
 * Adds a global angular force to the physics manager
 */
63
inline void PhysicsManager::add_angular_force(AngularForce *f);

444 19 clear_linear_forces 0 4 508 35 PhysicsManager::clear_linear_forces 0 1 224 50
/**
 * Resets the physics manager force vector
 */
54
inline void PhysicsManager::clear_linear_forces(void);

445 20 clear_angular_forces 0 4 508 36 PhysicsManager::clear_angular_forces 0 1 225 50
/**
 * Resets the physics manager force vector
 */
55
inline void PhysicsManager::clear_angular_forces(void);

446 15 clear_physicals 0 4 508 31 PhysicsManager::clear_physicals 0 1 226 52
/**
 * Resets the physics manager objects vector
 */
50
inline void PhysicsManager::clear_physicals(void);

447 13 set_viscosity 0 4 508 29 PhysicsManager::set_viscosity 0 1 227 36
/**
 * Set the global viscosity.
 */
65
inline void PhysicsManager::set_viscosity(PN_stdfloat viscosity);

448 13 get_viscosity 0 4 508 29 PhysicsManager::get_viscosity 0 1 228 36
/**
 * Get the global viscosity.
 */
61
inline PN_stdfloat PhysicsManager::get_viscosity(void) const;

449 15 remove_physical 0 4 508 31 PhysicsManager::remove_physical 0 1 229 50
/**
 * takes a physical out of the object list
 */
50
void PhysicsManager::remove_physical(Physical *p);

450 20 remove_physical_node 0 4 508 36 PhysicsManager::remove_physical_node 0 1 230 50
/**
 * Removes a physicalnode from the manager
 */
59
void PhysicsManager::remove_physical_node(PhysicalNode *p);

451 19 remove_linear_force 0 4 508 35 PhysicsManager::remove_linear_force 0 1 231 55
/**
 * takes a linear force out of the physics list
 */
57
void PhysicsManager::remove_linear_force(LinearForce *f);

452 20 remove_angular_force 0 4 508 36 PhysicsManager::remove_angular_force 0 1 232 57
/**
 * takes an angular force out of the physics list
 */
59
void PhysicsManager::remove_angular_force(AngularForce *f);

453 10 do_physics 0 4 508 26 PhysicsManager::do_physics 0 2 233 234 243
/**
 * This is the main high-level API call.  Performs integration on every
 * attached Physical.
 */

/**
 * This is the main high-level API call.  Performs integration on a single
 * physical.  Make sure its associated forces are active.
 */
110
void PhysicsManager::do_physics(PN_stdfloat dt);
void PhysicsManager::do_physics(PN_stdfloat dt, Physical *p);

454 16 init_random_seed 0 4 508 32 PhysicsManager::init_random_seed 0 1 235 153
/**
 * One-time config function, sets up the random seed used by the physics and
 * particle systems.  For synchronizing across distributed computers
 */
44
void PhysicsManager::init_random_seed(void);

455 6 output 0 6 508 22 PhysicsManager::output 0 1 236 67
/**
 * Write a string representation of this instance to <out>.
 */
61
virtual void PhysicsManager::output(std::ostream &out) const;

456 15 write_physicals 0 6 508 31 PhysicsManager::write_physicals 0 1 237 67
/**
 * Write a string representation of this instance to <out>.
 */
86
virtual void PhysicsManager::write_physicals(std::ostream &out, int indent = 0) const;

457 19 write_linear_forces 0 6 508 35 PhysicsManager::write_linear_forces 0 1 238 67
/**
 * Write a string representation of this instance to <out>.
 */
90
virtual void PhysicsManager::write_linear_forces(std::ostream &out, int indent = 0) const;

458 20 write_angular_forces 0 6 508 36 PhysicsManager::write_angular_forces 0 1 239 67
/**
 * Write a string representation of this instance to <out>.
 */
91
virtual void PhysicsManager::write_angular_forces(std::ostream &out, int indent = 0) const;

459 5 write 0 6 508 21 PhysicsManager::write 0 1 240 67
/**
 * Write a string representation of this instance to <out>.
 */
76
virtual void PhysicsManager::write(std::ostream &out, int indent = 0) const;

460 12 debug_output 0 6 508 28 PhysicsManager::debug_output 0 1 241 67
/**
 * Write a string representation of this instance to <out>.
 */
83
virtual void PhysicsManager::debug_output(std::ostream &out, int indent = 0) const;

241
1 0 0 7 3 509 0 0 30 /**
 * Default Constructor
 */ 0 
2 0 0 15 3 509 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 510  
3 0 0 7 4 509 0 0 10 /**
 *
 */ 2 4 this 3 509  5 other 1 510  
4 0 0 4 5 512 0 0 34 /**
 * Process Flag assignment
 */ 2 4 this 3 509  4 flag 1 463  
5 0 0 6 6 463 0 0 29 /**
 * Process Flag Query
 */ 1 4 this 3 510  
6 0 0 4 7 512 0 0 48 /**
 * Set the mass in slugs (or kilograms).
 */ 2 4 this 3 509  6 param0 0 464  
7 0 0 6 8 464 0 0 48 /**
 * Get the mass in slugs (or kilograms).
 */ 1 4 this 3 510  
8 0 0 4 9 512 0 0 80 /**
 * Vector position assignment.  This is also used as the center of mass.
 */ 2 4 this 3 509  3 pos 1 513  
9 0 0 4 9 512 0 0 40 /**
 * Piecewise position assignment
 */ 4 4 this 3 509  1 x 1 464  1 y 1 464  1 z 1 464  
10 0 0 7 10 515 0 0 25 /**
 * Position Query
 */ 1 4 this 3 510  
11 0 0 4 11 512 0 0 118 /**
 * use this to place an object in a completely new position, that has nothing
 * to do with its last position.
 */ 2 4 this 3 509  3 pos 1 513  
12 0 0 4 12 512 0 0 35 /**
 * Last position assignment
 */ 2 4 this 3 509  3 pos 1 513  
13 0 0 7 13 515 0 0 96 /**
 * Get the position of the physics object at the start of the most recent
 * do_physics.
 */ 1 4 this 3 510  
14 0 0 4 14 512 0 0 37 /**
 * Vector velocity assignment
 */ 2 4 this 3 509  3 vel 1 516  
15 0 0 4 14 512 0 0 40 /**
 * Piecewise velocity assignment
 */ 4 4 this 3 509  1 x 1 464  1 y 1 464  1 z 1 464  
16 0 0 7 15 518 0 0 36 /**
 * Velocity Query per second
 */ 1 4 this 3 510  
17 0 0 7 16 518 0 0 42 /**
 * Velocity Query over the last dt
 */ 1 4 this 3 510  
18 0 0 4 17 512 0 0 204 /**
 * Adds an torque force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the angular velocity, add a vector to it and set that
 * value to be the new angular velocity.
 */ 2 4 this 3 509  6 torque 1 519  
19 0 0 4 18 512 0 0 189 /**
 * Adds an impulse force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the velocity, add a vector to it and set that value to
 * be the new velocity.
 */ 2 4 this 3 509  7 impulse 1 516  
20 0 0 4 19 512 0 0 371 /**
 * Adds an impulse and/or torque (i.e.  an instantanious change in velocity)
 * based on how well the offset and impulse align with the center of mass (aka
 * position). If you wanted to immitate this function you could work out the
 * impulse and torque and call add_impulse and add_torque respectively.
 * offset and force are in global (or parent) coordinates.
 */ 3 4 this 3 509  26 offset_from_center_of_mass 1 513  7 impulse 1 516  
21 0 0 4 20 512 0 0 204 /**
 * Adds an torque force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the angular velocity, add a vector to it and set that
 * value to be the new angular velocity.
 */ 2 4 this 3 509  6 torque 1 519  
22 0 0 4 21 512 0 0 189 /**
 * Adds an impulse force (i.e.  an instantanious change in velocity).  This is
 * a quicker way to get the velocity, add a vector to it and set that value to
 * be the new velocity.
 */ 2 4 this 3 509  7 impulse 1 516  
23 0 0 4 22 512 0 0 358 /**
 * Adds an impulse and/or torque (i.e.  an instantanious change in velocity)
 * based on how well the offset and impulse align with the center of mass (aka
 * position). If you wanted to immitate this function you could work out the
 * impulse and torque and call add_impulse and add_torque respectively.
 * offset and force are in local coordinates.
 */ 3 4 this 3 509  26 offset_from_center_of_mass 1 513  7 impulse 1 516  
24 0 0 4 23 512 0 0 24 /**
 * tv assignment
 */ 2 4 this 3 509  2 tv 1 464  
25 0 0 6 24 464 0 0 19 /**
 * tv query
 */ 1 4 this 3 510  
26 0 0 4 25 512 0 0 122 /**
 * Set flag to determine whether this object should do any rotation or
 * orientation calculations.  Optimization.
 */ 2 4 this 3 509  4 flag 1 463  
27 0 0 6 26 463 0 0 30 /**
 * See set_oriented().
 */ 1 4 this 3 510  
28 0 0 4 27 512 0 0 10 /**
 *
 */ 2 4 this 3 509  11 orientation 1 521  
29 0 0 7 28 523 0 0 35 /**
 * get current orientation.
 */ 1 4 this 3 510  
30 0 0 4 29 512 0 0 68 /**
 * set the orientation while clearing the rotation velocity.
 */ 2 4 this 3 509  11 orientation 1 521  
31 0 0 4 30 512 0 0 57 /**
 * set rotation as a quaternion delta per second.
 */ 2 4 this 3 509  8 rotation 1 519  
32 0 0 7 31 524 0 0 35 /**
 * get rotation per second.
 */ 1 4 this 3 510  
33 0 0 7 32 527 0 0 95 /**
 * returns a transform matrix that represents the object's willingness to be
 * forced.
 */ 1 4 this 3 510  
34 0 0 7 33 527 0 0 79 /**
 * returns a transform matrix to this object's local coordinate system.
 */ 1 4 this 3 510  
35 0 0 7 34 509 0 0 24 /**
 * dynamic copy.
 */ 1 4 this 3 510  
36 0 0 4 35 512 0 0 0 2 4 this 3 509  4 name 1 528  
37 0 0 6 36 528 0 0 0 1 4 this 3 509  
38 0 0 4 37 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 510  3 out 1 529  
39 0 0 4 38 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 510  3 out 1 529  6 indent 5 531  
40 0 0 7 60 533 0 0 0 0 
41 0 0 7 62 534 281 0 10 /**
 *
 */ 0 
42 0 0 15 62 534 281 0 10 /**
 *
 */ 1 4 copy 1 535  
43 0 0 6 63 534 0 0 0 2 4 this 3 534  4 copy 1 535  
44 0 0 4 65 512 0 0 54 /**
 * Adds a new PhysicsObject to the collection.
 */ 2 4 this 3 534  14 physics_object 1 509  
45 0 0 6 66 463 0 0 170 /**
 * Removes the indicated PhysicsObject from the collection.  Returns true if
 * the physics_object was removed, false if it was not a member of the
 * collection.
 */ 2 4 this 3 534  14 physics_object 1 509  
46 0 0 4 67 512 0 0 235 /**
 * Adds all the PhysicsObjects indicated in the other collection to this
 * collection.  The other physics_objects are simply appended to the end of
 * the physics_objects in this list; duplicates are not automatically removed.
 */ 2 4 this 3 534  5 other 1 535  
47 0 0 4 68 512 0 0 100 /**
 * Removes from this collection all of the PhysicsObjects listed in the other
 * collection.
 */ 2 4 this 3 534  5 other 1 535  
48 0 0 4 69 512 0 0 207 /**
 * Removes any duplicate entries of the same PhysicsObjects on this
 * collection.  If a PhysicsObject appears multiple times, the first
 * appearance is retained; subsequent appearances are removed.
 */ 1 4 this 3 534  
49 0 0 6 70 463 0 0 102 /**
 * Returns true if the indicated PhysicsObject appears in this collection,
 * false otherwise.
 */ 2 4 this 3 535  14 physics_object 1 509  
50 0 0 4 71 512 0 0 58 /**
 * Removes all PhysicsObjects from the collection.
 */ 1 4 this 3 534  
51 0 0 6 72 463 0 0 93 /**
 * Returns true if there are no PhysicsObjects in the collection, false
 * otherwise.
 */ 1 4 this 3 535  
52 0 0 6 73 531 0 0 66 /**
 * Returns the number of PhysicsObjects in the collection.
 */ 1 4 this 3 535  
53 0 0 7 74 509 0 0 59 /**
 * Returns the nth PhysicsObject in the collection.
 */ 2 4 this 3 535  5 index 1 531  
54 0 0 7 76 509 0 0 0 2 4 this 3 535  5 index 1 531  
55 0 0 6 77 531 0 0 124 /**
 * Returns the number of physics objects in the collection.  This is the same
 * thing as get_num_physics_objects().
 */ 1 4 this 3 535  
56 0 0 6 78 534 0 0 0 2 4 this 3 534  5 other 1 535  
57 0 0 7 79 534 281 0 0 2 4 this 3 535  5 other 1 535  
58 0 0 4 80 512 0 0 112 /**
 * Writes a brief one-line description of the PhysicsObjectCollection to the
 * indicated output stream.
 */ 2 4 this 3 535  3 out 1 529  
59 0 0 4 81 512 0 0 117 /**
 * Writes a complete multi-line description of the PhysicsObjectCollection to
 * the indicated output stream.
 */ 3 4 this 3 535  3 out 1 529  12 indent_level 5 531  
60 0 0 6 83 463 0 0 8 /**

 */ 1 4 this 3 537  
61 0 0 4 84 512 0 0 8 /**

 */ 2 4 this 3 491  6 active 1 463  
62 0 0 6 85 463 0 0 0 1 4 this 3 537  
63 0 0 7 86 539 0 0 8 /**

 */ 1 4 this 3 537  
64 0 0 7 87 541 0 0 8 /**

 */ 1 4 this 3 537  
65 0 0 4 88 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 537  3 out 1 529  
66 0 0 4 89 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 537  3 out 1 529  12 indent_level 5 531  
67 0 0 7 90 533 0 0 0 0 
68 0 0 4 92 512 0 0 0 2 4 this 3 542  1 a 1 543  
69 0 0 4 93 512 0 0 8 /**

 */ 2 4 this 3 542  1 m 1 463  
70 0 0 6 94 464 0 0 8 /**

 */ 1 4 this 3 544  
71 0 0 6 95 463 0 0 8 /**

 */ 1 4 this 3 544  
72 0 0 4 96 512 0 0 8 /**

 */ 4 4 this 3 542  1 x 1 463  1 y 1 463  1 z 1 463  
73 0 0 7 97 518 0 0 8 /**

 */ 1 4 this 3 542  
74 0 0 7 98 518 0 0 8 /**

 */ 2 4 this 3 542  2 po 1 510  
75 0 0 7 99 542 0 0 0 1 4 this 3 542  
76 0 0 7 100 533 0 0 0 0 
77 0 0 7 102 548 0 0 0 1 4 this 3 546  
78 0 0 7 103 524 0 0 23 /**
 * access query
 */ 2 4 this 3 548  2 po 1 510  
79 0 0 7 104 533 0 0 0 0 
80 0 0 15 106 483 0 0 122 /**
 * copy constructor (note- does deep copy of pn's) but does NOT attach itself
 * to its template's physicsmanager.
 */ 1 4 copy 1 549  
81 0 0 7 106 483 0 0 519 /**
 * Default Constructor The idea here is that most physicals will NOT be
 * collections of sets (i.e.  particle systems and whatever else).  Because of
 * this, the default constructor, unless otherwise specified, will
 * automatically allocate and initialize one PhysicalObject.  This makes it
 * easier for high-level work.
 *
 * pre-alloc is ONLY for multiple-object physicals, and if true, fills the
 * physics_object vector with dead nodes, pre-allocating for the speed end of
 * the speed-vs-overhead deal.
 */ 2 13 total_objects 5 531  9 pre_alloc 5 463  
82 0 0 6 107 551 0 0 10 // helpers 1 4 this 3 549  
83 0 0 7 108 552 0 0 8 /**

 */ 1 4 this 3 549  
84 0 0 7 109 541 0 0 8 /**

 */ 1 4 this 3 549  
85 0 0 7 110 509 0 0 8 /**

 */ 1 4 this 3 549  
86 0 0 4 111 512 0 0 39 /**
 * Erases the linear force list
 */ 1 4 this 3 483  
87 0 0 4 112 512 0 0 40 /**
 * Erases the angular force list
 */ 1 4 this 3 483  
88 0 0 4 113 512 0 0 33 /**
 * Erases the object list
 */ 1 4 this 3 483  
89 0 0 4 114 512 0 0 48 /**
 * Adds a linear force to the force list
 */ 2 4 this 3 483  1 f 1 542  
90 0 0 4 115 512 0 0 50 /**
 * Adds an angular force to the force list
 */ 2 4 this 3 483  1 f 1 548  
91 0 0 4 116 512 0 0 54 /**
 * Adds an object to the physics object vector
 */ 2 4 this 3 483  2 po 1 509  
92 0 0 4 117 512 0 0 53 /**
 * removes a linear force from the force list
 */ 2 4 this 3 483  1 f 1 542  
93 0 0 4 118 512 0 0 55 /**
 * removes an angular force from the force list
 */ 2 4 this 3 483  1 f 1 548  
94 0 0 6 119 531 0 0 8 /**

 */ 1 4 this 3 549  
95 0 0 7 120 542 0 0 8 /**

 */ 2 4 this 3 549  5 index 1 531  
96 0 0 6 124 531 0 0 8 /**

 */ 1 4 this 3 549  
97 0 0 7 125 548 0 0 8 /**

 */ 2 4 this 3 549  5 index 1 531  
98 0 0 4 129 512 0 0 35 /**
 * Set the local viscosity.
 */ 2 4 this 3 483  9 viscosity 1 464  
99 0 0 6 130 464 0 0 35 /**
 * Get the local viscosity.
 */ 1 4 this 3 549  
100 0 0 7 132 535 0 0 8 /**

 */ 1 4 this 3 549  
101 0 0 4 134 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 549  3 out 5 529  
102 0 0 4 135 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 549  3 out 5 529  6 indent 5 531  
103 0 0 4 136 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 549  3 out 5 529  6 indent 5 531  
104 0 0 4 137 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 549  3 out 5 529  6 indent 5 531  
105 0 0 4 138 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 549  3 out 5 529  6 indent 5 531  
106 0 0 7 139 533 0 0 0 0 
107 0 0 7 142 552 0 0 30 /**
 * default constructor
 */ 1 4 name 1 528  
108 0 0 4 143 512 0 0 8 /**

 */ 1 4 this 3 552  
109 0 0 7 144 483 0 0 8 /**

 */ 2 4 this 3 553  5 index 1 555  
110 0 0 6 145 555 0 0 8 /**

 */ 1 4 this 3 553  
111 0 0 4 147 512 0 0 164 /**
 * Adds a Physical to this PhysicalNode.  If it is already added to this node,
 * does nothing.  It is an error to add a Physical to multiple PhysicalNodes.
 */ 2 4 this 3 552  8 physical 1 483  
112 0 0 4 148 512 0 0 27 /**
 * append operation
 */ 2 4 this 3 552  5 other 1 553  
113 0 0 4 149 512 0 0 28 /**
 * replace operation
 */ 3 4 this 3 552  5 index 1 555  8 physical 1 483  
114 0 0 4 150 512 0 0 27 /**
 * insert operation
 */ 3 4 this 3 552  5 index 1 555  8 physical 1 483  
115 0 0 4 151 512 0 0 27 /**
 * remove operation
 */ 2 4 this 3 552  8 physical 1 483  
116 0 0 4 151 512 0 0 27 /**
 * remove operation
 */ 2 4 this 3 552  5 index 1 555  
117 0 0 7 154 533 0 0 0 0 
118 0 0 15 156 559 0 0 28 /**
 * Copy Constructor.
 */ 1 4 copy 1 557  
119 0 0 7 156 559 0 0 22 /**
 * Constructor
 */ 1 4 name 5 528  
120 0 0 7 157 509 0 0 0 1 4 this 3 559  
121 0 0 4 158 512 0 0 10 /**
 *
 */ 2 4 this 3 559  14 contact_vector 1 516  
122 0 0 6 159 516 0 0 10 /**
 *
 */ 1 4 this 3 557  
123 0 0 4 160 512 0 0 159 /**
 * this sets the transform generated by the contained Physical, moving the
 * node and subsequent geometry.  i.e.  copy from PhysicsObject to PandaNode
 */ 1 4 this 3 559  
124 0 0 4 161 512 0 0 0 2 4 this 3 559  5 limit 1 464  
125 0 0 7 162 533 0 0 0 0 
126 0 0 15 169 562 0 0 0 1 6 param0 0 560  
127 0 0 4 165 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 560  3 out 1 529  
128 0 0 4 166 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 560  3 out 1 529  6 indent 5 531  
129 0 0 4 167 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 560  3 out 1 529  6 indent 5 531  
130 0 0 4 168 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 560  3 out 1 529  6 indent 5 531  
131 0 0 7 172 563 0 0 22 /**
 * constructor
 */ 0 
132 0 0 15 174 566 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 564  
133 0 0 7 174 566 0 0 22 /**
 * constructor
 */ 1 4 quat 1 519  
134 0 0 7 174 566 0 0 22 /**
 * constructor
 */ 3 1 h 1 464  1 p 1 464  1 r 1 464  
135 0 0 4 175 512 0 0 8 /**

 */ 2 4 this 3 566  4 quat 1 519  
136 0 0 4 176 512 0 0 8 /**

 */ 4 4 this 3 566  1 h 1 464  1 p 1 464  1 r 1 464  
137 0 0 7 177 524 0 0 8 /**

 */ 1 4 this 3 564  
138 0 0 7 178 533 0 0 0 0 
139 0 0 7 180 539 0 0 30 /**
 * default constructor
 */ 1 4 name 1 528  
140 0 0 4 181 512 0 0 8 /**

 */ 1 4 this 3 539  
141 0 0 7 182 491 0 0 8 /**

 */ 2 4 this 3 567  5 index 1 555  
142 0 0 6 183 555 0 0 8 /**

 */ 1 4 this 3 567  
143 0 0 4 185 512 0 0 8 /**

 */ 2 4 this 3 539  5 force 1 491  
144 0 0 4 186 512 0 0 27 /**
 * append operation
 */ 2 4 this 3 539  5 other 1 567  
145 0 0 4 187 512 0 0 28 /**
 * replace operation
 */ 3 4 this 3 539  5 index 1 555  5 force 1 491  
146 0 0 4 188 512 0 0 27 /**
 * insert operation
 */ 3 4 this 3 539  5 index 1 555  5 force 1 491  
147 0 0 4 189 512 0 0 27 /**
 * remove operation
 */ 2 4 this 3 539  5 force 1 491  
148 0 0 4 189 512 0 0 27 /**
 * remove operation
 */ 2 4 this 3 539  5 index 1 555  
149 0 0 4 192 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 567  3 out 1 529  6 indent 5 531  
150 0 0 7 193 533 0 0 0 0 
151 0 0 15 195 571 0 0 27 /**
 * Copy Constructor
 */ 1 4 copy 1 569  
152 0 0 7 195 571 0 0 29 /**
 * Vector Constructor
 */ 3 2 po 5 510  1 a 5 464  4 mass 5 463  
153 0 0 4 196 512 0 0 32 /**
 * encapsulating wrapper
 */ 1 4 this 3 571  
154 0 0 4 197 512 0 0 32 /**
 * encapsulating wrapper
 */ 2 4 this 3 571  2 po 1 510  
155 0 0 7 198 510 0 0 42 /**
 * piecewise encapsulating wrapper
 */ 1 4 this 3 569  
156 0 0 4 199 512 0 0 32 /**
 * encapsulating wrapper
 */ 2 4 this 3 571  1 v 1 516  
157 0 0 4 199 512 0 0 42 /**
 * piecewise encapsulating wrapper
 */ 4 4 this 3 571  1 x 1 464  1 y 1 464  1 z 1 464  
158 0 0 7 200 518 0 0 10 /**
 *
 */ 1 4 this 3 569  
159 0 0 7 201 533 0 0 0 0 
160 0 0 15 203 574 0 0 27 /**
 * copy Constructor
 */ 1 4 copy 1 572  
161 0 0 7 203 574 0 0 29 /**
 * Simple Constructor
 */ 5 6 radius 5 464  6 length 5 464  4 coef 5 464  1 a 5 464  2 md 5 463  
162 0 0 4 204 512 0 0 8 /**

 */ 2 4 this 3 574  4 coef 1 464  
163 0 0 6 205 464 0 0 8 /**

 */ 1 4 this 3 572  
164 0 0 4 206 512 0 0 8 /**

 */ 2 4 this 3 574  6 radius 1 464  
165 0 0 6 207 464 0 0 8 /**

 */ 1 4 this 3 572  
166 0 0 4 208 512 0 0 8 /**

 */ 2 4 this 3 574  6 length 1 464  
167 0 0 6 209 464 0 0 8 /**

 */ 1 4 this 3 572  
168 0 0 7 210 533 0 0 0 0 
169 0 0 4 213 512 0 0 25 /**
 * set the radius
 */ 2 4 this 3 575  1 r 1 464  
170 0 0 4 214 512 0 0 42 /**
 * falloff_type encapsulating wrap
 */ 2 4 this 3 575  2 ft 1 495  
171 0 0 4 215 512 0 0 31 /**
 * set the force center
 */ 2 4 this 3 575  1 p 1 513  
172 0 0 6 216 464 0 0 23 /**
 * radius query
 */ 1 4 this 3 576  
173 0 0 6 217 495 0 0 29 /**
 * falloff_type query
 */ 1 4 this 3 576  
174 0 0 7 218 515 0 0 29 /**
 * force_center query
 */ 1 4 this 3 576  
175 0 0 6 219 464 0 0 46 /**
 * calculate the term based on falloff
 */ 1 4 this 3 576  
176 0 0 7 220 533 0 0 0 0 
177 0 0 7 223 578 0 0 22 /**
 * constructor
 */ 0 
178 0 0 15 225 581 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 579  
179 0 0 7 225 581 0 0 22 /**
 * Constructor
 */ 3 4 coef 5 464  1 a 5 464  1 m 5 463  
180 0 0 4 226 512 0 0 8 /**

 */ 2 4 this 3 581  4 coef 1 464  
181 0 0 6 227 464 0 0 8 /**

 */ 1 4 this 3 579  
182 0 0 7 228 533 0 0 0 0 
183 0 0 7 230 533 0 0 0 0 
184 0 0 15 232 584 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 582  
185 0 0 7 232 584 0 0 22 /**
 * constructor
 */ 2 1 a 5 464  1 m 5 463  
186 0 0 7 233 533 0 0 0 0 
187 0 0 15 235 587 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 585  
188 0 0 7 235 587 0 0 22 /**
 * constructor
 */ 2 1 a 5 464  1 m 5 463  
189 0 0 7 236 533 0 0 0 0 
190 0 0 7 238 588 0 0 29 /**
 * Simple constructor
 */ 0 
191 0 0 7 238 588 0 0 29 /**
 * Simple constructor
 */ 5 1 p 1 513  1 f 1 495  1 r 1 464  1 a 5 464  1 m 5 463  
192 0 0 15 238 588 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 589  
193 0 0 7 239 533 0 0 0 0 
194 0 0 7 241 591 0 0 29 /**
 * Simple constructor
 */ 0 
195 0 0 7 241 591 0 0 29 /**
 * Simple constructor
 */ 5 1 p 1 513  1 f 1 495  1 r 1 464  1 a 5 464  4 mass 5 463  
196 0 0 15 241 591 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 592  
197 0 0 7 242 533 0 0 0 0 
198 0 0 15 244 596 0 0 27 /**
 * copy constructor
 */ 1 4 copy 1 594  
199 0 0 7 246 533 0 0 0 0 
200 0 0 7 248 597 0 0 29 /**
 * Vector Constructor
 */ 3 3 vec 1 516  1 a 5 464  4 mass 5 463  
201 0 0 15 248 597 0 0 27 /**
 * Copy Constructor
 */ 1 4 copy 1 598  
202 0 0 7 248 597 0 0 40 /**
 * Default/Piecewise constructor
 */ 5 1 x 5 464  1 y 5 464  1 z 5 464  1 a 5 464  4 mass 5 463  
203 0 0 4 249 512 0 0 32 /**
 * encapsulating wrapper
 */ 2 4 this 3 597  1 v 1 516  
204 0 0 4 249 512 0 0 42 /**
 * piecewise encapsulating wrapper
 */ 4 4 this 3 597  1 x 1 464  1 y 1 464  1 z 1 464  
205 0 0 7 250 518 0 0 10 /**
 *
 */ 1 4 this 3 598  
206 0 0 7 251 533 0 0 0 0 
207 0 0 7 254 600 0 0 10 /**
 *
 */ 0 
208 0 0 4 255 512 0 0 48 // These setters and getter are a bit of a hack: 2 4 this 3 600  5 speed 1 464  
209 0 0 6 256 464 0 0 0 1 4 this 3 600  
210 0 0 4 257 512 0 0 0 2 4 this 3 600  4 coef 1 464  
211 0 0 6 258 464 0 0 0 1 4 this 3 600  
212 0 0 4 259 512 0 0 0 2 4 this 3 600  4 coef 1 464  
213 0 0 6 260 464 0 0 0 1 4 this 3 600  
214 0 0 7 261 533 0 0 0 0 
215 0 0 7 263 551 436 0 79 /**
 * Default Constructor.  NOTE: EulerIntegrator is the standard default.
 */ 0 
216 0 0 15 263 551 436 0 0 1 6 param0 0 601  
217 0 0 4 265 512 0 0 53 /**
 * Hooks a linear integrator into the manager
 */ 2 4 this 3 551  1 i 1 603  
218 0 0 4 266 512 0 0 55 /**
 * Hooks an angular integrator into the manager
 */ 2 4 this 3 551  1 i 1 604  
219 0 0 4 267 512 0 0 54 /**
 * Registers a Physical class with the manager
 */ 2 4 this 3 551  1 p 1 483  
220 0 0 4 268 512 0 0 52 /**
 * Please call attach_physical_node instead.
 */ 2 4 this 3 551  1 p 1 552  
221 0 0 4 269 512 0 0 52 /**
 * Registers a physicalnode with the manager
 */ 2 4 this 3 551  1 p 1 552  
222 0 0 4 270 512 0 0 60 /**
 * Adds a global linear force to the physics manager
 */ 2 4 this 3 551  1 f 1 542  
223 0 0 4 271 512 0 0 61 /**
 * Adds a global angular force to the physics manager
 */ 2 4 this 3 551  1 f 1 548  
224 0 0 4 272 512 0 0 50 /**
 * Resets the physics manager force vector
 */ 1 4 this 3 551  
225 0 0 4 273 512 0 0 50 /**
 * Resets the physics manager force vector
 */ 1 4 this 3 551  
226 0 0 4 274 512 0 0 52 /**
 * Resets the physics manager objects vector
 */ 1 4 this 3 551  
227 0 0 4 275 512 0 0 36 /**
 * Set the global viscosity.
 */ 2 4 this 3 551  9 viscosity 1 464  
228 0 0 6 276 464 0 0 36 /**
 * Get the global viscosity.
 */ 1 4 this 3 601  
229 0 0 4 277 512 0 0 50 /**
 * takes a physical out of the object list
 */ 2 4 this 3 551  1 p 1 483  
230 0 0 4 278 512 0 0 50 /**
 * Removes a physicalnode from the manager
 */ 2 4 this 3 551  1 p 1 552  
231 0 0 4 279 512 0 0 55 /**
 * takes a linear force out of the physics list
 */ 2 4 this 3 551  1 f 1 542  
232 0 0 4 280 512 0 0 57 /**
 * takes an angular force out of the physics list
 */ 2 4 this 3 551  1 f 1 548  
233 0 0 4 281 512 0 0 101 /**
 * This is the main high-level API call.  Performs integration on every
 * attached Physical.
 */ 2 4 this 3 551  2 dt 1 464  
234 0 0 4 281 512 0 0 140 /**
 * This is the main high-level API call.  Performs integration on a single
 * physical.  Make sure its associated forces are active.
 */ 3 4 this 3 551  2 dt 1 464  1 p 1 483  
235 0 0 4 282 512 0 0 153 /**
 * One-time config function, sets up the random seed used by the physics and
 * particle systems.  For synchronizing across distributed computers
 */ 1 4 this 3 551  
236 0 0 4 283 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 2 4 this 3 601  3 out 1 529  
237 0 0 4 284 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 601  3 out 1 529  6 indent 5 531  
238 0 0 4 285 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 601  3 out 1 529  6 indent 5 531  
239 0 0 4 286 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 601  3 out 1 529  6 indent 5 531  
240 0 0 4 287 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 601  3 out 1 529  6 indent 5 531  
241 0 0 4 288 512 0 0 67 /**
 * Write a string representation of this instance to <out>.
 */ 3 4 this 3 601  3 out 1 529  6 indent 5 531  
144
461 13 PhysicsObject 0 75777 13 PhysicsObject 13 PhysicsObject 0 0 0 1 242 0 10 605 606 607 608 609 610 611 612 613 614 36 243 244 245 246 247 248 249 250 251 252 253 254 255 256 257 258 259 260 261 262 263 264 265 266 267 268 269 270 271 272 273 274 275 276 277 278 0 0 1 0 462 0 0 0 0 170
/**
 * A body on which physics will be applied.  If you're looking to add physical
 * motion to your class, do NOT derive from this.  Derive from Physical
 * instead.
 */

462 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

463 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

464 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 465 0 0 0 0 0 0 0 0 0 0

465 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

466 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 467 0 0 0 0 0 0 0 0 0 0

467 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

468 8 LVector3 0 2105344 8 LVector3 8 LVector3 0 0 469 0 0 0 0 0 0 0 0 0 0

469 9 LVector3f 0 2048 9 LVector3f 9 LVector3f 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

470 12 LOrientation 0 2105344 12 LOrientation 12 LOrientation 0 0 471 0 0 0 0 0 0 0 0 0 0

471 13 LOrientationf 0 2048 13 LOrientationf 13 LOrientationf 0 0 0 0 0 0 0 0 0 0 0 0 65
/**
 * This is a unit quaternion representing an orientation.
 */

472 9 LRotation 0 2105344 9 LRotation 9 LRotation 0 0 473 0 0 0 0 0 0 0 0 0 0

473 10 LRotationf 0 2048 10 LRotationf 10 LRotationf 0 0 0 0 0 0 0 0 0 0 0 0 61
/**
 * This is a unit quaternion representing a rotation.
 */

474 23 PhysicsObjectCollection 0 26625 23 PhysicsObjectCollection 23 PhysicsObjectCollection 0 0 0 1 279 281 0 17 280 282 283 284 285 286 287 288 289 290 291 292 293 294 295 296 297 1 621 0 0 0 0 145
/**
 * This is a set of zero or more PhysicsObjects.  It's handy for returning
 * from functions that need to return multiple PhysicsObjects.
 */

475 9 BaseForce 0 75777 9 BaseForce 9 BaseForce 0 0 0 0 0 0 8 298 299 300 301 302 303 304 305 0 0 1 0 462 0 0 0 0 76
/**
 * pure virtual base class for all forces that could POSSIBLY exist.
 */

476 11 LinearForce 0 75777 11 LinearForce 11 LinearForce 0 0 0 0 0 0 9 306 307 308 309 310 311 312 313 314 0 0 1 0 475 0 0 0 0 111
/**
 * A force that acts on a PhysicsObject by way of an Integrator.  This is a
 * pure virtual base class.
 */

477 12 AngularForce 0 75777 12 AngularForce 12 AngularForce 0 0 0 0 0 0 3 315 316 317 0 0 1 0 475 0 0 0 0 56
/**
 * pure virtual parent of all quat-based forces.
 */

478 8 Physical 0 75777 8 Physical 8 Physical 0 0 0 1 318 0 4 615 616 617 618 25 319 320 321 322 323 324 325 326 327 328 329 330 331 332 333 334 335 336 337 338 339 340 341 342 343 2 622 623 0 1 0 462 0 0 0 0 126
/**
 * Defines a set of physically modeled attributes.  If you want physics
 * applied to your class, derive it from this.
 */

479 24 PointerTo< LinearForce > 0 2048 24 PointerTo< LinearForce > 24 PointerTo< LinearForce > 0 0 0 0 0 0 0 0 0 0 0 0 0

480 25 PointerTo< AngularForce > 0 2048 25 PointerTo< AngularForce > 25 PointerTo< AngularForce > 0 0 0 0 0 0 0 0 0 0 0 0 0

481 12 PhysicalNode 0 75777 12 PhysicalNode 12 PhysicalNode 0 0 0 1 344 0 1 619 9 345 346 347 348 349 350 351 352 353 1 624 0 1 0 482 0 0 0 0 68
/**
 * Graph node that encapsulated a series of physical objects
 */

482 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

483 10 Physical * 0 8576 10 Physical * 10 Physical * 0 0 478 0 0 0 0 0 0 0 0 0 0

484 9 ActorNode 0 75777 9 ActorNode 9 ActorNode 0 0 0 1 354 0 0 6 355 356 357 358 359 360 0 0 1 0 481 0 0 0 0 296
/**
 * Like a physical node, but with a little more.  The actornode assumes
 * responsibility for its own transform, and changes in its own PhysicsObject
 * will be reflected as transforms.  This relation goes both ways; changes in
 * the transform will update the object's position (shoves).
 */

485 14 BaseIntegrator 0 75777 14 BaseIntegrator 14 BaseIntegrator 0 0 0 1 365 0 0 4 361 362 363 364 0 0 1 0 486 0 0 0 0 147
/**
 * pure virtual integrator class that holds cached matrix information that
 * really should be common to any possible child implementation.
 */

486 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

487 17 AngularIntegrator 0 75777 17 AngularIntegrator 17 AngularIntegrator 0 0 0 0 0 0 0 0 0 1 0 485 0 0 0 0 124
/**
 * Pure virtual base class for physical modeling.  Takes physically modelable
 * objects and applies forces to them.
 */

488 22 AngularEulerIntegrator 0 75777 22 AngularEulerIntegrator 22 AngularEulerIntegrator 0 0 0 1 366 0 0 0 0 0 1 0 487 0 0 0 0 104
/**
 * Performs Euler integration on a vector of physically modelable objects
 * given a quantum dt.
 */

489 18 AngularVectorForce 0 75777 18 AngularVectorForce 18 AngularVectorForce 0 0 0 1 367 0 0 4 368 369 370 371 0 0 1 0 477 0 0 0 0 92
/**
 * a simple directed torque force, the angular equivalent of simple vector
 * force.
 */

490 9 ForceNode 0 75777 9 ForceNode 9 ForceNode 0 0 0 1 372 0 1 620 10 373 374 375 376 377 378 379 380 381 382 1 625 0 1 0 482 0 0 0 0 200
/**
 * A force that lives in the scene graph and is therefore subject to local
 * coordinate systems.  An example of this would be simulating gravity in a
 * rotating space station.  or something.
 */

491 11 BaseForce * 0 8576 11 BaseForce * 11 BaseForce * 0 0 475 0 0 0 0 0 0 0 0 0 0

492 18 LinearControlForce 0 75777 18 LinearControlForce 18 LinearControlForce 0 0 0 1 383 0 0 6 384 385 386 387 388 389 0 0 1 0 476 0 0 0 0 303
/**
 * Simple directed vector force.  This force is different from the others in
 * that it can be global and still only affect a single object.  That might
 * not make sense for a physics simulation, but it's very handy for a game.
 * I.e.  this is the force applied by user on the selected object.
 */

493 25 LinearCylinderVortexForce 0 75777 25 LinearCylinderVortexForce 25 LinearCylinderVortexForce 0 0 0 1 390 0 0 7 391 392 393 394 395 396 397 0 0 1 0 476 0 0 0 0 328
/**
 * Defines a cylinder inside of which all forces are tangential to the theta
 * of the particle wrt the z-axis in local coord.  space.  This happens by
 * assigning the force a node by which the cylinder is transformed.  Be
 * warned- this will suck anything that it can reach directly into orbit and
 * will NOT let go.
 */

494 19 LinearDistanceForce 0 43009 19 LinearDistanceForce 19 LinearDistanceForce 0 0 0 0 0 0 8 398 399 400 401 402 403 404 405 0 0 1 0 476 0 0 0 1 495 51
/**
 * Pure virtual class for sinks and sources
 */

495 11 FalloffType 0 794624 32 LinearDistanceForce::FalloffType 32 LinearDistanceForce::FalloffType 494 0 0 0 0 0 0 0 0 0 3 13 FT_ONE_OVER_R 34 LinearDistanceForce::FT_ONE_OVER_R 0
0 21 FT_ONE_OVER_R_SQUARED 42 LinearDistanceForce::FT_ONE_OVER_R_SQUARED 0
1 19 FT_ONE_OVER_R_CUBED 40 LinearDistanceForce::FT_ONE_OVER_R_CUBED 0
2 0 0

496 16 LinearIntegrator 0 75777 16 LinearIntegrator 16 LinearIntegrator 0 0 0 0 0 0 0 0 0 1 0 485 0 0 0 0 124
/**
 * Pure virtual base class for physical modeling.  Takes physically modelable
 * objects and applies forces to them.
 */

497 21 LinearEulerIntegrator 0 75777 21 LinearEulerIntegrator 21 LinearEulerIntegrator 0 0 0 1 406 0 0 0 0 0 1 0 496 0 0 0 0 104
/**
 * Performs Euler integration on a vector of physically modelable objects
 * given a quantum dt.
 */

498 19 LinearFrictionForce 0 75777 19 LinearFrictionForce 19 LinearFrictionForce 0 0 0 1 407 0 0 3 408 409 410 0 0 1 0 476 0 0 0 0 36
/**
 * Friction-based drag force
 */

499 17 LinearRandomForce 0 75777 17 LinearRandomForce 17 LinearRandomForce 0 0 0 0 0 0 1 411 0 0 1 0 476 0 0 0 0 61
/**
 * Pure virtual, parent to noiseForce and jitterForce
 */

500 17 LinearJitterForce 0 75777 17 LinearJitterForce 17 LinearJitterForce 0 0 0 1 412 0 0 1 413 0 0 1 0 499 0 0 0 0 94
/**
 * Completely random noise force vector.  Not repeatable, reliable, or
 * predictable.
 */

501 16 LinearNoiseForce 0 75777 16 LinearNoiseForce 16 LinearNoiseForce 0 0 0 1 414 0 0 1 415 0 0 1 0 499 0 0 0 0 40
/**
 * Repeating noise force vector.
 */

502 15 LinearSinkForce 0 75777 15 LinearSinkForce 15 LinearSinkForce 0 0 0 1 416 0 0 1 417 0 0 1 0 494 0 0 0 0 46
/**
 * Attractor force.  Think black hole.
 */

503 17 LinearSourceForce 0 75777 17 LinearSourceForce 17 LinearSourceForce 0 0 0 1 418 0 0 1 419 0 0 1 0 494 0 0 0 0 27
/**
 * Repellant force.
 */

504 22 LinearUserDefinedForce 0 75777 22 LinearUserDefinedForce 22 LinearUserDefinedForce 0 0 0 1 420 0 0 2 421 422 0 0 1 0 476 0 0 0 0 65
/**
 * A programmable force that takes an evaluator function.
 */

505 17 LinearVectorForce 0 75777 17 LinearVectorForce 17 LinearVectorForce 0 0 0 1 423 0 0 3 424 425 426 0 0 1 0 476 0 0 0 0 93
/**
 * Simple directed vector force.  Suitable for gravity, non-turbulent wind,
 * etc...
 */

506 23 PhysicsCollisionHandler 0 75777 23 PhysicsCollisionHandler 23 PhysicsCollisionHandler 0 0 0 1 427 0 0 7 428 429 430 431 432 433 434 0 0 1 0 507 0 0 0 0 176
/**
 * A specialized kind of CollisionHandler that simply pushes back on things
 * that attempt to move into solid walls.  This also puts forces onto the
 * physics objects
 */

507 22 CollisionHandlerPusher 0 2048 22 CollisionHandlerPusher 22 CollisionHandlerPusher 0 0 0 0 0 0 0 0 0 0 0 0 197
/**
 * A specialized kind of CollisionHandler that simply pushes back on things
 * that attempt to move into solid walls.  This is the simplest kind of "real-
 * world" collisions you can have.
 */

508 14 PhysicsManager 0 26625 14 PhysicsManager 14 PhysicsManager 0 0 0 1 435 436 0 24 437 438 439 440 441 442 443 444 445 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 0 0 0 0 0 152
/**
 * Physics don't get much higher-level than this.  Attach as many Physicals
 * (particle systems, etc..) as you want, pick an integrator and go.
 */

509 15 PhysicsObject * 0 8576 15 PhysicsObject * 15 PhysicsObject * 0 0 461 0 0 0 0 0 0 0 0 0 0

510 21 PhysicsObject const * 0 8576 21 PhysicsObject const * 21 PhysicsObject const * 0 0 511 0 0 0 0 0 0 0 0 0 0

511 19 PhysicsObject const 0 8832 19 PhysicsObject const 19 PhysicsObject const 0 0 461 0 0 0 0 0 0 0 0 0 0

512 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

513 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 514 0 0 0 0 0 0 0 0 0 0

514 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 466 0 0 0 0 0 0 0 0 0 0

515 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 466 0 0 0 0 0 0 0 0 0 0

516 16 LVector3 const * 0 8576 16 LVector3 const * 16 LVector3 const * 0 0 517 0 0 0 0 0 0 0 0 0 0

517 14 LVector3 const 0 8832 14 LVector3 const 14 LVector3 const 0 0 468 0 0 0 0 0 0 0 0 0 0

518 10 LVector3 * 0 8576 10 LVector3 * 10 LVector3 * 0 0 468 0 0 0 0 0 0 0 0 0 0

519 17 LRotation const * 0 8576 17 LRotation const * 17 LRotation const * 0 0 520 0 0 0 0 0 0 0 0 0 0

520 15 LRotation const 0 8832 15 LRotation const 15 LRotation const 0 0 472 0 0 0 0 0 0 0 0 0 0

521 20 LOrientation const * 0 8576 20 LOrientation const * 20 LOrientation const * 0 0 522 0 0 0 0 0 0 0 0 0 0

522 18 LOrientation const 0 8832 18 LOrientation const 18 LOrientation const 0 0 470 0 0 0 0 0 0 0 0 0 0

523 14 LOrientation * 0 8576 14 LOrientation * 14 LOrientation * 0 0 470 0 0 0 0 0 0 0 0 0 0

524 11 LRotation * 0 8576 11 LRotation * 11 LRotation * 0 0 472 0 0 0 0 0 0 0 0 0 0

525 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 526 0 0 0 0 0 0 0 0 0 0

526 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

527 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 525 0 0 0 0 0 0 0 0 0 0

528 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

529 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 530 0 0 0 0 0 0 0 0 0 0

530 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

531 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

532 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

533 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 532 0 0 0 0 0 0 0 0 0 0

534 25 PhysicsObjectCollection * 0 8576 25 PhysicsObjectCollection * 25 PhysicsObjectCollection * 0 0 474 0 0 0 0 0 0 0 0 0 0

535 31 PhysicsObjectCollection const * 0 8576 31 PhysicsObjectCollection const * 31 PhysicsObjectCollection const * 0 0 536 0 0 0 0 0 0 0 0 0 0

536 29 PhysicsObjectCollection const 0 8832 29 PhysicsObjectCollection const 29 PhysicsObjectCollection const 0 0 474 0 0 0 0 0 0 0 0 0 0

537 17 BaseForce const * 0 8576 17 BaseForce const * 17 BaseForce const * 0 0 538 0 0 0 0 0 0 0 0 0 0

538 15 BaseForce const 0 8832 15 BaseForce const 15 BaseForce const 0 0 475 0 0 0 0 0 0 0 0 0 0

539 11 ForceNode * 0 8576 11 ForceNode * 11 ForceNode * 0 0 490 0 0 0 0 0 0 0 0 0 0

540 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

541 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 540 0 0 0 0 0 0 0 0 0 0

542 13 LinearForce * 0 8576 13 LinearForce * 13 LinearForce * 0 0 476 0 0 0 0 0 0 0 0 0 0

543 17 PN_stdfloat const 0 8832 17 PN_stdfloat const 17 PN_stdfloat const 0 0 464 0 0 0 0 0 0 0 0 0 0

544 19 LinearForce const * 0 8576 19 LinearForce const * 19 LinearForce const * 0 0 545 0 0 0 0 0 0 0 0 0 0

545 17 LinearForce const 0 8832 17 LinearForce const 17 LinearForce const 0 0 476 0 0 0 0 0 0 0 0 0 0

546 20 AngularForce const * 0 8576 20 AngularForce const * 20 AngularForce const * 0 0 547 0 0 0 0 0 0 0 0 0 0

547 18 AngularForce const 0 8832 18 AngularForce const 18 AngularForce const 0 0 477 0 0 0 0 0 0 0 0 0 0

548 14 AngularForce * 0 8576 14 AngularForce * 14 AngularForce * 0 0 477 0 0 0 0 0 0 0 0 0 0

549 16 Physical const * 0 8576 16 Physical const * 16 Physical const * 0 0 550 0 0 0 0 0 0 0 0 0 0

550 14 Physical const 0 8832 14 Physical const 14 Physical const 0 0 478 0 0 0 0 0 0 0 0 0 0

551 16 PhysicsManager * 0 8576 16 PhysicsManager * 16 PhysicsManager * 0 0 508 0 0 0 0 0 0 0 0 0 0

552 14 PhysicalNode * 0 8576 14 PhysicalNode * 14 PhysicalNode * 0 0 481 0 0 0 0 0 0 0 0 0 0

553 20 PhysicalNode const * 0 8576 20 PhysicalNode const * 20 PhysicalNode const * 0 0 554 0 0 0 0 0 0 0 0 0 0

554 18 PhysicalNode const 0 8832 18 PhysicalNode const 18 PhysicalNode const 0 0 481 0 0 0 0 0 0 0 0 0 0

555 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 556 0 0 0 0 0 0 0 0 0 0

556 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

557 17 ActorNode const * 0 8576 17 ActorNode const * 17 ActorNode const * 0 0 558 0 0 0 0 0 0 0 0 0 0

558 15 ActorNode const 0 8832 15 ActorNode const 15 ActorNode const 0 0 484 0 0 0 0 0 0 0 0 0 0

559 11 ActorNode * 0 8576 11 ActorNode * 11 ActorNode * 0 0 484 0 0 0 0 0 0 0 0 0 0

560 22 BaseIntegrator const * 0 8576 22 BaseIntegrator const * 22 BaseIntegrator const * 0 0 561 0 0 0 0 0 0 0 0 0 0

561 20 BaseIntegrator const 0 8832 20 BaseIntegrator const 20 BaseIntegrator const 0 0 485 0 0 0 0 0 0 0 0 0 0

562 16 BaseIntegrator * 0 8576 16 BaseIntegrator * 16 BaseIntegrator * 0 0 485 0 0 0 0 0 0 0 0 0 0

563 24 AngularEulerIntegrator * 0 8576 24 AngularEulerIntegrator * 24 AngularEulerIntegrator * 0 0 488 0 0 0 0 0 0 0 0 0 0

564 26 AngularVectorForce const * 0 8576 26 AngularVectorForce const * 26 AngularVectorForce const * 0 0 565 0 0 0 0 0 0 0 0 0 0

565 24 AngularVectorForce const 0 8832 24 AngularVectorForce const 24 AngularVectorForce const 0 0 489 0 0 0 0 0 0 0 0 0 0

566 20 AngularVectorForce * 0 8576 20 AngularVectorForce * 20 AngularVectorForce * 0 0 489 0 0 0 0 0 0 0 0 0 0

567 17 ForceNode const * 0 8576 17 ForceNode const * 17 ForceNode const * 0 0 568 0 0 0 0 0 0 0 0 0 0

568 15 ForceNode const 0 8832 15 ForceNode const 15 ForceNode const 0 0 490 0 0 0 0 0 0 0 0 0 0

569 26 LinearControlForce const * 0 8576 26 LinearControlForce const * 26 LinearControlForce const * 0 0 570 0 0 0 0 0 0 0 0 0 0

570 24 LinearControlForce const 0 8832 24 LinearControlForce const 24 LinearControlForce const 0 0 492 0 0 0 0 0 0 0 0 0 0

571 20 LinearControlForce * 0 8576 20 LinearControlForce * 20 LinearControlForce * 0 0 492 0 0 0 0 0 0 0 0 0 0

572 33 LinearCylinderVortexForce const * 0 8576 33 LinearCylinderVortexForce const * 33 LinearCylinderVortexForce const * 0 0 573 0 0 0 0 0 0 0 0 0 0

573 31 LinearCylinderVortexForce const 0 8832 31 LinearCylinderVortexForce const 31 LinearCylinderVortexForce const 0 0 493 0 0 0 0 0 0 0 0 0 0

574 27 LinearCylinderVortexForce * 0 8576 27 LinearCylinderVortexForce * 27 LinearCylinderVortexForce * 0 0 493 0 0 0 0 0 0 0 0 0 0

575 21 LinearDistanceForce * 0 8576 21 LinearDistanceForce * 21 LinearDistanceForce * 0 0 494 0 0 0 0 0 0 0 0 0 0

576 27 LinearDistanceForce const * 0 8576 27 LinearDistanceForce const * 27 LinearDistanceForce const * 0 0 577 0 0 0 0 0 0 0 0 0 0

577 25 LinearDistanceForce const 0 8832 25 LinearDistanceForce const 25 LinearDistanceForce const 0 0 494 0 0 0 0 0 0 0 0 0 0

578 23 LinearEulerIntegrator * 0 8576 23 LinearEulerIntegrator * 23 LinearEulerIntegrator * 0 0 497 0 0 0 0 0 0 0 0 0 0

579 27 LinearFrictionForce const * 0 8576 27 LinearFrictionForce const * 27 LinearFrictionForce const * 0 0 580 0 0 0 0 0 0 0 0 0 0

580 25 LinearFrictionForce const 0 8832 25 LinearFrictionForce const 25 LinearFrictionForce const 0 0 498 0 0 0 0 0 0 0 0 0 0

581 21 LinearFrictionForce * 0 8576 21 LinearFrictionForce * 21 LinearFrictionForce * 0 0 498 0 0 0 0 0 0 0 0 0 0

582 25 LinearJitterForce const * 0 8576 25 LinearJitterForce const * 25 LinearJitterForce const * 0 0 583 0 0 0 0 0 0 0 0 0 0

583 23 LinearJitterForce const 0 8832 23 LinearJitterForce const 23 LinearJitterForce const 0 0 500 0 0 0 0 0 0 0 0 0 0

584 19 LinearJitterForce * 0 8576 19 LinearJitterForce * 19 LinearJitterForce * 0 0 500 0 0 0 0 0 0 0 0 0 0

585 24 LinearNoiseForce const * 0 8576 24 LinearNoiseForce const * 24 LinearNoiseForce const * 0 0 586 0 0 0 0 0 0 0 0 0 0

586 22 LinearNoiseForce const 0 8832 22 LinearNoiseForce const 22 LinearNoiseForce const 0 0 501 0 0 0 0 0 0 0 0 0 0

587 18 LinearNoiseForce * 0 8576 18 LinearNoiseForce * 18 LinearNoiseForce * 0 0 501 0 0 0 0 0 0 0 0 0 0

588 17 LinearSinkForce * 0 8576 17 LinearSinkForce * 17 LinearSinkForce * 0 0 502 0 0 0 0 0 0 0 0 0 0

589 23 LinearSinkForce const * 0 8576 23 LinearSinkForce const * 23 LinearSinkForce const * 0 0 590 0 0 0 0 0 0 0 0 0 0

590 21 LinearSinkForce const 0 8832 21 LinearSinkForce const 21 LinearSinkForce const 0 0 502 0 0 0 0 0 0 0 0 0 0

591 19 LinearSourceForce * 0 8576 19 LinearSourceForce * 19 LinearSourceForce * 0 0 503 0 0 0 0 0 0 0 0 0 0

592 25 LinearSourceForce const * 0 8576 25 LinearSourceForce const * 25 LinearSourceForce const * 0 0 593 0 0 0 0 0 0 0 0 0 0

593 23 LinearSourceForce const 0 8832 23 LinearSourceForce const 23 LinearSourceForce const 0 0 503 0 0 0 0 0 0 0 0 0 0

594 30 LinearUserDefinedForce const * 0 8576 30 LinearUserDefinedForce const * 30 LinearUserDefinedForce const * 0 0 595 0 0 0 0 0 0 0 0 0 0

595 28 LinearUserDefinedForce const 0 8832 28 LinearUserDefinedForce const 28 LinearUserDefinedForce const 0 0 504 0 0 0 0 0 0 0 0 0 0

596 24 LinearUserDefinedForce * 0 8576 24 LinearUserDefinedForce * 24 LinearUserDefinedForce * 0 0 504 0 0 0 0 0 0 0 0 0 0

597 19 LinearVectorForce * 0 8576 19 LinearVectorForce * 19 LinearVectorForce * 0 0 505 0 0 0 0 0 0 0 0 0 0

598 25 LinearVectorForce const * 0 8576 25 LinearVectorForce const * 25 LinearVectorForce const * 0 0 599 0 0 0 0 0 0 0 0 0 0

599 23 LinearVectorForce const 0 8832 23 LinearVectorForce const 23 LinearVectorForce const 0 0 505 0 0 0 0 0 0 0 0 0 0

600 25 PhysicsCollisionHandler * 0 8576 25 PhysicsCollisionHandler * 25 PhysicsCollisionHandler * 0 0 506 0 0 0 0 0 0 0 0 0 0

601 22 PhysicsManager const * 0 8576 22 PhysicsManager const * 22 PhysicsManager const * 0 0 602 0 0 0 0 0 0 0 0 0 0

602 20 PhysicsManager const 0 8832 20 PhysicsManager const 20 PhysicsManager const 0 0 508 0 0 0 0 0 0 0 0 0 0

603 18 LinearIntegrator * 0 8576 18 LinearIntegrator * 18 LinearIntegrator * 0 0 496 0 0 0 0 0 0 0 0 0 0

604 19 AngularIntegrator * 0 8576 19 AngularIntegrator * 19 AngularIntegrator * 0 0 487 0 0 0 0 0 0 0 0 0 0

0
16
605 6 active 0 6 463 245 244 0 0 0 0 0 0 21 PhysicsObject::active 0

606 4 mass 0 6 464 247 246 0 0 0 0 0 0 19 PhysicsObject::mass 0

607 8 position 0 6 466 249 248 0 0 0 0 0 0 23 PhysicsObject::position 0

608 13 last_position 0 6 466 252 251 0 0 0 0 0 0 28 PhysicsObject::last_position 0

609 8 velocity 0 6 468 254 253 0 0 0 0 0 0 23 PhysicsObject::velocity 0

610 17 implicit_velocity 0 2 468 255 0 0 0 0 0 0 0 32 PhysicsObject::implicit_velocity 0

611 17 terminal_velocity 0 6 464 263 262 0 0 0 0 0 0 32 PhysicsObject::terminal_velocity 0

612 8 oriented 0 6 463 265 264 0 0 0 0 0 0 23 PhysicsObject::oriented 0

613 11 orientation 0 6 470 267 266 0 0 0 0 0 0 26 PhysicsObject::orientation 0

614 8 rotation 0 6 472 270 269 0 0 0 0 0 0 23 PhysicsObject::rotation 0

615 13 linear_forces 0 66 479 332 0 0 0 0 331 0 0 23 Physical::linear_forces 0

616 14 angular_forces 0 66 480 334 0 0 0 0 333 0 0 24 Physical::angular_forces 0

617 9 viscosity 0 2 464 336 0 0 0 0 0 0 0 19 Physical::viscosity 0

618 7 objects 0 2 474 337 0 0 0 0 0 0 0 17 Physical::objects 0

619 9 physicals 0 358 483 346 350 0 0 352 347 351 0 23 PhysicalNode::physicals 0

620 6 forces 0 358 491 374 378 0 0 380 375 379 0 17 ForceNode::forces 0

5
621 19 get_physics_objects 0 290 291 44 PhysicsObjectCollection::get_physics_objects 0

622 17 get_linear_forces 0 331 332 27 Physical::get_linear_forces 0

623 18 get_angular_forces 0 333 334 28 Physical::get_angular_forces 0

624 13 get_physicals 0 347 346 27 PhysicalNode::get_physicals 0

625 10 get_forces 0 375 374 21 ForceNode::get_forces 0

