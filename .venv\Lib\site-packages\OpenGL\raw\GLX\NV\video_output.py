'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_video_output'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_video_output',error_checker=_errors._error_checker)
GLX_VIDEO_OUT_ALPHA_NV=_C('GLX_VIDEO_OUT_ALPHA_NV',0x20C4)
GLX_VIDEO_OUT_COLOR_AND_ALPHA_NV=_C('GLX_VIDEO_OUT_COLOR_AND_ALPHA_NV',0x20C6)
GLX_VIDEO_OUT_COLOR_AND_DEPTH_NV=_C('GLX_VIDEO_OUT_COLOR_AND_DEPTH_NV',0x20C7)
GLX_VIDEO_OUT_COLOR_NV=_C('GLX_VIDEO_OUT_COLOR_NV',0x20C3)
GLX_VIDEO_OUT_DEPTH_NV=_C('GLX_VIDEO_OUT_DEPTH_NV',0x20C5)
GLX_VIDEO_OUT_FIELD_1_NV=_C('GLX_VIDEO_OUT_FIELD_1_NV',0x20C9)
GLX_VIDEO_OUT_FIELD_2_NV=_C('GLX_VIDEO_OUT_FIELD_2_NV',0x20CA)
GLX_VIDEO_OUT_FRAME_NV=_C('GLX_VIDEO_OUT_FRAME_NV',0x20C8)
GLX_VIDEO_OUT_STACKED_FIELDS_1_2_NV=_C('GLX_VIDEO_OUT_STACKED_FIELDS_1_2_NV',0x20CB)
GLX_VIDEO_OUT_STACKED_FIELDS_2_1_NV=_C('GLX_VIDEO_OUT_STACKED_FIELDS_2_1_NV',0x20CC)
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.GLXVideoDeviceNV,_cs.GLXPbuffer,_cs.c_int)
def glXBindVideoImageNV(dpy,VideoDevice,pbuf,iVideoBuffer):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.GLXVideoDeviceNV))
def glXGetVideoDeviceNV(dpy,screen,numVideoDevices,pVideoDevice):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.GLXVideoDeviceNV,ctypes.POINTER(_cs.c_ulong),ctypes.POINTER(_cs.c_ulong))
def glXGetVideoInfoNV(dpy,screen,VideoDevice,pulCounterOutputPbuffer,pulCounterOutputVideo):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.c_int,_cs.GLXVideoDeviceNV)
def glXReleaseVideoDeviceNV(dpy,screen,VideoDevice):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.GLXPbuffer)
def glXReleaseVideoImageNV(dpy,pbuf):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),_cs.GLXPbuffer,_cs.c_int,ctypes.POINTER(_cs.c_ulong),_cs.GLboolean)
def glXSendPbufferToVideoNV(dpy,pbuf,iBufferType,pulCounterPbuffer,bBlock):pass
