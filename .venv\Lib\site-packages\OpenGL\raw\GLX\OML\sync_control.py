'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_OML_sync_control'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_OML_sync_control',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,ctypes.POINTER(_cs.int32_t),ctypes.POINTER(_cs.int32_t))
def glXGetMscRateOML(dpy,drawable,numerator,denominator):pass
@_f
@_p.types(_cs.<PERSON><PERSON>,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t))
def glXGetSyncValuesOML(dpy,drawable,ust,msc,sbc):pass
@_f
@_p.types(_cs.int64_t,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.int64_t,_cs.int64_t,_cs.int64_t)
def glXSwapBuffersMscOML(dpy,drawable,target_msc,divisor,remainder):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.int64_t,_cs.int64_t,_cs.int64_t,ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t))
def glXWaitForMscOML(dpy,drawable,target_msc,divisor,remainder,ust,msc,sbc):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.int64_t,ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t),ctypes.POINTER(_cs.int64_t))
def glXWaitForSbcOML(dpy,drawable,target_sbc,ust,msc,sbc):pass
