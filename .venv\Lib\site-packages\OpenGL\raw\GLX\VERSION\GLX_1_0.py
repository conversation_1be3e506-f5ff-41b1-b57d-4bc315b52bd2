'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_VERSION_GLX_1_0'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_VERSION_GLX_1_0',error_checker=_errors._error_checker)
GLX_ACCUM_ALPHA_SIZE=_C('GLX_ACCUM_ALPHA_SIZE',17)
GLX_ACCUM_BLUE_SIZE=_C('GLX_ACCUM_BLUE_SIZE',16)
GLX_ACCUM_GREEN_SIZE=_C('GLX_ACCUM_GREEN_SIZE',15)
GLX_ACCUM_RED_SIZE=_C('GLX_ACCUM_RED_SIZE',14)
GLX_ALPHA_SIZE=_C('GLX_ALPHA_SIZE',11)
GLX_AUX_BUFFERS=_C('GLX_AUX_BUFFERS',7)
GLX_BAD_ATTRIBUTE=_C('GLX_BAD_ATTRIBUTE',2)
GLX_BAD_CONTEXT=_C('GLX_BAD_CONTEXT',5)
GLX_BAD_ENUM=_C('GLX_BAD_ENUM',7)
GLX_BAD_SCREEN=_C('GLX_BAD_SCREEN',1)
GLX_BAD_VALUE=_C('GLX_BAD_VALUE',6)
GLX_BAD_VISUAL=_C('GLX_BAD_VISUAL',4)
GLX_BLUE_SIZE=_C('GLX_BLUE_SIZE',10)
GLX_BUFFER_SIZE=_C('GLX_BUFFER_SIZE',2)
GLX_BufferSwapComplete=_C('GLX_BufferSwapComplete',1)
GLX_DEPTH_SIZE=_C('GLX_DEPTH_SIZE',12)
GLX_DOUBLEBUFFER=_C('GLX_DOUBLEBUFFER',5)
# GLX_EXTENSION_NAME=_C('GLX_EXTENSION_NAME',"GLX")
GLX_GREEN_SIZE=_C('GLX_GREEN_SIZE',9)
GLX_LEVEL=_C('GLX_LEVEL',3)
GLX_NO_EXTENSION=_C('GLX_NO_EXTENSION',3)
GLX_PbufferClobber=_C('GLX_PbufferClobber',0)
GLX_RED_SIZE=_C('GLX_RED_SIZE',8)
GLX_RGBA=_C('GLX_RGBA',4)
GLX_STENCIL_SIZE=_C('GLX_STENCIL_SIZE',13)
GLX_STEREO=_C('GLX_STEREO',6)
GLX_USE_GL=_C('GLX_USE_GL',1)
__GLX_NUMBER_EVENTS=_C('__GLX_NUMBER_EVENTS',17)
@_f
@_p.types(ctypes.POINTER(_cs.XVisualInfo),ctypes.POINTER(_cs.Display),_cs.c_int,ctypes.POINTER(_cs.c_int))
def glXChooseVisual(dpy,screen,attribList):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXContext,_cs.GLXContext,_cs.c_ulong)
def glXCopyContext(dpy,src,dst,mask):pass
@_f
@_p.types(_cs.GLXContext,ctypes.POINTER(_cs.Display),ctypes.POINTER(_cs.XVisualInfo),_cs.GLXContext,_cs.Bool)
def glXCreateContext(dpy,vis,shareList,direct):pass
@_f
@_p.types(_cs.GLXPixmap,ctypes.POINTER(_cs.Display),ctypes.POINTER(_cs.XVisualInfo),_cs.Pixmap)
def glXCreateGLXPixmap(dpy,visual,pixmap):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXContext)
def glXDestroyContext(dpy,ctx):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXPixmap)
def glXDestroyGLXPixmap(dpy,pixmap):pass
@_f
@_p.types(_cs.c_int,ctypes.POINTER(_cs.Display),ctypes.POINTER(_cs.XVisualInfo),_cs.c_int,ctypes.POINTER(_cs.c_int))
def glXGetConfig(dpy,visual,attrib,value):pass
@_f
@_p.types(_cs.GLXContext,)
def glXGetCurrentContext():pass
@_f
@_p.types(_cs.GLXDrawable,)
def glXGetCurrentDrawable():pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXContext)
def glXIsDirect(dpy,ctx):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.GLXContext)
def glXMakeCurrent(dpy,drawable,ctx):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int))
def glXQueryExtension(dpy,errorb,event):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),ctypes.POINTER(_cs.c_int),ctypes.POINTER(_cs.c_int))
def glXQueryVersion(dpy,maj,min):pass
@_f
@_p.types(None,ctypes.POINTER(_cs.Display),_cs.GLXDrawable)
def glXSwapBuffers(dpy,drawable):pass
@_f
@_p.types(None,_cs.Font,_cs.c_int,_cs.c_int,_cs.c_int)
def glXUseXFont(font,first,count,list):pass
@_f
@_p.types(None,)
def glXWaitGL():pass
@_f
@_p.types(None,)
def glXWaitX():pass
