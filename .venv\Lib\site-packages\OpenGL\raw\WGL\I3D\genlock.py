'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_I3D_genlock'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_I3D_genlock',error_checker=_errors._error_checker)
WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D=_C('WGL_GENLOCK_SOURCE_DIGITAL_FIELD_I3D',0x2049)
WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D=_C('WGL_GENLOCK_SOURCE_DIGITAL_SYNC_I3D',0x2048)
WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D=_C('WGL_GENLOCK_SOURCE_EDGE_BOTH_I3D',0x204C)
WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D=_C('WGL_GENLOCK_SOURCE_EDGE_FALLING_I3D',0x204A)
WGL_GENLOCK_SOURCE_EDGE_RISING_I3D=_C('WGL_GENLOCK_SOURCE_EDGE_RISING_I3D',0x204B)
WGL_GENLOCK_SOURCE_EXTERNAL_FIELD_I3D=_C('WGL_GENLOCK_SOURCE_EXTERNAL_FIELD_I3D',0x2046)
WGL_GENLOCK_SOURCE_EXTERNAL_SYNC_I3D=_C('WGL_GENLOCK_SOURCE_EXTERNAL_SYNC_I3D',0x2045)
WGL_GENLOCK_SOURCE_EXTERNAL_TTL_I3D=_C('WGL_GENLOCK_SOURCE_EXTERNAL_TTL_I3D',0x2047)
WGL_GENLOCK_SOURCE_MULTIVIEW_I3D=_C('WGL_GENLOCK_SOURCE_MULTIVIEW_I3D',0x2044)
@_f
@_p.types(_cs.BOOL,_cs.HDC)
def wglDisableGenlockI3D(hDC):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC)
def wglEnableGenlockI3D(hDC):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT)
def wglGenlockSampleRateI3D(hDC,uRate):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT)
def wglGenlockSourceDelayI3D(hDC,uDelay):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT)
def wglGenlockSourceEdgeI3D(hDC,uEdge):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT)
def wglGenlockSourceI3D(hDC,uSource):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.UINT))
def wglGetGenlockSampleRateI3D(hDC,uRate):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.UINT))
def wglGetGenlockSourceDelayI3D(hDC,uDelay):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.UINT))
def wglGetGenlockSourceEdgeI3D(hDC,uEdge):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.UINT))
def wglGetGenlockSourceI3D(hDC,uSource):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.BOOL))
def wglIsEnabledGenlockI3D(hDC,pFlag):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,ctypes.POINTER(_cs.UINT),ctypes.POINTER(_cs.UINT))
def wglQueryGenlockMaxSourceDelayI3D(hDC,uMaxLineDelay,uMaxPixelDelay):pass
