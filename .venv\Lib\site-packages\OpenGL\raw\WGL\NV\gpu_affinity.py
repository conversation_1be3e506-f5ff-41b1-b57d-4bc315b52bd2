'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_NV_gpu_affinity'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_NV_gpu_affinity',error_checker=_errors._error_checker)
ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV=_C('ERROR_INCOMPATIBLE_AFFINITY_MASKS_NV',0x20D0)
ERROR_MISSING_AFFINITY_MASK_NV=_C('ERROR_MISSING_AFFINITY_MASK_NV',0x20D1)
@_f
@_p.types(_cs.HDC,ctypes.POINTER(_cs.HGPUNV))
def wglCreateAffinityDCNV(phGpuList):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC)
def wglDeleteDCNV(hdc):pass
@_f
@_p.types(_cs.BOOL,_cs.HGPUNV,_cs.UINT,_cs.PGPU_DEVICE)
def wglEnumGpuDevicesNV(hGpu,iDeviceIndex,lpGpuDevice):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT,ctypes.POINTER(_cs.HGPUNV))
def wglEnumGpusFromAffinityDCNV(hAffinityDC,iGpuIndex,hGpu):pass
@_f
@_p.types(_cs.BOOL,_cs.UINT,ctypes.POINTER(_cs.HGPUNV))
def wglEnumGpusNV(iGpuIndex,phGpu):pass
