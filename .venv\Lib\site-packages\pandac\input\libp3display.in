1730992849
3 3
12 libp3display 4 62HU 12 panda3d.core 
669
653 8 get_pipe 0 4 1322 24 GraphicsDevice::get_pipe 0 1 1 72
/**
 * Returns the GraphicsPipe that this device is associated with.
 */
58
inline GraphicsPipe *GraphicsDevice::get_pipe(void) const;

654 14 get_class_type 0 4 1322 30 GraphicsDevice::get_class_type 0 1 2 0
55
static TypeHandle GraphicsDevice::get_class_type(void);

655 8 is_valid 0 4 1324 22 GraphicsPipe::is_valid 0 1 3 379
/**
 * Returns false if this pipe is known to be invalid, meaning that an attempt
 * to create a GraphicsWindow with the pipe will certainly fail.  Returns true
 * if the pipe is probably valid (is this case, an attempt to create a
 * GraphicsWindow should succeed, but might still fail).
 *
 * Use the GraphicsEngine class to create a GraphicsWindow on a particular
 * pipe.
 */
47
inline bool GraphicsPipe::is_valid(void) const;

656 19 get_supported_types 0 4 1324 33 GraphicsPipe::get_supported_types 0 1 4 373
/**
 * Returns the mask of bits that represents the kinds of GraphicsOutput
 * objects this pipe might be able to successfully create.  The return value
 * is the union of bits in GraphicsPipe::OutputTypes that represents the set
 * of GraphicsOutput types.
 *
 * A 1 bit in a particular position is not a guarantee of success, but a 0 bit
 * is a guarantee of failure.
 */
57
inline int GraphicsPipe::get_supported_types(void) const;

657 13 supports_type 0 4 1324 27 GraphicsPipe::supports_type 0 1 5 432
/**
 * A convenience function to ask if a particular type or types of
 * GraphicsObjects are supported.  The parameter is a union of one or more
 * bits defined in GrpahicsPipe::OutputTypes.
 *
 * Returns true if all of the requested types are listed in the
 * supported_types mask, false if any one of them is not.  This is not a
 * guarantee that the indicated output type will successfully be created when
 * it is attempted.
 */
57
inline bool GraphicsPipe::supports_type(int flags) const;

658 17 get_display_width 0 4 1324 31 GraphicsPipe::get_display_width 0 1 6 268
/**
 * Returns the width of the entire display, if it is known.  This may return
 * 0.  This is not a guarantee that windows (particularly fullscreen windows)
 * may not be created larger than this width, but it is intended to provide a
 * hint to the application.
 */
55
inline int GraphicsPipe::get_display_width(void) const;

659 18 get_display_height 0 4 1324 32 GraphicsPipe::get_display_height 0 1 7 133
/**
 * Returns the height of the entire display, if it is known.  This may return
 * 0.  See the caveats for get_display_width().
 */
56
inline int GraphicsPipe::get_display_height(void) const;

660 16 get_display_zoom 0 4 1324 30 GraphicsPipe::get_display_zoom 0 1 8 429
/**
 * Returns the display zoom factor configured in the operating system.  If the
 * operating system automatically scales windows to match the DPI (such as when
 * dpi-aware is set to false), this will be 1.0.  Otherwise, this will be set to
 * a value approximating the density of the monitor divided by the standard
 * density of the operating system (usually 96), yielding a value like 1.5 or
 * 2.0.
 *
 * @since 1.10.8
 */
55
PN_stdfloat GraphicsPipe::get_display_zoom(void) const;

661 23 get_display_information 0 4 1324 37 GraphicsPipe::get_display_information 0 1 9 46
/**
 * Gets the pipe's DisplayInformation.
 */
64
DisplayInformation *GraphicsPipe::get_display_information(void);

662 19 ~DisplayInformation 0 516 1331 39 DisplayInformation::~DisplayInformation 0 0 10
/**
 *
 */
46
DisplayInformation::~DisplayInformation(void);

663 18 DisplayInformation 0 260 1331 38 DisplayInformation::DisplayInformation 0 2 13 14 10
/**
 *
 */
130
DisplayInformation::DisplayInformation(void);
inline DisplayInformation::DisplayInformation(DisplayInformation const &) = default;

664 17 get_display_state 0 4 1331 37 DisplayInformation::get_display_state 0 1 15 10
/**
 *
 */
48
int DisplayInformation::get_display_state(void);

665 24 get_maximum_window_width 0 4 1331 44 DisplayInformation::get_maximum_window_width 0 1 16 10
/**
 *
 */
55
int DisplayInformation::get_maximum_window_width(void);

666 25 get_maximum_window_height 0 4 1331 45 DisplayInformation::get_maximum_window_height 0 1 17 10
/**
 *
 */
56
int DisplayInformation::get_maximum_window_height(void);

667 25 get_window_bits_per_pixel 0 4 1331 45 DisplayInformation::get_window_bits_per_pixel 0 1 18 10
/**
 *
 */
56
int DisplayInformation::get_window_bits_per_pixel(void);

668 23 get_total_display_modes 0 4 1331 43 DisplayInformation::get_total_display_modes 0 1 19 10
/**
 *
 */
54
int DisplayInformation::get_total_display_modes(void);

669 16 get_display_mode 0 4 1331 36 DisplayInformation::get_display_mode 0 1 20 10
/**
 *
 */
75
DisplayMode const &DisplayInformation::get_display_mode(int display_index);

670 30 get_current_display_mode_index 0 4 1331 50 DisplayInformation::get_current_display_mode_index 0 1 21 174
/**
 * Returns the index of the current display mode (determined at the time of
 * application start) in the display mode array, or -1 if this could not be
 * determined.
 */
67
int DisplayInformation::get_current_display_mode_index(void) const;

671 22 get_display_mode_width 0 4 1331 42 DisplayInformation::get_display_mode_width 0 1 22 49
// Older interface for display modes.

/**
 *
 */
66
int DisplayInformation::get_display_mode_width(int display_index);

672 23 get_display_mode_height 0 4 1331 43 DisplayInformation::get_display_mode_height 0 1 23 10
/**
 *
 */
67
int DisplayInformation::get_display_mode_height(int display_index);

673 31 get_display_mode_bits_per_pixel 0 4 1331 51 DisplayInformation::get_display_mode_bits_per_pixel 0 1 24 10
/**
 *
 */
75
int DisplayInformation::get_display_mode_bits_per_pixel(int display_index);

674 29 get_display_mode_refresh_rate 0 4 1331 49 DisplayInformation::get_display_mode_refresh_rate 0 1 25 10
/**
 *
 */
73
int DisplayInformation::get_display_mode_refresh_rate(int display_index);

675 32 get_display_mode_fullscreen_only 0 4 1331 52 DisplayInformation::get_display_mode_fullscreen_only 0 1 26 10
/**
 *
 */
76
int DisplayInformation::get_display_mode_fullscreen_only(int display_index);

676 16 get_shader_model 0 4 1331 36 DisplayInformation::get_shader_model 0 1 27 10
/**
 *
 */
78
GraphicsStateGuardian::ShaderModel DisplayInformation::get_shader_model(void);

677 16 get_video_memory 0 4 1331 36 DisplayInformation::get_video_memory 0 1 28 10
/**
 *
 */
47
int DisplayInformation::get_video_memory(void);

678 18 get_texture_memory 0 4 1331 38 DisplayInformation::get_texture_memory 0 1 29 10
/**
 *
 */
49
int DisplayInformation::get_texture_memory(void);

679 25 update_memory_information 0 4 1331 45 DisplayInformation::update_memory_information 0 1 30 10
/**
 *
 */
57
void DisplayInformation::update_memory_information(void);

680 19 get_physical_memory 0 4 1331 39 DisplayInformation::get_physical_memory 0 1 31 10
/**
 *
 */
55
uint64_t DisplayInformation::get_physical_memory(void);

681 29 get_available_physical_memory 0 4 1331 49 DisplayInformation::get_available_physical_memory 0 1 32 10
/**
 *
 */
65
uint64_t DisplayInformation::get_available_physical_memory(void);

682 18 get_page_file_size 0 4 1331 38 DisplayInformation::get_page_file_size 0 1 33 10
/**
 *
 */
54
uint64_t DisplayInformation::get_page_file_size(void);

683 28 get_available_page_file_size 0 4 1331 48 DisplayInformation::get_available_page_file_size 0 1 34 10
/**
 *
 */
64
uint64_t DisplayInformation::get_available_page_file_size(void);

684 26 get_process_virtual_memory 0 4 1331 46 DisplayInformation::get_process_virtual_memory 0 1 35 10
/**
 *
 */
62
uint64_t DisplayInformation::get_process_virtual_memory(void);

685 36 get_available_process_virtual_memory 0 4 1331 56 DisplayInformation::get_available_process_virtual_memory 0 1 36 10
/**
 *
 */
72
uint64_t DisplayInformation::get_available_process_virtual_memory(void);

686 15 get_memory_load 0 4 1331 35 DisplayInformation::get_memory_load 0 1 37 10
/**
 *
 */
46
int DisplayInformation::get_memory_load(void);

687 20 get_page_fault_count 0 4 1331 40 DisplayInformation::get_page_fault_count 0 1 38 10
/**
 *
 */
56
uint64_t DisplayInformation::get_page_fault_count(void);

688 18 get_process_memory 0 4 1331 38 DisplayInformation::get_process_memory 0 1 39 10
/**
 *
 */
54
uint64_t DisplayInformation::get_process_memory(void);

689 23 get_peak_process_memory 0 4 1331 43 DisplayInformation::get_peak_process_memory 0 1 40 10
/**
 *
 */
59
uint64_t DisplayInformation::get_peak_process_memory(void);

690 19 get_page_file_usage 0 4 1331 39 DisplayInformation::get_page_file_usage 0 1 41 10
/**
 *
 */
55
uint64_t DisplayInformation::get_page_file_usage(void);

691 24 get_peak_page_file_usage 0 4 1331 44 DisplayInformation::get_peak_page_file_usage 0 1 42 10
/**
 *
 */
60
uint64_t DisplayInformation::get_peak_page_file_usage(void);

692 13 get_vendor_id 0 4 1331 33 DisplayInformation::get_vendor_id 0 1 43 10
/**
 *
 */
44
int DisplayInformation::get_vendor_id(void);

693 13 get_device_id 0 4 1331 33 DisplayInformation::get_device_id 0 1 44 10
/**
 *
 */
44
int DisplayInformation::get_device_id(void);

694 18 get_driver_product 0 4 1331 38 DisplayInformation::get_driver_product 0 1 45 10
/**
 *
 */
49
int DisplayInformation::get_driver_product(void);

695 18 get_driver_version 0 4 1331 38 DisplayInformation::get_driver_version 0 1 46 10
/**
 *
 */
49
int DisplayInformation::get_driver_version(void);

696 22 get_driver_sub_version 0 4 1331 42 DisplayInformation::get_driver_sub_version 0 1 47 10
/**
 *
 */
53
int DisplayInformation::get_driver_sub_version(void);

697 16 get_driver_build 0 4 1331 36 DisplayInformation::get_driver_build 0 1 48 10
/**
 *
 */
47
int DisplayInformation::get_driver_build(void);

698 21 get_driver_date_month 0 4 1331 41 DisplayInformation::get_driver_date_month 0 1 49 10
/**
 *
 */
52
int DisplayInformation::get_driver_date_month(void);

699 19 get_driver_date_day 0 4 1331 39 DisplayInformation::get_driver_date_day 0 1 50 10
/**
 *
 */
50
int DisplayInformation::get_driver_date_day(void);

700 20 get_driver_date_year 0 4 1331 40 DisplayInformation::get_driver_date_year 0 1 51 10
/**
 *
 */
51
int DisplayInformation::get_driver_date_year(void);

701 21 get_cpu_vendor_string 0 4 1331 41 DisplayInformation::get_cpu_vendor_string 0 1 52 10
/**
 *
 */
73
std::string const &DisplayInformation::get_cpu_vendor_string(void) const;

702 20 get_cpu_brand_string 0 4 1331 40 DisplayInformation::get_cpu_brand_string 0 1 53 10
/**
 *
 */
72
std::string const &DisplayInformation::get_cpu_brand_string(void) const;

703 27 get_cpu_version_information 0 4 1331 47 DisplayInformation::get_cpu_version_information 0 1 54 10
/**
 *
 */
67
unsigned int DisplayInformation::get_cpu_version_information(void);

704 19 get_cpu_brand_index 0 4 1331 39 DisplayInformation::get_cpu_brand_index 0 1 55 10
/**
 *
 */
59
unsigned int DisplayInformation::get_cpu_brand_index(void);

705 17 get_cpu_frequency 0 4 1331 37 DisplayInformation::get_cpu_frequency 0 1 56 10
/**
 *
 */
53
uint64_t DisplayInformation::get_cpu_frequency(void);

706 12 get_cpu_time 0 4 1331 32 DisplayInformation::get_cpu_time 0 1 57 57
/**
 * Equivalent to the rdtsc processor instruction.
 */
55
static uint64_t DisplayInformation::get_cpu_time(void);

707 25 get_maximum_cpu_frequency 0 4 1331 45 DisplayInformation::get_maximum_cpu_frequency 0 1 58 10
/**
 *
 */
61
uint64_t DisplayInformation::get_maximum_cpu_frequency(void);

708 25 get_current_cpu_frequency 0 4 1331 45 DisplayInformation::get_current_cpu_frequency 0 1 59 10
/**
 *
 */
61
uint64_t DisplayInformation::get_current_cpu_frequency(void);

709 20 update_cpu_frequency 0 4 1331 40 DisplayInformation::update_cpu_frequency 0 1 60 10
/**
 *
 */
68
void DisplayInformation::update_cpu_frequency(int processor_number);

710 17 get_num_cpu_cores 0 4 1331 37 DisplayInformation::get_num_cpu_cores 0 1 61 148
/**
 * Returns the number of individual CPU cores in the system, or 0 if this
 * number is not available.  A hyperthreaded CPU counts once here.
 */
48
int DisplayInformation::get_num_cpu_cores(void);

711 20 get_num_logical_cpus 0 4 1331 40 DisplayInformation::get_num_logical_cpus 0 1 62 151
/**
 * Returns the number of logical CPU's in the system, or 0 if this number is
 * not available.  A hyperthreaded CPU counts as two or more here.
 */
51
int DisplayInformation::get_num_logical_cpus(void);

712 20 get_os_version_major 0 4 1331 40 DisplayInformation::get_os_version_major 0 1 63 33
/**
 * Returns -1 if not set.
 */
51
int DisplayInformation::get_os_version_major(void);

713 20 get_os_version_minor 0 4 1331 40 DisplayInformation::get_os_version_minor 0 1 64 33
/**
 * Returns -1 if not set.
 */
51
int DisplayInformation::get_os_version_minor(void);

714 20 get_os_version_build 0 4 1331 40 DisplayInformation::get_os_version_build 0 1 65 33
/**
 * Returns -1 if not set.
 */
51
int DisplayInformation::get_os_version_build(void);

715 18 get_os_platform_id 0 4 1331 38 DisplayInformation::get_os_platform_id 0 1 66 33
/**
 * Returns -1 if not set.
 */
49
int DisplayInformation::get_os_platform_id(void);

716 15 lookup_cpu_data 0 6 1324 29 GraphicsPipe::lookup_cpu_data 0 1 10 148
/**
 * Looks up the detailed CPU information and stores it in
 * _display_information, if supported by the OS. This may take a second or
 * two.
 */
49
virtual void GraphicsPipe::lookup_cpu_data(void);

717 18 get_interface_name 0 6 1324 32 GraphicsPipe::get_interface_name 0 1 11 0
69
virtual std::string GraphicsPipe::get_interface_name(void) const = 0;

718 14 get_class_type 0 4 1324 28 GraphicsPipe::get_class_type 0 1 12 0
53
static TypeHandle GraphicsPipe::get_class_type(void);

719 15 ~DrawableRegion 0 518 1335 31 DrawableRegion::~DrawableRegion 0 0 10
/**
 *
 */
46
virtual DrawableRegion::~DrawableRegion(void);

720 22 set_clear_color_active 0 4 1335 38 DrawableRegion::set_clear_color_active 0 1 67 234
/**
 * Toggles the flag that indicates whether the color buffer should be cleared
 * every frame.  If this is true, the color buffer will be cleared to the
 * color indicated by set_clear_color(); otherwise, it will be left alone.
 */
76
inline void DrawableRegion::set_clear_color_active(bool clear_color_active);

721 22 get_clear_color_active 0 4 1335 38 DrawableRegion::get_clear_color_active 0 1 68 155
/**
 * Returns the current setting of the flag that indicates whether the color
 * buffer should be cleared every frame.  See set_clear_color_active().
 */
63
inline bool DrawableRegion::get_clear_color_active(void) const;

722 22 set_clear_depth_active 0 4 1335 38 DrawableRegion::set_clear_depth_active 0 1 69 243
/**
 * Toggles the flag that indicates whether the depth buffer should be cleared
 * every frame.  If this is true, the depth buffer will be cleared to the
 * depth value indicated by set_clear_depth(); otherwise, it will be left
 * alone.
 */
76
inline void DrawableRegion::set_clear_depth_active(bool clear_depth_active);

723 22 get_clear_depth_active 0 4 1335 38 DrawableRegion::get_clear_depth_active 0 1 70 155
/**
 * Returns the current setting of the flag that indicates whether the depth
 * buffer should be cleared every frame.  See set_clear_depth_active().
 */
63
inline bool DrawableRegion::get_clear_depth_active(void) const;

724 24 set_clear_stencil_active 0 4 1335 40 DrawableRegion::set_clear_stencil_active 0 1 71 243
/**
 * Toggles the flag that indicates whether the stencil buffer should be
 * cleared every frame.  If this is true, the stencil buffer will be cleared
 * to the value indicated by set_clear_stencil(); otherwise, it will be left
 * alone.
 */
80
inline void DrawableRegion::set_clear_stencil_active(bool clear_stencil_active);

725 24 get_clear_stencil_active 0 4 1335 40 DrawableRegion::get_clear_stencil_active 0 1 72 157
/**
 * Returns the current setting of the flag that indicates whether the color
 * buffer should be cleared every frame.  See set_clear_stencil_active().
 */
65
inline bool DrawableRegion::get_clear_stencil_active(void) const;

726 15 set_clear_color 0 4 1335 31 DrawableRegion::set_clear_color 0 1 73 256
/**
 * Sets the clear color to the indicated value.  This is the value that will
 * be used to clear the color buffer every frame, but only if
 * get_clear_color_active() returns true.  If get_clear_color_active() returns
 * false, this is meaningless.
 */
65
inline void DrawableRegion::set_clear_color(LColor const &color);

727 15 get_clear_color 0 4 1335 31 DrawableRegion::get_clear_color 0 1 74 250
/**
 * Returns the current clear color value.  This is the value that will be used
 * to clear the color buffer every frame, but only if get_clear_color_active()
 * returns true.  If get_clear_color_active() returns false, this is
 * meaningless.
 */
65
inline LColor const &DrawableRegion::get_clear_color(void) const;

728 15 set_clear_depth 0 4 1335 31 DrawableRegion::set_clear_depth 0 1 75 256
/**
 * Sets the clear depth to the indicated value.  This is the value that will
 * be used to clear the depth buffer every frame, but only if
 * get_clear_depth_active() returns true.  If get_clear_depth_active() returns
 * false, this is meaningless.
 */
63
inline void DrawableRegion::set_clear_depth(PN_stdfloat depth);

729 15 get_clear_depth 0 4 1335 31 DrawableRegion::get_clear_depth 0 1 76 250
/**
 * Returns the current clear depth value.  This is the value that will be used
 * to clear the depth buffer every frame, but only if get_clear_depth_active()
 * returns true.  If get_clear_depth_active() returns false, this is
 * meaningless.
 */
63
inline PN_stdfloat DrawableRegion::get_clear_depth(void) const;

730 17 set_clear_stencil 0 4 1335 33 DrawableRegion::set_clear_stencil 0 1 77 262
/**
 * Sets the clear stencil to the indicated value.  This is the value that will
 * be used to clear the stencil buffer every frame, but only if
 * get_clear_color_active() returns true.  If get_clear_stencil_active()
 * returns false, this is meaningless.
 */
68
inline void DrawableRegion::set_clear_stencil(unsigned int stencil);

731 17 get_clear_stencil 0 4 1335 33 DrawableRegion::get_clear_stencil 0 1 78 258
/**
 * Returns the current clear stencil value.  This is the value that will be
 * used to clear the stencil buffer every frame, but only if
 * get_clear_stencil_active() returns true.  If get_clear_stencil_active()
 * returns false, this is meaningless.
 */
66
inline unsigned int DrawableRegion::get_clear_stencil(void) const;

732 16 set_clear_active 0 6 1335 32 DrawableRegion::set_clear_active 0 1 79 55
/**
 * Sets the clear-active flag for any bitplane.
 */
76
virtual void DrawableRegion::set_clear_active(int n, bool clear_aux_active);

733 16 get_clear_active 0 6 1335 32 DrawableRegion::get_clear_active 0 1 80 55
/**
 * Gets the clear-active flag for any bitplane.
 */
59
virtual bool DrawableRegion::get_clear_active(int n) const;

734 15 set_clear_value 0 6 1335 31 DrawableRegion::set_clear_value 0 1 81 49
/**
 * Sets the clear value for any bitplane.
 */
79
virtual void DrawableRegion::set_clear_value(int n, LColor const &clear_value);

735 15 get_clear_value 0 6 1335 31 DrawableRegion::get_clear_value 0 1 82 52
/**
 * Returns the clear value for any bitplane.
 */
67
virtual LColor const &DrawableRegion::get_clear_value(int n) const;

736 14 disable_clears 0 6 1335 30 DrawableRegion::disable_clears 0 1 83 110
/**
 * Disables both the color and depth clear.  See set_clear_color_active and
 * set_clear_depth_active.
 */
50
virtual void DrawableRegion::disable_clears(void);

737 19 is_any_clear_active 0 6 1335 35 DrawableRegion::is_any_clear_active 0 1 84 183
/**
 * Returns true if any of the clear types (so far there are just color or
 * depth) have been set active, or false if none of them are active and there
 * is no need to clear.
 */
61
virtual bool DrawableRegion::is_any_clear_active(void) const;

738 14 set_pixel_zoom 0 6 1335 30 DrawableRegion::set_pixel_zoom 0 1 85 975
/**
 * Sets the amount by which the pixels of the region are scaled internally
 * when filling the image interally.  Setting this number larger makes the
 * pixels blockier, but may make the rendering faster, particularly for
 * software renderers.  Setting this number to 2.0 reduces the number of
 * pixels that have to be filled by the renderer by a factor of 2.0.  It
 * doesn't make sense to set this lower than 1.0.
 *
 * It is possible to set this on either individual DisplayRegions or on
 * overall GraphicsWindows, but you will get better performance for setting it
 * on the window rather than its individual DisplayRegions.  Also, you may not
 * set it on a DisplayRegion that doesn't have both clear_color() and
 * clear_depth() enabled.
 *
 * This property is only supported on renderers for which it is particularly
 * useful--currently, this is the tinydisplay software renderer.  Other kinds
 * of renderers allow you to set this property, but ignore it.
 */
68
virtual void DrawableRegion::set_pixel_zoom(PN_stdfloat pixel_zoom);

739 14 get_pixel_zoom 0 4 1335 30 DrawableRegion::get_pixel_zoom 0 1 86 138
/**
 * Returns the value set by set_pixel_zoom(), regardless of whether it is
 * being respected or not.  Also see get_pixel_factor().
 */
62
inline PN_stdfloat DrawableRegion::get_pixel_zoom(void) const;

740 16 get_pixel_factor 0 4 1335 32 DrawableRegion::get_pixel_factor 0 1 87 340
/**
 * Returns the amount by which the height and width of the region will be
 * scaled internally, based on the zoom factor set by set_pixel_zoom().  This
 * will return 1.0 if the pixel_zoom was not set or if it is not being
 * respected (for instance, because the underlying renderer doesn't support it
 * --see supports_pixel_zoom).
 */
64
inline PN_stdfloat DrawableRegion::get_pixel_factor(void) const;

741 19 supports_pixel_zoom 0 6 1335 35 DrawableRegion::supports_pixel_zoom 0 1 88 451
/**
 * Returns true if a call to set_pixel_zoom() will be respected, false if it
 * will be ignored.  If this returns false, then get_pixel_factor() will
 * always return 1.0, regardless of what value you specify for
 * set_pixel_zoom().
 *
 * This may return false if the underlying renderer doesn't support pixel
 * zooming, or if you have called this on a DisplayRegion that doesn't have
 * both set_clear_color() and set_clear_depth() enabled.
 */
61
virtual bool DrawableRegion::supports_pixel_zoom(void) const;

742 21 get_renderbuffer_type 0 4 1335 37 DrawableRegion::get_renderbuffer_type 0 1 89 83
/**
 * Returns the RenderBuffer::Type that corresponds to a RenderTexturePlane.
 */
60
static int DrawableRegion::get_renderbuffer_type(int plane);

743 12 WindowHandle 0 260 1342 26 WindowHandle::WindowHandle 0 2 90 91 22
/**
 *
 */

/**
 *
 */
130
inline WindowHandle::WindowHandle(WindowHandle::OSHandle *os_handle);
inline WindowHandle::WindowHandle(WindowHandle const &copy);

744 13 get_os_handle 0 4 1342 27 WindowHandle::get_os_handle 0 1 92 91
/**
 * Returns the OS-specific handle stored internally to the WindowHandle
 * wrapper.
 */
71
inline WindowHandle::OSHandle *WindowHandle::get_os_handle(void) const;

745 13 set_os_handle 0 4 1342 27 WindowHandle::set_os_handle 0 1 93 91
/**
 * Changes the OS-specific handle stored internally to the WindowHandle
 * wrapper.
 */
75
inline void WindowHandle::set_os_handle(WindowHandle::OSHandle *os_handle);

746 14 get_int_handle 0 6 1344 38 WindowHandle::OSHandle::get_int_handle 0 1 99 149
/**
 * Returns the OS-specific handle converted to an integer, if this is possible
 * for the particular representation.  Returns 0 if it is not.
 */
71
virtual std::size_t WindowHandle::OSHandle::get_int_handle(void) const;

747 6 output 0 6 1344 30 WindowHandle::OSHandle::output 0 1 100 10
/**
 *
 */
69
virtual void WindowHandle::OSHandle::output(std::ostream &out) const;

748 14 get_class_type 0 4 1344 38 WindowHandle::OSHandle::get_class_type 0 1 101 0
63
static TypeHandle WindowHandle::OSHandle::get_class_type(void);

749 8 OSHandle 0 260 1344 32 WindowHandle::OSHandle::OSHandle 0 1 98 197
/**
 * The base class of OSHandle doesn't have a usable constructor.  Always
 * construct an instance of some specialized type, that stores the appropriate
 * kind of window handle for each OS.
 */
82
inline WindowHandle::OSHandle::OSHandle(WindowHandle::OSHandle const &) = default;

750 20 send_windows_message 0 4 1342 34 WindowHandle::send_windows_message 0 1 94 285
/**
 * Call this method on a parent WindowHandle to deliver a Windows message to
 * the current child window, if any.  This is used in the web plugin system to
 * deliver button events detected directly by the browser system into Panda,
 * which is particularly necessary on Vista.
 */
82
void WindowHandle::send_windows_message(unsigned int msg, int wparam, int lparam);

751 14 get_int_handle 0 4 1342 28 WindowHandle::get_int_handle 0 1 95 149
/**
 * Returns the OS-specific handle converted to an integer, if this is possible
 * for the particular representation.  Returns 0 if it is not.
 */
53
std::size_t WindowHandle::get_int_handle(void) const;

752 6 output 0 4 1342 20 WindowHandle::output 0 1 96 10
/**
 *
 */
51
void WindowHandle::output(std::ostream &out) const;

753 14 get_class_type 0 4 1342 28 WindowHandle::get_class_type 0 1 97 0
53
static TypeHandle WindowHandle::get_class_type(void);

754 16 WindowProperties 0 260 1345 34 WindowProperties::WindowProperties 0 1 102 22
/**
 *
 */

/**
 *
 */
83
WindowProperties::WindowProperties(PyObject *self, PyObject *args, PyObject *kwds);

755 10 operator = 0 4 1345 28 WindowProperties::operator = 0 1 103 0
64
void WindowProperties::operator =(WindowProperties const &copy);

756 17 ~WindowProperties 0 516 1345 35 WindowProperties::~WindowProperties 0 0 10
/**
 *
 */
49
inline WindowProperties::~WindowProperties(void);

757 21 get_config_properties 0 4 1345 39 WindowProperties::get_config_properties 0 1 104 128
/**
 * Returns a WindowProperties structure with all of the default values filled
 * in according to the user's config file.
 */
70
static WindowProperties WindowProperties::get_config_properties(void);

758 11 get_default 0 4 1345 29 WindowProperties::get_default 0 1 105 186
/**
 * Returns the "default" WindowProperties.  If set_default() has been called,
 * this returns that WindowProperties structure; otherwise, this returns
 * get_config_properties().
 */
60
static WindowProperties WindowProperties::get_default(void);

759 11 set_default 0 4 1345 29 WindowProperties::set_default 0 1 106 290
/**
 * Replaces the "default" WindowProperties with the specified structure.  The
 * specified WindowProperties will be returned by future calls to
 * get_default(), until clear_default() is called.
 *
 * Note that this completely replaces the default properties; it is not
 * additive.
 */
86
static void WindowProperties::set_default(WindowProperties const &default_properties);

760 13 clear_default 0 4 1345 31 WindowProperties::clear_default 0 1 107 104
/**
 * Returns the "default" WindowProperties to whatever is specified in the
 * user's config file.
 */
50
static void WindowProperties::clear_default(void);

761 4 size 0 4 1345 22 WindowProperties::size 0 2 108 109 216
/**
 * Returns a WindowProperties structure with only the size specified.  The
 * size is the only property that matters to buffers.
 *
 * @deprecated in the Python API, use WindowProperties(size=(x, y)) instead.
 */
143
static WindowProperties WindowProperties::size(LVecBase2i const &size);
static WindowProperties WindowProperties::size(int x_size, int y_size);

762 11 operator == 0 4 1345 29 WindowProperties::operator == 0 1 110 0
72
bool WindowProperties::operator ==(WindowProperties const &other) const;

763 11 operator != 0 4 1345 29 WindowProperties::operator != 0 1 111 0
79
inline bool WindowProperties::operator !=(WindowProperties const &other) const;

764 5 clear 0 4 1345 23 WindowProperties::clear 0 1 112 138
/**
 * Unsets all properties that have been specified so far, and resets the
 * WindowProperties structure to its initial empty state.
 */
35
void WindowProperties::clear(void);

765 16 is_any_specified 0 4 1345 34 WindowProperties::is_any_specified 0 1 113 79
/**
 * Returns true if any properties have been specified, false otherwise.
 */
59
inline bool WindowProperties::is_any_specified(void) const;

766 10 set_origin 0 4 1345 28 WindowProperties::set_origin 0 2 114 115 474
/**
 * Specifies the origin on the screen (in pixels, relative to the top-left
 * corner) at which the window should appear.  This is the origin of the top-
 * left corner of the useful part of the window, not including decorations.
 */

/**
 * Specifies the origin on the screen (in pixels, relative to the top-left
 * corner) at which the window should appear.  This is the origin of the top-
 * left corner of the useful part of the window, not including decorations.
 */
135
inline void WindowProperties::set_origin(LPoint2i const &origin);
inline void WindowProperties::set_origin(int x_origin, int y_origin);

767 10 get_origin 0 4 1345 28 WindowProperties::get_origin 0 1 116 97
/**
 * Returns the coordinates of the window's top-left corner, not including
 * decorations.
 */
57
inline LPoint2i WindowProperties::get_origin(void) const;

768 12 get_x_origin 0 4 1345 30 WindowProperties::get_x_origin 0 1 117 98
/**
 * Returns the x coordinate of the window's top-left corner, not including
 * decorations.
 */
54
inline int WindowProperties::get_x_origin(void) const;

769 12 get_y_origin 0 4 1345 30 WindowProperties::get_y_origin 0 1 118 98
/**
 * Returns the y coordinate of the window's top-left corner, not including
 * decorations.
 */
54
inline int WindowProperties::get_y_origin(void) const;

770 10 has_origin 0 4 1345 28 WindowProperties::has_origin 0 1 119 81
/**
 * Returns true if the window origin has been specified, false otherwise.
 */
53
inline bool WindowProperties::has_origin(void) const;

771 12 clear_origin 0 4 1345 30 WindowProperties::clear_origin 0 1 120 64
/**
 * Removes the origin specification from the properties.
 */
49
inline void WindowProperties::clear_origin(void);

772 8 set_size 0 4 1345 26 WindowProperties::set_size 0 2 121 122 296
/**
 * Specifies the requested size of the window, in pixels.  This is the size of
 * the useful part of the window, not including decorations.
 */

/**
 * Specifies the requested size of the window, in pixels.  This is the size of
 * the useful part of the window, not including decorations.
 */
126
inline void WindowProperties::set_size(LVector2i const &size);
inline void WindowProperties::set_size(int x_size, int y_size);

773 8 get_size 0 4 1345 26 WindowProperties::get_size 0 1 123 97
/**
 * Returns size in pixels of the useful part of the window, not including
 * decorations.
 */
56
inline LVector2i WindowProperties::get_size(void) const;

774 10 get_x_size 0 4 1345 28 WindowProperties::get_x_size 0 1 124 154
/**
 * Returns size in pixels in the x dimension of the useful part of the window,
 * not including decorations.  That is, this is the window's width.
 */
52
inline int WindowProperties::get_x_size(void) const;

775 10 get_y_size 0 4 1345 28 WindowProperties::get_y_size 0 1 125 155
/**
 * Returns size in pixels in the y dimension of the useful part of the window,
 * not including decorations.  That is, this is the window's height.
 */
52
inline int WindowProperties::get_y_size(void) const;

776 8 has_size 0 4 1345 26 WindowProperties::has_size 0 1 126 79
/**
 * Returns true if the window size has been specified, false otherwise.
 */
51
inline bool WindowProperties::has_size(void) const;

777 10 clear_size 0 4 1345 28 WindowProperties::clear_size 0 1 127 62
/**
 * Removes the size specification from the properties.
 */
47
inline void WindowProperties::clear_size(void);

778 14 has_mouse_mode 0 4 1345 32 WindowProperties::has_mouse_mode 0 1 128 10
/**
 *
 */
57
inline bool WindowProperties::has_mouse_mode(void) const;

779 14 set_mouse_mode 0 4 1345 32 WindowProperties::set_mouse_mode 0 1 129 962
/**
 * Specifies the mode in which the window is to operate its mouse pointer.
 *
 * M_absolute: the normal mode in which a mouse pointer operates, where the
 * mouse can move outside the window and the mouse coordinates are relative to
 * its position in the window.
 *
 * M_relative (OSX or Unix/X11 only): a mode where only relative movements are
 * reported; particularly useful for FPS-style mouse movements where you have
 * hidden the mouse pointer and are are more interested in how fast the mouse
 * is moving, rather than precisely where the pointer is hovering.
 *
 * This has no effect on Windows.  On Unix/X11, this requires the Xxf86dga
 * extension to be available.
 *
 * M_confined: this mode reports absolute mouse positions, but confines the
 * mouse pointer to the window boundary.  It can portably replace M_relative
 * for an FPS, but you need to periodically move the pointer to the center of
 * the window and track movement deltas.
 *
 */
79
inline void WindowProperties::set_mouse_mode(WindowProperties::MouseMode mode);

780 14 get_mouse_mode 0 4 1345 32 WindowProperties::get_mouse_mode 0 1 130 32
/**
 * See set_mouse_mode().
 */
80
inline WindowProperties::MouseMode WindowProperties::get_mouse_mode(void) const;

781 16 clear_mouse_mode 0 4 1345 34 WindowProperties::clear_mouse_mode 0 1 131 68
/**
 * Removes the mouse_mode specification from the properties.
 */
53
inline void WindowProperties::clear_mouse_mode(void);

782 9 set_title 0 4 1345 27 WindowProperties::set_title 0 1 132 69
/**
 * Specifies the title that should be assigned to the window.
 */
66
inline void WindowProperties::set_title(std::string const &title);

783 9 get_title 0 4 1345 27 WindowProperties::get_title 0 1 133 38
/**
 * Returns the window's title.
 */
59
inline std::string WindowProperties::get_title(void) const;

784 9 has_title 0 4 1345 27 WindowProperties::has_title 0 1 134 80
/**
 * Returns true if the window title has been specified, false otherwise.
 */
52
inline bool WindowProperties::has_title(void) const;

785 11 clear_title 0 4 1345 29 WindowProperties::clear_title 0 1 135 63
/**
 * Removes the title specification from the properties.
 */
48
inline void WindowProperties::clear_title(void);

786 15 set_undecorated 0 4 1345 33 WindowProperties::set_undecorated 0 1 136 128
/**
 * Specifies whether the window should be created with a visible title and
 * border (false, the default) or not (true).
 */
64
inline void WindowProperties::set_undecorated(bool undecorated);

787 15 get_undecorated 0 4 1345 33 WindowProperties::get_undecorated 0 1 137 52
/**
 * Returns true if the window has no border.
 */
58
inline bool WindowProperties::get_undecorated(void) const;

788 15 has_undecorated 0 4 1345 33 WindowProperties::has_undecorated 0 1 138 64
/**
 * Returns true if set_undecorated() has been specified.
 */
58
inline bool WindowProperties::has_undecorated(void) const;

789 17 clear_undecorated 0 4 1345 35 WindowProperties::clear_undecorated 0 1 139 69
/**
 * Removes the undecorated specification from the properties.
 */
54
inline void WindowProperties::clear_undecorated(void);

790 14 set_fixed_size 0 4 1345 32 WindowProperties::set_fixed_size 0 1 140 72
/**
 * Specifies whether the window should be resizable by the user.
 */
62
inline void WindowProperties::set_fixed_size(bool fixed_size);

791 14 get_fixed_size 0 4 1345 32 WindowProperties::get_fixed_size 0 1 141 85
/**
 * Returns true if the window cannot be resized by the user, false otherwise.
 */
57
inline bool WindowProperties::get_fixed_size(void) const;

792 14 has_fixed_size 0 4 1345 32 WindowProperties::has_fixed_size 0 1 142 63
/**
 * Returns true if set_fixed_size() has been specified.
 */
57
inline bool WindowProperties::has_fixed_size(void) const;

793 16 clear_fixed_size 0 4 1345 34 WindowProperties::clear_fixed_size 0 1 143 68
/**
 * Removes the fixed_size specification from the properties.
 */
53
inline void WindowProperties::clear_fixed_size(void);

794 14 set_fullscreen 0 4 1345 32 WindowProperties::set_fullscreen 0 1 144 131
/**
 * Specifies whether the window should be opened in fullscreen mode (true) or
 * normal windowed mode (false, the default).
 */
62
inline void WindowProperties::set_fullscreen(bool fullscreen);

795 14 get_fullscreen 0 4 1345 32 WindowProperties::get_fullscreen 0 1 145 60
/**
 * Returns true if the window is in fullscreen mode.
 */
57
inline bool WindowProperties::get_fullscreen(void) const;

796 14 has_fullscreen 0 4 1345 32 WindowProperties::has_fullscreen 0 1 146 63
/**
 * Returns true if set_fullscreen() has been specified.
 */
57
inline bool WindowProperties::has_fullscreen(void) const;

797 16 clear_fullscreen 0 4 1345 34 WindowProperties::clear_fullscreen 0 1 147 68
/**
 * Removes the fullscreen specification from the properties.
 */
53
inline void WindowProperties::clear_fullscreen(void);

798 14 set_foreground 0 4 1345 32 WindowProperties::set_foreground 0 1 148 120
/**
 * Specifies whether the window should be opened in the foreground (true), or
 * left in the background (false).
 */
62
inline void WindowProperties::set_foreground(bool foreground);

799 14 get_foreground 0 4 1345 32 WindowProperties::get_foreground 0 1 149 59
/**
 * Returns true if the window is in the foreground.
 */
57
inline bool WindowProperties::get_foreground(void) const;

800 14 has_foreground 0 4 1345 32 WindowProperties::has_foreground 0 1 150 63
/**
 * Returns true if set_foreground() has been specified.
 */
57
inline bool WindowProperties::has_foreground(void) const;

801 16 clear_foreground 0 4 1345 34 WindowProperties::clear_foreground 0 1 151 68
/**
 * Removes the foreground specification from the properties.
 */
53
inline void WindowProperties::clear_foreground(void);

802 13 set_minimized 0 4 1345 31 WindowProperties::set_minimized 0 1 152 97
/**
 * Specifies whether the window should be created minimized (true), or normal
 * (false).
 */
60
inline void WindowProperties::set_minimized(bool minimized);

803 13 get_minimized 0 4 1345 31 WindowProperties::get_minimized 0 1 153 51
/**
 * Returns true if the window is minimized.
 */
56
inline bool WindowProperties::get_minimized(void) const;

804 13 has_minimized 0 4 1345 31 WindowProperties::has_minimized 0 1 154 62
/**
 * Returns true if set_minimized() has been specified.
 */
56
inline bool WindowProperties::has_minimized(void) const;

805 15 clear_minimized 0 4 1345 33 WindowProperties::clear_minimized 0 1 155 67
/**
 * Removes the minimized specification from the properties.
 */
52
inline void WindowProperties::clear_minimized(void);

806 12 set_raw_mice 0 4 1345 30 WindowProperties::set_raw_mice 0 1 156 74
/**
 * Specifies whether the window should read the raw mouse devices.
 */
58
inline void WindowProperties::set_raw_mice(bool raw_mice);

807 12 get_raw_mice 0 4 1345 30 WindowProperties::get_raw_mice 0 1 157 57
/**
 * Returns true if the window reads the raw mice.
 */
55
inline bool WindowProperties::get_raw_mice(void) const;

808 12 has_raw_mice 0 4 1345 30 WindowProperties::has_raw_mice 0 1 158 61
/**
 * Returns true if set_raw_mice() has been specified.
 */
55
inline bool WindowProperties::has_raw_mice(void) const;

809 14 clear_raw_mice 0 4 1345 32 WindowProperties::clear_raw_mice 0 1 159 66
/**
 * Removes the raw_mice specification from the properties.
 */
51
inline void WindowProperties::clear_raw_mice(void);

810 8 set_open 0 4 1345 26 WindowProperties::set_open 0 1 160 174
/**
 * Specifies whether the window should be open.  It is legal to create a
 * GraphicsWindow in the closed state, and later request it to open by
 * changing this flag.
 */
50
inline void WindowProperties::set_open(bool open);

811 8 get_open 0 4 1345 26 WindowProperties::get_open 0 1 161 46
/**
 * Returns true if the window is open.
 */
51
inline bool WindowProperties::get_open(void) const;

812 8 has_open 0 4 1345 26 WindowProperties::has_open 0 1 162 57
/**
 * Returns true if set_open() has been specified.
 */
51
inline bool WindowProperties::has_open(void) const;

813 10 clear_open 0 4 1345 28 WindowProperties::clear_open 0 1 163 62
/**
 * Removes the open specification from the properties.
 */
47
inline void WindowProperties::clear_open(void);

814 17 set_cursor_hidden 0 4 1345 35 WindowProperties::set_cursor_hidden 0 1 164 64
/**
 * Specifies whether the mouse cursor should be visible.
 */
68
inline void WindowProperties::set_cursor_hidden(bool cursor_hidden);

815 17 get_cursor_hidden 0 4 1345 35 WindowProperties::get_cursor_hidden 0 1 165 57
/**
 * Returns true if the mouse cursor is invisible.
 */
60
inline bool WindowProperties::get_cursor_hidden(void) const;

816 17 has_cursor_hidden 0 4 1345 35 WindowProperties::has_cursor_hidden 0 1 166 66
/**
 * Returns true if set_cursor_hidden() has been specified.
 */
60
inline bool WindowProperties::has_cursor_hidden(void) const;

817 19 clear_cursor_hidden 0 4 1345 37 WindowProperties::clear_cursor_hidden 0 1 167 71
/**
 * Removes the cursor_hidden specification from the properties.
 */
56
inline void WindowProperties::clear_cursor_hidden(void);

818 17 set_icon_filename 0 4 1345 35 WindowProperties::set_icon_filename 0 1 168 106
/**
 * Specifies the file that contains the icon to associate with the window when
 * it is minimized.
 */
79
inline void WindowProperties::set_icon_filename(Filename const &icon_filename);

819 17 get_icon_filename 0 4 1345 35 WindowProperties::get_icon_filename 0 1 169 64
/**
 * Returns the icon filename associated with the window.
 */
64
inline Filename WindowProperties::get_icon_filename(void) const;

820 17 has_icon_filename 0 4 1345 35 WindowProperties::has_icon_filename 0 1 170 66
/**
 * Returns true if set_icon_filename() has been specified.
 */
60
inline bool WindowProperties::has_icon_filename(void) const;

821 19 clear_icon_filename 0 4 1345 37 WindowProperties::clear_icon_filename 0 1 171 71
/**
 * Removes the icon_filename specification from the properties.
 */
56
inline void WindowProperties::clear_icon_filename(void);

822 19 set_cursor_filename 0 4 1345 37 WindowProperties::set_cursor_filename 0 1 172 134
/**
 * Specifies the file that contains the icon to associate with the mouse
 * cursor when it is within the window (and visible).
 */
83
inline void WindowProperties::set_cursor_filename(Filename const &cursor_filename);

823 19 get_cursor_filename 0 4 1345 37 WindowProperties::get_cursor_filename 0 1 173 70
/**
 * Returns the icon filename associated with the mouse cursor.
 */
66
inline Filename WindowProperties::get_cursor_filename(void) const;

824 19 has_cursor_filename 0 4 1345 37 WindowProperties::has_cursor_filename 0 1 174 68
/**
 * Returns true if set_cursor_filename() has been specified.
 */
62
inline bool WindowProperties::has_cursor_filename(void) const;

825 21 clear_cursor_filename 0 4 1345 39 WindowProperties::clear_cursor_filename 0 1 175 73
/**
 * Removes the cursor_filename specification from the properties.
 */
58
inline void WindowProperties::clear_cursor_filename(void);

826 11 set_z_order 0 4 1345 29 WindowProperties::set_z_order 0 1 176 317
/**
 * Specifies the relative ordering of the window with respect to other
 * windows.  If the z_order is Z_top, the window will always be on top of
 * other windows; if it is Z_bottom, it will always be below other windows.
 * Most windows will want to be Z_normal, which allows the user to control the
 * order.
 */
76
inline void WindowProperties::set_z_order(WindowProperties::ZOrder z_order);

827 11 get_z_order 0 4 1345 29 WindowProperties::get_z_order 0 1 177 40
/**
 * Returns the window's z_order.
 */
74
inline WindowProperties::ZOrder WindowProperties::get_z_order(void) const;

828 11 has_z_order 0 4 1345 29 WindowProperties::has_z_order 0 1 178 82
/**
 * Returns true if the window z_order has been specified, false otherwise.
 */
54
inline bool WindowProperties::has_z_order(void) const;

829 13 clear_z_order 0 4 1345 31 WindowProperties::clear_z_order 0 1 179 65
/**
 * Removes the z_order specification from the properties.
 */
50
inline void WindowProperties::clear_z_order(void);

830 17 set_parent_window 0 4 1345 35 WindowProperties::set_parent_window 0 2 180 181 1266
/**
 * Specifies the window that this window should be attached to.  If this is
 * NULL or unspecified, the window will be created as a toplevel window on the
 * desktop; if this is non-NULL, the window will be bound as a child window to
 * the indicated parent window.
 *
 * You should use GraphicsPipe::make_window_handle() to create an instance of
 * a WindowHandle object given an appropriate OS-specific window handle
 * representation.  Each OS-specific GraphicsPipe class defines a
 * make_window_handle() method that returns an appropriate WindowHandle object
 * to wrap the particular OS-specific representation.
 */

/**
 * Specifies the window that this window should be attached to.
 *
 * This is a deprecated variant on this method, and exists only for backward
 * compatibility.  Future code should use the version of set_parent_window()
 * below that receives a WindowHandle object; that interface is much more
 * robust.
 *
 * In this deprecated variant, the actual value for "parent" is platform-
 * specific.  On Windows, it is the HWND of the parent window, cast to an
 * unsigned integer.  On X11, it is the Window pointer of the parent window,
 * similarly cast.  On OSX, this is the NSWindow pointer, which doesn't appear
 * to work at all.
 */
149
void WindowProperties::set_parent_window(std::size_t parent);
inline void WindowProperties::set_parent_window(WindowHandle *parent_window = nullptr);

831 17 get_parent_window 0 4 1345 35 WindowProperties::get_parent_window 0 1 182 102
/**
 * Returns the parent window specification, or NULL if there is no parent
 * window specified.
 */
69
inline WindowHandle *WindowProperties::get_parent_window(void) const;

832 17 has_parent_window 0 4 1345 35 WindowProperties::has_parent_window 0 1 183 72
/**
 * Checks the S_parent_window specification from the properties.
 */
60
inline bool WindowProperties::has_parent_window(void) const;

833 19 clear_parent_window 0 4 1345 37 WindowProperties::clear_parent_window 0 1 184 73
/**
 * Removes the S_parent_window specification from the properties.
 */
56
inline void WindowProperties::clear_parent_window(void);

834 14 add_properties 0 4 1345 32 WindowProperties::add_properties 0 1 185 123
/**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */
69
void WindowProperties::add_properties(WindowProperties const &other);

835 6 output 0 4 1345 24 WindowProperties::output 0 1 186 123
/**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */
55
void WindowProperties::output(std::ostream &out) const;

836 29 upcast_to_TypedReferenceCount 0 12 1353 44 DisplayRegion::upcast_to_TypedReferenceCount 0 1 242 48
upcast from DisplayRegion to TypedReferenceCount
72
TypedReferenceCount *DisplayRegion::upcast_to_TypedReferenceCount(void);

837 25 downcast_to_DisplayRegion 0 12 1323 46 TypedReferenceCount::downcast_to_DisplayRegion 0 0 50
downcast from TypedReferenceCount to DisplayRegion
68
DisplayRegion *TypedReferenceCount::downcast_to_DisplayRegion(void);

838 24 upcast_to_DrawableRegion 0 12 1353 39 DisplayRegion::upcast_to_DrawableRegion 0 1 243 43
upcast from DisplayRegion to DrawableRegion
62
DrawableRegion *DisplayRegion::upcast_to_DrawableRegion(void);

839 25 downcast_to_DisplayRegion 0 12 1335 41 DrawableRegion::downcast_to_DisplayRegion 0 0 45
downcast from DrawableRegion to DisplayRegion
63
DisplayRegion *DrawableRegion::downcast_to_DisplayRegion(void);

840 14 ~DisplayRegion 0 518 1353 29 DisplayRegion::~DisplayRegion 0 0 10
/**
 *
 */
44
virtual DisplayRegion::~DisplayRegion(void);

841 15 get_num_regions 0 4 1353 30 DisplayRegion::get_num_regions 0 1 187 62
/**
 * Returns the number of regions, see set_num_regions.
 */
54
inline int DisplayRegion::get_num_regions(void) const;

842 15 set_num_regions 0 4 1353 30 DisplayRegion::set_num_regions 0 1 188 313
/**
 * Sets the number of regions that this DisplayRegion indicates.  Usually,
 * this number is 1 (and it is always at least 1), and only the first is used
 * for rendering.  However, if more than one is provided, you may select which
 * one to render into using a geometry shader (gl_ViewportIndex in GLSL).
 */
50
inline void DisplayRegion::set_num_regions(int i);

843 14 get_dimensions 0 4 1353 29 DisplayRegion::get_dimensions 0 1 189 433
/**
 * Retrieves the coordinates of the DisplayRegion's rectangle within its
 * GraphicsOutput.  These numbers will be in the range [0..1].
 */

/**
 * Retrieves the coordinates of the DisplayRegion's rectangle within its
 * GraphicsOutput.  These numbers will be in the range [0..1].
 */

/**
 * Retrieves the coordinates of the DisplayRegion's rectangle within its
 * GraphicsOutput.  These numbers will be in the range [0..1].
 */
297
inline void DisplayRegion::get_dimensions(PN_stdfloat &l, PN_stdfloat &r, PN_stdfloat &b, PN_stdfloat &t) const;
inline void DisplayRegion::get_dimensions(int i, PN_stdfloat &l, PN_stdfloat &r, PN_stdfloat &b, PN_stdfloat &t) const;
inline LVecBase4 DisplayRegion::get_dimensions(int i = 0) const;

844 8 get_left 0 4 1353 23 DisplayRegion::get_left 0 1 190 143
/**
 * Retrieves the x coordinate of the left edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */
60
inline PN_stdfloat DisplayRegion::get_left(int i = 0) const;

845 9 get_right 0 4 1353 24 DisplayRegion::get_right 0 1 191 144
/**
 * Retrieves the x coordinate of the right edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */
61
inline PN_stdfloat DisplayRegion::get_right(int i = 0) const;

846 10 get_bottom 0 4 1353 25 DisplayRegion::get_bottom 0 1 192 145
/**
 * Retrieves the y coordinate of the bottom edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */
62
inline PN_stdfloat DisplayRegion::get_bottom(int i = 0) const;

847 7 get_top 0 4 1353 22 DisplayRegion::get_top 0 1 193 142
/**
 * Retrieves the y coordinate of the top edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */
59
inline PN_stdfloat DisplayRegion::get_top(int i = 0) const;

848 14 set_dimensions 0 4 1353 29 DisplayRegion::set_dimensions 0 4 194 195 196 197 1074
/**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */

/**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */

/**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */

/**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
364
inline void DisplayRegion::set_dimensions(PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);
inline void DisplayRegion::set_dimensions(int i, PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);
inline void DisplayRegion::set_dimensions(LVecBase4 const &dimensions);
virtual void DisplayRegion::set_dimensions(int i, LVecBase4 const &dimensions);

849 10 get_window 0 4 1353 25 DisplayRegion::get_window 0 1 198 131
/**
 * Returns the GraphicsOutput that this DisplayRegion is ultimately associated
 * with, or NULL if no window is associated.
 */
61
inline GraphicsOutput *DisplayRegion::get_window(void) const;

850 8 get_pipe 0 4 1353 23 DisplayRegion::get_pipe 0 1 199 127
/**
 * Returns the GraphicsPipe that this DisplayRegion is ultimately associated
 * with, or NULL if no pipe is associated.
 */
50
GraphicsPipe *DisplayRegion::get_pipe(void) const;

851 9 is_stereo 0 6 1353 24 DisplayRegion::is_stereo 0 1 200 74
/**
 * Returns true if this is a StereoDisplayRegion, false otherwise.
 */
50
virtual bool DisplayRegion::is_stereo(void) const;

852 28 upcast_to_GraphicsOutputBase 0 12 1356 44 GraphicsOutput::upcast_to_GraphicsOutputBase 0 1 329 48
upcast from GraphicsOutput to GraphicsOutputBase
71
GraphicsOutputBase *GraphicsOutput::upcast_to_GraphicsOutputBase(void);

853 26 downcast_to_GraphicsOutput 0 12 1357 46 GraphicsOutputBase::downcast_to_GraphicsOutput 0 0 50
downcast from GraphicsOutputBase to GraphicsOutput
69
GraphicsOutput *GraphicsOutputBase::downcast_to_GraphicsOutput(void);

854 24 upcast_to_DrawableRegion 0 12 1356 40 GraphicsOutput::upcast_to_DrawableRegion 0 1 330 44
upcast from GraphicsOutput to DrawableRegion
63
DrawableRegion *GraphicsOutput::upcast_to_DrawableRegion(void);

855 26 downcast_to_GraphicsOutput 0 12 1335 42 DrawableRegion::downcast_to_GraphicsOutput 0 0 46
downcast from DrawableRegion to GraphicsOutput
65
GraphicsOutput *DrawableRegion::downcast_to_GraphicsOutput(void);

856 15 ~GraphicsOutput 0 518 1356 31 GraphicsOutput::~GraphicsOutput 0 0 10
/**
 *
 */
46
virtual GraphicsOutput::~GraphicsOutput(void);

857 7 get_gsg 0 4 1356 23 GraphicsOutput::get_gsg 0 1 244 306
/**
 * Returns the GSG that is associated with this window.  There is a one-to-one
 * association between windows and GSG's.
 *
 * This may return NULL if the graphics context has not yet been created for
 * the window, e.g.  before the first frame has rendered; or after the window
 * has been closed.
 */
66
inline GraphicsStateGuardian *GraphicsOutput::get_gsg(void) const;

858 8 get_pipe 0 4 1356 24 GraphicsOutput::get_pipe 0 1 245 297
/**
 * Returns the GraphicsPipe that this window is associated with.  It is
 * possible that the GraphicsPipe might have been deleted while an outstanding
 * PT(GraphicsOutput) prevented all of its children windows from also being
 * deleted; in this unlikely case, get_pipe() may return NULL.
 */
58
inline GraphicsPipe *GraphicsOutput::get_pipe(void) const;

859 10 get_engine 0 4 1356 26 GraphicsOutput::get_engine 0 1 246 200
/**
 * Returns the graphics engine that created this output.  Since there is
 * normally only one GraphicsEngine object in an application, this is usually
 * the same as the global GraphicsEngine.
 */
62
inline GraphicsEngine *GraphicsOutput::get_engine(void) const;

860 8 get_name 0 4 1356 24 GraphicsOutput::get_name 0 1 247 78
/**
 * Returns the name that was passed to the GraphicsOutput constructor.
 */
63
inline std::string const &GraphicsOutput::get_name(void) const;

861 11 release_all 0 4 1361 34 GraphicsStateGuardian::release_all 0 1 331 41
/**
 * Releases all prepared objects.
 */
53
inline void GraphicsStateGuardian::release_all(void);

862 20 release_all_textures 0 4 1361 43 GraphicsStateGuardian::release_all_textures 0 1 332 73
/**
 * Frees the resources for all textures associated with this GSG.
 */
61
inline int GraphicsStateGuardian::release_all_textures(void);

863 20 release_all_samplers 0 4 1361 43 GraphicsStateGuardian::release_all_samplers 0 1 333 73
/**
 * Frees the resources for all samplers associated with this GSG.
 */
61
inline int GraphicsStateGuardian::release_all_samplers(void);

864 17 release_all_geoms 0 4 1361 40 GraphicsStateGuardian::release_all_geoms 0 1 334 70
/**
 * Frees the resources for all geoms associated with this GSG.
 */
58
inline int GraphicsStateGuardian::release_all_geoms(void);

865 26 release_all_vertex_buffers 0 4 1361 49 GraphicsStateGuardian::release_all_vertex_buffers 0 1 335 79
/**
 * Frees the resources for all vertex buffers associated with this GSG.
 */
67
inline int GraphicsStateGuardian::release_all_vertex_buffers(void);

866 25 release_all_index_buffers 0 4 1361 48 GraphicsStateGuardian::release_all_index_buffers 0 1 336 78
/**
 * Frees the resources for all index buffers associated with this GSG.
 */
66
inline int GraphicsStateGuardian::release_all_index_buffers(void);

867 26 release_all_shader_buffers 0 4 1361 49 GraphicsStateGuardian::release_all_shader_buffers 0 1 337 78
/**
 * Frees the resources for all index buffers associated with this GSG.
 */
67
inline int GraphicsStateGuardian::release_all_shader_buffers(void);

868 10 set_active 0 4 1361 33 GraphicsStateGuardian::set_active 0 1 338 257
/**
 * Sets the active flag associated with the GraphicsStateGuardian.  If the
 * GraphicsStateGuardian is marked inactive, nothing is rendered.  This is not
 * normally turned off unless there is a problem with the rendering detected
 * at a low level.
 */
59
inline void GraphicsStateGuardian::set_active(bool active);

869 9 is_active 0 4 1361 32 GraphicsStateGuardian::is_active 0 1 339 77
/**
 * Returns the active flag associated with the GraphicsStateGuardian.
 */
57
inline bool GraphicsStateGuardian::is_active(void) const;

870 8 is_valid 0 4 1361 31 GraphicsStateGuardian::is_valid 0 1 340 170
/**
 * Returns true if the GSG has been correctly initialized within a graphics
 * context, false if there has been some problem or it hasn't been initialized
 * yet.
 */
56
inline bool GraphicsStateGuardian::is_valid(void) const;

871 11 needs_reset 0 4 1361 34 GraphicsStateGuardian::needs_reset 0 1 341 64
/**
 * Returns true if the gsg is marked as needing a reset.
 */
59
inline bool GraphicsStateGuardian::needs_reset(void) const;

872 21 set_incomplete_render 0 4 1361 44 GraphicsStateGuardian::set_incomplete_render 0 1 342 962
/**
 * Sets the incomplete_render flag.  When this is true, the frame will be
 * rendered even if some of the geometry or textures in the scene are not
 * available (e.g.  they have been temporarily paged out).  When this is
 * false, the frame will be held up while this data is reloaded.
 *
 * Setting this true allows for a smoother frame rate, but occasionally parts
 * of the frame will be invisible or missing (they will generally come in
 * within a second or two).  Setting this false guarantees that every frame
 * will be complete, but may cause more chugs as things are loaded up at
 * runtime.
 *
 * You may want to set this false during loading screens, to guarantee that
 * all of your assets are available by the time you take the loading screen
 * down.
 *
 * This flag may also be set individually on each DisplayRegion.  It will be
 * considered true for a given DisplayRegion only if it is true on both the
 * GSG and on the DisplayRegion.
 */
81
inline void GraphicsStateGuardian::set_incomplete_render(bool incomplete_render);

873 21 get_incomplete_render 0 6 1361 44 GraphicsStateGuardian::get_incomplete_render 0 0 76
/**
 * Returns the incomplete_render flag.  See set_incomplete_render().
 */
77
virtual inline bool GraphicsStateGuardian::get_incomplete_render(void) const;

874 31 get_effective_incomplete_render 0 6 1361 54 GraphicsStateGuardian::get_effective_incomplete_render 0 0 421
/**
 * Returns true if the GSG is effectively in incomplete_render state,
 * considering both the GSG's incomplete_render and its current
 * DisplayRegion's incomplete_render flags.  It only makes sense to call this
 * during the draw traversal; at other times this return value will be
 * meaningless.
 *
 * See CullTraverser::get_effective_incomplete_render() for this same
 * information during the cull traversal.
 */
87
virtual inline bool GraphicsStateGuardian::get_effective_incomplete_render(void) const;

875 10 set_loader 0 4 1361 33 GraphicsStateGuardian::set_loader 0 1 343 135
/**
 * Sets the Loader object that will be used by this GSG to load textures when
 * necessary, if get_incomplete_render() is true.
 */
62
inline void GraphicsStateGuardian::set_loader(Loader *loader);

876 10 get_loader 0 4 1361 33 GraphicsStateGuardian::get_loader 0 1 344 138
/**
 * Returns the Loader object that will be used by this GSG to load textures
 * when necessary, if get_incomplete_render() is true.
 */
61
inline Loader *GraphicsStateGuardian::get_loader(void) const;

877 20 set_shader_generator 0 4 1361 43 GraphicsStateGuardian::set_shader_generator 0 1 345 111
/**
 * Sets the ShaderGenerator object that will be used by this GSG to generate
 * shaders when necessary.
 */
91
inline void GraphicsStateGuardian::set_shader_generator(ShaderGenerator *shader_generator);

878 20 get_shader_generator 0 4 1361 43 GraphicsStateGuardian::get_shader_generator 0 1 346 114
/**
 * Returns the ShaderGenerator object that will be used by this GSG to
 * generate shaders when necessary.
 */
80
inline ShaderGenerator *GraphicsStateGuardian::get_shader_generator(void) const;

879 8 get_pipe 0 4 1361 31 GraphicsStateGuardian::get_pipe 0 1 347 67
/**
 * Returns the graphics pipe on which this GSG was created.
 */
65
inline GraphicsPipe *GraphicsStateGuardian::get_pipe(void) const;

880 10 get_engine 0 4 1361 33 GraphicsStateGuardian::get_engine 0 1 348 0
62
GraphicsEngine *GraphicsStateGuardian::get_engine(void) const;

881 19 get_threading_model 0 4 1361 42 GraphicsStateGuardian::get_threading_model 0 1 349 72
/**
 * Returns the threading model that was used to create this GSG.
 */
92
inline GraphicsThreadingModel const &GraphicsStateGuardian::get_threading_model(void) const;

882 11 is_hardware 0 4 1361 34 GraphicsStateGuardian::is_hardware 0 1 350 119
/**
 * Returns true if this GSG appears to be hardware-accelerated, or false if it
 * is known to be software only.
 */
59
inline bool GraphicsStateGuardian::is_hardware(void) const;

883 22 get_max_texture_stages 0 4 1361 45 GraphicsStateGuardian::get_max_texture_stages 0 1 351 490
/**
 * Returns the maximum number of simultaneous textures that may be applied to
 * geometry with multitexturing, as supported by this particular GSG.  If you
 * exceed this number, the lowest-priority texture stages will not be applied.
 * Use TextureStage::set_priority() to adjust the relative importance of the
 * different texture stages.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
69
inline int GraphicsStateGuardian::get_max_texture_stages(void) const;

884 28 get_max_3d_texture_dimension 0 4 1361 51 GraphicsStateGuardian::get_max_3d_texture_dimension 0 1 352 325
/**
 * Returns the largest possible texture size in any one dimension for a 3-d
 * texture, or -1 if there is no particular limit.  Returns 0 if 3-d textures
 * are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
75
inline int GraphicsStateGuardian::get_max_3d_texture_dimension(void) const;

885 31 get_max_2d_texture_array_layers 0 4 1361 54 GraphicsStateGuardian::get_max_2d_texture_array_layers 0 1 353 308
//z axis

//z axis

/**
 * Returns the largest possible number of pages, or -1 if there is no
 * particular limit.  Returns 0 if 2-d texture arrays not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
78
inline int GraphicsStateGuardian::get_max_2d_texture_array_layers(void) const;

886 26 get_max_cube_map_dimension 0 4 1361 49 GraphicsStateGuardian::get_max_cube_map_dimension 0 1 354 355
//z axis

//z axis

/**
 * Returns the largest possible texture size in any one dimension for a cube
 * map texture, or -1 if there is no particular limit.  Returns 0 if cube map
 * textures are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
73
inline int GraphicsStateGuardian::get_max_cube_map_dimension(void) const;

887 27 get_max_buffer_texture_size 0 4 1361 50 GraphicsStateGuardian::get_max_buffer_texture_size 0 1 355 295
/**
 * Returns the largest possible buffer texture size, or -1 if there is no
 * particular limit.  Returns 0 if cube map textures are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
74
inline int GraphicsStateGuardian::get_max_buffer_texture_size(void) const;

888 28 get_supports_texture_combine 0 4 1361 51 GraphicsStateGuardian::get_supports_texture_combine 0 1 356 279
/**
 * Returns true if this particular GSG can use the TextureStage::M_combine
 * mode, which includes all of the texture blend modes specified by
 * set_combine_rgb() and/or set_combine_alpha().  If this is false, you must
 * limit yourself to using the simpler blend modes.
 */
76
inline bool GraphicsStateGuardian::get_supports_texture_combine(void) const;

889 33 get_supports_texture_saved_result 0 4 1361 56 GraphicsStateGuardian::get_supports_texture_saved_result 0 1 357 185
/**
 * Returns true if this GSG can use the TextureStage::CS_last_saved_result
 * source, which allows you to save the result of a TextureStage and re-use it
 * for multiple inputs.
 */
81
inline bool GraphicsStateGuardian::get_supports_texture_saved_result(void) const;

890 25 get_supports_texture_dot3 0 4 1361 48 GraphicsStateGuardian::get_supports_texture_dot3 0 1 358 107
/**
 * Returns true if this GSG can use the TextureStage::CM_dot3_rgb or
 * CM_dot3_rgba combine modes.
 */
73
inline bool GraphicsStateGuardian::get_supports_texture_dot3(void) const;

891 23 get_supports_3d_texture 0 4 1361 46 GraphicsStateGuardian::get_supports_3d_texture 0 1 359 73
/**
 * Returns true if this GSG can render 3-d (volumetric) textures.
 */
71
inline bool GraphicsStateGuardian::get_supports_3d_texture(void) const;

892 29 get_supports_2d_texture_array 0 4 1361 52 GraphicsStateGuardian::get_supports_2d_texture_array 0 1 360 66
/**
 * Returns true if this GSG can render 2-d textures array.
 */
77
inline bool GraphicsStateGuardian::get_supports_2d_texture_array(void) const;

893 21 get_supports_cube_map 0 4 1361 44 GraphicsStateGuardian::get_supports_cube_map 0 1 361 65
/**
 * Returns true if this GSG can render cube map textures.
 */
69
inline bool GraphicsStateGuardian::get_supports_cube_map(void) const;

894 27 get_supports_buffer_texture 0 4 1361 50 GraphicsStateGuardian::get_supports_buffer_texture 0 1 362 63
/**
 * Returns true if this GSG can render buffer textures.
 */
75
inline bool GraphicsStateGuardian::get_supports_buffer_texture(void) const;

895 27 get_supports_cube_map_array 0 4 1361 50 GraphicsStateGuardian::get_supports_cube_map_array 0 1 363 63
/**
 * Returns true if this GSG can render cube map arrays.
 */
75
inline bool GraphicsStateGuardian::get_supports_cube_map_array(void) const;

896 25 get_supports_tex_non_pow2 0 4 1361 48 GraphicsStateGuardian::get_supports_tex_non_pow2 0 1 364 79
/**
 * Returns true if this GSG can handle non power of two sized textures.
 */
73
inline bool GraphicsStateGuardian::get_supports_tex_non_pow2(void) const;

897 31 get_supports_compressed_texture 0 4 1361 54 GraphicsStateGuardian::get_supports_compressed_texture 0 1 365 149
/**
 * Returns true if this GSG can compress textures as it loads them into
 * texture memory, and/or accept pre-compressed textures for storing.
 */
79
inline bool GraphicsStateGuardian::get_supports_compressed_texture(void) const;

898 14 get_max_lights 0 4 1361 37 GraphicsStateGuardian::get_max_lights 0 1 366 278
/**
 * Returns the maximum number of simultaneous lights that may be rendered on
 * geometry, or -1 if there is no particular limit.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
61
inline int GraphicsStateGuardian::get_max_lights(void) const;

899 19 get_max_clip_planes 0 4 1361 42 GraphicsStateGuardian::get_max_clip_planes 0 1 367 282
/**
 * Returns the maximum number of simultaneous clip planes that may be applied
 * to geometry, or -1 if there is no particular limit.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
66
inline int GraphicsStateGuardian::get_max_clip_planes(void) const;

900 25 get_max_vertex_transforms 0 4 1361 48 GraphicsStateGuardian::get_max_vertex_transforms 0 1 368 465
/**
 * Returns the maximum number of transform matrices that may be simultaneously
 * used to transform any one vertex by the graphics hardware.  If this number
 * is 0, then the hardware (or the graphics backend) doesn't support soft-
 * skinned vertices (in which case Panda will animate the vertices in
 * software).
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
72
inline int GraphicsStateGuardian::get_max_vertex_transforms(void) const;

901 32 get_max_vertex_transform_indices 0 4 1361 55 GraphicsStateGuardian::get_max_vertex_transform_indices 0 1 369 450
/**
 * Returns the maximum number of transforms there may be in a single
 * TransformTable for this graphics hardware.  If this number is 0 (but
 * get_max_transforms() is nonzero), then the graphics hardware (or API)
 * doesn't support indexed transforms, but can support direct transform
 * references.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
79
inline int GraphicsStateGuardian::get_max_vertex_transform_indices(void) const;

902 25 get_copy_texture_inverted 0 4 1361 48 GraphicsStateGuardian::get_copy_texture_inverted 0 1 370 574
/**
 * Returns true if this particular GSG has the property that any framebuffer-
 * to-texture copy results in a texture that is upside-down and backwards from
 * Panda's usual convention; that is, it copies into a texture from the bottom
 * up instead of from the top down.
 *
 * If this is true, then on offscreen GraphicsBuffer created for the purposes
 * of rendering into a texture should be created with the invert flag set
 * true, to compensate.  Panda will do this automatically if you create an
 * offscreen buffer using GraphicsOutput::make_texture_buffer().
 */
73
inline bool GraphicsStateGuardian::get_copy_texture_inverted(void) const;

903 28 get_supports_generate_mipmap 0 4 1361 51 GraphicsStateGuardian::get_supports_generate_mipmap 0 1 371 257
/**
 * Returns true if this particular GSG can generate mipmaps for a texture
 * automatically, or if they must be generated in software.  If this is true,
 * then mipmaps can safely be enabled for rendered textures (e.g.  using the
 * MultitexReducer).
 */
76
inline bool GraphicsStateGuardian::get_supports_generate_mipmap(void) const;

904 26 get_supports_depth_texture 0 4 1361 49 GraphicsStateGuardian::get_supports_depth_texture 0 1 372 246
/**
 * Returns true if this particular GSG supports textures whose format is
 * F_depth_stencil.  This returns true if the GSG supports GL_DEPTH_COMPONENT
 * textures, which are considered a limited but still valid case of
 * F_depth_stencil.
 */
74
inline bool GraphicsStateGuardian::get_supports_depth_texture(void) const;

905 26 get_supports_depth_stencil 0 4 1361 49 GraphicsStateGuardian::get_supports_depth_stencil 0 1 373 192
/**
 * Returns true if this particular GSG supports textures whose format is
 * F_depth_stencil.  This only returns true if the GSG supports the full
 * packed depth-stencil functionality.
 */
74
inline bool GraphicsStateGuardian::get_supports_depth_stencil(void) const;

906 30 get_supports_luminance_texture 0 4 1361 53 GraphicsStateGuardian::get_supports_luminance_texture 0 1 374 75
/**
 * Returns true if this particular GSG supports luminance textures.
 */
78
inline bool GraphicsStateGuardian::get_supports_luminance_texture(void) const;

907 28 get_supports_sampler_objects 0 4 1361 51 GraphicsStateGuardian::get_supports_sampler_objects 0 1 375 345
/**
 * Returns true if this particular GSG supports the use of sampler objects to
 * record texture sampling parameters separately from the texture objects.
 * This doesn't really affect functionality, but if this is false, it may mean
 * that using the same texture with different SamplerState objects will result
 * in reduced performance.
 */
76
inline bool GraphicsStateGuardian::get_supports_sampler_objects(void) const;

908 26 get_supports_basic_shaders 0 4 1361 49 GraphicsStateGuardian::get_supports_basic_shaders 0 1 376 79
/**
 * Returns true if this particular GSG supports arbfp1+arbvp1 or above.
 */
74
inline bool GraphicsStateGuardian::get_supports_basic_shaders(void) const;

909 29 get_supports_geometry_shaders 0 4 1361 52 GraphicsStateGuardian::get_supports_geometry_shaders 0 1 377 73
/**
 * Returns true if this particular GSG supports geometry shaders.
 */
77
inline bool GraphicsStateGuardian::get_supports_geometry_shaders(void) const;

910 33 get_supports_tessellation_shaders 0 4 1361 56 GraphicsStateGuardian::get_supports_tessellation_shaders 0 1 378 76
/**
 * Returns true if this particular GSG supports tesselation shaders.
 */
81
inline bool GraphicsStateGuardian::get_supports_tessellation_shaders(void) const;

911 28 get_supports_compute_shaders 0 4 1361 51 GraphicsStateGuardian::get_supports_compute_shaders 0 1 379 72
/**
 * Returns true if this particular GSG supports compute shaders.
 */
76
inline bool GraphicsStateGuardian::get_supports_compute_shaders(void) const;

912 17 get_supports_glsl 0 4 1361 40 GraphicsStateGuardian::get_supports_glsl 0 1 380 69
/**
 * Returns true if this particular GSG supports GLSL shaders.
 */
65
inline bool GraphicsStateGuardian::get_supports_glsl(void) const;

913 20 get_supports_stencil 0 4 1361 43 GraphicsStateGuardian::get_supports_stencil 0 1 381 79
/**
 * Returns true if this particular GSG supports stencil buffers at all.
 */
68
inline bool GraphicsStateGuardian::get_supports_stencil(void) const;

914 30 get_supports_two_sided_stencil 0 4 1361 53 GraphicsStateGuardian::get_supports_two_sided_stencil 0 1 382 153
/**
 * Returns true if this particular GSG supports two sided stencil: different
 * stencil settings for the front and back side of the same polygon.
 */
78
inline bool GraphicsStateGuardian::get_supports_two_sided_stencil(void) const;

915 32 get_supports_geometry_instancing 0 4 1361 55 GraphicsStateGuardian::get_supports_geometry_instancing 0 1 383 206
/**
 * Returns true if this particular GSG supports hardware geometry instancing:
 * the ability to render multiple copies of a model.  In OpenGL, this is done
 * using the EXT_draw_instanced extension.
 */
80
inline bool GraphicsStateGuardian::get_supports_geometry_instancing(void) const;

916 26 get_supports_indirect_draw 0 4 1361 49 GraphicsStateGuardian::get_supports_indirect_draw 0 1 384 116
/**
 * Returns true if this particular GSG supports draw calls for which the
 * information comes from a buffer.
 */
74
inline bool GraphicsStateGuardian::get_supports_indirect_draw(void) const;

917 28 get_supports_occlusion_query 0 4 1361 51 GraphicsStateGuardian::get_supports_occlusion_query 0 1 385 266
/**
 * Returns true if this GSG supports an occlusion query.  If this is true,
 * then begin_occlusion_query() and end_occlusion_query() may be called to
 * bracket a sequence of draw_triangles() (or whatever) calls to measure
 * pixels that pass the depth test.
 */
76
inline bool GraphicsStateGuardian::get_supports_occlusion_query(void) const;

918 24 get_supports_timer_query 0 4 1361 47 GraphicsStateGuardian::get_supports_timer_query 0 1 386 59
/**
 * Returns true if this GSG supports a timer query.
 */
72
inline bool GraphicsStateGuardian::get_supports_timer_query(void) const;

919 24 get_timer_queries_active 0 4 1361 47 GraphicsStateGuardian::get_timer_queries_active 0 1 387 75
/**
 * Returns true if timer queries are currently enabled on this GSG.
 */
72
inline bool GraphicsStateGuardian::get_timer_queries_active(void) const;

920 21 get_max_color_targets 0 4 1361 44 GraphicsStateGuardian::get_max_color_targets 0 1 388 488
/**
 * Returns the maximum number of simultaneous color textures that may be
 * attached for render-to-texture, as supported by this particular GSG.  If
 * you exceed this number, the lowest-priority render targets will not be
 * applied.  Use RenderTarget::set_priority() to adjust the relative
 * importance of the different render targets.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
68
inline int GraphicsStateGuardian::get_max_color_targets(void) const;

921 39 get_maximum_simultaneous_render_targets 0 4 1361 62 GraphicsStateGuardian::get_maximum_simultaneous_render_targets 0 1 389 99
/**
 * Deprecated.  Use get_max_color_targets() instead, which returns the exact
 * same value.
 */
86
inline int GraphicsStateGuardian::get_maximum_simultaneous_render_targets(void) const;

922 33 get_supports_dual_source_blending 0 4 1361 56 GraphicsStateGuardian::get_supports_dual_source_blending 0 1 390 121
/**
 * Returns true if dual source (incoming1_color and incoming1_alpha) blend
 * operands are supported by this GSG.
 */
81
inline bool GraphicsStateGuardian::get_supports_dual_source_blending(void) const;

923 26 get_max_vertices_per_array 0 6 1361 49 GraphicsStateGuardian::get_max_vertices_per_array 0 0 129
/**
 * Returns the maximum number of vertices that should be put into any one
 * GeomVertexData object for use with this GSG.
 */
81
virtual inline int GraphicsStateGuardian::get_max_vertices_per_array(void) const;

924 30 get_max_vertices_per_primitive 0 6 1361 53 GraphicsStateGuardian::get_max_vertices_per_primitive 0 0 134
/**
 * Returns the maximum number of vertex indices that should be put into any
 * one GeomPrimitive object for use with this GSG.
 */
85
virtual inline int GraphicsStateGuardian::get_max_vertices_per_primitive(void) const;

925 25 get_max_texture_dimension 0 6 1361 48 GraphicsStateGuardian::get_max_texture_dimension 0 0 279
/**
 * Returns the largest possible texture size in any one dimension supported by
 * the GSG, or -1 if there is no particular limit.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */
80
virtual inline int GraphicsStateGuardian::get_max_texture_dimension(void) const;

926 25 get_supports_texture_srgb 0 6 1361 48 GraphicsStateGuardian::get_supports_texture_srgb 0 0 61
/**
 * Returns true if this GSG can handle sRGB textures.
 */
81
virtual inline bool GraphicsStateGuardian::get_supports_texture_srgb(void) const;

927 24 get_supports_multisample 0 6 1361 47 GraphicsStateGuardian::get_supports_multisample 0 0 0
73
virtual bool GraphicsStateGuardian::get_supports_multisample(void) const;

928 26 get_supports_shadow_filter 0 6 1361 49 GraphicsStateGuardian::get_supports_shadow_filter 0 0 104
/**
 * Returns true if this particular GSG supports the filter mode FT_shadow for
 * depth textures.
 */
82
virtual inline bool GraphicsStateGuardian::get_supports_shadow_filter(void) const;

929 17 get_supports_hlsl 0 6 1361 40 GraphicsStateGuardian::get_supports_hlsl 0 0 69
/**
 * Returns true if this particular GSG supports HLSL shaders.
 */
73
virtual inline bool GraphicsStateGuardian::get_supports_hlsl(void) const;

930 16 get_shader_model 0 4 1361 39 GraphicsStateGuardian::get_shader_model 0 1 391 34
/**
 * Returns the ShaderModel
 */
94
inline GraphicsStateGuardian::ShaderModel GraphicsStateGuardian::get_shader_model(void) const;

931 16 set_shader_model 0 4 1361 39 GraphicsStateGuardian::set_shader_model 0 1 392 144
/**
 * Sets the ShaderModel.  This will override the auto- detected shader model
 * during GSG reset.  Useful for testing lower-end shaders.
 */
101
inline void GraphicsStateGuardian::set_shader_model(GraphicsStateGuardian::ShaderModel shader_model);

932 23 get_supports_cg_profile 0 6 1361 46 GraphicsStateGuardian::get_supports_cg_profile 0 1 393 0
91
virtual bool GraphicsStateGuardian::get_supports_cg_profile(std::string const &name) const;

933 28 get_color_scale_via_lighting 0 4 1361 51 GraphicsStateGuardian::get_color_scale_via_lighting 0 1 394 214
/**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) set color and/or color scale using materials and/or ambient
 * lights, or false if we need to actually munge the color.
 */
76
inline bool GraphicsStateGuardian::get_color_scale_via_lighting(void) const;

934 27 get_alpha_scale_via_texture 0 4 1361 50 GraphicsStateGuardian::get_alpha_scale_via_texture 0 2 395 396 520
/**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) an alpha scale via an additional Texture layer, or false if we
 * need to actually munge the alpha.
 */

/**
 * This variant of get_alpha_scale_via_texture() answers the question of
 * whether the GSG can implement an alpha scale via an additional Texture
 * layer, considering the current TextureAttrib that will be in effect.  This
 * considers whether there is at least one additional texture slot available
 * on the GSG.
 */
178
inline bool GraphicsStateGuardian::get_alpha_scale_via_texture(void) const;
inline bool GraphicsStateGuardian::get_alpha_scale_via_texture(TextureAttrib const *tex_attrib) const;

935 23 get_runtime_color_scale 0 4 1361 46 GraphicsStateGuardian::get_runtime_color_scale 0 1 397 203
/**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) set color and/or color scale directly, without requiring any
 * munging of vertices or tricks with lighting.
 */
71
inline bool GraphicsStateGuardian::get_runtime_color_scale(void) const;

936 29 get_alpha_scale_texture_stage 0 4 1361 52 GraphicsStateGuardian::get_alpha_scale_texture_stage 0 1 398 128
/**
 * Returns the TextureStage that will be used to apply an alpha scale, if
 * get_alpha_scale_via_texture() returns true.
 */
87
static inline TextureStage *GraphicsStateGuardian::get_alpha_scale_texture_stage(void);

937 21 set_coordinate_system 0 4 1361 44 GraphicsStateGuardian::set_coordinate_system 0 1 399 0
71
void GraphicsStateGuardian::set_coordinate_system(CoordinateSystem cs);

938 21 get_coordinate_system 0 4 1361 44 GraphicsStateGuardian::get_coordinate_system 0 1 400 179
/**
 * Returns the coordinate system in effect on this particular gsg.  Normally,
 * this will be the default coordinate system, but it might be set differently
 * at runtime.
 */
81
inline CoordinateSystem GraphicsStateGuardian::get_coordinate_system(void) const;

939 30 get_internal_coordinate_system 0 6 1361 53 GraphicsStateGuardian::get_internal_coordinate_system 0 1 401 0
91
virtual CoordinateSystem GraphicsStateGuardian::get_internal_coordinate_system(void) const;

940 20 get_prepared_objects 0 6 1361 43 GraphicsStateGuardian::get_prepared_objects 0 1 402 0
83
virtual PreparedGraphicsObjects *GraphicsStateGuardian::get_prepared_objects(void);

941 9 set_gamma 0 6 1361 32 GraphicsStateGuardian::set_gamma 0 1 403 0
65
virtual bool GraphicsStateGuardian::set_gamma(PN_stdfloat gamma);

942 9 get_gamma 0 4 1361 32 GraphicsStateGuardian::get_gamma 0 1 404 0
57
PN_stdfloat GraphicsStateGuardian::get_gamma(void) const;

943 13 restore_gamma 0 6 1361 36 GraphicsStateGuardian::restore_gamma 0 1 405 0
56
virtual void GraphicsStateGuardian::restore_gamma(void);

944 28 set_texture_quality_override 0 4 1361 51 GraphicsStateGuardian::set_texture_quality_override 0 1 406 405
/**
 * Specifies the global quality_level to be imposed for all Textures rendered
 * by this GSG.  This overrides the value set on individual textures via
 * Texture::set_quality_level().  Set this to Texture::QL_default in order to
 * allow the individual texture quality levels to be respected.
 *
 * This is mainly useful for the tinydisplay software renderer.  See
 * Texture::set_quality_level().
 */
101
inline void GraphicsStateGuardian::set_texture_quality_override(Texture::QualityLevel quality_level);

945 28 get_texture_quality_override 0 4 1361 51 GraphicsStateGuardian::get_texture_quality_override 0 1 407 203
/**
 * Returns the global quality_level override specified by
 * set_texture_quality_override.
 *
 * This is mainly useful for the tinydisplay software renderer.  See
 * Texture::set_quality_level().
 */
93
inline Texture::QualityLevel GraphicsStateGuardian::get_texture_quality_override(void) const;

946 21 get_prepared_textures 0 4 1361 44 GraphicsStateGuardian::get_prepared_textures 0 1 408 0
67
PyObject *GraphicsStateGuardian::get_prepared_textures(void) const;

947 26 traverse_prepared_textures 0 4 1361 49 GraphicsStateGuardian::traverse_prepared_textures 0 0 0
121
void GraphicsStateGuardian::traverse_prepared_textures(GraphicsStateGuardian::TextureCallback *func, void *callback_arg);

948 17 set_flash_texture 0 4 1361 40 GraphicsStateGuardian::set_flash_texture 0 1 409 0
60
void GraphicsStateGuardian::set_flash_texture(Texture *tex);

949 19 clear_flash_texture 0 4 1361 42 GraphicsStateGuardian::clear_flash_texture 0 1 410 0
54
void GraphicsStateGuardian::clear_flash_texture(void);

950 17 get_flash_texture 0 4 1361 40 GraphicsStateGuardian::get_flash_texture 0 1 411 0
62
Texture *GraphicsStateGuardian::get_flash_texture(void) const;

951 13 has_extension 0 6 1361 36 GraphicsStateGuardian::has_extension 0 1 412 0
86
virtual bool GraphicsStateGuardian::has_extension(std::string const &extension) const;

952 17 get_driver_vendor 0 6 1361 40 GraphicsStateGuardian::get_driver_vendor 0 1 413 0
67
virtual std::string GraphicsStateGuardian::get_driver_vendor(void);

953 19 get_driver_renderer 0 6 1361 42 GraphicsStateGuardian::get_driver_renderer 0 1 414 0
69
virtual std::string GraphicsStateGuardian::get_driver_renderer(void);

954 18 get_driver_version 0 6 1361 41 GraphicsStateGuardian::get_driver_version 0 1 415 0
68
virtual std::string GraphicsStateGuardian::get_driver_version(void);

955 24 get_driver_version_major 0 6 1361 47 GraphicsStateGuardian::get_driver_version_major 0 1 416 0
66
virtual int GraphicsStateGuardian::get_driver_version_major(void);

956 24 get_driver_version_minor 0 6 1361 47 GraphicsStateGuardian::get_driver_version_minor 0 1 417 0
66
virtual int GraphicsStateGuardian::get_driver_version_minor(void);

957 31 get_driver_shader_version_major 0 6 1361 54 GraphicsStateGuardian::get_driver_shader_version_major 0 1 418 0
73
virtual int GraphicsStateGuardian::get_driver_shader_version_major(void);

958 31 get_driver_shader_version_minor 0 6 1361 54 GraphicsStateGuardian::get_driver_shader_version_minor 0 1 419 0
73
virtual int GraphicsStateGuardian::get_driver_shader_version_minor(void);

959 9 set_scene 0 4 1361 32 GraphicsStateGuardian::set_scene 0 1 420 0
63
bool GraphicsStateGuardian::set_scene(SceneSetup *scene_setup);

960 9 get_scene 0 6 1361 32 GraphicsStateGuardian::get_scene 0 1 421 0
71
virtual SceneSetup *GraphicsStateGuardian::get_scene(void) const final;

961 11 begin_scene 0 6 1361 34 GraphicsStateGuardian::begin_scene 0 1 422 0
54
virtual bool GraphicsStateGuardian::begin_scene(void);

962 9 end_scene 0 6 1361 32 GraphicsStateGuardian::end_scene 0 1 423 0
52
virtual void GraphicsStateGuardian::end_scene(void);

963 14 get_class_type 0 4 1361 37 GraphicsStateGuardian::get_class_type 0 1 424 0
62
static TypeHandle GraphicsStateGuardian::get_class_type(void);

964 14 GraphicsEngine 0 260 1378 30 GraphicsEngine::GraphicsEngine 0 1 425 187
/**
 * Creates a new GraphicsEngine object.  The Pipeline is normally left to
 * default to NULL, which indicates the global render pipeline, but it may be
 * any Pipeline you choose.
 */
70
explicit GraphicsEngine::GraphicsEngine(Pipeline *pipeline = nullptr);

965 19 set_threading_model 0 4 1378 35 GraphicsEngine::set_threading_model 0 1 426 171
/**
 * Specifies how future objects created via make_gsg(), make_buffer(), and
 * make_output() will be threaded.  This does not affect any already-created
 * objects.
 */
88
void GraphicsEngine::set_threading_model(GraphicsThreadingModel const &threading_model);

966 19 get_threading_model 0 4 1378 35 GraphicsEngine::get_threading_model 0 1 427 109
/**
 * Returns the threading model that will be applied to future objects.  See
 * set_threading_model().
 */
71
GraphicsThreadingModel GraphicsEngine::get_threading_model(void) const;

967 22 GraphicsThreadingModel 0 260 1380 46 GraphicsThreadingModel::GraphicsThreadingModel 0 2 ************
/**
 *
 */

/**
 * The threading model accepts a string representing the names of the two
 * threads that will process cull and draw for the given window, separated by
 * a slash.  The names are completely arbitrary and are used only to
 * differentiate threads.  The two names may be the same, meaning the same
 * thread, or each may be the empty string, which represents the previous
 * thread.
 *
 * Thus, for example, "cull/draw" indicates that the window will be culled in
 * a thread called "cull", and drawn in a separate thread called "draw".
 * "draw/draw" or simply "draw" indicates the window will be culled and drawn
 * in the same thread, "draw". On the other hand, "/draw" indicates the thread
 * will be culled in the main, or app thread, and drawn in a separate thread
 * named "draw".  The empty string, "" or "/", indicates the thread will be
 * culled and drawn in the main thread; that is to say, a single-process
 * model.
 *
 * Finally, if the threading model begins with a "-" character, then cull and
 * draw are run simultaneously, in the same thread, with no binning or state
 * sorting.  It simplifies the cull process but it forces the scene to render
 * in scene graph order; state sorting and alpha sorting is lost.
 */
175
GraphicsThreadingModel::GraphicsThreadingModel(std::string const &model = string());
inline GraphicsThreadingModel::GraphicsThreadingModel(GraphicsThreadingModel const &copy);

968 10 operator = 0 4 1380 34 GraphicsThreadingModel::operator = 0 1 456 0
83
inline void GraphicsThreadingModel::operator =(GraphicsThreadingModel const &copy);

969 9 get_model 0 4 1380 33 GraphicsThreadingModel::get_model 0 1 457 90
/**
 * Returns the string that describes the threading model.  See the
 * constructor.
 */
58
std::string GraphicsThreadingModel::get_model(void) const;

970 13 get_cull_name 0 4 1380 37 GraphicsThreadingModel::get_cull_name 0 1 458 81
/**
 * Returns the name of the thread that will handle culling in this model.
 */
76
inline std::string const &GraphicsThreadingModel::get_cull_name(void) const;

971 13 set_cull_name 0 4 1380 37 GraphicsThreadingModel::set_cull_name 0 1 459 209
/**
 * Changes the name of the thread that will handle culling in this model.
 * This won't change any windows that were already created with this model;
 * this only has an effect on newly-opened windows.
 */
80
inline void GraphicsThreadingModel::set_cull_name(std::string const &cull_name);

972 14 get_cull_stage 0 4 1380 38 GraphicsThreadingModel::get_cull_stage 0 1 460 181
/**
 * Returns the pipeline stage from which the cull thread should access data.
 * This will be 0 if the cull is run in the same thread as app, or 1 if it is
 * its own thread.
 */
62
inline int GraphicsThreadingModel::get_cull_stage(void) const;

973 13 get_draw_name 0 4 1380 37 GraphicsThreadingModel::get_draw_name 0 1 461 135
/**
 * Returns the name of the thread that will handle sending the actual graphics
 * primitives to the graphics API in this model.
 */
76
inline std::string const &GraphicsThreadingModel::get_draw_name(void) const;

974 13 set_draw_name 0 4 1380 37 GraphicsThreadingModel::set_draw_name 0 1 462 209
/**
 * Changes the name of the thread that will handle drawing in this model.
 * This won't change any windows that were already created with this model;
 * this only has an effect on newly-opened windows.
 */
80
inline void GraphicsThreadingModel::set_draw_name(std::string const &cull_name);

975 14 get_draw_stage 0 4 1380 38 GraphicsThreadingModel::get_draw_stage 0 1 463 251
/**
 * Returns the pipeline stage from which the draw thread should access data.
 * This will be the same value as get_cull_stage() if cull and draw are run in
 * the same thread, or one more than that value if draw should be in its own
 * thread.
 */
62
inline int GraphicsThreadingModel::get_draw_stage(void) const;

976 16 get_cull_sorting 0 4 1380 40 GraphicsThreadingModel::get_cull_sorting 0 1 464 136
/**
 * Returns true if the model involves a separate cull pass, or false if
 * culling happens implicitly, at the same time as draw.
 */
65
inline bool GraphicsThreadingModel::get_cull_sorting(void) const;

977 16 set_cull_sorting 0 4 1380 40 GraphicsThreadingModel::set_cull_sorting 0 1 465 230
/**
 * Changes the flag that indicates whether the threading model involves a
 * separate cull pass.  This won't change any windows that were already
 * created with this model; this only has an effect on newly-opened windows.
 */
72
inline void GraphicsThreadingModel::set_cull_sorting(bool cull_sorting);

978 18 is_single_threaded 0 4 1380 42 GraphicsThreadingModel::is_single_threaded 0 1 466 110
/**
 * Returns true if the threading model is a single-threaded model, or false if
 * it involves threads.
 */
67
inline bool GraphicsThreadingModel::is_single_threaded(void) const;

979 10 is_default 0 4 1380 34 GraphicsThreadingModel::is_default 0 1 467 124
/**
 * Returns true if the threading model is the default, cull-then-draw single-
 * threaded model, or false otherwise.
 */
59
inline bool GraphicsThreadingModel::is_default(void) const;

980 6 output 0 4 1380 30 GraphicsThreadingModel::output 0 1 468 10
/**
 *
 */
68
inline void GraphicsThreadingModel::output(std::ostream &out) const;

981 23 ~GraphicsThreadingModel 0 516 1380 47 GraphicsThreadingModel::~GraphicsThreadingModel 0 0 0
54
GraphicsThreadingModel::~GraphicsThreadingModel(void);

982 15 get_render_lock 0 4 1378 31 GraphicsEngine::get_render_lock 0 1 428 241
/**
 * Returns a ReMutex object that is held by the GraphicsEngine during the
 * entire call to render_frame().  While you hold this lock you can be
 * confident that no part of the frame will be rendered (at least by the app
 * thread).
 */
66
inline ReMutex const &GraphicsEngine::get_render_lock(void) const;

983 13 set_auto_flip 0 4 1378 29 GraphicsEngine::set_auto_flip 0 1 429 559
/**
 * Set this flag true to indicate the GraphicsEngine should automatically
 * cause windows to sync and flip as soon as they have finished drawing,
 * rather than waiting for all of the windows to finish drawing first so they
 * can flip together.
 *
 * This only affects the timing of when the flip occurs.  If this is true (the
 * default), the flip occurs before render_frame() returns.  If this is false,
 * the flip occurs whenever flip_frame() is called, or at the beginning of the
 * next call to render_frame(), if flip_frame() is never called.
 */
58
inline void GraphicsEngine::set_auto_flip(bool auto_flip);

984 13 get_auto_flip 0 4 1378 29 GraphicsEngine::get_auto_flip 0 1 430 82
/**
 * Returns the current setting for the auto-flip flag.  See set_auto_flip.
 */
54
inline bool GraphicsEngine::get_auto_flip(void) const;

985 15 set_portal_cull 0 4 1378 31 GraphicsEngine::set_portal_cull 0 1 431 91
/**
 * Set this flag true to indicate the GraphicsEngine should start portal
 * culling
 */
56
inline void GraphicsEngine::set_portal_cull(bool value);

986 15 get_portal_cull 0 4 1378 31 GraphicsEngine::get_portal_cull 0 1 432 67
/**
 * Returns the current setting for the portal culling flag.
 */
56
inline bool GraphicsEngine::get_portal_cull(void) const;

987 18 set_default_loader 0 4 1378 34 GraphicsEngine::set_default_loader 0 1 433 147
/**
 * Sets the Loader object that will be assigned to every GSG created with this
 * GraphicsEngine.  See GraphicsStateGuardian::set_loader().
 */
63
inline void GraphicsEngine::set_default_loader(Loader *loader);

988 18 get_default_loader 0 4 1378 34 GraphicsEngine::get_default_loader 0 1 434 150
/**
 * Returns the Loader object that will be assigned to every GSG created with
 * this GraphicsEngine.  See GraphicsStateGuardian::set_loader().
 */
62
inline Loader *GraphicsEngine::get_default_loader(void) const;

989 11 make_output 0 4 1378 27 GraphicsEngine::make_output 0 1 435 0
252
GraphicsOutput *GraphicsEngine::make_output(GraphicsPipe *pipe, std::string const &name, int sort, FrameBufferProperties const &fb_prop, WindowProperties const &win_prop, int flags, GraphicsStateGuardian *gsg = nullptr, GraphicsOutput *host = nullptr);

990 11 make_buffer 0 4 1378 27 GraphicsEngine::make_buffer 0 2 436 437 1207
// Syntactic shorthand versions of make_output

/**
 * Syntactic shorthand for make_output.  This is the preferred way to create
 * an offscreen buffer, when you already have an onscreen window or another
 * buffer to start with.  For the first parameter, pass an existing
 * GraphicsOutput object, e.g.  the main window; this allows the buffer to
 * adapt itself to that window's framebuffer properties, and allows maximum
 * sharing of resources.
 */

/**
 * Syntactic shorthand for make_output.  This flavor accepts a GSG rather than
 * a GraphicsOutput as the first parameter, which is too limiting and
 * disallows the possibility of creating a ParasiteBuffer if the user's
 * graphics hardware prefers that.  It also attempts to request specific
 * framebuffer properties and may therefore do a poorer job of sharing the GSG
 * between the old buffer and the new.
 *
 * For these reasons, this variant is a poor choice unless you are creating an
 * offscreen buffer for the first time, without an onscreen window already in
 * existence.  If you already have an onscreen window, you should use the
 * other flavor of make_buffer() instead, which accepts a GraphicsOutput as
 * the first parameter.
 */
271
inline GraphicsOutput *GraphicsEngine::make_buffer(GraphicsOutput *host, std::string const &name, int sort, int x_size, int y_size);
inline GraphicsOutput *GraphicsEngine::make_buffer(GraphicsStateGuardian *gsg, std::string const &name, int sort, int x_size, int y_size);

991 13 make_parasite 0 4 1378 29 GraphicsEngine::make_parasite 0 1 438 47
/**
 * Syntactic shorthand for make_buffer.
 */
134
inline GraphicsOutput *GraphicsEngine::make_parasite(GraphicsOutput *host, std::string const &name, int sort, int x_size, int y_size);

992 10 add_window 0 4 1378 26 GraphicsEngine::add_window 0 1 439 609
/**
 * This can be used to add a newly-created GraphicsOutput object (and its GSG)
 * to the engine's list of windows, and requests that it be opened.  This
 * shouldn't be called by user code as make_output normally does this under
 * the hood; it may be useful in esoteric cases in which a custom window
 * object is used.
 *
 * This can be called during the rendering loop, unlike make_output(); the
 * window will be opened before the next frame begins rendering.  Because it
 * doesn't call open_windows(), however, it's not guaranteed that the window
 * will succeed opening even if it returns true.
 */
66
bool GraphicsEngine::add_window(GraphicsOutput *window, int sort);

993 13 remove_window 0 4 1378 29 GraphicsEngine::remove_window 0 1 440 886
/**
 * Removes the indicated window or offscreen buffer from the set of windows
 * that will be processed when render_frame() is called.  This also closes the
 * window if it is open, and removes the window from its GraphicsPipe,
 * allowing the window to be destructed if there are no other references to
 * it.  (However, the window may not be actually closed until next frame, if
 * it is controlled by a sub-thread.)
 *
 * The return value is true if the window was removed, false if it was not
 * found.
 *
 * Unlike remove_all_windows(), this function does not terminate any of the
 * threads that may have been started to service this window; they are left
 * running (since you might open a new window later on these threads).  If
 * your intention is to clean up before shutting down, it is better to call
 * remove_all_windows() then to call remove_window() one at a time.
 */
59
bool GraphicsEngine::remove_window(GraphicsOutput *window);

994 18 remove_all_windows 0 4 1378 34 GraphicsEngine::remove_all_windows 0 1 441 158
/**
 * Removes and closes all windows from the engine.  This also cleans up and
 * terminates any threads that have been started to service those windows.
 */
46
void GraphicsEngine::remove_all_windows(void);

995 17 reset_all_windows 0 4 1378 33 GraphicsEngine::reset_all_windows 0 1 442 199
/**
 * Resets the framebuffer of the current window.  This is currently used by
 * DirectX 8 only.  It calls a reset_window function on each active window to
 * release/create old/new framebuffer
 */
55
void GraphicsEngine::reset_all_windows(bool swapchain);

996 8 is_empty 0 4 1378 24 GraphicsEngine::is_empty 0 1 443 116
/**
 * Returns true if there are no windows or buffers managed by the engine,
 * false if there is at least one.
 */
42
bool GraphicsEngine::is_empty(void) const;

997 15 get_num_windows 0 4 1378 31 GraphicsEngine::get_num_windows 0 1 444 76
/**
 * Returns the number of windows (or buffers) managed by the engine.
 */
48
int GraphicsEngine::get_num_windows(void) const;

998 10 get_window 0 4 1378 26 GraphicsEngine::get_window 0 1 445 84
/**
 * Returns the nth window or buffers managed by the engine, in sorted order.
 */
56
GraphicsOutput *GraphicsEngine::get_window(int n) const;

999 12 render_frame 0 4 1378 28 GraphicsEngine::render_frame 0 1 446 103
/**
 * Renders the next frame in all the registered windows, and flips all of the
 * frame buffers.
 */
40
void GraphicsEngine::render_frame(void);

1000 12 open_windows 0 4 1378 28 GraphicsEngine::open_windows 0 1 447 351
/**
 * Fully opens (or closes) any windows that have recently been requested open
 * or closed, without rendering any frames.  It is not necessary to call this
 * explicitly, since windows will be automatically opened or closed when the
 * next frame is rendered, but you may call this if you want your windows now
 * without seeing a frame go by.
 */
40
void GraphicsEngine::open_windows(void);

1001 10 sync_frame 0 4 1378 26 GraphicsEngine::sync_frame 0 1 448 284
/**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing.  The windows are not yet flipped when this returns; see also
 * flip_frame(). It is not usually necessary to call this explicitly, unless
 * you need to see the previous frame right away.
 */
38
void GraphicsEngine::sync_frame(void);

1002 10 ready_flip 0 4 1378 26 GraphicsEngine::ready_flip 0 1 449 617
/**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing.  Returns when all threads have actually finished drawing, as
 * opposed to 'sync_frame' we seems to return once all draw calls have been
 * submitted.  Calling 'flip_frame' after this function should immediately
 * cause a buffer flip.  This function will only work in opengl right now, for
 * all other graphics pipelines it will simply return immediately.  In opengl
 * it's a bit of a hack: it will attempt to read a single pixel from the frame
 * buffer to force the graphics card to finish drawing before it returns
 */
38
void GraphicsEngine::ready_flip(void);

1003 10 flip_frame 0 4 1378 26 GraphicsEngine::flip_frame 0 1 450 239
/**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing, and then flips all the windows.  It is not usually necessary to
 * call this explicitly, unless you need to see the previous frame right away.
 */
38
void GraphicsEngine::flip_frame(void);

1004 20 extract_texture_data 0 4 1378 36 GraphicsEngine::extract_texture_data 0 1 451 1037
/**
 * Asks the indicated GraphicsStateGuardian to retrieve the texture memory
 * image of the indicated texture and store it in the texture's ram_image
 * field.  The image can then be written to disk via Texture::write(), or
 * otherwise manipulated on the CPU.
 *
 * This is useful for retrieving the contents of a texture that has been
 * somehow generated on the graphics card, instead of having been loaded the
 * normal way via Texture::read() or Texture::load(). It is particularly
 * useful for getting the data associated with a compressed texture image.
 *
 * Since this requires a round-trip to the draw thread, it may require waiting
 * for the current thread to finish rendering if it is called in a
 * multithreaded environment.  However, you can call this several consecutive
 * times on different textures for little additional cost.
 *
 * If the texture has not yet been loaded to the GSG in question, it will be
 * loaded immediately.
 *
 * The return value is true if the operation is successful, false otherwise.
 */
84
bool GraphicsEngine::extract_texture_data(Texture *tex, GraphicsStateGuardian *gsg);

1005 16 dispatch_compute 0 4 1378 32 GraphicsEngine::dispatch_compute 0 1 452 653
/**
 * Asks the indicated GraphicsStateGuardian to dispatch the compute shader in
 * the given ShaderAttrib using the given work group counts.  This can act as
 * an interface for running a one-off compute shader, without having to store
 * it in the scene graph using a ComputeNode.
 *
 * Since this requires a round-trip to the draw thread, it may require waiting
 * for the current thread to finish rendering if it is called in a
 * multithreaded environment.  However, you can call this several consecutive
 * times on different textures for little additional cost.
 *
 * The return value is true if the operation is successful, false otherwise.
 */
124
void GraphicsEngine::dispatch_compute(LVecBase3i const &work_groups, ShaderAttrib const *sattr, GraphicsStateGuardian *gsg);

1006 14 get_global_ptr 0 4 1378 30 GraphicsEngine::get_global_ptr 0 1 453 10
/**
 *
 */
60
static GraphicsEngine *GraphicsEngine::get_global_ptr(void);

1007 14 count_textures 0 4 1356 30 GraphicsOutput::count_textures 0 1 248 261
/**
 * If the GraphicsOutput is set to render into a texture, returns the number
 * of textures that are being rendered into.  Normally, the textures would be
 * associated with different buffers - a color texture, a depth texture, and a
 * stencil texture.
 */
54
inline int GraphicsOutput::count_textures(void) const;

1008 11 has_texture 0 4 1356 27 GraphicsOutput::has_texture 0 1 249 84
/**
 * Returns true if the GraphicsOutput is rendering into any textures at all.
 */
52
inline bool GraphicsOutput::has_texture(void) const;

1009 11 get_texture 0 6 1356 27 GraphicsOutput::get_texture 0 1 250 405
/**
 * Returns the nth texture into which the GraphicsOutput renders.  Returns
 * NULL if there is no such texture.
 *
 * If the texture is non-NULL, it may be applied to geometry to be rendered
 * for any other windows or outputs that share the same GSG as this
 * GraphicsOutput.  The effect is undefined for windows that share a different
 * GSG; usually in these cases the texture will be invalid.
 */
69
virtual inline Texture *GraphicsOutput::get_texture(int i = 0) const;

1010 17 get_texture_plane 0 4 1356 33 GraphicsOutput::get_texture_plane 0 1 251 123
/**
 * Returns the RenderTexturePlane associated with the nth render-texture.
 * Returns 0 if there is no such texture.
 */
93
inline DrawableRegion::RenderTexturePlane GraphicsOutput::get_texture_plane(int i = 0) const;

1011 12 get_rtm_mode 0 4 1356 28 GraphicsOutput::get_rtm_mode 0 1 252 129
/**
 * Returns the RenderTextureMode associated with the nth render-texture.
 * Returns RTM_none if there is no such texture.
 */
87
inline GraphicsOutput::RenderTextureMode GraphicsOutput::get_rtm_mode(int i = 0) const;

1012 21 clear_render_textures 0 4 1356 37 GraphicsOutput::clear_render_textures 0 1 253 130
/**
 * If the GraphicsOutput is currently rendering to a texture, then all
 * textures are dissociated from the GraphicsOuput.
 */
49
void GraphicsOutput::clear_render_textures(void);

1013 18 add_render_texture 0 4 1356 34 GraphicsOutput::add_render_texture 0 1 254 1233
/**
 * Creates a new Texture object, suitable for rendering the contents of this
 * buffer into, and appends it to the list of render textures.
 *
 * If tex is not NULL, it is the texture that will be set up for rendering
 * into; otherwise, a new Texture object will be created, in which case you
 * may call get_texture() to retrieve the new texture pointer.
 *
 * You can specify a bitplane to attach the texture to.  the legal choices
 * are:
 *
 * - RTP_depth
 * - RTP_depth_stencil
 * - RTP_color
 * - RTP_aux_rgba_0
 * - RTP_aux_rgba_1
 * - RTP_aux_rgba_2
 * - RTP_aux_rgba_3
 *
 * If you do not specify a bitplane to attach the texture to, this routine
 * will use a default based on the texture's format:
 *
 * - F_depth_component attaches to RTP_depth
 * - F_depth_stencil attaches to RTP_depth_stencil
 * - all other formats attach to RTP_color.
 *
 * The texture's format will be changed to match the format of the bitplane to
 * which it is attached.  For example, if you pass in an F_rgba texture and
 * order that it be attached to RTP_depth_stencil, it will turn into an
 * F_depth_stencil texture.
 *
 * Also see make_texture_buffer(), which is a higher-level interface for
 * preparing render-to-a-texture mode.
 */
169
void GraphicsOutput::add_render_texture(Texture *tex, GraphicsOutput::RenderTextureMode mode, DrawableRegion::RenderTexturePlane bitplane = ::DrawableRegion::RTP_COUNT);

1014 20 setup_render_texture 0 4 1356 36 GraphicsOutput::setup_render_texture 0 1 255 259
/**
 * This is a deprecated interface that made sense back when GraphicsOutputs
 * could only render into one texture at a time.  From now on, use
 * clear_render_textures and add_render_texture instead.
 *
 * @deprecated Use add_render_texture() instead.
 */
86
void GraphicsOutput::setup_render_texture(Texture *tex, bool allow_bind, bool to_ram);

1015 8 get_size 0 4 1356 24 GraphicsOutput::get_size 0 1 256 410
/**
 * Returns the visible size of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */
62
inline LVecBase2i const &GraphicsOutput::get_size(void) const;

1016 10 get_x_size 0 4 1356 26 GraphicsOutput::get_x_size 0 1 257 411
/**
 * Returns the visible width of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */
50
inline int GraphicsOutput::get_x_size(void) const;

1017 10 get_y_size 0 4 1356 26 GraphicsOutput::get_y_size 0 1 258 412
/**
 * Returns the visible height of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */
50
inline int GraphicsOutput::get_y_size(void) const;

1018 11 get_fb_size 0 4 1356 27 GraphicsOutput::get_fb_size 0 1 259 175
/**
 * Returns the internal size of the window or buffer.  This is almost always
 * the same as get_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */
58
inline LVecBase2i GraphicsOutput::get_fb_size(void) const;

1019 13 get_fb_x_size 0 4 1356 29 GraphicsOutput::get_fb_x_size 0 1 260 178
/**
 * Returns the internal width of the window or buffer.  This is almost always
 * the same as get_x_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */
53
inline int GraphicsOutput::get_fb_x_size(void) const;

1020 13 get_fb_y_size 0 4 1356 29 GraphicsOutput::get_fb_y_size 0 1 261 179
/**
 * Returns the internal height of the window or buffer.  This is almost always
 * the same as get_y_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */
53
inline int GraphicsOutput::get_fb_y_size(void) const;

1021 17 get_sbs_left_size 0 4 1356 33 GraphicsOutput::get_sbs_left_size 0 1 262 232
/**
 * If side-by-side stereo is enabled, this returns the pixel size of the left
 * eye, based on scaling get_size() by get_sbs_left_dimensions().  If side-by-
 * side stereo is not enabled, this returns the same as get_size().
 */
64
inline LVecBase2i GraphicsOutput::get_sbs_left_size(void) const;

1022 19 get_sbs_left_x_size 0 4 1356 35 GraphicsOutput::get_sbs_left_x_size 0 1 263 237
/**
 * If side-by-side stereo is enabled, this returns the pixel width of the left
 * eye, based on scaling get_x_size() by get_sbs_left_dimensions().  If side-
 * by-side stereo is not enabled, this returns the same as get_x_size().
 */
59
inline int GraphicsOutput::get_sbs_left_x_size(void) const;

1023 19 get_sbs_left_y_size 0 4 1356 35 GraphicsOutput::get_sbs_left_y_size 0 1 264 237
/**
 * If side-by-side stereo is enabled, this returns the pixel height of the
 * left eye, based on scaling get_y_size() by get_sbs_left_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_y_size().
 */
59
inline int GraphicsOutput::get_sbs_left_y_size(void) const;

1024 18 get_sbs_right_size 0 4 1356 34 GraphicsOutput::get_sbs_right_size 0 1 265 234
/**
 * If side-by-side stereo is enabled, this returns the pixel size of the right
 * eye, based on scaling get_size() by get_sbs_right_dimensions().  If side-
 * by-side stereo is not enabled, this returns the same as get_size().
 */
65
inline LVecBase2i GraphicsOutput::get_sbs_right_size(void) const;

1025 20 get_sbs_right_x_size 0 4 1356 36 GraphicsOutput::get_sbs_right_x_size 0 1 266 238
/**
 * If side-by-side stereo is enabled, this returns the pixel width of the
 * right eye, based on scaling get_x_size() by get_sbs_right_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_x_size().
 */
60
inline int GraphicsOutput::get_sbs_right_x_size(void) const;

1026 20 get_sbs_right_y_size 0 4 1356 36 GraphicsOutput::get_sbs_right_y_size 0 1 267 239
/**
 * If side-by-side stereo is enabled, this returns the pixel height of the
 * right eye, based on scaling get_y_size() by get_sbs_right_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_y_size().
 */
60
inline int GraphicsOutput::get_sbs_right_y_size(void) const;

1027 8 has_size 0 4 1356 24 GraphicsOutput::has_size 0 1 268 258
/**
 * Returns true if the size of the window/frame buffer is known, false
 * otherwise.  In certain cases the size may not be known until after the
 * object has been fully created.  Also, certain objects (like windows) may
 * change size spontaneously.
 */
49
inline bool GraphicsOutput::has_size(void) const;

1028 8 is_valid 0 4 1356 24 GraphicsOutput::is_valid 0 1 269 99
/**
 * Returns true if the output is fully created and ready for rendering, false
 * otherwise.
 */
49
inline bool GraphicsOutput::is_valid(void) const;

1029 15 is_nonzero_size 0 4 1356 31 GraphicsOutput::is_nonzero_size 0 1 270 124
/**
 * Returns true if the output has a nonzero size in both X and Y, or false if
 * it is zero (and therefore invalid).
 */
56
inline bool GraphicsOutput::is_nonzero_size(void) const;

1030 10 set_active 0 4 1356 26 GraphicsOutput::set_active 0 1 271 134
/**
 * Sets the active flag associated with the GraphicsOutput.  If the
 * GraphicsOutput is marked inactive, nothing is rendered.
 */
45
void GraphicsOutput::set_active(bool active);

1031 9 is_active 0 6 1356 25 GraphicsOutput::is_active 0 1 272 84
/**
 * Returns true if the window is ready to be rendered into, false otherwise.
 */
51
virtual bool GraphicsOutput::is_active(void) const;

1032 12 set_one_shot 0 4 1356 28 GraphicsOutput::set_one_shot 0 1 273 837
/**
 * Changes the current setting of the one-shot flag.  When this is true, the
 * GraphicsOutput will render the current frame and then automatically set
 * itself inactive.  This is particularly useful for buffers that are created
 * for the purposes of render-to-texture, for static textures that don't need
 * to be continually re-rendered once they have been rendered the first time.
 *
 * Setting the buffer inactive is not the same thing as destroying it.  You
 * are still responsible for passing this buffer to
 * GraphicsEngine::remove_window() when you no longer need the texture, in
 * order to clean up fully.  (However, you should not call remove_window() on
 * this buffer while the texture is still needed, because depending on the
 * render-to-texture mechanism in use, this may invalidate the texture
 * contents.)
 */
49
void GraphicsOutput::set_one_shot(bool one_shot);

1033 12 get_one_shot 0 4 1356 28 GraphicsOutput::get_one_shot 0 1 274 163
/**
 * Returns the current setting of the one-shot flag.  When this is true, the
 * GraphicsOutput will automatically set itself inactive after the next frame.
 */
46
bool GraphicsOutput::get_one_shot(void) const;

1034 12 set_inverted 0 4 1356 28 GraphicsOutput::set_inverted 0 1 275 582
/**
 * Changes the current setting of the inverted flag.  When this is true, the
 * scene is rendered into the window upside-down and backwards, that is,
 * inverted as if viewed through a mirror placed on the floor.
 *
 * This is primarily intended to support DirectX (and a few buggy OpenGL
 * graphics drivers) that perform a framebuffer-to-texture copy upside-down
 * from the usual OpenGL (and Panda) convention.  Panda will automatically set
 * this flag for offscreen buffers on hardware that is known to do this, to
 * compensate when rendering offscreen into a texture.
 */
49
void GraphicsOutput::set_inverted(bool inverted);

1035 12 get_inverted 0 4 1356 28 GraphicsOutput::get_inverted 0 1 276 198
/**
 * Returns the current setting of the inverted flag.  When this is true, the
 * scene is rendered into the window upside-down, flipped like a mirror along
 * the X axis.  See set_inverted().
 */
53
inline bool GraphicsOutput::get_inverted(void) const;

1036 13 set_swap_eyes 0 4 1356 29 GraphicsOutput::set_swap_eyes 0 1 277 383
/**
 * Changes the "swap eyes" flag.  This flag is normally false.  When it is
 * true, the left and right channels of a stereo DisplayRegion are sent to the
 * opposite channels in the rendering backend.  This is meant to work around
 * hardware that inadvertently swaps the output channels, or hardware for
 * which it cannot be determined which channel is which until runtime.
 */
58
inline void GraphicsOutput::set_swap_eyes(bool swap_eyes);

1037 13 get_swap_eyes 0 4 1356 29 GraphicsOutput::get_swap_eyes 0 1 278 85
/**
 * Returns the current setting of the "swap eyes" flag.  See set_swap_eyes().
 */
54
inline bool GraphicsOutput::get_swap_eyes(void) const;

1038 19 set_red_blue_stereo 0 4 1356 35 GraphicsOutput::set_red_blue_stereo 0 1 279 592
/**
 * Enables red-blue stereo mode on this particular window.  When red-blue
 * stereo mode is in effect, DisplayRegions that have the "left" channel set
 * will render in the red (or specified) channel only, while DisplayRegions
 * that have the "right" channel set will render in the blue (or specified)
 * channel only.
 *
 * The remaining two parameters specify the particular color channel(s) to
 * associate with each eye.  Use the bits defined in
 * ColorWriteAttrib::Channels.
 *
 * This can be used to achieve a cheesy stereo mode in the absence of
 * hardware-supported stereo.
 */
139
inline void GraphicsOutput::set_red_blue_stereo(bool red_blue_stereo, unsigned int left_eye_color_mask, unsigned int right_eye_color_mask);

1039 19 get_red_blue_stereo 0 4 1356 35 GraphicsOutput::get_red_blue_stereo 0 1 280 119
/**
 * Returns whether red-blue stereo mode is in effect for this particular
 * window.  See set_red_blue_stereo().
 */
60
inline bool GraphicsOutput::get_red_blue_stereo(void) const;

1040 23 get_left_eye_color_mask 0 4 1356 39 GraphicsOutput::get_left_eye_color_mask 0 1 281 198
/**
 * Returns the color mask in effect when rendering a left-eye view in red_blue
 * stereo mode.  This is one or more bits defined in
 * ColorWriteAttrib::Channels.  See set_red_blue_stereo().
 */
72
inline unsigned int GraphicsOutput::get_left_eye_color_mask(void) const;

1041 24 get_right_eye_color_mask 0 4 1356 40 GraphicsOutput::get_right_eye_color_mask 0 1 282 199
/**
 * Returns the color mask in effect when rendering a right-eye view in
 * red_blue stereo mode.  This is one or more bits defined in
 * ColorWriteAttrib::Channels.  See set_red_blue_stereo().
 */
73
inline unsigned int GraphicsOutput::get_right_eye_color_mask(void) const;

1042 23 set_side_by_side_stereo 0 4 1356 39 GraphicsOutput::set_side_by_side_stereo 0 2 283 284 1230
/**
 * Enables side-by-side stereo mode on this particular window.  When side-by-
 * side stereo mode is in effect, DisplayRegions that have the "left" channel
 * set will render on the part of the window specified by sbs_left_dimensions
 * (typically the left half: (0, 0.5, 0, 1)), while DisplayRegions that have
 * the "right" channel set will render on the part of the window specified by
 * sbs_right_dimensions (typically the right half: (0.5, 1, 0, 1)).
 *
 * This is commonly used in a dual-monitor mode, where a window is opened that
 * spans two monitors, and each monitor represents a different eye.
 */

/**
 * Enables side-by-side stereo mode on this particular window.  When side-by-
 * side stereo mode is in effect, DisplayRegions that have the "left" channel
 * set will render on the part of the window specified by sbs_left_dimensions
 * (typically the left half: (0, 0.5, 0, 1)), while DisplayRegions that have
 * the "right" channel set will render on the part of the window specified by
 * sbs_right_dimensions (typically the right half: (0.5, 1, 0, 1)).
 *
 * This is commonly used in a dual-monitor mode, where a window is opened that
 * spans two monitors, and each monitor represents a different eye.
 */
220
void GraphicsOutput::set_side_by_side_stereo(bool side_by_side_stereo);
void GraphicsOutput::set_side_by_side_stereo(bool side_by_side_stereo, LVecBase4 const &sbs_left_dimensions, LVecBase4 const &sbs_right_dimensions);

1043 23 get_side_by_side_stereo 0 4 1356 39 GraphicsOutput::get_side_by_side_stereo 0 1 285 127
/**
 * Returns whether side-by-side stereo mode is in effect for this particular
 * window.  See set_side_by_side_stereo().
 */
64
inline bool GraphicsOutput::get_side_by_side_stereo(void) const;

1044 23 get_sbs_left_dimensions 0 4 1356 39 GraphicsOutput::get_sbs_left_dimensions 0 1 286 185
/**
 * Returns the effective sub-region of the window for displaying the left
 * channel, if side-by-side stereo mode is in effect for the window.  See
 * set_side_by_side_stereo().
 */
76
inline LVecBase4 const &GraphicsOutput::get_sbs_left_dimensions(void) const;

1045 24 get_sbs_right_dimensions 0 4 1356 40 GraphicsOutput::get_sbs_right_dimensions 0 1 287 186
/**
 * Returns the effective sub-region of the window for displaying the right
 * channel, if side-by-side stereo mode is in effect for the window.  See
 * set_side_by_side_stereo().
 */
77
inline LVecBase4 const &GraphicsOutput::get_sbs_right_dimensions(void) const;

1046 17 get_fb_properties 0 4 1356 33 GraphicsOutput::get_fb_properties 0 1 288 60
/**
 * Returns the framebuffer properties of the window.
 */
82
inline FrameBufferProperties const &GraphicsOutput::get_fb_properties(void) const;

1047 9 is_stereo 0 4 1356 25 GraphicsOutput::is_stereo 0 1 289 188
/**
 * Returns Returns true if this window can render stereo DisplayRegions,
 * either through red-blue stereo (see set_red_blue_stereo()) or through true
 * hardware stereo rendering.
 */
50
inline bool GraphicsOutput::is_stereo(void) const;

1048 17 clear_delete_flag 0 4 1356 33 GraphicsOutput::clear_delete_flag 0 1 290 133
/**
 * Resets the delete flag, so the GraphicsOutput will not be automatically
 * deleted before the beginning of the next frame.
 */
52
inline void GraphicsOutput::clear_delete_flag(void);

1049 15 get_delete_flag 0 4 1356 31 GraphicsOutput::get_delete_flag 0 1 291 195
/**
 * Returns the current setting of the delete flag.  When this is true, the
 * GraphicsOutput will automatically be removed before the beginning of the
 * next frame by the GraphicsEngine.
 */
49
bool GraphicsOutput::get_delete_flag(void) const;

1050 8 set_sort 0 6 1356 24 GraphicsOutput::set_sort 0 1 292 109
/**
 * Adjusts the sorting order of this particular GraphicsOutput, relative to
 * other GraphicsOutputs.
 */
48
virtual void GraphicsOutput::set_sort(int sort);

1051 8 get_sort 0 4 1356 24 GraphicsOutput::get_sort 0 1 293 174
/**
 * Returns the sorting order of this particular GraphicsOutput.  The various
 * GraphicsOutputs within a particular thread will be rendered in the
 * indicated order.
 */
48
inline int GraphicsOutput::get_sort(void) const;

1052 14 set_child_sort 0 4 1356 30 GraphicsOutput::set_child_sort 0 1 294 633
/**
 * Specifies the sort value of future offscreen buffers created by
 * make_texture_sort().
 *
 * The purpose of this method is to allow the user to limit the sort value
 * chosen for a buffer created via make_texture_buffer().  Normally, this
 * buffer will be assigned a value of get_sort() - 1, so that it will be
 * rendered before this window is rendered; but sometimes this isn't
 * sufficiently early, especially if other buffers also have a view into the
 * same scene.
 *
 * If you specify a value here, then new buffers created via
 * make_texture_buffer() will be given that sort value instead of get_sort() -
 * 1.
 */
59
inline void GraphicsOutput::set_child_sort(int child_sort);

1053 16 clear_child_sort 0 4 1356 32 GraphicsOutput::clear_child_sort 0 1 295 139
/**
 * Resets the sort value of future offscreen buffers created by
 * make_texture_sort() to the default value.  See set_child_sort().
 */
51
inline void GraphicsOutput::clear_child_sort(void);

1054 14 get_child_sort 0 4 1356 30 GraphicsOutput::get_child_sort 0 1 296 118
/**
 * Returns the sort value of future offscreen buffers created by
 * make_texture_sort(). See set_child_sort().
 */
54
inline int GraphicsOutput::get_child_sort(void) const;

1055 12 trigger_copy 0 4 1356 28 GraphicsOutput::trigger_copy 0 1 297 170
/**
 * When the GraphicsOutput is in triggered copy mode, this function triggers
 * the copy (at the end of the next frame).
 * @returns a future that can be awaited.
 */
55
inline AsyncFuture *GraphicsOutput::trigger_copy(void);

1056 19 make_display_region 0 4 1356 35 GraphicsOutput::make_display_region 0 3 298 299 300 1105
/**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */

/**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */

/**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */
264
inline DisplayRegion *GraphicsOutput::make_display_region(void);
inline DisplayRegion *GraphicsOutput::make_display_region(PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);
DisplayRegion *GraphicsOutput::make_display_region(LVecBase4 const &dimensions);

1057 24 make_mono_display_region 0 4 1356 40 GraphicsOutput::make_mono_display_region 0 3 301 302 303 1267
/**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */

/**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */

/**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */
279
inline DisplayRegion *GraphicsOutput::make_mono_display_region(void);
inline DisplayRegion *GraphicsOutput::make_mono_display_region(PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);
DisplayRegion *GraphicsOutput::make_mono_display_region(LVecBase4 const &dimensions);

1058 26 make_stereo_display_region 0 4 1356 42 GraphicsOutput::make_stereo_display_region 0 3 304 305 306 520
/**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */

/**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */

/**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */
303
inline StereoDisplayRegion *GraphicsOutput::make_stereo_display_region(void);
inline StereoDisplayRegion *GraphicsOutput::make_stereo_display_region(PN_stdfloat l, PN_stdfloat r, PN_stdfloat b, PN_stdfloat t);
StereoDisplayRegion *GraphicsOutput::make_stereo_display_region(LVecBase4 const &dimensions);

1059 21 remove_display_region 0 4 1356 37 GraphicsOutput::remove_display_region 0 1 307 224
/**
 * Removes the indicated DisplayRegion from the window, and destructs it if
 * there are no other references.
 *
 * Returns true if the DisplayRegion is found and removed, false if it was not
 * a part of the window.
 */
74
bool GraphicsOutput::remove_display_region(DisplayRegion *display_region);

1060 26 remove_all_display_regions 0 4 1356 42 GraphicsOutput::remove_all_display_regions 0 1 308 114
/**
 * Removes all display regions from the window, except the default one that is
 * created with the window.
 */
54
void GraphicsOutput::remove_all_display_regions(void);

1061 26 get_overlay_display_region 0 4 1356 42 GraphicsOutput::get_overlay_display_region 0 1 309 570
/**
 * Returns the special "overlay" DisplayRegion that is created for each window
 * or buffer.  This DisplayRegion covers the entire window, but cannot be used
 * for rendering.  It is a placeholder only, to indicate the dimensions of the
 * window, and is usually used internally for purposes such as clearing the
 * window, or grabbing a screenshot of the window.
 *
 * There are very few applications that require access to this DisplayRegion.
 * Normally, you should create your own DisplayRegion that covers the window,
 * if you want to render to the window.
 */
77
inline DisplayRegion *GraphicsOutput::get_overlay_display_region(void) const;

1062 26 set_overlay_display_region 0 4 1356 42 GraphicsOutput::set_overlay_display_region 0 1 310 707
/**
 * Replaces the special "overlay" DisplayRegion that is created for each
 * window or buffer.  See get_overlay_display_region().  This must be a new
 * DisplayRegion that has already been created for this window, for instance
 * via a call to make_mono_display_region().  You are responsible for ensuring
 * that the new DisplayRegion covers the entire window.  The previous overlay
 * display region is not automatically removed; you must explicitly call
 * remove_display_region() on it after replacing it with this method, if you
 * wish it to be removed.
 *
 * Normally, there is no reason to change the overlay DisplayRegion, so this
 * method should be used only in very unusual circumstances.
 */
79
void GraphicsOutput::set_overlay_display_region(DisplayRegion *display_region);

1063 23 get_num_display_regions 0 4 1356 39 GraphicsOutput::get_num_display_regions 0 1 311 113
/**
 * Returns the number of DisplayRegions that have been created within the
 * window, active or otherwise.
 */
56
int GraphicsOutput::get_num_display_regions(void) const;

1064 18 get_display_region 0 4 1356 34 GraphicsOutput::get_display_region 0 1 312 260
/**
 * Returns the nth DisplayRegion of those that have been created within the
 * window.  This may return NULL if n is out of bounds; particularly likely if
 * the number of display regions has changed since the last call to
 * get_num_display_regions().
 */
75
PointerTo< DisplayRegion > GraphicsOutput::get_display_region(int n) const;

1065 30 get_num_active_display_regions 0 4 1356 46 GraphicsOutput::get_num_active_display_regions 0 1 313 99
/**
 * Returns the number of active DisplayRegions that have been created within
 * the window.
 */
63
int GraphicsOutput::get_num_active_display_regions(void) const;

1066 25 get_active_display_region 0 4 1356 41 GraphicsOutput::get_active_display_region 0 1 314 274
/**
 * Returns the nth active DisplayRegion of those that have been created within
 * the window.  This may return NULL if n is out of bounds; particularly
 * likely if the number of display regions has changed since the last call to
 * get_num_active_display_regions().
 */
82
PointerTo< DisplayRegion > GraphicsOutput::get_active_display_region(int n) const;

1067 19 make_texture_buffer 0 4 1356 35 GraphicsOutput::make_texture_buffer 0 1 315 1369
/**
 * Creates and returns an offscreen buffer for rendering into, the result of
 * which will be a texture suitable for applying to geometry within the scene
 * rendered into this window.
 *
 * If you pass zero as the buffer size, the buffer will have the same size as
 * the host window, and will automatically be resized when the host window is.
 *
 * If tex is not NULL, it is the texture that will be set up for rendering
 * into; otherwise, a new Texture object will be created.  In either case, the
 * target texture can be retrieved from the return value with
 * buffer->get_texture() (assuming the return value is not NULL).
 *
 * If to_ram is true, the buffer will be set up to download its contents to
 * the system RAM memory associated with the Texture object, instead of
 * keeping it strictly within texture memory; this is much slower, but it
 * allows using the texture with any GSG.
 *
 * This will attempt to be smart about maximizing render performance while
 * minimizing framebuffer waste.  It might return a GraphicsBuffer set to
 * render directly into a texture, if possible; or it might return a
 * ParasiteBuffer that renders into this window.  The return value is NULL if
 * the buffer could not be created for some reason.
 *
 * When you are done using the buffer, you should remove it with a call to
 * GraphicsEngine::remove_window().
 */
184
GraphicsOutput *GraphicsOutput::make_texture_buffer(std::string const &name, int x_size, int y_size, Texture *tex = nullptr, bool to_ram = false, FrameBufferProperties *fbp = nullptr);

1068 13 make_cube_map 0 4 1356 29 GraphicsOutput::make_cube_map 0 1 316 763
/**
 * This is similar to make_texture_buffer() in that it allocates a separate
 * buffer suitable for rendering to a texture that can be assigned to geometry
 * in this window, but in this case, the buffer is set up to render the six
 * faces of a cube map.
 *
 * The buffer is automatically set up with six display regions and six
 * cameras, each of which are assigned the indicated draw_mask and parented to
 * the given camera_rig node (which you should then put in your scene to
 * render the cube map from the appropriate point of view).
 *
 * You may take the texture associated with the buffer and apply it to
 * geometry, particularly with TexGenAttrib::M_world_cube_map also in effect,
 * to apply a reflection of everything seen by the camera rig.
 */
219
GraphicsOutput *GraphicsOutput::make_cube_map(std::string const &name, int size, NodePath &camera_rig, DrawMask camera_mask = PandaNode::get_all_camera_mask(), bool to_ram = false, FrameBufferProperties *fbp = nullptr);

1069 24 make_screenshot_filename 0 4 1356 40 GraphicsOutput::make_screenshot_filename 0 1 317 423
/**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The default filename
 * is generated from the supplied prefix and from the Config variable
 * screenshot-filename, which contains the following strings:
 *
 * %~p - the supplied prefix %~f - the frame count %~e - the value of
 * screenshot-extension All other % strings in strftime().
 */
106
static inline Filename GraphicsOutput::make_screenshot_filename(std::string const &prefix = "screenshot");

1070 23 save_screenshot_default 0 4 1356 39 GraphicsOutput::save_screenshot_default 0 1 318 198
/**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The filename is
 * generated by make_screenshot_filename().
 */
98
inline Filename GraphicsOutput::save_screenshot_default(std::string const &prefix = "screenshot");

1071 15 save_screenshot 0 4 1356 31 GraphicsOutput::save_screenshot 0 1 319 304
/**
 * Saves a screenshot of the region to the indicated filename.  The image
 * comment is an optional user readable string that will be saved with the
 * header of the image (if the file format supports embedded data; for example
 * jpg allows comments).  Returns true on success, false on failure.
 */
109
inline bool GraphicsOutput::save_screenshot(Filename const &filename, std::string const &image_comment = "");

1072 14 get_screenshot 0 4 1356 30 GraphicsOutput::get_screenshot 0 2 320 321 274
/**
 * Captures the most-recently rendered image from the framebuffer into the
 * indicated PNMImage.  Returns true on success, false on failure.
 */

/**
 * Captures the most-recently rendered image from the framebuffer and returns
 * it as Texture, or NULL on failure.
 */
126
inline bool GraphicsOutput::get_screenshot(PNMImage &image);
inline PointerTo< Texture > GraphicsOutput::get_screenshot(void);

1073 16 get_texture_card 0 4 1356 32 GraphicsOutput::get_texture_card 0 1 322 573
/**
 * Returns a PandaNode containing a square polygon.  The dimensions are
 * (-1,0,-1) to (1,0,1). The texture coordinates are such that the texture of
 * this GraphicsOutput is aligned properly to the polygon.  The GraphicsOutput
 * promises to surgically update the Geom inside the PandaNode if necessary to
 * maintain this invariant.
 *
 * Each invocation of this function returns a freshly- allocated PandaNode.
 * You can therefore safely modify the RenderAttribs of the PandaNode.  The
 * PandaNode is initially textured with the texture of this GraphicOutput.
 */
48
NodePath GraphicsOutput::get_texture_card(void);

1074 18 share_depth_buffer 0 6 1356 34 GraphicsOutput::share_depth_buffer 0 1 323 124
/**
 * Will attempt to use the depth buffer of the input graphics_output.  The
 * buffer sizes must be exactly the same.
 */
81
virtual bool GraphicsOutput::share_depth_buffer(GraphicsOutput *graphics_output);

1075 20 unshare_depth_buffer 0 6 1356 36 GraphicsOutput::unshare_depth_buffer 0 1 324 48
/**
 * Discontinue sharing the depth buffer.
 */
56
virtual void GraphicsOutput::unshare_depth_buffer(void);

1076 27 get_supports_render_texture 0 6 1356 43 GraphicsOutput::get_supports_render_texture 0 1 325 191
/**
 * Returns true if this particular GraphicsOutput can render directly into a
 * texture, or false if it must always copy-to-texture at the end of each
 * frame to achieve this effect.
 */
69
virtual bool GraphicsOutput::get_supports_render_texture(void) const;

1077 10 flip_ready 0 6 1356 26 GraphicsOutput::flip_ready 0 1 326 259
// These are not intended to be called directly by the user, but they're
// published anyway since they might occasionally be useful for low-level
// debugging.

/**
 * Returns true if a frame has been rendered and needs to be flipped, false
 * otherwise.
 */
52
virtual bool GraphicsOutput::flip_ready(void) const;

1078 8 get_host 0 6 1356 24 GraphicsOutput::get_host 0 1 327 218
/**
 * This is normally called only from within make_texture_buffer().  When
 * called on a ParasiteBuffer, it returns the host of that buffer; but when
 * called on some other buffer, it returns the buffer itself.
 */
55
virtual GraphicsOutput *GraphicsOutput::get_host(void);

1079 14 get_class_type 0 4 1356 30 GraphicsOutput::get_class_type 0 1 328 0
55
static TypeHandle GraphicsOutput::get_class_type(void);

1080 10 set_camera 0 6 1353 25 DisplayRegion::set_camera 0 1 201 508
/**
 * Sets the camera that is associated with this DisplayRegion.  There is a
 * one-to-many association between cameras and DisplayRegions; one camera may
 * be shared by multiple DisplayRegions.
 *
 * The camera is actually set via a NodePath, which clarifies which instance
 * of the camera (if there happen to be multiple instances) we should use.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
63
virtual void DisplayRegion::set_camera(NodePath const &camera);

1081 10 get_camera 0 4 1353 25 DisplayRegion::get_camera 0 1 202 117
/**
 * Returns the camera associated with this DisplayRegion, or an empty NodePath
 * if no camera is associated.
 */
103
inline NodePath DisplayRegion::get_camera(Thread *current_thread = Thread::get_current_thread()) const;

1082 10 set_active 0 6 1353 25 DisplayRegion::set_active 0 1 203 284
/**
 * Sets the active flag associated with the DisplayRegion.  If the
 * DisplayRegion is marked inactive, nothing is rendered.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
52
virtual void DisplayRegion::set_active(bool active);

1083 9 is_active 0 4 1353 24 DisplayRegion::is_active 0 1 204 69
/**
 * Returns the active flag associated with the DisplayRegion.
 */
49
inline bool DisplayRegion::is_active(void) const;

1084 8 set_sort 0 6 1353 23 DisplayRegion::set_sort 0 1 205 325
/**
 * Sets the sort value associated with the DisplayRegion.  Within a window,
 * DisplayRegions will be rendered in order from the lowest sort value to the
 * highest.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
47
virtual void DisplayRegion::set_sort(int sort);

1085 8 get_sort 0 4 1353 23 DisplayRegion::get_sort 0 1 206 68
/**
 * Returns the sort value associated with the DisplayRegion.
 */
47
inline int DisplayRegion::get_sort(void) const;

1086 18 set_stereo_channel 0 6 1353 33 DisplayRegion::set_stereo_channel 0 1 207 1240
/**
 * Specifies whether the DisplayRegion represents the left or right channel of
 * a stereo pair, or whether it is a normal, monocular image.  This
 * automatically adjusts the lens that is used to render to this DisplayRegion
 * to its left or right eye, according to the lens's stereo properties.
 *
 * When the DisplayRegion is attached to a stereo window (one for which
 * is_stereo() returns true), this also specifies which physical channel the
 * DisplayRegion renders to.
 *
 * Normally you would create at least two DisplayRegions for a stereo window,
 * one for each of the left and right channels.  The two DisplayRegions may
 * share the same camera (and thus the same lens); this parameter is used to
 * control the exact properties of the lens when it is used to render into
 * this DisplayRegion.
 *
 * Also see the StereoDisplayRegion, which automates managing a pair of
 * left/right DisplayRegions.
 *
 * An ordinary DisplayRegion may be set to SC_mono, SC_left, or SC_right.  You
 * may set SC_stereo only on a StereoDisplayRegion.
 *
 * This call also resets tex_view_offset to its default value, which is 0 for
 * the left eye or 1 for the right eye of a stereo display region, or 0 for a
 * mono display region.
 */
83
virtual void DisplayRegion::set_stereo_channel(Lens::StereoChannel stereo_channel);

1087 18 get_stereo_channel 0 4 1353 33 DisplayRegion::get_stereo_channel 0 1 208 181
/**
 * Returns whether the DisplayRegion is specified as the left or right channel
 * of a stereo pair, or whether it is a normal, monocular image.  See
 * set_stereo_channel().
 */
73
inline Lens::StereoChannel DisplayRegion::get_stereo_channel(void) const;

1088 19 set_tex_view_offset 0 6 1353 34 DisplayRegion::set_tex_view_offset 0 1 209 423
/**
 * Sets the current texture view offset for this DisplayRegion.  This is
 * normally set to zero.  If nonzero, it is used to select a particular view
 * of any multiview textures that are rendered within this DisplayRegion.
 *
 * For a StereoDisplayRegion, this is normally 0 for the left eye, and 1 for
 * the right eye, to support stereo textures.  This is set automatically when
 * you call set_stereo_channel().
 */
69
virtual void DisplayRegion::set_tex_view_offset(int tex_view_offset);

1089 19 get_tex_view_offset 0 4 1353 34 DisplayRegion::get_tex_view_offset 0 1 210 360
/**
 * Returns the current texture view offset for this DisplayRegion.  This is
 * normally set to zero.  If nonzero, it is used to select a particular view
 * of any multiview textures that are rendered within this DisplayRegion.
 *
 * For a StereoDisplayRegion, this is normally 0 for the left eye, and 1 for
 * the right eye, to support stereo textures.
 */
58
inline int DisplayRegion::get_tex_view_offset(void) const;

1090 21 set_incomplete_render 0 6 1353 36 DisplayRegion::set_incomplete_render 0 1 211 550
/**
 * Sets the incomplete_render flag.  When this is true, the frame will be
 * rendered even if some of the geometry or textures in the scene are not
 * available (e.g.  they have been temporarily paged out).  When this is
 * false, the frame will be held up while this data is reloaded.
 *
 * This flag may also be set on the GraphicsStateGuardian.  It will be
 * considered true for a given DisplayRegion only if it is true on both the
 * GSG and on the DisplayRegion.
 *
 * See GraphicsStateGuardian::set_incomplete_render() for more detail.
 */
74
virtual void DisplayRegion::set_incomplete_render(bool incomplete_render);

1091 21 get_incomplete_render 0 4 1353 36 DisplayRegion::get_incomplete_render 0 1 212 76
/**
 * Returns the incomplete_render flag.  See set_incomplete_render().
 */
61
inline bool DisplayRegion::get_incomplete_render(void) const;

1092 27 set_texture_reload_priority 0 6 1353 42 DisplayRegion::set_texture_reload_priority 0 1 213 582
/**
 * Specifies an integer priority which is assigned to any asynchronous texture
 * reload requests spawned while processing this DisplayRegion.  This controls
 * which textures are loaded first when multiple textures need to be reloaded
 * at once; it also controls the relative priority between asynchronous
 * texture loads and asynchronous model or animation loads.
 *
 * Specifying a larger number here makes the textures rendered by this
 * DisplayRegion load up first.  This may be particularly useful to do, for
 * instance, for the DisplayRegion that renders the gui.
 */
85
virtual void DisplayRegion::set_texture_reload_priority(int texture_reload_priority);

1093 27 get_texture_reload_priority 0 4 1353 42 DisplayRegion::get_texture_reload_priority 0 1 214 129
/**
 * Returns the priority which is assigned to asynchronous texture reload
 * requests.  See set_texture_reload_priority().
 */
66
inline int DisplayRegion::get_texture_reload_priority(void) const;

1094 14 set_lens_index 0 4 1353 29 DisplayRegion::set_lens_index 0 1 215 331
/**
 * Sets the lens index, allows for multiple lenses to be attached to a camera.
 * This is useful for a variety of setups, such as fish eye rendering.  The
 * default is 0.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
46
void DisplayRegion::set_lens_index(int index);

1095 14 get_lens_index 0 4 1353 29 DisplayRegion::get_lens_index 0 1 216 219
/**
 * Returns the specific lens of the associated Camera that will be used for
 * rendering this scene.  Most Cameras hold only one lens, but for multiple
 * lenses this method may be used to selected between them.
 */
53
inline int DisplayRegion::get_lens_index(void) const;

1096 18 set_cull_traverser 0 6 1353 33 DisplayRegion::set_cull_traverser 0 1 217 218
/**
 * Specifies the CullTraverser that will be used to draw the contents of this
 * DisplayRegion.  Normally the default CullTraverser is sufficient, but this
 * may be changed to change the default cull behavior.
 */
68
virtual void DisplayRegion::set_cull_traverser(CullTraverser *trav);

1097 18 get_cull_traverser 0 4 1353 33 DisplayRegion::get_cull_traverser 0 1 218 101
/**
 * Returns the CullTraverser that will be used to draw the contents of this
 * DisplayRegion.
 */
55
CullTraverser *DisplayRegion::get_cull_traverser(void);

1098 18 set_cube_map_index 0 4 1353 33 DisplayRegion::set_cube_map_index 0 1 219 57
/**
 * Deprecated; replaced by set_target_tex_page().
 */
66
inline void DisplayRegion::set_cube_map_index(int cube_map_index);

1099 19 set_target_tex_page 0 6 1353 34 DisplayRegion::set_target_tex_page 0 1 220 678
/**
 * This is a special parameter that is only used when rendering the faces of a
 * cube map or multipage and/or multiview texture.
 *
 * This sets up the DisplayRegion to render to the ith page and jth view of
 * its associated texture(s); the value must be consistent with the range of
 * values availble to the texture.  A normal DisplayRegion that is not
 * associated with any particular page should be set to page -1 and view 0.
 *
 * This is particularly useful when rendering cube maps and/or stereo
 * textures.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */
58
virtual void DisplayRegion::set_target_tex_page(int page);

1100 19 get_target_tex_page 0 4 1353 34 DisplayRegion::get_target_tex_page 0 1 221 165
/**
 * Returns the target page number associated with this particular
 * DisplayRegion, or -1 if it is not associated with a page.  See
 * set_target_tex_page().
 */
58
inline int DisplayRegion::get_target_tex_page(void) const;

1101 19 set_scissor_enabled 0 4 1353 34 DisplayRegion::set_scissor_enabled 0 1 222 138
/**
 * Sets whether or not scissor testing is enabled for this region.  The
 * default is true, except for the overlay display region.
 */
69
inline void DisplayRegion::set_scissor_enabled(bool scissor_enabled);

1102 19 get_scissor_enabled 0 4 1353 34 DisplayRegion::get_scissor_enabled 0 1 223 141
/**
 * Returns whether or not scissor testing is enabled for this region.  The
 * default is true, except for the overlay display region.
 */
59
inline bool DisplayRegion::get_scissor_enabled(void) const;

1103 17 set_cull_callback 0 4 1353 32 DisplayRegion::set_cull_callback 0 1 224 1064
/**
 * Sets the CallbackObject that will be notified when the DisplayRegion is
 * visited during the cull traversal.  This callback will be made during the
 * cull thread.
 *
 * The cull traversal is responsible for determining which nodes are visible
 * and within the view frustum, and for accumulating state and transform, and
 * generally building up the list of CullableObjects that are to be eventually
 * passed to the draw traversal for rendering.
 *
 * At the time the cull traversal callback is made, the traversal for this
 * DisplayRegion has not yet started.
 *
 * The callback is passed an instance of a DisplayRegionCullCallbackData,
 * which contains pointers to the current scene information, as well as the
 * current DisplayRegion and GSG.  The callback *replaces* the normal cull
 * behavior, so if your callback does nothing, the scene graph will not be
 * traversed and therefore nothing will be drawn.  If you wish the normal cull
 * traversal to be performed for this DisplayRegion, you must call
 * cbdata->upcall() from your callback.
 */
69
inline void DisplayRegion::set_cull_callback(CallbackObject *object);

1104 19 clear_cull_callback 0 4 1353 34 DisplayRegion::clear_cull_callback 0 1 225 78
/**
 * Removes the callback set by an earlier call to set_cull_callback().
 */
53
inline void DisplayRegion::clear_cull_callback(void);

1105 17 get_cull_callback 0 4 1353 32 DisplayRegion::get_cull_callback 0 1 226 65
/**
 * Returns the CallbackObject set by set_cull_callback().
 */
68
inline CallbackObject *DisplayRegion::get_cull_callback(void) const;

1106 17 set_draw_callback 0 4 1353 32 DisplayRegion::set_draw_callback 0 1 227 1346
/**
 * Sets the CallbackObject that will be notified when the contents of
 * DisplayRegion is drawn during the draw traversal.  This callback will be
 * made during the draw thread.
 *
 * The draw traversal is responsible for actually issuing the commands to the
 * graphics engine to draw primitives.  Its job is to walk through the list of
 * CullableObjects build up by the cull traversal, as quickly as possible,
 * issuing the appropriate commands to draw each one.
 *
 * At the time the draw traversal callback is made, the graphics state is in
 * the initial state, and no projection matrix or modelview matrix is in
 * effect.  begin_scene() has not yet been called, and no objects have yet
 * been drawn.  However, the viewport has already been set to the appropriate
 * part of the window, and the clear commands for this DisplayRegion (if any)
 * have been issued.
 *
 * The callback is passed an instance of a DisplayRegionDrawCallbackData,
 * which contains pointers to the current scene information, as well as the
 * current DisplayRegion and GSG.  The callback *replaces* the normal draw
 * behavior, so if your callback does nothing, nothing in the DisplayRegion
 * will be drawn.  If you wish the draw traversal to continue to draw the
 * contents of this DisplayRegion, you must call cbdata->upcall() from your
 * callback.
 */
69
inline void DisplayRegion::set_draw_callback(CallbackObject *object);

1107 19 clear_draw_callback 0 4 1353 34 DisplayRegion::clear_draw_callback 0 1 228 78
/**
 * Removes the callback set by an earlier call to set_draw_callback().
 */
53
inline void DisplayRegion::clear_draw_callback(void);

1108 17 get_draw_callback 0 4 1353 32 DisplayRegion::get_draw_callback 0 1 229 65
/**
 * Returns the CallbackObject set by set_draw_callback().
 */
68
inline CallbackObject *DisplayRegion::get_draw_callback(void) const;

1109 15 get_pixel_width 0 4 1353 30 DisplayRegion::get_pixel_width 0 1 230 60
/**
 * Returns the width of the DisplayRegion in pixels.
 */
59
inline int DisplayRegion::get_pixel_width(int i = 0) const;

1110 16 get_pixel_height 0 4 1353 31 DisplayRegion::get_pixel_height 0 1 231 61
/**
 * Returns the height of the DisplayRegion in pixels.
 */
60
inline int DisplayRegion::get_pixel_height(int i = 0) const;

1111 14 get_pixel_size 0 4 1353 29 DisplayRegion::get_pixel_size 0 1 232 59
/**
 * Returns the size of the DisplayRegion in pixels.
 */
65
inline LVecBase2i DisplayRegion::get_pixel_size(int i = 0) const;

1112 6 output 0 6 1353 21 DisplayRegion::output 0 1 233 10
/**
 *
 */
60
virtual void DisplayRegion::output(std::ostream &out) const;

1113 24 make_screenshot_filename 0 4 1353 39 DisplayRegion::make_screenshot_filename 0 1 234 372
/**
 * Synthesizes a suitable default filename for passing to save_screenshot().
 *
 * The default filename is generated from the supplied prefix and from the
 * Config variable screenshot-filename, which contains the following strings:
 *
 * %~p - the supplied prefix %~f - the frame count %~e - the value of
 * screenshot-extension All other % strings in strftime().
 */
98
static Filename DisplayRegion::make_screenshot_filename(std::string const &prefix = "screenshot");

1114 23 save_screenshot_default 0 4 1353 38 DisplayRegion::save_screenshot_default 0 1 235 198
/**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The filename is
 * generated by make_screenshot_filename().
 */
90
Filename DisplayRegion::save_screenshot_default(std::string const &prefix = "screenshot");

1115 15 save_screenshot 0 4 1353 30 DisplayRegion::save_screenshot 0 1 236 117
/**
 * Saves a screenshot of the region to the indicated filename.  Returns true
 * on success, false on failure.
 */
101
bool DisplayRegion::save_screenshot(Filename const &filename, std::string const &image_comment = "");

1116 14 get_screenshot 0 4 1353 29 DisplayRegion::get_screenshot 0 2 237 238 276
/**
 * Captures the most-recently rendered image from the framebuffer into the
 * indicated PNMImage.  Returns true on success, false on failure.
 */

/**
 * Captures the most-recently rendered image from the framebuffer and returns
 * it as a Texture, or NULL on failure.
 */
110
bool DisplayRegion::get_screenshot(PNMImage &image);
PointerTo< Texture > DisplayRegion::get_screenshot(void);

1117 17 clear_cull_result 0 4 1353 32 DisplayRegion::clear_cull_result 0 1 239 10
/**
 *
 */
44
void DisplayRegion::clear_cull_result(void);

1118 22 make_cull_result_graph 0 6 1353 37 DisplayRegion::make_cull_result_graph 0 1 240 697
/**
 * Returns a special scene graph constructed to represent the results of the
 * last frame's cull operation.
 *
 * This will be a hierarchy of nodes, one node for each bin, each of which
 * will in term be a parent of a number of GeomNodes, representing the
 * geometry drawn in each bin.
 *
 * This is useful mainly for high-level debugging and abstraction tools; it
 * should not be mistaken for the low-level cull result itself, which is
 * constructed and maintained internally.  No such scene graph is normally
 * constructed during the rendering of a frame; this is an artificial
 * construct created for the purpose of making it easy to analyze the results
 * of the cull operation.
 */
75
virtual PointerTo< PandaNode > DisplayRegion::make_cull_result_graph(void);

1119 14 get_class_type 0 4 1353 29 DisplayRegion::get_class_type 0 1 241 0
54
static TypeHandle DisplayRegion::get_class_type(void);

1120 12 get_left_eye 0 4 1394 33 StereoDisplayRegion::get_left_eye 0 1 469 85
/**
 * Returns a pointer to the left DisplayRegion managed by this stereo object.
 */
62
inline DisplayRegion *StereoDisplayRegion::get_left_eye(void);

1121 13 get_right_eye 0 4 1394 34 StereoDisplayRegion::get_right_eye 0 1 470 86
/**
 * Returns a pointer to the right DisplayRegion managed by this stereo object.
 */
63
inline DisplayRegion *StereoDisplayRegion::get_right_eye(void);

1122 14 get_class_type 0 4 1394 35 StereoDisplayRegion::get_class_type 0 1 471 0
60
static TypeHandle StereoDisplayRegion::get_class_type(void);

1123 14 get_depth_bits 0 4 1396 37 FrameBufferProperties::get_depth_bits 0 1 474 58
// Individual queries.

// Individual queries.

/**
 *
 */
61
inline int FrameBufferProperties::get_depth_bits(void) const;

1124 14 get_color_bits 0 4 1396 37 FrameBufferProperties::get_color_bits 0 1 475 10
/**
 *
 */
61
inline int FrameBufferProperties::get_color_bits(void) const;

1125 12 get_red_bits 0 4 1396 35 FrameBufferProperties::get_red_bits 0 1 476 10
/**
 *
 */
59
inline int FrameBufferProperties::get_red_bits(void) const;

1126 14 get_green_bits 0 4 1396 37 FrameBufferProperties::get_green_bits 0 1 477 10
/**
 *
 */
61
inline int FrameBufferProperties::get_green_bits(void) const;

1127 13 get_blue_bits 0 4 1396 36 FrameBufferProperties::get_blue_bits 0 1 478 10
/**
 *
 */
60
inline int FrameBufferProperties::get_blue_bits(void) const;

1128 14 get_alpha_bits 0 4 1396 37 FrameBufferProperties::get_alpha_bits 0 1 479 10
/**
 *
 */
61
inline int FrameBufferProperties::get_alpha_bits(void) const;

1129 16 get_stencil_bits 0 4 1396 39 FrameBufferProperties::get_stencil_bits 0 1 480 10
/**
 *
 */
63
inline int FrameBufferProperties::get_stencil_bits(void) const;

1130 14 get_accum_bits 0 4 1396 37 FrameBufferProperties::get_accum_bits 0 1 481 10
/**
 *
 */
61
inline int FrameBufferProperties::get_accum_bits(void) const;

1131 12 get_aux_rgba 0 4 1396 35 FrameBufferProperties::get_aux_rgba 0 1 482 10
/**
 *
 */
59
inline int FrameBufferProperties::get_aux_rgba(void) const;

1132 13 get_aux_hrgba 0 4 1396 36 FrameBufferProperties::get_aux_hrgba 0 1 483 10
/**
 *
 */
60
inline int FrameBufferProperties::get_aux_hrgba(void) const;

1133 13 get_aux_float 0 4 1396 36 FrameBufferProperties::get_aux_float 0 1 484 10
/**
 *
 */
60
inline int FrameBufferProperties::get_aux_float(void) const;

1134 16 get_multisamples 0 4 1396 39 FrameBufferProperties::get_multisamples 0 1 485 10
/**
 *
 */
63
inline int FrameBufferProperties::get_multisamples(void) const;

1135 20 get_coverage_samples 0 4 1396 43 FrameBufferProperties::get_coverage_samples 0 1 486 110
/**
 * If coverage samples are specified, and there is hardware support, we use
 * coverage multisampling.
 */
67
inline int FrameBufferProperties::get_coverage_samples(void) const;

1136 16 get_back_buffers 0 4 1396 39 FrameBufferProperties::get_back_buffers 0 1 487 10
/**
 *
 */
63
inline int FrameBufferProperties::get_back_buffers(void) const;

1137 17 get_indexed_color 0 4 1396 40 FrameBufferProperties::get_indexed_color 0 1 488 10
/**
 *
 */
65
inline bool FrameBufferProperties::get_indexed_color(void) const;

1138 13 get_rgb_color 0 4 1396 36 FrameBufferProperties::get_rgb_color 0 1 489 10
/**
 *
 */
61
inline bool FrameBufferProperties::get_rgb_color(void) const;

1139 10 get_stereo 0 4 1396 33 FrameBufferProperties::get_stereo 0 1 490 10
/**
 *
 */
58
inline bool FrameBufferProperties::get_stereo(void) const;

1140 18 get_force_hardware 0 4 1396 41 FrameBufferProperties::get_force_hardware 0 1 491 10
/**
 *
 */
66
inline bool FrameBufferProperties::get_force_hardware(void) const;

1141 18 get_force_software 0 4 1396 41 FrameBufferProperties::get_force_software 0 1 492 10
/**
 *
 */
66
inline bool FrameBufferProperties::get_force_software(void) const;

1142 14 get_srgb_color 0 4 1396 37 FrameBufferProperties::get_srgb_color 0 1 493 10
/**
 *
 */
62
inline bool FrameBufferProperties::get_srgb_color(void) const;

1143 15 get_float_color 0 4 1396 38 FrameBufferProperties::get_float_color 0 1 494 10
/**
 *
 */
63
inline bool FrameBufferProperties::get_float_color(void) const;

1144 15 get_float_depth 0 4 1396 38 FrameBufferProperties::get_float_depth 0 1 495 10
/**
 *
 */
63
inline bool FrameBufferProperties::get_float_depth(void) const;

1145 14 set_depth_bits 0 4 1396 37 FrameBufferProperties::set_depth_bits 0 1 496 62
// Individual assigners.

// Individual assigners.

/**
 *
 */
57
inline void FrameBufferProperties::set_depth_bits(int n);

1146 14 set_color_bits 0 4 1396 37 FrameBufferProperties::set_color_bits 0 1 497 317
/**
 * Sets the number of requested color bits as a single number that represents
 * the sum of the individual numbers of red, green and blue bits.  Panda won't
 * care how the individual bits are divided up.
 *
 * See also set_rgba_bits, which allows you to specify requirements for the
 * individual components.
 */
57
inline void FrameBufferProperties::set_color_bits(int n);

1147 13 set_rgba_bits 0 4 1396 36 FrameBufferProperties::set_rgba_bits 0 1 498 91
/**
 * Convenience method for setting the red, green, blue and alpha bits in one
 * go.
 */
77
inline void FrameBufferProperties::set_rgba_bits(int r, int g, int b, int a);

1148 12 set_red_bits 0 4 1396 35 FrameBufferProperties::set_red_bits 0 1 499 10
/**
 *
 */
55
inline void FrameBufferProperties::set_red_bits(int n);

1149 14 set_green_bits 0 4 1396 37 FrameBufferProperties::set_green_bits 0 1 500 10
/**
 *
 */
57
inline void FrameBufferProperties::set_green_bits(int n);

1150 13 set_blue_bits 0 4 1396 36 FrameBufferProperties::set_blue_bits 0 1 501 10
/**
 *
 */
56
inline void FrameBufferProperties::set_blue_bits(int n);

1151 14 set_alpha_bits 0 4 1396 37 FrameBufferProperties::set_alpha_bits 0 1 502 10
/**
 *
 */
57
inline void FrameBufferProperties::set_alpha_bits(int n);

1152 16 set_stencil_bits 0 4 1396 39 FrameBufferProperties::set_stencil_bits 0 1 503 10
/**
 *
 */
59
inline void FrameBufferProperties::set_stencil_bits(int n);

1153 14 set_accum_bits 0 4 1396 37 FrameBufferProperties::set_accum_bits 0 1 504 10
/**
 *
 */
57
inline void FrameBufferProperties::set_accum_bits(int n);

1154 12 set_aux_rgba 0 4 1396 35 FrameBufferProperties::set_aux_rgba 0 1 505 10
/**
 *
 */
55
inline void FrameBufferProperties::set_aux_rgba(int n);

1155 13 set_aux_hrgba 0 4 1396 36 FrameBufferProperties::set_aux_hrgba 0 1 506 10
/**
 *
 */
56
inline void FrameBufferProperties::set_aux_hrgba(int n);

1156 13 set_aux_float 0 4 1396 36 FrameBufferProperties::set_aux_float 0 1 507 10
/**
 *
 */
56
inline void FrameBufferProperties::set_aux_float(int n);

1157 16 set_multisamples 0 4 1396 39 FrameBufferProperties::set_multisamples 0 1 508 10
/**
 *
 */
59
inline void FrameBufferProperties::set_multisamples(int n);

1158 20 set_coverage_samples 0 4 1396 43 FrameBufferProperties::set_coverage_samples 0 1 509 109
/**
 * If coverage samples are specified, and there is hardware support, we use
 * coverage multisampling
 */
63
inline void FrameBufferProperties::set_coverage_samples(int n);

1159 16 set_back_buffers 0 4 1396 39 FrameBufferProperties::set_back_buffers 0 1 510 10
/**
 *
 */
59
inline void FrameBufferProperties::set_back_buffers(int n);

1160 17 set_indexed_color 0 4 1396 40 FrameBufferProperties::set_indexed_color 0 1 511 10
/**
 *
 */
61
inline void FrameBufferProperties::set_indexed_color(bool n);

1161 13 set_rgb_color 0 4 1396 36 FrameBufferProperties::set_rgb_color 0 1 512 10
/**
 *
 */
57
inline void FrameBufferProperties::set_rgb_color(bool n);

1162 10 set_stereo 0 4 1396 33 FrameBufferProperties::set_stereo 0 1 513 10
/**
 *
 */
54
inline void FrameBufferProperties::set_stereo(bool n);

1163 18 set_force_hardware 0 4 1396 41 FrameBufferProperties::set_force_hardware 0 1 514 10
/**
 *
 */
62
inline void FrameBufferProperties::set_force_hardware(bool n);

1164 18 set_force_software 0 4 1396 41 FrameBufferProperties::set_force_software 0 1 515 10
/**
 *
 */
62
inline void FrameBufferProperties::set_force_software(bool n);

1165 14 set_srgb_color 0 4 1396 37 FrameBufferProperties::set_srgb_color 0 1 516 10
/**
 *
 */
58
inline void FrameBufferProperties::set_srgb_color(bool n);

1166 15 set_float_color 0 4 1396 38 FrameBufferProperties::set_float_color 0 1 517 10
/**
 *
 */
59
inline void FrameBufferProperties::set_float_color(bool n);

1167 15 set_float_depth 0 4 1396 38 FrameBufferProperties::set_float_depth 0 1 518 10
/**
 *
 */
59
inline void FrameBufferProperties::set_float_depth(bool n);

1168 21 FrameBufferProperties 0 260 1396 44 FrameBufferProperties::FrameBufferProperties 0 2 472 473 0
165
constexpr FrameBufferProperties::FrameBufferProperties(void) = default;
inline FrameBufferProperties::FrameBufferProperties(FrameBufferProperties const &) = default;

1169 11 get_default 0 4 1396 34 FrameBufferProperties::get_default 0 1 519 133
/**
 * Returns a FrameBufferProperties structure with all of the default values
 * filled in according to the user's config file.
 */
77
static FrameBufferProperties const &FrameBufferProperties::get_default(void);

1170 11 operator == 0 4 1396 34 FrameBufferProperties::operator == 0 1 520 0
82
bool FrameBufferProperties::operator ==(FrameBufferProperties const &other) const;

1171 11 operator != 0 4 1396 34 FrameBufferProperties::operator != 0 1 521 0
89
inline bool FrameBufferProperties::operator !=(FrameBufferProperties const &other) const;

1172 5 clear 0 4 1396 28 FrameBufferProperties::clear 0 1 522 143
/**
 * Unsets all properties that have been specified so far, and resets the
 * FrameBufferProperties structure to its initial empty state.
 */
40
void FrameBufferProperties::clear(void);

1173 17 set_all_specified 0 4 1396 40 FrameBufferProperties::set_all_specified 0 1 523 51
/**
 * Marks all bits as having been specified.
 */
52
void FrameBufferProperties::set_all_specified(void);

1174 8 subsumes 0 4 1396 31 FrameBufferProperties::subsumes 0 1 524 157
/**
 * Returns true if this set of properties makes strictly greater or equal
 * demands of the framebuffer than the other set of framebuffer properties.
 */
79
bool FrameBufferProperties::subsumes(FrameBufferProperties const &other) const;

1175 14 add_properties 0 4 1396 37 FrameBufferProperties::add_properties 0 1 525 123
/**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */
79
void FrameBufferProperties::add_properties(FrameBufferProperties const &other);

1176 6 output 0 4 1396 29 FrameBufferProperties::output 0 1 526 45
/**
 * Generates a string representation.
 */
60
void FrameBufferProperties::output(std::ostream &out) const;

1177 23 set_one_bit_per_channel 0 4 1396 46 FrameBufferProperties::set_one_bit_per_channel 0 1 527 132
/**
 * If any of the depth, color, alpha, accum, or stencil properties is set to
 * more than one, then they are reduced to one.
 */
58
void FrameBufferProperties::set_one_bit_per_channel(void);

1178 9 is_stereo 0 4 1396 32 FrameBufferProperties::is_stereo 0 1 528 10
/**
 *
 */
57
inline bool FrameBufferProperties::is_stereo(void) const;

1179 18 is_single_buffered 0 4 1396 41 FrameBufferProperties::is_single_buffered 0 1 529 10
/**
 *
 */
66
inline bool FrameBufferProperties::is_single_buffered(void) const;

1180 11 get_quality 0 4 1396 34 FrameBufferProperties::get_quality 0 1 530 914
/**
 * Assumes that these properties are a description of a window.
 *
 * Measures how well this window satisfies a specified set of requirements.  A
 * higher quality number means that more requirements were satisfied.  A
 * quality of zero means that the window is unsuitable.
 *
 * The routine deducts a lot if the window fails to provide a requested
 * feature.  It deducts less if the window provides a feature, but at a
 * degraded level of functionality (ie, the user asks for rgba8, color, but
 * the window only provides rgba4).  The routine also deducts a small amount
 * for unnecessary features.  For example, if the window has an accumulation
 * buffer when one is not requested will reduce quality slightly.  Maximum
 * quality is obtained when the window exactly matches the request.
 *
 * If you want to know whether the window satisfies all of the requirements,
 * use the "subsumes" function.
 */
80
int FrameBufferProperties::get_quality(FrameBufferProperties const &reqs) const;

1181 16 is_any_specified 0 4 1396 39 FrameBufferProperties::is_any_specified 0 1 531 79
/**
 * Returns true if any properties have been specified, false otherwise.
 */
57
bool FrameBufferProperties::is_any_specified(void) const;

1182 8 is_basic 0 4 1396 31 FrameBufferProperties::is_basic 0 1 532 181
/**
 * Returns true if the properties are extremely basic.  The following count as
 * basic: rgb or rgba, depth.  If anything else is specified, the properties
 * are non-basic.
 */
49
bool FrameBufferProperties::is_basic(void) const;

1183 12 get_aux_mask 0 4 1396 35 FrameBufferProperties::get_aux_mask 0 1 533 83
/**
 * Converts the aux bitplanes of the framebuffer into a RenderBuffer::Type.
 */
52
int FrameBufferProperties::get_aux_mask(void) const;

1184 15 get_buffer_mask 0 4 1396 38 FrameBufferProperties::get_buffer_mask 0 1 534 90
/**
 * Converts the non-aux bitplanes of the framebuffer into a
 * RenderBuffer::Type.
 */
55
int FrameBufferProperties::get_buffer_mask(void) const;

1185 24 verify_hardware_software 0 4 1396 47 FrameBufferProperties::verify_hardware_software 0 1 535 165
/**
 * Validates that the properties represent the desired kind of renderer
 * (hardware or software).  If not, prints out an error message and returns
 * false.
 */
124
bool FrameBufferProperties::verify_hardware_software(FrameBufferProperties const &props, std::string const &renderer) const;

1186 19 setup_color_texture 0 4 1396 42 FrameBufferProperties::setup_color_texture 0 1 536 295
/**
 * Sets the texture up for render-to-texture matching these framebuffer
 * properties.
 *
 * Returns true if there was a format that had enough bits, false otherwise.
 * Of course, this is no guarantee that a particular graphics back-end
 * supports rendering to textures of that format.
 */
68
bool FrameBufferProperties::setup_color_texture(Texture *tex) const;

1187 19 setup_depth_texture 0 4 1396 42 FrameBufferProperties::setup_depth_texture 0 1 537 295
/**
 * Sets the texture up for render-to-texture matching these framebuffer
 * properties.
 *
 * Returns true if there was a format that had enough bits, false otherwise.
 * Of course, this is no guarantee that a particular graphics back-end
 * supports rendering to textures of that format.
 */
68
bool FrameBufferProperties::setup_depth_texture(Texture *tex) const;

1188 22 ~FrameBufferProperties 0 516 1396 45 FrameBufferProperties::~FrameBufferProperties 0 0 0
52
FrameBufferProperties::~FrameBufferProperties(void);

1189 11 button_down 0 4 1397 38 GraphicsWindowInputDevice::button_down 0 1 538 182
// The following interface is for the various kinds of GraphicsWindows to
// record the data incoming on the device.

/**
 * Records that the indicated button has been depressed.
 */
130
void GraphicsWindowInputDevice::button_down(ButtonHandle button, double time = ClockObject::get_global_clock()->get_frame_time());

1190 18 button_resume_down 0 4 1397 45 GraphicsWindowInputDevice::button_resume_down 0 1 539 189
/**
 * Records that the indicated button was depressed earlier, and we only just
 * detected the event after the fact.  This is mainly useful for tracking the
 * state of modifier keys.
 */
137
void GraphicsWindowInputDevice::button_resume_down(ButtonHandle button, double time = ClockObject::get_global_clock()->get_frame_time());

1191 9 button_up 0 4 1397 36 GraphicsWindowInputDevice::button_up 0 1 540 63
/**
 * Records that the indicated button has been released.
 */
128
void GraphicsWindowInputDevice::button_up(ButtonHandle button, double time = ClockObject::get_global_clock()->get_frame_time());

1192 9 keystroke 0 4 1397 36 GraphicsWindowInputDevice::keystroke 0 1 541 67
/**
 * Records that the indicated keystroke has been generated.
 */
120
void GraphicsWindowInputDevice::keystroke(int keycode, double time = ClockObject::get_global_clock()->get_frame_time());

1193 9 candidate 0 4 1397 36 GraphicsWindowInputDevice::candidate 0 1 542 196
/**
 * Records that the indicated candidate string has been highlighted.  This is
 * used to implement IME support for typing in international languages,
 * especially Chinese/Japanese/Korean.
 */
160
void GraphicsWindowInputDevice::candidate(std::wstring const &candidate_string, std::size_t highlight_start, std::size_t highlight_end, std::size_t cursor_pos);

1194 10 focus_lost 0 4 1397 37 GraphicsWindowInputDevice::focus_lost 0 1 543 356
/**
 * This should be called when the window focus is lost, so that we may miss
 * upcoming button events (especially "up" events) for the next period of
 * time.  It generates keyboard and mouse "up" events for those buttons that
 * we previously sent unpaired "down" events, so that the Panda application
 * will believe all buttons are now released.
 */
108
void GraphicsWindowInputDevice::focus_lost(double time = ClockObject::get_global_clock()->get_frame_time());

1195 15 raw_button_down 0 4 1397 42 GraphicsWindowInputDevice::raw_button_down 0 1 544 64
/**
 * Records that the indicated button has been depressed.
 */
134
void GraphicsWindowInputDevice::raw_button_down(ButtonHandle button, double time = ClockObject::get_global_clock()->get_frame_time());

1196 13 raw_button_up 0 4 1397 40 GraphicsWindowInputDevice::raw_button_up 0 1 545 63
/**
 * Records that the indicated button has been released.
 */
132
void GraphicsWindowInputDevice::raw_button_up(ButtonHandle button, double time = ClockObject::get_global_clock()->get_frame_time());

1197 11 get_pointer 0 4 1397 38 GraphicsWindowInputDevice::get_pointer 0 1 546 140
/**
 * Returns the PointerData associated with the input device's pointer.  This
 * only makes sense if has_pointer() also returns true.
 */
70
inline PointerData GraphicsWindowInputDevice::get_pointer(void) const;

1198 21 set_pointer_in_window 0 4 1397 48 GraphicsWindowInputDevice::set_pointer_in_window 0 1 547 148
/**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer is within the window, at the given pixel coordinates.
 */
139
void GraphicsWindowInputDevice::set_pointer_in_window(double x, double y, double time = ClockObject::get_global_clock()->get_frame_time());

1199 25 set_pointer_out_of_window 0 4 1397 52 GraphicsWindowInputDevice::set_pointer_out_of_window 0 1 548 126
/**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer is no longer within the window.
 */
123
void GraphicsWindowInputDevice::set_pointer_out_of_window(double time = ClockObject::get_global_clock()->get_frame_time());

1200 14 update_pointer 0 4 1397 41 GraphicsWindowInputDevice::update_pointer 0 1 549 112
/**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer data has changed.
 */
137
inline void GraphicsWindowInputDevice::update_pointer(PointerData data, double time = ClockObject::get_global_clock()->get_frame_time());

1201 13 pointer_moved 0 4 1397 40 GraphicsWindowInputDevice::pointer_moved 0 1 550 134
/**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer has moved by the given relative amount.
 */
138
inline void GraphicsWindowInputDevice::pointer_moved(double x, double y, double time = ClockObject::get_global_clock()->get_frame_time());

1202 14 remove_pointer 0 4 1397 41 GraphicsWindowInputDevice::remove_pointer 0 1 551 112
/**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer no longer exists.
 */
62
inline void GraphicsWindowInputDevice::remove_pointer(int id);

1203 14 get_class_type 0 4 1397 41 GraphicsWindowInputDevice::get_class_type 0 1 552 0
66
static TypeHandle GraphicsWindowInputDevice::get_class_type(void);

1204 26 ~GraphicsWindowInputDevice 0 516 1397 53 GraphicsWindowInputDevice::~GraphicsWindowInputDevice 0 0 0
60
GraphicsWindowInputDevice::~GraphicsWindowInputDevice(void);

1205 5 get_x 0 4 1399 16 TouchInfo::get_x 0 1 554 0
27
int TouchInfo::get_x(void);

1206 5 get_y 0 4 1399 16 TouchInfo::get_y 0 1 555 0
27
int TouchInfo::get_y(void);

1207 6 get_id 0 4 1399 17 TouchInfo::get_id 0 1 556 0
28
int TouchInfo::get_id(void);

1208 9 get_flags 0 4 1399 20 TouchInfo::get_flags 0 1 557 0
31
int TouchInfo::get_flags(void);

1209 9 TouchInfo 0 260 1399 20 TouchInfo::TouchInfo 0 1 553 0
57
inline TouchInfo::TouchInfo(TouchInfo const &) = default;

1210 10 ~TouchInfo 0 516 1399 21 TouchInfo::~TouchInfo 0 0 0
28
TouchInfo::~TouchInfo(void);

1211 8 get_hwnd 0 4 1401 40 GraphicsWindowProcCallbackData::get_hwnd 0 1 558 51
/**
 * Returns the Windows proc hwnd parameter.
 */
70
inline uintptr_t GraphicsWindowProcCallbackData::get_hwnd(void) const;

1212 7 get_msg 0 4 1401 39 GraphicsWindowProcCallbackData::get_msg 0 1 559 50
/**
 * Returns the Windows proc msg parameter.
 */
63
inline int GraphicsWindowProcCallbackData::get_msg(void) const;

1213 10 get_wparam 0 4 1401 42 GraphicsWindowProcCallbackData::get_wparam 0 1 560 53
/**
 * Returns the Windows proc wparam parameter.
 */
66
inline int GraphicsWindowProcCallbackData::get_wparam(void) const;

1214 10 get_lparam 0 4 1401 42 GraphicsWindowProcCallbackData::get_lparam 0 1 561 53
/**
 * Returns the Windows proc lparam parameter.
 */
66
inline int GraphicsWindowProcCallbackData::get_lparam(void) const;

1215 14 is_touch_event 0 4 1401 46 GraphicsWindowProcCallbackData::is_touch_event 0 1 562 57
/**
 * Returns whether the event is a touch event.
 *
 */
58
bool GraphicsWindowProcCallbackData::is_touch_event(void);

1216 15 get_num_touches 0 4 1401 47 GraphicsWindowProcCallbackData::get_num_touches 0 1 563 66
/**
 * Returns the current number of touches on the window.
 *
 */
58
int GraphicsWindowProcCallbackData::get_num_touches(void);

1217 14 get_touch_info 0 4 1401 46 GraphicsWindowProcCallbackData::get_touch_info 0 1 564 74
/**
 * Returns the TouchInfo object describing the specified touch.
 *
 */
68
TouchInfo GraphicsWindowProcCallbackData::get_touch_info(int index);

1218 14 get_class_type 0 4 1401 46 GraphicsWindowProcCallbackData::get_class_type 0 1 565 0
71
static TypeHandle GraphicsWindowProcCallbackData::get_class_type(void);

1219 31 ~GraphicsWindowProcCallbackData 0 516 1401 63 GraphicsWindowProcCallbackData::~GraphicsWindowProcCallbackData 0 0 0
70
GraphicsWindowProcCallbackData::~GraphicsWindowProcCallbackData(void);

1220 14 get_properties 0 4 1403 30 GraphicsWindow::get_properties 0 1 566 56
/**
 * Returns the current properties of the window.
 */
66
WindowProperties const GraphicsWindow::get_properties(void) const;

1221 24 get_requested_properties 0 4 1403 40 GraphicsWindow::get_requested_properties 0 1 567 185
/**
 * Returns the properties of the window that are currently requested.  These
 * properties will be applied to the window (if valid) at the next execution
 * of process_events().
 */
76
WindowProperties const GraphicsWindow::get_requested_properties(void) const;

1222 25 clear_rejected_properties 0 4 1403 41 GraphicsWindow::clear_rejected_properties 0 1 568 102
/**
 * Empties the set of failed properties that will be returned by
 * get_rejected_properties().
 */
53
void GraphicsWindow::clear_rejected_properties(void);

1223 23 get_rejected_properties 0 4 1403 39 GraphicsWindow::get_rejected_properties 0 1 569 276
/**
 * Returns the set of properties that have recently been requested, but could
 * not be applied to the window for some reason.  This set of properties will
 * remain unchanged until they are changed by a new failed request, or
 * clear_rejected_properties() is called.
 */
69
WindowProperties GraphicsWindow::get_rejected_properties(void) const;

1224 18 request_properties 0 4 1403 34 GraphicsWindow::request_properties 0 1 570 307
/**
 * Requests a property change on the window.  For example, use this method to
 * request a window change size or minimize or something.
 *
 * The change is not made immediately; rather, the request is saved and will
 * be applied the next time the window task is run (probably at the next
 * frame).
 */
86
void GraphicsWindow::request_properties(WindowProperties const &requested_properties);

1225 9 is_closed 0 4 1403 25 GraphicsWindow::is_closed 0 1 571 327
/**
 * Returns true if the window has not yet been opened, or has been fully
 * closed, false if it is open.  The window is not opened immediately after
 * GraphicsEngine::make_output() is called; nor is it closed immediately after
 * GraphicsEngine::remove_window() is called.  Either operation may take a
 * frame or two.
 */
50
inline bool GraphicsWindow::is_closed(void) const;

1226 13 is_fullscreen 0 4 1403 29 GraphicsWindow::is_fullscreen 0 1 572 97
/**
 * Returns true if the window has been opened as a fullscreen window, false
 * otherwise.
 */
54
inline bool GraphicsWindow::is_fullscreen(void) const;

1227 16 set_window_event 0 4 1403 32 GraphicsWindow::set_window_event 0 1 573 320
/**
 * Changes the name of the event that is generated when this window is
 * modified externally, e.g.  to be resized or closed by the user.
 *
 * By default, all windows have the same window event unless they are
 * explicitly changed.  When the event is generated, it includes one
 * parameter: the window itself.
 */
71
void GraphicsWindow::set_window_event(std::string const &window_event);

1228 16 get_window_event 0 4 1403 32 GraphicsWindow::get_window_event 0 1 574 173
/**
 * Returns the name of the event that is generated when this window is
 * modified externally, e.g.  to be resized or closed by the user.  See
 * set_window_event().
 */
57
std::string GraphicsWindow::get_window_event(void) const;

1229 23 set_close_request_event 0 4 1403 39 GraphicsWindow::set_close_request_event 0 1 575 914
/**
 * Sets the event that is triggered when the user requests to close the
 * window, e.g.  via alt-F4, or clicking on the close box.
 *
 * The default for each window is for this event to be the empty string, which
 * means the window-close request is handled immediately by Panda (and the
 * window will be closed without the app getting a chance to intervene).  If
 * you set this to a nonempty string, then the window is not closed, but
 * instead the event is thrown.  It is then up to the app to respond
 * appropriately, for instance by presenting an "are you sure?"  dialog box,
 * and eventually calling close_window() when the user is sure.
 *
 * It is considered poor form to set this string and then not handle the
 * event.  This can frustrate the user by making it difficult for him to
 * cleanly shut down the application (and may force the user to hard-kill the
 * app, or reboot the machine).
 */
85
void GraphicsWindow::set_close_request_event(std::string const &close_request_event);

1230 23 get_close_request_event 0 4 1403 39 GraphicsWindow::get_close_request_event 0 1 576 225
/**
 * Returns the name of the event set via set_close_request_event().  If this
 * string is nonempty, then when the user requests to close window, this event
 * will be generated instead.  See set_close_request_event().
 */
64
std::string GraphicsWindow::get_close_request_event(void) const;

1231 18 set_unexposed_draw 0 4 1403 34 GraphicsWindow::set_unexposed_draw 0 1 577 536
/**
 * If this flag is false, the window is redrawn only after it has received a
 * recent "unexpose" or "draw" event from the underlying windowing system.  If
 * this flag is true, the window is redrawn every frame regardless.  Setting
 * this false may prevent the window from redrawing unnecessarily when it is
 * hidden, and may play nicer with other windows on the desktop, but may
 * adversely affect frame rate even when the window is fully visible; setting
 * it true will ensure that the window contents are always current.
 */
68
inline void GraphicsWindow::set_unexposed_draw(bool unexposed_draw);

1232 18 get_unexposed_draw 0 4 1403 34 GraphicsWindow::get_unexposed_draw 0 1 578 36
/**
 * See set_unexposed_draw().
 */
59
inline bool GraphicsWindow::get_unexposed_draw(void) const;

1233 17 get_window_handle 0 4 1403 33 GraphicsWindow::get_window_handle 0 1 579 329
/**
 * Returns the WindowHandle corresponding to this window on the desktop.  This
 * is mainly useful for communicating with external libraries.  Use
 * window_handle->get_os_handle()->get_handle(), or
 * window_handle->get_string_handle(), to get the actual OS-specific window
 * handle object, whatever type that might be.
 */
67
inline WindowHandle *GraphicsWindow::get_window_handle(void) const;

1234 21 get_num_input_devices 0 4 1403 37 GraphicsWindow::get_num_input_devices 0 1 580 387
// Mouse and keyboard routines

// Mouse and keyboard routines

// Mouse and keyboard routines

/**
 * Returns the number of separate input devices associated with the window.
 * Typically, a window will have exactly one input device: the keyboard/mouse
 * pair.  However, some windows may have no input devices, and others may add
 * additional devices, for instance for a joystick.
 */
54
int GraphicsWindow::get_num_input_devices(void) const;

1235 16 get_input_device 0 4 1403 32 GraphicsWindow::get_input_device 0 1 581 152
/**
 * Returns the nth input device associated with the window.  Typically, a
 * window will have exactly one input device: the keyboard/mouse pair.
 */
59
InputDevice *GraphicsWindow::get_input_device(int i) const;

1236 21 get_input_device_name 0 4 1403 37 GraphicsWindow::get_input_device_name 0 1 582 52
/**
 * Returns the name of the nth input device.
 */
68
std::string GraphicsWindow::get_input_device_name(int device) const;

1237 11 has_pointer 0 4 1403 27 GraphicsWindow::has_pointer 0 1 583 119
/**
 * Returns true if the nth input device has a screen-space pointer (for
 * instance, a mouse), false otherwise.
 */
51
bool GraphicsWindow::has_pointer(int device) const;

1238 12 has_keyboard 0 4 1403 28 GraphicsWindow::has_keyboard 0 1 584 80
/**
 * Returns true if the nth input device has a keyboard, false otherwise.
 */
52
bool GraphicsWindow::has_keyboard(int device) const;

1239 16 get_keyboard_map 0 6 1403 32 GraphicsWindow::get_keyboard_map 0 1 585 101
/**
 * Returns a ButtonMap containing the association between raw buttons and
 * virtual buttons.
 */
64
virtual ButtonMap *GraphicsWindow::get_keyboard_map(void) const;

1240 21 enable_pointer_events 0 4 1403 37 GraphicsWindow::enable_pointer_events 0 1 586 52
/**
 * Turn on the generation of pointer events.
 */
55
void GraphicsWindow::enable_pointer_events(int device);

1241 22 disable_pointer_events 0 4 1403 38 GraphicsWindow::disable_pointer_events 0 1 587 53
/**
 * Turn off the generation of pointer events.
 */
56
void GraphicsWindow::disable_pointer_events(int device);

1242 11 get_pointer 0 6 1403 27 GraphicsWindow::get_pointer 0 1 588 206
/**
 * Returns the MouseData associated with the nth input device's pointer.
 * Using this to access raw mice (with an index other than 0) is deprecated,
 * see the InputDeviceManager interface instead.
 */
64
virtual MouseData GraphicsWindow::get_pointer(int device) const;

1243 12 move_pointer 0 6 1403 28 GraphicsWindow::move_pointer 0 1 589 258
/**
 * Forces the pointer to the indicated position within the window, if
 * possible.
 *
 * Returns true if successful, false on failure.  This may fail if the mouse
 * is not currently within the window, or if the API doesn't support this
 * operation.
 */
68
virtual bool GraphicsWindow::move_pointer(int device, int x, int y);

1244 9 close_ime 0 6 1403 25 GraphicsWindow::close_ime 0 1 590 51
/**
 * Forces the ime window to close if any
 *
 */
45
virtual void GraphicsWindow::close_ime(void);

1245 14 get_class_type 0 4 1403 30 GraphicsWindow::get_class_type 0 1 591 0
55
static TypeHandle GraphicsWindow::get_class_type(void);

1246 10 get_window 0 4 1405 54 CallbackGraphicsWindow::WindowCallbackData::get_window 0 1 603 63
/**
 * Returns the window this callback was triggered from.
 */
98
inline CallbackGraphicsWindow *CallbackGraphicsWindow::WindowCallbackData::get_window(void) const;

1247 14 get_class_type 0 4 1405 58 CallbackGraphicsWindow::WindowCallbackData::get_class_type 0 1 604 0
83
static TypeHandle CallbackGraphicsWindow::WindowCallbackData::get_class_type(void);

1248 19 ~WindowCallbackData 0 516 1405 63 CallbackGraphicsWindow::WindowCallbackData::~WindowCallbackData 0 0 0
70
CallbackGraphicsWindow::WindowCallbackData::~WindowCallbackData(void);

1249 14 get_class_type 0 4 1407 58 CallbackGraphicsWindow::EventsCallbackData::get_class_type 0 1 605 0
83
static TypeHandle CallbackGraphicsWindow::EventsCallbackData::get_class_type(void);

1250 19 ~EventsCallbackData 0 516 1407 63 CallbackGraphicsWindow::EventsCallbackData::~EventsCallbackData 0 0 0
70
CallbackGraphicsWindow::EventsCallbackData::~EventsCallbackData(void);

1251 14 get_properties 0 4 1408 62 CallbackGraphicsWindow::PropertiesCallbackData::get_properties 0 1 606 208
/**
 * Returns the WindowProperties object that this callback should process.  Any
 * properties that are handled should be removed from this object; properties
 * that are unhandled should be left alone.
 */
100
inline WindowProperties &CallbackGraphicsWindow::PropertiesCallbackData::get_properties(void) const;

1252 14 get_class_type 0 4 1408 62 CallbackGraphicsWindow::PropertiesCallbackData::get_class_type 0 1 607 0
87
static TypeHandle CallbackGraphicsWindow::PropertiesCallbackData::get_class_type(void);

1253 17 get_callback_type 0 4 1410 61 CallbackGraphicsWindow::RenderCallbackData::get_callback_type 0 1 608 172
/**
 * Since the render callback is shared for several functions, this method is
 * needed to indicate which particular function is being invoked with this
 * callback.
 */
124
inline CallbackGraphicsWindow::RenderCallbackType CallbackGraphicsWindow::RenderCallbackData::get_callback_type(void) const;

1254 14 get_frame_mode 0 4 1410 58 CallbackGraphicsWindow::RenderCallbackData::get_frame_mode 0 1 609 215
/**
 * If the callback type (returned by get_callback_type) is RCT_begin_frame or
 * RCT_end_frame, then this method will return the particular frame mode
 * indicating what, precisely, we want to do this frame.
 */
104
inline GraphicsOutput::FrameMode CallbackGraphicsWindow::RenderCallbackData::get_frame_mode(void) const;

1255 15 set_render_flag 0 4 1410 59 CallbackGraphicsWindow::RenderCallbackData::set_render_flag 0 1 610 242
/**
 * If the callback type is RCT_begin_frame, this call is available to specify
 * the return value from the begin_frame() call.  If this is true (the
 * default), the frame is rendered normally; if it is false, the frame is
 * omitted.
 */
90
inline void CallbackGraphicsWindow::RenderCallbackData::set_render_flag(bool render_flag);

1256 15 get_render_flag 0 4 1410 59 CallbackGraphicsWindow::RenderCallbackData::get_render_flag 0 1 611 82
/**
 * Returns the current setting of the render flag.  See set_render_flag().
 */
84
inline bool CallbackGraphicsWindow::RenderCallbackData::get_render_flag(void) const;

1257 14 get_class_type 0 4 1410 58 CallbackGraphicsWindow::RenderCallbackData::get_class_type 0 1 612 0
83
static TypeHandle CallbackGraphicsWindow::RenderCallbackData::get_class_type(void);

1258 19 ~RenderCallbackData 0 516 1410 63 CallbackGraphicsWindow::RenderCallbackData::~RenderCallbackData 0 0 0
70
CallbackGraphicsWindow::RenderCallbackData::~RenderCallbackData(void);

1259 19 set_events_callback 0 4 1404 43 CallbackGraphicsWindow::set_events_callback 0 1 592 462
/**
 * Sets the CallbackObject that will be notified when this window is polled
 * for window events, including mouse and keyboard events, as well as window
 * resize events and other system-generated events.
 *
 * This callback will receive a CallbackGraphicsWindow::EventsCallbackData.
 *
 * This callback should process any system-generated events, and call
 * data->upcall() to process requested property change requests made via
 * request_properties().
 */
80
inline void CallbackGraphicsWindow::set_events_callback(CallbackObject *object);

1260 21 clear_events_callback 0 4 1404 45 CallbackGraphicsWindow::clear_events_callback 0 1 593 80
/**
 * Removes the callback set by an earlier call to set_events_callback().
 */
64
inline void CallbackGraphicsWindow::clear_events_callback(void);

1261 19 get_events_callback 0 4 1404 43 CallbackGraphicsWindow::get_events_callback 0 1 594 67
/**
 * Returns the CallbackObject set by set_events_callback().
 */
79
inline CallbackObject *CallbackGraphicsWindow::get_events_callback(void) const;

1262 23 set_properties_callback 0 4 1404 47 CallbackGraphicsWindow::set_properties_callback 0 1 595 672
/**
 * Sets the CallbackObject that will be notified when this window receives a
 * property change request from user code (e.g.  via request_properties).
 *
 * This callback will receive a
 * CallbackGraphicsWindow::PropertiesCallbackData, which provides a
 * get_properties() method that returns a modifiable reference to a
 * WindowsProperties object.  This object will contain only those properties
 * requested by user code.  The callback should handle any of the requests it
 * finds, including and especially set_open(), and remove them from the object
 * when it has handled them.  Any unhandled properties should be left
 * unchanged in the properties object.
 */
84
inline void CallbackGraphicsWindow::set_properties_callback(CallbackObject *object);

1263 25 clear_properties_callback 0 4 1404 49 CallbackGraphicsWindow::clear_properties_callback 0 1 596 84
/**
 * Removes the callback set by an earlier call to set_properties_callback().
 */
68
inline void CallbackGraphicsWindow::clear_properties_callback(void);

1264 23 get_properties_callback 0 4 1404 47 CallbackGraphicsWindow::get_properties_callback 0 1 597 71
/**
 * Returns the CallbackObject set by set_properties_callback().
 */
83
inline CallbackObject *CallbackGraphicsWindow::get_properties_callback(void) const;

1265 19 set_render_callback 0 4 1404 43 CallbackGraphicsWindow::set_render_callback 0 1 598 383
/**
 * Sets the CallbackObject that will be notified when this window is invoked
 * (in the draw thread) to render its contents, and/or flip the graphics
 * buffers.
 *
 * This callback will actually serve several different functions.  It
 * receivces a RenderCallbackData, and you can query data->get_callback_type()
 * to return the actual function of each particular callback.
 */
80
inline void CallbackGraphicsWindow::set_render_callback(CallbackObject *object);

1266 21 clear_render_callback 0 4 1404 45 CallbackGraphicsWindow::clear_render_callback 0 1 599 80
/**
 * Removes the callback set by an earlier call to set_render_callback().
 */
64
inline void CallbackGraphicsWindow::clear_render_callback(void);

1267 19 get_render_callback 0 4 1404 43 CallbackGraphicsWindow::get_render_callback 0 1 600 67
/**
 * Returns the CallbackObject set by set_render_callback().
 */
79
inline CallbackObject *CallbackGraphicsWindow::get_render_callback(void) const;

1268 19 create_input_device 0 4 1404 43 CallbackGraphicsWindow::create_input_device 0 1 601 121
/**
 * Adds a new input device (mouse) to the window with the indicated name.
 * Returns the index of the new device.
 */
73
int CallbackGraphicsWindow::create_input_device(std::string const &name);

1269 14 get_class_type 0 4 1404 38 CallbackGraphicsWindow::get_class_type 0 1 602 0
63
static TypeHandle CallbackGraphicsWindow::get_class_type(void);

1270 9 get_width 0 20 1411 22 DisplayMode::get_width 0 0 34
getter for int DisplayMode::width;
39
int DisplayMode::get_width(void) const;

1271 9 set_width 0 36 1411 22 DisplayMode::set_width 0 0 34
setter for int DisplayMode::width;
39
void DisplayMode::set_width(int value);

1272 10 get_height 0 20 1411 23 DisplayMode::get_height 0 0 35
getter for int DisplayMode::height;
40
int DisplayMode::get_height(void) const;

1273 10 set_height 0 36 1411 23 DisplayMode::set_height 0 0 35
setter for int DisplayMode::height;
40
void DisplayMode::set_height(int value);

1274 18 get_bits_per_pixel 0 20 1411 31 DisplayMode::get_bits_per_pixel 0 0 43
getter for int DisplayMode::bits_per_pixel;
48
int DisplayMode::get_bits_per_pixel(void) const;

1275 18 set_bits_per_pixel 0 36 1411 31 DisplayMode::set_bits_per_pixel 0 0 43
setter for int DisplayMode::bits_per_pixel;
48
void DisplayMode::set_bits_per_pixel(int value);

1276 16 get_refresh_rate 0 20 1411 29 DisplayMode::get_refresh_rate 0 0 41
getter for int DisplayMode::refresh_rate;
46
int DisplayMode::get_refresh_rate(void) const;

1277 16 set_refresh_rate 0 36 1411 29 DisplayMode::set_refresh_rate 0 0 41
setter for int DisplayMode::refresh_rate;
46
void DisplayMode::set_refresh_rate(int value);

1278 19 get_fullscreen_only 0 20 1411 32 DisplayMode::get_fullscreen_only 0 0 44
getter for int DisplayMode::fullscreen_only;
49
int DisplayMode::get_fullscreen_only(void) const;

1279 19 set_fullscreen_only 0 36 1411 32 DisplayMode::set_fullscreen_only 0 0 44
setter for int DisplayMode::fullscreen_only;
49
void DisplayMode::set_fullscreen_only(int value);

1280 11 operator == 0 4 1411 24 DisplayMode::operator == 0 1 615 0
62
bool DisplayMode::operator ==(DisplayMode const &other) const;

1281 11 operator != 0 4 1411 24 DisplayMode::operator != 0 1 616 0
62
bool DisplayMode::operator !=(DisplayMode const &other) const;

1282 6 output 0 4 1411 19 DisplayMode::output 0 1 617 10
/**
 *
 */
50
void DisplayMode::output(std::ostream &out) const;

1283 11 DisplayMode 0 260 1411 24 DisplayMode::DisplayMode 0 2 613 614 0
112
inline DisplayMode::DisplayMode(void) = default;
inline DisplayMode::DisplayMode(DisplayMode const &) = default;

1284 12 ~DisplayMode 0 516 1411 25 DisplayMode::~DisplayMode 0 0 0
32
DisplayMode::~DisplayMode(void);

1285 16 get_cull_handler 0 4 1412 47 DisplayRegionCullCallbackData::get_cull_handler 0 1 618 114
/**
 * Returns a pointer to the CullHandler, which accepts each object to be added
 * to the list for drawing.
 */
80
inline CullHandler *DisplayRegionCullCallbackData::get_cull_handler(void) const;

1286 15 get_scene_setup 0 4 1412 46 DisplayRegionCullCallbackData::get_scene_setup 0 1 619 111
/**
 * Returns a pointer to the SceneSetup object, which contains information
 * about the camera and such.
 */
78
inline SceneSetup *DisplayRegionCullCallbackData::get_scene_setup(void) const;

1287 14 get_class_type 0 4 1412 45 DisplayRegionCullCallbackData::get_class_type 0 1 620 0
70
static TypeHandle DisplayRegionCullCallbackData::get_class_type(void);

1288 30 ~DisplayRegionCullCallbackData 0 516 1412 61 DisplayRegionCullCallbackData::~DisplayRegionCullCallbackData 0 0 0
68
DisplayRegionCullCallbackData::~DisplayRegionCullCallbackData(void);

1289 15 get_cull_result 0 4 1413 46 DisplayRegionDrawCallbackData::get_cull_result 0 1 621 122
/**
 * Returns a pointer to the CullResult, the list of CullableObjects that
 * should be drawn in this DisplayRegion.
 */
78
inline CullResult *DisplayRegionDrawCallbackData::get_cull_result(void) const;

1290 15 get_scene_setup 0 4 1413 46 DisplayRegionDrawCallbackData::get_scene_setup 0 1 622 111
/**
 * Returns a pointer to the SceneSetup object, which contains information
 * about the camera and such.
 */
78
inline SceneSetup *DisplayRegionDrawCallbackData::get_scene_setup(void) const;

1291 14 get_class_type 0 4 1413 45 DisplayRegionDrawCallbackData::get_class_type 0 1 623 0
70
static TypeHandle DisplayRegionDrawCallbackData::get_class_type(void);

1292 30 ~DisplayRegionDrawCallbackData 0 516 1413 61 DisplayRegionDrawCallbackData::~DisplayRegionDrawCallbackData 0 0 0
68
DisplayRegionDrawCallbackData::~DisplayRegionDrawCallbackData(void);

1293 23 DisplaySearchParameters 0 260 1414 48 DisplaySearchParameters::DisplaySearchParameters 0 2 624 625 10
/**
 *
 */
155
DisplaySearchParameters::DisplaySearchParameters(void);
inline DisplaySearchParameters::DisplaySearchParameters(DisplaySearchParameters const &) = default;

1294 24 ~DisplaySearchParameters 0 516 1414 49 DisplaySearchParameters::~DisplaySearchParameters 0 0 10
/**
 *
 */
56
DisplaySearchParameters::~DisplaySearchParameters(void);

1295 17 set_minimum_width 0 4 1414 42 DisplaySearchParameters::set_minimum_width 0 1 626 10
/**
 *
 */
67
void DisplaySearchParameters::set_minimum_width(int minimum_width);

1296 17 set_maximum_width 0 4 1414 42 DisplaySearchParameters::set_maximum_width 0 1 627 10
/**
 *
 */
67
void DisplaySearchParameters::set_maximum_width(int maximum_width);

1297 18 set_minimum_height 0 4 1414 43 DisplaySearchParameters::set_minimum_height 0 1 628 10
/**
 *
 */
69
void DisplaySearchParameters::set_minimum_height(int minimum_height);

1298 18 set_maximum_height 0 4 1414 43 DisplaySearchParameters::set_maximum_height 0 1 629 10
/**
 *
 */
69
void DisplaySearchParameters::set_maximum_height(int maximum_height);

1299 26 set_minimum_bits_per_pixel 0 4 1414 51 DisplaySearchParameters::set_minimum_bits_per_pixel 0 1 630 10
/**
 *
 */
85
void DisplaySearchParameters::set_minimum_bits_per_pixel(int minimum_bits_per_pixel);

1300 26 set_maximum_bits_per_pixel 0 4 1414 51 DisplaySearchParameters::set_maximum_bits_per_pixel 0 1 631 10
/**
 *
 */
85
void DisplaySearchParameters::set_maximum_bits_per_pixel(int maximum_bits_per_pixel);

1301 8 set_size 0 6 1415 24 GraphicsBuffer::set_size 0 1 632 269
/**
 * This is called by the GraphicsEngine to request that the buffer resize
 * itself.  Although calls to get the size will return the new value, much of
 * the actual resizing work doesn't take place until the next begin_frame.
 * Not all buffers are resizeable.
 */
52
virtual void GraphicsBuffer::set_size(int x, int y);

1302 14 get_class_type 0 4 1415 30 GraphicsBuffer::get_class_type 0 1 633 0
55
static TypeHandle GraphicsBuffer::get_class_type(void);

1303 18 get_num_pipe_types 0 4 1416 41 GraphicsPipeSelection::get_num_pipe_types 0 1 634 121
/**
 * Returns the number of different types of GraphicsPipes that are available
 * to create through this interface.
 */
58
int GraphicsPipeSelection::get_num_pipe_types(void) const;

1304 13 get_pipe_type 0 4 1416 36 GraphicsPipeSelection::get_pipe_type 0 1 635 81
/**
 * Returns the nth type of GraphicsPipe available through this interface.
 */
61
TypeHandle GraphicsPipeSelection::get_pipe_type(int n) const;

1305 16 print_pipe_types 0 4 1416 39 GraphicsPipeSelection::print_pipe_types 0 1 636 106
/**
 * Writes a list of the currently known GraphicsPipe types to nout, for the
 * user's information.
 */
57
void GraphicsPipeSelection::print_pipe_types(void) const;

1306 9 make_pipe 0 4 1416 32 GraphicsPipeSelection::make_pipe 0 2 637 638 532
/**
 * Creates a new GraphicsPipe of the indicated type (or a type more specific
 * than the indicated type, if necessary) and returns it.  Returns NULL if the
 * type cannot be matched.
 *
 * If the type is not already defined, this will implicitly load the named
 * module, or if module_name is empty, it will call load_aux_modules().
 */

/**
 * Creates a new GraphicsPipe of the indicated type (or a type more specific
 * than the indicated type, if necessary) and returns it.  Returns NULL if the
 * type cannot be matched.
 */
209
PointerTo< GraphicsPipe > GraphicsPipeSelection::make_pipe(std::string const &type_name, std::string const &module_name = string());
PointerTo< GraphicsPipe > GraphicsPipeSelection::make_pipe(TypeHandle type);

1307 16 make_module_pipe 0 4 1416 39 GraphicsPipeSelection::make_module_pipe 0 1 639 173
/**
 * Returns a new GraphicsPipe of a type defined by the indicated module.
 * Returns NULL if the module is not found or does not properly recommend a
 * GraphicsPipe.
 */
98
PointerTo< GraphicsPipe > GraphicsPipeSelection::make_module_pipe(std::string const &module_name);

1308 17 make_default_pipe 0 4 1416 40 GraphicsPipeSelection::make_default_pipe 0 1 640 170
/**
 * Creates a new GraphicsPipe of some arbitrary type.  The user may specify a
 * preference using the Configrc file; otherwise, one will be chosen
 * arbitrarily.
 */
73
PointerTo< GraphicsPipe > GraphicsPipeSelection::make_default_pipe(void);

1309 19 get_num_aux_modules 0 4 1416 42 GraphicsPipeSelection::get_num_aux_modules 0 1 641 196
/**
 * Returns the number of display modules that are still to be loaded.  If this
 * is nonzero, then calling load_aux_modules() will likely increase the number
 * of GraphicsPipes available.
 */
66
inline int GraphicsPipeSelection::get_num_aux_modules(void) const;

1310 16 load_aux_modules 0 4 1416 39 GraphicsPipeSelection::load_aux_modules 0 1 642 132
/**
 * Loads all the modules named in the aux-display Configrc variable, making as
 * many graphics pipes as possible available.
 */
51
void GraphicsPipeSelection::load_aux_modules(void);

1311 14 get_global_ptr 0 4 1416 37 GraphicsPipeSelection::get_global_ptr 0 1 643 76
/**
 * Returns a pointer to the one global GraphicsPipeSelection object.
 */
81
static inline GraphicsPipeSelection *GraphicsPipeSelection::get_global_ptr(void);

1312 16 MouseAndKeyboard 0 260 1418 34 MouseAndKeyboard::MouseAndKeyboard 0 2 644 645 10
/**
 *
 */
184
explicit MouseAndKeyboard::MouseAndKeyboard(GraphicsWindow *window, int device, std::string const &name);
inline MouseAndKeyboard::MouseAndKeyboard(MouseAndKeyboard const &) = default;

1313 10 set_source 0 4 1418 28 MouseAndKeyboard::set_source 0 1 646 134
/**
 * Redirects the class to get the data from the mouse and keyboard associated
 * with a different window and/or device number.
 */
70
void MouseAndKeyboard::set_source(GraphicsWindow *window, int device);

1314 14 get_class_type 0 4 1418 32 MouseAndKeyboard::get_class_type 0 1 647 0
57
static TypeHandle MouseAndKeyboard::get_class_type(void);

1315 17 ~MouseAndKeyboard 0 516 1418 35 MouseAndKeyboard::~MouseAndKeyboard 0 0 0
42
MouseAndKeyboard::~MouseAndKeyboard(void);

1316 8 make_int 0 4 1420 28 NativeWindowHandle::make_int 0 1 648 306
/**
 * Constructs a new WindowHandle with an int value, which is understood to be
 * either an HWND or a Window, cast to int.  This method exists for the
 * convenience of Python, which likes to deal with ints; C++ code should use
 * one of the more specific make_x11() or make_win32() methods instead.
 */
82
static PointerTo< WindowHandle > NativeWindowHandle::make_int(std::size_t window);

1317 15 make_subprocess 0 4 1420 35 NativeWindowHandle::make_subprocess 0 1 649 314
/**
 * Constructs a new WindowHandle that references a SubprocessWindowBuffer read
 * in another process, with the named pipe filename that it uses for
 * communication.
 *
 * This is (at present, and maybe always) useful only on the OS X platform,
 * where parenting child windows is particularly problematic.
 */
95
static PointerTo< WindowHandle > NativeWindowHandle::make_subprocess(Filename const &filename);

1318 14 get_class_type 0 4 1420 34 NativeWindowHandle::get_class_type 0 1 650 0
59
static TypeHandle NativeWindowHandle::get_class_type(void);

1319 19 ~NativeWindowHandle 0 516 1420 39 NativeWindowHandle::~NativeWindowHandle 0 0 0
46
NativeWindowHandle::~NativeWindowHandle(void);

1320 8 set_size 0 4 1421 24 ParasiteBuffer::set_size 0 1 651 269
/**
 * This is called by the GraphicsEngine to request that the buffer resize
 * itself.  Although calls to get the size will return the new value, much of
 * the actual resizing work doesn't take place until the next begin_frame.
 * Not all buffers are resizeable.
 */
44
void ParasiteBuffer::set_size(int x, int y);

1321 14 get_class_type 0 4 1421 30 ParasiteBuffer::get_class_type 0 1 652 0
55
static TypeHandle ParasiteBuffer::get_class_type(void);

652
1 0 0 7 3 1368 0 0 72 /**
 * Returns the GraphicsPipe that this device is associated with.
 */ 1 4 this 3 1422  
2 0 0 7 4 1424 0 0 0 0 
3 0 0 6 8 1350 0 0 379 /**
 * Returns false if this pipe is known to be invalid, meaning that an attempt
 * to create a GraphicsWindow with the pipe will certainly fail.  Returns true
 * if the pipe is probably valid (is this case, an attempt to create a
 * GraphicsWindow should succeed, but might still fail).
 *
 * Use the GraphicsEngine class to create a GraphicsWindow on a particular
 * pipe.
 */ 1 4 this 3 1425  
4 0 0 6 9 1327 0 0 373 /**
 * Returns the mask of bits that represents the kinds of GraphicsOutput
 * objects this pipe might be able to successfully create.  The return value
 * is the union of bits in GraphicsPipe::OutputTypes that represents the set
 * of GraphicsOutput types.
 *
 * A 1 bit in a particular position is not a guarantee of success, but a 0 bit
 * is a guarantee of failure.
 */ 1 4 this 3 1425  
5 0 0 6 10 1350 0 0 432 /**
 * A convenience function to ask if a particular type or types of
 * GraphicsObjects are supported.  The parameter is a union of one or more
 * bits defined in GrpahicsPipe::OutputTypes.
 *
 * Returns true if all of the requested types are listed in the
 * supported_types mask, false if any one of them is not.  This is not a
 * guarantee that the indicated output type will successfully be created when
 * it is attempted.
 */ 2 4 this 3 1425  5 flags 1 1327  
6 0 0 6 11 1327 0 0 268 /**
 * Returns the width of the entire display, if it is known.  This may return
 * 0.  This is not a guarantee that windows (particularly fullscreen windows)
 * may not be created larger than this width, but it is intended to provide a
 * hint to the application.
 */ 1 4 this 3 1425  
7 0 0 6 12 1327 0 0 133 /**
 * Returns the height of the entire display, if it is known.  This may return
 * 0.  See the caveats for get_display_width().
 */ 1 4 this 3 1425  
8 0 0 6 13 1328 0 0 429 /**
 * Returns the display zoom factor configured in the operating system.  If the
 * operating system automatically scales windows to match the DPI (such as when
 * dpi-aware is set to false), this will be 1.0.  Otherwise, this will be set to
 * a value approximating the density of the monitor divided by the standard
 * density of the operating system (usually 96), yielding a value like 1.5 or
 * 2.0.
 *
 * @since 1.10.8
 */ 1 4 this 3 1425  
9 0 0 6 20 1330 0 0 46 /**
 * Gets the pipe's DisplayInformation.
 */ 1 4 this 3 1368  
10 0 0 4 80 1427 0 0 148 /**
 * Looks up the detailed CPU information and stores it in
 * _display_information, if supported by the OS. This may take a second or
 * two.
 */ 1 4 this 3 1368  
11 0 0 6 81 1428 0 0 0 1 4 this 3 1425  
12 0 0 7 85 1424 0 0 0 0 
13 0 0 7 26 1330 662 0 10 /**
 *
 */ 0 
14 0 0 15 26 1330 662 0 0 1 6 param0 0 1429  
15 0 0 6 27 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
16 0 0 6 28 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
17 0 0 6 29 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
18 0 0 6 30 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
19 0 0 6 31 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
20 0 0 6 32 1431 0 0 10 /**
 *
 */ 2 4 this 3 1330  13 display_index 1 1327  
21 0 0 6 34 1327 0 0 174 /**
 * Returns the index of the current display mode (determined at the time of
 * application start) in the display mode array, or -1 if this could not be
 * determined.
 */ 1 4 this 3 1429  
22 0 0 6 35 1327 0 0 37 // Older interface for display modes. 2 4 this 3 1330  13 display_index 1 1327  
23 0 0 6 36 1327 0 0 10 /**
 *
 */ 2 4 this 3 1330  13 display_index 1 1327  
24 0 0 6 37 1327 0 0 10 /**
 *
 */ 2 4 this 3 1330  13 display_index 1 1327  
25 0 0 6 38 1327 0 0 10 /**
 *
 */ 2 4 this 3 1330  13 display_index 1 1327  
26 0 0 6 39 1327 0 0 10 /**
 *
 */ 2 4 this 3 1330  13 display_index 1 1327  
27 0 0 6 40 1363 0 0 10 /**
 *
 */ 1 4 this 3 1330  
28 0 0 6 41 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
29 0 0 6 42 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
30 0 0 4 43 1427 0 0 10 /**
 *
 */ 1 4 this 3 1330  
31 0 0 6 44 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
32 0 0 6 45 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
33 0 0 6 46 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
34 0 0 6 47 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
35 0 0 6 48 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
36 0 0 6 49 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
37 0 0 6 50 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
38 0 0 6 51 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
39 0 0 6 52 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
40 0 0 6 53 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
41 0 0 6 54 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
42 0 0 6 55 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
43 0 0 6 56 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
44 0 0 6 57 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
45 0 0 6 58 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
46 0 0 6 59 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
47 0 0 6 60 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
48 0 0 6 61 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
49 0 0 6 62 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
50 0 0 6 63 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
51 0 0 6 64 1327 0 0 10 /**
 *
 */ 1 4 this 3 1330  
52 0 0 6 65 1428 0 0 10 /**
 *
 */ 1 4 this 3 1429  
53 0 0 6 66 1428 0 0 10 /**
 *
 */ 1 4 this 3 1429  
54 0 0 6 67 1341 0 0 10 /**
 *
 */ 1 4 this 3 1330  
55 0 0 6 68 1341 0 0 10 /**
 *
 */ 1 4 this 3 1330  
56 0 0 6 69 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
57 0 0 6 70 1433 0 0 57 /**
 * Equivalent to the rdtsc processor instruction.
 */ 0 
58 0 0 6 71 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
59 0 0 6 72 1433 0 0 10 /**
 *
 */ 1 4 this 3 1330  
60 0 0 4 73 1427 0 0 10 /**
 *
 */ 2 4 this 3 1330  16 processor_number 1 1327  
61 0 0 6 74 1327 0 0 148 /**
 * Returns the number of individual CPU cores in the system, or 0 if this
 * number is not available.  A hyperthreaded CPU counts once here.
 */ 1 4 this 3 1330  
62 0 0 6 75 1327 0 0 151 /**
 * Returns the number of logical CPU's in the system, or 0 if this number is
 * not available.  A hyperthreaded CPU counts as two or more here.
 */ 1 4 this 3 1330  
63 0 0 6 76 1327 0 0 33 /**
 * Returns -1 if not set.
 */ 1 4 this 3 1330  
64 0 0 6 77 1327 0 0 33 /**
 * Returns -1 if not set.
 */ 1 4 this 3 1330  
65 0 0 6 78 1327 0 0 33 /**
 * Returns -1 if not set.
 */ 1 4 this 3 1330  
66 0 0 6 79 1327 0 0 33 /**
 * Returns -1 if not set.
 */ 1 4 this 3 1330  
67 0 0 4 89 1427 0 0 234 /**
 * Toggles the flag that indicates whether the color buffer should be cleared
 * every frame.  If this is true, the color buffer will be cleared to the
 * color indicated by set_clear_color(); otherwise, it will be left alone.
 */ 2 4 this 3 1435  18 clear_color_active 1 1350  
68 0 0 6 90 1350 0 0 155 /**
 * Returns the current setting of the flag that indicates whether the color
 * buffer should be cleared every frame.  See set_clear_color_active().
 */ 1 4 this 3 1436  
69 0 0 4 91 1427 0 0 243 /**
 * Toggles the flag that indicates whether the depth buffer should be cleared
 * every frame.  If this is true, the depth buffer will be cleared to the
 * depth value indicated by set_clear_depth(); otherwise, it will be left
 * alone.
 */ 2 4 this 3 1435  18 clear_depth_active 1 1350  
70 0 0 6 92 1350 0 0 155 /**
 * Returns the current setting of the flag that indicates whether the depth
 * buffer should be cleared every frame.  See set_clear_depth_active().
 */ 1 4 this 3 1436  
71 0 0 4 93 1427 0 0 243 /**
 * Toggles the flag that indicates whether the stencil buffer should be
 * cleared every frame.  If this is true, the stencil buffer will be cleared
 * to the value indicated by set_clear_stencil(); otherwise, it will be left
 * alone.
 */ 2 4 this 3 1435  20 clear_stencil_active 1 1350  
72 0 0 6 94 1350 0 0 157 /**
 * Returns the current setting of the flag that indicates whether the color
 * buffer should be cleared every frame.  See set_clear_stencil_active().
 */ 1 4 this 3 1436  
73 0 0 4 95 1427 0 0 256 /**
 * Sets the clear color to the indicated value.  This is the value that will
 * be used to clear the color buffer every frame, but only if
 * get_clear_color_active() returns true.  If get_clear_color_active() returns
 * false, this is meaningless.
 */ 2 4 this 3 1435  5 color 1 1438  
74 0 0 6 96 1438 0 0 250 /**
 * Returns the current clear color value.  This is the value that will be used
 * to clear the color buffer every frame, but only if get_clear_color_active()
 * returns true.  If get_clear_color_active() returns false, this is
 * meaningless.
 */ 1 4 this 3 1436  
75 0 0 4 102 1427 0 0 256 /**
 * Sets the clear depth to the indicated value.  This is the value that will
 * be used to clear the depth buffer every frame, but only if
 * get_clear_depth_active() returns true.  If get_clear_depth_active() returns
 * false, this is meaningless.
 */ 2 4 this 3 1435  5 depth 1 1328  
76 0 0 6 103 1328 0 0 250 /**
 * Returns the current clear depth value.  This is the value that will be used
 * to clear the depth buffer every frame, but only if get_clear_depth_active()
 * returns true.  If get_clear_depth_active() returns false, this is
 * meaningless.
 */ 1 4 this 3 1436  
77 0 0 4 105 1427 0 0 0 2 4 this 3 1435  7 stencil 1 1341  
78 0 0 6 106 1341 0 0 258 /**
 * Returns the current clear stencil value.  This is the value that will be
 * used to clear the stencil buffer every frame, but only if
 * get_clear_stencil_active() returns true.  If get_clear_stencil_active()
 * returns false, this is meaningless.
 */ 1 4 this 3 1436  
79 0 0 4 109 1427 0 0 55 /**
 * Sets the clear-active flag for any bitplane.
 */ 3 4 this 3 1435  1 n 1 1327  16 clear_aux_active 1 1350  
80 0 0 6 110 1350 0 0 55 /**
 * Gets the clear-active flag for any bitplane.
 */ 2 4 this 3 1436  1 n 1 1327  
81 0 0 4 111 1427 0 0 49 /**
 * Sets the clear value for any bitplane.
 */ 3 4 this 3 1435  1 n 1 1327  11 clear_value 1 1438  
82 0 0 6 112 1438 0 0 52 /**
 * Returns the clear value for any bitplane.
 */ 2 4 this 3 1436  1 n 1 1327  
83 0 0 4 113 1427 0 0 110 /**
 * Disables both the color and depth clear.  See set_clear_color_active and
 * set_clear_depth_active.
 */ 1 4 this 3 1435  
84 0 0 6 114 1350 0 0 183 /**
 * Returns true if any of the clear types (so far there are just color or
 * depth) have been set active, or false if none of them are active and there
 * is no need to clear.
 */ 1 4 this 3 1436  
85 0 0 4 115 1427 0 0 975 /**
 * Sets the amount by which the pixels of the region are scaled internally
 * when filling the image interally.  Setting this number larger makes the
 * pixels blockier, but may make the rendering faster, particularly for
 * software renderers.  Setting this number to 2.0 reduces the number of
 * pixels that have to be filled by the renderer by a factor of 2.0.  It
 * doesn't make sense to set this lower than 1.0.
 *
 * It is possible to set this on either individual DisplayRegions or on
 * overall GraphicsWindows, but you will get better performance for setting it
 * on the window rather than its individual DisplayRegions.  Also, you may not
 * set it on a DisplayRegion that doesn't have both clear_color() and
 * clear_depth() enabled.
 *
 * This property is only supported on renderers for which it is particularly
 * useful--currently, this is the tinydisplay software renderer.  Other kinds
 * of renderers allow you to set this property, but ignore it.
 */ 2 4 this 3 1435  10 pixel_zoom 1 1328  
86 0 0 6 116 1328 0 0 138 /**
 * Returns the value set by set_pixel_zoom(), regardless of whether it is
 * being respected or not.  Also see get_pixel_factor().
 */ 1 4 this 3 1436  
87 0 0 6 117 1328 0 0 340 /**
 * Returns the amount by which the height and width of the region will be
 * scaled internally, based on the zoom factor set by set_pixel_zoom().  This
 * will return 1.0 if the pixel_zoom was not set or if it is not being
 * respected (for instance, because the underlying renderer doesn't support it
 * --see supports_pixel_zoom).
 */ 1 4 this 3 1436  
88 0 0 6 118 1350 0 0 451 /**
 * Returns true if a call to set_pixel_zoom() will be respected, false if it
 * will be ignored.  If this returns false, then get_pixel_factor() will
 * always return 1.0, regardless of what value you specify for
 * set_pixel_zoom().
 *
 * This may return false if the underlying renderer doesn't support pixel
 * zooming, or if you have called this on a DisplayRegion that doesn't have
 * both set_clear_color() and set_clear_depth() enabled.
 */ 1 4 this 3 1436  
89 0 0 6 121 1327 0 0 83 /**
 * Returns the RenderBuffer::Type that corresponds to a RenderTexturePlane.
 */ 1 5 plane 1 1327  
90 0 0 15 123 1352 0 0 10 /**
 *
 */ 1 4 copy 1 1439  
91 0 0 23 123 1352 0 0 10 /**
 *
 */ 1 9 os_handle 1 1343  
92 0 0 7 124 1343 0 0 91 /**
 * Returns the OS-specific handle stored internally to the WindowHandle
 * wrapper.
 */ 1 4 this 3 1439  
93 0 0 4 125 1427 0 0 91 /**
 * Changes the OS-specific handle stored internally to the WindowHandle
 * wrapper.
 */ 2 4 this 3 1352  9 os_handle 1 1343  
94 0 0 4 133 1427 0 0 285 /**
 * Call this method on a parent WindowHandle to deliver a Windows message to
 * the current child window, if any.  This is used in the web plugin system to
 * deliver button events detected directly by the browser system into Panda,
 * which is particularly necessary on Vista.
 */ 4 4 this 3 1352  3 msg 1 1341  6 wparam 1 1327  6 lparam 1 1327  
95 0 0 6 134 1441 0 0 149 /**
 * Returns the OS-specific handle converted to an integer, if this is possible
 * for the particular representation.  Returns 0 if it is not.
 */ 1 4 this 3 1439  
96 0 0 4 135 1427 0 0 10 /**
 *
 */ 2 4 this 3 1439  3 out 1 1442  
97 0 0 7 136 1424 0 0 0 0 
98 0 0 15 132 1343 0 0 0 1 6 param0 0 1444  
99 0 0 6 129 1441 0 0 149 /**
 * Returns the OS-specific handle converted to an integer, if this is possible
 * for the particular representation.  Returns 0 if it is not.
 */ 1 4 this 3 1444  
100 0 0 4 130 1427 0 0 10 /**
 *
 */ 2 4 this 3 1444  3 out 1 1442  
101 0 0 7 131 1424 0 0 0 0 
102 0 0 39 140 1449 756 0 0 2 4 args 1 1446  4 kwds 1 1446  
103 0 0 6 141 1449 0 0 0 2 4 this 3 1449  4 copy 1 1450  
104 0 0 7 143 1449 756 0 128 /**
 * Returns a WindowProperties structure with all of the default values filled
 * in according to the user's config file.
 */ 0 
105 0 0 7 144 1449 756 0 186 /**
 * Returns the "default" WindowProperties.  If set_default() has been called,
 * this returns that WindowProperties structure; otherwise, this returns
 * get_config_properties().
 */ 0 
106 0 0 4 145 1427 0 0 290 /**
 * Replaces the "default" WindowProperties with the specified structure.  The
 * specified WindowProperties will be returned by future calls to
 * get_default(), until clear_default() is called.
 *
 * Note that this completely replaces the default properties; it is not
 * additive.
 */ 1 18 default_properties 1 1450  
107 0 0 4 146 1427 0 0 104 /**
 * Returns the "default" WindowProperties to whatever is specified in the
 * user's config file.
 */ 0 
108 0 0 7 149 1449 756 0 216 /**
 * Returns a WindowProperties structure with only the size specified.  The
 * size is the only property that matters to buffers.
 *
 * @deprecated in the Python API, use WindowProperties(size=(x, y)) instead.
 */ 1 4 size 1 1452  
109 0 0 7 149 1449 756 0 0 2 6 x_size 1 1327  6 y_size 1 1327  
110 0 0 6 150 1350 0 0 0 2 4 this 3 1450  5 other 1 1450  
111 0 0 6 151 1350 0 0 0 2 4 this 3 1450  5 other 1 1450  
112 0 0 4 152 1427 0 0 138 /**
 * Unsets all properties that have been specified so far, and resets the
 * WindowProperties structure to its initial empty state.
 */ 1 4 this 3 1449  
113 0 0 6 153 1350 0 0 79 /**
 * Returns true if any properties have been specified, false otherwise.
 */ 1 4 this 3 1450  
114 0 0 4 154 1427 0 0 236 /**
 * Specifies the origin on the screen (in pixels, relative to the top-left
 * corner) at which the window should appear.  This is the origin of the top-
 * left corner of the useful part of the window, not including decorations.
 */ 2 4 this 3 1449  6 origin 1 1453  
115 0 0 4 154 1427 0 0 236 /**
 * Specifies the origin on the screen (in pixels, relative to the top-left
 * corner) at which the window should appear.  This is the origin of the top-
 * left corner of the useful part of the window, not including decorations.
 */ 3 4 this 3 1449  8 x_origin 1 1327  8 y_origin 1 1327  
116 0 0 7 155 1455 0 0 97 /**
 * Returns the coordinates of the window's top-left corner, not including
 * decorations.
 */ 1 4 this 3 1450  
117 0 0 6 156 1327 0 0 98 /**
 * Returns the x coordinate of the window's top-left corner, not including
 * decorations.
 */ 1 4 this 3 1450  
118 0 0 6 157 1327 0 0 98 /**
 * Returns the y coordinate of the window's top-left corner, not including
 * decorations.
 */ 1 4 this 3 1450  
119 0 0 6 158 1350 0 0 81 /**
 * Returns true if the window origin has been specified, false otherwise.
 */ 1 4 this 3 1450  
120 0 0 4 159 1427 0 0 64 /**
 * Removes the origin specification from the properties.
 */ 1 4 this 3 1449  
121 0 0 4 162 1427 0 0 147 /**
 * Specifies the requested size of the window, in pixels.  This is the size of
 * the useful part of the window, not including decorations.
 */ 2 4 this 3 1449  4 size 1 1456  
122 0 0 4 162 1427 0 0 147 /**
 * Specifies the requested size of the window, in pixels.  This is the size of
 * the useful part of the window, not including decorations.
 */ 3 4 this 3 1449  6 x_size 1 1327  6 y_size 1 1327  
123 0 0 7 163 1458 0 0 97 /**
 * Returns size in pixels of the useful part of the window, not including
 * decorations.
 */ 1 4 this 3 1450  
124 0 0 6 164 1327 0 0 154 /**
 * Returns size in pixels in the x dimension of the useful part of the window,
 * not including decorations.  That is, this is the window's width.
 */ 1 4 this 3 1450  
125 0 0 6 165 1327 0 0 155 /**
 * Returns size in pixels in the y dimension of the useful part of the window,
 * not including decorations.  That is, this is the window's height.
 */ 1 4 this 3 1450  
126 0 0 6 166 1350 0 0 79 /**
 * Returns true if the window size has been specified, false otherwise.
 */ 1 4 this 3 1450  
127 0 0 4 167 1427 0 0 62 /**
 * Removes the size specification from the properties.
 */ 1 4 this 3 1449  
128 0 0 6 170 1350 0 0 10 /**
 *
 */ 1 4 this 3 1450  
129 0 0 4 171 1427 0 0 962 /**
 * Specifies the mode in which the window is to operate its mouse pointer.
 *
 * M_absolute: the normal mode in which a mouse pointer operates, where the
 * mouse can move outside the window and the mouse coordinates are relative to
 * its position in the window.
 *
 * M_relative (OSX or Unix/X11 only): a mode where only relative movements are
 * reported; particularly useful for FPS-style mouse movements where you have
 * hidden the mouse pointer and are are more interested in how fast the mouse
 * is moving, rather than precisely where the pointer is hovering.
 *
 * This has no effect on Windows.  On Unix/X11, this requires the Xxf86dga
 * extension to be available.
 *
 * M_confined: this mode reports absolute mouse positions, but confines the
 * mouse pointer to the window boundary.  It can portably replace M_relative
 * for an FPS, but you need to periodically move the pointer to the center of
 * the window and track movement deltas.
 *
 */ 2 4 this 3 1449  4 mode 1 1347  
130 0 0 6 172 1347 0 0 32 /**
 * See set_mouse_mode().
 */ 1 4 this 3 1450  
131 0 0 4 173 1427 0 0 68 /**
 * Removes the mouse_mode specification from the properties.
 */ 1 4 this 3 1449  
132 0 0 4 175 1427 0 0 69 /**
 * Specifies the title that should be assigned to the window.
 */ 2 4 this 3 1449  5 title 1 1428  
133 0 0 6 176 1428 0 0 38 /**
 * Returns the window's title.
 */ 1 4 this 3 1450  
134 0 0 6 177 1350 0 0 80 /**
 * Returns true if the window title has been specified, false otherwise.
 */ 1 4 this 3 1450  
135 0 0 4 178 1427 0 0 63 /**
 * Removes the title specification from the properties.
 */ 1 4 this 3 1449  
136 0 0 4 180 1427 0 0 128 /**
 * Specifies whether the window should be created with a visible title and
 * border (false, the default) or not (true).
 */ 2 4 this 3 1449  11 undecorated 1 1350  
137 0 0 6 181 1350 0 0 52 /**
 * Returns true if the window has no border.
 */ 1 4 this 3 1450  
138 0 0 6 182 1350 0 0 64 /**
 * Returns true if set_undecorated() has been specified.
 */ 1 4 this 3 1450  
139 0 0 4 183 1427 0 0 69 /**
 * Removes the undecorated specification from the properties.
 */ 1 4 this 3 1449  
140 0 0 4 186 1427 0 0 72 /**
 * Specifies whether the window should be resizable by the user.
 */ 2 4 this 3 1449  10 fixed_size 1 1350  
141 0 0 6 187 1350 0 0 85 /**
 * Returns true if the window cannot be resized by the user, false otherwise.
 */ 1 4 this 3 1450  
142 0 0 6 188 1350 0 0 63 /**
 * Returns true if set_fixed_size() has been specified.
 */ 1 4 this 3 1450  
143 0 0 4 189 1427 0 0 68 /**
 * Removes the fixed_size specification from the properties.
 */ 1 4 this 3 1449  
144 0 0 4 191 1427 0 0 131 /**
 * Specifies whether the window should be opened in fullscreen mode (true) or
 * normal windowed mode (false, the default).
 */ 2 4 this 3 1449  10 fullscreen 1 1350  
145 0 0 6 192 1350 0 0 60 /**
 * Returns true if the window is in fullscreen mode.
 */ 1 4 this 3 1450  
146 0 0 6 193 1350 0 0 63 /**
 * Returns true if set_fullscreen() has been specified.
 */ 1 4 this 3 1450  
147 0 0 4 194 1427 0 0 68 /**
 * Removes the fullscreen specification from the properties.
 */ 1 4 this 3 1449  
148 0 0 4 196 1427 0 0 120 /**
 * Specifies whether the window should be opened in the foreground (true), or
 * left in the background (false).
 */ 2 4 this 3 1449  10 foreground 1 1350  
149 0 0 6 197 1350 0 0 59 /**
 * Returns true if the window is in the foreground.
 */ 1 4 this 3 1450  
150 0 0 6 198 1350 0 0 63 /**
 * Returns true if set_foreground() has been specified.
 */ 1 4 this 3 1450  
151 0 0 4 199 1427 0 0 68 /**
 * Removes the foreground specification from the properties.
 */ 1 4 this 3 1449  
152 0 0 4 201 1427 0 0 97 /**
 * Specifies whether the window should be created minimized (true), or normal
 * (false).
 */ 2 4 this 3 1449  9 minimized 1 1350  
153 0 0 6 202 1350 0 0 51 /**
 * Returns true if the window is minimized.
 */ 1 4 this 3 1450  
154 0 0 6 203 1350 0 0 62 /**
 * Returns true if set_minimized() has been specified.
 */ 1 4 this 3 1450  
155 0 0 4 204 1427 0 0 67 /**
 * Removes the minimized specification from the properties.
 */ 1 4 this 3 1449  
156 0 0 4 206 1427 0 0 74 /**
 * Specifies whether the window should read the raw mouse devices.
 */ 2 4 this 3 1449  8 raw_mice 1 1350  
157 0 0 6 207 1350 0 0 57 /**
 * Returns true if the window reads the raw mice.
 */ 1 4 this 3 1450  
158 0 0 6 208 1350 0 0 61 /**
 * Returns true if set_raw_mice() has been specified.
 */ 1 4 this 3 1450  
159 0 0 4 209 1427 0 0 66 /**
 * Removes the raw_mice specification from the properties.
 */ 1 4 this 3 1449  
160 0 0 4 210 1427 0 0 174 /**
 * Specifies whether the window should be open.  It is legal to create a
 * GraphicsWindow in the closed state, and later request it to open by
 * changing this flag.
 */ 2 4 this 3 1449  4 open 1 1350  
161 0 0 6 211 1350 0 0 46 /**
 * Returns true if the window is open.
 */ 1 4 this 3 1450  
162 0 0 6 212 1350 0 0 57 /**
 * Returns true if set_open() has been specified.
 */ 1 4 this 3 1450  
163 0 0 4 213 1427 0 0 62 /**
 * Removes the open specification from the properties.
 */ 1 4 this 3 1449  
164 0 0 4 215 1427 0 0 64 /**
 * Specifies whether the mouse cursor should be visible.
 */ 2 4 this 3 1449  13 cursor_hidden 1 1350  
165 0 0 6 216 1350 0 0 57 /**
 * Returns true if the mouse cursor is invisible.
 */ 1 4 this 3 1450  
166 0 0 6 217 1350 0 0 66 /**
 * Returns true if set_cursor_hidden() has been specified.
 */ 1 4 this 3 1450  
167 0 0 4 218 1427 0 0 71 /**
 * Removes the cursor_hidden specification from the properties.
 */ 1 4 this 3 1449  
168 0 0 4 220 1427 0 0 106 /**
 * Specifies the file that contains the icon to associate with the window when
 * it is minimized.
 */ 2 4 this 3 1449  13 icon_filename 1 1459  
169 0 0 7 221 1461 0 0 64 /**
 * Returns the icon filename associated with the window.
 */ 1 4 this 3 1450  
170 0 0 6 222 1350 0 0 66 /**
 * Returns true if set_icon_filename() has been specified.
 */ 1 4 this 3 1450  
171 0 0 4 223 1427 0 0 71 /**
 * Removes the icon_filename specification from the properties.
 */ 1 4 this 3 1449  
172 0 0 4 226 1427 0 0 134 /**
 * Specifies the file that contains the icon to associate with the mouse
 * cursor when it is within the window (and visible).
 */ 2 4 this 3 1449  15 cursor_filename 1 1459  
173 0 0 7 227 1461 0 0 70 /**
 * Returns the icon filename associated with the mouse cursor.
 */ 1 4 this 3 1450  
174 0 0 6 228 1350 0 0 68 /**
 * Returns true if set_cursor_filename() has been specified.
 */ 1 4 this 3 1450  
175 0 0 4 229 1427 0 0 73 /**
 * Removes the cursor_filename specification from the properties.
 */ 1 4 this 3 1449  
176 0 0 4 231 1427 0 0 317 /**
 * Specifies the relative ordering of the window with respect to other
 * windows.  If the z_order is Z_top, the window will always be on top of
 * other windows; if it is Z_bottom, it will always be below other windows.
 * Most windows will want to be Z_normal, which allows the user to control the
 * order.
 */ 2 4 this 3 1449  7 z_order 1 1346  
177 0 0 6 232 1346 0 0 40 /**
 * Returns the window's z_order.
 */ 1 4 this 3 1450  
178 0 0 6 233 1350 0 0 82 /**
 * Returns true if the window z_order has been specified, false otherwise.
 */ 1 4 this 3 1450  
179 0 0 4 234 1427 0 0 65 /**
 * Removes the z_order specification from the properties.
 */ 1 4 this 3 1449  
180 0 0 4 236 1427 0 0 625 /**
 * Specifies the window that this window should be attached to.  If this is
 * NULL or unspecified, the window will be created as a toplevel window on the
 * desktop; if this is non-NULL, the window will be bound as a child window to
 * the indicated parent window.
 *
 * You should use GraphicsPipe::make_window_handle() to create an instance of
 * a WindowHandle object given an appropriate OS-specific window handle
 * representation.  Each OS-specific GraphicsPipe class defines a
 * make_window_handle() method that returns an appropriate WindowHandle object
 * to wrap the particular OS-specific representation.
 */ 2 4 this 3 1449  13 parent_window 5 1352  
181 0 0 4 236 1427 0 0 639 /**
 * Specifies the window that this window should be attached to.
 *
 * This is a deprecated variant on this method, and exists only for backward
 * compatibility.  Future code should use the version of set_parent_window()
 * below that receives a WindowHandle object; that interface is much more
 * robust.
 *
 * In this deprecated variant, the actual value for "parent" is platform-
 * specific.  On Windows, it is the HWND of the parent window, cast to an
 * unsigned integer.  On X11, it is the Window pointer of the parent window,
 * similarly cast.  On OSX, this is the NSWindow pointer, which doesn't appear
 * to work at all.
 */ 2 4 this 3 1449  6 parent 1 1441  
182 0 0 7 237 1352 0 0 102 /**
 * Returns the parent window specification, or NULL if there is no parent
 * window specified.
 */ 1 4 this 3 1450  
183 0 0 6 238 1350 0 0 72 /**
 * Checks the S_parent_window specification from the properties.
 */ 1 4 this 3 1450  
184 0 0 4 239 1427 0 0 73 /**
 * Removes the S_parent_window specification from the properties.
 */ 1 4 this 3 1449  
185 0 0 4 242 1427 0 0 123 /**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */ 2 4 this 3 1449  5 other 1 1450  
186 0 0 4 243 1427 0 0 123 /**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */ 2 4 this 3 1450  3 out 1 1442  
187 0 0 6 250 1327 0 0 62 /**
 * Returns the number of regions, see set_num_regions.
 */ 1 4 this 3 1462  
188 0 0 4 251 1427 0 0 313 /**
 * Sets the number of regions that this DisplayRegion indicates.  Usually,
 * this number is 1 (and it is always at least 1), and only the first is used
 * for rendering.  However, if more than one is provided, you may select which
 * one to render into using a geometry shader (gl_ViewportIndex in GLSL).
 */ 2 4 this 3 1395  1 i 1 1327  
189 0 0 7 252 1464 0 0 143 /**
 * Retrieves the coordinates of the DisplayRegion's rectangle within its
 * GraphicsOutput.  These numbers will be in the range [0..1].
 */ 2 4 this 3 1462  1 i 5 1327  
190 0 0 6 253 1328 0 0 143 /**
 * Retrieves the x coordinate of the left edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */ 2 4 this 3 1462  1 i 5 1327  
191 0 0 6 254 1328 0 0 144 /**
 * Retrieves the x coordinate of the right edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */ 2 4 this 3 1462  1 i 5 1327  
192 0 0 6 255 1328 0 0 145 /**
 * Retrieves the y coordinate of the bottom edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */ 2 4 this 3 1462  1 i 5 1327  
193 0 0 6 256 1328 0 0 142 /**
 * Retrieves the y coordinate of the top edge of the rectangle within its
 * GraphicsOutput.  This number will be in the range [0..1].
 */ 2 4 this 3 1462  1 i 5 1327  
194 0 0 4 257 1427 0 0 229 /**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */ 2 4 this 3 1395  10 dimensions 1 1465  
195 0 0 4 257 1427 0 0 229 /**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */ 5 4 this 3 1395  1 l 1 1328  1 r 1 1328  1 b 1 1328  1 t 1 1328  
196 0 0 4 257 1427 0 0 381 /**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 3 4 this 3 1395  1 i 1 1327  10 dimensions 1 1465  
197 0 0 4 257 1427 0 0 229 /**
 * Changes the portion of the framebuffer this DisplayRegion corresponds to.
 * The parameters range from 0 to 1, where 0,0 is the lower left corner and
 * 1,1 is the upper right; (0, 1, 0, 1) represents the whole screen.
 */ 6 4 this 3 1395  1 i 1 1327  1 l 1 1328  1 r 1 1328  1 b 1 1328  1 t 1 1328  
198 0 0 7 260 1355 856 0 131 /**
 * Returns the GraphicsOutput that this DisplayRegion is ultimately associated
 * with, or NULL if no window is associated.
 */ 1 4 this 3 1462  
199 0 0 7 261 1368 0 0 127 /**
 * Returns the GraphicsPipe that this DisplayRegion is ultimately associated
 * with, or NULL if no pipe is associated.
 */ 1 4 this 3 1462  
200 0 0 6 262 1350 0 0 74 /**
 * Returns true if this is a StereoDisplayRegion, false otherwise.
 */ 1 4 this 3 1462  
201 0 0 4 619 1427 0 0 508 /**
 * Sets the camera that is associated with this DisplayRegion.  There is a
 * one-to-many association between cameras and DisplayRegions; one camera may
 * be shared by multiple DisplayRegions.
 *
 * The camera is actually set via a NodePath, which clarifies which instance
 * of the camera (if there happen to be multiple instances) we should use.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 2 4 this 3 1395  6 camera 1 1467  
202 0 0 7 620 1471 0 0 117 /**
 * Returns the camera associated with this DisplayRegion, or an empty NodePath
 * if no camera is associated.
 */ 2 4 this 3 1462  14 current_thread 5 1469  
203 0 0 4 623 1427 0 0 284 /**
 * Sets the active flag associated with the DisplayRegion.  If the
 * DisplayRegion is marked inactive, nothing is rendered.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 2 4 this 3 1395  6 active 1 1350  
204 0 0 6 624 1350 0 0 69 /**
 * Returns the active flag associated with the DisplayRegion.
 */ 1 4 this 3 1462  
205 0 0 4 626 1427 0 0 325 /**
 * Sets the sort value associated with the DisplayRegion.  Within a window,
 * DisplayRegions will be rendered in order from the lowest sort value to the
 * highest.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 2 4 this 3 1395  4 sort 1 1327  
206 0 0 6 627 1327 0 0 68 /**
 * Returns the sort value associated with the DisplayRegion.
 */ 1 4 this 3 1462  
207 0 0 4 629 1427 0 0 1240 /**
 * Specifies whether the DisplayRegion represents the left or right channel of
 * a stereo pair, or whether it is a normal, monocular image.  This
 * automatically adjusts the lens that is used to render to this DisplayRegion
 * to its left or right eye, according to the lens's stereo properties.
 *
 * When the DisplayRegion is attached to a stereo window (one for which
 * is_stereo() returns true), this also specifies which physical channel the
 * DisplayRegion renders to.
 *
 * Normally you would create at least two DisplayRegions for a stereo window,
 * one for each of the left and right channels.  The two DisplayRegions may
 * share the same camera (and thus the same lens); this parameter is used to
 * control the exact properties of the lens when it is used to render into
 * this DisplayRegion.
 *
 * Also see the StereoDisplayRegion, which automates managing a pair of
 * left/right DisplayRegions.
 *
 * An ordinary DisplayRegion may be set to SC_mono, SC_left, or SC_right.  You
 * may set SC_stereo only on a StereoDisplayRegion.
 *
 * This call also resets tex_view_offset to its default value, which is 0 for
 * the left eye or 1 for the right eye of a stereo display region, or 0 for a
 * mono display region.
 */ 2 4 this 3 1395  14 stereo_channel 1 1388  
208 0 0 6 630 1388 0 0 181 /**
 * Returns whether the DisplayRegion is specified as the left or right channel
 * of a stereo pair, or whether it is a normal, monocular image.  See
 * set_stereo_channel().
 */ 1 4 this 3 1462  
209 0 0 4 634 1427 0 0 423 /**
 * Sets the current texture view offset for this DisplayRegion.  This is
 * normally set to zero.  If nonzero, it is used to select a particular view
 * of any multiview textures that are rendered within this DisplayRegion.
 *
 * For a StereoDisplayRegion, this is normally 0 for the left eye, and 1 for
 * the right eye, to support stereo textures.  This is set automatically when
 * you call set_stereo_channel().
 */ 2 4 this 3 1395  15 tex_view_offset 1 1327  
210 0 0 6 635 1327 0 0 360 /**
 * Returns the current texture view offset for this DisplayRegion.  This is
 * normally set to zero.  If nonzero, it is used to select a particular view
 * of any multiview textures that are rendered within this DisplayRegion.
 *
 * For a StereoDisplayRegion, this is normally 0 for the left eye, and 1 for
 * the right eye, to support stereo textures.
 */ 1 4 this 3 1462  
211 0 0 4 637 1427 0 0 550 /**
 * Sets the incomplete_render flag.  When this is true, the frame will be
 * rendered even if some of the geometry or textures in the scene are not
 * available (e.g.  they have been temporarily paged out).  When this is
 * false, the frame will be held up while this data is reloaded.
 *
 * This flag may also be set on the GraphicsStateGuardian.  It will be
 * considered true for a given DisplayRegion only if it is true on both the
 * GSG and on the DisplayRegion.
 *
 * See GraphicsStateGuardian::set_incomplete_render() for more detail.
 */ 2 4 this 3 1395  17 incomplete_render 1 1350  
212 0 0 6 638 1350 0 0 76 /**
 * Returns the incomplete_render flag.  See set_incomplete_render().
 */ 1 4 this 3 1462  
213 0 0 4 640 1427 0 0 582 /**
 * Specifies an integer priority which is assigned to any asynchronous texture
 * reload requests spawned while processing this DisplayRegion.  This controls
 * which textures are loaded first when multiple textures need to be reloaded
 * at once; it also controls the relative priority between asynchronous
 * texture loads and asynchronous model or animation loads.
 *
 * Specifying a larger number here makes the textures rendered by this
 * DisplayRegion load up first.  This may be particularly useful to do, for
 * instance, for the DisplayRegion that renders the gui.
 */ 2 4 this 3 1395  23 texture_reload_priority 1 1327  
214 0 0 6 641 1327 0 0 129 /**
 * Returns the priority which is assigned to asynchronous texture reload
 * requests.  See set_texture_reload_priority().
 */ 1 4 this 3 1462  
215 0 0 4 643 1427 0 0 331 /**
 * Sets the lens index, allows for multiple lenses to be attached to a camera.
 * This is useful for a variety of setups, such as fish eye rendering.  The
 * default is 0.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 2 4 this 3 1395  5 index 1 1327  
216 0 0 6 644 1327 0 0 219 /**
 * Returns the specific lens of the associated Camera that will be used for
 * rendering this scene.  Most Cameras hold only one lens, but for multiple
 * lenses this method may be used to selected between them.
 */ 1 4 this 3 1462  
217 0 0 4 646 1427 0 0 218 /**
 * Specifies the CullTraverser that will be used to draw the contents of this
 * DisplayRegion.  Normally the default CullTraverser is sufficient, but this
 * may be changed to change the default cull behavior.
 */ 2 4 this 3 1395  4 trav 1 1390  
218 0 0 7 647 1390 0 0 101 /**
 * Returns the CullTraverser that will be used to draw the contents of this
 * DisplayRegion.
 */ 1 4 this 3 1395  
219 0 0 4 651 1427 0 0 57 /**
 * Deprecated; replaced by set_target_tex_page().
 */ 2 4 this 3 1395  14 cube_map_index 1 1327  
220 0 0 4 652 1427 0 0 678 /**
 * This is a special parameter that is only used when rendering the faces of a
 * cube map or multipage and/or multiview texture.
 *
 * This sets up the DisplayRegion to render to the ith page and jth view of
 * its associated texture(s); the value must be consistent with the range of
 * values availble to the texture.  A normal DisplayRegion that is not
 * associated with any particular page should be set to page -1 and view 0.
 *
 * This is particularly useful when rendering cube maps and/or stereo
 * textures.
 *
 * Don't call this in a downstream thread unless you don't mind it blowing
 * away other changes you might have recently made in an upstream thread.
 */ 2 4 this 3 1395  4 page 1 1327  
221 0 0 6 653 1327 0 0 165 /**
 * Returns the target page number associated with this particular
 * DisplayRegion, or -1 if it is not associated with a page.  See
 * set_target_tex_page().
 */ 1 4 this 3 1462  
222 0 0 4 655 1427 0 0 138 /**
 * Sets whether or not scissor testing is enabled for this region.  The
 * default is true, except for the overlay display region.
 */ 2 4 this 3 1395  15 scissor_enabled 1 1350  
223 0 0 6 656 1350 0 0 141 /**
 * Returns whether or not scissor testing is enabled for this region.  The
 * default is true, except for the overlay display region.
 */ 1 4 this 3 1462  
224 0 0 4 658 1427 0 0 1064 /**
 * Sets the CallbackObject that will be notified when the DisplayRegion is
 * visited during the cull traversal.  This callback will be made during the
 * cull thread.
 *
 * The cull traversal is responsible for determining which nodes are visible
 * and within the view frustum, and for accumulating state and transform, and
 * generally building up the list of CullableObjects that are to be eventually
 * passed to the draw traversal for rendering.
 *
 * At the time the cull traversal callback is made, the traversal for this
 * DisplayRegion has not yet started.
 *
 * The callback is passed an instance of a DisplayRegionCullCallbackData,
 * which contains pointers to the current scene information, as well as the
 * current DisplayRegion and GSG.  The callback *replaces* the normal cull
 * behavior, so if your callback does nothing, the scene graph will not be
 * traversed and therefore nothing will be drawn.  If you wish the normal cull
 * traversal to be performed for this DisplayRegion, you must call
 * cbdata->upcall() from your callback.
 */ 2 4 this 3 1395  6 object 1 1392  
225 0 0 4 659 1427 0 0 78 /**
 * Removes the callback set by an earlier call to set_cull_callback().
 */ 1 4 this 3 1395  
226 0 0 7 660 1392 0 0 65 /**
 * Returns the CallbackObject set by set_cull_callback().
 */ 1 4 this 3 1462  
227 0 0 4 664 1427 0 0 1346 /**
 * Sets the CallbackObject that will be notified when the contents of
 * DisplayRegion is drawn during the draw traversal.  This callback will be
 * made during the draw thread.
 *
 * The draw traversal is responsible for actually issuing the commands to the
 * graphics engine to draw primitives.  Its job is to walk through the list of
 * CullableObjects build up by the cull traversal, as quickly as possible,
 * issuing the appropriate commands to draw each one.
 *
 * At the time the draw traversal callback is made, the graphics state is in
 * the initial state, and no projection matrix or modelview matrix is in
 * effect.  begin_scene() has not yet been called, and no objects have yet
 * been drawn.  However, the viewport has already been set to the appropriate
 * part of the window, and the clear commands for this DisplayRegion (if any)
 * have been issued.
 *
 * The callback is passed an instance of a DisplayRegionDrawCallbackData,
 * which contains pointers to the current scene information, as well as the
 * current DisplayRegion and GSG.  The callback *replaces* the normal draw
 * behavior, so if your callback does nothing, nothing in the DisplayRegion
 * will be drawn.  If you wish the draw traversal to continue to draw the
 * contents of this DisplayRegion, you must call cbdata->upcall() from your
 * callback.
 */ 2 4 this 3 1395  6 object 1 1392  
228 0 0 4 665 1427 0 0 78 /**
 * Removes the callback set by an earlier call to set_draw_callback().
 */ 1 4 this 3 1395  
229 0 0 7 666 1392 0 0 65 /**
 * Returns the CallbackObject set by set_draw_callback().
 */ 1 4 this 3 1462  
230 0 0 6 668 1327 0 0 60 /**
 * Returns the width of the DisplayRegion in pixels.
 */ 2 4 this 3 1462  1 i 5 1327  
231 0 0 6 669 1327 0 0 61 /**
 * Returns the height of the DisplayRegion in pixels.
 */ 2 4 this 3 1462  1 i 5 1327  
232 0 0 7 670 1472 0 0 59 /**
 * Returns the size of the DisplayRegion in pixels.
 */ 2 4 this 3 1462  1 i 5 1327  
233 0 0 4 672 1427 0 0 10 /**
 *
 */ 2 4 this 3 1462  3 out 1 1442  
234 0 0 7 673 1461 0 0 372 /**
 * Synthesizes a suitable default filename for passing to save_screenshot().
 *
 * The default filename is generated from the supplied prefix and from the
 * Config variable screenshot-filename, which contains the following strings:
 *
 * %~p - the supplied prefix %~f - the frame count %~e - the value of
 * screenshot-extension All other % strings in strftime().
 */ 1 6 prefix 5 1428  
235 0 0 7 674 1461 0 0 198 /**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The filename is
 * generated by make_screenshot_filename().
 */ 2 4 this 3 1395  6 prefix 5 1428  
236 0 0 6 675 1350 0 0 117 /**
 * Saves a screenshot of the region to the indicated filename.  Returns true
 * on success, false on failure.
 */ 3 4 this 3 1395  8 filename 1 1459  13 image_comment 5 1428  
237 0 0 7 676 1374 0 0 125 /**
 * Captures the most-recently rendered image from the framebuffer and returns
 * it as a Texture, or NULL on failure.
 */ 1 4 this 3 1395  
238 0 0 6 676 1350 0 0 149 /**
 * Captures the most-recently rendered image from the framebuffer into the
 * indicated PNMImage.  Returns true on success, false on failure.
 */ 2 4 this 3 1395  5 image 1 1473  
239 0 0 4 677 1427 0 0 10 /**
 *
 */ 1 4 this 3 1395  
240 0 0 7 678 1476 0 0 697 /**
 * Returns a special scene graph constructed to represent the results of the
 * last frame's cull operation.
 *
 * This will be a hierarchy of nodes, one node for each bin, each of which
 * will in term be a parent of a number of GeomNodes, representing the
 * geometry drawn in each bin.
 *
 * This is useful mainly for high-level debugging and abstraction tools; it
 * should not be mistaken for the low-level cull result itself, which is
 * constructed and maintained internally.  No such scene graph is normally
 * constructed during the rendering of a frame; this is an artificial
 * construct created for the purpose of making it easy to analyze the results
 * of the cull operation.
 */ 1 4 this 3 1395  
241 0 0 7 679 1424 0 0 0 0 
242 0 0 7 245 1477 0 0 0 1 4 this 3 1395  
243 0 0 6 247 1435 0 0 0 1 4 this 3 1395  
244 0 0 7 274 1360 0 0 306 /**
 * Returns the GSG that is associated with this window.  There is a one-to-one
 * association between windows and GSG's.
 *
 * This may return NULL if the graphics context has not yet been created for
 * the window, e.g.  before the first frame has rendered; or after the window
 * has been closed.
 */ 1 4 this 3 1478  
245 0 0 7 275 1368 0 0 297 /**
 * Returns the GraphicsPipe that this window is associated with.  It is
 * possible that the GraphicsPipe might have been deleted while an outstanding
 * PT(GraphicsOutput) prevented all of its children windows from also being
 * deleted; in this unlikely case, get_pipe() may return NULL.
 */ 1 4 this 3 1478  
246 0 0 7 276 1377 0 0 200 /**
 * Returns the graphics engine that created this output.  Since there is
 * normally only one GraphicsEngine object in an application, this is usually
 * the same as the global GraphicsEngine.
 */ 1 4 this 3 1478  
247 0 0 6 277 1428 0 0 78 /**
 * Returns the name that was passed to the GraphicsOutput constructor.
 */ 1 4 this 3 1478  
248 0 0 6 526 1327 0 0 261 /**
 * If the GraphicsOutput is set to render into a texture, returns the number
 * of textures that are being rendered into.  Normally, the textures would be
 * associated with different buffers - a color texture, a depth texture, and a
 * stencil texture.
 */ 1 4 this 3 1478  
249 0 0 6 527 1350 0 0 84 /**
 * Returns true if the GraphicsOutput is rendering into any textures at all.
 */ 1 4 this 3 1478  
250 0 0 7 528 1374 0 0 405 /**
 * Returns the nth texture into which the GraphicsOutput renders.  Returns
 * NULL if there is no such texture.
 *
 * If the texture is non-NULL, it may be applied to geometry to be rendered
 * for any other windows or outputs that share the same GSG as this
 * GraphicsOutput.  The effect is undefined for windows that share a different
 * GSG; usually in these cases the texture will be invalid.
 */ 2 4 this 3 1478  1 i 5 1327  
251 0 0 6 529 1336 0 0 123 /**
 * Returns the RenderTexturePlane associated with the nth render-texture.
 * Returns 0 if there is no such texture.
 */ 2 4 this 3 1478  1 i 5 1327  
252 0 0 6 530 1358 0 0 129 /**
 * Returns the RenderTextureMode associated with the nth render-texture.
 * Returns RTM_none if there is no such texture.
 */ 2 4 this 3 1478  1 i 5 1327  
253 0 0 4 531 1427 0 0 130 /**
 * If the GraphicsOutput is currently rendering to a texture, then all
 * textures are dissociated from the GraphicsOuput.
 */ 1 4 this 3 1355  
254 0 0 4 532 1427 0 0 1233 /**
 * Creates a new Texture object, suitable for rendering the contents of this
 * buffer into, and appends it to the list of render textures.
 *
 * If tex is not NULL, it is the texture that will be set up for rendering
 * into; otherwise, a new Texture object will be created, in which case you
 * may call get_texture() to retrieve the new texture pointer.
 *
 * You can specify a bitplane to attach the texture to.  the legal choices
 * are:
 *
 * - RTP_depth
 * - RTP_depth_stencil
 * - RTP_color
 * - RTP_aux_rgba_0
 * - RTP_aux_rgba_1
 * - RTP_aux_rgba_2
 * - RTP_aux_rgba_3
 *
 * If you do not specify a bitplane to attach the texture to, this routine
 * will use a default based on the texture's format:
 *
 * - F_depth_component attaches to RTP_depth
 * - F_depth_stencil attaches to RTP_depth_stencil
 * - all other formats attach to RTP_color.
 *
 * The texture's format will be changed to match the format of the bitplane to
 * which it is attached.  For example, if you pass in an F_rgba texture and
 * order that it be attached to RTP_depth_stencil, it will turn into an
 * F_depth_stencil texture.
 *
 * Also see make_texture_buffer(), which is a higher-level interface for
 * preparing render-to-a-texture mode.
 */ 4 4 this 3 1355  3 tex 1 1374  4 mode 1 1358  8 bitplane 5 1336  
255 0 0 4 533 1427 0 0 259 /**
 * This is a deprecated interface that made sense back when GraphicsOutputs
 * could only render into one texture at a time.  From now on, use
 * clear_render_textures and add_render_texture instead.
 *
 * @deprecated Use add_render_texture() instead.
 */ 4 4 this 3 1355  3 tex 1 1374  10 allow_bind 1 1350  6 to_ram 1 1350  
256 0 0 6 534 1452 0 0 410 /**
 * Returns the visible size of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */ 1 4 this 3 1478  
257 0 0 6 535 1327 0 0 411 /**
 * Returns the visible width of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */ 1 4 this 3 1478  
258 0 0 6 536 1327 0 0 412 /**
 * Returns the visible height of the window or buffer, if it is known.  In
 * certain cases (e.g.  fullscreen windows), the size may not be known until
 * after the object has been fully created.  Check has_size() first.
 *
 * Certain objects (like windows) may change size spontaneously; this method
 * is not thread-safe.  To get the size of a window in a thread-safe manner,
 * query get_properties().
 */ 1 4 this 3 1478  
259 0 0 7 537 1472 0 0 175 /**
 * Returns the internal size of the window or buffer.  This is almost always
 * the same as get_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */ 1 4 this 3 1478  
260 0 0 6 538 1327 0 0 178 /**
 * Returns the internal width of the window or buffer.  This is almost always
 * the same as get_x_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */ 1 4 this 3 1478  
261 0 0 6 539 1327 0 0 179 /**
 * Returns the internal height of the window or buffer.  This is almost always
 * the same as get_y_size(), except when a pixel_zoom is in effect--see
 * set_pixel_zoom().
 */ 1 4 this 3 1478  
262 0 0 7 540 1472 0 0 232 /**
 * If side-by-side stereo is enabled, this returns the pixel size of the left
 * eye, based on scaling get_size() by get_sbs_left_dimensions().  If side-by-
 * side stereo is not enabled, this returns the same as get_size().
 */ 1 4 this 3 1478  
263 0 0 6 541 1327 0 0 237 /**
 * If side-by-side stereo is enabled, this returns the pixel width of the left
 * eye, based on scaling get_x_size() by get_sbs_left_dimensions().  If side-
 * by-side stereo is not enabled, this returns the same as get_x_size().
 */ 1 4 this 3 1478  
264 0 0 6 542 1327 0 0 237 /**
 * If side-by-side stereo is enabled, this returns the pixel height of the
 * left eye, based on scaling get_y_size() by get_sbs_left_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_y_size().
 */ 1 4 this 3 1478  
265 0 0 7 543 1472 0 0 234 /**
 * If side-by-side stereo is enabled, this returns the pixel size of the right
 * eye, based on scaling get_size() by get_sbs_right_dimensions().  If side-
 * by-side stereo is not enabled, this returns the same as get_size().
 */ 1 4 this 3 1478  
266 0 0 6 544 1327 0 0 238 /**
 * If side-by-side stereo is enabled, this returns the pixel width of the
 * right eye, based on scaling get_x_size() by get_sbs_right_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_x_size().
 */ 1 4 this 3 1478  
267 0 0 6 545 1327 0 0 239 /**
 * If side-by-side stereo is enabled, this returns the pixel height of the
 * right eye, based on scaling get_y_size() by get_sbs_right_dimensions().  If
 * side-by-side stereo is not enabled, this returns the same as get_y_size().
 */ 1 4 this 3 1478  
268 0 0 6 546 1350 0 0 258 /**
 * Returns true if the size of the window/frame buffer is known, false
 * otherwise.  In certain cases the size may not be known until after the
 * object has been fully created.  Also, certain objects (like windows) may
 * change size spontaneously.
 */ 1 4 this 3 1478  
269 0 0 6 547 1350 0 0 99 /**
 * Returns true if the output is fully created and ready for rendering, false
 * otherwise.
 */ 1 4 this 3 1478  
270 0 0 6 548 1350 0 0 124 /**
 * Returns true if the output has a nonzero size in both X and Y, or false if
 * it is zero (and therefore invalid).
 */ 1 4 this 3 1478  
271 0 0 4 555 1427 0 0 134 /**
 * Sets the active flag associated with the GraphicsOutput.  If the
 * GraphicsOutput is marked inactive, nothing is rendered.
 */ 2 4 this 3 1355  6 active 1 1350  
272 0 0 6 556 1350 0 0 84 /**
 * Returns true if the window is ready to be rendered into, false otherwise.
 */ 1 4 this 3 1478  
273 0 0 4 558 1427 0 0 837 /**
 * Changes the current setting of the one-shot flag.  When this is true, the
 * GraphicsOutput will render the current frame and then automatically set
 * itself inactive.  This is particularly useful for buffers that are created
 * for the purposes of render-to-texture, for static textures that don't need
 * to be continually re-rendered once they have been rendered the first time.
 *
 * Setting the buffer inactive is not the same thing as destroying it.  You
 * are still responsible for passing this buffer to
 * GraphicsEngine::remove_window() when you no longer need the texture, in
 * order to clean up fully.  (However, you should not call remove_window() on
 * this buffer while the texture is still needed, because depending on the
 * render-to-texture mechanism in use, this may invalidate the texture
 * contents.)
 */ 2 4 this 3 1355  8 one_shot 1 1350  
274 0 0 6 559 1350 0 0 163 /**
 * Returns the current setting of the one-shot flag.  When this is true, the
 * GraphicsOutput will automatically set itself inactive after the next frame.
 */ 1 4 this 3 1478  
275 0 0 4 561 1427 0 0 582 /**
 * Changes the current setting of the inverted flag.  When this is true, the
 * scene is rendered into the window upside-down and backwards, that is,
 * inverted as if viewed through a mirror placed on the floor.
 *
 * This is primarily intended to support DirectX (and a few buggy OpenGL
 * graphics drivers) that perform a framebuffer-to-texture copy upside-down
 * from the usual OpenGL (and Panda) convention.  Panda will automatically set
 * this flag for offscreen buffers on hardware that is known to do this, to
 * compensate when rendering offscreen into a texture.
 */ 2 4 this 3 1355  8 inverted 1 1350  
276 0 0 6 562 1350 0 0 198 /**
 * Returns the current setting of the inverted flag.  When this is true, the
 * scene is rendered into the window upside-down, flipped like a mirror along
 * the X axis.  See set_inverted().
 */ 1 4 this 3 1478  
277 0 0 4 564 1427 0 0 383 /**
 * Changes the "swap eyes" flag.  This flag is normally false.  When it is
 * true, the left and right channels of a stereo DisplayRegion are sent to the
 * opposite channels in the rendering backend.  This is meant to work around
 * hardware that inadvertently swaps the output channels, or hardware for
 * which it cannot be determined which channel is which until runtime.
 */ 2 4 this 3 1355  9 swap_eyes 1 1350  
278 0 0 6 565 1350 0 0 85 /**
 * Returns the current setting of the "swap eyes" flag.  See set_swap_eyes().
 */ 1 4 this 3 1478  
279 0 0 4 567 1427 0 0 592 /**
 * Enables red-blue stereo mode on this particular window.  When red-blue
 * stereo mode is in effect, DisplayRegions that have the "left" channel set
 * will render in the red (or specified) channel only, while DisplayRegions
 * that have the "right" channel set will render in the blue (or specified)
 * channel only.
 *
 * The remaining two parameters specify the particular color channel(s) to
 * associate with each eye.  Use the bits defined in
 * ColorWriteAttrib::Channels.
 *
 * This can be used to achieve a cheesy stereo mode in the absence of
 * hardware-supported stereo.
 */ 4 4 this 3 1355  15 red_blue_stereo 1 1350  19 left_eye_color_mask 1 1341  20 right_eye_color_mask 1 1341  
280 0 0 6 568 1350 0 0 119 /**
 * Returns whether red-blue stereo mode is in effect for this particular
 * window.  See set_red_blue_stereo().
 */ 1 4 this 3 1478  
281 0 0 6 569 1341 0 0 198 /**
 * Returns the color mask in effect when rendering a left-eye view in red_blue
 * stereo mode.  This is one or more bits defined in
 * ColorWriteAttrib::Channels.  See set_red_blue_stereo().
 */ 1 4 this 3 1478  
282 0 0 6 570 1341 0 0 199 /**
 * Returns the color mask in effect when rendering a right-eye view in
 * red_blue stereo mode.  This is one or more bits defined in
 * ColorWriteAttrib::Channels.  See set_red_blue_stereo().
 */ 1 4 this 3 1478  
283 0 0 4 571 1427 0 0 614 /**
 * Enables side-by-side stereo mode on this particular window.  When side-by-
 * side stereo mode is in effect, DisplayRegions that have the "left" channel
 * set will render on the part of the window specified by sbs_left_dimensions
 * (typically the left half: (0, 0.5, 0, 1)), while DisplayRegions that have
 * the "right" channel set will render on the part of the window specified by
 * sbs_right_dimensions (typically the right half: (0.5, 1, 0, 1)).
 *
 * This is commonly used in a dual-monitor mode, where a window is opened that
 * spans two monitors, and each monitor represents a different eye.
 */ 2 4 this 3 1355  19 side_by_side_stereo 1 1350  
284 0 0 4 571 1427 0 0 614 /**
 * Enables side-by-side stereo mode on this particular window.  When side-by-
 * side stereo mode is in effect, DisplayRegions that have the "left" channel
 * set will render on the part of the window specified by sbs_left_dimensions
 * (typically the left half: (0, 0.5, 0, 1)), while DisplayRegions that have
 * the "right" channel set will render on the part of the window specified by
 * sbs_right_dimensions (typically the right half: (0.5, 1, 0, 1)).
 *
 * This is commonly used in a dual-monitor mode, where a window is opened that
 * spans two monitors, and each monitor represents a different eye.
 */ 4 4 this 3 1355  19 side_by_side_stereo 1 1350  19 sbs_left_dimensions 1 1465  20 sbs_right_dimensions 1 1465  
285 0 0 6 572 1350 0 0 127 /**
 * Returns whether side-by-side stereo mode is in effect for this particular
 * window.  See set_side_by_side_stereo().
 */ 1 4 this 3 1478  
286 0 0 6 573 1465 0 0 185 /**
 * Returns the effective sub-region of the window for displaying the left
 * channel, if side-by-side stereo mode is in effect for the window.  See
 * set_side_by_side_stereo().
 */ 1 4 this 3 1478  
287 0 0 6 574 1465 0 0 186 /**
 * Returns the effective sub-region of the window for displaying the right
 * channel, if side-by-side stereo mode is in effect for the window.  See
 * set_side_by_side_stereo().
 */ 1 4 this 3 1478  
288 0 0 6 575 1480 0 0 60 /**
 * Returns the framebuffer properties of the window.
 */ 1 4 this 3 1478  
289 0 0 6 576 1350 0 0 188 /**
 * Returns Returns true if this window can render stereo DisplayRegions,
 * either through red-blue stereo (see set_red_blue_stereo()) or through true
 * hardware stereo rendering.
 */ 1 4 this 3 1478  
290 0 0 4 577 1427 0 0 133 /**
 * Resets the delete flag, so the GraphicsOutput will not be automatically
 * deleted before the beginning of the next frame.
 */ 1 4 this 3 1355  
291 0 0 6 578 1350 0 0 195 /**
 * Returns the current setting of the delete flag.  When this is true, the
 * GraphicsOutput will automatically be removed before the beginning of the
 * next frame by the GraphicsEngine.
 */ 1 4 this 3 1478  
292 0 0 4 579 1427 0 0 109 /**
 * Adjusts the sorting order of this particular GraphicsOutput, relative to
 * other GraphicsOutputs.
 */ 2 4 this 3 1355  4 sort 1 1327  
293 0 0 6 580 1327 0 0 174 /**
 * Returns the sorting order of this particular GraphicsOutput.  The various
 * GraphicsOutputs within a particular thread will be rendered in the
 * indicated order.
 */ 1 4 this 3 1478  
294 0 0 4 582 1427 0 0 633 /**
 * Specifies the sort value of future offscreen buffers created by
 * make_texture_sort().
 *
 * The purpose of this method is to allow the user to limit the sort value
 * chosen for a buffer created via make_texture_buffer().  Normally, this
 * buffer will be assigned a value of get_sort() - 1, so that it will be
 * rendered before this window is rendered; but sometimes this isn't
 * sufficiently early, especially if other buffers also have a view into the
 * same scene.
 *
 * If you specify a value here, then new buffers created via
 * make_texture_buffer() will be given that sort value instead of get_sort() -
 * 1.
 */ 2 4 this 3 1355  10 child_sort 1 1327  
295 0 0 4 583 1427 0 0 139 /**
 * Resets the sort value of future offscreen buffers created by
 * make_texture_sort() to the default value.  See set_child_sort().
 */ 1 4 this 3 1355  
296 0 0 6 584 1327 0 0 118 /**
 * Returns the sort value of future offscreen buffers created by
 * make_texture_sort(). See set_child_sort().
 */ 1 4 this 3 1478  
297 0 0 7 586 1483 0 0 170 /**
 * When the GraphicsOutput is in triggered copy mode, this function triggers
 * the copy (at the end of the next frame).
 * @returns a future that can be awaited.
 */ 1 4 this 3 1355  
298 0 0 7 587 1395 840 0 321 /**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */ 1 4 this 3 1355  
299 0 0 7 587 1395 840 0 390 /**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */ 2 4 this 3 1355  10 dimensions 1 1465  
300 0 0 7 587 1395 840 0 390 /**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * If is_stereo() is true for this window, and default-stereo-camera is
 * configured true, this actually makes a StereoDisplayRegion.  Call
 * make_mono_display_region() or make_stereo_display_region() if you want to
 * insist on one or the other.
 */ 5 4 this 3 1355  1 l 1 1328  1 r 1 1328  1 b 1 1328  1 t 1 1328  
301 0 0 7 588 1395 840 0 398 /**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */ 1 4 this 3 1355  
302 0 0 7 588 1395 840 0 467 /**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */ 2 4 this 3 1355  10 dimensions 1 1465  
303 0 0 7 588 1395 840 0 398 /**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This generally returns a mono DisplayRegion, even if is_stereo() is true.
 * However, if side-by-side stereo is enabled, this will return a
 * StereoDisplayRegion whose two eyes are both set to SC_mono.  (This is
 * necessary because in side-by-side stereo mode, it is necessary to draw even
 * mono DisplayRegions twice).
 */ 5 4 this 3 1355  1 l 1 1328  1 r 1 1328  1 b 1 1328  1 t 1 1328  
304 0 0 7 589 1484 840 0 149 /**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */ 1 4 this 3 1355  
305 0 0 7 589 1484 840 0 218 /**
 * Creates a new DisplayRegion that covers the indicated sub-rectangle within
 * the window.  The range on all parameters is 0..1.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */ 2 4 this 3 1355  10 dimensions 1 1465  
306 0 0 7 589 1484 840 0 149 /**
 * Creates a new DisplayRegion that covers the entire window.
 *
 * This always returns a stereo DisplayRegion, even if is_stereo() is false.
 */ 5 4 this 3 1355  1 l 1 1328  1 r 1 1328  1 b 1 1328  1 t 1 1328  
307 0 0 6 590 1350 0 0 224 /**
 * Removes the indicated DisplayRegion from the window, and destructs it if
 * there are no other references.
 *
 * Returns true if the DisplayRegion is found and removed, false if it was not
 * a part of the window.
 */ 2 4 this 3 1355  14 display_region 1 1395  
308 0 0 4 591 1427 0 0 114 /**
 * Removes all display regions from the window, except the default one that is
 * created with the window.
 */ 1 4 this 3 1355  
309 0 0 7 592 1395 840 0 570 /**
 * Returns the special "overlay" DisplayRegion that is created for each window
 * or buffer.  This DisplayRegion covers the entire window, but cannot be used
 * for rendering.  It is a placeholder only, to indicate the dimensions of the
 * window, and is usually used internally for purposes such as clearing the
 * window, or grabbing a screenshot of the window.
 *
 * There are very few applications that require access to this DisplayRegion.
 * Normally, you should create your own DisplayRegion that covers the window,
 * if you want to render to the window.
 */ 1 4 this 3 1478  
310 0 0 4 593 1427 0 0 707 /**
 * Replaces the special "overlay" DisplayRegion that is created for each
 * window or buffer.  See get_overlay_display_region().  This must be a new
 * DisplayRegion that has already been created for this window, for instance
 * via a call to make_mono_display_region().  You are responsible for ensuring
 * that the new DisplayRegion covers the entire window.  The previous overlay
 * display region is not automatically removed; you must explicitly call
 * remove_display_region() on it after replacing it with this method, if you
 * wish it to be removed.
 *
 * Normally, there is no reason to change the overlay DisplayRegion, so this
 * method should be used only in very unusual circumstances.
 */ 2 4 this 3 1355  14 display_region 1 1395  
311 0 0 6 594 1327 0 0 113 /**
 * Returns the number of DisplayRegions that have been created within the
 * window, active or otherwise.
 */ 1 4 this 3 1478  
312 0 0 7 595 1395 840 0 260 /**
 * Returns the nth DisplayRegion of those that have been created within the
 * window.  This may return NULL if n is out of bounds; particularly likely if
 * the number of display regions has changed since the last call to
 * get_num_display_regions().
 */ 2 4 this 3 1478  1 n 1 1327  
313 0 0 6 599 1327 0 0 99 /**
 * Returns the number of active DisplayRegions that have been created within
 * the window.
 */ 1 4 this 3 1478  
314 0 0 7 600 1395 840 0 274 /**
 * Returns the nth active DisplayRegion of those that have been created within
 * the window.  This may return NULL if n is out of bounds; particularly
 * likely if the number of display regions has changed since the last call to
 * get_num_active_display_regions().
 */ 2 4 this 3 1478  1 n 1 1327  
315 0 0 7 603 1355 856 0 1369 /**
 * Creates and returns an offscreen buffer for rendering into, the result of
 * which will be a texture suitable for applying to geometry within the scene
 * rendered into this window.
 *
 * If you pass zero as the buffer size, the buffer will have the same size as
 * the host window, and will automatically be resized when the host window is.
 *
 * If tex is not NULL, it is the texture that will be set up for rendering
 * into; otherwise, a new Texture object will be created.  In either case, the
 * target texture can be retrieved from the return value with
 * buffer->get_texture() (assuming the return value is not NULL).
 *
 * If to_ram is true, the buffer will be set up to download its contents to
 * the system RAM memory associated with the Texture object, instead of
 * keeping it strictly within texture memory; this is much slower, but it
 * allows using the texture with any GSG.
 *
 * This will attempt to be smart about maximizing render performance while
 * minimizing framebuffer waste.  It might return a GraphicsBuffer set to
 * render directly into a texture, if possible; or it might return a
 * ParasiteBuffer that renders into this window.  The return value is NULL if
 * the buffer could not be created for some reason.
 *
 * When you are done using the buffer, you should remove it with a call to
 * GraphicsEngine::remove_window().
 */ 7 4 this 3 1355  4 name 1 1428  6 x_size 1 1327  6 y_size 1 1327  3 tex 5 1374  6 to_ram 5 1350  3 fbp 5 1485  
316 0 0 7 604 1355 856 0 763 /**
 * This is similar to make_texture_buffer() in that it allocates a separate
 * buffer suitable for rendering to a texture that can be assigned to geometry
 * in this window, but in this case, the buffer is set up to render the six
 * faces of a cube map.
 *
 * The buffer is automatically set up with six display regions and six
 * cameras, each of which are assigned the indicated draw_mask and parented to
 * the given camera_rig node (which you should then put in your scene to
 * render the cube map from the appropriate point of view).
 *
 * You may take the texture associated with the buffer and apply it to
 * geometry, particularly with TexGenAttrib::M_world_cube_map also in effect,
 * to apply a reflection of everything seen by the camera rig.
 */ 7 4 this 3 1355  4 name 1 1428  4 size 1 1327  10 camera_rig 1 1471  11 camera_mask 5 1486  6 to_ram 5 1350  3 fbp 5 1485  
317 0 0 7 605 1461 0 0 423 /**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The default filename
 * is generated from the supplied prefix and from the Config variable
 * screenshot-filename, which contains the following strings:
 *
 * %~p - the supplied prefix %~f - the frame count %~e - the value of
 * screenshot-extension All other % strings in strftime().
 */ 1 6 prefix 5 1428  
318 0 0 7 606 1461 0 0 198 /**
 * Saves a screenshot of the region to a default filename, and returns the
 * filename, or empty string if the screenshot failed.  The filename is
 * generated by make_screenshot_filename().
 */ 2 4 this 3 1355  6 prefix 5 1428  
319 0 0 6 607 1350 0 0 304 /**
 * Saves a screenshot of the region to the indicated filename.  The image
 * comment is an optional user readable string that will be saved with the
 * header of the image (if the file format supports embedded data; for example
 * jpg allows comments).  Returns true on success, false on failure.
 */ 3 4 this 3 1355  8 filename 1 1459  13 image_comment 5 1428  
320 0 0 7 608 1374 0 0 123 /**
 * Captures the most-recently rendered image from the framebuffer and returns
 * it as Texture, or NULL on failure.
 */ 1 4 this 3 1355  
321 0 0 6 608 1350 0 0 149 /**
 * Captures the most-recently rendered image from the framebuffer into the
 * indicated PNMImage.  Returns true on success, false on failure.
 */ 2 4 this 3 1355  5 image 1 1473  
322 0 0 7 609 1471 0 0 573 /**
 * Returns a PandaNode containing a square polygon.  The dimensions are
 * (-1,0,-1) to (1,0,1). The texture coordinates are such that the texture of
 * this GraphicsOutput is aligned properly to the polygon.  The GraphicsOutput
 * promises to surgically update the Geom inside the PandaNode if necessary to
 * maintain this invariant.
 *
 * Each invocation of this function returns a freshly- allocated PandaNode.
 * You can therefore safely modify the RenderAttribs of the PandaNode.  The
 * PandaNode is initially textured with the texture of this GraphicOutput.
 */ 1 4 this 3 1355  
323 0 0 6 610 1350 0 0 124 /**
 * Will attempt to use the depth buffer of the input graphics_output.  The
 * buffer sizes must be exactly the same.
 */ 2 4 this 3 1355  15 graphics_output 1 1355  
324 0 0 4 611 1427 0 0 48 /**
 * Discontinue sharing the depth buffer.
 */ 1 4 this 3 1355  
325 0 0 6 612 1350 0 0 191 /**
 * Returns true if this particular GraphicsOutput can render directly into a
 * texture, or false if it must always copy-to-texture at the end of each
 * frame to achieve this effect.
 */ 1 4 this 3 1478  
326 0 0 6 614 1350 0 0 160 // These are not intended to be called directly by the user, but they're
// published anyway since they might occasionally be useful for low-level
// debugging. 1 4 this 3 1478  
327 0 0 7 615 1355 856 0 218 /**
 * This is normally called only from within make_texture_buffer().  When
 * called on a ParasiteBuffer, it returns the host of that buffer; but when
 * called on some other buffer, it returns the buffer itself.
 */ 1 4 this 3 1355  
328 0 0 7 616 1424 0 0 0 0 
329 0 0 7 267 1490 0 0 0 1 4 this 3 1355  
330 0 0 6 269 1435 0 0 0 1 4 this 3 1355  
331 0 0 4 283 1427 0 0 41 /**
 * Releases all prepared objects.
 */ 1 4 this 3 1360  
332 0 0 6 284 1327 0 0 73 /**
 * Frees the resources for all textures associated with this GSG.
 */ 1 4 this 3 1360  
333 0 0 6 285 1327 0 0 73 /**
 * Frees the resources for all samplers associated with this GSG.
 */ 1 4 this 3 1360  
334 0 0 6 286 1327 0 0 70 /**
 * Frees the resources for all geoms associated with this GSG.
 */ 1 4 this 3 1360  
335 0 0 6 287 1327 0 0 79 /**
 * Frees the resources for all vertex buffers associated with this GSG.
 */ 1 4 this 3 1360  
336 0 0 6 288 1327 0 0 78 /**
 * Frees the resources for all index buffers associated with this GSG.
 */ 1 4 this 3 1360  
337 0 0 6 289 1327 0 0 78 /**
 * Frees the resources for all index buffers associated with this GSG.
 */ 1 4 this 3 1360  
338 0 0 4 290 1427 0 0 257 /**
 * Sets the active flag associated with the GraphicsStateGuardian.  If the
 * GraphicsStateGuardian is marked inactive, nothing is rendered.  This is not
 * normally turned off unless there is a problem with the rendering detected
 * at a low level.
 */ 2 4 this 3 1360  6 active 1 1350  
339 0 0 6 291 1350 0 0 77 /**
 * Returns the active flag associated with the GraphicsStateGuardian.
 */ 1 4 this 3 1491  
340 0 0 6 292 1350 0 0 170 /**
 * Returns true if the GSG has been correctly initialized within a graphics
 * context, false if there has been some problem or it hasn't been initialized
 * yet.
 */ 1 4 this 3 1491  
341 0 0 6 293 1350 0 0 64 /**
 * Returns true if the gsg is marked as needing a reset.
 */ 1 4 this 3 1491  
342 0 0 4 296 1427 0 0 962 /**
 * Sets the incomplete_render flag.  When this is true, the frame will be
 * rendered even if some of the geometry or textures in the scene are not
 * available (e.g.  they have been temporarily paged out).  When this is
 * false, the frame will be held up while this data is reloaded.
 *
 * Setting this true allows for a smoother frame rate, but occasionally parts
 * of the frame will be invisible or missing (they will generally come in
 * within a second or two).  Setting this false guarantees that every frame
 * will be complete, but may cause more chugs as things are loaded up at
 * runtime.
 *
 * You may want to set this false during loading screens, to guarantee that
 * all of your assets are available by the time you take the loading screen
 * down.
 *
 * This flag may also be set individually on each DisplayRegion.  It will be
 * considered true for a given DisplayRegion only if it is true on both the
 * GSG and on the DisplayRegion.
 */ 2 4 this 3 1360  17 incomplete_render 1 1350  
343 0 0 4 301 1427 0 0 135 /**
 * Sets the Loader object that will be used by this GSG to load textures when
 * necessary, if get_incomplete_render() is true.
 */ 2 4 this 3 1360  6 loader 1 1364  
344 0 0 7 302 1364 0 0 138 /**
 * Returns the Loader object that will be used by this GSG to load textures
 * when necessary, if get_incomplete_render() is true.
 */ 1 4 this 3 1491  
345 0 0 4 306 1427 0 0 111 /**
 * Sets the ShaderGenerator object that will be used by this GSG to generate
 * shaders when necessary.
 */ 2 4 this 3 1360  16 shader_generator 1 1366  
346 0 0 7 307 1366 0 0 114 /**
 * Returns the ShaderGenerator object that will be used by this GSG to
 * generate shaders when necessary.
 */ 1 4 this 3 1491  
347 0 0 7 311 1368 0 0 67 /**
 * Returns the graphics pipe on which this GSG was created.
 */ 1 4 this 3 1491  
348 0 0 7 312 1377 0 0 0 1 4 this 3 1491  
349 0 0 6 313 1493 0 0 72 /**
 * Returns the threading model that was used to create this GSG.
 */ 1 4 this 3 1491  
350 0 0 6 316 1350 0 0 119 /**
 * Returns true if this GSG appears to be hardware-accelerated, or false if it
 * is known to be software only.
 */ 1 4 this 3 1491  
351 0 0 6 317 1327 0 0 490 /**
 * Returns the maximum number of simultaneous textures that may be applied to
 * geometry with multitexturing, as supported by this particular GSG.  If you
 * exceed this number, the lowest-priority texture stages will not be applied.
 * Use TextureStage::set_priority() to adjust the relative importance of the
 * different texture stages.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
352 0 0 6 318 1327 0 0 325 /**
 * Returns the largest possible texture size in any one dimension for a 3-d
 * texture, or -1 if there is no particular limit.  Returns 0 if 3-d textures
 * are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
353 0 0 6 319 1327 0 0 288 /**
 * Returns the largest possible number of pages, or -1 if there is no
 * particular limit.  Returns 0 if 2-d texture arrays not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
354 0 0 6 320 1327 0 0 335 /**
 * Returns the largest possible texture size in any one dimension for a cube
 * map texture, or -1 if there is no particular limit.  Returns 0 if cube map
 * textures are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
355 0 0 6 321 1327 0 0 295 /**
 * Returns the largest possible buffer texture size, or -1 if there is no
 * particular limit.  Returns 0 if cube map textures are not supported.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
356 0 0 6 322 1350 0 0 279 /**
 * Returns true if this particular GSG can use the TextureStage::M_combine
 * mode, which includes all of the texture blend modes specified by
 * set_combine_rgb() and/or set_combine_alpha().  If this is false, you must
 * limit yourself to using the simpler blend modes.
 */ 1 4 this 3 1491  
357 0 0 6 323 1350 0 0 185 /**
 * Returns true if this GSG can use the TextureStage::CS_last_saved_result
 * source, which allows you to save the result of a TextureStage and re-use it
 * for multiple inputs.
 */ 1 4 this 3 1491  
358 0 0 6 324 1350 0 0 107 /**
 * Returns true if this GSG can use the TextureStage::CM_dot3_rgb or
 * CM_dot3_rgba combine modes.
 */ 1 4 this 3 1491  
359 0 0 6 325 1350 0 0 73 /**
 * Returns true if this GSG can render 3-d (volumetric) textures.
 */ 1 4 this 3 1491  
360 0 0 6 326 1350 0 0 66 /**
 * Returns true if this GSG can render 2-d textures array.
 */ 1 4 this 3 1491  
361 0 0 6 327 1350 0 0 65 /**
 * Returns true if this GSG can render cube map textures.
 */ 1 4 this 3 1491  
362 0 0 6 328 1350 0 0 63 /**
 * Returns true if this GSG can render buffer textures.
 */ 1 4 this 3 1491  
363 0 0 6 329 1350 0 0 63 /**
 * Returns true if this GSG can render cube map arrays.
 */ 1 4 this 3 1491  
364 0 0 6 330 1350 0 0 79 /**
 * Returns true if this GSG can handle non power of two sized textures.
 */ 1 4 this 3 1491  
365 0 0 6 331 1350 0 0 149 /**
 * Returns true if this GSG can compress textures as it loads them into
 * texture memory, and/or accept pre-compressed textures for storing.
 */ 1 4 this 3 1491  
366 0 0 6 332 1327 0 0 278 /**
 * Returns the maximum number of simultaneous lights that may be rendered on
 * geometry, or -1 if there is no particular limit.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
367 0 0 6 333 1327 0 0 282 /**
 * Returns the maximum number of simultaneous clip planes that may be applied
 * to geometry, or -1 if there is no particular limit.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
368 0 0 6 334 1327 0 0 465 /**
 * Returns the maximum number of transform matrices that may be simultaneously
 * used to transform any one vertex by the graphics hardware.  If this number
 * is 0, then the hardware (or the graphics backend) doesn't support soft-
 * skinned vertices (in which case Panda will animate the vertices in
 * software).
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
369 0 0 6 335 1327 0 0 450 /**
 * Returns the maximum number of transforms there may be in a single
 * TransformTable for this graphics hardware.  If this number is 0 (but
 * get_max_transforms() is nonzero), then the graphics hardware (or API)
 * doesn't support indexed transforms, but can support direct transform
 * references.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
370 0 0 6 336 1350 0 0 574 /**
 * Returns true if this particular GSG has the property that any framebuffer-
 * to-texture copy results in a texture that is upside-down and backwards from
 * Panda's usual convention; that is, it copies into a texture from the bottom
 * up instead of from the top down.
 *
 * If this is true, then on offscreen GraphicsBuffer created for the purposes
 * of rendering into a texture should be created with the invert flag set
 * true, to compensate.  Panda will do this automatically if you create an
 * offscreen buffer using GraphicsOutput::make_texture_buffer().
 */ 1 4 this 3 1491  
371 0 0 6 337 1350 0 0 257 /**
 * Returns true if this particular GSG can generate mipmaps for a texture
 * automatically, or if they must be generated in software.  If this is true,
 * then mipmaps can safely be enabled for rendered textures (e.g.  using the
 * MultitexReducer).
 */ 1 4 this 3 1491  
372 0 0 6 338 1350 0 0 246 /**
 * Returns true if this particular GSG supports textures whose format is
 * F_depth_stencil.  This returns true if the GSG supports GL_DEPTH_COMPONENT
 * textures, which are considered a limited but still valid case of
 * F_depth_stencil.
 */ 1 4 this 3 1491  
373 0 0 6 339 1350 0 0 192 /**
 * Returns true if this particular GSG supports textures whose format is
 * F_depth_stencil.  This only returns true if the GSG supports the full
 * packed depth-stencil functionality.
 */ 1 4 this 3 1491  
374 0 0 6 340 1350 0 0 75 /**
 * Returns true if this particular GSG supports luminance textures.
 */ 1 4 this 3 1491  
375 0 0 6 341 1350 0 0 345 /**
 * Returns true if this particular GSG supports the use of sampler objects to
 * record texture sampling parameters separately from the texture objects.
 * This doesn't really affect functionality, but if this is false, it may mean
 * that using the same texture with different SamplerState objects will result
 * in reduced performance.
 */ 1 4 this 3 1491  
376 0 0 6 342 1350 0 0 79 /**
 * Returns true if this particular GSG supports arbfp1+arbvp1 or above.
 */ 1 4 this 3 1491  
377 0 0 6 343 1350 0 0 73 /**
 * Returns true if this particular GSG supports geometry shaders.
 */ 1 4 this 3 1491  
378 0 0 6 344 1350 0 0 76 /**
 * Returns true if this particular GSG supports tesselation shaders.
 */ 1 4 this 3 1491  
379 0 0 6 345 1350 0 0 72 /**
 * Returns true if this particular GSG supports compute shaders.
 */ 1 4 this 3 1491  
380 0 0 6 346 1350 0 0 69 /**
 * Returns true if this particular GSG supports GLSL shaders.
 */ 1 4 this 3 1491  
381 0 0 6 347 1350 0 0 79 /**
 * Returns true if this particular GSG supports stencil buffers at all.
 */ 1 4 this 3 1491  
382 0 0 6 348 1350 0 0 153 /**
 * Returns true if this particular GSG supports two sided stencil: different
 * stencil settings for the front and back side of the same polygon.
 */ 1 4 this 3 1491  
383 0 0 6 349 1350 0 0 206 /**
 * Returns true if this particular GSG supports hardware geometry instancing:
 * the ability to render multiple copies of a model.  In OpenGL, this is done
 * using the EXT_draw_instanced extension.
 */ 1 4 this 3 1491  
384 0 0 6 350 1350 0 0 116 /**
 * Returns true if this particular GSG supports draw calls for which the
 * information comes from a buffer.
 */ 1 4 this 3 1491  
385 0 0 6 351 1350 0 0 266 /**
 * Returns true if this GSG supports an occlusion query.  If this is true,
 * then begin_occlusion_query() and end_occlusion_query() may be called to
 * bracket a sequence of draw_triangles() (or whatever) calls to measure
 * pixels that pass the depth test.
 */ 1 4 this 3 1491  
386 0 0 6 352 1350 0 0 59 /**
 * Returns true if this GSG supports a timer query.
 */ 1 4 this 3 1491  
387 0 0 6 353 1350 0 0 75 /**
 * Returns true if timer queries are currently enabled on this GSG.
 */ 1 4 this 3 1491  
388 0 0 6 354 1327 0 0 488 /**
 * Returns the maximum number of simultaneous color textures that may be
 * attached for render-to-texture, as supported by this particular GSG.  If
 * you exceed this number, the lowest-priority render targets will not be
 * applied.  Use RenderTarget::set_priority() to adjust the relative
 * importance of the different render targets.
 *
 * The value returned may not be meaningful until after the graphics context
 * has been fully created (e.g.  the window has been opened).
 */ 1 4 this 3 1491  
389 0 0 6 355 1327 0 0 99 /**
 * Deprecated.  Use get_max_color_targets() instead, which returns the exact
 * same value.
 */ 1 4 this 3 1491  
390 0 0 6 356 1350 0 0 121 /**
 * Returns true if dual source (incoming1_color and incoming1_alpha) blend
 * operands are supported by this GSG.
 */ 1 4 this 3 1491  
391 0 0 6 410 1363 0 0 34 /**
 * Returns the ShaderModel
 */ 1 4 this 3 1491  
392 0 0 4 411 1427 0 0 144 /**
 * Sets the ShaderModel.  This will override the auto- detected shader model
 * during GSG reset.  Useful for testing lower-end shaders.
 */ 2 4 this 3 1360  12 shader_model 1 1363  
393 0 0 6 413 1350 0 0 0 2 4 this 3 1491  4 name 1 1428  
394 0 0 6 414 1350 0 0 214 /**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) set color and/or color scale using materials and/or ambient
 * lights, or false if we need to actually munge the color.
 */ 1 4 this 3 1491  
395 0 0 6 415 1350 0 0 194 /**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) an alpha scale via an additional Texture layer, or false if we
 * need to actually munge the alpha.
 */ 1 4 this 3 1491  
396 0 0 6 415 1350 0 0 324 /**
 * This variant of get_alpha_scale_via_texture() answers the question of
 * whether the GSG can implement an alpha scale via an additional Texture
 * layer, considering the current TextureAttrib that will be in effect.  This
 * considers whether there is at least one additional texture slot available
 * on the GSG.
 */ 2 4 this 3 1491  10 tex_attrib 1 1495  
397 0 0 6 416 1350 0 0 203 /**
 * Returns true if this particular GSG can implement (or would prefer to
 * implement) set color and/or color scale directly, without requiring any
 * munging of vertices or tricks with lighting.
 */ 1 4 this 3 1491  
398 0 0 7 417 1499 0 0 128 /**
 * Returns the TextureStage that will be used to apply an alpha scale, if
 * get_alpha_scale_via_texture() returns true.
 */ 0 
399 0 0 4 418 1427 0 0 0 2 4 this 3 1360  2 cs 1 1369  
400 0 0 6 419 1369 0 0 179 /**
 * Returns the coordinate system in effect on this particular gsg.  Normally,
 * this will be the default coordinate system, but it might be set differently
 * at runtime.
 */ 1 4 this 3 1491  
401 0 0 6 420 1369 0 0 0 1 4 this 3 1491  
402 0 0 7 423 1370 0 0 0 1 4 this 3 1360  
403 0 0 6 427 1350 0 0 0 2 4 this 3 1360  5 gamma 1 1328  
404 0 0 6 428 1328 0 0 0 1 4 this 3 1491  
405 0 0 4 429 1427 0 0 0 1 4 this 3 1360  
406 0 0 4 431 1427 0 0 405 /**
 * Specifies the global quality_level to be imposed for all Textures rendered
 * by this GSG.  This overrides the value set on individual textures via
 * Texture::set_quality_level().  Set this to Texture::QL_default in order to
 * allow the individual texture quality levels to be respected.
 *
 * This is mainly useful for the tinydisplay software renderer.  See
 * Texture::set_quality_level().
 */ 2 4 this 3 1360  13 quality_level 1 1372  
407 0 0 6 432 1372 0 0 203 /**
 * Returns the global quality_level override specified by
 * set_texture_quality_override.
 *
 * This is mainly useful for the tinydisplay software renderer.  See
 * Texture::set_quality_level().
 */ 1 4 this 3 1491  
408 0 0 38 436 1446 0 0 0 1 4 this 3 1491  
409 0 0 4 438 1427 0 0 0 2 4 this 3 1360  3 tex 1 1374  
410 0 0 4 439 1427 0 0 0 1 4 this 3 1360  
411 0 0 7 440 1374 0 0 0 1 4 this 3 1491  
412 0 0 6 443 1350 0 0 0 2 4 this 3 1491  9 extension 1 1428  
413 0 0 6 444 1428 0 0 0 1 4 this 3 1360  
414 0 0 6 445 1428 0 0 0 1 4 this 3 1360  
415 0 0 6 446 1428 0 0 0 1 4 this 3 1360  
416 0 0 6 447 1327 0 0 0 1 4 this 3 1360  
417 0 0 6 448 1327 0 0 0 1 4 this 3 1360  
418 0 0 6 449 1327 0 0 0 1 4 this 3 1360  
419 0 0 6 450 1327 0 0 0 1 4 this 3 1360  
420 0 0 6 458 1350 0 0 0 2 4 this 3 1360  11 scene_setup 1 1375  
421 0 0 7 459 1375 0 0 0 1 4 this 3 1491  
422 0 0 6 463 1350 0 0 0 1 4 this 3 1360  
423 0 0 4 464 1427 0 0 0 1 4 this 3 1360  
424 0 0 7 465 1424 0 0 0 0 
425 0 0 7 471 1377 0 0 187 /**
 * Creates a new GraphicsEngine object.  The Pipeline is normally left to
 * default to NULL, which indicates the global render pipeline, but it may be
 * any Pipeline you choose.
 */ 1 8 pipeline 5 1500  
426 0 0 4 472 1427 0 0 171 /**
 * Specifies how future objects created via make_gsg(), make_buffer(), and
 * make_output() will be threaded.  This does not affect any already-created
 * objects.
 */ 2 4 this 3 1377  15 threading_model 1 1493  
427 0 0 7 473 1504 981 0 109 /**
 * Returns the threading model that will be applied to future objects.  See
 * set_threading_model().
 */ 1 4 this 3 1502  
428 0 0 6 491 1505 0 0 241 /**
 * Returns a ReMutex object that is held by the GraphicsEngine during the
 * entire call to render_frame().  While you hold this lock you can be
 * confident that no part of the frame will be rendered (at least by the app
 * thread).
 */ 1 4 this 3 1502  
429 0 0 4 495 1427 0 0 559 /**
 * Set this flag true to indicate the GraphicsEngine should automatically
 * cause windows to sync and flip as soon as they have finished drawing,
 * rather than waiting for all of the windows to finish drawing first so they
 * can flip together.
 *
 * This only affects the timing of when the flip occurs.  If this is true (the
 * default), the flip occurs before render_frame() returns.  If this is false,
 * the flip occurs whenever flip_frame() is called, or at the beginning of the
 * next call to render_frame(), if flip_frame() is never called.
 */ 2 4 this 3 1377  9 auto_flip 1 1350  
430 0 0 6 496 1350 0 0 82 /**
 * Returns the current setting for the auto-flip flag.  See set_auto_flip.
 */ 1 4 this 3 1502  
431 0 0 4 498 1427 0 0 91 /**
 * Set this flag true to indicate the GraphicsEngine should start portal
 * culling
 */ 2 4 this 3 1377  5 value 1 1350  
432 0 0 6 499 1350 0 0 67 /**
 * Returns the current setting for the portal culling flag.
 */ 1 4 this 3 1502  
433 0 0 4 501 1427 0 0 147 /**
 * Sets the Loader object that will be assigned to every GSG created with this
 * GraphicsEngine.  See GraphicsStateGuardian::set_loader().
 */ 2 4 this 3 1377  6 loader 1 1364  
434 0 0 7 502 1364 0 0 150 /**
 * Returns the Loader object that will be assigned to every GSG created with
 * this GraphicsEngine.  See GraphicsStateGuardian::set_loader().
 */ 1 4 this 3 1502  
435 0 0 7 504 1355 856 0 0 9 4 this 3 1377  4 pipe 1 1368  4 name 1 1428  4 sort 1 1327  7 fb_prop 1 1480  8 win_prop 1 1450  5 flags 1 1327  3 gsg 5 1360  4 host 5 1355  
436 0 0 7 505 1355 856 0 404 /**
 * Syntactic shorthand for make_output.  This is the preferred way to create
 * an offscreen buffer, when you already have an onscreen window or another
 * buffer to start with.  For the first parameter, pass an existing
 * GraphicsOutput object, e.g.  the main window; this allows the buffer to
 * adapt itself to that window's framebuffer properties, and allows maximum
 * sharing of resources.
 */ 6 4 this 3 1377  4 host 1 1355  4 name 1 1428  4 sort 1 1327  6 x_size 1 1327  6 y_size 1 1327  
437 0 0 7 505 1355 856 0 753 /**
 * Syntactic shorthand for make_output.  This flavor accepts a GSG rather than
 * a GraphicsOutput as the first parameter, which is too limiting and
 * disallows the possibility of creating a ParasiteBuffer if the user's
 * graphics hardware prefers that.  It also attempts to request specific
 * framebuffer properties and may therefore do a poorer job of sharing the GSG
 * between the old buffer and the new.
 *
 * For these reasons, this variant is a poor choice unless you are creating an
 * offscreen buffer for the first time, without an onscreen window already in
 * existence.  If you already have an onscreen window, you should use the
 * other flavor of make_buffer() instead, which accepts a GraphicsOutput as
 * the first parameter.
 */ 6 4 this 3 1377  3 gsg 1 1360  4 name 1 1428  4 sort 1 1327  6 x_size 1 1327  6 y_size 1 1327  
438 0 0 7 506 1355 856 0 47 /**
 * Syntactic shorthand for make_buffer.
 */ 6 4 this 3 1377  4 host 1 1355  4 name 1 1428  4 sort 1 1327  6 x_size 1 1327  6 y_size 1 1327  
439 0 0 6 507 1350 0 0 609 /**
 * This can be used to add a newly-created GraphicsOutput object (and its GSG)
 * to the engine's list of windows, and requests that it be opened.  This
 * shouldn't be called by user code as make_output normally does this under
 * the hood; it may be useful in esoteric cases in which a custom window
 * object is used.
 *
 * This can be called during the rendering loop, unlike make_output(); the
 * window will be opened before the next frame begins rendering.  Because it
 * doesn't call open_windows(), however, it's not guaranteed that the window
 * will succeed opening even if it returns true.
 */ 3 4 this 3 1377  6 window 1 1355  4 sort 1 1327  
440 0 0 6 508 1350 0 0 886 /**
 * Removes the indicated window or offscreen buffer from the set of windows
 * that will be processed when render_frame() is called.  This also closes the
 * window if it is open, and removes the window from its GraphicsPipe,
 * allowing the window to be destructed if there are no other references to
 * it.  (However, the window may not be actually closed until next frame, if
 * it is controlled by a sub-thread.)
 *
 * The return value is true if the window was removed, false if it was not
 * found.
 *
 * Unlike remove_all_windows(), this function does not terminate any of the
 * threads that may have been started to service this window; they are left
 * running (since you might open a new window later on these threads).  If
 * your intention is to clean up before shutting down, it is better to call
 * remove_all_windows() then to call remove_window() one at a time.
 */ 2 4 this 3 1377  6 window 1 1355  
441 0 0 4 509 1427 0 0 158 /**
 * Removes and closes all windows from the engine.  This also cleans up and
 * terminates any threads that have been started to service those windows.
 */ 1 4 this 3 1377  
442 0 0 4 510 1427 0 0 199 /**
 * Resets the framebuffer of the current window.  This is currently used by
 * DirectX 8 only.  It calls a reset_window function on each active window to
 * release/create old/new framebuffer
 */ 2 4 this 3 1377  9 swapchain 1 1350  
443 0 0 6 511 1350 0 0 116 /**
 * Returns true if there are no windows or buffers managed by the engine,
 * false if there is at least one.
 */ 1 4 this 3 1502  
444 0 0 6 512 1327 0 0 76 /**
 * Returns the number of windows (or buffers) managed by the engine.
 */ 1 4 this 3 1502  
445 0 0 7 513 1355 856 0 84 /**
 * Returns the nth window or buffers managed by the engine, in sorted order.
 */ 2 4 this 3 1502  1 n 1 1327  
446 0 0 4 516 1427 0 0 103 /**
 * Renders the next frame in all the registered windows, and flips all of the
 * frame buffers.
 */ 1 4 this 3 1377  
447 0 0 4 517 1427 0 0 351 /**
 * Fully opens (or closes) any windows that have recently been requested open
 * or closed, without rendering any frames.  It is not necessary to call this
 * explicitly, since windows will be automatically opened or closed when the
 * next frame is rendered, but you may call this if you want your windows now
 * without seeing a frame go by.
 */ 1 4 this 3 1377  
448 0 0 4 518 1427 0 0 284 /**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing.  The windows are not yet flipped when this returns; see also
 * flip_frame(). It is not usually necessary to call this explicitly, unless
 * you need to see the previous frame right away.
 */ 1 4 this 3 1377  
449 0 0 4 519 1427 0 0 617 /**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing.  Returns when all threads have actually finished drawing, as
 * opposed to 'sync_frame' we seems to return once all draw calls have been
 * submitted.  Calling 'flip_frame' after this function should immediately
 * cause a buffer flip.  This function will only work in opengl right now, for
 * all other graphics pipelines it will simply return immediately.  In opengl
 * it's a bit of a hack: it will attempt to read a single pixel from the frame
 * buffer to force the graphics card to finish drawing before it returns
 */ 1 4 this 3 1377  
450 0 0 4 520 1427 0 0 239 /**
 * Waits for all the threads that started drawing their last frame to finish
 * drawing, and then flips all the windows.  It is not usually necessary to
 * call this explicitly, unless you need to see the previous frame right away.
 */ 1 4 this 3 1377  
451 0 0 6 521 1350 0 0 1037 /**
 * Asks the indicated GraphicsStateGuardian to retrieve the texture memory
 * image of the indicated texture and store it in the texture's ram_image
 * field.  The image can then be written to disk via Texture::write(), or
 * otherwise manipulated on the CPU.
 *
 * This is useful for retrieving the contents of a texture that has been
 * somehow generated on the graphics card, instead of having been loaded the
 * normal way via Texture::read() or Texture::load(). It is particularly
 * useful for getting the data associated with a compressed texture image.
 *
 * Since this requires a round-trip to the draw thread, it may require waiting
 * for the current thread to finish rendering if it is called in a
 * multithreaded environment.  However, you can call this several consecutive
 * times on different textures for little additional cost.
 *
 * If the texture has not yet been loaded to the GSG in question, it will be
 * loaded immediately.
 *
 * The return value is true if the operation is successful, false otherwise.
 */ 3 4 this 3 1377  3 tex 1 1374  3 gsg 1 1360  
452 0 0 4 522 1427 0 0 653 /**
 * Asks the indicated GraphicsStateGuardian to dispatch the compute shader in
 * the given ShaderAttrib using the given work group counts.  This can act as
 * an interface for running a one-off compute shader, without having to store
 * it in the scene graph using a ComputeNode.
 *
 * Since this requires a round-trip to the draw thread, it may require waiting
 * for the current thread to finish rendering if it is called in a
 * multithreaded environment.  However, you can call this several consecutive
 * times on different textures for little additional cost.
 *
 * The return value is true if the operation is successful, false otherwise.
 */ 4 4 this 3 1377  11 work_groups 1 1506  5 sattr 1 1509  3 gsg 1 1360  
453 0 0 7 523 1377 0 0 10 /**
 *
 */ 0 
454 0 0 15 476 1504 981 0 10 /**
 *
 */ 1 4 copy 1 1493  
455 0 0 23 476 1504 981 0 1237 /**
 * The threading model accepts a string representing the names of the two
 * threads that will process cull and draw for the given window, separated by
 * a slash.  The names are completely arbitrary and are used only to
 * differentiate threads.  The two names may be the same, meaning the same
 * thread, or each may be the empty string, which represents the previous
 * thread.
 *
 * Thus, for example, "cull/draw" indicates that the window will be culled in
 * a thread called "cull", and drawn in a separate thread called "draw".
 * "draw/draw" or simply "draw" indicates the window will be culled and drawn
 * in the same thread, "draw". On the other hand, "/draw" indicates the thread
 * will be culled in the main, or app thread, and drawn in a separate thread
 * named "draw".  The empty string, "" or "/", indicates the thread will be
 * culled and drawn in the main thread; that is to say, a single-process
 * model.
 *
 * Finally, if the threading model begins with a "-" character, then cull and
 * draw are run simultaneously, in the same thread, with no binning or state
 * sorting.  It simplifies the cull process but it forces the scene to render
 * in scene graph order; state sorting and alpha sorting is lost.
 */ 1 5 model 5 1428  
456 0 0 6 477 1504 0 0 0 2 4 this 3 1504  4 copy 1 1493  
457 0 0 6 478 1428 0 0 90 /**
 * Returns the string that describes the threading model.  See the
 * constructor.
 */ 1 4 this 3 1493  
458 0 0 6 479 1428 0 0 81 /**
 * Returns the name of the thread that will handle culling in this model.
 */ 1 4 this 3 1493  
459 0 0 4 480 1427 0 0 209 /**
 * Changes the name of the thread that will handle culling in this model.
 * This won't change any windows that were already created with this model;
 * this only has an effect on newly-opened windows.
 */ 2 4 this 3 1504  9 cull_name 1 1428  
460 0 0 6 481 1327 0 0 181 /**
 * Returns the pipeline stage from which the cull thread should access data.
 * This will be 0 if the cull is run in the same thread as app, or 1 if it is
 * its own thread.
 */ 1 4 this 3 1493  
461 0 0 6 482 1428 0 0 135 /**
 * Returns the name of the thread that will handle sending the actual graphics
 * primitives to the graphics API in this model.
 */ 1 4 this 3 1493  
462 0 0 4 483 1427 0 0 209 /**
 * Changes the name of the thread that will handle drawing in this model.
 * This won't change any windows that were already created with this model;
 * this only has an effect on newly-opened windows.
 */ 2 4 this 3 1504  9 cull_name 1 1428  
463 0 0 6 484 1327 0 0 251 /**
 * Returns the pipeline stage from which the draw thread should access data.
 * This will be the same value as get_cull_stage() if cull and draw are run in
 * the same thread, or one more than that value if draw should be in its own
 * thread.
 */ 1 4 this 3 1493  
464 0 0 6 485 1350 0 0 136 /**
 * Returns true if the model involves a separate cull pass, or false if
 * culling happens implicitly, at the same time as draw.
 */ 1 4 this 3 1493  
465 0 0 4 486 1427 0 0 230 /**
 * Changes the flag that indicates whether the threading model involves a
 * separate cull pass.  This won't change any windows that were already
 * created with this model; this only has an effect on newly-opened windows.
 */ 2 4 this 3 1504  12 cull_sorting 1 1350  
466 0 0 6 487 1350 0 0 110 /**
 * Returns true if the threading model is a single-threaded model, or false if
 * it involves threads.
 */ 1 4 this 3 1493  
467 0 0 6 488 1350 0 0 124 /**
 * Returns true if the threading model is the default, cull-then-draw single-
 * threaded model, or false otherwise.
 */ 1 4 this 3 1493  
468 0 0 4 489 1427 0 0 10 /**
 *
 */ 2 4 this 3 1493  3 out 1 1442  
469 0 0 7 681 1395 840 0 85 /**
 * Returns a pointer to the left DisplayRegion managed by this stereo object.
 */ 1 4 this 3 1484  
470 0 0 7 682 1395 840 0 86 /**
 * Returns a pointer to the right DisplayRegion managed by this stereo object.
 */ 1 4 this 3 1484  
471 0 0 7 686 1424 0 0 0 0 
472 0 0 7 755 1485 1188 0 0 0 
473 0 0 15 755 1485 1188 0 0 1 6 param0 0 1480  
474 0 0 6 688 1327 0 0 22 // Individual queries. 1 4 this 3 1480  
475 0 0 6 689 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
476 0 0 6 690 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
477 0 0 6 691 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
478 0 0 6 692 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
479 0 0 6 693 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
480 0 0 6 694 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
481 0 0 6 695 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
482 0 0 6 696 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
483 0 0 6 697 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
484 0 0 6 698 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
485 0 0 6 699 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
486 0 0 6 700 1327 0 0 110 /**
 * If coverage samples are specified, and there is hardware support, we use
 * coverage multisampling.
 */ 1 4 this 3 1480  
487 0 0 6 701 1327 0 0 10 /**
 *
 */ 1 4 this 3 1480  
488 0 0 6 702 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
489 0 0 6 703 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
490 0 0 6 704 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
491 0 0 6 705 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
492 0 0 6 706 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
493 0 0 6 707 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
494 0 0 6 708 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
495 0 0 6 709 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
496 0 0 4 710 1427 0 0 24 // Individual assigners. 2 4 this 3 1485  1 n 1 1327  
497 0 0 4 711 1427 0 0 317 /**
 * Sets the number of requested color bits as a single number that represents
 * the sum of the individual numbers of red, green and blue bits.  Panda won't
 * care how the individual bits are divided up.
 *
 * See also set_rgba_bits, which allows you to specify requirements for the
 * individual components.
 */ 2 4 this 3 1485  1 n 1 1327  
498 0 0 4 712 1427 0 0 91 /**
 * Convenience method for setting the red, green, blue and alpha bits in one
 * go.
 */ 5 4 this 3 1485  1 r 1 1327  1 g 1 1327  1 b 1 1327  1 a 1 1327  
499 0 0 4 713 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
500 0 0 4 714 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
501 0 0 4 715 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
502 0 0 4 716 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
503 0 0 4 717 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
504 0 0 4 718 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
505 0 0 4 719 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
506 0 0 4 720 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
507 0 0 4 721 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
508 0 0 4 722 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
509 0 0 4 723 1427 0 0 109 /**
 * If coverage samples are specified, and there is hardware support, we use
 * coverage multisampling
 */ 2 4 this 3 1485  1 n 1 1327  
510 0 0 4 724 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1327  
511 0 0 4 725 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
512 0 0 4 726 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
513 0 0 4 727 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
514 0 0 4 728 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
515 0 0 4 729 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
516 0 0 4 730 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
517 0 0 4 731 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
518 0 0 4 732 1427 0 0 10 /**
 *
 */ 2 4 this 3 1485  1 n 1 1350  
519 0 0 6 756 1480 0 0 133 /**
 * Returns a FrameBufferProperties structure with all of the default values
 * filled in according to the user's config file.
 */ 0 
520 0 0 6 757 1350 0 0 0 2 4 this 3 1480  5 other 1 1480  
521 0 0 6 758 1350 0 0 0 2 4 this 3 1480  5 other 1 1480  
522 0 0 4 759 1427 0 0 143 /**
 * Unsets all properties that have been specified so far, and resets the
 * FrameBufferProperties structure to its initial empty state.
 */ 1 4 this 3 1485  
523 0 0 4 760 1427 0 0 51 /**
 * Marks all bits as having been specified.
 */ 1 4 this 3 1485  
524 0 0 6 761 1350 0 0 157 /**
 * Returns true if this set of properties makes strictly greater or equal
 * demands of the framebuffer than the other set of framebuffer properties.
 */ 2 4 this 3 1480  5 other 1 1480  
525 0 0 4 762 1427 0 0 123 /**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */ 2 4 this 3 1485  5 other 1 1480  
526 0 0 4 763 1427 0 0 45 /**
 * Generates a string representation.
 */ 2 4 this 3 1480  3 out 1 1442  
527 0 0 4 764 1427 0 0 132 /**
 * If any of the depth, color, alpha, accum, or stencil properties is set to
 * more than one, then they are reduced to one.
 */ 1 4 this 3 1485  
528 0 0 6 765 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
529 0 0 6 766 1350 0 0 10 /**
 *
 */ 1 4 this 3 1480  
530 0 0 6 767 1327 0 0 914 /**
 * Assumes that these properties are a description of a window.
 *
 * Measures how well this window satisfies a specified set of requirements.  A
 * higher quality number means that more requirements were satisfied.  A
 * quality of zero means that the window is unsuitable.
 *
 * The routine deducts a lot if the window fails to provide a requested
 * feature.  It deducts less if the window provides a feature, but at a
 * degraded level of functionality (ie, the user asks for rgba8, color, but
 * the window only provides rgba4).  The routine also deducts a small amount
 * for unnecessary features.  For example, if the window has an accumulation
 * buffer when one is not requested will reduce quality slightly.  Maximum
 * quality is obtained when the window exactly matches the request.
 *
 * If you want to know whether the window satisfies all of the requirements,
 * use the "subsumes" function.
 */ 2 4 this 3 1480  4 reqs 1 1480  
531 0 0 6 768 1350 0 0 79 /**
 * Returns true if any properties have been specified, false otherwise.
 */ 1 4 this 3 1480  
532 0 0 6 769 1350 0 0 181 /**
 * Returns true if the properties are extremely basic.  The following count as
 * basic: rgb or rgba, depth.  If anything else is specified, the properties
 * are non-basic.
 */ 1 4 this 3 1480  
533 0 0 6 770 1327 0 0 83 /**
 * Converts the aux bitplanes of the framebuffer into a RenderBuffer::Type.
 */ 1 4 this 3 1480  
534 0 0 6 771 1327 0 0 90 /**
 * Converts the non-aux bitplanes of the framebuffer into a
 * RenderBuffer::Type.
 */ 1 4 this 3 1480  
535 0 0 6 772 1350 0 0 165 /**
 * Validates that the properties represent the desired kind of renderer
 * (hardware or software).  If not, prints out an error message and returns
 * false.
 */ 3 4 this 3 1480  5 props 1 1480  8 renderer 1 1428  
536 0 0 6 773 1350 0 0 295 /**
 * Sets the texture up for render-to-texture matching these framebuffer
 * properties.
 *
 * Returns true if there was a format that had enough bits, false otherwise.
 * Of course, this is no guarantee that a particular graphics back-end
 * supports rendering to textures of that format.
 */ 2 4 this 3 1480  3 tex 1 1374  
537 0 0 6 774 1350 0 0 295 /**
 * Sets the texture up for render-to-texture matching these framebuffer
 * properties.
 *
 * Returns true if there was a format that had enough bits, false otherwise.
 * Of course, this is no guarantee that a particular graphics back-end
 * supports rendering to textures of that format.
 */ 2 4 this 3 1480  3 tex 1 1374  
538 0 0 4 778 1427 0 0 116 // The following interface is for the various kinds of GraphicsWindows to
// record the data incoming on the device. 3 4 this 3 1512  6 button 1 1513  4 time 5 1515  
539 0 0 4 779 1427 0 0 189 /**
 * Records that the indicated button was depressed earlier, and we only just
 * detected the event after the fact.  This is mainly useful for tracking the
 * state of modifier keys.
 */ 3 4 this 3 1512  6 button 1 1513  4 time 5 1515  
540 0 0 4 780 1427 0 0 63 /**
 * Records that the indicated button has been released.
 */ 3 4 this 3 1512  6 button 1 1513  4 time 5 1515  
541 0 0 4 781 1427 0 0 67 /**
 * Records that the indicated keystroke has been generated.
 */ 3 4 this 3 1512  7 keycode 1 1327  4 time 5 1515  
542 0 0 4 782 1427 0 0 196 /**
 * Records that the indicated candidate string has been highlighted.  This is
 * used to implement IME support for typing in international languages,
 * especially Chinese/Japanese/Korean.
 */ 5 4 this 3 1512  16 candidate_string 1 1428  15 highlight_start 1 1441  13 highlight_end 1 1441  10 cursor_pos 1 1441  
543 0 0 4 783 1427 0 0 356 /**
 * This should be called when the window focus is lost, so that we may miss
 * upcoming button events (especially "up" events) for the next period of
 * time.  It generates keyboard and mouse "up" events for those buttons that
 * we previously sent unpaired "down" events, so that the Panda application
 * will believe all buttons are now released.
 */ 2 4 this 3 1512  4 time 5 1515  
544 0 0 4 784 1427 0 0 64 /**
 * Records that the indicated button has been depressed.
 */ 3 4 this 3 1512  6 button 1 1513  4 time 5 1515  
545 0 0 4 785 1427 0 0 63 /**
 * Records that the indicated button has been released.
 */ 3 4 this 3 1512  6 button 1 1513  4 time 5 1515  
546 0 0 7 786 1519 0 0 140 /**
 * Returns the PointerData associated with the input device's pointer.  This
 * only makes sense if has_pointer() also returns true.
 */ 1 4 this 3 1517  
547 0 0 4 787 1427 0 0 148 /**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer is within the window, at the given pixel coordinates.
 */ 4 4 this 3 1512  1 x 1 1515  1 y 1 1515  4 time 5 1515  
548 0 0 4 788 1427 0 0 126 /**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer is no longer within the window.
 */ 2 4 this 3 1512  4 time 5 1515  
549 0 0 4 789 1427 0 0 112 /**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer data has changed.
 */ 3 4 this 3 1512  4 data 1 1519  4 time 5 1515  
550 0 0 4 790 1427 0 0 134 /**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer has moved by the given relative amount.
 */ 4 4 this 3 1512  1 x 1 1515  1 y 1 1515  4 time 5 1515  
551 0 0 4 791 1427 0 0 112 /**
 * To be called by a particular kind of GraphicsWindow to indicate that the
 * pointer no longer exists.
 */ 2 4 this 3 1512  2 id 1 1327  
552 0 0 7 792 1424 0 0 0 0 
553 0 0 15 800 1522 1210 0 0 1 6 param0 0 1520  
554 0 0 6 796 1327 0 0 0 1 4 this 3 1522  
555 0 0 6 797 1327 0 0 0 1 4 this 3 1522  
556 0 0 6 798 1327 0 0 0 1 4 this 3 1522  
557 0 0 6 799 1327 0 0 0 1 4 this 3 1522  
558 0 0 6 804 1525 0 0 51 /**
 * Returns the Windows proc hwnd parameter.
 */ 1 4 this 3 1523  
559 0 0 6 805 1327 0 0 50 /**
 * Returns the Windows proc msg parameter.
 */ 1 4 this 3 1523  
560 0 0 6 806 1327 0 0 53 /**
 * Returns the Windows proc wparam parameter.
 */ 1 4 this 3 1523  
561 0 0 6 807 1327 0 0 53 /**
 * Returns the Windows proc lparam parameter.
 */ 1 4 this 3 1523  
562 0 0 6 808 1350 0 0 57 /**
 * Returns whether the event is a touch event.
 *
 */ 1 4 this 3 1526  
563 0 0 6 809 1327 0 0 66 /**
 * Returns the current number of touches on the window.
 *
 */ 1 4 this 3 1526  
564 0 0 7 810 1522 1210 0 74 /**
 * Returns the TouchInfo object describing the specified touch.
 *
 */ 2 4 this 3 1526  5 index 1 1327  
565 0 0 7 811 1424 0 0 0 0 
566 0 0 7 814 1450 0 0 56 /**
 * Returns the current properties of the window.
 */ 1 4 this 3 1527  
567 0 0 7 815 1450 0 0 185 /**
 * Returns the properties of the window that are currently requested.  These
 * properties will be applied to the window (if valid) at the next execution
 * of process_events().
 */ 1 4 this 3 1527  
568 0 0 4 816 1427 0 0 102 /**
 * Empties the set of failed properties that will be returned by
 * get_rejected_properties().
 */ 1 4 this 3 1529  
569 0 0 7 817 1449 756 0 276 /**
 * Returns the set of properties that have recently been requested, but could
 * not be applied to the window for some reason.  This set of properties will
 * remain unchanged until they are changed by a new failed request, or
 * clear_rejected_properties() is called.
 */ 1 4 this 3 1527  
570 0 0 4 818 1427 0 0 307 /**
 * Requests a property change on the window.  For example, use this method to
 * request a window change size or minimize or something.
 *
 * The change is not made immediately; rather, the request is saved and will
 * be applied the next time the window task is run (probably at the next
 * frame).
 */ 2 4 this 3 1529  20 requested_properties 1 1450  
571 0 0 6 819 1350 0 0 327 /**
 * Returns true if the window has not yet been opened, or has been fully
 * closed, false if it is open.  The window is not opened immediately after
 * GraphicsEngine::make_output() is called; nor is it closed immediately after
 * GraphicsEngine::remove_window() is called.  Either operation may take a
 * frame or two.
 */ 1 4 this 3 1527  
572 0 0 6 820 1350 0 0 97 /**
 * Returns true if the window has been opened as a fullscreen window, false
 * otherwise.
 */ 1 4 this 3 1527  
573 0 0 4 825 1427 0 0 320 /**
 * Changes the name of the event that is generated when this window is
 * modified externally, e.g.  to be resized or closed by the user.
 *
 * By default, all windows have the same window event unless they are
 * explicitly changed.  When the event is generated, it includes one
 * parameter: the window itself.
 */ 2 4 this 3 1529  12 window_event 1 1428  
574 0 0 6 826 1428 0 0 173 /**
 * Returns the name of the event that is generated when this window is
 * modified externally, e.g.  to be resized or closed by the user.  See
 * set_window_event().
 */ 1 4 this 3 1527  
575 0 0 4 828 1427 0 0 914 /**
 * Sets the event that is triggered when the user requests to close the
 * window, e.g.  via alt-F4, or clicking on the close box.
 *
 * The default for each window is for this event to be the empty string, which
 * means the window-close request is handled immediately by Panda (and the
 * window will be closed without the app getting a chance to intervene).  If
 * you set this to a nonempty string, then the window is not closed, but
 * instead the event is thrown.  It is then up to the app to respond
 * appropriately, for instance by presenting an "are you sure?"  dialog box,
 * and eventually calling close_window() when the user is sure.
 *
 * It is considered poor form to set this string and then not handle the
 * event.  This can frustrate the user by making it difficult for him to
 * cleanly shut down the application (and may force the user to hard-kill the
 * app, or reboot the machine).
 */ 2 4 this 3 1529  19 close_request_event 1 1428  
576 0 0 6 829 1428 0 0 225 /**
 * Returns the name of the event set via set_close_request_event().  If this
 * string is nonempty, then when the user requests to close window, this event
 * will be generated instead.  See set_close_request_event().
 */ 1 4 this 3 1527  
577 0 0 4 831 1427 0 0 536 /**
 * If this flag is false, the window is redrawn only after it has received a
 * recent "unexpose" or "draw" event from the underlying windowing system.  If
 * this flag is true, the window is redrawn every frame regardless.  Setting
 * this false may prevent the window from redrawing unnecessarily when it is
 * hidden, and may play nicer with other windows on the desktop, but may
 * adversely affect frame rate even when the window is fully visible; setting
 * it true will ensure that the window contents are always current.
 */ 2 4 this 3 1529  14 unexposed_draw 1 1350  
578 0 0 6 832 1350 0 0 36 /**
 * See set_unexposed_draw().
 */ 1 4 this 3 1527  
579 0 0 7 834 1352 0 0 329 /**
 * Returns the WindowHandle corresponding to this window on the desktop.  This
 * is mainly useful for communicating with external libraries.  Use
 * window_handle->get_os_handle()->get_handle(), or
 * window_handle->get_string_handle(), to get the actual OS-specific window
 * handle object, whatever type that might be.
 */ 1 4 this 3 1527  
580 0 0 6 836 1327 0 0 291 /**
 * Returns the number of separate input devices associated with the window.
 * Typically, a window will have exactly one input device: the keyboard/mouse
 * pair.  However, some windows may have no input devices, and others may add
 * additional devices, for instance for a joystick.
 */ 1 4 this 3 1527  
581 0 0 7 837 1530 0 0 152 /**
 * Returns the nth input device associated with the window.  Typically, a
 * window will have exactly one input device: the keyboard/mouse pair.
 */ 2 4 this 3 1527  1 i 1 1327  
582 0 0 6 838 1428 0 0 52 /**
 * Returns the name of the nth input device.
 */ 2 4 this 3 1527  6 device 1 1327  
583 0 0 6 841 1350 0 0 119 /**
 * Returns true if the nth input device has a screen-space pointer (for
 * instance, a mouse), false otherwise.
 */ 2 4 this 3 1527  6 device 1 1327  
584 0 0 6 842 1350 0 0 80 /**
 * Returns true if the nth input device has a keyboard, false otherwise.
 */ 2 4 this 3 1527  6 device 1 1327  
585 0 0 7 843 1532 0 0 101 /**
 * Returns a ButtonMap containing the association between raw buttons and
 * virtual buttons.
 */ 1 4 this 3 1527  
586 0 0 4 844 1427 0 0 52 /**
 * Turn on the generation of pointer events.
 */ 2 4 this 3 1529  6 device 1 1327  
587 0 0 4 845 1427 0 0 53 /**
 * Turn off the generation of pointer events.
 */ 2 4 this 3 1529  6 device 1 1327  
588 0 0 7 846 1534 0 0 206 /**
 * Returns the MouseData associated with the nth input device's pointer.
 * Using this to access raw mice (with an index other than 0) is deprecated,
 * see the InputDeviceManager interface instead.
 */ 2 4 this 3 1527  6 device 1 1327  
589 0 0 6 847 1350 0 0 258 /**
 * Forces the pointer to the indicated position within the window, if
 * possible.
 *
 * Returns true if successful, false on failure.  This may fail if the mouse
 * is not currently within the window, or if the API doesn't support this
 * operation.
 */ 4 4 this 3 1529  6 device 1 1327  1 x 1 1327  1 y 1 1327  
590 0 0 4 848 1427 0 0 51 /**
 * Forces the ime window to close if any
 *
 */ 1 4 this 3 1529  
591 0 0 7 849 1424 0 0 0 0 
592 0 0 4 874 1427 0 0 462 /**
 * Sets the CallbackObject that will be notified when this window is polled
 * for window events, including mouse and keyboard events, as well as window
 * resize events and other system-generated events.
 *
 * This callback will receive a CallbackGraphicsWindow::EventsCallbackData.
 *
 * This callback should process any system-generated events, and call
 * data->upcall() to process requested property change requests made via
 * request_properties().
 */ 2 4 this 3 1406  6 object 1 1392  
593 0 0 4 875 1427 0 0 80 /**
 * Removes the callback set by an earlier call to set_events_callback().
 */ 1 4 this 3 1406  
594 0 0 7 876 1392 0 0 67 /**
 * Returns the CallbackObject set by set_events_callback().
 */ 1 4 this 3 1535  
595 0 0 4 877 1427 0 0 672 /**
 * Sets the CallbackObject that will be notified when this window receives a
 * property change request from user code (e.g.  via request_properties).
 *
 * This callback will receive a
 * CallbackGraphicsWindow::PropertiesCallbackData, which provides a
 * get_properties() method that returns a modifiable reference to a
 * WindowsProperties object.  This object will contain only those properties
 * requested by user code.  The callback should handle any of the requests it
 * finds, including and especially set_open(), and remove them from the object
 * when it has handled them.  Any unhandled properties should be left
 * unchanged in the properties object.
 */ 2 4 this 3 1406  6 object 1 1392  
596 0 0 4 878 1427 0 0 84 /**
 * Removes the callback set by an earlier call to set_properties_callback().
 */ 1 4 this 3 1406  
597 0 0 7 879 1392 0 0 71 /**
 * Returns the CallbackObject set by set_properties_callback().
 */ 1 4 this 3 1535  
598 0 0 4 880 1427 0 0 383 /**
 * Sets the CallbackObject that will be notified when this window is invoked
 * (in the draw thread) to render its contents, and/or flip the graphics
 * buffers.
 *
 * This callback will actually serve several different functions.  It
 * receivces a RenderCallbackData, and you can query data->get_callback_type()
 * to return the actual function of each particular callback.
 */ 2 4 this 3 1406  6 object 1 1392  
599 0 0 4 881 1427 0 0 80 /**
 * Removes the callback set by an earlier call to set_render_callback().
 */ 1 4 this 3 1406  
600 0 0 7 882 1392 0 0 67 /**
 * Returns the CallbackObject set by set_render_callback().
 */ 1 4 this 3 1535  
601 0 0 6 883 1327 0 0 121 /**
 * Adds a new input device (mouse) to the window with the indicated name.
 * Returns the index of the new device.
 */ 2 4 this 3 1406  4 name 1 1428  
602 0 0 7 884 1424 0 0 0 0 
603 0 0 7 852 1406 856 0 63 /**
 * Returns the window this callback was triggered from.
 */ 1 4 this 3 1537  
604 0 0 7 855 1424 0 0 0 0 
605 0 0 7 858 1424 0 0 0 0 
606 0 0 6 861 1449 0 0 208 /**
 * Returns the WindowProperties object that this callback should process.  Any
 * properties that are handled should be removed from this object; properties
 * that are unhandled should be left alone.
 */ 1 4 this 3 1539  
607 0 0 7 862 1424 0 0 0 0 
608 0 0 6 865 1409 0 0 172 /**
 * Since the render callback is shared for several functions, this method is
 * needed to indicate which particular function is being invoked with this
 * callback.
 */ 1 4 this 3 1541  
609 0 0 6 866 1359 0 0 215 /**
 * If the callback type (returned by get_callback_type) is RCT_begin_frame or
 * RCT_end_frame, then this method will return the particular frame mode
 * indicating what, precisely, we want to do this frame.
 */ 1 4 this 3 1541  
610 0 0 4 869 1427 0 0 242 /**
 * If the callback type is RCT_begin_frame, this call is available to specify
 * the return value from the begin_frame() call.  If this is true (the
 * default), the frame is rendered normally; if it is false, the frame is
 * omitted.
 */ 2 4 this 3 1543  11 render_flag 1 1350  
611 0 0 6 870 1350 0 0 82 /**
 * Returns the current setting of the render flag.  See set_render_flag().
 */ 1 4 this 3 1541  
612 0 0 7 872 1424 0 0 0 0 
613 0 0 7 904 1544 1284 0 0 0 
614 0 0 15 904 1544 1284 0 0 1 6 param0 0 1431  
615 0 0 6 901 1350 0 0 0 2 4 this 3 1431  5 other 1 1431  
616 0 0 6 902 1350 0 0 0 2 4 this 3 1431  5 other 1 1431  
617 0 0 4 903 1427 0 0 10 /**
 *
 */ 2 4 this 3 1431  3 out 1 1442  
618 0 0 6 907 1547 0 0 114 /**
 * Returns a pointer to the CullHandler, which accepts each object to be added
 * to the list for drawing.
 */ 1 4 this 3 1545  
619 0 0 7 908 1375 0 0 111 /**
 * Returns a pointer to the SceneSetup object, which contains information
 * about the camera and such.
 */ 1 4 this 3 1545  
620 0 0 7 909 1424 0 0 0 0 
621 0 0 7 912 1552 0 0 122 /**
 * Returns a pointer to the CullResult, the list of CullableObjects that
 * should be drawn in this DisplayRegion.
 */ 1 4 this 3 1550  
622 0 0 7 913 1375 0 0 111 /**
 * Returns a pointer to the SceneSetup object, which contains information
 * about the camera and such.
 */ 1 4 this 3 1550  
623 0 0 7 914 1424 0 0 0 0 
624 0 0 7 917 1553 1294 0 10 /**
 *
 */ 0 
625 0 0 15 917 1553 1294 0 0 1 6 param0 0 1554  
626 0 0 4 919 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  13 minimum_width 1 1327  
627 0 0 4 920 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  13 maximum_width 1 1327  
628 0 0 4 921 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  14 minimum_height 1 1327  
629 0 0 4 922 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  14 maximum_height 1 1327  
630 0 0 4 923 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  22 minimum_bits_per_pixel 1 1327  
631 0 0 4 924 1427 0 0 10 /**
 *
 */ 2 4 this 3 1553  22 maximum_bits_per_pixel 1 1327  
632 0 0 4 926 1427 0 0 269 /**
 * This is called by the GraphicsEngine to request that the buffer resize
 * itself.  Although calls to get the size will return the new value, much of
 * the actual resizing work doesn't take place until the next begin_frame.
 * Not all buffers are resizeable.
 */ 3 4 this 3 1556  1 x 1 1327  1 y 1 1327  
633 0 0 7 927 1424 0 0 0 0 
634 0 0 6 929 1327 0 0 121 /**
 * Returns the number of different types of GraphicsPipes that are available
 * to create through this interface.
 */ 1 4 this 3 1557  
635 0 0 7 930 1424 0 0 81 /**
 * Returns the nth type of GraphicsPipe available through this interface.
 */ 2 4 this 3 1557  1 n 1 1327  
636 0 0 4 934 1427 0 0 106 /**
 * Writes a list of the currently known GraphicsPipe types to nout, for the
 * user's information.
 */ 1 4 this 3 1557  
637 0 0 7 935 1368 0 0 190 /**
 * Creates a new GraphicsPipe of the indicated type (or a type more specific
 * than the indicated type, if necessary) and returns it.  Returns NULL if the
 * type cannot be matched.
 */ 2 4 this 3 1559  4 type 1 1424  
638 0 0 7 935 1368 0 0 340 /**
 * Creates a new GraphicsPipe of the indicated type (or a type more specific
 * than the indicated type, if necessary) and returns it.  Returns NULL if the
 * type cannot be matched.
 *
 * If the type is not already defined, this will implicitly load the named
 * module, or if module_name is empty, it will call load_aux_modules().
 */ 3 4 this 3 1559  9 type_name 1 1428  11 module_name 5 1428  
639 0 0 7 936 1368 0 0 173 /**
 * Returns a new GraphicsPipe of a type defined by the indicated module.
 * Returns NULL if the module is not found or does not properly recommend a
 * GraphicsPipe.
 */ 2 4 this 3 1559  11 module_name 1 1428  
640 0 0 7 937 1368 0 0 170 /**
 * Creates a new GraphicsPipe of some arbitrary type.  The user may specify a
 * preference using the Configrc file; otherwise, one will be chosen
 * arbitrarily.
 */ 1 4 this 3 1559  
641 0 0 6 938 1327 0 0 196 /**
 * Returns the number of display modules that are still to be loaded.  If this
 * is nonzero, then calling load_aux_modules() will likely increase the number
 * of GraphicsPipes available.
 */ 1 4 this 3 1557  
642 0 0 4 939 1427 0 0 132 /**
 * Loads all the modules named in the aux-display Configrc variable, making as
 * many graphics pipes as possible available.
 */ 1 4 this 3 1559  
643 0 0 6 940 1559 0 0 76 /**
 * Returns a pointer to the one global GraphicsPipeSelection object.
 */ 0 
644 0 0 7 943 1560 1315 0 10 /**
 *
 */ 3 6 window 1 1529  6 device 1 1327  4 name 1 1428  
645 0 0 15 943 1560 1315 0 0 1 6 param0 0 1561  
646 0 0 4 944 1427 0 0 134 /**
 * Redirects the class to get the data from the mouse and keyboard associated
 * with a different window and/or device number.
 */ 3 4 this 3 1560  6 window 1 1529  6 device 1 1327  
647 0 0 7 945 1424 0 0 0 0 
648 0 0 7 948 1352 0 0 306 /**
 * Constructs a new WindowHandle with an int value, which is understood to be
 * either an HWND or a Window, cast to int.  This method exists for the
 * convenience of Python, which likes to deal with ints; C++ code should use
 * one of the more specific make_x11() or make_win32() methods instead.
 */ 1 6 window 1 1441  
649 0 0 7 949 1352 0 0 314 /**
 * Constructs a new WindowHandle that references a SubprocessWindowBuffer read
 * in another process, with the named pipe filename that it uses for
 * communication.
 *
 * This is (at present, and maybe always) useful only on the OS X platform,
 * where parenting child windows is particularly problematic.
 */ 1 8 filename 1 1459  
650 0 0 7 950 1424 0 0 0 0 
651 0 0 4 953 1427 0 0 269 /**
 * This is called by the GraphicsEngine to request that the buffer resize
 * itself.  Although calls to get the size will return the new value, much of
 * the actual resizing work doesn't take place until the next begin_frame.
 * Not all buffers are resizeable.
 */ 3 4 this 3 1563  1 x 1 1327  1 y 1 1327  
652 0 0 7 954 1424 0 0 0 0 
242
1322 14 GraphicsDevice 0 75777 14 GraphicsDevice 14 GraphicsDevice 0 0 0 0 0 0 2 653 654 0 0 1 0 1323 0 0 0 0 262
/**
 * An abstract device object that is part of Graphics Pipe.  This device is
 * set to NULL for OpenGL. But DirectX uses it to take control of multiple
 * windows under single device or multiple devices (i.e.  more than one
 * adapters in the machine).
 *
 */

1323 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1324 12 GraphicsPipe 0 75777 12 GraphicsPipe 12 GraphicsPipe 0 0 0 0 0 5 1564 1565 1566 1567 1568 10 655 656 657 658 659 660 661 716 717 718 0 0 1 0 1323 0 0 0 2 1325 1326 743
/**
 * An object to create GraphicsOutputs that share a particular 3-D API.
 * Normally, there will only be one GraphicsPipe in an application, although
 * it is possible to have multiple of these at once if there are multiple
 * different API's available in the same machine.
 *
 * Often, the GraphicsPipe corresponds to a physical output device, hence the
 * term "pipe", but this is not necessarily the case.
 *
 * The GraphicsPipe is used by the GraphicsEngine object to create and destroy
 * windows; it keeps ownership of the windows it creates.
 *
 * M. Asad added new/interim functionality where GraphicsPipe now contains a
 * device interface to directx/opengl which will be used to handle multiple
 * windows from same device.
 *
 */

1325 11 OutputTypes 0 794624 25 GraphicsPipe::OutputTypes 25 GraphicsPipe::OutputTypes 1324 0 0 0 0 0 0 0 0 0 4 9 OT_window 23 GraphicsPipe::OT_window 0
1 20 OT_fullscreen_window 34 GraphicsPipe::OT_fullscreen_window 0
2 9 OT_buffer 23 GraphicsPipe::OT_buffer 0
4 17 OT_texture_buffer 31 GraphicsPipe::OT_texture_buffer 0
8 0 0

1326 19 BufferCreationFlags 0 794624 33 GraphicsPipe::BufferCreationFlags 33 GraphicsPipe::BufferCreationFlags 1324 0 0 0 0 0 0 0 0 0 14 18 BF_refuse_parasite 32 GraphicsPipe::BF_refuse_parasite 54
// Flags that control what type of output is returned.
1 19 BF_require_parasite 33 GraphicsPipe::BF_require_parasite 0
2 16 BF_refuse_window 30 GraphicsPipe::BF_refuse_window 0
4 17 BF_require_window 31 GraphicsPipe::BF_require_window 0
8 26 BF_require_callback_window 40 GraphicsPipe::BF_require_callback_window 0
16 17 BF_can_bind_color 31 GraphicsPipe::BF_can_bind_color 53
// Need capability: bind the color bitplane to a tex.
64 17 BF_can_bind_every 31 GraphicsPipe::BF_can_bind_every 48
// Need capability: bind all bitplanes to a tex.
128 13 BF_resizeable 27 GraphicsPipe::BF_resizeable 32
// Buffer should allow set_size.
256 18 BF_size_track_host 32 GraphicsPipe::BF_size_track_host 37
// Buffer should track the host size.
512 17 BF_rtt_cumulative 31 GraphicsPipe::BF_rtt_cumulative 48
// Buffer supports cumulative render-to-texture.
1024 20 BF_fb_props_optional 34 GraphicsPipe::BF_fb_props_optional 40
// FrameBufferProperties can be ignored.
2048 14 BF_size_square 28 GraphicsPipe::BF_size_square 48
// x_size must equal y_size (e.g. for cube maps)
4096 15 BF_size_power_2 29 GraphicsPipe::BF_size_power_2 48
// x_size and y_size must each be a power of two
8192 19 BF_can_bind_layered 33 GraphicsPipe::BF_can_bind_layered 45
// Need capability: support RTM_bind_layered.
16384 0 0

1327 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

1328 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 1329 0 0 0 0 0 0 0 0 0 0

1329 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

1330 20 DisplayInformation * 0 8576 20 DisplayInformation * 20 DisplayInformation * 0 0 1331 0 0 0 0 0 0 0 0 0 0

1331 18 DisplayInformation 0 26625 18 DisplayInformation 18 DisplayInformation 0 0 0 1 663 662 0 52 664 665 666 667 668 669 670 671 672 673 674 675 676 677 678 679 680 681 682 683 684 685 686 687 688 689 690 691 692 693 694 695 696 697 698 699 700 701 702 703 704 705 706 707 708 709 710 711 712 713 714 715 1 1742 0 0 0 1 1332 59
/**
 * This class contains various display information.
 */

1332 14 DetectionState 0 794624 34 DisplayInformation::DetectionState 34 DisplayInformation::DetectionState 1331 0 0 0 0 0 0 0 0 0 5 10 DS_unknown 30 DisplayInformation::DS_unknown 0
0 10 DS_success 30 DisplayInformation::DS_success 0
1 25 DS_direct_3d_create_error 45 DisplayInformation::DS_direct_3d_create_error 0
2 22 DS_create_window_error 42 DisplayInformation::DS_create_window_error 0
3 22 DS_create_device_error 42 DisplayInformation::DS_create_device_error 0
4 0 0

1333 6 string 0 2105344 11 std::string 11 std::string 0 0 1334 0 0 0 0 0 0 0 0 0 0

1334 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

1335 14 DrawableRegion 0 26625 14 DrawableRegion 14 DrawableRegion 0 0 0 0 719 5 1569 1570 1571 1572 1573 23 720 721 722 723 724 725 726 727 728 729 730 731 732 733 734 735 736 737 738 739 740 741 742 0 0 0 0 1 1336 389
/**
 * This is a base class for GraphicsWindow (actually, GraphicsOutput) and
 * DisplayRegion, both of which are conceptually rectangular regions into
 * which drawing commands may be issued.  Sometimes you want to deal with a
 * single display region, and sometimes you want to deal with the whole window
 * at once, particularly for issuing clear commands and capturing screenshots.
 */

1336 18 RenderTexturePlane 0 794624 34 DrawableRegion::RenderTexturePlane 34 DrawableRegion::RenderTexturePlane 1335 0 0 0 0 0 0 0 0 0 17 11 RTP_stencil 27 DrawableRegion::RTP_stencil 0
0 17 RTP_depth_stencil 33 DrawableRegion::RTP_depth_stencil 0
1 9 RTP_color 25 DrawableRegion::RTP_color 0
2 14 RTP_aux_rgba_0 30 DrawableRegion::RTP_aux_rgba_0 0
3 14 RTP_aux_rgba_1 30 DrawableRegion::RTP_aux_rgba_1 0
4 14 RTP_aux_rgba_2 30 DrawableRegion::RTP_aux_rgba_2 0
5 14 RTP_aux_rgba_3 30 DrawableRegion::RTP_aux_rgba_3 0
6 15 RTP_aux_hrgba_0 31 DrawableRegion::RTP_aux_hrgba_0 0
7 15 RTP_aux_hrgba_1 31 DrawableRegion::RTP_aux_hrgba_1 0
8 15 RTP_aux_hrgba_2 31 DrawableRegion::RTP_aux_hrgba_2 0
9 15 RTP_aux_hrgba_3 31 DrawableRegion::RTP_aux_hrgba_3 0
10 15 RTP_aux_float_0 31 DrawableRegion::RTP_aux_float_0 0
11 15 RTP_aux_float_1 31 DrawableRegion::RTP_aux_float_1 0
12 15 RTP_aux_float_2 31 DrawableRegion::RTP_aux_float_2 0
13 15 RTP_aux_float_3 31 DrawableRegion::RTP_aux_float_3 0
14 9 RTP_depth 25 DrawableRegion::RTP_depth 0
15 9 RTP_COUNT 25 DrawableRegion::RTP_COUNT 0
16 0 167
// It seems awkward to have this type, and also RenderBuffer::Type.
// However, the fact that RenderBuffer::Type is a bitmask makes it awfully
// awkward to work with.

1337 12 LColor const 0 8832 12 LColor const 12 LColor const 0 0 1338 0 0 0 0 0 0 0 0 0 0

1338 6 LColor 0 2105344 6 LColor 6 LColor 0 0 1339 0 0 0 0 0 0 0 0 0 0

1339 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 1340 0 0 0 0 0 0 0 0 0 0

1340 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1341 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

1342 12 WindowHandle 0 75777 12 WindowHandle 12 WindowHandle 0 0 0 1 743 0 1 1574 6 744 745 750 751 752 753 0 0 1 0 1323 0 0 0 1 1344 519
/**
 * This object represents a window on the desktop, not necessarily a Panda
 * window.  This structure can be assigned to a WindowProperties to indicate a
 * parent window.
 *
 * It also has callbacks so the Panda window can communicate with its parent
 * window, which is particularly important when running embedded in a browser.
 *
 * To create a WindowHandle, you would usually call one of the
 * NativeWindowHandle::make_*() methods, depending on the kind of native
 * window handle object you already have.
 */

1343 10 OSHandle * 0 8576 24 WindowHandle::OSHandle * 24 WindowHandle::OSHandle * 0 0 1344 0 0 0 0 0 0 0 0 0 0

1344 8 OSHandle 0 337921 22 WindowHandle::OSHandle 22 WindowHandle::OSHandle 1342 0 0 1 749 0 0 3 746 747 748 0 0 1 0 1323 0 0 0 0 151
// This internal pointer within WindowHandle stores the actual OS-specific
// window handle type, whatever type that is.  It is subclassed for each OS.

1345 16 WindowProperties 0 26625 16 WindowProperties 16 WindowProperties 0 0 0 1 754 756 17 1575 1576 1577 1578 1579 1580 1581 1582 1583 1584 1585 1586 1587 1588 1589 1590 1591 80 755 757 758 759 760 761 762 763 764 765 766 767 768 769 770 771 772 773 774 775 776 777 778 779 780 781 782 783 784 785 786 787 788 789 790 791 792 793 794 795 796 797 798 799 800 801 802 803 804 805 806 807 808 809 810 811 812 813 814 815 816 817 818 819 820 821 822 823 824 825 826 827 828 829 830 831 832 833 834 835 0 0 0 0 2 1346 1347 213
/**
 * A container for the various kinds of properties we might ask to have on a
 * graphics window before we open it.  This also serves to hold the current
 * properties for a window after it has been opened.
 */

1346 6 ZOrder 0 794624 24 WindowProperties::ZOrder 24 WindowProperties::ZOrder 1345 0 0 0 0 0 0 0 0 0 3 8 Z_bottom 26 WindowProperties::Z_bottom 0
0 8 Z_normal 26 WindowProperties::Z_normal 0
1 5 Z_top 23 WindowProperties::Z_top 0
2 0 0

1347 9 MouseMode 0 794624 27 WindowProperties::MouseMode 27 WindowProperties::MouseMode 1345 0 0 0 0 0 0 0 0 0 3 10 M_absolute 28 WindowProperties::M_absolute 0
0 10 M_relative 28 WindowProperties::M_relative 0
1 10 M_confined 28 WindowProperties::M_confined 0
2 0 0

1348 8 LPoint2i 0 2048 8 LPoint2i 8 LPoint2i 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

1349 9 LVector2i 0 2048 9 LVector2i 9 LVector2i 0 0 0 0 0 0 0 0 0 0 0 0 49
/**
 * This is a two-component vector offset.
 */

1350 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

1351 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

1352 14 WindowHandle * 0 8576 14 WindowHandle * 14 WindowHandle * 0 0 1342 0 0 0 0 0 0 0 0 0 0

1353 13 DisplayRegion 0 26625 13 DisplayRegion 13 DisplayRegion 0 0 0 0 840 18 1592 1593 1684 1685 1686 1687 1688 1689 1690 1691 1692 1693 1694 1695 1696 1697 1698 1699 51 841 842 843 844 845 846 847 848 849 850 851 1080 1081 1082 1083 1084 1085 1086 1087 1088 1089 1090 1091 1092 1093 1094 1095 1096 1097 1098 1099 1100 1101 1102 1103 1104 1105 1106 1107 1108 1109 1110 1111 1112 1113 1114 1115 1116 1117 1118 1119 0 0 2 3 1323 836 837 3 1335 838 839 0 0 399
/**
 * A rectangular subregion within a window for rendering into.  Typically,
 * there is one DisplayRegion that covers the whole window, but you may also
 * create smaller DisplayRegions for having different regions within the
 * window that represent different scenes.  You may also stack up
 * DisplayRegions like panes of glass, usually for layering 2-d interfaces on
 * top of a 3-d scene.
 */

1354 9 LVecBase4 0 2105344 9 LVecBase4 9 LVecBase4 0 0 1340 0 0 0 0 0 0 0 0 0 0

1355 16 GraphicsOutput * 0 8576 16 GraphicsOutput * 16 GraphicsOutput * 0 0 1356 0 0 0 0 0 0 0 0 0 0

1356 14 GraphicsOutput 0 26625 14 GraphicsOutput 14 GraphicsOutput 0 0 0 0 856 17 1594 1662 1663 1670 1671 1672 1673 1674 1675 1676 1677 1678 1679 1680 1681 1682 1683 77 857 858 859 860 1007 1008 1009 1010 1011 1012 1013 1014 1015 1016 1017 1018 1019 1020 1021 1022 1023 1024 1025 1026 1027 1028 1029 1030 1031 1032 1033 1034 1035 1036 1037 1038 1039 1040 1041 1042 1043 1044 1045 1046 1047 1048 1049 1050 1051 1052 1053 1054 1055 1056 1057 1058 1059 1060 1061 1062 1063 1064 1065 1066 1067 1068 1069 1070 1071 1072 1073 1074 1075 1076 1077 1078 1079 2 1744 1745 0 2 3 1357 852 853 3 1335 854 855 0 2 1358 1359 727
/**
 * This is a base class for the various different classes that represent the
 * result of a frame of rendering.  The most common kind of GraphicsOutput is
 * a GraphicsWindow, which is a real-time window on the desktop, but another
 * example is GraphicsBuffer, which is an offscreen buffer.
 *
 * The actual rendering, and anything associated with the graphics context
 * itself, is managed by the associated GraphicsStateGuardian (which might
 * output to multiple GraphicsOutput objects).
 *
 * GraphicsOutputs are not actually writable to bam files, of course, but they
 * may be passed as event parameters, so they inherit from
 * TypedWritableReferenceCount instead of TypedReferenceCount for that
 * convenience.
 */

1357 18 GraphicsOutputBase 0 2048 18 GraphicsOutputBase 18 GraphicsOutputBase 0 0 0 0 0 0 0 0 0 0 0 0 80
/**
 * An abstract base class for GraphicsOutput, for all the usual reasons.
 */

1358 17 RenderTextureMode 0 794624 33 GraphicsOutput::RenderTextureMode 33 GraphicsOutput::RenderTextureMode 1356 0 0 0 0 0 0 0 0 0 7 8 RTM_none 24 GraphicsOutput::RTM_none 0
0 16 RTM_bind_or_copy 32 GraphicsOutput::RTM_bind_or_copy 104
// Try to render to the texture directly, but if that is not possible,
// fall back to RTM_copy_texture.
1 16 RTM_copy_texture 32 GraphicsOutput::RTM_copy_texture 61
// Copy the image from the buffer to the texture every frame.
2 12 RTM_copy_ram 28 GraphicsOutput::RTM_copy_ram 60
// Copy the image from the buffer to system RAM every frame.
3 26 RTM_triggered_copy_texture 42 GraphicsOutput::RTM_triggered_copy_texture 83
// Copy the image from the buffer to the texture after a call to
// trigger_copy().
4 22 RTM_triggered_copy_ram 38 GraphicsOutput::RTM_triggered_copy_ram 82
// Copy the image from the buffer to system RAM after a call to
// trigger_copy().
5 16 RTM_bind_layered 32 GraphicsOutput::RTM_bind_layered 169
// Render directly to a layered texture, such as a cube map, 3D texture or
// 2D texture array.  The layer that is being rendered to is selected by a
// geometry shader.
6 0 0

1359 9 FrameMode 0 794624 25 GraphicsOutput::FrameMode 25 GraphicsOutput::FrameMode 1356 0 0 0 0 0 0 0 0 0 3 9 FM_render 25 GraphicsOutput::FM_render 28
// We are rendering a frame.
0 11 FM_parasite 27 GraphicsOutput::FM_parasite 42
// We are rendering a frame of a parasite.
1 10 FM_refresh 26 GraphicsOutput::FM_refresh 61
// We are just refreshing the display or exposing the window.
2 0 55
// There are many reasons to call begin_frameend_frame.

1360 23 GraphicsStateGuardian * 0 8576 23 GraphicsStateGuardian * 23 GraphicsStateGuardian * 0 0 1361 0 0 0 0 0 0 0 0 0 0

1361 21 GraphicsStateGuardian 0 75777 21 GraphicsStateGuardian 21 GraphicsStateGuardian 0 0 0 0 0 67 1595 1596 1597 1598 1599 1600 1601 1602 1603 1604 1605 1606 1607 1608 1609 1610 1611 1612 1613 1614 1615 1616 1617 1618 1619 1620 1621 1622 1623 1624 1625 1626 1627 1628 1629 1630 1631 1632 1633 1634 1635 1636 1637 1638 1639 1640 1641 1642 1643 1644 1645 1646 1647 1648 1649 1650 1651 1652 1653 1654 1655 1656 1657 1658 1659 1660 1661 94 861 862 863 864 865 866 867 868 869 870 871 872 875 876 877 878 879 880 881 882 883 884 885 886 887 888 889 890 891 892 893 894 895 896 897 898 899 900 901 902 903 904 905 906 907 908 909 910 911 912 913 914 915 916 917 918 919 920 921 922 930 931 932 933 934 935 936 937 938 939 940 941 942 943 944 945 946 947 948 949 950 951 952 953 954 955 956 957 958 959 960 961 962 963 0 0 1 0 1362 0 0 0 1 1363 308
/**
 * Encapsulates all the communication with a particular instance of a given
 * rendering backend.  Tries to guarantee that redundant state-change requests
 * are not issued (hence "state guardian").
 *
 * There will be one of these objects for each different graphics context
 * active in the system.
 */

1362 25 GraphicsStateGuardianBase 0 2048 25 GraphicsStateGuardianBase 25 GraphicsStateGuardianBase 0 0 0 0 0 0 0 0 0 0 0 0 607
/**
 * This is a base class for the GraphicsStateGuardian class, which is itself a
 * base class for the various GSG's for different platforms.  This class
 * contains all the function prototypes to support the double-dispatch of GSG
 * to geoms, transitions, etc.  It lives in a separate class in its own
 * package so we can avoid circular build dependency problems.
 *
 * GraphicsStateGuardians are not actually writable to bam files, of course,
 * but they may be passed as event parameters, so they inherit from
 * TypedWritableReferenceCount instead of TypedReferenceCount for that
 * convenience.
 */

1363 11 ShaderModel 0 794624 34 GraphicsStateGuardian::ShaderModel 34 GraphicsStateGuardian::ShaderModel 1361 0 0 0 0 0 0 0 0 0 8 5 SM_00 28 GraphicsStateGuardian::SM_00 0
0 5 SM_11 28 GraphicsStateGuardian::SM_11 0
1 5 SM_20 28 GraphicsStateGuardian::SM_20 0
2 5 SM_2X 28 GraphicsStateGuardian::SM_2X 0
3 5 SM_30 28 GraphicsStateGuardian::SM_30 0
4 5 SM_40 28 GraphicsStateGuardian::SM_40 0
5 5 SM_50 28 GraphicsStateGuardian::SM_50 0
6 5 SM_51 28 GraphicsStateGuardian::SM_51 0
7 0 0

1364 8 Loader * 0 8576 8 Loader * 8 Loader * 0 0 1365 0 0 0 0 0 0 0 0 0 0

1365 6 Loader 0 2048 6 Loader 6 Loader 0 0 0 0 0 0 0 0 0 0 0 0 503
/**
 * A convenient class for loading models from disk, in bam or egg format (or
 * any of a number of other formats implemented by a LoaderFileType, such as
 * ptloader).
 *
 * This class supports synchronous as well as asynchronous loading.  In
 * asynchronous loading, the model is loaded in the background by a thread,
 * and an event will be generated when the model is available.  If threading
 * is not available, the asynchronous loading interface may be used, but it
 * loads synchronously.
 */

1366 17 ShaderGenerator * 0 8576 17 ShaderGenerator * 17 ShaderGenerator * 0 0 1367 0 0 0 0 0 0 0 0 0 0

1367 15 ShaderGenerator 0 2048 15 ShaderGenerator 15 ShaderGenerator 0 0 0 0 0 0 0 0 0 0 0 0 1069
/**
 * The ShaderGenerator is a device that effectively replaces the classic fixed
 * function pipeline with a 'next-gen' fixed function pipeline.  The next-gen
 * fixed function pipeline supports features like normal mapping, gloss
 * mapping, cartoon lighting, and so forth.  It works by automatically
 * generating a shader from a given RenderState.
 *
 * Currently, there is one ShaderGenerator object per GraphicsStateGuardian.
 * It is our intent that in time, people will write classes that derive from
 * ShaderGenerator but which yield slightly different results.
 *
 * The ShaderGenerator owes its existence to the 'Bamboo Team' at Carnegie
 * Mellon's Entertainment Technology Center.  This is a group of students who,
 * as a semester project, decided that next-gen graphics should be accessible
 * to everyone, even if they don't know shader programming.  The group
 * consisted of:
 *
 * Aaron Lo, Programmer Heegun Lee, Programmer Erin Fernandez, Artist/Tester
 * Joe Grubb, Artist/Tester Ivan Ortega, Technical Artist/Tester
 *
 * Thanks to them!
 *
 */

1368 14 GraphicsPipe * 0 8576 14 GraphicsPipe * 14 GraphicsPipe * 0 0 1324 0 0 0 0 0 0 0 0 0 0

1369 16 CoordinateSystem 0 532480 16 CoordinateSystem 16 CoordinateSystem 0 0 0 0 0 0 0 0 0 0 6 10 CS_default 10 CS_default 210
// The CS_default entry does not refer to a particular coordinate system,
// but rather to the value stored in default_coordinate_system, which in
// turn is loaded from the config variable "coordinate-system".
0 12 CS_zup_right 12 CS_zup_right 21
// Z-Up, Right-handed
1 12 CS_yup_right 12 CS_yup_right 21
// Y-Up, Right-handed
2 11 CS_zup_left 11 CS_zup_left 20
// Z-Up, Left-handed
3 11 CS_yup_left 11 CS_yup_left 20
// Y-Up, Left-handed
4 10 CS_invalid 10 CS_invalid 156
// CS_invalid is not a coordinate system at all.  It can be used in user-
// input processing code to indicate a contradictory coordinate system
// request.
5 0 0

1370 25 PreparedGraphicsObjects * 0 8576 25 PreparedGraphicsObjects * 25 PreparedGraphicsObjects * 0 0 1371 0 0 0 0 0 0 0 0 0 0

1371 23 PreparedGraphicsObjects 0 2048 23 PreparedGraphicsObjects 23 PreparedGraphicsObjects 0 0 0 0 0 0 0 0 0 0 0 0 658
/**
 * A table of objects that are saved within the graphics context for reference
 * by handle later.  Generally, this represents things like OpenGL texture
 * objects or display lists (or their equivalent on other platforms).
 *
 * This object simply records the pointers to the context objects created by
 * the individual GSG's; these context objects will contain enough information
 * to reference or release the actual object stored within the graphics
 * context.
 *
 * These tables may potentially be shared between related graphics contexts,
 * hence their storage here in a separate object rather than as a part of the
 * GraphicsStateGuardian.
 */

1372 12 QualityLevel 0 794624 21 Texture::QualityLevel 21 Texture::QualityLevel 1373 0 0 0 0 0 0 0 0 0 4 10 QL_default 19 Texture::QL_default 37
// according to texture-quality-level
0 10 QL_fastest 19 Texture::QL_fastest 0
1 9 QL_normal 18 Texture::QL_normal 0
2 7 QL_best 16 Texture::QL_best 0
3 0 0

1373 7 Texture 0 2048 7 Texture 7 Texture 0 0 0 0 0 0 0 0 0 0 0 0 688
/**
 * Represents a texture object, which is typically a single 2-d image but may
 * also represent a 1-d or 3-d texture image, or the six 2-d faces of a cube
 * map texture.
 *
 * A texture's image data might be stored in system RAM (see get_ram_image())
 * or its image may be represented in texture memory on one or more
 * GraphicsStateGuardians (see prepare()), or both.  The typical usage pattern
 * is that a texture is loaded from an image file on disk, which copies its
 * image data into system RAM; then the first time the texture is rendered its
 * image data is copied to texture memory (actually, to the graphics API), and
 * the system RAM image is automatically freed.
 */

1374 9 Texture * 0 8576 9 Texture * 9 Texture * 0 0 1373 0 0 0 0 0 0 0 0 0 0

1375 12 SceneSetup * 0 8576 12 SceneSetup * 12 SceneSetup * 0 0 1376 0 0 0 0 0 0 0 0 0 0

1376 10 SceneSetup 0 2048 10 SceneSetup 10 SceneSetup 0 0 0 0 0 0 0 0 0 0 0 0 128
/**
 * This object holds the camera position, etc., and other general setup
 * information for rendering a particular scene.
 */

1377 16 GraphicsEngine * 0 8576 16 GraphicsEngine * 16 GraphicsEngine * 0 0 1378 0 0 0 0 0 0 0 0 0 0

1378 14 GraphicsEngine 0 75777 14 GraphicsEngine 14 GraphicsEngine 0 0 0 1 964 0 6 1664 1665 1666 1667 1668 1669 27 965 966 982 983 984 985 986 987 988 989 990 991 992 993 994 995 996 997 998 999 1000 1001 1002 1003 1004 1005 1006 1 1743 0 1 0 1379 0 0 0 0 498
/**
 * This class is the main interface to controlling the render process.  There
 * is typically only one GraphicsEngine in an application, and it synchronizes
 * rendering to all all of the active windows; although it is possible to have
 * multiple GraphicsEngine objects if multiple synchronicity groups are
 * required.
 *
 * The GraphicsEngine is responsible for managing the various cull and draw
 * threads.  The application simply calls engine->render_frame() and considers
 * it done.
 */

1379 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

1380 22 GraphicsThreadingModel 0 141313 22 GraphicsThreadingModel 22 GraphicsThreadingModel 0 0 0 1 967 981 0 13 968 969 970 971 972 973 974 975 976 977 978 979 980 0 0 0 0 0 115
/**
 * This represents the user's specification of how a particular frame is
 * handled by the various threads.
 */

1381 13 ReMutex const 0 8832 13 ReMutex const 13 ReMutex const 0 0 1382 0 0 0 0 0 0 0 0 0 0

1382 7 ReMutex 0 2048 7 ReMutex 7 ReMutex 0 0 0 0 0 0 0 0 0 0 0 0 0

1383 12 string const 0 8832 17 std::string const 17 std::string const 0 0 1333 0 0 0 0 0 0 0 0 0 0

1384 16 LVecBase2i const 0 8832 16 LVecBase2i const 16 LVecBase2i const 0 0 1385 0 0 0 0 0 0 0 0 0 0

1385 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

1386 26 PointerTo< DisplayRegion > 0 2048 26 PointerTo< DisplayRegion > 26 PointerTo< DisplayRegion > 0 0 0 0 0 0 0 0 0 0 0 0 0

1387 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

1388 13 StereoChannel 0 794624 19 Lens::StereoChannel 19 Lens::StereoChannel 1389 0 0 0 0 0 0 0 0 0 4 7 SC_mono 13 Lens::SC_mono 0
0 7 SC_left 13 Lens::SC_left 0
1 8 SC_right 14 Lens::SC_right 0
2 9 SC_stereo 15 Lens::SC_stereo 24
// == SC_left | SC_right
3 0 0

1389 4 Lens 0 2048 4 Lens 4 Lens 0 0 0 0 0 0 0 0 0 0 0 0 398
/**
 * A base class for any number of different kinds of lenses, linear and
 * otherwise.  Presently, this includes perspective and orthographic lenses.
 *
 * A Lens object is the main part of a Camera node, which defines the
 * fundamental interface to point-of-view for rendering.  Lenses are also used
 * in other contexts, however; for instance, a Spotlight is also defined using
 * a lens.
 */

1390 15 CullTraverser * 0 8576 15 CullTraverser * 15 CullTraverser * 0 0 1391 0 0 0 0 0 0 0 0 0 0

1391 13 CullTraverser 0 2048 13 CullTraverser 13 CullTraverser 0 0 0 0 0 0 0 0 0 0 0 0 279
/**
 * This object performs a depth-first traversal of the scene graph, with
 * optional view-frustum culling, collecting CullState and searching for
 * GeomNodes.  Each renderable Geom encountered is passed along with its
 * associated RenderState to the CullHandler object.
 */

1392 16 CallbackObject * 0 8576 16 CallbackObject * 16 CallbackObject * 0 0 1393 0 0 0 0 0 0 0 0 0 0

1393 14 CallbackObject 0 2048 14 CallbackObject 14 CallbackObject 0 0 0 0 0 0 0 0 0 0 0 0 276
/**
 * This is a generic object that can be assigned to a callback at various
 * points in the rendering process.  This is actually a base class for a
 * handful of specialized callback object types.  You can also subclass it
 * yourself to make your own callback handler.
 */

1394 19 StereoDisplayRegion 0 75777 19 StereoDisplayRegion 19 StereoDisplayRegion 0 0 0 0 840 2 1700 1701 3 1120 1121 1122 0 0 1 0 1353 0 0 0 0 532
/**
 * This is a special DisplayRegion wrapper that actually includes a pair of
 * DisplayRegions internally: the left and right eyes.  The DisplayRegion
 * represented here does not have a physical association with the window, but
 * it pretends it does.  Instead, it maintains a pointer to the left and right
 * DisplayRegions separately.
 *
 * Operations on the StereoDisplayRegion object affect both left and right
 * eyes together.  To access the left or right eyes independently, use
 * get_left_eye() and get_right_eye().
 */

1395 15 DisplayRegion * 0 8576 15 DisplayRegion * 15 DisplayRegion * 0 0 1353 0 0 0 0 0 0 0 0 0 0

1396 21 FrameBufferProperties 0 141313 21 FrameBufferProperties 21 FrameBufferProperties 0 0 0 1 1168 1188 22 1702 1703 1704 1705 1706 1707 1708 1709 1710 1711 1712 1713 1714 1715 1716 1717 1718 1719 1720 1721 1722 1723 64 1123 1124 1125 1126 1127 1128 1129 1130 1131 1132 1133 1134 1135 1136 1137 1138 1139 1140 1141 1142 1143 1144 1145 1146 1147 1148 1149 1150 1151 1152 1153 1154 1155 1156 1157 1158 1159 1160 1161 1162 1163 1164 1165 1166 1167 1169 1170 1171 1172 1173 1174 1175 1176 1177 1178 1179 1180 1181 1182 1183 1184 1185 1186 1187 0 0 0 0 0 132
/**
 * A container for the various kinds of properties we might ask to have on a
 * graphics frameBuffer before we create a GSG.
 */

1397 25 GraphicsWindowInputDevice 0 141313 25 GraphicsWindowInputDevice 25 GraphicsWindowInputDevice 0 0 0 0 1204 0 15 1189 1190 1191 1192 1193 1194 1195 1196 1197 1198 1199 1200 1201 1202 1203 0 0 1 0 1398 0 0 0 0 231
/**
 * This is a virtual input device that represents the keyboard and mouse pair
 * that is associated with a particular window.  It collects mouse and
 * keyboard events from the windowing system while the window is in focus.
 */

1398 11 InputDevice 0 2048 11 InputDevice 11 InputDevice 0 0 0 0 0 0 0 0 0 0 0 0 959
/**
 * This is a structure representing a single input device.  Input devices may
 * have zero or more buttons, pointers, or axes associated with them, and
 * optionally a motion tracker.
 *
 * These devices are brought under a common interface because there is such a
 * large range of devices out there that may support any number of these types
 * of axes, we couldn't even begin to cover them with type-specific
 * subclasses.
 *
 * Use the various has_() and get_num_() methods to determine information about
 * the device capabilities. For instance, has_keyboard() will give an
 * indication that you can receive keystroke events from this device, and
 * get_num_buttons() will tell you that the device may send button events.
 *
 * There is the DeviceType enumeration, however, which will (if known) contain
 * identification of the general category of devices this fits in, such as
 * keyboard, mouse, gamepad, or flight stick.
 *
 * @since 1.10.0
 */

1399 9 TouchInfo 0 141313 9 TouchInfo 9 TouchInfo 0 0 0 1 1209 1210 0 4 1205 1206 1207 1208 0 0 0 0 1 1400 55
/**
 * Stores information for a single touch event.
 */

1400 14 TouchInfoFlags 0 794624 25 TouchInfo::TouchInfoFlags 25 TouchInfo::TouchInfoFlags 1399 0 0 0 0 0 0 0 0 0 3 8 TIF_move 19 TouchInfo::TIF_move 0
1 8 TIF_down 19 TouchInfo::TIF_down 0
2 6 TIF_up 17 TouchInfo::TIF_up 0
4 0 0

1401 30 GraphicsWindowProcCallbackData 0 141313 30 GraphicsWindowProcCallbackData 30 GraphicsWindowProcCallbackData 0 0 0 0 1219 0 8 1211 1212 1213 1214 1215 1216 1217 1218 0 0 1 0 1402 0 0 0 0 188
/**
 * This specialization on CallbackData is passed when the callback is
 * initiated from from an implementation of the GraphicsWindowProc class, such
 * as PythonGraphicsWindowProc.
 */

1402 12 CallbackData 0 2048 12 CallbackData 12 CallbackData 0 0 0 0 0 0 0 0 0 0 0 0 348
/**
 * This is a generic data block that is passed along to a CallbackObject when
 * a callback is made.  It contains data specific to the particular callback
 * type in question.
 *
 * This is actually an abstract base class and contains no data.
 * Specializations of this class will contain the actual data relevant to each
 * callback type.
 */

1403 14 GraphicsWindow 0 75777 14 GraphicsWindow 14 GraphicsWindow 0 0 0 0 856 8 1724 1725 1726 1727 1728 1729 1730 1731 26 1220 1221 1222 1223 1224 1225 1226 1227 1228 1229 1230 1231 1232 1233 1234 1235 1236 1237 1238 1239 1240 1241 1242 1243 1244 1245 2 1746 1747 0 1 0 1356 0 0 0 0 122
/**
 * A window, fullscreen or on a desktop, into which a graphics device sends
 * its output for interactive display.
 */

1404 22 CallbackGraphicsWindow 0 75777 22 CallbackGraphicsWindow 22 CallbackGraphicsWindow 0 0 0 0 856 0 11 1259 1260 1261 1262 1263 1264 1265 1266 1267 1268 1269 0 0 1 0 1403 0 0 0 5 1405 1407 1408 1409 1410 285
/**
 * This special window object doesn't represent a window in its own right, but
 * instead hooks into some third-party API for creating and rendering to
 * windows via callbacks.  This can be used to allow Panda to render into an
 * already-created OpenGL context, for instance.
 */

1405 18 WindowCallbackData 0 403457 42 CallbackGraphicsWindow::WindowCallbackData 42 CallbackGraphicsWindow::WindowCallbackData 1404 0 0 0 1248 1 1732 2 1246 1247 0 0 1 0 1402 0 0 0 0 0

1406 24 CallbackGraphicsWindow * 0 8576 24 CallbackGraphicsWindow * 24 CallbackGraphicsWindow * 0 0 1404 0 0 0 0 0 0 0 0 0 0

1407 18 EventsCallbackData 0 403457 42 CallbackGraphicsWindow::EventsCallbackData 42 CallbackGraphicsWindow::EventsCallbackData 1404 0 0 0 1250 0 1 1249 0 0 1 0 1405 0 0 0 0 0

1408 22 PropertiesCallbackData 0 272385 46 CallbackGraphicsWindow::PropertiesCallbackData 46 CallbackGraphicsWindow::PropertiesCallbackData 1404 0 0 0 0 0 2 1251 1252 0 0 1 0 1405 0 0 0 0 0

1409 18 RenderCallbackType 0 794624 42 CallbackGraphicsWindow::RenderCallbackType 42 CallbackGraphicsWindow::RenderCallbackType 1404 0 0 0 0 0 0 0 0 0 4 15 RCT_begin_frame 39 CallbackGraphicsWindow::RCT_begin_frame 0
0 13 RCT_end_frame 37 CallbackGraphicsWindow::RCT_end_frame 0
1 14 RCT_begin_flip 38 CallbackGraphicsWindow::RCT_begin_flip 0
2 12 RCT_end_flip 36 CallbackGraphicsWindow::RCT_end_flip 0
3 0 0

1410 18 RenderCallbackData 0 403457 42 CallbackGraphicsWindow::RenderCallbackData 42 CallbackGraphicsWindow::RenderCallbackData 1404 0 0 0 1258 3 1733 1734 1735 5 1253 1254 1255 1256 1257 0 0 1 0 1405 0 0 0 0 0

1411 11 DisplayMode 0 140289 11 DisplayMode 11 DisplayMode 0 0 0 1 1283 1284 5 1736 1737 1738 1739 1740 3 1280 1281 1282 0 0 0 0 0 0

1412 29 DisplayRegionCullCallbackData 0 141313 29 DisplayRegionCullCallbackData 29 DisplayRegionCullCallbackData 0 0 0 0 1288 0 3 1285 1286 1287 0 0 1 0 1402 0 0 0 0 136
/**
 * This specialization on CallbackData is passed when the callback is
 * initiated from the cull traversal, for a DisplayRegion.
 */

1413 29 DisplayRegionDrawCallbackData 0 141313 29 DisplayRegionDrawCallbackData 29 DisplayRegionDrawCallbackData 0 0 0 0 1292 0 3 1289 1290 1291 0 0 1 0 1402 0 0 0 0 136
/**
 * This specialization on CallbackData is passed when the callback is
 * initiated from the draw traversal, for a DisplayRegion.
 */

1414 23 DisplaySearchParameters 0 26625 23 DisplaySearchParameters 23 DisplaySearchParameters 0 0 0 1 1293 1294 0 6 1295 1296 1297 1298 1299 1300 0 0 0 0 0 62
/**
 * Parameters used for searching display capabilities.
 */

1415 14 GraphicsBuffer 0 75777 14 GraphicsBuffer 14 GraphicsBuffer 0 0 0 0 856 0 2 1301 1302 0 0 1 0 1356 0 0 0 0 154
/**
 * An offscreen buffer for rendering into.  This is similar in function to a
 * GraphicsWindow, except that the output is not visible to the user.
 */

1416 21 GraphicsPipeSelection 0 43009 21 GraphicsPipeSelection 21 GraphicsPipeSelection 0 0 0 0 0 1 1741 9 1303 1304 1305 1306 1307 1308 1309 1310 1311 1 1748 0 0 0 0 197
/**
 * This maintains a list of GraphicsPipes by type that are available for
 * creation.  Normally there is one default interactive GraphicsPipe, and
 * possibly other types available as well.
 */

1417 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

1418 16 MouseAndKeyboard 0 141313 16 MouseAndKeyboard 16 MouseAndKeyboard 0 0 0 1 1312 1315 0 2 1313 1314 0 0 1 0 1419 0 0 0 0 772
/**
 * Reads the mouse and/or keyboard data sent from a GraphicsWindow, and
 * transmits it down the data graph.
 *
 * The mouse and keyboard devices are bundled together into one device here,
 * because they interrelate so much.  A mouse might be constrained by the
 * holding down of the shift key, for instance, or the clicking of the mouse
 * button might be handled in much the same way as a keyboard key.
 *
 * Mouse data is sent down the data graph as an x,y position as well as the
 * set of buttons currently being held down; keyboard data is sent down as a
 * set of keypress events in an EventDataTransition.  To throw these events to
 * the system, you must attach an EventThrower to the MouseAndKeyboard object;
 * otherwise, the events will be discarded.
 */

1419 8 DataNode 0 2048 8 DataNode 8 DataNode 0 0 0 0 0 0 0 0 0 0 0 0 422
/**
 * The fundamental type of node for the data graph.  The DataNode class is
 * itself primarily intended as an abstract class; it defines no inputs and no
 * outputs.  Most kinds of data nodes will derive from this to specify the
 * inputs and outputs in the constructor.
 *
 * DataNode does not attempt to cycle its data with a PipelineCycler.  The
 * data graph is intended to be used only within a single thread.
 */

1420 18 NativeWindowHandle 0 16918529 18 NativeWindowHandle 18 NativeWindowHandle 0 0 0 0 1319 0 3 1316 1317 1318 0 0 1 0 1342 0 0 0 0 254
/**
 * This subclass of WindowHandle exists to allow simple creation of a
 * WindowHandle of the appropriate type to the current OS.
 *
 * This class exists for name scoping only.  Don't use the constructor
 * directly; use one of the make_* methods.
 */

1421 14 ParasiteBuffer 0 75777 14 ParasiteBuffer 14 ParasiteBuffer 0 0 0 0 856 0 2 1320 1321 0 0 1 0 1356 0 0 0 0 1033
/**
 * This is a special GraphicsOutput type that acts a lot like a
 * GraphicsBuffer, effectively allowing rendering to an offscreen buffer,
 * except it does not create any framebuffer space for itself.  Instead, it
 * renders into the framebuffer owned by some other GraphicsOutput.
 *
 * The x_size and y_size must therefore fit within the bounds of the source
 * GraphicsOutput.
 *
 * Since the framebuffer will be subsequently cleared when the actual owner
 * draws in it later, this only makes sense if we are going to copy the
 * contents of the framebuffer to a texture immediately after we draw it.
 * Thus, has_texture() is implicitly true for a ParasiteBuffer.
 *
 * This class is useful to render offscreen to a texture while preventing the
 * waste of framebuffer memory for API's that are unable to render directly
 * into a texture (and must render into a separate framebuffer first and then
 * copy to texture).  It is also the only way to render to a texture on API's
 * that do not support offscreen rendering.
 */

1422 22 GraphicsDevice const * 0 8576 22 GraphicsDevice const * 22 GraphicsDevice const * 0 0 1423 0 0 0 0 0 0 0 0 0 0

1423 20 GraphicsDevice const 0 8832 20 GraphicsDevice const 20 GraphicsDevice const 0 0 1322 0 0 0 0 0 0 0 0 0 0

1424 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 1417 0 0 0 0 0 0 0 0 0 0

1425 20 GraphicsPipe const * 0 8576 20 GraphicsPipe const * 20 GraphicsPipe const * 0 0 1426 0 0 0 0 0 0 0 0 0 0

1426 18 GraphicsPipe const 0 8832 18 GraphicsPipe const 18 GraphicsPipe const 0 0 1324 0 0 0 0 0 0 0 0 0 0

1427 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

1428 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

1429 26 DisplayInformation const * 0 8576 26 DisplayInformation const * 26 DisplayInformation const * 0 0 1430 0 0 0 0 0 0 0 0 0 0

1430 24 DisplayInformation const 0 8832 24 DisplayInformation const 24 DisplayInformation const 0 0 1331 0 0 0 0 0 0 0 0 0 0

1431 19 DisplayMode const * 0 8576 19 DisplayMode const * 19 DisplayMode const * 0 0 1432 0 0 0 0 0 0 0 0 0 0

1432 17 DisplayMode const 0 8832 17 DisplayMode const 17 DisplayMode const 0 0 1411 0 0 0 0 0 0 0 0 0 0

1433 8 uint64_t 0 2105344 8 uint64_t 8 uint64_t 0 0 1434 0 0 0 0 0 0 0 0 0 0

1434 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

1435 16 DrawableRegion * 0 8576 16 DrawableRegion * 16 DrawableRegion * 0 0 1335 0 0 0 0 0 0 0 0 0 0

1436 22 DrawableRegion const * 0 8576 22 DrawableRegion const * 22 DrawableRegion const * 0 0 1437 0 0 0 0 0 0 0 0 0 0

1437 20 DrawableRegion const 0 8832 20 DrawableRegion const 20 DrawableRegion const 0 0 1335 0 0 0 0 0 0 0 0 0 0

1438 14 LColor const * 0 8576 14 LColor const * 14 LColor const * 0 0 1337 0 0 0 0 0 0 0 0 0 0

1439 20 WindowHandle const * 0 8576 20 WindowHandle const * 20 WindowHandle const * 0 0 1440 0 0 0 0 0 0 0 0 0 0

1440 18 WindowHandle const 0 8832 18 WindowHandle const 18 WindowHandle const 0 0 1342 0 0 0 0 0 0 0 0 0 0

1441 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 1434 0 0 0 0 0 0 0 0 0 0

1442 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 1443 0 0 0 0 0 0 0 0 0 0

1443 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

1444 16 OSHandle const * 0 8576 30 WindowHandle::OSHandle const * 30 WindowHandle::OSHandle const * 0 0 1445 0 0 0 0 0 0 0 0 0 0

1445 14 OSHandle const 0 8832 28 WindowHandle::OSHandle const 28 WindowHandle::OSHandle const 0 0 1344 0 0 0 0 0 0 0 0 0 0

1446 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 1447 0 0 0 0 0 0 0 0 0 0

1447 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 1448 0 0 0 0 0 0 0 0 0 0

1448 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

1449 18 WindowProperties * 0 8576 18 WindowProperties * 18 WindowProperties * 0 0 1345 0 0 0 0 0 0 0 0 0 0

1450 24 WindowProperties const * 0 8576 24 WindowProperties const * 24 WindowProperties const * 0 0 1451 0 0 0 0 0 0 0 0 0 0

1451 22 WindowProperties const 0 8832 22 WindowProperties const 22 WindowProperties const 0 0 1345 0 0 0 0 0 0 0 0 0 0

1452 18 LVecBase2i const * 0 8576 18 LVecBase2i const * 18 LVecBase2i const * 0 0 1384 0 0 0 0 0 0 0 0 0 0

1453 16 LPoint2i const * 0 8576 16 LPoint2i const * 16 LPoint2i const * 0 0 1454 0 0 0 0 0 0 0 0 0 0

1454 14 LPoint2i const 0 8832 14 LPoint2i const 14 LPoint2i const 0 0 1348 0 0 0 0 0 0 0 0 0 0

1455 10 LPoint2i * 0 8576 10 LPoint2i * 10 LPoint2i * 0 0 1348 0 0 0 0 0 0 0 0 0 0

1456 17 LVector2i const * 0 8576 17 LVector2i const * 17 LVector2i const * 0 0 1457 0 0 0 0 0 0 0 0 0 0

1457 15 LVector2i const 0 8832 15 LVector2i const 15 LVector2i const 0 0 1349 0 0 0 0 0 0 0 0 0 0

1458 11 LVector2i * 0 8576 11 LVector2i * 11 LVector2i * 0 0 1349 0 0 0 0 0 0 0 0 0 0

1459 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 1460 0 0 0 0 0 0 0 0 0 0

1460 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 1351 0 0 0 0 0 0 0 0 0 0

1461 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 1351 0 0 0 0 0 0 0 0 0 0

1462 21 DisplayRegion const * 0 8576 21 DisplayRegion const * 21 DisplayRegion const * 0 0 1463 0 0 0 0 0 0 0 0 0 0

1463 19 DisplayRegion const 0 8832 19 DisplayRegion const 19 DisplayRegion const 0 0 1353 0 0 0 0 0 0 0 0 0 0

1464 11 LVecBase4 * 0 8576 11 LVecBase4 * 11 LVecBase4 * 0 0 1354 0 0 0 0 0 0 0 0 0 0

1465 17 LVecBase4 const * 0 8576 17 LVecBase4 const * 17 LVecBase4 const * 0 0 1466 0 0 0 0 0 0 0 0 0 0

1466 15 LVecBase4 const 0 8832 15 LVecBase4 const 15 LVecBase4 const 0 0 1354 0 0 0 0 0 0 0 0 0 0

1467 16 NodePath const * 0 8576 16 NodePath const * 16 NodePath const * 0 0 1468 0 0 0 0 0 0 0 0 0 0

1468 14 NodePath const 0 8832 14 NodePath const 14 NodePath const 0 0 1387 0 0 0 0 0 0 0 0 0 0

1469 8 Thread * 0 8576 8 Thread * 8 Thread * 0 0 1470 0 0 0 0 0 0 0 0 0 0

1470 6 Thread 0 2048 6 Thread 6 Thread 0 0 0 0 0 0 0 0 0 0 0 0 396
/**
 * A thread; that is, a lightweight process.  This is an abstract base class;
 * to use it, you must subclass from it and redefine thread_main().
 *
 * The thread itself will keep a reference count on the Thread object while it
 * is running; when the thread returns from its root function, the Thread
 * object will automatically be destructed if no other pointers are
 * referencing it.
 */

1471 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 1387 0 0 0 0 0 0 0 0 0 0

1472 12 LVecBase2i * 0 8576 12 LVecBase2i * 12 LVecBase2i * 0 0 1385 0 0 0 0 0 0 0 0 0 0

1473 10 PNMImage * 0 8576 10 PNMImage * 10 PNMImage * 0 0 1474 0 0 0 0 0 0 0 0 0 0

1474 8 PNMImage 0 2048 8 PNMImage 8 PNMImage 0 0 0 0 0 0 0 0 0 0 0 0 1516
/**
 * The name of this class derives from the fact that we originally implemented
 * it as a layer on top of the "pnm library", based on netpbm, which was built
 * to implement pbm, pgm, and pbm files, and is the underlying support of a
 * number of public-domain image file converters.  Nowadays we are no longer
 * derived directly from the pnm library, mainly to allow support of C++
 * iostreams instead of the C stdio FILE interface.
 *
 * Conceptually, a PNMImage is a two-dimensional array of xels, which are the
 * PNM-defined generic pixel type.  Each xel may have a red, green, and blue
 * component, or (if the image is grayscale) a gray component.  The image may
 * be read in, the individual xels manipulated, and written out again, or a
 * black image may be constructed from scratch.
 *
 * A PNMImage has a color space and a maxval, the combination of which defines
 * how a floating-point linear color value is encoded as an integer value in
 * memory.  The functions ending in _val operate on encoded colors, whereas
 * the regular ones work with linear floating-point values.  All operations
 * are color space correct unless otherwise specified.
 *
 * The image is of size XSize() by YSize() xels, numbered from top to bottom,
 * left to right, beginning at zero.
 *
 * Files can be specified by filename, or by an iostream pointer.  The
 * filename "-" refers to stdin or stdout.
 *
 * This class is not inherently thread-safe; use it from a single thread or
 * protect access using a mutex.
 */

1475 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

1476 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 1475 0 0 0 0 0 0 0 0 0 0

1477 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 1323 0 0 0 0 0 0 0 0 0 0

1478 22 GraphicsOutput const * 0 8576 22 GraphicsOutput const * 22 GraphicsOutput const * 0 0 1479 0 0 0 0 0 0 0 0 0 0

1479 20 GraphicsOutput const 0 8832 20 GraphicsOutput const 20 GraphicsOutput const 0 0 1356 0 0 0 0 0 0 0 0 0 0

1480 29 FrameBufferProperties const * 0 8576 29 FrameBufferProperties const * 29 FrameBufferProperties const * 0 0 1481 0 0 0 0 0 0 0 0 0 0

1481 27 FrameBufferProperties const 0 8832 27 FrameBufferProperties const 27 FrameBufferProperties const 0 0 1396 0 0 0 0 0 0 0 0 0 0

1482 11 AsyncFuture 0 2048 11 AsyncFuture 11 AsyncFuture 0 0 0 0 0 0 0 0 0 0 0 0 1711
/**
 * This class represents a thread-safe handle to a promised future result of
 * an asynchronous operation, providing methods to query its status and result
 * as well as register callbacks for this future's completion.
 *
 * An AsyncFuture can be awaited from within a coroutine or task.  It keeps
 * track of tasks waiting for this future and automatically reactivates them
 * upon this future's completion.
 *
 * A task itself is also a subclass of AsyncFuture.  Other subclasses are
 * not generally necessary, except to override the function of `cancel()`.
 *
 * Until the future is done, it is "owned" by the resolver thread, though it's
 * still legal for other threads to query its state.  When the resolver thread
 * resolves this future using `set_result()`, or any thread calls `cancel()`,
 * it instantly enters the "done" state, after which the result becomes a
 * read-only field that all threads can access.
 *
 * When the future returns true for done(), a thread can use cancelled() to
 * determine whether the future was cancelled or get_result() to access the
 * result of the operation.  Not all operations define a meaningful result
 * value, so some will always return nullptr.
 *
 * In Python, the `cancelled()`, `wait()` and `get_result()` methods are
 * wrapped up into a single `result()` method which waits for the future to
 * complete before either returning the result or throwing an exception if the
 * future was cancelled.
 * However, it is preferable to use the `await` keyword when running from a
 * coroutine, which only suspends the current task and not the entire thread.
 *
 * This API aims to mirror and be compatible with Python's Future class.
 *
 * @since 1.10.0
 */

1483 13 AsyncFuture * 0 8576 13 AsyncFuture * 13 AsyncFuture * 0 0 1482 0 0 0 0 0 0 0 0 0 0

1484 21 StereoDisplayRegion * 0 8576 21 StereoDisplayRegion * 21 StereoDisplayRegion * 0 0 1394 0 0 0 0 0 0 0 0 0 0

1485 23 FrameBufferProperties * 0 8576 23 FrameBufferProperties * 23 FrameBufferProperties * 0 0 1396 0 0 0 0 0 0 0 0 0 0

1486 10 DrawMask * 0 8576 10 DrawMask * 10 DrawMask * 0 0 1487 0 0 0 0 0 0 0 0 0 0

1487 8 DrawMask 0 2105344 8 DrawMask 8 DrawMask 0 0 1488 0 0 0 0 0 0 0 0 0 0

1488 9 BitMask32 0 2105344 9 BitMask32 9 BitMask32 0 0 1489 0 0 0 0 0 0 0 0 0 0

1489 23 BitMask< uint32_t, 32 > 0 2048 23 BitMask< uint32_t, 32 > 23 BitMask< uint32_t, 32 > 0 0 0 0 0 0 0 0 0 0 0 0 0

1490 20 GraphicsOutputBase * 0 8576 20 GraphicsOutputBase * 20 GraphicsOutputBase * 0 0 1357 0 0 0 0 0 0 0 0 0 0

1491 29 GraphicsStateGuardian const * 0 8576 29 GraphicsStateGuardian const * 29 GraphicsStateGuardian const * 0 0 1492 0 0 0 0 0 0 0 0 0 0

1492 27 GraphicsStateGuardian const 0 8832 27 GraphicsStateGuardian const 27 GraphicsStateGuardian const 0 0 1361 0 0 0 0 0 0 0 0 0 0

1493 30 GraphicsThreadingModel const * 0 8576 30 GraphicsThreadingModel const * 30 GraphicsThreadingModel const * 0 0 1494 0 0 0 0 0 0 0 0 0 0

1494 28 GraphicsThreadingModel const 0 8832 28 GraphicsThreadingModel const 28 GraphicsThreadingModel const 0 0 1380 0 0 0 0 0 0 0 0 0 0

1495 21 TextureAttrib const * 0 8576 21 TextureAttrib const * 21 TextureAttrib const * 0 0 1496 0 0 0 0 0 0 0 0 0 0

1496 19 TextureAttrib const 0 8832 19 TextureAttrib const 19 TextureAttrib const 0 0 1497 0 0 0 0 0 0 0 0 0 0

1497 13 TextureAttrib 0 2048 13 TextureAttrib 13 TextureAttrib 0 0 0 0 0 0 0 0 0 0 0 0 130
/**
 * Indicates the set of TextureStages and their associated Textures that
 * should be applied to (or removed from) a node.
 */

1498 12 TextureStage 0 2048 12 TextureStage 12 TextureStage 0 0 0 0 0 0 0 0 0 0 0 0 336
/**
 * Defines the properties of a named stage of the multitexture pipeline.  The
 * TextureAttrib will associated a number of these stages with Texture
 * objects, and the GSG will render geometry by sorting all of the currently
 * active TextureStages in order and then issuing the appropriate rendering
 * calls to activate them.
 */

1499 14 TextureStage * 0 8576 14 TextureStage * 14 TextureStage * 0 0 1498 0 0 0 0 0 0 0 0 0 0

1500 10 Pipeline * 0 8576 10 Pipeline * 10 Pipeline * 0 0 1501 0 0 0 0 0 0 0 0 0 0

1501 8 Pipeline 0 2048 8 Pipeline 8 Pipeline 0 0 0 0 0 0 0 0 0 0 0 0 0

1502 22 GraphicsEngine const * 0 8576 22 GraphicsEngine const * 22 GraphicsEngine const * 0 0 1503 0 0 0 0 0 0 0 0 0 0

1503 20 GraphicsEngine const 0 8832 20 GraphicsEngine const 20 GraphicsEngine const 0 0 1378 0 0 0 0 0 0 0 0 0 0

1504 24 GraphicsThreadingModel * 0 8576 24 GraphicsThreadingModel * 24 GraphicsThreadingModel * 0 0 1380 0 0 0 0 0 0 0 0 0 0

1505 15 ReMutex const * 0 8576 15 ReMutex const * 15 ReMutex const * 0 0 1381 0 0 0 0 0 0 0 0 0 0

1506 18 LVecBase3i const * 0 8576 18 LVecBase3i const * 18 LVecBase3i const * 0 0 1507 0 0 0 0 0 0 0 0 0 0

1507 16 LVecBase3i const 0 8832 16 LVecBase3i const 16 LVecBase3i const 0 0 1508 0 0 0 0 0 0 0 0 0 0

1508 10 LVecBase3i 0 2048 10 LVecBase3i 10 LVecBase3i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1509 20 ShaderAttrib const * 0 8576 20 ShaderAttrib const * 20 ShaderAttrib const * 0 0 1510 0 0 0 0 0 0 0 0 0 0

1510 18 ShaderAttrib const 0 8832 18 ShaderAttrib const 18 ShaderAttrib const 0 0 1511 0 0 0 0 0 0 0 0 0 0

1511 12 ShaderAttrib 0 2048 12 ShaderAttrib 12 ShaderAttrib 0 0 0 0 0 0 0 0 0 0 0 0 10
/**
 *
 */

1512 27 GraphicsWindowInputDevice * 0 8576 27 GraphicsWindowInputDevice * 27 GraphicsWindowInputDevice * 0 0 1397 0 0 0 0 0 0 0 0 0 0

1513 14 ButtonHandle * 0 8576 14 ButtonHandle * 14 ButtonHandle * 0 0 1514 0 0 0 0 0 0 0 0 0 0

1514 12 ButtonHandle 0 16779264 12 ButtonHandle 12 ButtonHandle 0 0 0 0 0 0 0 0 0 0 0 0 162
/**
 * A ButtonHandle represents a single button from any device, including
 * keyboard buttons and mouse buttons (but see KeyboardButton and
 * MouseButton).
 */

1515 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

1516 11 PointerData 0 2048 11 PointerData 11 PointerData 0 0 0 0 0 0 0 0 0 0 0 0 124
/**
 * Holds the data that might be generated by a 2-d pointer input device, such
 * as the mouse in the GraphicsWindow.
 */

1517 33 GraphicsWindowInputDevice const * 0 8576 33 GraphicsWindowInputDevice const * 33 GraphicsWindowInputDevice const * 0 0 1518 0 0 0 0 0 0 0 0 0 0

1518 31 GraphicsWindowInputDevice const 0 8832 31 GraphicsWindowInputDevice const 31 GraphicsWindowInputDevice const 0 0 1397 0 0 0 0 0 0 0 0 0 0

1519 13 PointerData * 0 8576 13 PointerData * 13 PointerData * 0 0 1516 0 0 0 0 0 0 0 0 0 0

1520 17 TouchInfo const * 0 8576 17 TouchInfo const * 17 TouchInfo const * 0 0 1521 0 0 0 0 0 0 0 0 0 0

1521 15 TouchInfo const 0 8832 15 TouchInfo const 15 TouchInfo const 0 0 1399 0 0 0 0 0 0 0 0 0 0

1522 11 TouchInfo * 0 8576 11 TouchInfo * 11 TouchInfo * 0 0 1399 0 0 0 0 0 0 0 0 0 0

1523 38 GraphicsWindowProcCallbackData const * 0 8576 38 GraphicsWindowProcCallbackData const * 38 GraphicsWindowProcCallbackData const * 0 0 1524 0 0 0 0 0 0 0 0 0 0

1524 36 GraphicsWindowProcCallbackData const 0 8832 36 GraphicsWindowProcCallbackData const 36 GraphicsWindowProcCallbackData const 0 0 1401 0 0 0 0 0 0 0 0 0 0

1525 9 uintptr_t 0 2105344 9 uintptr_t 9 uintptr_t 0 0 1433 0 0 0 0 0 0 0 0 0 0

1526 32 GraphicsWindowProcCallbackData * 0 8576 32 GraphicsWindowProcCallbackData * 32 GraphicsWindowProcCallbackData * 0 0 1401 0 0 0 0 0 0 0 0 0 0

1527 22 GraphicsWindow const * 0 8576 22 GraphicsWindow const * 22 GraphicsWindow const * 0 0 1528 0 0 0 0 0 0 0 0 0 0

1528 20 GraphicsWindow const 0 8832 20 GraphicsWindow const 20 GraphicsWindow const 0 0 1403 0 0 0 0 0 0 0 0 0 0

1529 16 GraphicsWindow * 0 8576 16 GraphicsWindow * 16 GraphicsWindow * 0 0 1403 0 0 0 0 0 0 0 0 0 0

1530 13 InputDevice * 0 8576 13 InputDevice * 13 InputDevice * 0 0 1398 0 0 0 0 0 0 0 0 0 0

1531 9 ButtonMap 0 2048 9 ButtonMap 9 ButtonMap 0 0 0 0 0 0 0 0 0 0 0 0 328
/**
 * This class represents a map containing all of the buttons of a (keyboard)
 * device, though it can also be used as a generic mapping between
 * ButtonHandles.  It maps an underlying 'raw' button to a 'virtual' button,
 * which may optionally be associated with an appropriate platform-specific
 * name for the button.
 */

1532 11 ButtonMap * 0 8576 11 ButtonMap * 11 ButtonMap * 0 0 1531 0 0 0 0 0 0 0 0 0 0

1533 9 MouseData 0 2105344 9 MouseData 9 MouseData 0 0 1516 0 0 0 0 0 0 0 0 0 0

1534 11 MouseData * 0 8576 11 MouseData * 11 MouseData * 0 0 1533 0 0 0 0 0 0 0 0 0 0

1535 30 CallbackGraphicsWindow const * 0 8576 30 CallbackGraphicsWindow const * 30 CallbackGraphicsWindow const * 0 0 1536 0 0 0 0 0 0 0 0 0 0

1536 28 CallbackGraphicsWindow const 0 8832 28 CallbackGraphicsWindow const 28 CallbackGraphicsWindow const 0 0 1404 0 0 0 0 0 0 0 0 0 0

1537 26 WindowCallbackData const * 0 8576 50 CallbackGraphicsWindow::WindowCallbackData const * 50 CallbackGraphicsWindow::WindowCallbackData const * 0 0 1538 0 0 0 0 0 0 0 0 0 0

1538 24 WindowCallbackData const 0 8832 48 CallbackGraphicsWindow::WindowCallbackData const 48 CallbackGraphicsWindow::WindowCallbackData const 0 0 1405 0 0 0 0 0 0 0 0 0 0

1539 30 PropertiesCallbackData const * 0 8576 54 CallbackGraphicsWindow::PropertiesCallbackData const * 54 CallbackGraphicsWindow::PropertiesCallbackData const * 0 0 1540 0 0 0 0 0 0 0 0 0 0

1540 28 PropertiesCallbackData const 0 8832 52 CallbackGraphicsWindow::PropertiesCallbackData const 52 CallbackGraphicsWindow::PropertiesCallbackData const 0 0 1408 0 0 0 0 0 0 0 0 0 0

1541 26 RenderCallbackData const * 0 8576 50 CallbackGraphicsWindow::RenderCallbackData const * 50 CallbackGraphicsWindow::RenderCallbackData const * 0 0 1542 0 0 0 0 0 0 0 0 0 0

1542 24 RenderCallbackData const 0 8832 48 CallbackGraphicsWindow::RenderCallbackData const 48 CallbackGraphicsWindow::RenderCallbackData const 0 0 1410 0 0 0 0 0 0 0 0 0 0

1543 20 RenderCallbackData * 0 8576 44 CallbackGraphicsWindow::RenderCallbackData * 44 CallbackGraphicsWindow::RenderCallbackData * 0 0 1410 0 0 0 0 0 0 0 0 0 0

1544 13 DisplayMode * 0 8576 13 DisplayMode * 13 DisplayMode * 0 0 1411 0 0 0 0 0 0 0 0 0 0

1545 37 DisplayRegionCullCallbackData const * 0 8576 37 DisplayRegionCullCallbackData const * 37 DisplayRegionCullCallbackData const * 0 0 1546 0 0 0 0 0 0 0 0 0 0

1546 35 DisplayRegionCullCallbackData const 0 8832 35 DisplayRegionCullCallbackData const 35 DisplayRegionCullCallbackData const 0 0 1412 0 0 0 0 0 0 0 0 0 0

1547 13 CullHandler * 0 8576 13 CullHandler * 13 CullHandler * 0 0 1548 0 0 0 0 0 0 0 0 0 0

1548 11 CullHandler 0 2048 11 CullHandler 11 CullHandler 0 0 0 0 0 0 0 0 0 0 0 0 225
/**
 * This defines the abstract interface for an object that receives Geoms
 * identified by the CullTraverser.  By itself, it's not a particularly useful
 * class; to use it, derive from it and redefine record_object().
 */

1549 10 CullResult 0 2048 10 CullResult 10 CullResult 0 0 0 0 0 0 0 0 0 0 0 0 349
/**
 * This stores the result of a BinCullHandler traversal: an ordered collection
 * of CullBins, each of which holds a number of Geoms and RenderStates to be
 * rendered in some defined order.
 *
 * This is also used to keep the results of last frame's cull traversal around
 * to make next frame's traversal of the same scene a little easier.
 */

1550 37 DisplayRegionDrawCallbackData const * 0 8576 37 DisplayRegionDrawCallbackData const * 37 DisplayRegionDrawCallbackData const * 0 0 1551 0 0 0 0 0 0 0 0 0 0

1551 35 DisplayRegionDrawCallbackData const 0 8832 35 DisplayRegionDrawCallbackData const 35 DisplayRegionDrawCallbackData const 0 0 1413 0 0 0 0 0 0 0 0 0 0

1552 12 CullResult * 0 8576 12 CullResult * 12 CullResult * 0 0 1549 0 0 0 0 0 0 0 0 0 0

1553 25 DisplaySearchParameters * 0 8576 25 DisplaySearchParameters * 25 DisplaySearchParameters * 0 0 1414 0 0 0 0 0 0 0 0 0 0

1554 31 DisplaySearchParameters const * 0 8576 31 DisplaySearchParameters const * 31 DisplaySearchParameters const * 0 0 1555 0 0 0 0 0 0 0 0 0 0

1555 29 DisplaySearchParameters const 0 8832 29 DisplaySearchParameters const 29 DisplaySearchParameters const 0 0 1414 0 0 0 0 0 0 0 0 0 0

1556 16 GraphicsBuffer * 0 8576 16 GraphicsBuffer * 16 GraphicsBuffer * 0 0 1415 0 0 0 0 0 0 0 0 0 0

1557 29 GraphicsPipeSelection const * 0 8576 29 GraphicsPipeSelection const * 29 GraphicsPipeSelection const * 0 0 1558 0 0 0 0 0 0 0 0 0 0

1558 27 GraphicsPipeSelection const 0 8832 27 GraphicsPipeSelection const 27 GraphicsPipeSelection const 0 0 1416 0 0 0 0 0 0 0 0 0 0

1559 23 GraphicsPipeSelection * 0 8576 23 GraphicsPipeSelection * 23 GraphicsPipeSelection * 0 0 1416 0 0 0 0 0 0 0 0 0 0

1560 18 MouseAndKeyboard * 0 8576 18 MouseAndKeyboard * 18 MouseAndKeyboard * 0 0 1418 0 0 0 0 0 0 0 0 0 0

1561 24 MouseAndKeyboard const * 0 8576 24 MouseAndKeyboard const * 24 MouseAndKeyboard const * 0 0 1562 0 0 0 0 0 0 0 0 0 0

1562 22 MouseAndKeyboard const 0 8832 22 MouseAndKeyboard const 22 MouseAndKeyboard const 0 0 1418 0 0 0 0 0 0 0 0 0 0

1563 16 ParasiteBuffer * 0 8576 16 ParasiteBuffer * 16 ParasiteBuffer * 0 0 1421 0 0 0 0 0 0 0 0 0 0

0
178
1564 13 display_width 0 2 1327 658 0 0 0 0 0 0 0 27 GraphicsPipe::display_width 0

1565 14 display_height 0 2 1327 659 0 0 0 0 0 0 0 28 GraphicsPipe::display_height 0

1566 12 display_zoom 0 2 1328 660 0 0 0 0 0 0 0 26 GraphicsPipe::display_zoom 0

1567 19 display_information 0 2 1330 661 0 0 0 0 0 0 0 33 GraphicsPipe::display_information 0

1568 14 interface_name 0 2 1333 717 0 0 0 0 0 0 0 28 GraphicsPipe::interface_name 0

1569 11 clear_color 0 6 1337 727 726 0 0 0 0 0 0 27 DrawableRegion::clear_color 0

1570 11 clear_depth 0 6 1328 729 728 0 0 0 0 0 0 27 DrawableRegion::clear_depth 0

1571 13 clear_stencil 0 6 1341 731 730 0 0 0 0 0 0 29 DrawableRegion::clear_stencil 0

1572 10 pixel_zoom 0 6 1328 739 738 0 0 0 0 0 0 26 DrawableRegion::pixel_zoom 0

1573 12 pixel_factor 0 2 1328 740 0 0 0 0 0 0 0 28 DrawableRegion::pixel_factor 0

1574 9 os_handle 0 6 1343 744 745 0 0 0 0 0 0 23 WindowHandle::os_handle 0

1575 17 config_properties 0 2 1345 757 0 0 0 0 0 0 0 35 WindowProperties::config_properties 0

1576 7 default 0 6 1345 758 759 0 0 0 0 0 0 25 WindowProperties::default 0

1577 6 origin 0 30 1348 767 766 770 771 0 0 0 0 24 WindowProperties::origin 0

1578 4 size 0 30 1349 773 772 776 777 0 0 0 0 22 WindowProperties::size 0

1579 10 mouse_mode 0 30 1347 780 779 778 781 0 0 0 0 28 WindowProperties::mouse_mode 0

1580 5 title 0 30 1333 783 782 784 785 0 0 0 0 23 WindowProperties::title 0

1581 11 undecorated 0 30 1350 787 786 788 789 0 0 0 0 29 WindowProperties::undecorated 0

1582 10 fixed_size 0 30 1350 791 790 792 793 0 0 0 0 28 WindowProperties::fixed_size 0

1583 10 fullscreen 0 30 1350 795 794 796 797 0 0 0 0 28 WindowProperties::fullscreen 0

1584 10 foreground 0 30 1350 799 798 800 801 0 0 0 0 28 WindowProperties::foreground 0

1585 9 minimized 0 30 1350 803 802 804 805 0 0 0 0 27 WindowProperties::minimized 0

1586 4 open 0 30 1350 811 810 812 813 0 0 0 0 22 WindowProperties::open 0

1587 13 cursor_hidden 0 30 1350 815 814 816 817 0 0 0 0 31 WindowProperties::cursor_hidden 0

1588 13 icon_filename 0 30 1351 819 818 820 821 0 0 0 0 31 WindowProperties::icon_filename 0

1589 15 cursor_filename 0 30 1351 823 822 824 825 0 0 0 0 33 WindowProperties::cursor_filename 0

1590 7 z_order 0 30 1346 827 826 828 829 0 0 0 0 25 WindowProperties::z_order 0

1591 13 parent_window 0 30 1352 831 830 832 833 0 0 0 0 31 WindowProperties::parent_window 0

1592 10 dimensions 0 6 1354 843 848 0 0 0 0 0 0 25 DisplayRegion::dimensions 0

1593 6 window 0 2 1355 849 0 0 0 0 0 0 0 21 DisplayRegion::window 0

1594 3 gsg 0 2 1360 857 0 0 0 0 0 0 0 19 GraphicsOutput::gsg 0

1595 6 active 0 6 1350 869 868 0 0 0 0 0 0 29 GraphicsStateGuardian::active 0

1596 5 valid 0 2 1350 870 0 0 0 0 0 0 0 28 GraphicsStateGuardian::valid 0

1597 17 incomplete_render 0 6 1350 873 872 0 0 0 0 0 0 40 GraphicsStateGuardian::incomplete_render 0

1598 27 effective_incomplete_render 0 2 1350 874 0 0 0 0 0 0 0 50 GraphicsStateGuardian::effective_incomplete_render 0

1599 6 loader 0 6 1364 876 875 0 0 0 0 0 0 29 GraphicsStateGuardian::loader 0

1600 16 shader_generator 0 6 1366 878 877 0 0 0 0 0 0 39 GraphicsStateGuardian::shader_generator 0

1601 4 pipe 0 2 1368 879 0 0 0 0 0 0 0 27 GraphicsStateGuardian::pipe 0

1602 22 max_vertices_per_array 0 2 1327 923 0 0 0 0 0 0 0 45 GraphicsStateGuardian::max_vertices_per_array 0

1603 26 max_vertices_per_primitive 0 2 1327 924 0 0 0 0 0 0 0 49 GraphicsStateGuardian::max_vertices_per_primitive 0

1604 18 max_texture_stages 0 2 1327 883 0 0 0 0 0 0 0 41 GraphicsStateGuardian::max_texture_stages 0

1605 21 max_texture_dimension 0 2 1327 925 0 0 0 0 0 0 0 44 GraphicsStateGuardian::max_texture_dimension 0

1606 24 max_3d_texture_dimension 0 2 1327 884 0 0 0 0 0 0 0 47 GraphicsStateGuardian::max_3d_texture_dimension 0

1607 27 max_2d_texture_array_layers 0 2 1327 885 0 0 0 0 0 0 0 50 GraphicsStateGuardian::max_2d_texture_array_layers 8
//z axis

1608 22 max_cube_map_dimension 0 2 1327 886 0 0 0 0 0 0 0 45 GraphicsStateGuardian::max_cube_map_dimension 8
//z axis

1609 23 max_buffer_texture_size 0 2 1327 887 0 0 0 0 0 0 0 46 GraphicsStateGuardian::max_buffer_texture_size 0

1610 24 supports_texture_combine 0 2 1350 888 0 0 0 0 0 0 0 47 GraphicsStateGuardian::supports_texture_combine 0

1611 29 supports_texture_saved_result 0 2 1350 889 0 0 0 0 0 0 0 52 GraphicsStateGuardian::supports_texture_saved_result 0

1612 21 supports_texture_dot3 0 2 1350 890 0 0 0 0 0 0 0 44 GraphicsStateGuardian::supports_texture_dot3 0

1613 19 supports_3d_texture 0 2 1350 891 0 0 0 0 0 0 0 42 GraphicsStateGuardian::supports_3d_texture 0

1614 25 supports_2d_texture_array 0 2 1350 892 0 0 0 0 0 0 0 48 GraphicsStateGuardian::supports_2d_texture_array 0

1615 17 supports_cube_map 0 2 1350 893 0 0 0 0 0 0 0 40 GraphicsStateGuardian::supports_cube_map 0

1616 23 supports_buffer_texture 0 2 1350 894 0 0 0 0 0 0 0 46 GraphicsStateGuardian::supports_buffer_texture 0

1617 23 supports_cube_map_array 0 2 1350 895 0 0 0 0 0 0 0 46 GraphicsStateGuardian::supports_cube_map_array 0

1618 21 supports_tex_non_pow2 0 2 1350 896 0 0 0 0 0 0 0 44 GraphicsStateGuardian::supports_tex_non_pow2 0

1619 21 supports_texture_srgb 0 2 1350 926 0 0 0 0 0 0 0 44 GraphicsStateGuardian::supports_texture_srgb 0

1620 27 supports_compressed_texture 0 2 1350 897 0 0 0 0 0 0 0 50 GraphicsStateGuardian::supports_compressed_texture 0

1621 10 max_lights 0 2 1327 898 0 0 0 0 0 0 0 33 GraphicsStateGuardian::max_lights 0

1622 15 max_clip_planes 0 2 1327 899 0 0 0 0 0 0 0 38 GraphicsStateGuardian::max_clip_planes 0

1623 21 max_vertex_transforms 0 2 1327 900 0 0 0 0 0 0 0 44 GraphicsStateGuardian::max_vertex_transforms 0

1624 28 max_vertex_transform_indices 0 2 1327 901 0 0 0 0 0 0 0 51 GraphicsStateGuardian::max_vertex_transform_indices 0

1625 21 copy_texture_inverted 0 2 1350 902 0 0 0 0 0 0 0 44 GraphicsStateGuardian::copy_texture_inverted 0

1626 20 supports_multisample 0 2 1350 927 0 0 0 0 0 0 0 43 GraphicsStateGuardian::supports_multisample 0

1627 24 supports_generate_mipmap 0 2 1350 903 0 0 0 0 0 0 0 47 GraphicsStateGuardian::supports_generate_mipmap 0

1628 22 supports_depth_texture 0 2 1350 904 0 0 0 0 0 0 0 45 GraphicsStateGuardian::supports_depth_texture 0

1629 22 supports_depth_stencil 0 2 1350 905 0 0 0 0 0 0 0 45 GraphicsStateGuardian::supports_depth_stencil 0

1630 26 supports_luminance_texture 0 2 1350 906 0 0 0 0 0 0 0 49 GraphicsStateGuardian::supports_luminance_texture 0

1631 22 supports_shadow_filter 0 2 1350 928 0 0 0 0 0 0 0 45 GraphicsStateGuardian::supports_shadow_filter 0

1632 24 supports_sampler_objects 0 2 1350 907 0 0 0 0 0 0 0 47 GraphicsStateGuardian::supports_sampler_objects 0

1633 22 supports_basic_shaders 0 2 1350 908 0 0 0 0 0 0 0 45 GraphicsStateGuardian::supports_basic_shaders 0

1634 25 supports_geometry_shaders 0 2 1350 909 0 0 0 0 0 0 0 48 GraphicsStateGuardian::supports_geometry_shaders 0

1635 29 supports_tessellation_shaders 0 2 1350 910 0 0 0 0 0 0 0 52 GraphicsStateGuardian::supports_tessellation_shaders 0

1636 24 supports_compute_shaders 0 2 1350 911 0 0 0 0 0 0 0 47 GraphicsStateGuardian::supports_compute_shaders 0

1637 13 supports_glsl 0 2 1350 912 0 0 0 0 0 0 0 36 GraphicsStateGuardian::supports_glsl 0

1638 13 supports_hlsl 0 2 1350 929 0 0 0 0 0 0 0 36 GraphicsStateGuardian::supports_hlsl 0

1639 16 supports_stencil 0 2 1350 913 0 0 0 0 0 0 0 39 GraphicsStateGuardian::supports_stencil 0

1640 26 supports_two_sided_stencil 0 2 1350 914 0 0 0 0 0 0 0 49 GraphicsStateGuardian::supports_two_sided_stencil 0

1641 28 supports_geometry_instancing 0 2 1350 915 0 0 0 0 0 0 0 51 GraphicsStateGuardian::supports_geometry_instancing 0

1642 22 supports_indirect_draw 0 2 1350 916 0 0 0 0 0 0 0 45 GraphicsStateGuardian::supports_indirect_draw 0

1643 24 supports_occlusion_query 0 2 1350 917 0 0 0 0 0 0 0 47 GraphicsStateGuardian::supports_occlusion_query 0

1644 20 supports_timer_query 0 2 1350 918 0 0 0 0 0 0 0 43 GraphicsStateGuardian::supports_timer_query 0

1645 20 timer_queries_active 0 2 1350 919 0 0 0 0 0 0 0 43 GraphicsStateGuardian::timer_queries_active 0

1646 17 max_color_targets 0 2 1327 920 0 0 0 0 0 0 0 40 GraphicsStateGuardian::max_color_targets 0

1647 29 supports_dual_source_blending 0 2 1350 922 0 0 0 0 0 0 0 52 GraphicsStateGuardian::supports_dual_source_blending 0

1648 12 shader_model 0 6 1363 930 931 0 0 0 0 0 0 35 GraphicsStateGuardian::shader_model 0

1649 17 coordinate_system 0 6 1369 938 937 0 0 0 0 0 0 40 GraphicsStateGuardian::coordinate_system 0

1650 16 prepared_objects 0 2 1370 940 0 0 0 0 0 0 0 39 GraphicsStateGuardian::prepared_objects 0

1651 5 gamma 0 6 1328 942 941 0 0 0 0 0 0 28 GraphicsStateGuardian::gamma 0

1652 24 texture_quality_override 0 6 1372 945 944 0 0 0 0 0 0 47 GraphicsStateGuardian::texture_quality_override 0

1653 13 flash_texture 0 6 1374 950 948 0 0 0 0 0 0 36 GraphicsStateGuardian::flash_texture 0

1654 13 driver_vendor 0 2 1333 952 0 0 0 0 0 0 0 36 GraphicsStateGuardian::driver_vendor 0

1655 15 driver_renderer 0 2 1333 953 0 0 0 0 0 0 0 38 GraphicsStateGuardian::driver_renderer 0

1656 14 driver_version 0 2 1333 954 0 0 0 0 0 0 0 37 GraphicsStateGuardian::driver_version 0

1657 20 driver_version_major 0 2 1327 955 0 0 0 0 0 0 0 43 GraphicsStateGuardian::driver_version_major 0

1658 20 driver_version_minor 0 2 1327 956 0 0 0 0 0 0 0 43 GraphicsStateGuardian::driver_version_minor 0

1659 27 driver_shader_version_major 0 2 1327 957 0 0 0 0 0 0 0 50 GraphicsStateGuardian::driver_shader_version_major 0

1660 27 driver_shader_version_minor 0 2 1327 958 0 0 0 0 0 0 0 50 GraphicsStateGuardian::driver_shader_version_minor 0

1661 5 scene 0 6 1375 960 959 0 0 0 0 0 0 28 GraphicsStateGuardian::scene 0

1662 4 pipe 0 2 1368 858 0 0 0 0 0 0 0 20 GraphicsOutput::pipe 0

1663 6 engine 0 2 1377 859 0 0 0 0 0 0 0 22 GraphicsOutput::engine 0

1664 15 threading_model 0 6 1380 966 965 0 0 0 0 0 0 31 GraphicsEngine::threading_model 0

1665 11 render_lock 0 2 1381 982 0 0 0 0 0 0 0 27 GraphicsEngine::render_lock 0

1666 9 auto_flip 0 6 1350 984 983 0 0 0 0 0 0 25 GraphicsEngine::auto_flip 0

1667 11 portal_cull 0 6 1350 986 985 0 0 0 0 0 0 27 GraphicsEngine::portal_cull 0

1668 14 default_loader 0 6 1364 988 987 0 0 0 0 0 0 30 GraphicsEngine::default_loader 0

1669 7 windows 0 66 1355 998 0 0 0 0 997 0 0 23 GraphicsEngine::windows 0

1670 4 name 0 2 1383 860 0 0 0 0 0 0 0 20 GraphicsOutput::name 0

1671 4 size 0 2 1384 1015 0 0 0 0 0 0 0 20 GraphicsOutput::size 0

1672 7 fb_size 0 2 1385 1018 0 0 0 0 0 0 0 23 GraphicsOutput::fb_size 0

1673 13 sbs_left_size 0 2 1385 1021 0 0 0 0 0 0 0 29 GraphicsOutput::sbs_left_size 0

1674 14 sbs_right_size 0 2 1385 1024 0 0 0 0 0 0 0 30 GraphicsOutput::sbs_right_size 0

1675 6 active 0 6 1350 1031 1030 0 0 0 0 0 0 22 GraphicsOutput::active 0

1676 8 one_shot 0 6 1350 1033 1032 0 0 0 0 0 0 24 GraphicsOutput::one_shot 0

1677 8 inverted 0 6 1350 1035 1034 0 0 0 0 0 0 24 GraphicsOutput::inverted 0

1678 9 swap_eyes 0 6 1350 1037 1036 0 0 0 0 0 0 25 GraphicsOutput::swap_eyes 0

1679 4 sort 0 6 1327 1051 1050 0 0 0 0 0 0 20 GraphicsOutput::sort 0

1680 10 child_sort 0 6 1327 1054 1052 0 0 0 0 0 0 26 GraphicsOutput::child_sort 0

1681 15 display_regions 0 66 1386 1064 0 0 0 0 1063 0 0 31 GraphicsOutput::display_regions 0

1682 22 active_display_regions 0 66 1386 1066 0 0 0 0 1065 0 0 38 GraphicsOutput::active_display_regions 0

1683 23 supports_render_texture 0 2 1350 1076 0 0 0 0 0 0 0 39 GraphicsOutput::supports_render_texture 0

1684 4 pipe 0 2 1368 850 0 0 0 0 0 0 0 19 DisplayRegion::pipe 0

1685 6 stereo 0 2 1350 851 0 0 0 0 0 0 0 21 DisplayRegion::stereo 0

1686 6 camera 0 6 1387 1081 1080 0 0 0 0 0 0 21 DisplayRegion::camera 0

1687 6 active 0 6 1350 1083 1082 0 0 0 0 0 0 21 DisplayRegion::active 0

1688 4 sort 0 6 1327 1085 1084 0 0 0 0 0 0 19 DisplayRegion::sort 0

1689 14 stereo_channel 0 6 1388 1087 1086 0 0 0 0 0 0 29 DisplayRegion::stereo_channel 0

1690 15 tex_view_offset 0 6 1327 1089 1088 0 0 0 0 0 0 30 DisplayRegion::tex_view_offset 0

1691 17 incomplete_render 0 6 1350 1091 1090 0 0 0 0 0 0 32 DisplayRegion::incomplete_render 0

1692 23 texture_reload_priority 0 6 1327 1093 1092 0 0 0 0 0 0 38 DisplayRegion::texture_reload_priority 0

1693 10 lens_index 0 6 1327 1095 1094 0 0 0 0 0 0 25 DisplayRegion::lens_index 0

1694 14 cull_traverser 0 6 1390 1097 1096 0 0 0 0 0 0 29 DisplayRegion::cull_traverser 0

1695 15 target_tex_page 0 6 1327 1100 1099 0 0 0 0 0 0 30 DisplayRegion::target_tex_page 0

1696 15 scissor_enabled 0 6 1350 1102 1101 0 0 0 0 0 0 30 DisplayRegion::scissor_enabled 0

1697 13 cull_callback 0 6 1392 1105 1103 0 0 0 0 0 0 28 DisplayRegion::cull_callback 0

1698 13 draw_callback 0 6 1392 1108 1106 0 0 0 0 0 0 28 DisplayRegion::draw_callback 0

1699 10 pixel_size 0 2 1385 1111 0 0 0 0 0 0 0 25 DisplayRegion::pixel_size 0

1700 8 left_eye 0 2 1395 1120 0 0 0 0 0 0 0 29 StereoDisplayRegion::left_eye 0

1701 9 right_eye 0 2 1395 1121 0 0 0 0 0 0 0 30 StereoDisplayRegion::right_eye 0

1702 10 depth_bits 0 6 1327 1123 1145 0 0 0 0 0 0 33 FrameBufferProperties::depth_bits 22
// Individual queries.

1703 10 color_bits 0 6 1327 1124 1146 0 0 0 0 0 0 33 FrameBufferProperties::color_bits 0

1704 8 red_bits 0 6 1327 1125 1148 0 0 0 0 0 0 31 FrameBufferProperties::red_bits 0

1705 10 green_bits 0 6 1327 1126 1149 0 0 0 0 0 0 33 FrameBufferProperties::green_bits 0

1706 9 blue_bits 0 6 1327 1127 1150 0 0 0 0 0 0 32 FrameBufferProperties::blue_bits 0

1707 10 alpha_bits 0 6 1327 1128 1151 0 0 0 0 0 0 33 FrameBufferProperties::alpha_bits 0

1708 12 stencil_bits 0 6 1327 1129 1152 0 0 0 0 0 0 35 FrameBufferProperties::stencil_bits 0

1709 10 accum_bits 0 6 1327 1130 1153 0 0 0 0 0 0 33 FrameBufferProperties::accum_bits 0

1710 8 aux_rgba 0 6 1327 1131 1154 0 0 0 0 0 0 31 FrameBufferProperties::aux_rgba 0

1711 9 aux_hrgba 0 6 1327 1132 1155 0 0 0 0 0 0 32 FrameBufferProperties::aux_hrgba 0

1712 9 aux_float 0 6 1327 1133 1156 0 0 0 0 0 0 32 FrameBufferProperties::aux_float 0

1713 12 multisamples 0 6 1327 1134 1157 0 0 0 0 0 0 35 FrameBufferProperties::multisamples 0

1714 16 coverage_samples 0 6 1327 1135 1158 0 0 0 0 0 0 39 FrameBufferProperties::coverage_samples 0

1715 12 back_buffers 0 6 1327 1136 1159 0 0 0 0 0 0 35 FrameBufferProperties::back_buffers 0

1716 13 indexed_color 0 6 1350 1137 1160 0 0 0 0 0 0 36 FrameBufferProperties::indexed_color 0

1717 9 rgb_color 0 6 1350 1138 1161 0 0 0 0 0 0 32 FrameBufferProperties::rgb_color 0

1718 6 stereo 0 6 1350 1139 1162 0 0 0 0 0 0 29 FrameBufferProperties::stereo 0

1719 14 force_hardware 0 6 1350 1140 1163 0 0 0 0 0 0 37 FrameBufferProperties::force_hardware 0

1720 14 force_software 0 6 1350 1141 1164 0 0 0 0 0 0 37 FrameBufferProperties::force_software 0

1721 10 srgb_color 0 6 1350 1142 1165 0 0 0 0 0 0 33 FrameBufferProperties::srgb_color 0

1722 11 float_color 0 6 1350 1143 1166 0 0 0 0 0 0 34 FrameBufferProperties::float_color 0

1723 11 float_depth 0 6 1350 1144 1167 0 0 0 0 0 0 34 FrameBufferProperties::float_depth 0

1724 10 properties 0 2 1345 1220 0 0 0 0 0 0 0 26 GraphicsWindow::properties 0

1725 20 requested_properties 0 2 1345 1221 0 0 0 0 0 0 0 36 GraphicsWindow::requested_properties 0

1726 19 rejected_properties 0 2 1345 1223 0 0 0 0 0 0 0 35 GraphicsWindow::rejected_properties 0

1727 6 closed 0 2 1350 1225 0 0 0 0 0 0 0 22 GraphicsWindow::closed 0

1728 12 window_event 0 6 1333 1228 1227 0 0 0 0 0 0 28 GraphicsWindow::window_event 0

1729 19 close_request_event 0 6 1333 1230 1229 0 0 0 0 0 0 35 GraphicsWindow::close_request_event 0

1730 14 unexposed_draw 0 6 1350 1232 1231 0 0 0 0 0 0 30 GraphicsWindow::unexposed_draw 0

1731 13 window_handle 0 2 1352 1233 0 0 0 0 0 0 0 29 GraphicsWindow::window_handle 0

1732 6 window 0 2 1406 1246 0 0 0 0 0 0 0 50 CallbackGraphicsWindow::WindowCallbackData::window 0

1733 13 callback_type 0 2 1409 1253 0 0 0 0 0 0 0 57 CallbackGraphicsWindow::RenderCallbackData::callback_type 0

1734 10 frame_mode 0 2 1359 1254 0 0 0 0 0 0 0 54 CallbackGraphicsWindow::RenderCallbackData::frame_mode 0

1735 11 render_flag 0 6 1350 1256 1255 0 0 0 0 0 0 55 CallbackGraphicsWindow::RenderCallbackData::render_flag 0

1736 5 width 0 6 1327 1270 1271 0 0 0 0 0 0 18 DisplayMode::width 0

1737 6 height 0 6 1327 1272 1273 0 0 0 0 0 0 19 DisplayMode::height 0

1738 14 bits_per_pixel 0 6 1327 1274 1275 0 0 0 0 0 0 27 DisplayMode::bits_per_pixel 0

1739 12 refresh_rate 0 6 1327 1276 1277 0 0 0 0 0 0 25 DisplayMode::refresh_rate 0

1740 15 fullscreen_only 0 6 1327 1278 1279 0 0 0 0 0 0 28 DisplayMode::fullscreen_only 0

1741 10 pipe_types 0 66 1417 1304 0 0 0 0 1303 0 0 33 GraphicsPipeSelection::pipe_types 0

7
1742 17 get_display_modes 0 668 669 37 DisplayInformation::get_display_modes 0

1743 11 get_windows 0 997 998 27 GraphicsEngine::get_windows 0

1744 19 get_display_regions 0 1063 1064 35 GraphicsOutput::get_display_regions 0

1745 26 get_active_display_regions 0 1065 1066 42 GraphicsOutput::get_active_display_regions 0

1746 17 get_input_devices 0 1234 1235 33 GraphicsWindow::get_input_devices 0

1747 22 get_input_device_names 0 1234 1236 38 GraphicsWindow::get_input_device_names 0

1748 14 get_pipe_types 0 1303 1304 37 GraphicsPipeSelection::get_pipe_types 0

