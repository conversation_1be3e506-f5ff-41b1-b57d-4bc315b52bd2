1730992849
3 3
9 libp3text 4 z_GL 12 panda3d.core 
409
398 13 get_character 0 4 807 24 TextGlyph::get_character 0 1 1 96
/**
 * Returns the Unicode value that corresponds to the character this glyph
 * represents.
 */
48
inline int TextGlyph::get_character(void) const;

399 8 has_quad 0 4 807 19 TextGlyph::has_quad 0 1 2 239
/**
 * Returns true if this glyph contains the definition for a simple quad,
 * rather than a more complex piece of geometry.
 *
 * You may still call get_geom() even if this returns true, which will
 * synthesize a Geom for this quad.
 */
44
inline bool TextGlyph::has_quad(void) const;

400 8 get_quad 0 4 807 19 TextGlyph::get_quad 0 1 3 255
/**
 * Assuming that this glyph is representable as a textured quad, returns its
 * dimensions and UV range.  Returns false if it is not representable as a
 * quad, or if it is whitespace.
 *
 * The order of the components is left, bottom, right, top.
 */
83
inline bool TextGlyph::get_quad(LVecBase4 &dimensions, LVecBase4 &texcoords) const;

401 9 get_state 0 4 807 20 TextGlyph::get_state 0 1 4 67
/**
 * Returns the state in which the glyph should be rendered.
 */
59
inline RenderState const *TextGlyph::get_state(void) const;

402 11 get_advance 0 4 807 22 TextGlyph::get_advance 0 1 5 181
/**
 * Returns the distance by which the character pointer should be advanced
 * after placing this character; i.e.  the approximate width the character
 * takes up on the line.
 */
54
inline PN_stdfloat TextGlyph::get_advance(void) const;

403 13 is_whitespace 0 6 807 24 TextGlyph::is_whitespace 0 1 6 127
/**
 * Returns true if this glyph represents invisible whitespace, or false if it
 * corresponds to some visible character.
 */
50
virtual bool TextGlyph::is_whitespace(void) const;

404 8 get_geom 0 4 807 19 TextGlyph::get_geom 0 1 7 195
/**
 * Returns a Geom that renders the particular glyph.  It will be generated if
 * necessary.
 *
 * This method will always return a copy of the Geom, so the caller is free to
 * modify it.
 */
77
PointerTo< Geom > TextGlyph::get_geom(GeomEnums::UsageHint usage_hint) const;

405 14 get_class_type 0 4 807 25 TextGlyph::get_class_type 0 1 8 0
50
static TypeHandle TextGlyph::get_class_type(void);

406 29 upcast_to_TypedReferenceCount 0 12 815 39 TextFont::upcast_to_TypedReferenceCount 0 1 20 43
upcast from TextFont to TypedReferenceCount
67
TypedReferenceCount *TextFont::upcast_to_TypedReferenceCount(void);

407 20 downcast_to_TextFont 0 12 808 41 TypedReferenceCount::downcast_to_TextFont 0 0 45
downcast from TypedReferenceCount to TextFont
58
TextFont *TypedReferenceCount::downcast_to_TextFont(void);

408 17 upcast_to_Namable 0 12 815 27 TextFont::upcast_to_Namable 0 1 21 31
upcast from TextFont to Namable
43
Namable *TextFont::upcast_to_Namable(void);

409 20 downcast_to_TextFont 0 12 816 29 Namable::downcast_to_TextFont 0 0 33
downcast from Namable to TextFont
46
TextFont *Namable::downcast_to_TextFont(void);

410 9 ~TextFont 0 518 815 19 TextFont::~TextFont 0 0 10
/**
 *
 */
34
virtual TextFont::~TextFont(void);

411 9 make_copy 0 6 815 19 TextFont::make_copy 0 1 9 0
66
virtual PointerTo< TextFont > TextFont::make_copy(void) const = 0;

412 8 is_valid 0 4 815 18 TextFont::is_valid 0 1 10 79
/**
 * Returns true if the font is valid and ready to use, false otherwise.
 */
43
inline bool TextFont::is_valid(void) const;

413 22 operator typecast bool 0 132 815 32 TextFont::operator typecast bool 0 1 19 0
34
inline operator bool (void) const;

414 15 get_line_height 0 4 815 25 TextFont::get_line_height 0 1 11 65
/**
 * Returns the number of units high each line of text is.
 */
57
inline PN_stdfloat TextFont::get_line_height(void) const;

415 15 set_line_height 0 4 815 25 TextFont::set_line_height 0 1 12 65
/**
 * Changes the number of units high each line of text is.
 */
63
inline void TextFont::set_line_height(PN_stdfloat line_height);

416 17 get_space_advance 0 4 815 27 TextFont::get_space_advance 0 1 13 55
/**
 * Returns the number of units wide a space is.
 */
59
inline PN_stdfloat TextFont::get_space_advance(void) const;

417 17 set_space_advance 0 4 815 27 TextFont::set_space_advance 0 1 14 55
/**
 * Changes the number of units wide a space is.
 */
67
inline void TextFont::set_space_advance(PN_stdfloat space_advance);

418 9 get_glyph 0 4 815 19 TextFont::get_glyph 0 1 15 294
/**
 * Gets the glyph associated with the given character code, as well as an
 * optional scaling parameter that should be applied to the glyph's geometry
 * and advance parameters.  Returns the glyph on success.  On failure, it may
 * still return a printable glyph, or it may return NULL.
 */
70
inline ConstPointerTo< TextGlyph > TextFont::get_glyph(int character);

419 11 get_kerning 0 6 815 21 TextFont::get_kerning 0 1 16 180
/**
 * Returns the amount by which to offset the second glyph when it directly
 * follows the first glyph.  This is an additional offset that is added on top
 * of the advance.
 */
71
virtual PN_stdfloat TextFont::get_kerning(int first, int second) const;

420 5 write 0 6 815 15 TextFont::write 0 1 17 10
/**
 *
 */
72
virtual void TextFont::write(std::ostream &out, int indent_level) const;

421 14 get_class_type 0 4 815 24 TextFont::get_class_type 0 1 18 0
49
static TypeHandle TextFont::get_class_type(void);

422 8 get_page 0 4 819 26 DynamicTextGlyph::get_page 0 1 22 61
/**
 * Returns the DynamicTextPage that this glyph is on.
 */
63
inline DynamicTextPage *DynamicTextGlyph::get_page(void) const;

423 8 get_size 0 4 821 25 DynamicTextPage::get_size 0 1 34 61
/**
 * Returns the size of the page (texture), in pixels.
 */
63
inline LVecBase2i const &DynamicTextPage::get_size(void) const;

424 10 get_x_size 0 4 821 27 DynamicTextPage::get_x_size 0 1 35 63
/**
 * Returns the x size of the page (texture), in pixels.
 */
51
inline int DynamicTextPage::get_x_size(void) const;

425 10 get_y_size 0 4 821 27 DynamicTextPage::get_y_size 0 1 36 63
/**
 * Returns the y size of the page (texture), in pixels.
 */
51
inline int DynamicTextPage::get_y_size(void) const;

426 8 is_empty 0 4 821 25 DynamicTextPage::is_empty 0 1 37 67
/**
 * Returns true if the page has no glyphs, false otherwise.
 */
50
inline bool DynamicTextPage::is_empty(void) const;

427 14 get_class_type 0 4 821 31 DynamicTextPage::get_class_type 0 1 38 0
56
static TypeHandle DynamicTextPage::get_class_type(void);

428 15 DynamicTextPage 0 260 821 32 DynamicTextPage::DynamicTextPage 0 1 33 10
/**
 *
 */
75
inline DynamicTextPage::DynamicTextPage(DynamicTextPage const &) = default;

429 16 ~DynamicTextPage 0 516 821 33 DynamicTextPage::~DynamicTextPage 0 0 0
40
DynamicTextPage::~DynamicTextPage(void);

430 10 intersects 0 4 819 28 DynamicTextGlyph::intersects 0 1 23 207
/**
 * Returns true if the particular position this glyph has been assigned to
 * overlaps the rectangle whose top left corner is at x, y and whose size is
 * given by x_size, y_size, or false otherwise.
 */
85
inline bool DynamicTextGlyph::intersects(int x, int y, int x_size, int y_size) const;

431 8 get_left 0 4 819 26 DynamicTextGlyph::get_left 0 1 24 99
/**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */
58
inline PN_stdfloat DynamicTextGlyph::get_left(void) const;

432 10 get_bottom 0 4 819 28 DynamicTextGlyph::get_bottom 0 1 25 99
/**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */
60
inline PN_stdfloat DynamicTextGlyph::get_bottom(void) const;

433 9 get_right 0 4 819 27 DynamicTextGlyph::get_right 0 1 26 99
/**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */
59
inline PN_stdfloat DynamicTextGlyph::get_right(void) const;

434 7 get_top 0 4 819 25 DynamicTextGlyph::get_top 0 1 27 99
/**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */
57
inline PN_stdfloat DynamicTextGlyph::get_top(void) const;

435 11 get_uv_left 0 4 819 29 DynamicTextGlyph::get_uv_left 0 1 28 95
/**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */
61
inline PN_stdfloat DynamicTextGlyph::get_uv_left(void) const;

436 13 get_uv_bottom 0 4 819 31 DynamicTextGlyph::get_uv_bottom 0 1 29 95
/**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */
63
inline PN_stdfloat DynamicTextGlyph::get_uv_bottom(void) const;

437 12 get_uv_right 0 4 819 30 DynamicTextGlyph::get_uv_right 0 1 30 95
/**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */
62
inline PN_stdfloat DynamicTextGlyph::get_uv_right(void) const;

438 10 get_uv_top 0 4 819 28 DynamicTextGlyph::get_uv_top 0 1 31 95
/**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */
60
inline PN_stdfloat DynamicTextGlyph::get_uv_top(void) const;

439 14 get_class_type 0 4 819 32 DynamicTextGlyph::get_class_type 0 1 32 0
57
static TypeHandle DynamicTextGlyph::get_class_type(void);

440 18 upcast_to_TextFont 0 12 823 35 DynamicTextFont::upcast_to_TextFont 0 1 87 39
upcast from DynamicTextFont to TextFont
52
TextFont *DynamicTextFont::upcast_to_TextFont(void);

441 27 downcast_to_DynamicTextFont 0 12 815 37 TextFont::downcast_to_DynamicTextFont 0 0 41
downcast from TextFont to DynamicTextFont
61
DynamicTextFont *TextFont::downcast_to_DynamicTextFont(void);

442 22 upcast_to_FreetypeFont 0 12 823 39 DynamicTextFont::upcast_to_FreetypeFont 0 1 88 43
upcast from DynamicTextFont to FreetypeFont
60
FreetypeFont *DynamicTextFont::upcast_to_FreetypeFont(void);

443 27 downcast_to_DynamicTextFont 0 12 824 41 FreetypeFont::downcast_to_DynamicTextFont 0 0 45
downcast from FreetypeFont to DynamicTextFont
65
DynamicTextFont *FreetypeFont::downcast_to_DynamicTextFont(void);

444 15 DynamicTextFont 0 260 823 32 DynamicTextFont::DynamicTextFont 0 3 39 40 41 324
/**
 * The constructor expects the name of some font file that FreeType can read,
 * along with face_index, indicating which font within the file to load
 * (usually 0).
 */

/**
 * This constructor accepts a table of data representing the font file, loaded
 * from some source other than a filename on disk.
 */

/**
 *
 */
237
DynamicTextFont::DynamicTextFont(Filename const &font_filename, int face_index = 0);
DynamicTextFont::DynamicTextFont(char const *font_data, int data_length, int face_index);
DynamicTextFont::DynamicTextFont(DynamicTextFont const &copy);

445 16 ~DynamicTextFont 0 518 823 33 DynamicTextFont::~DynamicTextFont 0 0 10
/**
 *
 */
48
virtual DynamicTextFont::~DynamicTextFont(void);

446 9 make_copy 0 6 823 26 DynamicTextFont::make_copy 0 1 42 47
/**
 * Returns a new copy of the same font.
 */
69
virtual PointerTo< TextFont > DynamicTextFont::make_copy(void) const;

447 8 get_name 0 4 823 25 DynamicTextFont::get_name 0 1 43 124
/**
 * Disambiguates the get_name() method between that inherited from TextFont
 * and that inherited from FreetypeFont.
 */
64
inline std::string const &DynamicTextFont::get_name(void) const;

448 14 set_point_size 0 4 823 31 DynamicTextFont::set_point_size 0 1 44 295
/**
 * Sets the point size of the font.  This controls the apparent size of the
 * font onscreen.  By convention, a 10 point font is about 1 screen unit high.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
68
inline bool DynamicTextFont::set_point_size(PN_stdfloat point_size);

449 14 get_point_size 0 4 823 31 DynamicTextFont::get_point_size 0 1 45 46
/**
 * Returns the point size of the font.
 */
63
inline PN_stdfloat DynamicTextFont::get_point_size(void) const;

450 19 set_pixels_per_unit 0 4 823 36 DynamicTextFont::set_pixels_per_unit 0 1 46 433
/**
 * Set the resolution of the texture map, and hence the clarity of the
 * resulting font.  This sets the number of pixels in the texture map that are
 * used for each onscreen unit.
 *
 * Setting this number larger results in an easier to read font, but at the
 * cost of more texture memory.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
78
inline bool DynamicTextFont::set_pixels_per_unit(PN_stdfloat pixels_per_unit);

451 19 get_pixels_per_unit 0 4 823 36 DynamicTextFont::get_pixels_per_unit 0 1 47 81
/**
 * Returns the resolution of the texture map.  See set_pixels_per_unit().
 */
68
inline PN_stdfloat DynamicTextFont::get_pixels_per_unit(void) const;

452 16 set_scale_factor 0 4 823 33 DynamicTextFont::set_scale_factor 0 1 48 591
/**
 * Sets the factor by which the font is rendered larger by the FreeType
 * library before being filtered down to its actual size in the texture as
 * specified by set_pixels_per_unit().  This may be set to a number larger
 * than 1.0 to improve the font's antialiasing (since FreeType doesn't really
 * do a swell job of antialiasing by itself).  There is some performance
 * implication for setting this different than 1.0, but it is probably small.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
72
inline bool DynamicTextFont::set_scale_factor(PN_stdfloat scale_factor);

453 16 get_scale_factor 0 4 823 33 DynamicTextFont::get_scale_factor 0 1 49 74
/**
 * Returns the antialiasing scale factor.  See set_scale_factor().
 */
65
inline PN_stdfloat DynamicTextFont::get_scale_factor(void) const;

454 20 set_native_antialias 0 4 823 37 DynamicTextFont::set_native_antialias 0 1 50 469
/**
 * Sets whether the Freetype library's built-in antialias mode is enabled.
 * There are two unrelated ways to achieve antialiasing: with Freetype's
 * native antialias mode, and with the use of a scale_factor greater than one.
 * By default, both modes are enabled.
 *
 * At low resolutions, some fonts may do better with one mode or the other.
 * In general, Freetype's native antialiasing will produce less blurry
 * results, but may introduce more artifacts.
 */
73
inline void DynamicTextFont::set_native_antialias(bool native_antialias);

455 20 get_native_antialias 0 4 823 37 DynamicTextFont::get_native_antialias 0 1 51 105
/**
 * Returns whether Freetype's built-in antialias mode is enabled.  See
 * set_native_antialias().
 */
62
inline bool DynamicTextFont::get_native_antialias(void) const;

456 19 get_font_pixel_size 0 4 823 36 DynamicTextFont::get_font_pixel_size 0 1 52 386
/**
 * This is used to report whether the requested pixel size is being only
 * approximated by a fixed-pixel-size font.  This returns 0 in the normal
 * case, in which a scalable font is used, or the fixed-pixel-size font has
 * exactly the requested pixel size.
 *
 * If this returns non-zero, it is the pixel size of the font that we are
 * using to approximate our desired size.
 */
60
inline int DynamicTextFont::get_font_pixel_size(void) const;

457 15 get_line_height 0 4 823 32 DynamicTextFont::get_line_height 0 1 53 65
/**
 * Returns the number of units high each line of text is.
 */
64
inline PN_stdfloat DynamicTextFont::get_line_height(void) const;

458 17 get_space_advance 0 4 823 34 DynamicTextFont::get_space_advance 0 1 54 55
/**
 * Returns the number of units wide a space is.
 */
66
inline PN_stdfloat DynamicTextFont::get_space_advance(void) const;

459 18 set_texture_margin 0 4 823 35 DynamicTextFont::set_texture_margin 0 1 55 207
/**
 * Sets the number of pixels of padding that is added around the border of
 * each glyph before adding it to the texture map.  This reduces the bleed in
 * from neighboring glyphs in the texture map.
 */
68
inline void DynamicTextFont::set_texture_margin(int texture_margin);

460 18 get_texture_margin 0 4 823 35 DynamicTextFont::get_texture_margin 0 1 56 146
/**
 * Returns the number of pixels of padding that is added around the border of
 * each glyph in the texture map.  See set_texture_margin().
 */
59
inline int DynamicTextFont::get_texture_margin(void) const;

461 15 set_poly_margin 0 4 823 32 DynamicTextFont::set_poly_margin 0 1 57 452
/**
 * Sets the number of pixels of padding that is included around each glyph in
 * the generated polygons.  This helps prevent the edges of the glyphs from
 * being cut off at small minifications.  It is not related to the amount of
 * extra pixels reserved in the texture map (but it should be set somewhat
 * smaller than this number, which is controlled by set_texture_margin(), to
 * prevent bleed-in from neighboring letters in the texture).
 */
70
inline void DynamicTextFont::set_poly_margin(PN_stdfloat poly_margin);

462 15 get_poly_margin 0 4 823 32 DynamicTextFont::get_poly_margin 0 1 58 139
/**
 * Returns the number of pixels of padding that is included around each glyph
 * in the generated polygons.  See set_poly_margin().
 */
64
inline PN_stdfloat DynamicTextFont::get_poly_margin(void) const;

463 13 set_page_size 0 4 823 30 DynamicTextFont::set_page_size 0 2 59 60 182
/**
 * Sets the x, y size of the textures that are created for the
 * DynamicTextFont.
 */

/**
 * Sets the x, y size of the textures that are created for the
 * DynamicTextFont.
 */
140
inline void DynamicTextFont::set_page_size(LVecBase2i const &page_size);
inline void DynamicTextFont::set_page_size(int x_size, int y_size);

464 13 get_page_size 0 4 823 30 DynamicTextFont::get_page_size 0 1 61 109
/**
 * Returns the size of the textures that are created for the DynamicTextFont.
 * See set_page_size().
 */
68
inline LVecBase2i const &DynamicTextFont::get_page_size(void) const;

465 15 get_page_x_size 0 4 823 32 DynamicTextFont::get_page_x_size 0 1 62 112
/**
 * Returns the x size of the textures that are created for the
 * DynamicTextFont.  See set_page_size().
 */
56
inline int DynamicTextFont::get_page_x_size(void) const;

466 15 get_page_y_size 0 4 823 32 DynamicTextFont::get_page_y_size 0 1 63 112
/**
 * Returns the y size of the textures that are created for the
 * DynamicTextFont.  See set_page_size().
 */
56
inline int DynamicTextFont::get_page_y_size(void) const;

467 13 set_minfilter 0 4 823 30 DynamicTextFont::set_minfilter 0 1 64 91
/**
 * Sets the filter type used when minimizing the textures created for this
 * font.
 */
76
inline void DynamicTextFont::set_minfilter(SamplerState::FilterType filter);

468 13 get_minfilter 0 4 823 30 DynamicTextFont::get_minfilter 0 1 65 94
/**
 * Returns the filter type used when minimizing the textures created for this
 * font.
 */
75
inline SamplerState::FilterType DynamicTextFont::get_minfilter(void) const;

469 13 set_magfilter 0 4 823 30 DynamicTextFont::set_magfilter 0 1 66 90
/**
 * Sets the filter type used when enlarging the textures created for this
 * font.
 */
76
inline void DynamicTextFont::set_magfilter(SamplerState::FilterType filter);

470 13 get_magfilter 0 4 823 30 DynamicTextFont::get_magfilter 0 1 67 93
/**
 * Returns the filter type used when enlarging the textures created for this
 * font.
 */
75
inline SamplerState::FilterType DynamicTextFont::get_magfilter(void) const;

471 22 set_anisotropic_degree 0 4 823 39 DynamicTextFont::set_anisotropic_degree 0 1 68 210
/**
 * Enables or disables anisotropic filtering on the textures created for this
 * font.  The default value is specified by the text-anisotropic-degree
 * variable.  See Texture::set_anisotropic_degree().
 */
76
inline void DynamicTextFont::set_anisotropic_degree(int anisotropic_degree);

472 22 get_anisotropic_degree 0 4 823 39 DynamicTextFont::get_anisotropic_degree 0 1 69 118
/**
 * Returns the current anisotropic degree for textures created for this font.
 * See set_anisotropic_degree().
 */
63
inline int DynamicTextFont::get_anisotropic_degree(void) const;

473 15 set_render_mode 0 4 823 32 DynamicTextFont::set_render_mode 0 1 70 211
/**
 * Specifies the way the glyphs on this particular font are generated.  The
 * default is RM_texture, which is the only mode supported for bitmap fonts.
 * Other modes are possible for most modern fonts.
 */
79
inline void DynamicTextFont::set_render_mode(TextFont::RenderMode render_mode);

474 15 get_render_mode 0 4 823 32 DynamicTextFont::get_render_mode 0 1 71 103
/**
 * Returns the way the glyphs on this particular font are generated.  See
 * set_render_mode().
 */
73
inline TextFont::RenderMode DynamicTextFont::get_render_mode(void) const;

475 6 set_fg 0 4 823 23 DynamicTextFont::set_fg 0 1 72 485
/**
 * Changes the color of the foreground pixels of the font as they are rendered
 * into the font texture.  The default is (1, 1, 1, 1), or opaque white, which
 * allows text created with the font to be colored individually.  Normally,
 * you would not change this unless you really need a particular color effect
 * to appear in the font itself.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
54
inline void DynamicTextFont::set_fg(LColor const &fg);

476 6 get_fg 0 4 823 23 DynamicTextFont::get_fg 0 1 73 127
/**
 * Returns the color of the foreground pixels of the font as they are rendered
 * into the font texture.  See set_fg().
 */
57
inline LColor const &DynamicTextFont::get_fg(void) const;

477 6 set_bg 0 4 823 23 DynamicTextFont::set_bg 0 1 74 673
/**
 * Changes the color of the background pixels of the font as they are rendered
 * into the font texture.  The default is (1, 1, 1, 0), or transparent white,
 * which allows text created with the font to be colored individually.  (Note
 * that it should not generally be (0, 0, 0, 0), which would tend to bleed
 * into the foreground color, unless you have also specified a outline color
 * of (0, 0, 0, 1)) .
 *
 * Normally, you would not change this unless you really need a particular
 * color effect to appear in the font itself.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
54
inline void DynamicTextFont::set_bg(LColor const &bg);

478 6 get_bg 0 4 823 23 DynamicTextFont::get_bg 0 1 75 127
/**
 * Returns the color of the background pixels of the font as they are rendered
 * into the font texture.  See set_bg().
 */
57
inline LColor const &DynamicTextFont::get_bg(void) const;

479 11 set_outline 0 4 823 28 DynamicTextFont::set_outline 0 1 76 722
/**
 * Sets up the font to have an outline around each font letter.  This is
 * achieved via a Gaussian post-process as each letter is generated; there is
 * some runtime cost for this effect, but it is minimal as each letter is
 * normally generated only once and then cached.
 *
 * The color is the desired color of the outline, width is the number of
 * points beyond the letter that the outline extends (a typical font is 10
 * points high), and feather is a number in the range 0.0 .. 1.0 that controls
 * the softness of the outline.  Set the width to 0.0 to disable the outline.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */
126
inline void DynamicTextFont::set_outline(LColor const &outline_color, PN_stdfloat outline_width, PN_stdfloat outline_feather);

480 17 get_outline_color 0 4 823 34 DynamicTextFont::get_outline_color 0 1 77 129
/**
 * Returns the color of the outline pixels of the font as they are rendered
 * into the font texture.  See set_outline().
 */
68
inline LColor const &DynamicTextFont::get_outline_color(void) const;

481 17 get_outline_width 0 4 823 34 DynamicTextFont::get_outline_width 0 1 78 130
/**
 * Returns the width of the outline pixels of the font, as the number of
 * points beyond each letter.  See set_outline().
 */
66
inline PN_stdfloat DynamicTextFont::get_outline_width(void) const;

482 19 get_outline_feather 0 4 823 36 DynamicTextFont::get_outline_feather 0 1 79 124
/**
 * Returns the softness of the outline pixels of the font, as a value in the
 * range 0.0 to 1.0. See set_outline().
 */
68
inline PN_stdfloat DynamicTextFont::get_outline_feather(void) const;

483 14 get_tex_format 0 4 823 31 DynamicTextFont::get_tex_format 0 1 80 138
/**
 * Returns the texture format used to render the individual pages.  This is
 * set automatically according to the colors selected.
 */
67
inline Texture::Format DynamicTextFont::get_tex_format(void) const;

484 13 get_num_pages 0 4 823 30 DynamicTextFont::get_num_pages 0 1 81 313
/**
 * Returns the number of pages associated with the font.  Initially, the font
 * has zero pages; when the first piece of text is rendered with the font, it
 * will add additional pages as needed.  Each page is a Texture object that
 * contains the images for each of the glyphs currently in use somewhere.
 */
47
int DynamicTextFont::get_num_pages(void) const;

485 8 get_page 0 4 823 25 DynamicTextFont::get_page 0 1 82 306
/**
 * Returns the nth page associated with the font.  Initially, the font has
 * zero pages; when the first piece of text is rendered with the font, it will
 * add additional pages as needed.  Each page is a Texture object that
 * contains the images for each of the glyphs currently in use somewhere.
 */
56
DynamicTextPage *DynamicTextFont::get_page(int n) const;

486 15 garbage_collect 0 4 823 32 DynamicTextFont::garbage_collect 0 1 83 136
/**
 * Removes all of the glyphs from the font that are no longer being used by
 * any Geoms.  Returns the number of glyphs removed.
 */
43
int DynamicTextFont::garbage_collect(void);

487 5 clear 0 4 823 22 DynamicTextFont::clear 0 1 84 385
/**
 * Drops all the glyphs out of the cache and frees any association with any
 * previously-generated pages.
 *
 * Calling this frequently can result in wasted texture memory, as any
 * previously rendered text will still keep a pointer to the old, previously-
 * generated pages.  As long as the previously rendered text remains around,
 * the old pages will also remain around.
 */
34
void DynamicTextFont::clear(void);

488 5 write 0 6 823 22 DynamicTextFont::write 0 1 85 10
/**
 *
 */
79
virtual void DynamicTextFont::write(std::ostream &out, int indent_level) const;

489 14 get_class_type 0 4 823 31 DynamicTextFont::get_class_type 0 1 86 0
56
static TypeHandle DynamicTextFont::get_class_type(void);

490 8 has_font 0 4 834 18 FontPool::has_font 0 1 89 74
/**
 * Returns true if the font has ever been loaded, false otherwise.
 */
67
static inline bool FontPool::has_font(std::string const &filename);

491 11 verify_font 0 4 834 21 FontPool::verify_font 0 1 90 299
/**
 * Loads the given filename up into a font, if it has not already been loaded,
 * and returns true to indicate success, or false to indicate failure.  If
 * this returns true, it is guaranteed that a subsequent call to load_font()
 * with the same font name will return a valid Font pointer.
 */
70
static inline bool FontPool::verify_font(std::string const &filename);

492 9 load_font 0 4 834 19 FontPool::load_font 0 1 91 253
/**
 * Loads the given filename up into a font, if it has not already been loaded,
 * and returns the new font.  If a font with the same filename was previously
 * loaded, returns that one instead.  If the font file cannot be found,
 * returns NULL.
 */
73
static inline TextFont *FontPool::load_font(std::string const &filename);

493 8 add_font 0 4 834 18 FontPool::add_font 0 1 92 162
/**
 * Adds the indicated already-loaded font to the pool.  The font will always
 * replace any previously-loaded font in the pool that had the same filename.
 */
83
static inline void FontPool::add_font(std::string const &filename, TextFont *font);

494 12 release_font 0 4 834 22 FontPool::release_font 0 1 93 261
/**
 * Removes the indicated font from the pool, indicating it will never be
 * loaded again; the font may then be freed.  If this function is never
 * called, a reference count will be maintained on every font ever loaded,
 * and fonts will never be freed.
 */
71
static inline void FontPool::release_font(std::string const &filename);

495 17 release_all_fonts 0 4 834 27 FontPool::release_all_fonts 0 1 94 83
/**
 * Releases all fonts in the pool and restores the pool to the empty state.
 */
53
static inline void FontPool::release_all_fonts(void);

496 15 garbage_collect 0 4 834 25 FontPool::garbage_collect 0 1 95 203
/**
 * Releases only those fonts in the pool that have a reference count of
 * exactly 1; i.e.  only those fonts that are not being used outside of the
 * pool.  Returns the number of fonts released.
 */
50
static inline int FontPool::garbage_collect(void);

497 13 list_contents 0 4 834 23 FontPool::list_contents 0 1 96 78
/**
 * Lists the contents of the font pool to the indicated output stream.
 */
62
static inline void FontPool::list_contents(std::ostream &out);

498 5 write 0 4 834 15 FontPool::write 0 1 97 78
/**
 * Lists the contents of the font pool to the indicated output stream.
 */
47
static void FontPool::write(std::ostream &out);

499 9 ~FontPool 0 516 834 19 FontPool::~FontPool 0 0 0
26
FontPool::~FontPool(void);

500 14 get_class_type 0 4 835 29 GeomTextGlyph::get_class_type 0 1 98 0
54
static TypeHandle GeomTextGlyph::get_class_type(void);

501 14 StaticTextFont 0 260 837 30 StaticTextFont::StaticTextFont 0 1 99 374
/**
 * The constructor expects the root node to a model generated via egg-mkfont,
 * which consists of a set of models, one per each character in the font.
 *
 * If a CoordinateSystem value is specified, it informs the font of the
 * coordinate system in which this model was generated.  "up" in this
 * coordinate system will be the direction of the top of the letters.
 */
88
StaticTextFont::StaticTextFont(PandaNode *font_def, CoordinateSystem cs = ::CS_default);

502 14 get_class_type 0 4 837 30 StaticTextFont::get_class_type 0 1 100 0
55
static TypeHandle StaticTextFont::get_class_type(void);

503 15 ~StaticTextFont 0 516 837 31 StaticTextFont::~StaticTextFont 0 0 0
38
StaticTextFont::~StaticTextFont(void);

504 14 TextProperties 0 260 838 30 TextProperties::TextProperties 0 2 101 102 22
/**
 *
 */

/**
 *
 */
97
TextProperties::TextProperties(void);
TextProperties::TextProperties(TextProperties const &copy);

505 10 operator = 0 4 838 26 TextProperties::operator = 0 1 103 0
60
void TextProperties::operator =(TextProperties const &copy);

506 11 operator == 0 4 838 27 TextProperties::operator == 0 1 104 0
68
bool TextProperties::operator ==(TextProperties const &other) const;

507 11 operator != 0 4 838 27 TextProperties::operator != 0 1 105 0
75
inline bool TextProperties::operator !=(TextProperties const &other) const;

508 5 clear 0 4 838 21 TextProperties::clear 0 1 106 136
/**
 * Unsets all properties that have been specified so far, and resets the
 * TextProperties structure to its initial empty state.
 */
33
void TextProperties::clear(void);

509 16 is_any_specified 0 4 838 32 TextProperties::is_any_specified 0 1 107 79
/**
 * Returns true if any properties have been specified, false otherwise.
 */
57
inline bool TextProperties::is_any_specified(void) const;

510 16 set_default_font 0 4 838 32 TextProperties::set_default_font 0 1 108 122
/**
 * Specifies the default font to be used for any TextNode whose font is
 * uninitialized or NULL.  See set_font().
 */
64
static inline void TextProperties::set_default_font(TextFont *);

511 16 get_default_font 0 4 838 32 TextProperties::get_default_font 0 1 109 122
/**
 * Specifies the default font to be used for any TextNode whose font is
 * uninitialized or NULL.  See set_font().
 */
63
static inline TextFont *TextProperties::get_default_font(void);

512 8 set_font 0 4 838 24 TextProperties::set_font 0 1 110 160
/**
 * Sets the font that will be used when making text.  If this is set to NULL,
 * the default font will be used, which can be set via set_default_font().
 */
53
inline void TextProperties::set_font(TextFont *font);

513 10 clear_font 0 4 838 26 TextProperties::clear_font 0 1 111 49
/**
 * Restores the default font to the text.
 */
45
inline void TextProperties::clear_font(void);

514 8 has_font 0 4 838 24 TextProperties::has_font 0 1 112 10
/**
 *
 */
49
inline bool TextProperties::has_font(void) const;

515 8 get_font 0 4 838 24 TextProperties::get_font 0 1 113 110
/**
 * Returns the font currently in use, if any.  If no font is in use, this
 * returns the default font.
 */
54
inline TextFont *TextProperties::get_font(void) const;

516 14 set_small_caps 0 4 838 30 TextProperties::set_small_caps 0 1 114 707
/**
 * Sets the small_caps flag.  When this is set, lowercase letters are
 * generated as scaled-down versions of their uppercase equivalents.  This is
 * particularly useful to set for fonts that do not have lowercase letters.
 *
 * It is also a good idea to set this for a (dynamic) font that has already
 * implemented lowercase letters as scaled-down versions of their uppercase
 * equivalents, since without this flag the texture memory may needlessly
 * duplicate equivalent glyphs for upper and lowercase letters.  Setting this
 * flag causes the texture memory to share the mixed-case letters.
 *
 * The amount by which the lowercase letters are scaled is specified by
 * set_small_caps_scale().
 */
60
inline void TextProperties::set_small_caps(bool small_caps);

517 16 clear_small_caps 0 4 838 32 TextProperties::clear_small_caps 0 1 115 10
/**
 *
 */
51
inline void TextProperties::clear_small_caps(void);

518 14 has_small_caps 0 4 838 30 TextProperties::has_small_caps 0 1 116 10
/**
 *
 */
55
inline bool TextProperties::has_small_caps(void) const;

519 14 get_small_caps 0 4 838 30 TextProperties::get_small_caps 0 1 117 62
/**
 * Returns the small_caps flag.  See set_small_caps().
 */
55
inline bool TextProperties::get_small_caps(void) const;

520 20 set_small_caps_scale 0 4 838 36 TextProperties::set_small_caps_scale 0 1 118 210
/**
 * Sets the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps().
 * Normally, this will be a number less than one.
 */
79
inline void TextProperties::set_small_caps_scale(PN_stdfloat small_caps_scale);

521 22 clear_small_caps_scale 0 4 838 38 TextProperties::clear_small_caps_scale 0 1 119 10
/**
 *
 */
57
inline void TextProperties::clear_small_caps_scale(void);

522 20 has_small_caps_scale 0 4 838 36 TextProperties::has_small_caps_scale 0 1 120 10
/**
 *
 */
61
inline bool TextProperties::has_small_caps_scale(void) const;

523 20 get_small_caps_scale 0 4 838 36 TextProperties::get_small_caps_scale 0 1 121 193
/**
 * Returns the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps()
 * and set_small_caps_scale().
 */
68
inline PN_stdfloat TextProperties::get_small_caps_scale(void) const;

524 9 set_slant 0 4 838 25 TextProperties::set_slant 0 1 122 70
/**
 * Specifies the factor by which the text slants to the right.
 */
57
inline void TextProperties::set_slant(PN_stdfloat slant);

525 11 clear_slant 0 4 838 27 TextProperties::clear_slant 0 1 123 10
/**
 *
 */
46
inline void TextProperties::clear_slant(void);

526 9 has_slant 0 4 838 25 TextProperties::has_slant 0 1 124 10
/**
 *
 */
50
inline bool TextProperties::has_slant(void) const;

527 9 get_slant 0 4 838 25 TextProperties::get_slant 0 1 125 83
/**
 * Returns the factor by which the text is specified to slant to the right.
 */
57
inline PN_stdfloat TextProperties::get_slant(void) const;

528 14 set_underscore 0 4 838 30 TextProperties::set_underscore 0 1 126 169
/**
 * Sets the underscore flag.  When this is set, the text is underscored with a
 * one-pixel line the same color as the text foreground, drawn at the
 * baseline.
 */
60
inline void TextProperties::set_underscore(bool underscore);

529 16 clear_underscore 0 4 838 32 TextProperties::clear_underscore 0 1 127 10
/**
 *
 */
51
inline void TextProperties::clear_underscore(void);

530 14 has_underscore 0 4 838 30 TextProperties::has_underscore 0 1 128 10
/**
 *
 */
55
inline bool TextProperties::has_underscore(void) const;

531 14 get_underscore 0 4 838 30 TextProperties::get_underscore 0 1 129 62
/**
 * Returns the underscore flag.  See set_underscore().
 */
55
inline bool TextProperties::get_underscore(void) const;

532 21 set_underscore_height 0 4 838 37 TextProperties::set_underscore_height 0 1 130 176
/**
 * Specifies the vertical height of the underscore, relative to the text
 * baseline.  This only has meaning if the underscore mode is enabled with
 * set_underscore().
 */
81
inline void TextProperties::set_underscore_height(PN_stdfloat underscore_height);

533 23 clear_underscore_height 0 4 838 39 TextProperties::clear_underscore_height 0 1 131 10
/**
 *
 */
58
inline void TextProperties::clear_underscore_height(void);

534 21 has_underscore_height 0 4 838 37 TextProperties::has_underscore_height 0 1 132 10
/**
 *
 */
62
inline bool TextProperties::has_underscore_height(void) const;

535 21 get_underscore_height 0 4 838 37 TextProperties::get_underscore_height 0 1 133 86
/**
 * Returns the vertical height of the underscore; see set_underscore_height().
 */
69
inline PN_stdfloat TextProperties::get_underscore_height(void) const;

536 9 set_align 0 4 838 25 TextProperties::set_align 0 1 134 66
/**
 * Specifies the alignment of the text within its margins.
 */
76
inline void TextProperties::set_align(TextProperties::Alignment align_type);

537 11 clear_align 0 4 838 27 TextProperties::clear_align 0 1 135 54
/**
 * Restores the default alignment of the text.
 */
46
inline void TextProperties::clear_align(void);

538 9 has_align 0 4 838 25 TextProperties::has_align 0 1 136 10
/**
 *
 */
50
inline bool TextProperties::has_align(void) const;

539 9 get_align 0 4 838 25 TextProperties::get_align 0 1 137 10
/**
 *
 */
71
inline TextProperties::Alignment TextProperties::get_align(void) const;

540 10 set_indent 0 4 838 26 TextProperties::set_indent 0 1 138 149
/**
 * Specifies the amount of extra space that is inserted before the first
 * character of each line.  This can be thought of as a left margin.
 */
59
inline void TextProperties::set_indent(PN_stdfloat indent);

541 12 clear_indent 0 4 838 28 TextProperties::clear_indent 0 1 139 84
/**
 * Removes the indent setting from the text.  Text will be as wide as it is.
 */
47
inline void TextProperties::clear_indent(void);

542 10 has_indent 0 4 838 26 TextProperties::has_indent 0 1 140 10
/**
 *
 */
51
inline bool TextProperties::has_indent(void) const;

543 10 get_indent 0 4 838 26 TextProperties::get_indent 0 1 141 10
/**
 *
 */
58
inline PN_stdfloat TextProperties::get_indent(void) const;

544 12 set_wordwrap 0 4 838 28 TextProperties::set_wordwrap 0 1 142 152
/**
 * Sets the text up to automatically wordwrap when it exceeds the indicated
 * width.  This can be thought of as a right margin or margin width.
 */
63
inline void TextProperties::set_wordwrap(PN_stdfloat wordwrap);

545 14 clear_wordwrap 0 4 838 30 TextProperties::clear_wordwrap 0 1 143 86
/**
 * Removes the wordwrap setting from the text.  Text will be as wide as it is.
 */
49
inline void TextProperties::clear_wordwrap(void);

546 12 has_wordwrap 0 4 838 28 TextProperties::has_wordwrap 0 1 144 10
/**
 *
 */
53
inline bool TextProperties::has_wordwrap(void) const;

547 12 get_wordwrap 0 4 838 28 TextProperties::get_wordwrap 0 1 145 10
/**
 *
 */
60
inline PN_stdfloat TextProperties::get_wordwrap(void) const;

548 32 set_preserve_trailing_whitespace 0 4 838 48 TextProperties::set_preserve_trailing_whitespace 0 1 146 344
/**
 * Sets the preserve_trailing_whitespace flag.  When this is set, trailing
 * whitespace at the end of the line is not stripped when the text is
 * wordwrapped (it is stripped by default).  Since the trailing whitespace is
 * invisible, this is important primarily for determining the proper width of
 * a frame or card behind the text.
 */
96
inline void TextProperties::set_preserve_trailing_whitespace(bool preserve_trailing_whitespace);

549 34 clear_preserve_trailing_whitespace 0 4 838 50 TextProperties::clear_preserve_trailing_whitespace 0 1 147 10
/**
 *
 */
69
inline void TextProperties::clear_preserve_trailing_whitespace(void);

550 32 has_preserve_trailing_whitespace 0 4 838 48 TextProperties::has_preserve_trailing_whitespace 0 1 148 10
/**
 *
 */
73
inline bool TextProperties::has_preserve_trailing_whitespace(void) const;

551 32 get_preserve_trailing_whitespace 0 4 838 48 TextProperties::get_preserve_trailing_whitespace 0 1 149 101
/**
 * Returns the preserve_trailing_whitespace flag.  See
 * set_preserve_trailing_whitespace().
 */
73
inline bool TextProperties::get_preserve_trailing_whitespace(void) const;

552 14 set_text_color 0 4 838 30 TextProperties::set_text_color 0 2 150 151 456
/**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */

/**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */
173
inline void TextProperties::set_text_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextProperties::set_text_color(LColor const &text_color);

553 16 clear_text_color 0 4 838 32 TextProperties::clear_text_color 0 1 152 117
/**
 * Removes the text color specification; the text will be colored whatever it
 * was in the source font file.
 */
51
inline void TextProperties::clear_text_color(void);

554 14 has_text_color 0 4 838 30 TextProperties::has_text_color 0 1 153 76
/**
 * Returns true if a text color was specified with set_text_color().
 */
55
inline bool TextProperties::has_text_color(void) const;

555 14 get_text_color 0 4 838 30 TextProperties::get_text_color 0 1 154 10
/**
 *
 */
57
inline LColor TextProperties::get_text_color(void) const;

556 16 set_shadow_color 0 4 838 32 TextProperties::set_shadow_color 0 2 155 156 22
/**
 *
 */

/**
 *
 */
179
inline void TextProperties::set_shadow_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextProperties::set_shadow_color(LColor const &shadow_color);

557 18 clear_shadow_color 0 4 838 34 TextProperties::clear_shadow_color 0 1 157 50
/**
 * Removes the shadow color specification.
 */
53
inline void TextProperties::clear_shadow_color(void);

558 16 has_shadow_color 0 4 838 32 TextProperties::has_shadow_color 0 1 158 10
/**
 *
 */
57
inline bool TextProperties::has_shadow_color(void) const;

559 16 get_shadow_color 0 4 838 32 TextProperties::get_shadow_color 0 1 159 10
/**
 *
 */
59
inline LColor TextProperties::get_shadow_color(void) const;

560 10 set_shadow 0 4 838 26 TextProperties::set_shadow 0 2 160 161 304
/**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */

/**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */
153
inline void TextProperties::set_shadow(PN_stdfloat xoffset, PN_stdfloat yoffset);
inline void TextProperties::set_shadow(LVecBase2 const &shadow_offset);

561 12 clear_shadow 0 4 838 28 TextProperties::clear_shadow 0 1 162 69
/**
 * Specifies that a shadow will not be drawn behind the text.
 */
47
inline void TextProperties::clear_shadow(void);

562 10 has_shadow 0 4 838 26 TextProperties::has_shadow 0 1 163 10
/**
 *
 */
51
inline bool TextProperties::has_shadow(void) const;

563 10 get_shadow 0 4 838 26 TextProperties::get_shadow 0 1 164 125
/**
 * Returns the offset of the shadow as set by set_shadow().  It is an error to
 * call this if has_shadow() is false.
 */
55
inline LVector2 TextProperties::get_shadow(void) const;

564 7 set_bin 0 4 838 23 TextProperties::set_bin 0 1 165 350
/**
 * Names the CullBin that the text geometry should be assigned to.  If this is
 * set, then a CullBinAttrib will be created to explicitly place each
 * component in the named bin.
 *
 * The draw_order value will also be passed to each CullBinAttrib as
 * appropriate; this is particularly useful if this names a CullBinFixed, e.g.
 * "fixed".
 */
60
inline void TextProperties::set_bin(std::string const &bin);

565 9 clear_bin 0 4 838 25 TextProperties::clear_bin 0 1 166 158
/**
 * Removes the effect of a previous call to set_bin().  Text will be drawn in
 * whatever bin it would like to be drawn in, with no explicit ordering.
 */
44
inline void TextProperties::clear_bin(void);

566 7 has_bin 0 4 838 23 TextProperties::has_bin 0 1 167 98
/**
 * Returns true if an explicit drawing bin has been set via set_bin(), false
 * otherwise.
 */
48
inline bool TextProperties::has_bin(void) const;

567 7 get_bin 0 4 838 23 TextProperties::get_bin 0 1 168 97
/**
 * Returns the drawing bin set with set_bin(), or empty string if no bin has
 * been set.
 */
62
inline std::string const &TextProperties::get_bin(void) const;

568 14 set_draw_order 0 4 838 30 TextProperties::set_draw_order 0 1 169 435
/**
 * Sets the drawing order of text created by the TextNode.  This is actually
 * the draw order of the card and frame.  The shadow is drawn at
 * _draw_order+1, and the text at _draw_order+2.
 *
 * This affects the sorting order assigned to the nodes as they are created,
 * and also is passed to whatever bin may be assigned via set_bin().
 *
 * The return value is the first unused draw_order number, e.g.  _draw_order +
 * 3.
 */
58
inline int TextProperties::set_draw_order(int draw_order);

569 16 clear_draw_order 0 4 838 32 TextProperties::clear_draw_order 0 1 170 10
/**
 *
 */
51
inline void TextProperties::clear_draw_order(void);

570 14 has_draw_order 0 4 838 30 TextProperties::has_draw_order 0 1 171 10
/**
 *
 */
55
inline bool TextProperties::has_draw_order(void) const;

571 14 get_draw_order 0 4 838 30 TextProperties::get_draw_order 0 1 172 63
/**
 * Returns the drawing order set with set_draw_order().
 */
54
inline int TextProperties::get_draw_order(void) const;

572 13 set_tab_width 0 4 838 29 TextProperties::set_tab_width 0 1 173 160
/**
 * Sets the width of each tab stop, in screen units.  A tab character embedded
 * in the text will advance the horizontal position to the next tab stop.
 */
65
inline void TextProperties::set_tab_width(PN_stdfloat tab_width);

573 15 clear_tab_width 0 4 838 31 TextProperties::clear_tab_width 0 1 174 10
/**
 *
 */
50
inline void TextProperties::clear_tab_width(void);

574 13 has_tab_width 0 4 838 29 TextProperties::has_tab_width 0 1 175 10
/**
 *
 */
54
inline bool TextProperties::has_tab_width(void) const;

575 13 get_tab_width 0 4 838 29 TextProperties::get_tab_width 0 1 176 53
/**
 * Returns the width set via set_tab_width().
 */
61
inline PN_stdfloat TextProperties::get_tab_width(void) const;

576 15 set_glyph_scale 0 4 838 31 TextProperties::set_glyph_scale 0 1 177 576
/**
 * Specifies the factor by which to scale each letter of the text as it is
 * placed, in addition to any scales inherited from the node or from
 * set_text_scale(). This can be used (possibly in conjunction with
 * set_glyph_shift()) to implement superscripting or subscripting.
 *
 * The glyph scale is cumulative when applied to nested TextProperties.  It is
 * intended primarily for implementing superscripts, not for scaling the text
 * in general.  See also set_text_scale(), which is intended primarily for
 * scaling the text in general, and is not cumulative.
 */
69
inline void TextProperties::set_glyph_scale(PN_stdfloat glyph_scale);

577 17 clear_glyph_scale 0 4 838 33 TextProperties::clear_glyph_scale 0 1 178 10
/**
 *
 */
52
inline void TextProperties::clear_glyph_scale(void);

578 15 has_glyph_scale 0 4 838 31 TextProperties::has_glyph_scale 0 1 179 10
/**
 *
 */
56
inline bool TextProperties::has_glyph_scale(void) const;

579 15 get_glyph_scale 0 4 838 31 TextProperties::get_glyph_scale 0 1 180 85
/**
 * Returns the scale factor of each letter as specified by set_glyph_scale().
 */
63
inline PN_stdfloat TextProperties::get_glyph_scale(void) const;

580 15 set_glyph_shift 0 4 838 31 TextProperties::set_glyph_shift 0 1 181 206
/**
 * Specifies a vertical amount to shift each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_scale())
 * to implement superscripting or subscripting.
 */
69
inline void TextProperties::set_glyph_shift(PN_stdfloat glyph_shift);

581 17 clear_glyph_shift 0 4 838 33 TextProperties::clear_glyph_shift 0 1 182 10
/**
 *
 */
52
inline void TextProperties::clear_glyph_shift(void);

582 15 has_glyph_shift 0 4 838 31 TextProperties::has_glyph_shift 0 1 183 10
/**
 *
 */
56
inline bool TextProperties::has_glyph_shift(void) const;

583 15 get_glyph_shift 0 4 838 31 TextProperties::get_glyph_shift 0 1 184 90
/**
 * Returns the vertical shift of each letter as specified by
 * set_glyph_shift().
 */
63
inline PN_stdfloat TextProperties::get_glyph_shift(void) const;

584 14 set_text_scale 0 4 838 30 TextProperties::set_text_scale 0 1 185 284
/**
 * Specifies the factor by which to scale the text, in addition to any
 * scalings imposed by the node, as well as in addition to the glyph scale.
 *
 * The text scale is not cumulative when applied to nested TextProperties.
 * See also set_glyph_scale(), which is cumulative.
 */
67
inline void TextProperties::set_text_scale(PN_stdfloat text_scale);

585 16 clear_text_scale 0 4 838 32 TextProperties::clear_text_scale 0 1 186 10
/**
 *
 */
51
inline void TextProperties::clear_text_scale(void);

586 14 has_text_scale 0 4 838 30 TextProperties::has_text_scale 0 1 187 10
/**
 *
 */
55
inline bool TextProperties::has_text_scale(void) const;

587 14 get_text_scale 0 4 838 30 TextProperties::get_text_scale 0 1 188 81
/**
 * Returns the scale factor of the text as specified by set_text_scale().
 */
62
inline PN_stdfloat TextProperties::get_text_scale(void) const;

588 13 set_direction 0 4 838 29 TextProperties::set_direction 0 1 189 142
/**
 * Specifies the text direction.  If none is specified, it will be guessed
 * based on the contents of the string.
 *
 * @since 1.10.0
 */
79
inline void TextProperties::set_direction(TextProperties::Direction direction);

589 15 clear_direction 0 4 838 31 TextProperties::clear_direction 0 1 190 160
/**
 * Clears the text direction setting.  If no text direction is specified, it
 * will be guessed based on the contents of the string.
 *
 * @since 1.10.0
 */
50
inline void TextProperties::clear_direction(void);

590 13 has_direction 0 4 838 29 TextProperties::has_direction 0 1 191 24
/**
 * @since 1.10.0
 */
54
inline bool TextProperties::has_direction(void) const;

591 13 get_direction 0 4 838 29 TextProperties::get_direction 0 1 192 97
/**
 * Returns the direction of the text as specified by set_direction().
 *
 * @since 1.10.0
 */
75
inline TextProperties::Direction TextProperties::get_direction(void) const;

592 14 add_properties 0 4 838 30 TextProperties::add_properties 0 1 193 123
/**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */
65
void TextProperties::add_properties(TextProperties const &other);

593 5 write 0 4 838 21 TextProperties::write 0 1 194 10
/**
 *
 */
74
void TextProperties::write(std::ostream &out, int indent_level = 0) const;

594 14 get_class_type 0 4 838 30 TextProperties::get_class_type 0 1 195 0
55
static TypeHandle TextProperties::get_class_type(void);

595 15 ~TextProperties 0 516 838 31 TextProperties::~TextProperties 0 0 0
38
TextProperties::~TextProperties(void);

596 11 TextGraphic 0 260 847 24 TextGraphic::TextGraphic 0 4 196 197 198 199 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
330
inline TextGraphic::TextGraphic(void);
inline explicit TextGraphic::TextGraphic(NodePath const &model, LVecBase4 const &frame);
inline explicit TextGraphic::TextGraphic(NodePath const &model, PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);
inline TextGraphic::TextGraphic(TextGraphic const &) = default;

597 9 get_model 0 4 847 22 TextGraphic::get_model 0 1 200 95
/**
 * Returns the NodePath associated with the graphic, that renders the desired
 * image.
 */
51
inline NodePath TextGraphic::get_model(void) const;

598 9 set_model 0 4 847 22 TextGraphic::set_model 0 1 201 146
/**
 * Changes the NodePath associated with the graphic.  This NodePath should
 * contain geometry that will render the desired graphic image.
 */
58
inline void TextGraphic::set_model(NodePath const &model);

599 9 get_frame 0 4 847 22 TextGraphic::get_frame 0 1 202 457
/**
 * Returns the frame specified for the graphic.  This is the amount of space
 * that will be reserved for the graphic when it is embedded in a text
 * paragraph, in the form (left, right, bottom, top).
 *
 * The actual graphic, as rendered by the NodePath specified via set_model(),
 * should more or less fit within this rectangle.  It is not required to fit
 * completely within it, but if it does not, it may visually overlap with
 * nearby text.
 */
52
inline LVecBase4 TextGraphic::get_frame(void) const;

600 9 set_frame 0 4 847 22 TextGraphic::set_frame 0 2 203 204 208
/**
 * Specifies the (left, right, bottom, top) bounding frame for the graphic.
 * See get_frame().
 */

/**
 * Specifies the (left, right, bottom, top) bounding frame for the graphic.
 * See get_frame().
 */
169
inline void TextGraphic::set_frame(LVecBase4 const &frame);
inline void TextGraphic::set_frame(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);

601 17 get_instance_flag 0 4 847 30 TextGraphic::get_instance_flag 0 1 205 63
/**
 * Returns the instance_flag.  See set_instance_flag().
 */
55
inline bool TextGraphic::get_instance_flag(void) const;

602 17 set_instance_flag 0 4 847 30 TextGraphic::set_instance_flag 0 1 206 355
/**
 * Sets the instance_flag.  When this is true, the graphic is directly
 * instanced to the scene graph whenever it appears; when it is false, the
 * graphic is copied.  The default is false, which is best for most
 * applications.  You might need to set it true for special kinds of
 * "graphics" like interactive elements, for instance a PGEntry.
 */
63
inline void TextGraphic::set_instance_flag(bool instance_flag);

603 12 ~TextGraphic 0 516 847 25 TextGraphic::~TextGraphic 0 0 0
32
TextGraphic::~TextGraphic(void);

604 14 set_properties 0 4 850 37 TextPropertiesManager::set_properties 0 1 207 377
/**
 * Defines the TextProperties associated with the indicated name.  When the
 * name is subsequently encountered in text embedded between \1 characters in
 * a TextNode string, the following text will be rendered with these
 * properties.
 *
 * If there was already a TextProperties structure associated with this name,
 * it is quietly replaced with the new definition.
 */
102
void TextPropertiesManager::set_properties(std::string const &name, TextProperties const &properties);

605 14 get_properties 0 4 850 37 TextPropertiesManager::get_properties 0 1 208 355
/**
 * Returns the TextProperties associated with the indicated name.  If there
 * was not previously a TextProperties associated with this name, a warning is
 * printed and then a default TextProperties structure is associated with the
 * name, and returned.
 *
 * Call has_properties() instead to check whether a particular name has been
 * defined.
 */
78
TextProperties TextPropertiesManager::get_properties(std::string const &name);

606 14 has_properties 0 4 850 37 TextPropertiesManager::has_properties 0 1 209 372
/**
 * Returns true if a TextProperties structure has been associated with the
 * indicated name, false otherwise.  Normally this means set_properties() has
 * been called with this name, but because get_properties() will implicitly
 * create a default TextProperties structure, it may also mean simply that
 * get_properties() has been called with the indicated name.
 */
74
bool TextPropertiesManager::has_properties(std::string const &name) const;

607 16 clear_properties 0 4 850 39 TextPropertiesManager::clear_properties 0 1 210 71
/**
 * Removes the named TextProperties structure from the manager.
 */
70
void TextPropertiesManager::clear_properties(std::string const &name);

608 11 set_graphic 0 4 850 34 TextPropertiesManager::set_graphic 0 2 211 212 651
/**
 * Defines the TextGraphic associated with the indicated name.  When the name
 * is subsequently encountered in text embedded between \5 characters in a
 * TextNode string, the specified graphic will be embedded in the text at that
 * point.
 *
 * If there was already a TextGraphic structure associated with this name, it
 * is quietly replaced with the new definition.
 */

/**
 * This flavor of set_graphic implicitly creates a frame for the model using
 * the model's actual computed bounding volume, as derived from
 * NodePath::calc_tight_bounds().  Create a TextGraphic object first if you
 * want to have explicit control of the frame.
 */
182
void TextPropertiesManager::set_graphic(std::string const &name, TextGraphic const &graphic);
void TextPropertiesManager::set_graphic(std::string const &name, NodePath const &model);

609 11 get_graphic 0 4 850 34 TextPropertiesManager::get_graphic 0 1 213 343
/**
 * Returns the TextGraphic associated with the indicated name.  If there was
 * not previously a TextGraphic associated with this name, a warning is
 * printed and then a default TextGraphic structure is associated with the
 * name, and returned.
 *
 * Call has_graphic() instead to check whether a particular name has been
 * defined.
 */
72
TextGraphic TextPropertiesManager::get_graphic(std::string const &name);

610 11 has_graphic 0 4 850 34 TextPropertiesManager::has_graphic 0 1 214 357
/**
 * Returns true if a TextGraphic structure has been associated with the
 * indicated name, false otherwise.  Normally this means set_graphic() has
 * been called with this name, but because get_graphic() will implicitly
 * create a default TextGraphic structure, it may also mean simply that
 * get_graphic() has been called with the indicated name.
 */
71
bool TextPropertiesManager::has_graphic(std::string const &name) const;

611 13 clear_graphic 0 4 850 36 TextPropertiesManager::clear_graphic 0 1 215 68
/**
 * Removes the named TextGraphic structure from the manager.
 */
67
void TextPropertiesManager::clear_graphic(std::string const &name);

612 5 write 0 4 850 28 TextPropertiesManager::write 0 1 216 10
/**
 *
 */
81
void TextPropertiesManager::write(std::ostream &out, int indent_level = 0) const;

613 14 get_global_ptr 0 4 850 37 TextPropertiesManager::get_global_ptr 0 1 217 74
/**
 * Returns the pointer to the global TextPropertiesManager object.
 */
74
static TextPropertiesManager *TextPropertiesManager::get_global_ptr(void);

614 13 TextAssembler 0 260 851 28 TextAssembler::TextAssembler 0 2 218 219 22
/**
 *
 */

/**
 *
 */
117
explicit TextAssembler::TextAssembler(TextEncoder *encoder);
TextAssembler::TextAssembler(TextAssembler const &copy);

615 10 operator = 0 4 851 25 TextAssembler::operator = 0 1 220 0
58
void TextAssembler::operator =(TextAssembler const &copy);

616 14 ~TextAssembler 0 516 851 29 TextAssembler::~TextAssembler 0 0 10
/**
 *
 */
36
TextAssembler::~TextAssembler(void);

617 5 clear 0 4 851 20 TextAssembler::clear 0 1 221 59
/**
 * Reinitializes the contents of the TextAssembler.
 */
32
void TextAssembler::clear(void);

618 14 set_usage_hint 0 4 851 29 TextAssembler::set_usage_hint 0 1 222 278
/**
 * Specifies the UsageHint that will be applied to generated geometry.  The
 * default is UH_static, which is probably the right setting, but if you know
 * the TextNode's geometry will have a short lifespan, it may be better to set
 * it to UH_stream.  See geomEnums.h.
 */
75
inline void TextAssembler::set_usage_hint(GeomEnums::UsageHint usage_hint);

619 14 get_usage_hint 0 4 851 29 TextAssembler::get_usage_hint 0 1 223 102
/**
 * Returns the UsageHint that will be applied to generated geometry.  See
 * set_usage_hint().
 */
70
inline GeomEnums::UsageHint TextAssembler::get_usage_hint(void) const;

620 12 set_max_rows 0 4 851 27 TextAssembler::set_max_rows 0 1 224 264
/**
 * If max_rows is greater than zero, no more than max_rows will be accepted.
 * Text beyond that will be truncated.
 *
 * Setting this will not truncate text immediately.  You must follow this up
 * with a call to set_wtext() to truncate the existing text.
 */
54
inline void TextAssembler::set_max_rows(int max_rows);

621 12 get_max_rows 0 4 851 27 TextAssembler::get_max_rows 0 1 225 123
/**
 * If max_rows is greater than zero, no more than max_rows will be accepted.
 * Text beyond that will be truncated.
 */
51
inline int TextAssembler::get_max_rows(void) const;

622 17 set_dynamic_merge 0 4 851 32 TextAssembler::set_dynamic_merge 0 1 226 75
/**
 * Sets the dynamic_merge flag.  See TextNode::set_flatten_flags().
 */
65
inline void TextAssembler::set_dynamic_merge(bool dynamic_merge);

623 17 get_dynamic_merge 0 4 851 32 TextAssembler::get_dynamic_merge 0 1 227 78
/**
 * Returns the dynamic_merge flag.  See TextNode::set_flatten_flags().
 */
57
inline bool TextAssembler::get_dynamic_merge(void) const;

624 18 set_multiline_mode 0 4 851 33 TextAssembler::set_multiline_mode 0 1 228 112
/**
 * Sets the multiline mode flag.  Set the multiline mode to allow text to
 * wrap.  It defaults to true.
 */
57
inline void TextAssembler::set_multiline_mode(bool flag);

625 18 get_multiline_mode 0 4 851 33 TextAssembler::get_multiline_mode 0 1 229 79
/**
 * Returns the multline_mode flag.  See TextNode::set_multiline_mode().
 */
58
inline bool TextAssembler::get_multiline_mode(void) const;

626 14 set_properties 0 4 851 29 TextAssembler::set_properties 0 1 230 135
/**
 * Specifies the default TextProperties that are applied to the text in the
 * absence of any nested property change sequences.
 */
76
inline void TextAssembler::set_properties(TextProperties const &properties);

627 14 get_properties 0 4 851 29 TextAssembler::get_properties 0 3 231 232 233 372
/**
 * Returns the default TextProperties that are applied to the text in the
 * absence of any nested property change sequences.
 */

/**
 * Returns the TextProperties in effect for the object at the indicated
 * position in the pre-wordwrapped string.
 */

/**
 * Returns the TextProperties in effect for the object at the indicated
 * position in the indicated row.
 */
224
inline TextProperties const &TextAssembler::get_properties(void) const;
inline TextProperties const &TextAssembler::get_properties(int n) const;
inline TextProperties const &TextAssembler::get_properties(int r, int c) const;

628 9 set_wtext 0 4 851 24 TextAssembler::set_wtext 0 1 234 326
/**
 * Accepts a new text string and associated properties structure, and
 * precomputes the wordwrapping layout appropriately.  After this call,
 * get_wordwrapped_wtext() and get_num_rows() can be called.
 *
 * The return value is true if all the text is accepted, or false if some was
 * truncated (see set_max_rows()).
 */
57
bool TextAssembler::set_wtext(std::wstring const &wtext);

629 11 set_wsubstr 0 4 851 26 TextAssembler::set_wsubstr 0 1 235 595
/**
 * Replaces the 'count' characters from 'start' of the current text with the
 * indicated replacement text.  If the replacement text does not have count
 * characters, the length of the string will be changed accordingly.
 *
 * The substring may include nested formatting characters, but they must be
 * self-contained and self-closed.  The formatting characters are not
 * literally saved in the internal string; they are parsed at the time of the
 * set_wsubstr() call.
 *
 * The return value is true if all the text is accepted, or false if some was
 * truncated (see set_max_rows()).
 */
81
bool TextAssembler::set_wsubstr(std::wstring const &wtext, int start, int count);

630 15 get_plain_wtext 0 4 851 30 TextAssembler::get_plain_wtext 0 1 236 379
/**
 * Returns a wstring that represents the contents of the text, without any
 * embedded properties characters.  If there is an embedded graphic object, a
 * zero value is inserted in that position.
 *
 * This string has the same length as get_num_characters(), and the characters
 * in this string correspond one-to-one with the characters returned by
 * get_character(n).
 */
56
std::wstring TextAssembler::get_plain_wtext(void) const;

631 27 get_wordwrapped_plain_wtext 0 4 851 42 TextAssembler::get_wordwrapped_plain_wtext 0 1 237 471
/**
 * Returns a wstring that represents the contents of the text, with newlines
 * inserted according to the wordwrapping.  The string will contain no
 * embedded properties characters.  If there is an embedded graphic object, a
 * zero value is inserted in that position.
 *
 * This string has the same number of newline characters as get_num_rows(),
 * and the characters in this string correspond one-to-one with the characters
 * returned by get_character(r, c).
 */
68
std::wstring TextAssembler::get_wordwrapped_plain_wtext(void) const;

632 9 get_wtext 0 4 851 24 TextAssembler::get_wtext 0 1 238 258
/**
 * Returns a wstring that represents the contents of the text.
 *
 * The string will contain embedded properties characters, which may not
 * exactly match the embedded properties characters of the original string,
 * but it will encode the same way.
 */
50
std::wstring TextAssembler::get_wtext(void) const;

633 21 get_wordwrapped_wtext 0 4 851 36 TextAssembler::get_wordwrapped_wtext 0 1 239 598
/**
 * Returns a wstring that represents the contents of the text, with newlines
 * inserted according to the wordwrapping.
 *
 * The string will contain embedded properties characters, which may not
 * exactly match the embedded properties characters of the original string,
 * but it will encode the same way.
 *
 * Embedded properties characters will be closed before every newline, then
 * reopened (if necessary) on the subsequent character following the newline.
 * This means it will be safe to divide the text up at the newline characters
 * and treat each line as an independent piece.
 */
62
std::wstring TextAssembler::get_wordwrapped_wtext(void) const;

634 8 calc_r_c 0 4 851 23 TextAssembler::calc_r_c 0 0 399
/**
 * Computes the row and column index of the nth character or graphic object in
 * the text.  Fills r and c accordingly.
 *
 * Returns true if the nth character is valid and has a corresponding r and c
 * position, false otherwise (for instance, a soft-hyphen character, or a
 * newline character, may not have a corresponding position). In either case,
 * r and c will be filled in sensibly.
 */
58
bool TextAssembler::calc_r_c(int &r, int &c, int n) const;

635 6 calc_r 0 4 851 21 TextAssembler::calc_r 0 1 240 330
/**
 * Computes the row index of the nth character or graphic object in the text
 * and returns it.
 *
 * If the nth character is not a normal printable character with a position in
 * the wordwrapped string, returns -1 (for instance, a soft-hyphen character,
 * or a newline character, may not have a corresponding position).
 */
46
inline int TextAssembler::calc_r(int n) const;

636 6 calc_c 0 4 851 21 TextAssembler::calc_c 0 1 241 333
/**
 * Computes the column index of the nth character or graphic object in the
 * text and returns it.
 *
 * If the nth character is not a normal printable character with a position in
 * the wordwrapped string, returns -1 (for instance, a soft-hyphen character,
 * or a newline character, may not have a corresponding position).
 */
46
inline int TextAssembler::calc_c(int n) const;

637 10 calc_index 0 4 851 25 TextAssembler::calc_index 0 1 242 299
/**
 * Computes the character index of the character at the rth row and cth column
 * position.  This is the inverse of calc_r_c().
 *
 * It is legal for c to exceed the index number of the last column by 1, and
 * it is legal for r to exceed the index number of the last row by 1, if c is
 * 0.
 */
50
int TextAssembler::calc_index(int r, int c) const;

638 18 get_num_characters 0 4 851 33 TextAssembler::get_num_characters 0 1 243 73
/**
 * Returns the number of characters of text, before wordwrapping.
 */
57
inline int TextAssembler::get_num_characters(void) const;

639 13 get_character 0 4 851 28 TextAssembler::get_character 0 2 244 245 357
/**
 * Returns the character at the indicated position in the pre-wordwrapped
 * string.  If the object at this position is a graphic object instead of a
 * character, returns 0.
 */

/**
 * Returns the character at the indicated position in the indicated row.  If
 * the object at this position is a graphic object instead of a character,
 * returns 0.
 */
124
inline char32_t TextAssembler::get_character(int n) const;
inline char32_t TextAssembler::get_character(int r, int c) const;

640 11 get_graphic 0 4 851 26 TextAssembler::get_graphic 0 2 246 247 372
/**
 * Returns the graphic object at the indicated position in the pre-wordwrapped
 * string.  If the object at this position is a character instead of a graphic
 * object, returns NULL.
 */

/**
 * Returns the graphic object at the indicated position in the indicated row.
 * If the object at this position is a character instead of a graphic object,
 * returns NULL.
 */
140
inline TextGraphic const *TextAssembler::get_graphic(int n) const;
inline TextGraphic const *TextAssembler::get_graphic(int r, int c) const;

641 9 get_width 0 4 851 24 TextAssembler::get_width 0 2 248 249 223
/**
 * Returns the width of the character or object at the indicated position in
 * the pre-wordwrapped string.
 */

/**
 * Returns the width of the character or object at the indicated position in
 * the indicated row.
 */
122
inline PN_stdfloat TextAssembler::get_width(int n) const;
inline PN_stdfloat TextAssembler::get_width(int r, int c) const;

642 12 get_num_rows 0 4 851 27 TextAssembler::get_num_rows 0 1 250 97
/**
 * Returns the number of rows of text after it has all been wordwrapped and
 * assembled.
 */
51
inline int TextAssembler::get_num_rows(void) const;

643 12 get_num_cols 0 4 851 27 TextAssembler::get_num_cols 0 1 251 82
/**
 * Returns the number of characters and/or graphic objects in the nth row.
 */
52
inline int TextAssembler::get_num_cols(int r) const;

644 8 get_xpos 0 4 851 23 TextAssembler::get_xpos 0 1 252 297
/**
 * Returns the x position of the origin of the character or graphic object at
 * the indicated position in the indicated row.
 *
 * It is legal for c to exceed the index number of the last column by 1, and
 * it is legal for r to exceed the index number of the last row by 1, if c is
 * 0.
 */
56
PN_stdfloat TextAssembler::get_xpos(int r, int c) const;

645 8 get_ypos 0 4 851 23 TextAssembler::get_ypos 0 1 253 230
/**
 * Returns the y position of the origin of all of the characters or graphic
 * objects in the indicated row.
 *
 * It is legal for r to exceed the index number of the last row by 1.  The
 * value of c is presently ignored.
 */
63
inline PN_stdfloat TextAssembler::get_ypos(int r, int c) const;

646 13 assemble_text 0 4 851 28 TextAssembler::assemble_text 0 1 254 247
/**
 * Actually assembles all of the text into a GeomNode, and returns the node
 * (or possibly a parent of the node, to keep the shadow separate).  Once this
 * has been called, you may query the extents of the text via get_ul(),
 * get_lr().
 */
58
PointerTo< PandaNode > TextAssembler::assemble_text(void);

647 6 get_ul 0 4 851 21 TextAssembler::get_ul 0 1 255 91
/**
 * Returns the upper-left corner of the assembled text, in 2-d text
 * coordinates.
 */
57
inline LVector2 const &TextAssembler::get_ul(void) const;

648 6 get_lr 0 4 851 21 TextAssembler::get_lr 0 1 256 92
/**
 * Returns the lower-right corner of the assembled text, in 2-d text
 * coordinates.
 */
57
inline LVector2 const &TextAssembler::get_lr(void) const;

649 10 calc_width 0 4 851 25 TextAssembler::calc_width 0 3 257 258 259 698
/**
 * Returns the width of a single character, according to its associated font.
 */

/**
 * Returns the width of a single character, according to its associated font.
 * This also correctly calculates the width of cheesy ligatures and accented
 * characters, which may not exist in the font as such.
 *
 * This does not take kerning into account, however.
 */

/**
 * Returns the width of a single character, according to its associated font.
 * This also correctly calculates the width of cheesy ligatures and accented
 * characters, which may not exist in the font as such.
 *
 * This does not take kerning into account, however.
 */

/**
 * Returns the width of a single TextGraphic image.
 */
306
static PN_stdfloat TextAssembler::calc_width(wchar_t character, TextProperties const &properties);
static PN_stdfloat TextAssembler::calc_width(char32_t character, TextProperties const &properties);
static PN_stdfloat TextAssembler::calc_width(TextGraphic const *graphic, TextProperties const &properties);

650 19 has_exact_character 0 4 851 34 TextAssembler::has_exact_character 0 1 260 634
/**
 * Returns true if the named character exists in the font exactly as named,
 * false otherwise.  Note that because Panda can assemble glyphs together
 * automatically using cheesy accent marks, this is not a reliable indicator
 * of whether a suitable glyph can be rendered for the character.  For that,
 * use has_character() instead.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".  It also returns false for characters that would be
 * synthesized within Panda, but see has_character().
 */
100
static bool TextAssembler::has_exact_character(wchar_t character, TextProperties const &properties);

651 13 has_character 0 4 851 28 TextAssembler::has_character 0 1 261 405
/**
 * Returns true if the named character exists in the font or can be
 * synthesized by Panda, false otherwise.  (Panda can synthesize some accented
 * characters by combining similar-looking glyphs from the font.)
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".
 */
94
static bool TextAssembler::has_character(wchar_t character, TextProperties const &properties);

652 13 is_whitespace 0 4 851 28 TextAssembler::is_whitespace 0 1 262 695
/**
 * Returns true if the indicated character represents whitespace in the font,
 * or false if anything visible will be rendered for it.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), and returns false for any other characters, including
 * characters that do not exist in the font (these would be rendered with the
 * "invalid glyph", which is visible).
 *
 * Note that this function can be reliably used to identify Unicode whitespace
 * characters only if the font has all of the whitespace characters defined.
 * It will return false for any character not in the font, even if it is an
 * official Unicode whitespace character.
 */
94
static bool TextAssembler::is_whitespace(wchar_t character, TextProperties const &properties);

653 19 upcast_to_PandaNode 0 12 855 29 TextNode::upcast_to_PandaNode 0 1 372 33
upcast from TextNode to PandaNode
47
PandaNode *TextNode::upcast_to_PandaNode(void);

654 20 downcast_to_TextNode 0 12 856 31 PandaNode::downcast_to_TextNode 0 0 35
downcast from PandaNode to TextNode
48
TextNode *PandaNode::downcast_to_TextNode(void);

655 21 upcast_to_TextEncoder 0 12 855 31 TextNode::upcast_to_TextEncoder 0 1 373 35
upcast from TextNode to TextEncoder
51
TextEncoder *TextNode::upcast_to_TextEncoder(void);

656 20 downcast_to_TextNode 0 12 857 33 TextEncoder::downcast_to_TextNode 0 0 37
downcast from TextEncoder to TextNode
50
TextNode *TextEncoder::downcast_to_TextNode(void);

657 24 upcast_to_TextProperties 0 12 855 34 TextNode::upcast_to_TextProperties 0 1 374 38
upcast from TextNode to TextProperties
57
TextProperties *TextNode::upcast_to_TextProperties(void);

658 20 downcast_to_TextNode 0 12 838 36 TextProperties::downcast_to_TextNode 0 0 40
downcast from TextProperties to TextNode
53
TextNode *TextProperties::downcast_to_TextNode(void);

659 8 TextNode 0 260 855 18 TextNode::TextNode 0 2 263 264 184
/**
 *
 */

/**
 * It's sort of a copy constructor: it copies the indicated TextProperties,
 * without copying a complete TextNode.
 */

/**
 * OK, this is a true copy constructor.
 */
135
explicit TextNode::TextNode(std::string const &name);
explicit TextNode::TextNode(std::string const &name, TextProperties const &copy);

660 9 ~TextNode 0 518 855 19 TextNode::~TextNode 0 0 10
/**
 *
 */
34
virtual TextNode::~TextNode(void);

661 15 get_line_height 0 4 855 25 TextNode::get_line_height 0 1 265 234
/**
 * Returns the number of units high each line of text is.  This is based on
 * the font.  Note that it is possible for the text to include nested font
 * change commands, in which case the value of this method is questionable.
 */
57
inline PN_stdfloat TextNode::get_line_height(void) const;

662 12 set_max_rows 0 4 855 22 TextNode::set_max_rows 0 1 266 183
/**
 * Sets the maximum number of rows that may be formatted by the TextNode.  If
 * more text than this is attempted, it will be truncated and has_overflow()
 * will return true.
 */
49
inline void TextNode::set_max_rows(int max_rows);

663 14 clear_max_rows 0 4 855 24 TextNode::clear_max_rows 0 1 267 96
/**
 * Resets the TextNode's default behavior of not limiting the number of rows
 * of text.
 */
43
inline void TextNode::clear_max_rows(void);

664 12 has_max_rows 0 4 855 22 TextNode::has_max_rows 0 1 268 117
/**
 * Returns true if a limit on the height of the TextNode has been set via
 * set_max_rows(), false otherwise.
 */
47
inline bool TextNode::has_max_rows(void) const;

665 12 get_max_rows 0 4 855 22 TextNode::get_max_rows 0 1 269 90
/**
 * Returns the limit on the height of the TextNode specified by
 * set_max_rows().
 */
46
inline int TextNode::get_max_rows(void) const;

666 12 has_overflow 0 4 855 22 TextNode::has_overflow 0 1 270 122
/**
 * Returns true if the last text set on the text node exceeded the max_rows
 * constraint, or false if it all fit.
 */
47
inline bool TextNode::has_overflow(void) const;

667 15 set_frame_color 0 4 855 25 TextNode::set_frame_color 0 2 271 272 22
/**
 *
 */

/**
 *
 */
164
inline void TextNode::set_frame_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextNode::set_frame_color(LColor const &frame_color);

668 15 get_frame_color 0 4 855 25 TextNode::get_frame_color 0 1 273 10
/**
 *
 */
52
inline LColor TextNode::get_frame_color(void) const;

669 15 set_card_border 0 4 855 25 TextNode::set_card_border 0 1 274 10
/**
 *
 */
80
inline void TextNode::set_card_border(PN_stdfloat size, PN_stdfloat uv_portion);

670 17 clear_card_border 0 4 855 27 TextNode::clear_card_border 0 1 275 10
/**
 *
 */
46
inline void TextNode::clear_card_border(void);

671 20 get_card_border_size 0 4 855 30 TextNode::get_card_border_size 0 1 276 10
/**
 *
 */
62
inline PN_stdfloat TextNode::get_card_border_size(void) const;

672 26 get_card_border_uv_portion 0 4 855 36 TextNode::get_card_border_uv_portion 0 1 277 10
/**
 *
 */
68
inline PN_stdfloat TextNode::get_card_border_uv_portion(void) const;

673 15 has_card_border 0 4 855 25 TextNode::has_card_border 0 1 278 10
/**
 *
 */
50
inline bool TextNode::has_card_border(void) const;

674 14 set_card_color 0 4 855 24 TextNode::set_card_color 0 2 279 280 22
/**
 *
 */

/**
 *
 */
161
inline void TextNode::set_card_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextNode::set_card_color(LColor const &card_color);

675 14 get_card_color 0 4 855 24 TextNode::get_card_color 0 1 281 10
/**
 *
 */
51
inline LColor TextNode::get_card_color(void) const;

676 16 set_card_texture 0 4 855 26 TextNode::set_card_texture 0 1 282 10
/**
 *
 */
62
inline void TextNode::set_card_texture(Texture *card_texture);

677 18 clear_card_texture 0 4 855 28 TextNode::clear_card_texture 0 1 283 10
/**
 *
 */
47
inline void TextNode::clear_card_texture(void);

678 16 has_card_texture 0 4 855 26 TextNode::has_card_texture 0 1 284 10
/**
 *
 */
51
inline bool TextNode::has_card_texture(void) const;

679 16 get_card_texture 0 4 855 26 TextNode::get_card_texture 0 1 285 10
/**
 *
 */
55
inline Texture *TextNode::get_card_texture(void) const;

680 19 set_frame_as_margin 0 4 855 29 TextNode::set_frame_as_margin 0 1 286 249
/**
 * Specifies that a border will be drawn around the text when it is next
 * created.  The parameters are the amount of additional padding to insert
 * between the frame and the text in each dimension, and all should generally
 * be positive.
 */
116
inline void TextNode::set_frame_as_margin(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);

681 16 set_frame_actual 0 4 855 26 TextNode::set_frame_actual 0 1 287 308
/**
 * Similar to set_frame_as_margin, except the frame is specified in actual
 * coordinate units (relative to the text's origin), irrespective of the size
 * of the text.  The left and bottom coordinates should generally be negative,
 * while the right and top coordinates should generally be positive.
 */
113
inline void TextNode::set_frame_actual(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);

682 11 clear_frame 0 4 855 21 TextNode::clear_frame 0 1 288 69
/**
 * Specifies that a border will not be drawn around the text.
 */
40
inline void TextNode::clear_frame(void);

683 9 has_frame 0 4 855 19 TextNode::has_frame 0 1 289 10
/**
 *
 */
44
inline bool TextNode::has_frame(void) const;

684 18 is_frame_as_margin 0 4 855 28 TextNode::is_frame_as_margin 0 1 290 368
/**
 * If this is true, the frame was set via a call to set_frame_as_margin(), and
 * the dimension of the frame as returned by get_frame_as_set() represent a
 * margin all around the text.  If false, then the frame was set via a call to
 * set_frame_actual(), and the dimensions of the frame as returned by
 * get_frame_as_set() are relative to the text's origin.
 */
53
inline bool TextNode::is_frame_as_margin(void) const;

685 16 get_frame_as_set 0 4 855 26 TextNode::get_frame_as_set 0 1 291 255
/**
 * Returns the dimensions of the frame as set by set_frame_as_margin() or
 * set_frame_actual().  Use is_frame_actual() to determine how to interpret
 * the values returned by this function.  It is an error to call this if
 * has_frame() is false.
 */
56
inline LVecBase4 TextNode::get_frame_as_set(void) const;

686 16 get_frame_actual 0 4 855 26 TextNode::get_frame_actual 0 1 292 431
/**
 * Returns the actual dimensions of the frame around the text.  If the frame
 * was set via set_frame_as_margin(), the result returned by this function
 * reflects the size of the current text; if the frame was set via
 * set_frame_actual(), this returns the values actually set.
 *
 * If the text has no frame at all, this returns the dimensions of the text
 * itself, as if the frame were set with a margin of 0, 0, 0, 0.
 */
56
inline LVecBase4 TextNode::get_frame_actual(void) const;

687 20 set_frame_line_width 0 4 855 30 TextNode::set_frame_line_width 0 1 293 84
/**
 * Specifies the thickness of the lines that will be used to draw the frame.
 */
67
inline void TextNode::set_frame_line_width(PN_stdfloat line_width);

688 20 get_frame_line_width 0 4 855 30 TextNode::get_frame_line_width 0 1 294 82
/**
 * Returns the thickness of the lines that will be used to draw the frame.
 */
62
inline PN_stdfloat TextNode::get_frame_line_width(void) const;

689 17 set_frame_corners 0 4 855 27 TextNode::set_frame_corners 0 1 295 209
/**
 * Enables or disables the drawing of corners for the frame.  These are extra
 * points drawn at each of the four corners, to soften the ugly edges
 * generated when the line width is greater than one.
 */
54
inline void TextNode::set_frame_corners(bool corners);

690 17 get_frame_corners 0 4 855 27 TextNode::get_frame_corners 0 1 296 10
/**
 *
 */
52
inline bool TextNode::get_frame_corners(void) const;

691 18 set_card_as_margin 0 4 855 28 TextNode::set_card_as_margin 0 1 297 294
/**
 * Specifies that a (possibly opaque or semitransparent) card will be held
 * behind the text when it is next created.  Like set_frame_as_margin, the
 * parameters are the amount of additional padding to insert around the text
 * in each dimension, and all should generally be positive.
 */
115
inline void TextNode::set_card_as_margin(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);

692 15 set_card_actual 0 4 855 25 TextNode::set_card_actual 0 1 298 306
/**
 * Similar to set_card_as_margin, except the card is specified in actual
 * coordinate units (relative to the text's origin), irrespective of the size
 * of the text.  The left and bottom coordinates should generally be negative,
 * while the right and top coordinates should generally be positive.
 */
112
inline void TextNode::set_card_actual(PN_stdfloat left, PN_stdfloat right, PN_stdfloat bottom, PN_stdfloat top);

693 14 set_card_decal 0 4 855 24 TextNode::set_card_decal 0 1 299 197
/**
 * Sets the card_decal flag.  When this is true, the text is decalled onto the
 * card, which is necessary if the TextNode is to be rendered in the 3-d world
 * without putting it in a bin.
 */
54
inline void TextNode::set_card_decal(bool card_decal);

694 10 clear_card 0 4 855 20 TextNode::clear_card 0 1 300 67
/**
 * Specifies that a card will not be drawn behind the text.
 */
39
inline void TextNode::clear_card(void);

695 8 has_card 0 4 855 18 TextNode::has_card 0 1 301 10
/**
 *
 */
43
inline bool TextNode::has_card(void) const;

696 14 get_card_decal 0 4 855 24 TextNode::get_card_decal 0 1 302 62
/**
 * Returns the card_decal flag.  See set_card_decal().
 */
49
inline bool TextNode::get_card_decal(void) const;

697 17 is_card_as_margin 0 4 855 27 TextNode::is_card_as_margin 0 1 303 360
/**
 * If this is true, the card was set via a call to set_card_as_margin(), and
 * the dimension of the card as returned by get_card_as_set() represent a
 * margin all around the text.  If false, then the card was set via a call to
 * set_card_actual(), and the dimensions of the card as returned by
 * get_card_as_set() are relative to the text's origin.
 */
52
inline bool TextNode::is_card_as_margin(void) const;

698 15 get_card_as_set 0 4 855 25 TextNode::get_card_as_set 0 1 304 250
/**
 * Returns the dimensions of the card as set by set_card_as_margin() or
 * set_card_actual().  Use is_card_actual() to determine how to interpret the
 * values returned by this function.  It is an error to call this if
 * has_card() is false.
 */
55
inline LVecBase4 TextNode::get_card_as_set(void) const;

699 15 get_card_actual 0 4 855 25 TextNode::get_card_actual 0 1 305 424
/**
 * Returns the actual dimensions of the card around the text.  If the card was
 * set via set_card_as_margin(), the result returned by this function reflects
 * the size of the current text; if the card was set via set_card_actual(),
 * this returns the values actually set.
 *
 * If the text has no card at all, this returns the dimensions of the text
 * itself, as if the card were set with a margin of 0, 0, 0, 0.
 */
55
inline LVecBase4 TextNode::get_card_actual(void) const;

700 20 get_card_transformed 0 4 855 30 TextNode::get_card_transformed 0 1 306 251
/**
 * Returns the actual card dimensions, transformed by the matrix set by
 * set_transform().  This returns the card dimensions in actual coordinates as
 * seen by the rest of the world.  Also see get_upper_left_3d() and
 * get_lower_right_3d().
 */
60
inline LVecBase4 TextNode::get_card_transformed(void) const;

701 13 set_transform 0 4 855 23 TextNode::set_transform 0 1 307 85
/**
 * Sets an additional transform that is applied to the entire text paragraph.
 */
63
inline void TextNode::set_transform(LMatrix4 const &transform);

702 13 get_transform 0 4 855 23 TextNode::get_transform 0 1 308 10
/**
 *
 */
52
inline LMatrix4 TextNode::get_transform(void) const;

703 21 set_coordinate_system 0 4 855 31 TextNode::set_coordinate_system 0 1 309 79
/**
 * Specifies the coordinate system in which the text will be generated.
 */
65
inline void TextNode::set_coordinate_system(CoordinateSystem cs);

704 21 get_coordinate_system 0 4 855 31 TextNode::get_coordinate_system 0 1 310 10
/**
 *
 */
68
inline CoordinateSystem TextNode::get_coordinate_system(void) const;

705 14 set_usage_hint 0 4 855 24 TextNode::set_usage_hint 0 1 311 278
/**
 * Specifies the UsageHint that will be applied to generated geometry.  The
 * default is UH_static, which is probably the right setting, but if you know
 * the TextNode's geometry will have a short lifespan, it may be better to set
 * it to UH_stream.  See geomEnums.h.
 */
70
inline void TextNode::set_usage_hint(GeomEnums::UsageHint usage_hint);

706 14 get_usage_hint 0 4 855 24 TextNode::get_usage_hint 0 1 312 102
/**
 * Returns the UsageHint that will be applied to generated geometry.  See
 * set_usage_hint().
 */
65
inline GeomEnums::UsageHint TextNode::get_usage_hint(void) const;

707 17 set_flatten_flags 0 4 855 27 TextNode::set_flatten_flags 0 1 313 1534
/**
 * Sets the flatten flags.  This should be a union of the
 * TextNode::FlattenFlags options.  This controls the degree of flattening
 * performed on the TextNode's internal geometry (i.e.  the scene graph
 * returned by generate()) each time the text is changed.  In general, more
 * flattening means a more optimal result, but it will take more time to
 * generate.
 *
 * The choice may be any of these three:
 *
 * FF_none - No flatten operation is called.  The letters are left as
 * independent Geoms.
 *
 * FF_light - A flatten_light() operation is called.  The attributes are
 * applied to the vertices, but no nodes are removed.
 *
 * FF_medium - A flatten_medium() operation is called.  The attributes are
 * applied to the vertices, and a few trivial nodes are removed.
 *
 * FF_strong - A flatten_strong() operation is called.  The attributes are
 * applied to the vertices, and the resulting nodes are aggressively combined
 * into as few nodes as possible.
 *
 * In addition to the above choices, you may optionally include the following
 * flag:
 *
 * FF_dynamic_merge - Copy the geoms into a single GeomVertexData as we go,
 * instead of relying on the flatten operation at the end.  This pre-flattens
 * the text considerably, and may obviate the need for flatten altogether; it
 * also tends to improve performance considerably even if you do call flatten.
 * However, it is not as fast as not calling flatten at all.
 *
 * The default is taken from the text-flatten and text-dynamic-merge config
 * variables.
 */
59
inline void TextNode::set_flatten_flags(int flatten_flags);

708 17 get_flatten_flags 0 4 855 27 TextNode::get_flatten_flags 0 1 314 63
/**
 * Returns the flatten flags.  See set_flatten_flags().
 */
51
inline int TextNode::get_flatten_flags(void) const;

709 8 set_font 0 4 855 18 TextNode::set_font 0 1 315 160
/**
 * Sets the font that will be used when making text.  If this is set to NULL,
 * the default font will be used, which can be set via set_default_font().
 */
47
inline void TextNode::set_font(TextFont *font);

710 10 clear_font 0 4 855 20 TextNode::clear_font 0 1 316 47
/**
 * Resets the font to the default font.
 */
39
inline void TextNode::clear_font(void);

711 14 set_small_caps 0 4 855 24 TextNode::set_small_caps 0 1 317 707
/**
 * Sets the small_caps flag.  When this is set, lowercase letters are
 * generated as scaled-down versions of their uppercase equivalents.  This is
 * particularly useful to set for fonts that do not have lowercase letters.
 *
 * It is also a good idea to set this for a (dynamic) font that has already
 * implemented lowercase letters as scaled-down versions of their uppercase
 * equivalents, since without this flag the texture memory may needlessly
 * duplicate equivalent glyphs for upper and lowercase letters.  Setting this
 * flag causes the texture memory to share the mixed-case letters.
 *
 * The amount by which the lowercase letters are scaled is specified by
 * set_small_caps_scale().
 */
54
inline void TextNode::set_small_caps(bool small_caps);

712 16 clear_small_caps 0 4 855 26 TextNode::clear_small_caps 0 1 318 10
/**
 *
 */
45
inline void TextNode::clear_small_caps(void);

713 20 set_small_caps_scale 0 4 855 30 TextNode::set_small_caps_scale 0 1 319 210
/**
 * Sets the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps().
 * Normally, this will be a number less than one.
 */
73
inline void TextNode::set_small_caps_scale(PN_stdfloat small_caps_scale);

714 22 clear_small_caps_scale 0 4 855 32 TextNode::clear_small_caps_scale 0 1 320 10
/**
 *
 */
51
inline void TextNode::clear_small_caps_scale(void);

715 9 set_slant 0 4 855 19 TextNode::set_slant 0 1 321 10
/**
 *
 */
51
inline void TextNode::set_slant(PN_stdfloat slant);

716 11 clear_slant 0 4 855 21 TextNode::clear_slant 0 1 322 10
/**
 *
 */
40
inline void TextNode::clear_slant(void);

717 9 set_align 0 4 855 19 TextNode::set_align 0 1 323 10
/**
 *
 */
70
inline void TextNode::set_align(TextProperties::Alignment align_type);

718 11 clear_align 0 4 855 21 TextNode::clear_align 0 1 324 10
/**
 *
 */
40
inline void TextNode::clear_align(void);

719 10 set_indent 0 4 855 20 TextNode::set_indent 0 1 325 149
/**
 * Specifies the amount of extra space that is inserted before the first
 * character of each line.  This can be thought of as a left margin.
 */
53
inline void TextNode::set_indent(PN_stdfloat indent);

720 12 clear_indent 0 4 855 22 TextNode::clear_indent 0 1 326 10
/**
 *
 */
41
inline void TextNode::clear_indent(void);

721 12 set_wordwrap 0 4 855 22 TextNode::set_wordwrap 0 1 327 152
/**
 * Sets the text up to automatically wordwrap when it exceeds the indicated
 * width.  This can be thought of as a right margin or margin width.
 */
57
inline void TextNode::set_wordwrap(PN_stdfloat wordwrap);

722 14 clear_wordwrap 0 4 855 24 TextNode::clear_wordwrap 0 1 328 93
/**
 * Removes the wordwrap setting from the TextNode.  Text will be as wide as it
 * is.
 */
43
inline void TextNode::clear_wordwrap(void);

723 14 set_text_color 0 4 855 24 TextNode::set_text_color 0 2 329 330 456
/**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */

/**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */
161
inline void TextNode::set_text_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextNode::set_text_color(LColor const &text_color);

724 16 clear_text_color 0 4 855 26 TextNode::clear_text_color 0 1 331 117
/**
 * Removes the text color specification; the text will be colored whatever it
 * was in the source font file.
 */
45
inline void TextNode::clear_text_color(void);

725 16 set_shadow_color 0 4 855 26 TextNode::set_shadow_color 0 2 332 333 22
/**
 *
 */

/**
 *
 */
167
inline void TextNode::set_shadow_color(PN_stdfloat r, PN_stdfloat g, PN_stdfloat b, PN_stdfloat a);
inline void TextNode::set_shadow_color(LColor const &shadow_color);

726 18 clear_shadow_color 0 4 855 28 TextNode::clear_shadow_color 0 1 334 10
/**
 *
 */
47
inline void TextNode::clear_shadow_color(void);

727 10 set_shadow 0 4 855 20 TextNode::set_shadow 0 2 335 336 304
/**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */

/**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */
141
inline void TextNode::set_shadow(PN_stdfloat xoffset, PN_stdfloat yoffset);
inline void TextNode::set_shadow(LVecBase2 const &shadow_offset);

728 12 clear_shadow 0 4 855 22 TextNode::clear_shadow 0 1 337 69
/**
 * Specifies that a shadow will not be drawn behind the text.
 */
41
inline void TextNode::clear_shadow(void);

729 7 set_bin 0 4 855 17 TextNode::set_bin 0 1 338 362
/**
 * Names the GeomBin that the TextNode geometry should be assigned to.  If
 * this is set, then a GeomBinTransition will be created to explicitly place
 * each component in the named bin.
 *
 * The draw_order value will also be passed to each GeomBinTransition as
 * appropriate; this is particularly useful if this names a GeomBinFixed, e.g.
 * "fixed".
 */
54
inline void TextNode::set_bin(std::string const &bin);

730 9 clear_bin 0 4 855 19 TextNode::clear_bin 0 1 339 158
/**
 * Removes the effect of a previous call to set_bin().  Text will be drawn in
 * whatever bin it would like to be drawn in, with no explicit ordering.
 */
38
inline void TextNode::clear_bin(void);

731 14 set_draw_order 0 4 855 24 TextNode::set_draw_order 0 1 340 435
/**
 * Sets the drawing order of text created by the TextMaker.  This is actually
 * the draw order of the card and frame.  The shadow is drawn at
 * _draw_order+1, and the text at _draw_order+2.
 *
 * This affects the sorting order assigned to the arcs as they are created,
 * and also is passed to whatever bin may be assigned via set_bin().
 *
 * The return value is the first unused draw_order number, e.g.  _draw_order +
 * 3.
 */
52
inline int TextNode::set_draw_order(int draw_order);

732 16 clear_draw_order 0 4 855 26 TextNode::clear_draw_order 0 1 341 10
/**
 *
 */
45
inline void TextNode::clear_draw_order(void);

733 13 set_tab_width 0 4 855 23 TextNode::set_tab_width 0 1 342 160
/**
 * Sets the width of each tab stop, in screen units.  A tab character embedded
 * in the text will advance the horizontal position to the next tab stop.
 */
59
inline void TextNode::set_tab_width(PN_stdfloat tab_width);

734 15 clear_tab_width 0 4 855 25 TextNode::clear_tab_width 0 1 343 10
/**
 *
 */
44
inline void TextNode::clear_tab_width(void);

735 15 set_glyph_scale 0 4 855 25 TextNode::set_glyph_scale 0 1 344 208
/**
 * Specifies the factor by which to scale each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_shift())
 * to implement superscripting or subscripting.
 */
63
inline void TextNode::set_glyph_scale(PN_stdfloat glyph_scale);

736 17 clear_glyph_scale 0 4 855 27 TextNode::clear_glyph_scale 0 1 345 10
/**
 *
 */
46
inline void TextNode::clear_glyph_scale(void);

737 15 set_glyph_shift 0 4 855 25 TextNode::set_glyph_shift 0 1 346 206
/**
 * Specifies a vertical amount to shift each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_scale())
 * to implement superscripting or subscripting.
 */
63
inline void TextNode::set_glyph_shift(PN_stdfloat glyph_shift);

738 17 clear_glyph_shift 0 4 855 27 TextNode::clear_glyph_shift 0 1 347 10
/**
 *
 */
46
inline void TextNode::clear_glyph_shift(void);

739 20 get_wordwrapped_text 0 4 855 30 TextNode::get_wordwrapped_text 0 1 348 323
// After the text has been set, you can query this to determine how it will
// be wordwrapped.

/**
 * Returns a string that represents the contents of the text, as it has been
 * formatted by wordwrap rules.
 *
 * In earlier versions, this did not contain any embedded special characters
 * like \1 or \3; now it does.
 */
62
inline std::string TextNode::get_wordwrapped_text(void) const;

740 10 calc_width 0 4 855 20 TextNode::calc_width 0 3 349 350 351 564
// These methods calculate the width of a single character or a line of text
// in the current font.

/**
 * Returns the width of a line of text of arbitrary characters.  The line
 * should not include the newline character.
 */

/**
 * Returns the width of a single character of the font, or 0.0 if the
 * character is not known.  This may be a wide character (greater than 255).
 */

/**
 * Returns the width of a line of text of arbitrary characters.  The line
 * should not include the newline character or any embedded control characters
 * like \1 or \3.
 */
196
PN_stdfloat TextNode::calc_width(wchar_t character) const;
inline PN_stdfloat TextNode::calc_width(std::string const &line) const;
PN_stdfloat TextNode::calc_width(std::wstring const &line) const;

741 19 has_exact_character 0 4 855 29 TextNode::has_exact_character 0 1 352 634
/**
 * Returns true if the named character exists in the font exactly as named,
 * false otherwise.  Note that because Panda can assemble glyphs together
 * automatically using cheesy accent marks, this is not a reliable indicator
 * of whether a suitable glyph can be rendered for the character.  For that,
 * use has_character() instead.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".  It also returns false for characters that would be
 * synthesized within Panda, but see has_character().
 */
60
bool TextNode::has_exact_character(wchar_t character) const;

742 13 has_character 0 4 855 23 TextNode::has_character 0 1 353 405
/**
 * Returns true if the named character exists in the font or can be
 * synthesized by Panda, false otherwise.  (Panda can synthesize some accented
 * characters by combining similar-looking glyphs from the font.)
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".
 */
54
bool TextNode::has_character(wchar_t character) const;

743 13 is_whitespace 0 4 855 23 TextNode::is_whitespace 0 1 354 695
/**
 * Returns true if the indicated character represents whitespace in the font,
 * or false if anything visible will be rendered for it.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), and returns false for any other characters, including
 * characters that do not exist in the font (these would be rendered with the
 * "invalid glyph", which is visible).
 *
 * Note that this function can be reliably used to identify Unicode whitespace
 * characters only if the font has all of the whitespace characters defined.
 * It will return false for any character not in the font, even if it is an
 * official Unicode whitespace character.
 */
54
bool TextNode::is_whitespace(wchar_t character) const;

744 21 get_wordwrapped_wtext 0 4 855 31 TextNode::get_wordwrapped_wtext 0 1 355 228
/**
 * Returns a wstring that represents the contents of the text, as it has been
 * formatted by wordwrap rules.
 *
 * In earlier versions, this did not contain any embedded special characters
 * like \1 or \3; now it does.
 */
64
inline std::wstring TextNode::get_wordwrapped_wtext(void) const;

745 6 output 0 6 855 16 TextNode::output 0 1 356 10
/**
 *
 */
55
virtual void TextNode::output(std::ostream &out) const;

746 5 write 0 6 855 15 TextNode::write 0 1 357 10
/**
 *
 */
76
virtual void TextNode::write(std::ostream &out, int indent_level = 0) const;

747 8 get_left 0 4 855 18 TextNode::get_left 0 1 358 234
// The following functions return information about the text that was last
// built (and is currently visible).

/**
 * Returns the leftmost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */
50
inline PN_stdfloat TextNode::get_left(void) const;

748 9 get_right 0 4 855 19 TextNode::get_right 0 1 359 122
/**
 * Returns the rightmost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */
51
inline PN_stdfloat TextNode::get_right(void) const;

749 10 get_bottom 0 4 855 20 TextNode::get_bottom 0 1 360 123
/**
 * Returns the bottommost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */
52
inline PN_stdfloat TextNode::get_bottom(void) const;

750 7 get_top 0 4 855 17 TextNode::get_top 0 1 361 120
/**
 * Returns the topmost extent of the text in local 2-d coordinates, unmodified
 * by the set_transform() matrix.
 */
49
inline PN_stdfloat TextNode::get_top(void) const;

751 10 get_height 0 4 855 20 TextNode::get_height 0 1 362 71
/**
 * Returns the net height of the text in local 2-d coordinates.
 */
52
inline PN_stdfloat TextNode::get_height(void) const;

752 9 get_width 0 4 855 19 TextNode::get_width 0 1 363 70
/**
 * Returns the net width of the text in local 2-d coordinates.
 */
51
inline PN_stdfloat TextNode::get_width(void) const;

753 17 get_upper_left_3d 0 4 855 27 TextNode::get_upper_left_3d 0 1 364 148
/**
 * Returns the upper-left extent of the text object, after it has been
 * transformed into 3-d space by applying the set_transform() matrix.
 */
55
inline LPoint3 TextNode::get_upper_left_3d(void) const;

754 18 get_lower_right_3d 0 4 855 28 TextNode::get_lower_right_3d 0 1 365 149
/**
 * Returns the lower-right extent of the text object, after it has been
 * transformed into 3-d space by applying the set_transform() matrix.
 */
56
inline LPoint3 TextNode::get_lower_right_3d(void) const;

755 12 get_num_rows 0 4 855 22 TextNode::get_num_rows 0 1 366 153
/**
 * Returns the number of rows of text that were generated.  This counts word-
 * wrapped rows as well as rows generated due to embedded newlines.
 */
46
inline int TextNode::get_num_rows(void) const;

756 8 generate 0 4 855 18 TextNode::generate 0 1 367 168
/**
 * Generates the text, according to the parameters indicated within the
 * TextNode, and returns a Node that may be parented within the tree to
 * represent it.
 */
55
inline PointerTo< PandaNode > TextNode::generate(void);

757 6 update 0 4 855 16 TextNode::update 0 1 368 193
/**
 * Can be called after the TextNode has been fully configured, to force the
 * node to recompute its text immediately, rather than waiting for it to be
 * drawn.  This call is optional.
 */
35
inline void TextNode::update(void);

758 12 force_update 0 4 855 22 TextNode::force_update 0 1 369 267
/**
 * Forces the TextNode to recompute itself now, even if it believes nothing
 * has changed.  Normally, this should not need to be called, but it may be
 * useful if some properties change outside of the TextNode's knowledge (for
 * instance, within the font).
 */
41
inline void TextNode::force_update(void);

759 17 get_internal_geom 0 4 855 27 TextNode::get_internal_geom 0 1 370 331
/**
 * Returns the actual node that is used internally to render the text, if the
 * TextNode is parented within the scene graph.
 *
 * In general, you should not call this method.  Call generate() instead if
 * you want to get a handle to geometry that represents the text.  This method
 * is provided as a debugging aid only.
 */
63
PointerTo< PandaNode > TextNode::get_internal_geom(void) const;

760 8 get_font 0 4 855 18 TextNode::get_font 0 0 110
/**
 * Returns the font currently in use, if any.  If no font is in use, this
 * returns the default font.
 */
48
inline TextFont *TextNode::get_font(void) const;

761 8 has_font 0 4 855 18 TextNode::has_font 0 1 375 10
/**
 *
 */
43
inline bool TextNode::has_font(void) const;

762 14 get_small_caps 0 4 855 24 TextNode::get_small_caps 0 0 62
/**
 * Returns the small_caps flag.  See set_small_caps().
 */
49
inline bool TextNode::get_small_caps(void) const;

763 14 has_small_caps 0 4 855 24 TextNode::has_small_caps 0 1 376 10
/**
 *
 */
49
inline bool TextNode::has_small_caps(void) const;

764 20 get_small_caps_scale 0 4 855 30 TextNode::get_small_caps_scale 0 0 193
/**
 * Returns the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps()
 * and set_small_caps_scale().
 */
62
inline PN_stdfloat TextNode::get_small_caps_scale(void) const;

765 20 has_small_caps_scale 0 4 855 30 TextNode::has_small_caps_scale 0 1 377 10
/**
 *
 */
55
inline bool TextNode::has_small_caps_scale(void) const;

766 9 get_slant 0 4 855 19 TextNode::get_slant 0 0 83
/**
 * Returns the factor by which the text is specified to slant to the right.
 */
51
inline PN_stdfloat TextNode::get_slant(void) const;

767 9 has_slant 0 4 855 19 TextNode::has_slant 0 1 378 10
/**
 *
 */
44
inline bool TextNode::has_slant(void) const;

768 14 get_underscore 0 4 855 24 TextNode::get_underscore 0 0 62
/**
 * Returns the underscore flag.  See set_underscore().
 */
49
inline bool TextNode::get_underscore(void) const;

769 14 has_underscore 0 4 855 24 TextNode::has_underscore 0 1 379 10
/**
 *
 */
49
inline bool TextNode::has_underscore(void) const;

770 14 set_underscore 0 4 855 24 TextNode::set_underscore 0 0 169
/**
 * Sets the underscore flag.  When this is set, the text is underscored with a
 * one-pixel line the same color as the text foreground, drawn at the
 * baseline.
 */
54
inline void TextNode::set_underscore(bool underscore);

771 16 clear_underscore 0 4 855 26 TextNode::clear_underscore 0 1 380 10
/**
 *
 */
45
inline void TextNode::clear_underscore(void);

772 21 get_underscore_height 0 4 855 31 TextNode::get_underscore_height 0 0 86
/**
 * Returns the vertical height of the underscore; see set_underscore_height().
 */
63
inline PN_stdfloat TextNode::get_underscore_height(void) const;

773 21 has_underscore_height 0 4 855 31 TextNode::has_underscore_height 0 1 381 10
/**
 *
 */
56
inline bool TextNode::has_underscore_height(void) const;

774 21 set_underscore_height 0 4 855 31 TextNode::set_underscore_height 0 0 176
/**
 * Specifies the vertical height of the underscore, relative to the text
 * baseline.  This only has meaning if the underscore mode is enabled with
 * set_underscore().
 */
75
inline void TextNode::set_underscore_height(PN_stdfloat underscore_height);

775 23 clear_underscore_height 0 4 855 33 TextNode::clear_underscore_height 0 1 382 10
/**
 *
 */
52
inline void TextNode::clear_underscore_height(void);

776 9 get_align 0 4 855 19 TextNode::get_align 0 0 10
/**
 *
 */
65
inline TextProperties::Alignment TextNode::get_align(void) const;

777 9 has_align 0 4 855 19 TextNode::has_align 0 1 383 10
/**
 *
 */
44
inline bool TextNode::has_align(void) const;

778 10 get_indent 0 4 855 20 TextNode::get_indent 0 0 10
/**
 *
 */
52
inline PN_stdfloat TextNode::get_indent(void) const;

779 10 has_indent 0 4 855 20 TextNode::has_indent 0 1 384 10
/**
 *
 */
45
inline bool TextNode::has_indent(void) const;

780 12 get_wordwrap 0 4 855 22 TextNode::get_wordwrap 0 0 10
/**
 *
 */
54
inline PN_stdfloat TextNode::get_wordwrap(void) const;

781 12 has_wordwrap 0 4 855 22 TextNode::has_wordwrap 0 1 385 10
/**
 *
 */
47
inline bool TextNode::has_wordwrap(void) const;

782 32 get_preserve_trailing_whitespace 0 4 855 42 TextNode::get_preserve_trailing_whitespace 0 0 101
/**
 * Returns the preserve_trailing_whitespace flag.  See
 * set_preserve_trailing_whitespace().
 */
67
inline bool TextNode::get_preserve_trailing_whitespace(void) const;

783 32 has_preserve_trailing_whitespace 0 4 855 42 TextNode::has_preserve_trailing_whitespace 0 1 386 10
/**
 *
 */
67
inline bool TextNode::has_preserve_trailing_whitespace(void) const;

784 32 set_preserve_trailing_whitespace 0 4 855 42 TextNode::set_preserve_trailing_whitespace 0 0 344
/**
 * Sets the preserve_trailing_whitespace flag.  When this is set, trailing
 * whitespace at the end of the line is not stripped when the text is
 * wordwrapped (it is stripped by default).  Since the trailing whitespace is
 * invisible, this is important primarily for determining the proper width of
 * a frame or card behind the text.
 */
90
inline void TextNode::set_preserve_trailing_whitespace(bool preserve_trailing_whitespace);

785 34 clear_preserve_trailing_whitespace 0 4 855 44 TextNode::clear_preserve_trailing_whitespace 0 1 387 10
/**
 *
 */
63
inline void TextNode::clear_preserve_trailing_whitespace(void);

786 14 get_text_color 0 4 855 24 TextNode::get_text_color 0 0 10
/**
 *
 */
51
inline LColor TextNode::get_text_color(void) const;

787 14 has_text_color 0 4 855 24 TextNode::has_text_color 0 1 388 76
/**
 * Returns true if a text color was specified with set_text_color().
 */
49
inline bool TextNode::has_text_color(void) const;

788 16 get_shadow_color 0 4 855 26 TextNode::get_shadow_color 0 0 10
/**
 *
 */
53
inline LColor TextNode::get_shadow_color(void) const;

789 16 has_shadow_color 0 4 855 26 TextNode::has_shadow_color 0 1 389 10
/**
 *
 */
51
inline bool TextNode::has_shadow_color(void) const;

790 10 get_shadow 0 4 855 20 TextNode::get_shadow 0 0 125
/**
 * Returns the offset of the shadow as set by set_shadow().  It is an error to
 * call this if has_shadow() is false.
 */
49
inline LVector2 TextNode::get_shadow(void) const;

791 10 has_shadow 0 4 855 20 TextNode::has_shadow 0 1 390 10
/**
 *
 */
45
inline bool TextNode::has_shadow(void) const;

792 7 get_bin 0 4 855 17 TextNode::get_bin 0 0 97
/**
 * Returns the drawing bin set with set_bin(), or empty string if no bin has
 * been set.
 */
56
inline std::string const &TextNode::get_bin(void) const;

793 7 has_bin 0 4 855 17 TextNode::has_bin 0 1 391 98
/**
 * Returns true if an explicit drawing bin has been set via set_bin(), false
 * otherwise.
 */
42
inline bool TextNode::has_bin(void) const;

794 14 get_draw_order 0 4 855 24 TextNode::get_draw_order 0 0 63
/**
 * Returns the drawing order set with set_draw_order().
 */
48
inline int TextNode::get_draw_order(void) const;

795 14 has_draw_order 0 4 855 24 TextNode::has_draw_order 0 1 392 10
/**
 *
 */
49
inline bool TextNode::has_draw_order(void) const;

796 13 get_tab_width 0 4 855 23 TextNode::get_tab_width 0 0 53
/**
 * Returns the width set via set_tab_width().
 */
55
inline PN_stdfloat TextNode::get_tab_width(void) const;

797 13 has_tab_width 0 4 855 23 TextNode::has_tab_width 0 1 393 10
/**
 *
 */
48
inline bool TextNode::has_tab_width(void) const;

798 15 get_glyph_scale 0 4 855 25 TextNode::get_glyph_scale 0 0 85
/**
 * Returns the scale factor of each letter as specified by set_glyph_scale().
 */
57
inline PN_stdfloat TextNode::get_glyph_scale(void) const;

799 15 has_glyph_scale 0 4 855 25 TextNode::has_glyph_scale 0 1 394 10
/**
 *
 */
50
inline bool TextNode::has_glyph_scale(void) const;

800 15 get_glyph_shift 0 4 855 25 TextNode::get_glyph_shift 0 0 90
/**
 * Returns the vertical shift of each letter as specified by
 * set_glyph_shift().
 */
57
inline PN_stdfloat TextNode::get_glyph_shift(void) const;

801 15 has_glyph_shift 0 4 855 25 TextNode::has_glyph_shift 0 1 395 10
/**
 *
 */
50
inline bool TextNode::has_glyph_shift(void) const;

802 14 get_text_scale 0 4 855 24 TextNode::get_text_scale 0 0 81
/**
 * Returns the scale factor of the text as specified by set_text_scale().
 */
56
inline PN_stdfloat TextNode::get_text_scale(void) const;

803 14 has_text_scale 0 4 855 24 TextNode::has_text_scale 0 1 396 10
/**
 *
 */
49
inline bool TextNode::has_text_scale(void) const;

804 14 set_text_scale 0 4 855 24 TextNode::set_text_scale 0 0 284
/**
 * Specifies the factor by which to scale the text, in addition to any
 * scalings imposed by the node, as well as in addition to the glyph scale.
 *
 * The text scale is not cumulative when applied to nested TextProperties.
 * See also set_glyph_scale(), which is cumulative.
 */
61
inline void TextNode::set_text_scale(PN_stdfloat text_scale);

805 16 clear_text_scale 0 4 855 26 TextNode::clear_text_scale 0 1 397 10
/**
 *
 */
45
inline void TextNode::clear_text_scale(void);

806 14 get_class_type 0 4 855 24 TextNode::get_class_type 0 1 371 0
49
static TypeHandle TextNode::get_class_type(void);

397
1 0 0 6 3 809 0 0 96 /**
 * Returns the Unicode value that corresponds to the character this glyph
 * represents.
 */ 1 4 this 3 863  
2 0 0 6 4 818 0 0 239 /**
 * Returns true if this glyph contains the definition for a simple quad,
 * rather than a more complex piece of geometry.
 *
 * You may still call get_geom() even if this returns true, which will
 * synthesize a Geom for this quad.
 */ 1 4 this 3 863  
3 0 0 6 5 818 0 0 255 /**
 * Assuming that this glyph is representable as a textured quad, returns its
 * dimensions and UV range.  Returns false if it is not representable as a
 * quad, or if it is whitespace.
 *
 * The order of the components is left, bottom, right, top.
 */ 3 4 this 3 863  10 dimensions 1 865  9 texcoords 1 865  
4 0 0 7 6 810 0 0 67 /**
 * Returns the state in which the glyph should be rendered.
 */ 1 4 this 3 863  
5 0 0 6 7 813 0 0 181 /**
 * Returns the distance by which the character pointer should be advanced
 * after placing this character; i.e.  the approximate width the character
 * takes up on the line.
 */ 1 4 this 3 863  
6 0 0 6 17 818 0 0 127 /**
 * Returns true if this glyph represents invisible whitespace, or false if it
 * corresponds to some visible character.
 */ 1 4 this 3 863  
7 0 0 7 18 866 0 0 195 /**
 * Returns a Geom that renders the particular glyph.  It will be generated if
 * necessary.
 *
 * This method will always return a copy of the Geom, so the caller is free to
 * modify it.
 */ 2 4 this 3 863  10 usage_hint 1 852  
8 0 0 7 19 868 0 0 0 0 
9 0 0 7 28 841 410 0 0 1 4 this 3 869  
10 0 0 6 29 818 0 0 79 /**
 * Returns true if the font is valid and ready to use, false otherwise.
 */ 1 4 this 3 869  
11 0 0 6 31 813 0 0 65 /**
 * Returns the number of units high each line of text is.
 */ 1 4 this 3 869  
12 0 0 4 32 871 0 0 65 /**
 * Changes the number of units high each line of text is.
 */ 2 4 this 3 841  11 line_height 1 813  
13 0 0 6 36 813 0 0 55 /**
 * Returns the number of units wide a space is.
 */ 1 4 this 3 869  
14 0 0 4 37 871 0 0 55 /**
 * Changes the number of units wide a space is.
 */ 2 4 this 3 841  13 space_advance 1 813  
15 0 0 7 39 863 0 0 294 /**
 * Gets the glyph associated with the given character code, as well as an
 * optional scaling parameter that should be applied to the glyph's geometry
 * and advance parameters.  Returns the glyph on success.  On failure, it may
 * still return a printable glyph, or it may return NULL.
 */ 2 4 this 3 841  9 character 1 809  
16 0 0 6 40 813 0 0 180 /**
 * Returns the amount by which to offset the second glyph when it directly
 * follows the first glyph.  This is an additional offset that is added on top
 * of the advance.
 */ 3 4 this 3 869  5 first 1 809  6 second 1 809  
17 0 0 4 41 871 0 0 10 /**
 *
 */ 3 4 this 3 869  3 out 1 872  12 indent_level 1 809  
18 0 0 7 42 868 0 0 0 0 
19 0 0 6 30 818 0 0 0 1 4 this 3 869  
20 0 0 7 21 874 0 0 0 1 4 this 3 841  
21 0 0 6 24 875 0 0 0 1 4 this 3 841  
22 0 0 7 44 820 429 0 61 /**
 * Returns the DynamicTextPage that this glyph is on.
 */ 1 4 this 3 876  
23 0 0 6 56 818 0 0 207 /**
 * Returns true if the particular position this glyph has been assigned to
 * overlaps the rectangle whose top left corner is at x, y and whose size is
 * given by x_size, y_size, or false otherwise.
 */ 5 4 this 3 876  1 x 1 809  1 y 1 809  6 x_size 1 809  6 y_size 1 809  
24 0 0 6 57 813 0 0 99 /**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
25 0 0 6 58 813 0 0 99 /**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
26 0 0 6 59 813 0 0 99 /**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
27 0 0 6 60 813 0 0 99 /**
 * Returns the vertex coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
28 0 0 6 61 813 0 0 95 /**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
29 0 0 6 62 813 0 0 95 /**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
30 0 0 6 63 813 0 0 95 /**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
31 0 0 6 64 813 0 0 95 /**
 * Returns the UV coordinates that can be used when creating a custom text
 * renderer.
 */ 1 4 this 3 876  
32 0 0 7 65 868 0 0 0 0 
33 0 0 15 54 820 429 0 0 1 6 param0 0 878  
34 0 0 6 49 880 0 0 61 /**
 * Returns the size of the page (texture), in pixels.
 */ 1 4 this 3 878  
35 0 0 6 50 809 0 0 63 /**
 * Returns the x size of the page (texture), in pixels.
 */ 1 4 this 3 878  
36 0 0 6 51 809 0 0 63 /**
 * Returns the y size of the page (texture), in pixels.
 */ 1 4 this 3 878  
37 0 0 6 52 818 0 0 67 /**
 * Returns true if the page has no glyphs, false otherwise.
 */ 1 4 this 3 878  
38 0 0 7 53 868 0 0 0 0 
39 0 0 15 72 883 445 0 10 /**
 *
 */ 1 4 copy 1 881  
40 0 0 23 72 883 445 0 173 /**
 * The constructor expects the name of some font file that FreeType can read,
 * along with face_index, indicating which font within the file to load
 * (usually 0).
 */ 2 13 font_filename 1 884  10 face_index 5 809  
41 0 0 23 72 883 445 0 137 /**
 * This constructor accepts a table of data representing the font file, loaded
 * from some source other than a filename on disk.
 */ 3 9 font_data 1 887  11 data_length 1 809  10 face_index 1 809  
42 0 0 7 74 841 410 0 47 /**
 * Returns a new copy of the same font.
 */ 1 4 this 3 881  
43 0 0 6 75 887 0 0 124 /**
 * Disambiguates the get_name() method between that inherited from TextFont
 * and that inherited from FreetypeFont.
 */ 1 4 this 3 881  
44 0 0 6 76 818 0 0 295 /**
 * Sets the point size of the font.  This controls the apparent size of the
 * font onscreen.  By convention, a 10 point font is about 1 screen unit high.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 2 4 this 3 883  10 point_size 1 813  
45 0 0 6 77 813 0 0 46 /**
 * Returns the point size of the font.
 */ 1 4 this 3 881  
46 0 0 6 79 818 0 0 433 /**
 * Set the resolution of the texture map, and hence the clarity of the
 * resulting font.  This sets the number of pixels in the texture map that are
 * used for each onscreen unit.
 *
 * Setting this number larger results in an easier to read font, but at the
 * cost of more texture memory.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 2 4 this 3 883  15 pixels_per_unit 1 813  
47 0 0 6 80 813 0 0 81 /**
 * Returns the resolution of the texture map.  See set_pixels_per_unit().
 */ 1 4 this 3 881  
48 0 0 6 82 818 0 0 591 /**
 * Sets the factor by which the font is rendered larger by the FreeType
 * library before being filtered down to its actual size in the texture as
 * specified by set_pixels_per_unit().  This may be set to a number larger
 * than 1.0 to improve the font's antialiasing (since FreeType doesn't really
 * do a swell job of antialiasing by itself).  There is some performance
 * implication for setting this different than 1.0, but it is probably small.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 2 4 this 3 883  12 scale_factor 1 813  
49 0 0 6 83 813 0 0 74 /**
 * Returns the antialiasing scale factor.  See set_scale_factor().
 */ 1 4 this 3 881  
50 0 0 4 85 871 0 0 469 /**
 * Sets whether the Freetype library's built-in antialias mode is enabled.
 * There are two unrelated ways to achieve antialiasing: with Freetype's
 * native antialias mode, and with the use of a scale_factor greater than one.
 * By default, both modes are enabled.
 *
 * At low resolutions, some fonts may do better with one mode or the other.
 * In general, Freetype's native antialiasing will produce less blurry
 * results, but may introduce more artifacts.
 */ 2 4 this 3 883  16 native_antialias 1 818  
51 0 0 6 86 818 0 0 105 /**
 * Returns whether Freetype's built-in antialias mode is enabled.  See
 * set_native_antialias().
 */ 1 4 this 3 881  
52 0 0 6 88 809 0 0 386 /**
 * This is used to report whether the requested pixel size is being only
 * approximated by a fixed-pixel-size font.  This returns 0 in the normal
 * case, in which a scalable font is used, or the fixed-pixel-size font has
 * exactly the requested pixel size.
 *
 * If this returns non-zero, it is the pixel size of the font that we are
 * using to approximate our desired size.
 */ 1 4 this 3 881  
53 0 0 6 90 813 0 0 65 /**
 * Returns the number of units high each line of text is.
 */ 1 4 this 3 881  
54 0 0 6 91 813 0 0 55 /**
 * Returns the number of units wide a space is.
 */ 1 4 this 3 881  
55 0 0 4 92 871 0 0 207 /**
 * Sets the number of pixels of padding that is added around the border of
 * each glyph before adding it to the texture map.  This reduces the bleed in
 * from neighboring glyphs in the texture map.
 */ 2 4 this 3 883  14 texture_margin 1 809  
56 0 0 6 93 809 0 0 146 /**
 * Returns the number of pixels of padding that is added around the border of
 * each glyph in the texture map.  See set_texture_margin().
 */ 1 4 this 3 881  
57 0 0 4 94 871 0 0 452 /**
 * Sets the number of pixels of padding that is included around each glyph in
 * the generated polygons.  This helps prevent the edges of the glyphs from
 * being cut off at small minifications.  It is not related to the amount of
 * extra pixels reserved in the texture map (but it should be set somewhat
 * smaller than this number, which is controlled by set_texture_margin(), to
 * prevent bleed-in from neighboring letters in the texture).
 */ 2 4 this 3 883  11 poly_margin 1 813  
58 0 0 6 95 813 0 0 139 /**
 * Returns the number of pixels of padding that is included around each glyph
 * in the generated polygons.  See set_poly_margin().
 */ 1 4 this 3 881  
59 0 0 4 98 871 0 0 90 /**
 * Sets the x, y size of the textures that are created for the
 * DynamicTextFont.
 */ 2 4 this 3 883  9 page_size 1 880  
60 0 0 4 98 871 0 0 90 /**
 * Sets the x, y size of the textures that are created for the
 * DynamicTextFont.
 */ 3 4 this 3 883  6 x_size 1 809  6 y_size 1 809  
61 0 0 6 99 880 0 0 109 /**
 * Returns the size of the textures that are created for the DynamicTextFont.
 * See set_page_size().
 */ 1 4 this 3 881  
62 0 0 6 100 809 0 0 112 /**
 * Returns the x size of the textures that are created for the
 * DynamicTextFont.  See set_page_size().
 */ 1 4 this 3 881  
63 0 0 6 101 809 0 0 112 /**
 * Returns the y size of the textures that are created for the
 * DynamicTextFont.  See set_page_size().
 */ 1 4 this 3 881  
64 0 0 4 105 871 0 0 91 /**
 * Sets the filter type used when minimizing the textures created for this
 * font.
 */ 2 4 this 3 883  6 filter 1 827  
65 0 0 6 106 827 0 0 94 /**
 * Returns the filter type used when minimizing the textures created for this
 * font.
 */ 1 4 this 3 881  
66 0 0 4 107 871 0 0 90 /**
 * Sets the filter type used when enlarging the textures created for this
 * font.
 */ 2 4 this 3 883  6 filter 1 827  
67 0 0 6 108 827 0 0 93 /**
 * Returns the filter type used when enlarging the textures created for this
 * font.
 */ 1 4 this 3 881  
68 0 0 4 109 871 0 0 210 /**
 * Enables or disables anisotropic filtering on the textures created for this
 * font.  The default value is specified by the text-anisotropic-degree
 * variable.  See Texture::set_anisotropic_degree().
 */ 2 4 this 3 883  18 anisotropic_degree 1 809  
69 0 0 6 110 809 0 0 118 /**
 * Returns the current anisotropic degree for textures created for this font.
 * See set_anisotropic_degree().
 */ 1 4 this 3 881  
70 0 0 4 116 871 0 0 211 /**
 * Specifies the way the glyphs on this particular font are generated.  The
 * default is RM_texture, which is the only mode supported for bitmap fonts.
 * Other modes are possible for most modern fonts.
 */ 2 4 this 3 883  11 render_mode 1 817  
71 0 0 6 117 817 0 0 103 /**
 * Returns the way the glyphs on this particular font are generated.  See
 * set_render_mode().
 */ 1 4 this 3 881  
72 0 0 4 119 871 0 0 485 /**
 * Changes the color of the foreground pixels of the font as they are rendered
 * into the font texture.  The default is (1, 1, 1, 1), or opaque white, which
 * allows text created with the font to be colored individually.  Normally,
 * you would not change this unless you really need a particular color effect
 * to appear in the font itself.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 2 4 this 3 883  2 fg 1 888  
73 0 0 6 120 888 0 0 127 /**
 * Returns the color of the foreground pixels of the font as they are rendered
 * into the font texture.  See set_fg().
 */ 1 4 this 3 881  
74 0 0 4 121 871 0 0 673 /**
 * Changes the color of the background pixels of the font as they are rendered
 * into the font texture.  The default is (1, 1, 1, 0), or transparent white,
 * which allows text created with the font to be colored individually.  (Note
 * that it should not generally be (0, 0, 0, 0), which would tend to bleed
 * into the foreground color, unless you have also specified a outline color
 * of (0, 0, 0, 1)) .
 *
 * Normally, you would not change this unless you really need a particular
 * color effect to appear in the font itself.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 2 4 this 3 883  2 bg 1 888  
75 0 0 6 122 888 0 0 127 /**
 * Returns the color of the background pixels of the font as they are rendered
 * into the font texture.  See set_bg().
 */ 1 4 this 3 881  
76 0 0 4 123 871 0 0 722 /**
 * Sets up the font to have an outline around each font letter.  This is
 * achieved via a Gaussian post-process as each letter is generated; there is
 * some runtime cost for this effect, but it is minimal as each letter is
 * normally generated only once and then cached.
 *
 * The color is the desired color of the outline, width is the number of
 * points beyond the letter that the outline extends (a typical font is 10
 * points high), and feather is a number in the range 0.0 .. 1.0 that controls
 * the softness of the outline.  Set the width to 0.0 to disable the outline.
 *
 * This should only be called before any characters have been requested out of
 * the font, or immediately after calling clear().
 */ 4 4 this 3 883  13 outline_color 1 888  13 outline_width 1 813  15 outline_feather 1 813  
77 0 0 6 124 888 0 0 129 /**
 * Returns the color of the outline pixels of the font as they are rendered
 * into the font texture.  See set_outline().
 */ 1 4 this 3 881  
78 0 0 6 125 813 0 0 130 /**
 * Returns the width of the outline pixels of the font, as the number of
 * points beyond each letter.  See set_outline().
 */ 1 4 this 3 881  
79 0 0 6 126 813 0 0 124 /**
 * Returns the softness of the outline pixels of the font, as a value in the
 * range 0.0 to 1.0. See set_outline().
 */ 1 4 this 3 881  
80 0 0 6 127 833 0 0 138 /**
 * Returns the texture format used to render the individual pages.  This is
 * set automatically according to the colors selected.
 */ 1 4 this 3 881  
81 0 0 6 136 809 0 0 313 /**
 * Returns the number of pages associated with the font.  Initially, the font
 * has zero pages; when the first piece of text is rendered with the font, it
 * will add additional pages as needed.  Each page is a Texture object that
 * contains the images for each of the glyphs currently in use somewhere.
 */ 1 4 this 3 881  
82 0 0 7 137 820 429 0 306 /**
 * Returns the nth page associated with the font.  Initially, the font has
 * zero pages; when the first piece of text is rendered with the font, it will
 * add additional pages as needed.  Each page is a Texture object that
 * contains the images for each of the glyphs currently in use somewhere.
 */ 2 4 this 3 881  1 n 1 809  
83 0 0 6 140 809 0 0 136 /**
 * Removes all of the glyphs from the font that are no longer being used by
 * any Geoms.  Returns the number of glyphs removed.
 */ 1 4 this 3 883  
84 0 0 4 141 871 0 0 385 /**
 * Drops all the glyphs out of the cache and frees any association with any
 * previously-generated pages.
 *
 * Calling this frequently can result in wasted texture memory, as any
 * previously rendered text will still keep a pointer to the old, previously-
 * generated pages.  As long as the previously rendered text remains around,
 * the old pages will also remain around.
 */ 1 4 this 3 883  
85 0 0 4 142 871 0 0 10 /**
 *
 */ 3 4 this 3 881  3 out 1 872  12 indent_level 1 809  
86 0 0 7 143 868 0 0 0 0 
87 0 0 7 67 841 410 0 0 1 4 this 3 883  
88 0 0 6 70 889 0 0 0 1 4 this 3 883  
89 0 0 6 145 818 0 0 74 /**
 * Returns true if the font has ever been loaded, false otherwise.
 */ 1 8 filename 1 887  
90 0 0 6 146 818 0 0 299 /**
 * Loads the given filename up into a font, if it has not already been loaded,
 * and returns true to indicate success, or false to indicate failure.  If
 * this returns true, it is guaranteed that a subsequent call to load_font()
 * with the same font name will return a valid Font pointer.
 */ 1 8 filename 1 887  
91 0 0 7 147 841 410 0 253 /**
 * Loads the given filename up into a font, if it has not already been loaded,
 * and returns the new font.  If a font with the same filename was previously
 * loaded, returns that one instead.  If the font file cannot be found,
 * returns NULL.
 */ 1 8 filename 1 887  
92 0 0 4 148 871 0 0 162 /**
 * Adds the indicated already-loaded font to the pool.  The font will always
 * replace any previously-loaded font in the pool that had the same filename.
 */ 2 8 filename 1 887  4 font 1 841  
93 0 0 4 149 871 0 0 261 /**
 * Removes the indicated font from the pool, indicating it will never be
 * loaded again; the font may then be freed.  If this function is never
 * called, a reference count will be maintained on every font ever loaded,
 * and fonts will never be freed.
 */ 1 8 filename 1 887  
94 0 0 4 150 871 0 0 83 /**
 * Releases all fonts in the pool and restores the pool to the empty state.
 */ 0 
95 0 0 6 151 809 0 0 203 /**
 * Releases only those fonts in the pool that have a reference count of
 * exactly 1; i.e.  only those fonts that are not being used outside of the
 * pool.  Returns the number of fonts released.
 */ 0 
96 0 0 4 152 871 0 0 78 /**
 * Lists the contents of the font pool to the indicated output stream.
 */ 1 3 out 1 872  
97 0 0 4 153 871 0 0 78 /**
 * Lists the contents of the font pool to the indicated output stream.
 */ 1 3 out 1 872  
98 0 0 7 157 868 0 0 0 0 
99 0 0 23 159 891 503 0 374 /**
 * The constructor expects the root node to a model generated via egg-mkfont,
 * which consists of a set of models, one per each character in the font.
 *
 * If a CoordinateSystem value is specified, it informs the font of the
 * coordinate system in which this model was generated.  "up" in this
 * coordinate system will be the direction of the top of the letters.
 */ 2 8 font_def 1 890  2 cs 5 862  
100 0 0 7 160 868 0 0 0 0 
101 0 0 7 165 892 595 0 10 /**
 *
 */ 0 
102 0 0 15 165 892 595 0 10 /**
 *
 */ 1 4 copy 1 893  
103 0 0 6 166 892 0 0 0 2 4 this 3 892  4 copy 1 893  
104 0 0 6 167 818 0 0 0 2 4 this 3 893  5 other 1 893  
105 0 0 6 168 818 0 0 0 2 4 this 3 893  5 other 1 893  
106 0 0 4 169 871 0 0 136 /**
 * Unsets all properties that have been specified so far, and resets the
 * TextProperties structure to its initial empty state.
 */ 1 4 this 3 892  
107 0 0 6 170 818 0 0 79 /**
 * Returns true if any properties have been specified, false otherwise.
 */ 1 4 this 3 893  
108 0 0 4 171 871 0 0 122 /**
 * Specifies the default font to be used for any TextNode whose font is
 * uninitialized or NULL.  See set_font().
 */ 1 6 param0 0 841  
109 0 0 7 172 841 410 0 122 /**
 * Specifies the default font to be used for any TextNode whose font is
 * uninitialized or NULL.  See set_font().
 */ 0 
110 0 0 4 173 871 0 0 160 /**
 * Sets the font that will be used when making text.  If this is set to NULL,
 * the default font will be used, which can be set via set_default_font().
 */ 2 4 this 3 892  4 font 1 841  
111 0 0 4 174 871 0 0 49 /**
 * Restores the default font to the text.
 */ 1 4 this 3 892  
112 0 0 6 175 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
113 0 0 7 176 841 410 0 110 /**
 * Returns the font currently in use, if any.  If no font is in use, this
 * returns the default font.
 */ 1 4 this 3 893  
114 0 0 4 177 871 0 0 707 /**
 * Sets the small_caps flag.  When this is set, lowercase letters are
 * generated as scaled-down versions of their uppercase equivalents.  This is
 * particularly useful to set for fonts that do not have lowercase letters.
 *
 * It is also a good idea to set this for a (dynamic) font that has already
 * implemented lowercase letters as scaled-down versions of their uppercase
 * equivalents, since without this flag the texture memory may needlessly
 * duplicate equivalent glyphs for upper and lowercase letters.  Setting this
 * flag causes the texture memory to share the mixed-case letters.
 *
 * The amount by which the lowercase letters are scaled is specified by
 * set_small_caps_scale().
 */ 2 4 this 3 892  10 small_caps 1 818  
115 0 0 4 178 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
116 0 0 6 179 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
117 0 0 6 180 818 0 0 62 /**
 * Returns the small_caps flag.  See set_small_caps().
 */ 1 4 this 3 893  
118 0 0 4 181 871 0 0 210 /**
 * Sets the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps().
 * Normally, this will be a number less than one.
 */ 2 4 this 3 892  16 small_caps_scale 1 813  
119 0 0 4 182 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
120 0 0 6 183 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
121 0 0 6 184 813 0 0 193 /**
 * Returns the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps()
 * and set_small_caps_scale().
 */ 1 4 this 3 893  
122 0 0 4 185 871 0 0 70 /**
 * Specifies the factor by which the text slants to the right.
 */ 2 4 this 3 892  5 slant 1 813  
123 0 0 4 186 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
124 0 0 6 187 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
125 0 0 6 188 813 0 0 83 /**
 * Returns the factor by which the text is specified to slant to the right.
 */ 1 4 this 3 893  
126 0 0 4 189 871 0 0 169 /**
 * Sets the underscore flag.  When this is set, the text is underscored with a
 * one-pixel line the same color as the text foreground, drawn at the
 * baseline.
 */ 2 4 this 3 892  10 underscore 1 818  
127 0 0 4 190 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
128 0 0 6 191 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
129 0 0 6 192 818 0 0 62 /**
 * Returns the underscore flag.  See set_underscore().
 */ 1 4 this 3 893  
130 0 0 4 193 871 0 0 176 /**
 * Specifies the vertical height of the underscore, relative to the text
 * baseline.  This only has meaning if the underscore mode is enabled with
 * set_underscore().
 */ 2 4 this 3 892  17 underscore_height 1 813  
131 0 0 4 194 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
132 0 0 6 195 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
133 0 0 6 196 813 0 0 86 /**
 * Returns the vertical height of the underscore; see set_underscore_height().
 */ 1 4 this 3 893  
134 0 0 4 197 871 0 0 66 /**
 * Specifies the alignment of the text within its margins.
 */ 2 4 this 3 892  10 align_type 1 839  
135 0 0 4 198 871 0 0 54 /**
 * Restores the default alignment of the text.
 */ 1 4 this 3 892  
136 0 0 6 199 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
137 0 0 6 200 839 0 0 10 /**
 *
 */ 1 4 this 3 893  
138 0 0 4 201 871 0 0 149 /**
 * Specifies the amount of extra space that is inserted before the first
 * character of each line.  This can be thought of as a left margin.
 */ 2 4 this 3 892  6 indent 1 813  
139 0 0 4 202 871 0 0 84 /**
 * Removes the indent setting from the text.  Text will be as wide as it is.
 */ 1 4 this 3 892  
140 0 0 6 203 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
141 0 0 6 204 813 0 0 10 /**
 *
 */ 1 4 this 3 893  
142 0 0 4 205 871 0 0 152 /**
 * Sets the text up to automatically wordwrap when it exceeds the indicated
 * width.  This can be thought of as a right margin or margin width.
 */ 2 4 this 3 892  8 wordwrap 1 813  
143 0 0 4 206 871 0 0 86 /**
 * Removes the wordwrap setting from the text.  Text will be as wide as it is.
 */ 1 4 this 3 892  
144 0 0 6 207 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
145 0 0 6 208 813 0 0 10 /**
 *
 */ 1 4 this 3 893  
146 0 0 4 209 871 0 0 344 /**
 * Sets the preserve_trailing_whitespace flag.  When this is set, trailing
 * whitespace at the end of the line is not stripped when the text is
 * wordwrapped (it is stripped by default).  Since the trailing whitespace is
 * invisible, this is important primarily for determining the proper width of
 * a frame or card behind the text.
 */ 2 4 this 3 892  28 preserve_trailing_whitespace 1 818  
147 0 0 4 210 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
148 0 0 6 211 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
149 0 0 6 212 818 0 0 101 /**
 * Returns the preserve_trailing_whitespace flag.  See
 * set_preserve_trailing_whitespace().
 */ 1 4 this 3 893  
150 0 0 4 213 871 0 0 227 /**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */ 2 4 this 3 892  10 text_color 1 888  
151 0 0 4 213 871 0 0 227 /**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */ 5 4 this 3 892  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
152 0 0 4 214 871 0 0 117 /**
 * Removes the text color specification; the text will be colored whatever it
 * was in the source font file.
 */ 1 4 this 3 892  
153 0 0 6 215 818 0 0 76 /**
 * Returns true if a text color was specified with set_text_color().
 */ 1 4 this 3 893  
154 0 0 7 216 894 0 0 10 /**
 *
 */ 1 4 this 3 893  
155 0 0 4 217 871 0 0 10 /**
 *
 */ 2 4 this 3 892  12 shadow_color 1 888  
156 0 0 4 217 871 0 0 10 /**
 *
 */ 5 4 this 3 892  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
157 0 0 4 218 871 0 0 50 /**
 * Removes the shadow color specification.
 */ 1 4 this 3 892  
158 0 0 6 219 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
159 0 0 7 220 894 0 0 10 /**
 *
 */ 1 4 this 3 893  
160 0 0 4 221 871 0 0 151 /**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */ 2 4 this 3 892  13 shadow_offset 1 895  
161 0 0 4 221 871 0 0 151 /**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */ 3 4 this 3 892  7 xoffset 1 813  7 yoffset 1 813  
162 0 0 4 222 871 0 0 69 /**
 * Specifies that a shadow will not be drawn behind the text.
 */ 1 4 this 3 892  
163 0 0 6 223 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
164 0 0 7 224 899 0 0 125 /**
 * Returns the offset of the shadow as set by set_shadow().  It is an error to
 * call this if has_shadow() is false.
 */ 1 4 this 3 893  
165 0 0 4 225 871 0 0 350 /**
 * Names the CullBin that the text geometry should be assigned to.  If this is
 * set, then a CullBinAttrib will be created to explicitly place each
 * component in the named bin.
 *
 * The draw_order value will also be passed to each CullBinAttrib as
 * appropriate; this is particularly useful if this names a CullBinFixed, e.g.
 * "fixed".
 */ 2 4 this 3 892  3 bin 1 887  
166 0 0 4 226 871 0 0 158 /**
 * Removes the effect of a previous call to set_bin().  Text will be drawn in
 * whatever bin it would like to be drawn in, with no explicit ordering.
 */ 1 4 this 3 892  
167 0 0 6 227 818 0 0 98 /**
 * Returns true if an explicit drawing bin has been set via set_bin(), false
 * otherwise.
 */ 1 4 this 3 893  
168 0 0 6 228 887 0 0 97 /**
 * Returns the drawing bin set with set_bin(), or empty string if no bin has
 * been set.
 */ 1 4 this 3 893  
169 0 0 6 229 809 0 0 435 /**
 * Sets the drawing order of text created by the TextNode.  This is actually
 * the draw order of the card and frame.  The shadow is drawn at
 * _draw_order+1, and the text at _draw_order+2.
 *
 * This affects the sorting order assigned to the nodes as they are created,
 * and also is passed to whatever bin may be assigned via set_bin().
 *
 * The return value is the first unused draw_order number, e.g.  _draw_order +
 * 3.
 */ 2 4 this 3 892  10 draw_order 1 809  
170 0 0 4 230 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
171 0 0 6 231 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
172 0 0 6 232 809 0 0 63 /**
 * Returns the drawing order set with set_draw_order().
 */ 1 4 this 3 893  
173 0 0 4 233 871 0 0 160 /**
 * Sets the width of each tab stop, in screen units.  A tab character embedded
 * in the text will advance the horizontal position to the next tab stop.
 */ 2 4 this 3 892  9 tab_width 1 813  
174 0 0 4 234 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
175 0 0 6 235 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
176 0 0 6 236 813 0 0 53 /**
 * Returns the width set via set_tab_width().
 */ 1 4 this 3 893  
177 0 0 4 237 871 0 0 576 /**
 * Specifies the factor by which to scale each letter of the text as it is
 * placed, in addition to any scales inherited from the node or from
 * set_text_scale(). This can be used (possibly in conjunction with
 * set_glyph_shift()) to implement superscripting or subscripting.
 *
 * The glyph scale is cumulative when applied to nested TextProperties.  It is
 * intended primarily for implementing superscripts, not for scaling the text
 * in general.  See also set_text_scale(), which is intended primarily for
 * scaling the text in general, and is not cumulative.
 */ 2 4 this 3 892  11 glyph_scale 1 813  
178 0 0 4 238 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
179 0 0 6 239 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
180 0 0 6 240 813 0 0 85 /**
 * Returns the scale factor of each letter as specified by set_glyph_scale().
 */ 1 4 this 3 893  
181 0 0 4 241 871 0 0 206 /**
 * Specifies a vertical amount to shift each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_scale())
 * to implement superscripting or subscripting.
 */ 2 4 this 3 892  11 glyph_shift 1 813  
182 0 0 4 242 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
183 0 0 6 243 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
184 0 0 6 244 813 0 0 90 /**
 * Returns the vertical shift of each letter as specified by
 * set_glyph_shift().
 */ 1 4 this 3 893  
185 0 0 4 245 871 0 0 284 /**
 * Specifies the factor by which to scale the text, in addition to any
 * scalings imposed by the node, as well as in addition to the glyph scale.
 *
 * The text scale is not cumulative when applied to nested TextProperties.
 * See also set_glyph_scale(), which is cumulative.
 */ 2 4 this 3 892  10 text_scale 1 813  
186 0 0 4 246 871 0 0 10 /**
 *
 */ 1 4 this 3 892  
187 0 0 6 247 818 0 0 10 /**
 *
 */ 1 4 this 3 893  
188 0 0 6 248 813 0 0 81 /**
 * Returns the scale factor of the text as specified by set_text_scale().
 */ 1 4 this 3 893  
189 0 0 4 249 871 0 0 142 /**
 * Specifies the text direction.  If none is specified, it will be guessed
 * based on the contents of the string.
 *
 * @since 1.10.0
 */ 2 4 this 3 892  9 direction 1 840  
190 0 0 4 250 871 0 0 160 /**
 * Clears the text direction setting.  If no text direction is specified, it
 * will be guessed based on the contents of the string.
 *
 * @since 1.10.0
 */ 1 4 this 3 892  
191 0 0 6 251 818 0 0 24 /**
 * @since 1.10.0
 */ 1 4 this 3 893  
192 0 0 6 252 840 0 0 97 /**
 * Returns the direction of the text as specified by set_direction().
 *
 * @since 1.10.0
 */ 1 4 this 3 893  
193 0 0 4 253 871 0 0 123 /**
 * Sets any properties that are explicitly specified in other on this object.
 * Leaves other properties unchanged.
 */ 2 4 this 3 892  5 other 1 893  
194 0 0 4 254 871 0 0 10 /**
 *
 */ 3 4 this 3 893  3 out 1 872  12 indent_level 5 809  
195 0 0 7 281 868 0 0 0 0 
196 0 0 7 284 900 603 0 10 /**
 *
 */ 0 
197 0 0 7 284 900 603 0 10 /**
 *
 */ 2 5 model 1 901  5 frame 1 903  
198 0 0 7 284 900 603 0 10 /**
 *
 */ 5 5 model 1 901  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
199 0 0 15 284 900 603 0 0 1 6 param0 0 905  
200 0 0 7 285 907 0 0 95 /**
 * Returns the NodePath associated with the graphic, that renders the desired
 * image.
 */ 1 4 this 3 905  
201 0 0 4 286 871 0 0 146 /**
 * Changes the NodePath associated with the graphic.  This NodePath should
 * contain geometry that will render the desired graphic image.
 */ 2 4 this 3 900  5 model 1 901  
202 0 0 7 289 865 0 0 457 /**
 * Returns the frame specified for the graphic.  This is the amount of space
 * that will be reserved for the graphic when it is embedded in a text
 * paragraph, in the form (left, right, bottom, top).
 *
 * The actual graphic, as rendered by the NodePath specified via set_model(),
 * should more or less fit within this rectangle.  It is not required to fit
 * completely within it, but if it does not, it may visually overlap with
 * nearby text.
 */ 1 4 this 3 905  
203 0 0 4 290 871 0 0 103 /**
 * Specifies the (left, right, bottom, top) bounding frame for the graphic.
 * See get_frame().
 */ 2 4 this 3 900  5 frame 1 903  
204 0 0 4 290 871 0 0 103 /**
 * Specifies the (left, right, bottom, top) bounding frame for the graphic.
 * See get_frame().
 */ 5 4 this 3 900  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
205 0 0 6 293 818 0 0 63 /**
 * Returns the instance_flag.  See set_instance_flag().
 */ 1 4 this 3 905  
206 0 0 4 294 871 0 0 355 /**
 * Sets the instance_flag.  When this is true, the graphic is directly
 * instanced to the scene graph whenever it appears; when it is false, the
 * graphic is copied.  The default is false, which is best for most
 * applications.  You might need to set it true for special kinds of
 * "graphics" like interactive elements, for instance a PGEntry.
 */ 2 4 this 3 900  13 instance_flag 1 818  
207 0 0 4 298 871 0 0 377 /**
 * Defines the TextProperties associated with the indicated name.  When the
 * name is subsequently encountered in text embedded between \1 characters in
 * a TextNode string, the following text will be rendered with these
 * properties.
 *
 * If there was already a TextProperties structure associated with this name,
 * it is quietly replaced with the new definition.
 */ 3 4 this 3 908  4 name 1 887  10 properties 1 893  
208 0 0 7 299 892 595 0 355 /**
 * Returns the TextProperties associated with the indicated name.  If there
 * was not previously a TextProperties associated with this name, a warning is
 * printed and then a default TextProperties structure is associated with the
 * name, and returned.
 *
 * Call has_properties() instead to check whether a particular name has been
 * defined.
 */ 2 4 this 3 908  4 name 1 887  
209 0 0 6 300 818 0 0 372 /**
 * Returns true if a TextProperties structure has been associated with the
 * indicated name, false otherwise.  Normally this means set_properties() has
 * been called with this name, but because get_properties() will implicitly
 * create a default TextProperties structure, it may also mean simply that
 * get_properties() has been called with the indicated name.
 */ 2 4 this 3 909  4 name 1 887  
210 0 0 4 301 871 0 0 71 /**
 * Removes the named TextProperties structure from the manager.
 */ 2 4 this 3 908  4 name 1 887  
211 0 0 4 302 871 0 0 271 /**
 * This flavor of set_graphic implicitly creates a frame for the model using
 * the model's actual computed bounding volume, as derived from
 * NodePath::calc_tight_bounds().  Create a TextGraphic object first if you
 * want to have explicit control of the frame.
 */ 3 4 this 3 908  4 name 1 887  5 model 1 901  
212 0 0 4 302 871 0 0 378 /**
 * Defines the TextGraphic associated with the indicated name.  When the name
 * is subsequently encountered in text embedded between \5 characters in a
 * TextNode string, the specified graphic will be embedded in the text at that
 * point.
 *
 * If there was already a TextGraphic structure associated with this name, it
 * is quietly replaced with the new definition.
 */ 3 4 this 3 908  4 name 1 887  7 graphic 1 905  
213 0 0 7 303 900 603 0 343 /**
 * Returns the TextGraphic associated with the indicated name.  If there was
 * not previously a TextGraphic associated with this name, a warning is
 * printed and then a default TextGraphic structure is associated with the
 * name, and returned.
 *
 * Call has_graphic() instead to check whether a particular name has been
 * defined.
 */ 2 4 this 3 908  4 name 1 887  
214 0 0 6 304 818 0 0 357 /**
 * Returns true if a TextGraphic structure has been associated with the
 * indicated name, false otherwise.  Normally this means set_graphic() has
 * been called with this name, but because get_graphic() will implicitly
 * create a default TextGraphic structure, it may also mean simply that
 * get_graphic() has been called with the indicated name.
 */ 2 4 this 3 909  4 name 1 887  
215 0 0 4 305 871 0 0 68 /**
 * Removes the named TextGraphic structure from the manager.
 */ 2 4 this 3 908  4 name 1 887  
216 0 0 4 306 871 0 0 10 /**
 *
 */ 3 4 this 3 909  3 out 1 872  12 indent_level 5 809  
217 0 0 6 307 908 0 0 74 /**
 * Returns the pointer to the global TextPropertiesManager object.
 */ 0 
218 0 0 15 309 913 616 0 10 /**
 *
 */ 1 4 copy 1 911  
219 0 0 7 309 913 616 0 10 /**
 *
 */ 1 7 encoder 1 914  
220 0 0 6 310 913 0 0 0 2 4 this 3 913  4 copy 1 911  
221 0 0 4 312 871 0 0 59 /**
 * Reinitializes the contents of the TextAssembler.
 */ 1 4 this 3 913  
222 0 0 4 313 871 0 0 278 /**
 * Specifies the UsageHint that will be applied to generated geometry.  The
 * default is UH_static, which is probably the right setting, but if you know
 * the TextNode's geometry will have a short lifespan, it may be better to set
 * it to UH_stream.  See geomEnums.h.
 */ 2 4 this 3 913  10 usage_hint 1 852  
223 0 0 6 314 852 0 0 102 /**
 * Returns the UsageHint that will be applied to generated geometry.  See
 * set_usage_hint().
 */ 1 4 this 3 911  
224 0 0 4 315 871 0 0 264 /**
 * If max_rows is greater than zero, no more than max_rows will be accepted.
 * Text beyond that will be truncated.
 *
 * Setting this will not truncate text immediately.  You must follow this up
 * with a call to set_wtext() to truncate the existing text.
 */ 2 4 this 3 913  8 max_rows 1 809  
225 0 0 6 316 809 0 0 123 /**
 * If max_rows is greater than zero, no more than max_rows will be accepted.
 * Text beyond that will be truncated.
 */ 1 4 this 3 911  
226 0 0 4 317 871 0 0 75 /**
 * Sets the dynamic_merge flag.  See TextNode::set_flatten_flags().
 */ 2 4 this 3 913  13 dynamic_merge 1 818  
227 0 0 6 318 818 0 0 78 /**
 * Returns the dynamic_merge flag.  See TextNode::set_flatten_flags().
 */ 1 4 this 3 911  
228 0 0 4 319 871 0 0 112 /**
 * Sets the multiline mode flag.  Set the multiline mode to allow text to
 * wrap.  It defaults to true.
 */ 2 4 this 3 913  4 flag 1 818  
229 0 0 6 320 818 0 0 79 /**
 * Returns the multline_mode flag.  See TextNode::set_multiline_mode().
 */ 1 4 this 3 911  
230 0 0 4 321 871 0 0 135 /**
 * Specifies the default TextProperties that are applied to the text in the
 * absence of any nested property change sequences.
 */ 2 4 this 3 913  10 properties 1 893  
231 0 0 6 322 893 0 0 133 /**
 * Returns the default TextProperties that are applied to the text in the
 * absence of any nested property change sequences.
 */ 1 4 this 3 911  
232 0 0 6 322 893 0 0 122 /**
 * Returns the TextProperties in effect for the object at the indicated
 * position in the pre-wordwrapped string.
 */ 2 4 this 3 911  1 n 1 809  
233 0 0 6 322 893 0 0 113 /**
 * Returns the TextProperties in effect for the object at the indicated
 * position in the indicated row.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
234 0 0 6 323 818 0 0 326 /**
 * Accepts a new text string and associated properties structure, and
 * precomputes the wordwrapping layout appropriately.  After this call,
 * get_wordwrapped_wtext() and get_num_rows() can be called.
 *
 * The return value is true if all the text is accepted, or false if some was
 * truncated (see set_max_rows()).
 */ 2 4 this 3 913  5 wtext 1 887  
235 0 0 6 324 818 0 0 595 /**
 * Replaces the 'count' characters from 'start' of the current text with the
 * indicated replacement text.  If the replacement text does not have count
 * characters, the length of the string will be changed accordingly.
 *
 * The substring may include nested formatting characters, but they must be
 * self-contained and self-closed.  The formatting characters are not
 * literally saved in the internal string; they are parsed at the time of the
 * set_wsubstr() call.
 *
 * The return value is true if all the text is accepted, or false if some was
 * truncated (see set_max_rows()).
 */ 4 4 this 3 913  5 wtext 1 887  5 start 1 809  5 count 1 809  
236 0 0 6 325 887 0 0 379 /**
 * Returns a wstring that represents the contents of the text, without any
 * embedded properties characters.  If there is an embedded graphic object, a
 * zero value is inserted in that position.
 *
 * This string has the same length as get_num_characters(), and the characters
 * in this string correspond one-to-one with the characters returned by
 * get_character(n).
 */ 1 4 this 3 911  
237 0 0 6 326 887 0 0 471 /**
 * Returns a wstring that represents the contents of the text, with newlines
 * inserted according to the wordwrapping.  The string will contain no
 * embedded properties characters.  If there is an embedded graphic object, a
 * zero value is inserted in that position.
 *
 * This string has the same number of newline characters as get_num_rows(),
 * and the characters in this string correspond one-to-one with the characters
 * returned by get_character(r, c).
 */ 1 4 this 3 911  
238 0 0 6 327 887 0 0 258 /**
 * Returns a wstring that represents the contents of the text.
 *
 * The string will contain embedded properties characters, which may not
 * exactly match the embedded properties characters of the original string,
 * but it will encode the same way.
 */ 1 4 this 3 911  
239 0 0 6 328 887 0 0 598 /**
 * Returns a wstring that represents the contents of the text, with newlines
 * inserted according to the wordwrapping.
 *
 * The string will contain embedded properties characters, which may not
 * exactly match the embedded properties characters of the original string,
 * but it will encode the same way.
 *
 * Embedded properties characters will be closed before every newline, then
 * reopened (if necessary) on the subsequent character following the newline.
 * This means it will be safe to divide the text up at the newline characters
 * and treat each line as an independent piece.
 */ 1 4 this 3 911  
240 0 0 6 330 809 0 0 330 /**
 * Computes the row index of the nth character or graphic object in the text
 * and returns it.
 *
 * If the nth character is not a normal printable character with a position in
 * the wordwrapped string, returns -1 (for instance, a soft-hyphen character,
 * or a newline character, may not have a corresponding position).
 */ 2 4 this 3 911  1 n 1 809  
241 0 0 6 331 809 0 0 333 /**
 * Computes the column index of the nth character or graphic object in the
 * text and returns it.
 *
 * If the nth character is not a normal printable character with a position in
 * the wordwrapped string, returns -1 (for instance, a soft-hyphen character,
 * or a newline character, may not have a corresponding position).
 */ 2 4 this 3 911  1 n 1 809  
242 0 0 6 332 809 0 0 299 /**
 * Computes the character index of the character at the rth row and cth column
 * position.  This is the inverse of calc_r_c().
 *
 * It is legal for c to exceed the index number of the last column by 1, and
 * it is legal for r to exceed the index number of the last row by 1, if c is
 * 0.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
243 0 0 6 333 809 0 0 73 /**
 * Returns the number of characters of text, before wordwrapping.
 */ 1 4 this 3 911  
244 0 0 6 334 915 0 0 182 /**
 * Returns the character at the indicated position in the pre-wordwrapped
 * string.  If the object at this position is a graphic object instead of a
 * character, returns 0.
 */ 2 4 this 3 911  1 n 1 809  
245 0 0 6 334 915 0 0 173 /**
 * Returns the character at the indicated position in the indicated row.  If
 * the object at this position is a graphic object instead of a character,
 * returns 0.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
246 0 0 6 335 905 0 0 190 /**
 * Returns the graphic object at the indicated position in the pre-wordwrapped
 * string.  If the object at this position is a character instead of a graphic
 * object, returns NULL.
 */ 2 4 this 3 911  1 n 1 809  
247 0 0 6 335 905 0 0 180 /**
 * Returns the graphic object at the indicated position in the indicated row.
 * If the object at this position is a character instead of a graphic object,
 * returns NULL.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
248 0 0 6 336 813 0 0 115 /**
 * Returns the width of the character or object at the indicated position in
 * the pre-wordwrapped string.
 */ 2 4 this 3 911  1 n 1 809  
249 0 0 6 336 813 0 0 106 /**
 * Returns the width of the character or object at the indicated position in
 * the indicated row.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
250 0 0 6 337 809 0 0 97 /**
 * Returns the number of rows of text after it has all been wordwrapped and
 * assembled.
 */ 1 4 this 3 911  
251 0 0 6 338 809 0 0 82 /**
 * Returns the number of characters and/or graphic objects in the nth row.
 */ 2 4 this 3 911  1 r 1 809  
252 0 0 6 339 813 0 0 297 /**
 * Returns the x position of the origin of the character or graphic object at
 * the indicated position in the indicated row.
 *
 * It is legal for c to exceed the index number of the last column by 1, and
 * it is legal for r to exceed the index number of the last row by 1, if c is
 * 0.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
253 0 0 6 340 813 0 0 230 /**
 * Returns the y position of the origin of all of the characters or graphic
 * objects in the indicated row.
 *
 * It is legal for r to exceed the index number of the last row by 1.  The
 * value of c is presently ignored.
 */ 3 4 this 3 911  1 r 1 809  1 c 1 809  
254 0 0 7 341 890 0 0 247 /**
 * Actually assembles all of the text into a GeomNode, and returns the node
 * (or possibly a parent of the node, to keep the shadow separate).  Once this
 * has been called, you may query the extents of the text via get_ul(),
 * get_lr().
 */ 1 4 this 3 913  
255 0 0 6 342 916 0 0 91 /**
 * Returns the upper-left corner of the assembled text, in 2-d text
 * coordinates.
 */ 1 4 this 3 911  
256 0 0 6 343 916 0 0 92 /**
 * Returns the lower-right corner of the assembled text, in 2-d text
 * coordinates.
 */ 1 4 this 3 911  
257 0 0 6 344 813 0 0 59 /**
 * Returns the width of a single TextGraphic image.
 */ 2 7 graphic 1 905  10 properties 1 893  
258 0 0 6 344 813 0 0 274 /**
 * Returns the width of a single character, according to its associated font.
 * This also correctly calculates the width of cheesy ligatures and accented
 * characters, which may not exist in the font as such.
 *
 * This does not take kerning into account, however.
 */ 2 9 character 1 915  10 properties 1 893  
259 0 0 6 344 813 0 0 274 /**
 * Returns the width of a single character, according to its associated font.
 * This also correctly calculates the width of cheesy ligatures and accented
 * characters, which may not exist in the font as such.
 *
 * This does not take kerning into account, however.
 */ 2 9 character 1 918  10 properties 1 893  
260 0 0 6 345 818 0 0 634 /**
 * Returns true if the named character exists in the font exactly as named,
 * false otherwise.  Note that because Panda can assemble glyphs together
 * automatically using cheesy accent marks, this is not a reliable indicator
 * of whether a suitable glyph can be rendered for the character.  For that,
 * use has_character() instead.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".  It also returns false for characters that would be
 * synthesized within Panda, but see has_character().
 */ 2 9 character 1 918  10 properties 1 893  
261 0 0 6 346 818 0 0 405 /**
 * Returns true if the named character exists in the font or can be
 * synthesized by Panda, false otherwise.  (Panda can synthesize some accented
 * characters by combining similar-looking glyphs from the font.)
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".
 */ 2 9 character 1 918  10 properties 1 893  
262 0 0 6 347 818 0 0 695 /**
 * Returns true if the indicated character represents whitespace in the font,
 * or false if anything visible will be rendered for it.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), and returns false for any other characters, including
 * characters that do not exist in the font (these would be rendered with the
 * "invalid glyph", which is visible).
 *
 * Note that this function can be reliably used to identify Unicode whitespace
 * characters only if the font has all of the whitespace characters defined.
 * It will return false for any character not in the font, even if it is an
 * official Unicode whitespace character.
 */ 2 9 character 1 918  10 properties 1 893  
263 0 0 7 365 919 660 0 10 /**
 *
 */ 1 4 name 1 887  
264 0 0 7 365 919 660 0 123 /**
 * It's sort of a copy constructor: it copies the indicated TextProperties,
 * without copying a complete TextNode.
 */ 2 4 name 1 887  4 copy 1 893  
265 0 0 6 368 813 0 0 234 /**
 * Returns the number of units high each line of text is.  This is based on
 * the font.  Note that it is possible for the text to include nested font
 * change commands, in which case the value of this method is questionable.
 */ 1 4 this 3 920  
266 0 0 4 369 871 0 0 183 /**
 * Sets the maximum number of rows that may be formatted by the TextNode.  If
 * more text than this is attempted, it will be truncated and has_overflow()
 * will return true.
 */ 2 4 this 3 919  8 max_rows 1 809  
267 0 0 4 370 871 0 0 96 /**
 * Resets the TextNode's default behavior of not limiting the number of rows
 * of text.
 */ 1 4 this 3 919  
268 0 0 6 371 818 0 0 117 /**
 * Returns true if a limit on the height of the TextNode has been set via
 * set_max_rows(), false otherwise.
 */ 1 4 this 3 920  
269 0 0 6 372 809 0 0 90 /**
 * Returns the limit on the height of the TextNode specified by
 * set_max_rows().
 */ 1 4 this 3 920  
270 0 0 6 373 818 0 0 122 /**
 * Returns true if the last text set on the text node exceeded the max_rows
 * constraint, or false if it all fit.
 */ 1 4 this 3 920  
271 0 0 4 374 871 0 0 10 /**
 *
 */ 2 4 this 3 919  11 frame_color 1 888  
272 0 0 4 374 871 0 0 10 /**
 *
 */ 5 4 this 3 919  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
273 0 0 7 375 894 0 0 10 /**
 *
 */ 1 4 this 3 920  
274 0 0 4 376 871 0 0 10 /**
 *
 */ 3 4 this 3 919  4 size 1 813  10 uv_portion 1 813  
275 0 0 4 377 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
276 0 0 6 378 813 0 0 10 /**
 *
 */ 1 4 this 3 920  
277 0 0 6 379 813 0 0 10 /**
 *
 */ 1 4 this 3 920  
278 0 0 6 380 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
279 0 0 4 381 871 0 0 10 /**
 *
 */ 2 4 this 3 919  10 card_color 1 888  
280 0 0 4 381 871 0 0 10 /**
 *
 */ 5 4 this 3 919  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
281 0 0 7 382 894 0 0 10 /**
 *
 */ 1 4 this 3 920  
282 0 0 4 383 871 0 0 10 /**
 *
 */ 2 4 this 3 919  12 card_texture 1 859  
283 0 0 4 384 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
284 0 0 6 385 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
285 0 0 7 386 859 0 0 10 /**
 *
 */ 1 4 this 3 920  
286 0 0 4 387 871 0 0 249 /**
 * Specifies that a border will be drawn around the text when it is next
 * created.  The parameters are the amount of additional padding to insert
 * between the frame and the text in each dimension, and all should generally
 * be positive.
 */ 5 4 this 3 919  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
287 0 0 4 388 871 0 0 308 /**
 * Similar to set_frame_as_margin, except the frame is specified in actual
 * coordinate units (relative to the text's origin), irrespective of the size
 * of the text.  The left and bottom coordinates should generally be negative,
 * while the right and top coordinates should generally be positive.
 */ 5 4 this 3 919  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
288 0 0 4 389 871 0 0 69 /**
 * Specifies that a border will not be drawn around the text.
 */ 1 4 this 3 919  
289 0 0 6 390 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
290 0 0 6 391 818 0 0 368 /**
 * If this is true, the frame was set via a call to set_frame_as_margin(), and
 * the dimension of the frame as returned by get_frame_as_set() represent a
 * margin all around the text.  If false, then the frame was set via a call to
 * set_frame_actual(), and the dimensions of the frame as returned by
 * get_frame_as_set() are relative to the text's origin.
 */ 1 4 this 3 920  
291 0 0 7 392 865 0 0 255 /**
 * Returns the dimensions of the frame as set by set_frame_as_margin() or
 * set_frame_actual().  Use is_frame_actual() to determine how to interpret
 * the values returned by this function.  It is an error to call this if
 * has_frame() is false.
 */ 1 4 this 3 920  
292 0 0 7 393 865 0 0 431 /**
 * Returns the actual dimensions of the frame around the text.  If the frame
 * was set via set_frame_as_margin(), the result returned by this function
 * reflects the size of the current text; if the frame was set via
 * set_frame_actual(), this returns the values actually set.
 *
 * If the text has no frame at all, this returns the dimensions of the text
 * itself, as if the frame were set with a margin of 0, 0, 0, 0.
 */ 1 4 this 3 920  
293 0 0 4 394 871 0 0 84 /**
 * Specifies the thickness of the lines that will be used to draw the frame.
 */ 2 4 this 3 919  10 line_width 1 813  
294 0 0 6 395 813 0 0 82 /**
 * Returns the thickness of the lines that will be used to draw the frame.
 */ 1 4 this 3 920  
295 0 0 4 396 871 0 0 209 /**
 * Enables or disables the drawing of corners for the frame.  These are extra
 * points drawn at each of the four corners, to soften the ugly edges
 * generated when the line width is greater than one.
 */ 2 4 this 3 919  7 corners 1 818  
296 0 0 6 397 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
297 0 0 4 398 871 0 0 294 /**
 * Specifies that a (possibly opaque or semitransparent) card will be held
 * behind the text when it is next created.  Like set_frame_as_margin, the
 * parameters are the amount of additional padding to insert around the text
 * in each dimension, and all should generally be positive.
 */ 5 4 this 3 919  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
298 0 0 4 399 871 0 0 306 /**
 * Similar to set_card_as_margin, except the card is specified in actual
 * coordinate units (relative to the text's origin), irrespective of the size
 * of the text.  The left and bottom coordinates should generally be negative,
 * while the right and top coordinates should generally be positive.
 */ 5 4 this 3 919  4 left 1 813  5 right 1 813  6 bottom 1 813  3 top 1 813  
299 0 0 4 400 871 0 0 197 /**
 * Sets the card_decal flag.  When this is true, the text is decalled onto the
 * card, which is necessary if the TextNode is to be rendered in the 3-d world
 * without putting it in a bin.
 */ 2 4 this 3 919  10 card_decal 1 818  
300 0 0 4 401 871 0 0 67 /**
 * Specifies that a card will not be drawn behind the text.
 */ 1 4 this 3 919  
301 0 0 6 402 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
302 0 0 6 403 818 0 0 62 /**
 * Returns the card_decal flag.  See set_card_decal().
 */ 1 4 this 3 920  
303 0 0 6 404 818 0 0 360 /**
 * If this is true, the card was set via a call to set_card_as_margin(), and
 * the dimension of the card as returned by get_card_as_set() represent a
 * margin all around the text.  If false, then the card was set via a call to
 * set_card_actual(), and the dimensions of the card as returned by
 * get_card_as_set() are relative to the text's origin.
 */ 1 4 this 3 920  
304 0 0 7 405 865 0 0 250 /**
 * Returns the dimensions of the card as set by set_card_as_margin() or
 * set_card_actual().  Use is_card_actual() to determine how to interpret the
 * values returned by this function.  It is an error to call this if
 * has_card() is false.
 */ 1 4 this 3 920  
305 0 0 7 406 865 0 0 424 /**
 * Returns the actual dimensions of the card around the text.  If the card was
 * set via set_card_as_margin(), the result returned by this function reflects
 * the size of the current text; if the card was set via set_card_actual(),
 * this returns the values actually set.
 *
 * If the text has no card at all, this returns the dimensions of the text
 * itself, as if the card were set with a margin of 0, 0, 0, 0.
 */ 1 4 this 3 920  
306 0 0 7 407 865 0 0 251 /**
 * Returns the actual card dimensions, transformed by the matrix set by
 * set_transform().  This returns the card dimensions in actual coordinates as
 * seen by the rest of the world.  Also see get_upper_left_3d() and
 * get_lower_right_3d().
 */ 1 4 this 3 920  
307 0 0 4 408 871 0 0 85 /**
 * Sets an additional transform that is applied to the entire text paragraph.
 */ 2 4 this 3 919  9 transform 1 922  
308 0 0 7 409 924 0 0 10 /**
 *
 */ 1 4 this 3 920  
309 0 0 4 410 871 0 0 79 /**
 * Specifies the coordinate system in which the text will be generated.
 */ 2 4 this 3 919  2 cs 1 862  
310 0 0 6 411 862 0 0 10 /**
 *
 */ 1 4 this 3 920  
311 0 0 4 412 871 0 0 278 /**
 * Specifies the UsageHint that will be applied to generated geometry.  The
 * default is UH_static, which is probably the right setting, but if you know
 * the TextNode's geometry will have a short lifespan, it may be better to set
 * it to UH_stream.  See geomEnums.h.
 */ 2 4 this 3 919  10 usage_hint 1 852  
312 0 0 6 413 852 0 0 102 /**
 * Returns the UsageHint that will be applied to generated geometry.  See
 * set_usage_hint().
 */ 1 4 this 3 920  
313 0 0 4 414 871 0 0 1534 /**
 * Sets the flatten flags.  This should be a union of the
 * TextNode::FlattenFlags options.  This controls the degree of flattening
 * performed on the TextNode's internal geometry (i.e.  the scene graph
 * returned by generate()) each time the text is changed.  In general, more
 * flattening means a more optimal result, but it will take more time to
 * generate.
 *
 * The choice may be any of these three:
 *
 * FF_none - No flatten operation is called.  The letters are left as
 * independent Geoms.
 *
 * FF_light - A flatten_light() operation is called.  The attributes are
 * applied to the vertices, but no nodes are removed.
 *
 * FF_medium - A flatten_medium() operation is called.  The attributes are
 * applied to the vertices, and a few trivial nodes are removed.
 *
 * FF_strong - A flatten_strong() operation is called.  The attributes are
 * applied to the vertices, and the resulting nodes are aggressively combined
 * into as few nodes as possible.
 *
 * In addition to the above choices, you may optionally include the following
 * flag:
 *
 * FF_dynamic_merge - Copy the geoms into a single GeomVertexData as we go,
 * instead of relying on the flatten operation at the end.  This pre-flattens
 * the text considerably, and may obviate the need for flatten altogether; it
 * also tends to improve performance considerably even if you do call flatten.
 * However, it is not as fast as not calling flatten at all.
 *
 * The default is taken from the text-flatten and text-dynamic-merge config
 * variables.
 */ 2 4 this 3 919  13 flatten_flags 1 809  
314 0 0 6 415 809 0 0 63 /**
 * Returns the flatten flags.  See set_flatten_flags().
 */ 1 4 this 3 920  
315 0 0 4 416 871 0 0 160 /**
 * Sets the font that will be used when making text.  If this is set to NULL,
 * the default font will be used, which can be set via set_default_font().
 */ 2 4 this 3 919  4 font 1 841  
316 0 0 4 417 871 0 0 47 /**
 * Resets the font to the default font.
 */ 1 4 this 3 919  
317 0 0 4 418 871 0 0 707 /**
 * Sets the small_caps flag.  When this is set, lowercase letters are
 * generated as scaled-down versions of their uppercase equivalents.  This is
 * particularly useful to set for fonts that do not have lowercase letters.
 *
 * It is also a good idea to set this for a (dynamic) font that has already
 * implemented lowercase letters as scaled-down versions of their uppercase
 * equivalents, since without this flag the texture memory may needlessly
 * duplicate equivalent glyphs for upper and lowercase letters.  Setting this
 * flag causes the texture memory to share the mixed-case letters.
 *
 * The amount by which the lowercase letters are scaled is specified by
 * set_small_caps_scale().
 */ 2 4 this 3 919  10 small_caps 1 818  
318 0 0 4 419 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
319 0 0 4 420 871 0 0 210 /**
 * Sets the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps().
 * Normally, this will be a number less than one.
 */ 2 4 this 3 919  16 small_caps_scale 1 813  
320 0 0 4 421 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
321 0 0 4 422 871 0 0 10 /**
 *
 */ 2 4 this 3 919  5 slant 1 813  
322 0 0 4 423 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
323 0 0 4 424 871 0 0 10 /**
 *
 */ 2 4 this 3 919  10 align_type 1 839  
324 0 0 4 425 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
325 0 0 4 426 871 0 0 149 /**
 * Specifies the amount of extra space that is inserted before the first
 * character of each line.  This can be thought of as a left margin.
 */ 2 4 this 3 919  6 indent 1 813  
326 0 0 4 427 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
327 0 0 4 428 871 0 0 152 /**
 * Sets the text up to automatically wordwrap when it exceeds the indicated
 * width.  This can be thought of as a right margin or margin width.
 */ 2 4 this 3 919  8 wordwrap 1 813  
328 0 0 4 429 871 0 0 93 /**
 * Removes the wordwrap setting from the TextNode.  Text will be as wide as it
 * is.
 */ 1 4 this 3 919  
329 0 0 4 430 871 0 0 227 /**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */ 2 4 this 3 919  10 text_color 1 888  
330 0 0 4 430 871 0 0 227 /**
 * Sets the color of the text.  Note that this will modulate the color of all
 * components of the text, including the shadow and outline.  If you wish to
 * only set the foreground color, see DynamicTextFont::set_fg().
 */ 5 4 this 3 919  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
331 0 0 4 431 871 0 0 117 /**
 * Removes the text color specification; the text will be colored whatever it
 * was in the source font file.
 */ 1 4 this 3 919  
332 0 0 4 432 871 0 0 10 /**
 *
 */ 2 4 this 3 919  12 shadow_color 1 888  
333 0 0 4 432 871 0 0 10 /**
 *
 */ 5 4 this 3 919  1 r 1 813  1 g 1 813  1 b 1 813  1 a 1 813  
334 0 0 4 433 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
335 0 0 4 434 871 0 0 151 /**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */ 2 4 this 3 919  13 shadow_offset 1 895  
336 0 0 4 434 871 0 0 151 /**
 * Specifies that the text should be drawn with a shadow, by creating a second
 * copy of the text and offsetting it slightly behind the first.
 */ 3 4 this 3 919  7 xoffset 1 813  7 yoffset 1 813  
337 0 0 4 435 871 0 0 69 /**
 * Specifies that a shadow will not be drawn behind the text.
 */ 1 4 this 3 919  
338 0 0 4 436 871 0 0 362 /**
 * Names the GeomBin that the TextNode geometry should be assigned to.  If
 * this is set, then a GeomBinTransition will be created to explicitly place
 * each component in the named bin.
 *
 * The draw_order value will also be passed to each GeomBinTransition as
 * appropriate; this is particularly useful if this names a GeomBinFixed, e.g.
 * "fixed".
 */ 2 4 this 3 919  3 bin 1 887  
339 0 0 4 437 871 0 0 158 /**
 * Removes the effect of a previous call to set_bin().  Text will be drawn in
 * whatever bin it would like to be drawn in, with no explicit ordering.
 */ 1 4 this 3 919  
340 0 0 6 438 809 0 0 435 /**
 * Sets the drawing order of text created by the TextMaker.  This is actually
 * the draw order of the card and frame.  The shadow is drawn at
 * _draw_order+1, and the text at _draw_order+2.
 *
 * This affects the sorting order assigned to the arcs as they are created,
 * and also is passed to whatever bin may be assigned via set_bin().
 *
 * The return value is the first unused draw_order number, e.g.  _draw_order +
 * 3.
 */ 2 4 this 3 919  10 draw_order 1 809  
341 0 0 4 439 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
342 0 0 4 440 871 0 0 160 /**
 * Sets the width of each tab stop, in screen units.  A tab character embedded
 * in the text will advance the horizontal position to the next tab stop.
 */ 2 4 this 3 919  9 tab_width 1 813  
343 0 0 4 441 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
344 0 0 4 442 871 0 0 208 /**
 * Specifies the factor by which to scale each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_shift())
 * to implement superscripting or subscripting.
 */ 2 4 this 3 919  11 glyph_scale 1 813  
345 0 0 4 443 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
346 0 0 4 444 871 0 0 206 /**
 * Specifies a vertical amount to shift each letter of the text as it is
 * placed.  This can be used (possibly in conjunction with set_glyph_scale())
 * to implement superscripting or subscripting.
 */ 2 4 this 3 919  11 glyph_shift 1 813  
347 0 0 4 445 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
348 0 0 6 446 887 0 0 227 /**
 * Returns a string that represents the contents of the text, as it has been
 * formatted by wordwrap rules.
 *
 * In earlier versions, this did not contain any embedded special characters
 * like \1 or \3; now it does.
 */ 1 4 this 3 920  
349 0 0 6 447 813 0 0 126 /**
 * Returns the width of a line of text of arbitrary characters.  The line
 * should not include the newline character.
 */ 2 4 this 3 920  4 line 1 887  
350 0 0 6 447 813 0 0 178 /**
 * Returns the width of a line of text of arbitrary characters.  The line
 * should not include the newline character or any embedded control characters
 * like \1 or \3.
 */ 2 4 this 3 920  4 line 1 887  
351 0 0 6 447 813 0 0 154 /**
 * Returns the width of a single character of the font, or 0.0 if the
 * character is not known.  This may be a wide character (greater than 255).
 */ 2 4 this 3 920  9 character 1 918  
352 0 0 6 448 818 0 0 634 /**
 * Returns true if the named character exists in the font exactly as named,
 * false otherwise.  Note that because Panda can assemble glyphs together
 * automatically using cheesy accent marks, this is not a reliable indicator
 * of whether a suitable glyph can be rendered for the character.  For that,
 * use has_character() instead.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".  It also returns false for characters that would be
 * synthesized within Panda, but see has_character().
 */ 2 4 this 3 920  9 character 1 918  
353 0 0 6 449 818 0 0 405 /**
 * Returns true if the named character exists in the font or can be
 * synthesized by Panda, false otherwise.  (Panda can synthesize some accented
 * characters by combining similar-looking glyphs from the font.)
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), but returns false for characters that would render with
 * the "invalid glyph".
 */ 2 4 this 3 920  9 character 1 918  
354 0 0 6 450 818 0 0 695 /**
 * Returns true if the indicated character represents whitespace in the font,
 * or false if anything visible will be rendered for it.
 *
 * This returns true for whitespace and Unicode whitespace characters (if they
 * exist in the font), and returns false for any other characters, including
 * characters that do not exist in the font (these would be rendered with the
 * "invalid glyph", which is visible).
 *
 * Note that this function can be reliably used to identify Unicode whitespace
 * characters only if the font has all of the whitespace characters defined.
 * It will return false for any character not in the font, even if it is an
 * official Unicode whitespace character.
 */ 2 4 this 3 920  9 character 1 918  
355 0 0 6 451 887 0 0 228 /**
 * Returns a wstring that represents the contents of the text, as it has been
 * formatted by wordwrap rules.
 *
 * In earlier versions, this did not contain any embedded special characters
 * like \1 or \3; now it does.
 */ 1 4 this 3 920  
356 0 0 4 452 871 0 0 10 /**
 *
 */ 2 4 this 3 920  3 out 1 872  
357 0 0 4 453 871 0 0 10 /**
 *
 */ 3 4 this 3 920  3 out 1 872  12 indent_level 5 809  
358 0 0 6 454 813 0 0 121 /**
 * Returns the leftmost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */ 1 4 this 3 920  
359 0 0 6 455 813 0 0 122 /**
 * Returns the rightmost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */ 1 4 this 3 920  
360 0 0 6 456 813 0 0 123 /**
 * Returns the bottommost extent of the text in local 2-d coordinates,
 * unmodified by the set_transform() matrix.
 */ 1 4 this 3 920  
361 0 0 6 457 813 0 0 120 /**
 * Returns the topmost extent of the text in local 2-d coordinates, unmodified
 * by the set_transform() matrix.
 */ 1 4 this 3 920  
362 0 0 6 458 813 0 0 71 /**
 * Returns the net height of the text in local 2-d coordinates.
 */ 1 4 this 3 920  
363 0 0 6 459 813 0 0 70 /**
 * Returns the net width of the text in local 2-d coordinates.
 */ 1 4 this 3 920  
364 0 0 7 460 927 0 0 148 /**
 * Returns the upper-left extent of the text object, after it has been
 * transformed into 3-d space by applying the set_transform() matrix.
 */ 1 4 this 3 920  
365 0 0 7 461 927 0 0 149 /**
 * Returns the lower-right extent of the text object, after it has been
 * transformed into 3-d space by applying the set_transform() matrix.
 */ 1 4 this 3 920  
366 0 0 6 462 809 0 0 153 /**
 * Returns the number of rows of text that were generated.  This counts word-
 * wrapped rows as well as rows generated due to embedded newlines.
 */ 1 4 this 3 920  
367 0 0 7 463 890 0 0 168 /**
 * Generates the text, according to the parameters indicated within the
 * TextNode, and returns a Node that may be parented within the tree to
 * represent it.
 */ 1 4 this 3 919  
368 0 0 4 464 871 0 0 193 /**
 * Can be called after the TextNode has been fully configured, to force the
 * node to recompute its text immediately, rather than waiting for it to be
 * drawn.  This call is optional.
 */ 1 4 this 3 919  
369 0 0 4 465 871 0 0 267 /**
 * Forces the TextNode to recompute itself now, even if it believes nothing
 * has changed.  Normally, this should not need to be called, but it may be
 * useful if some properties change outside of the TextNode's knowledge (for
 * instance, within the font).
 */ 1 4 this 3 919  
370 0 0 7 466 890 0 0 331 /**
 * Returns the actual node that is used internally to render the text, if the
 * TextNode is parented within the scene graph.
 *
 * In general, you should not call this method.  Call generate() instead if
 * you want to get a handle to geometry that represents the text.  This method
 * is provided as a debugging aid only.
 */ 1 4 this 3 920  
371 0 0 7 546 868 0 0 0 0 
372 0 0 7 358 890 0 0 0 1 4 this 3 919  
373 0 0 6 361 914 0 0 0 1 4 this 3 919  
374 0 0 6 363 892 0 0 0 1 4 this 3 919  
375 0 0 6 483 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
376 0 0 6 486 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
377 0 0 6 489 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
378 0 0 6 492 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
379 0 0 6 495 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
380 0 0 4 497 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
381 0 0 6 500 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
382 0 0 4 502 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
383 0 0 6 505 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
384 0 0 6 508 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
385 0 0 6 511 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
386 0 0 6 514 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
387 0 0 4 516 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
388 0 0 6 519 818 0 0 76 /**
 * Returns true if a text color was specified with set_text_color().
 */ 1 4 this 3 920  
389 0 0 6 522 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
390 0 0 6 525 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
391 0 0 6 528 818 0 0 98 /**
 * Returns true if an explicit drawing bin has been set via set_bin(), false
 * otherwise.
 */ 1 4 this 3 920  
392 0 0 6 531 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
393 0 0 6 534 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
394 0 0 6 537 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
395 0 0 6 540 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
396 0 0 6 543 818 0 0 10 /**
 *
 */ 1 4 this 3 920  
397 0 0 4 545 871 0 0 10 /**
 *
 */ 1 4 this 3 919  
121
807 9 TextGlyph 0 75777 9 TextGlyph 9 TextGlyph 0 0 0 0 0 3 928 929 930 8 398 399 400 401 402 403 404 405 0 0 1 0 808 0 0 0 0 127
/**
 * A representation of a single glyph (character) from a font.  This is a
 * piece of renderable geometry of some kind.
 */

808 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

809 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

810 19 RenderState const * 0 8576 19 RenderState const * 19 RenderState const * 0 0 811 0 0 0 0 0 0 0 0 0 0

811 17 RenderState const 0 8832 17 RenderState const 17 RenderState const 0 0 812 0 0 0 0 0 0 0 0 0 0

812 11 RenderState 0 2048 11 RenderState 11 RenderState 0 0 0 0 0 0 0 0 0 0 0 0 340
/**
 * This represents a unique collection of RenderAttrib objects that correspond
 * to a particular renderable state.
 *
 * You should not attempt to create or modify a RenderState object directly.
 * Instead, call one of the make() functions to create one for you.  And
 * instead of modifying a RenderState object, create a new one.
 */

813 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 814 0 0 0 0 0 0 0 0 0 0

814 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

815 8 TextFont 0 26625 8 TextFont 8 TextFont 0 0 0 0 410 3 931 932 933 10 411 412 414 415 416 417 418 419 420 421 0 1 413 2 3 808 406 407 3 816 408 409 0 1 817 252
/**
 * An encapsulation of a font; i.e.  a set of glyphs that may be assembled
 * together by a TextNode to represent a string of text.
 *
 * This is just an abstract interface; see StaticTextFont or DynamicTextFont
 * for an actual implementation.
 */

816 7 Namable 0 2048 7 Namable 7 Namable 0 0 0 0 0 0 0 0 0 0 0 0 125
/**
 * A base class for all things which can have a name.  The name is either
 * empty or nonempty, but it is never NULL.
 */

817 10 RenderMode 0 794624 20 TextFont::RenderMode 20 TextFont::RenderMode 815 0 0 0 0 0 0 0 0 0 7 10 RM_texture 20 TextFont::RM_texture 44
// Each glyph is a single textured rectangle
0 12 RM_wireframe 22 TextFont::RM_wireframe 39
// Each glyph is a lot of line segments
1 10 RM_polygon 20 TextFont::RM_polygon 35
// Each glyph is a lot of triangles
2 11 RM_extruded 21 TextFont::RM_extruded 38
// a 3-D outline, like a cookie cutter
3 8 RM_solid 18 TextFont::RM_solid 44
// combination of RM_extruded and RM_polygon
4 17 RM_distance_field 27 TextFont::RM_distance_field 0
5 10 RM_invalid 20 TextFont::RM_invalid 57
// Returned by string_render_mode() for an invalid match.
6 0 0

818 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

819 16 DynamicTextGlyph 0 75777 16 DynamicTextGlyph 16 DynamicTextGlyph 0 0 0 0 0 1 934 11 422 430 431 432 433 434 435 436 437 438 439 0 0 1 0 807 0 0 0 0 188
/**
 * A specialization on TextGlyph that is generated and stored by a
 * DynamicTextFont.  This keeps some additional information, such as where the
 * glyph appears on a texture map.
 */

820 17 DynamicTextPage * 0 8576 17 DynamicTextPage * 17 DynamicTextPage * 0 0 821 0 0 0 0 0 0 0 0 0 0

821 15 DynamicTextPage 0 141313 15 DynamicTextPage 15 DynamicTextPage 0 0 0 1 428 429 0 5 423 424 425 426 427 0 0 1 0 822 0 0 0 0 197
/**
 * A single "page" of a DynamicTextFont.  This is a single texture that holds
 * a number of glyphs for rendering.  The font starts out with one page, and
 * will add more as it needs them.
 */

822 7 Texture 0 2048 7 Texture 7 Texture 0 0 0 0 0 0 0 0 0 0 0 0 688
/**
 * Represents a texture object, which is typically a single 2-d image but may
 * also represent a 1-d or 3-d texture image, or the six 2-d faces of a cube
 * map texture.
 *
 * A texture's image data might be stored in system RAM (see get_ram_image())
 * or its image may be represented in texture memory on one or more
 * GraphicsStateGuardians (see prepare()), or both.  The typical usage pattern
 * is that a texture is loaded from an image file on disk, which copies its
 * image data into system RAM; then the first time the texture is rendered its
 * image data is copied to texture memory (actually, to the graphics API), and
 * the system RAM image is automatically freed.
 */

823 15 DynamicTextFont 0 26625 15 DynamicTextFont 15 DynamicTextFont 0 0 0 1 444 445 16 935 936 937 938 939 940 941 942 943 944 945 946 947 948 949 950 44 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 461 462 463 464 465 466 467 468 469 470 471 472 473 474 475 476 477 478 479 480 481 482 483 484 485 486 487 488 489 1 1008 0 2 3 815 440 441 3 824 442 443 0 0 231
/**
 * A DynamicTextFont is a special TextFont object that rasterizes its glyphs
 * from a standard font file (e.g.  a TTF file) on the fly.  It requires the
 * FreeType 2.0 library (or any higher, backward-compatible version).
 */

824 12 FreetypeFont 0 2048 12 FreetypeFont 12 FreetypeFont 0 0 0 0 0 0 0 0 0 0 0 0 247
/**
 * This is a common base class for both DynamicTextFont and PNMTextMaker.
 * Both of these are utility classes that use the FreeType library to generate
 * glyphs from fonts; this class abstracts out that common wrapper around
 * FreeType.
 */

825 16 LVecBase2i const 0 8832 16 LVecBase2i const 16 LVecBase2i const 0 0 826 0 0 0 0 0 0 0 0 0 0

826 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

827 10 FilterType 0 794624 24 SamplerState::FilterType 24 SamplerState::FilterType 828 0 0 0 0 0 0 0 0 0 9 10 FT_nearest 24 SamplerState::FT_nearest 25
// Point sample the pixel
0 9 FT_linear 23 SamplerState::FT_linear 48
// Bilinear filtering of four neighboring pixels
1 25 FT_nearest_mipmap_nearest 39 SamplerState::FT_nearest_mipmap_nearest 55
// Point sample the pixel from the nearest mipmap level
2 24 FT_linear_mipmap_nearest 38 SamplerState::FT_linear_mipmap_nearest 58
// Bilinear filter the pixel from the nearest mipmap level
3 24 FT_nearest_mipmap_linear 38 SamplerState::FT_nearest_mipmap_linear 68
// Point sample the pixel from two mipmap levels, and linearly blend
4 23 FT_linear_mipmap_linear 37 SamplerState::FT_linear_mipmap_linear 116
// A.k.a.  trilinear filtering: Bilinear filter the pixel from two mipmap
// levels, and linearly blend the results.
5 9 FT_shadow 23 SamplerState::FT_shadow 79
// The OpenGL ARB_shadow extension can be thought of as a kind of
// filtering.
6 10 FT_default 24 SamplerState::FT_default 129
// Default is usually linear, but it depends on format.  This was added at
// the end of the list to avoid bumping TXO version #.
7 10 FT_invalid 24 SamplerState::FT_invalid 57
// Returned by string_filter_type() for an invalid match.
8 0 0

828 12 SamplerState 0 2048 12 SamplerState 12 SamplerState 0 0 0 0 0 0 0 0 0 0 0 0 176
/**
 * Represents a set of settings that indicate how a texture is sampled.  This
 * can be used to sample the same texture using different settings in
 * different places.
 */

829 12 LColor const 0 8832 12 LColor const 12 LColor const 0 0 830 0 0 0 0 0 0 0 0 0 0

830 6 LColor 0 2105344 6 LColor 6 LColor 0 0 831 0 0 0 0 0 0 0 0 0 0

831 7 LColorf 0 2105344 7 LColorf 7 LColorf 0 0 832 0 0 0 0 0 0 0 0 0 0

832 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

833 6 Format 0 794624 15 Texture::Format 15 Texture::Format 822 0 0 0 0 0 0 0 0 0 52 15 F_depth_stencil 24 Texture::F_depth_stencil 0
1 13 F_color_index 22 Texture::F_color_index 0
2 5 F_red 14 Texture::F_red 0
3 7 F_green 16 Texture::F_green 0
4 6 F_blue 15 Texture::F_blue 0
5 7 F_alpha 16 Texture::F_alpha 0
6 5 F_rgb 14 Texture::F_rgb 55
// any suitable RGB mode, whatever the hardware prefers
7 6 F_rgb5 15 Texture::F_rgb5 27
// 5 bits per R,G,B channel
8 6 F_rgb8 15 Texture::F_rgb8 27
// 8 bits per R,G,B channel
9 7 F_rgb12 16 Texture::F_rgb12 28
// 12 bits per R,G,B channel
10 8 F_rgb332 17 Texture::F_rgb332 33
// 3 bits per R & G, 2 bits for B
11 6 F_rgba 15 Texture::F_rgba 56
// any suitable RGBA mode, whatever the hardware prefers
12 6 F_rgbm 15 Texture::F_rgbm 58
// as above, but only requires 1 bit for alpha (i.e. mask)
13 7 F_rgba4 16 Texture::F_rgba4 29
// 4 bits per R,G,B,A channel
14 7 F_rgba5 16 Texture::F_rgba5 40
// 5 bits per R,G,B channel, 1 bit alpha
15 7 F_rgba8 16 Texture::F_rgba8 29
// 8 bits per R,G,B,A channel
16 8 F_rgba12 17 Texture::F_rgba12 30
// 12 bits per R,G,B,A channel
17 11 F_luminance 20 Texture::F_luminance 0
18 17 F_luminance_alpha 26 Texture::F_luminance_alpha 33
// 8 bits luminance, 8 bits alpha
19 21 F_luminance_alphamask 30 Texture::F_luminance_alphamask 46
// 8 bits luminance, only needs 1 bit of alpha
20 8 F_rgba16 17 Texture::F_rgba16 30
// 16 bits per R,G,B,A channel
21 8 F_rgba32 17 Texture::F_rgba32 30
// 32 bits per R,G,B,A channel
22 17 F_depth_component 26 Texture::F_depth_component 0
23 19 F_depth_component16 28 Texture::F_depth_component16 0
24 19 F_depth_component24 28 Texture::F_depth_component24 0
25 19 F_depth_component32 28 Texture::F_depth_component32 0
26 5 F_r16 14 Texture::F_r16 0
27 6 F_rg16 15 Texture::F_rg16 0
28 7 F_rgb16 16 Texture::F_rgb16 0
29 6 F_srgb 15 Texture::F_srgb 101
// These formats are in the sRGB color space.  RGB is 2.2 gamma corrected,
// alpha is always linear.
30 12 F_srgb_alpha 21 Texture::F_srgb_alpha 0
31 12 F_sluminance 21 Texture::F_sluminance 0
32 18 F_sluminance_alpha 27 Texture::F_sluminance_alpha 0
33 6 F_r32i 15 Texture::F_r32i 41
// 32-bit integer, used for atomic access
34 5 F_r32 14 Texture::F_r32 0
35 6 F_rg32 15 Texture::F_rg32 0
36 7 F_rgb32 16 Texture::F_rgb32 0
37 5 F_r8i 14 Texture::F_r8i 31
// 8 integer bits per R channel
38 6 F_rg8i 15 Texture::F_rg8i 33
// 8 integer bits per R,G channel
39 7 F_rgb8i 16 Texture::F_rgb8i 35
// 8 integer bits per R,G,B channel
40 8 F_rgba8i 17 Texture::F_rgba8i 37
// 8 integer bits per R,G,B,A channel
41 13 F_r11_g11_b10 22 Texture::F_r11_g11_b10 58
// unsigned floating-point, 11 Red, 11 Green, 10 Blue Bits
42 9 F_rgb9_e5 18 Texture::F_rgb9_e5 0
43 10 F_rgb10_a2 19 Texture::F_rgb10_a2 0
44 4 F_rg 13 Texture::F_rg 0
45 6 F_r16i 15 Texture::F_r16i 0
46 7 F_rg16i 16 Texture::F_rg16i 0
47 8 F_rgb16i 17 Texture::F_rgb16i 18
// not recommended
48 9 F_rgba16i 18 Texture::F_rgba16i 0
49 7 F_rg32i 16 Texture::F_rg32i 0
50 8 F_rgb32i 17 Texture::F_rgb32i 0
51 9 F_rgba32i 18 Texture::F_rgba32i 0
52 0 0

834 8 FontPool 0 141313 8 FontPool 8 FontPool 0 0 0 0 499 0 9 490 491 492 493 494 495 496 497 498 0 0 0 0 0 186
/**
 * This is the preferred interface for loading fonts for the TextNode system.
 * It is similar to ModelPool and TexturePool in that it unifies references to
 * the same filename.
 */

835 13 GeomTextGlyph 0 75777 13 GeomTextGlyph 13 GeomTextGlyph 0 0 0 0 0 0 1 500 0 0 1 0 836 0 0 0 0 292
/**
 * This is a specialization on Geom for containing a primitive intended to
 * represent a TextGlyph.  Its sole purpose is to maintain the geom count on
 * the glyph, so we can determine the actual usage count on a dynamic glyph
 * (and thus know when it is safe to recycle the glyph).
 */

836 4 Geom 0 2048 4 Geom 4 Geom 0 0 0 0 0 0 0 0 0 0 0 0 418
/**
 * A container for geometry primitives.  This class associates one or more
 * GeomPrimitive objects with a table of vertices defined by a GeomVertexData
 * object.  All of the primitives stored in a particular Geom are drawn from
 * the same set of vertices (each primitive uses a subset of all of the
 * vertices in the table), and all of them must be rendered at the same time,
 * in the same graphics state.
 */

837 14 StaticTextFont 0 141313 14 StaticTextFont 14 StaticTextFont 0 0 0 1 501 503 0 1 502 0 0 1 0 815 0 0 0 0 251
/**
 * A StaticTextFont is loaded up from a model that was previously generated
 * via egg-mkfont, and contains all of its glyphs already generated and
 * available for use.  It doesn't require linking with any external libraries
 * like FreeType.
 */

838 14 TextProperties 0 141313 14 TextProperties 14 TextProperties 0 0 0 1 504 595 20 951 952 953 954 955 956 957 958 959 960 961 962 963 964 965 966 967 968 969 970 90 505 506 507 508 509 510 511 512 513 514 515 516 517 518 519 520 521 522 523 524 525 526 527 528 529 530 531 532 533 534 535 536 537 538 539 540 541 542 543 544 545 546 547 548 549 550 551 552 553 554 555 556 557 558 559 560 561 562 563 564 565 566 567 568 569 570 571 572 573 574 575 576 577 578 579 580 581 582 583 584 585 586 587 588 589 590 591 592 593 594 0 0 0 0 2 839 840 822
/**
 * This defines the set of visual properties that may be assigned to the
 * individual characters of the text.  (Properties which affect the overall
 * block of text can only be specified on the TextNode directly).
 *
 * Typically, there is just one set of properties on a given block of text,
 * which is set directly on the TextNode (TextNode inherits from
 * TextProperties). That makes all of the text within a particular block have
 * the same appearance.
 *
 * This separate class exists in order to implement multiple different kinds
 * of text appearing within one block.  The text string itself may reference a
 * TextProperties structure by name using the \1 and \2 tokens embedded within
 * the string; each nested TextProperties structure modifies the appearance of
 * subsequent text within the block.
 */

839 9 Alignment 0 794624 25 TextProperties::Alignment 25 TextProperties::Alignment 838 0 0 0 0 0 0 0 0 0 6 6 A_left 22 TextProperties::A_left 0
0 7 A_right 23 TextProperties::A_right 0
1 8 A_center 24 TextProperties::A_center 0
2 12 A_boxed_left 28 TextProperties::A_boxed_left 0
3 13 A_boxed_right 29 TextProperties::A_boxed_right 0
4 14 A_boxed_center 30 TextProperties::A_boxed_center 0
5 0 0

840 9 Direction 0 794624 25 TextProperties::Direction 25 TextProperties::Direction 838 0 0 0 0 0 0 0 0 0 2 5 D_ltr 21 TextProperties::D_ltr 0
0 5 D_rtl 21 TextProperties::D_rtl 0
1 0 0

841 10 TextFont * 0 8576 10 TextFont * 10 TextFont * 0 0 815 0 0 0 0 0 0 0 0 0 0

842 8 LVector2 0 2105344 8 LVector2 8 LVector2 0 0 843 0 0 0 0 0 0 0 0 0 0

843 9 LVector2f 0 2048 9 LVector2f 9 LVector2f 0 0 0 0 0 0 0 0 0 0 0 0 49
/**
 * This is a two-component vector offset.
 */

844 12 string const 0 8832 17 std::string const 17 std::string const 0 0 845 0 0 0 0 0 0 0 0 0 0

845 6 string 0 2105344 11 std::string 11 std::string 0 0 846 0 0 0 0 0 0 0 0 0 0

846 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

847 11 TextGraphic 0 141313 11 TextGraphic 11 TextGraphic 0 0 0 1 596 603 3 971 972 973 6 597 598 599 600 601 602 0 0 0 0 0 761
/**
 * This defines a special model that has been constructed for the purposes of
 * embedding an arbitrary graphic image within a text paragraph.
 *
 * It can be any arbitrary model, though it should be built along the same
 * scale as the text, and it should probably be at least mostly two-
 * dimensional.  Typically, this means it should be constructed in the X-Z
 * plane, and it should have a maximum vertical (Z) height of 1.0.
 *
 * The frame specifies an arbitrary bounding volume in the form (left, right,
 * bottom, top).  This indicates the amount of space that will be reserved
 * within the paragraph.  The actual model is not actually required to fit
 * within this rectangle, but if it does not, it may visually overlap with
 * nearby text.
 */

848 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

849 9 LVecBase4 0 2105344 9 LVecBase4 9 LVecBase4 0 0 832 0 0 0 0 0 0 0 0 0 0

850 21 TextPropertiesManager 0 43009 21 TextPropertiesManager 21 TextPropertiesManager 0 0 0 0 0 0 10 604 605 606 607 608 609 610 611 612 613 0 0 0 0 0 1100
/**
 * This defines all of the TextProperties structures that might be referenced
 * by name from an embedded text string.
 *
 * A text string, as rendered by a TextNode, can contain embedded references
 * to one of the TextProperties defined here, by enclosing the name between \1
 * (ASCII 0x01) characters; this causes a "push" to the named state.  All text
 * following the closing \1 character will then be rendered in the new state.
 * The next \2 (ASCII 0x02) character will then restore the previous state for
 * subsequent text.
 *
 * For instance, "x\1up\1n\2 + y" indicates that the character "x" will be
 * rendered in the normal state, the character "n" will be rendered in the
 * "up" state, and then " + y" will be rendered in the normal state again.
 *
 * This can also be used to define arbitrary models that can serve as embedded
 * graphic images in a text paragraph.  This works similarly; the convention
 * is to create a TextGraphic that describes the graphic image, and then
 * associate it here via the set_graphic() call.  Then "\5name\5" will embed
 * the named graphic.
 */

851 13 TextAssembler 0 26625 13 TextAssembler 13 TextAssembler 0 0 0 1 614 616 5 974 975 976 977 978 37 615 617 618 619 620 621 622 623 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 646 647 648 649 650 651 652 0 0 0 0 0 290
/**
 * This class is not normally used directly by user code, but is used by the
 * TextNode to lay out a block of text and convert it into rows of Geoms
 * according to the TextProperties.  However, user code may take advantage of
 * it, if desired, for very low-level text operations.
 */

852 9 UsageHint 0 794624 20 GeomEnums::UsageHint 20 GeomEnums::UsageHint 853 0 0 0 0 0 0 0 0 0 5 9 UH_client 20 GeomEnums::UH_client 80
// UH_client: don't attempt to upload the data; always keep it on the
// client.
0 9 UH_stream 20 GeomEnums::UH_stream 154
// UH_stream: the data will be created once, used to render a few times,
// and then discarded.  This should be used for short-lived temporary
// objects.
1 10 UH_dynamic 21 GeomEnums::UH_dynamic 170
// UH_dynamic: the data will be repeatedly modified and re-rendered.  This
// is for data that will be modified at runtime, such as animated or soft-
// skinned vertices.
2 9 UH_static 20 GeomEnums::UH_static 281
// UH_static: the data will be created once, and used to render many
// times, without modification.  This is the most common case, since
// typically vertex data is not directly animated (this is not related to
// scene graph animation, e.g.  from adjusting transforms on a node).
3 14 UH_unspecified 25 GeomEnums::UH_unspecified 265
// UH_unspecified: the usage is unspecified.  This is intended as a "don't
// care" option for abstract objects; it should not be applied to any
// actual geometry to be rendered.  You take your chances if a geom
// actually gets into the scene graph with this set.
4 0 0

853 9 GeomEnums 0 2048 9 GeomEnums 9 GeomEnums 0 0 0 0 0 0 0 0 0 0 0 0 184
/**
 * This class exists just to provide scoping for the various enumerated types
 * used by Geom, GeomVertexData, GeomVertexArrayData, GeomPrimitive, and other
 * related classes.
 */

854 20 TextProperties const 0 8832 20 TextProperties const 20 TextProperties const 0 0 838 0 0 0 0 0 0 0 0 0 0

855 8 TextNode 0 26625 8 TextNode 8 TextNode 0 0 0 1 659 660 29 979 980 981 982 983 984 985 986 987 988 989 990 991 992 993 994 995 996 997 998 999 1000 1001 1002 1003 1004 1005 1006 1007 100 661 662 663 664 665 666 667 668 669 670 671 672 673 674 675 676 677 678 679 680 681 682 683 684 685 686 687 688 689 690 691 692 693 694 695 696 697 698 699 700 701 702 703 704 705 706 707 708 709 710 711 712 713 714 715 716 717 718 719 720 721 722 723 724 725 726 727 728 729 730 731 732 733 734 735 736 737 738 739 740 741 742 743 744 745 746 747 748 749 750 751 752 753 754 755 756 757 758 759 806 0 0 3 3 856 653 654 3 857 655 656 3 838 657 658 0 1 858 917
/**
 * The primary interface to this module.  This class does basic text assembly;
 * given a string of text and a TextFont object, it creates a piece of
 * geometry that may be placed in the 3-d or 2-d world to represent the
 * indicated text.
 *
 * The TextNode may be used in one of two ways.  Naively, it may simply be
 * parented directly into the scene graph and rendered as if it were a
 * GeomNode; in this mode, the actual polygon geometry that renders the text
 * is not directly visible or accessible, but remains hidden within the
 * TextNode.
 *
 * The second way TextNode may be used is as a text generator.  To use it in
 * this way, do not parent the TextNode to the scene graph; instead, set the
 * properties of the text and call generate() to return an ordinary node,
 * containing ordinary geometry, which you may use however you like.  Each
 * time you call generate() a new node is returned.
 */

856 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

857 11 TextEncoder 0 2048 11 TextEncoder 11 TextEncoder 0 0 0 0 0 0 0 0 0 0 0 0 399
/**
 * This class can be used to convert text between multiple representations,
 * e.g.  UTF-8 to UTF-16.  You may use it as a static class object, passing
 * the encoding each time, or you may create an instance and use that object,
 * which will record the current encoding and retain the current string.
 *
 * This class is also a base class of TextNode, which inherits this
 * functionality.
 */

858 12 FlattenFlags 0 794624 22 TextNode::FlattenFlags 22 TextNode::FlattenFlags 855 0 0 0 0 0 0 0 0 0 5 7 FF_none 17 TextNode::FF_none 0
0 8 FF_light 18 TextNode::FF_light 0
1 9 FF_medium 19 TextNode::FF_medium 0
2 9 FF_strong 19 TextNode::FF_strong 0
4 16 FF_dynamic_merge 26 TextNode::FF_dynamic_merge 0
8 0 0

859 9 Texture * 0 8576 9 Texture * 9 Texture * 0 0 822 0 0 0 0 0 0 0 0 0 0

860 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 861 0 0 0 0 0 0 0 0 0 0

861 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

862 16 CoordinateSystem 0 532480 16 CoordinateSystem 16 CoordinateSystem 0 0 0 0 0 0 0 0 0 0 6 10 CS_default 10 CS_default 210
// The CS_default entry does not refer to a particular coordinate system,
// but rather to the value stored in default_coordinate_system, which in
// turn is loaded from the config variable "coordinate-system".
0 12 CS_zup_right 12 CS_zup_right 21
// Z-Up, Right-handed
1 12 CS_yup_right 12 CS_yup_right 21
// Y-Up, Right-handed
2 11 CS_zup_left 11 CS_zup_left 20
// Z-Up, Left-handed
3 11 CS_yup_left 11 CS_yup_left 20
// Y-Up, Left-handed
4 10 CS_invalid 10 CS_invalid 156
// CS_invalid is not a coordinate system at all.  It can be used in user-
// input processing code to indicate a contradictory coordinate system
// request.
5 0 0

863 17 TextGlyph const * 0 8576 17 TextGlyph const * 17 TextGlyph const * 0 0 864 0 0 0 0 0 0 0 0 0 0

864 15 TextGlyph const 0 8832 15 TextGlyph const 15 TextGlyph const 0 0 807 0 0 0 0 0 0 0 0 0 0

865 11 LVecBase4 * 0 8576 11 LVecBase4 * 11 LVecBase4 * 0 0 849 0 0 0 0 0 0 0 0 0 0

866 6 Geom * 0 8576 6 Geom * 6 Geom * 0 0 836 0 0 0 0 0 0 0 0 0 0

867 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

868 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 867 0 0 0 0 0 0 0 0 0 0

869 16 TextFont const * 0 8576 16 TextFont const * 16 TextFont const * 0 0 870 0 0 0 0 0 0 0 0 0 0

870 14 TextFont const 0 8832 14 TextFont const 14 TextFont const 0 0 815 0 0 0 0 0 0 0 0 0 0

871 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

872 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 873 0 0 0 0 0 0 0 0 0 0

873 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

874 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 808 0 0 0 0 0 0 0 0 0 0

875 9 Namable * 0 8576 9 Namable * 9 Namable * 0 0 816 0 0 0 0 0 0 0 0 0 0

876 24 DynamicTextGlyph const * 0 8576 24 DynamicTextGlyph const * 24 DynamicTextGlyph const * 0 0 877 0 0 0 0 0 0 0 0 0 0

877 22 DynamicTextGlyph const 0 8832 22 DynamicTextGlyph const 22 DynamicTextGlyph const 0 0 819 0 0 0 0 0 0 0 0 0 0

878 23 DynamicTextPage const * 0 8576 23 DynamicTextPage const * 23 DynamicTextPage const * 0 0 879 0 0 0 0 0 0 0 0 0 0

879 21 DynamicTextPage const 0 8832 21 DynamicTextPage const 21 DynamicTextPage const 0 0 821 0 0 0 0 0 0 0 0 0 0

880 18 LVecBase2i const * 0 8576 18 LVecBase2i const * 18 LVecBase2i const * 0 0 825 0 0 0 0 0 0 0 0 0 0

881 23 DynamicTextFont const * 0 8576 23 DynamicTextFont const * 23 DynamicTextFont const * 0 0 882 0 0 0 0 0 0 0 0 0 0

882 21 DynamicTextFont const 0 8832 21 DynamicTextFont const 21 DynamicTextFont const 0 0 823 0 0 0 0 0 0 0 0 0 0

883 17 DynamicTextFont * 0 8576 17 DynamicTextFont * 17 DynamicTextFont * 0 0 823 0 0 0 0 0 0 0 0 0 0

884 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 885 0 0 0 0 0 0 0 0 0 0

885 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 886 0 0 0 0 0 0 0 0 0 0

886 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

887 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

888 14 LColor const * 0 8576 14 LColor const * 14 LColor const * 0 0 829 0 0 0 0 0 0 0 0 0 0

889 14 FreetypeFont * 0 8576 14 FreetypeFont * 14 FreetypeFont * 0 0 824 0 0 0 0 0 0 0 0 0 0

890 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 856 0 0 0 0 0 0 0 0 0 0

891 16 StaticTextFont * 0 8576 16 StaticTextFont * 16 StaticTextFont * 0 0 837 0 0 0 0 0 0 0 0 0 0

892 16 TextProperties * 0 8576 16 TextProperties * 16 TextProperties * 0 0 838 0 0 0 0 0 0 0 0 0 0

893 22 TextProperties const * 0 8576 22 TextProperties const * 22 TextProperties const * 0 0 854 0 0 0 0 0 0 0 0 0 0

894 8 LColor * 0 8576 8 LColor * 8 LColor * 0 0 830 0 0 0 0 0 0 0 0 0 0

895 17 LVecBase2 const * 0 8576 17 LVecBase2 const * 17 LVecBase2 const * 0 0 896 0 0 0 0 0 0 0 0 0 0

896 15 LVecBase2 const 0 8832 15 LVecBase2 const 15 LVecBase2 const 0 0 897 0 0 0 0 0 0 0 0 0 0

897 9 LVecBase2 0 2105344 9 LVecBase2 9 LVecBase2 0 0 898 0 0 0 0 0 0 0 0 0 0

898 10 LVecBase2f 0 2048 10 LVecBase2f 10 LVecBase2f 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

899 10 LVector2 * 0 8576 10 LVector2 * 10 LVector2 * 0 0 842 0 0 0 0 0 0 0 0 0 0

900 13 TextGraphic * 0 8576 13 TextGraphic * 13 TextGraphic * 0 0 847 0 0 0 0 0 0 0 0 0 0

901 16 NodePath const * 0 8576 16 NodePath const * 16 NodePath const * 0 0 902 0 0 0 0 0 0 0 0 0 0

902 14 NodePath const 0 8832 14 NodePath const 14 NodePath const 0 0 848 0 0 0 0 0 0 0 0 0 0

903 17 LVecBase4 const * 0 8576 17 LVecBase4 const * 17 LVecBase4 const * 0 0 904 0 0 0 0 0 0 0 0 0 0

904 15 LVecBase4 const 0 8832 15 LVecBase4 const 15 LVecBase4 const 0 0 849 0 0 0 0 0 0 0 0 0 0

905 19 TextGraphic const * 0 8576 19 TextGraphic const * 19 TextGraphic const * 0 0 906 0 0 0 0 0 0 0 0 0 0

906 17 TextGraphic const 0 8832 17 TextGraphic const 17 TextGraphic const 0 0 847 0 0 0 0 0 0 0 0 0 0

907 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 848 0 0 0 0 0 0 0 0 0 0

908 23 TextPropertiesManager * 0 8576 23 TextPropertiesManager * 23 TextPropertiesManager * 0 0 850 0 0 0 0 0 0 0 0 0 0

909 29 TextPropertiesManager const * 0 8576 29 TextPropertiesManager const * 29 TextPropertiesManager const * 0 0 910 0 0 0 0 0 0 0 0 0 0

910 27 TextPropertiesManager const 0 8832 27 TextPropertiesManager const 27 TextPropertiesManager const 0 0 850 0 0 0 0 0 0 0 0 0 0

911 21 TextAssembler const * 0 8576 21 TextAssembler const * 21 TextAssembler const * 0 0 912 0 0 0 0 0 0 0 0 0 0

912 19 TextAssembler const 0 8832 19 TextAssembler const 19 TextAssembler const 0 0 851 0 0 0 0 0 0 0 0 0 0

913 15 TextAssembler * 0 8576 15 TextAssembler * 15 TextAssembler * 0 0 851 0 0 0 0 0 0 0 0 0 0

914 13 TextEncoder * 0 8576 13 TextEncoder * 13 TextEncoder * 0 0 857 0 0 0 0 0 0 0 0 0 0

915 8 char32_t 0 8198 8 char32_t 8 char32_t 0 1 0 0 0 0 0 0 0 0 0 0 0

916 16 LVector2 const * 0 8576 16 LVector2 const * 16 LVector2 const * 0 0 917 0 0 0 0 0 0 0 0 0 0

917 14 LVector2 const 0 8832 14 LVector2 const 14 LVector2 const 0 0 842 0 0 0 0 0 0 0 0 0 0

918 7 wchar_t 0 8194 7 wchar_t 7 wchar_t 0 1 0 0 0 0 0 0 0 0 0 0 0

919 10 TextNode * 0 8576 10 TextNode * 10 TextNode * 0 0 855 0 0 0 0 0 0 0 0 0 0

920 16 TextNode const * 0 8576 16 TextNode const * 16 TextNode const * 0 0 921 0 0 0 0 0 0 0 0 0 0

921 14 TextNode const 0 8832 14 TextNode const 14 TextNode const 0 0 855 0 0 0 0 0 0 0 0 0 0

922 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 923 0 0 0 0 0 0 0 0 0 0

923 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 860 0 0 0 0 0 0 0 0 0 0

924 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 860 0 0 0 0 0 0 0 0 0 0

925 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 926 0 0 0 0 0 0 0 0 0 0

926 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

927 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 925 0 0 0 0 0 0 0 0 0 0

0
80
928 9 character 0 2 809 398 0 0 0 0 0 0 0 20 TextGlyph::character 0

929 5 state 0 2 810 401 0 0 0 0 0 0 0 16 TextGlyph::state 0

930 7 advance 0 2 813 402 0 0 0 0 0 0 0 18 TextGlyph::advance 0

931 5 valid 0 2 818 412 0 0 0 0 0 0 0 15 TextFont::valid 0

932 11 line_height 0 6 813 414 415 0 0 0 0 0 0 21 TextFont::line_height 0

933 13 space_advance 0 6 813 416 417 0 0 0 0 0 0 23 TextFont::space_advance 0

934 4 page 0 2 820 422 0 0 0 0 0 0 0 22 DynamicTextGlyph::page 0

935 10 point_size 0 6 813 449 448 0 0 0 0 0 0 27 DynamicTextFont::point_size 0

936 15 pixels_per_unit 0 6 813 451 450 0 0 0 0 0 0 32 DynamicTextFont::pixels_per_unit 0

937 12 scale_factor 0 6 813 453 452 0 0 0 0 0 0 29 DynamicTextFont::scale_factor 0

938 16 native_antialias 0 6 818 455 454 0 0 0 0 0 0 33 DynamicTextFont::native_antialias 0

939 15 font_pixel_size 0 2 809 456 0 0 0 0 0 0 0 32 DynamicTextFont::font_pixel_size 0

940 14 texture_margin 0 6 809 460 459 0 0 0 0 0 0 31 DynamicTextFont::texture_margin 0

941 11 poly_margin 0 6 813 462 461 0 0 0 0 0 0 28 DynamicTextFont::poly_margin 0

942 9 page_size 0 6 825 464 463 0 0 0 0 0 0 26 DynamicTextFont::page_size 0

943 9 minfilter 0 6 827 468 467 0 0 0 0 0 0 26 DynamicTextFont::minfilter 0

944 9 magfilter 0 6 827 470 469 0 0 0 0 0 0 26 DynamicTextFont::magfilter 0

945 18 anisotropic_degree 0 6 809 472 471 0 0 0 0 0 0 35 DynamicTextFont::anisotropic_degree 0

946 11 render_mode 0 6 817 474 473 0 0 0 0 0 0 28 DynamicTextFont::render_mode 0

947 2 fg 0 6 829 476 475 0 0 0 0 0 0 19 DynamicTextFont::fg 0

948 2 bg 0 6 829 478 477 0 0 0 0 0 0 19 DynamicTextFont::bg 0

949 10 tex_format 0 2 833 483 0 0 0 0 0 0 0 27 DynamicTextFont::tex_format 0

950 5 pages 0 66 820 485 0 0 0 0 484 0 0 22 DynamicTextFont::pages 0

951 4 font 0 30 841 515 512 514 513 0 0 0 0 20 TextProperties::font 0

952 10 small_caps 0 30 818 519 516 518 517 0 0 0 0 26 TextProperties::small_caps 0

953 16 small_caps_scale 0 30 813 523 520 522 521 0 0 0 0 32 TextProperties::small_caps_scale 0

954 5 slant 0 30 813 527 524 526 525 0 0 0 0 21 TextProperties::slant 0

955 10 underscore 0 30 818 531 528 530 529 0 0 0 0 26 TextProperties::underscore 0

956 17 underscore_height 0 30 813 535 532 534 533 0 0 0 0 33 TextProperties::underscore_height 0

957 5 align 0 30 839 539 536 538 537 0 0 0 0 21 TextProperties::align 0

958 6 indent 0 30 813 543 540 542 541 0 0 0 0 22 TextProperties::indent 0

959 8 wordwrap 0 30 813 547 544 546 545 0 0 0 0 24 TextProperties::wordwrap 0

960 28 preserve_trailing_whitespace 0 30 818 551 548 550 549 0 0 0 0 44 TextProperties::preserve_trailing_whitespace 0

961 10 text_color 0 30 830 555 552 554 553 0 0 0 0 26 TextProperties::text_color 0

962 12 shadow_color 0 30 830 559 556 558 557 0 0 0 0 28 TextProperties::shadow_color 0

963 6 shadow 0 30 842 563 560 562 561 0 0 0 0 22 TextProperties::shadow 0

964 3 bin 0 30 844 567 564 566 565 0 0 0 0 19 TextProperties::bin 0

965 10 draw_order 0 30 809 571 568 570 569 0 0 0 0 26 TextProperties::draw_order 0

966 9 tab_width 0 30 813 575 572 574 573 0 0 0 0 25 TextProperties::tab_width 0

967 11 glyph_scale 0 30 813 579 576 578 577 0 0 0 0 27 TextProperties::glyph_scale 0

968 11 glyph_shift 0 30 813 583 580 582 581 0 0 0 0 27 TextProperties::glyph_shift 0

969 10 text_scale 0 30 813 587 584 586 585 0 0 0 0 26 TextProperties::text_scale 0

970 9 direction 0 30 840 591 588 590 589 0 0 0 0 25 TextProperties::direction 0

971 5 model 0 6 848 597 598 0 0 0 0 0 0 18 TextGraphic::model 0

972 5 frame 0 6 849 599 600 0 0 0 0 0 0 18 TextGraphic::frame 0

973 13 instance_flag 0 6 818 601 602 0 0 0 0 0 0 26 TextGraphic::instance_flag 0

974 10 usage_hint 0 6 852 619 618 0 0 0 0 0 0 25 TextAssembler::usage_hint 0

975 8 max_rows 0 6 809 621 620 0 0 0 0 0 0 23 TextAssembler::max_rows 0

976 13 dynamic_merge 0 6 818 623 622 0 0 0 0 0 0 28 TextAssembler::dynamic_merge 0

977 14 multiline_mode 0 6 818 625 624 0 0 0 0 0 0 29 TextAssembler::multiline_mode 0

978 10 properties 0 6 854 627 626 0 0 0 0 0 0 25 TextAssembler::properties 0

979 8 max_rows 0 6 809 665 662 0 0 0 0 0 0 18 TextNode::max_rows 0

980 11 frame_color 0 6 830 668 667 0 0 0 0 0 0 21 TextNode::frame_color 0

981 10 card_color 0 6 830 675 674 0 0 0 0 0 0 20 TextNode::card_color 0

982 12 card_texture 0 6 859 679 676 0 0 0 0 0 0 22 TextNode::card_texture 0

983 16 frame_line_width 0 6 813 688 687 0 0 0 0 0 0 26 TextNode::frame_line_width 0

984 13 frame_corners 0 6 818 690 689 0 0 0 0 0 0 23 TextNode::frame_corners 0

985 9 transform 0 6 860 702 701 0 0 0 0 0 0 19 TextNode::transform 0

986 17 coordinate_system 0 6 862 704 703 0 0 0 0 0 0 27 TextNode::coordinate_system 0

987 10 usage_hint 0 6 852 706 705 0 0 0 0 0 0 20 TextNode::usage_hint 0

988 13 flatten_flags 0 6 809 708 707 0 0 0 0 0 0 23 TextNode::flatten_flags 0

989 4 font 0 30 841 760 709 761 710 0 0 0 0 14 TextNode::font 110
/**
 * Returns the font currently in use, if any.  If no font is in use, this
 * returns the default font.
 */

990 10 small_caps 0 30 818 762 711 763 712 0 0 0 0 20 TextNode::small_caps 62
/**
 * Returns the small_caps flag.  See set_small_caps().
 */

991 16 small_caps_scale 0 30 813 764 713 765 714 0 0 0 0 26 TextNode::small_caps_scale 193
/**
 * Returns the scale factor applied to lowercase letters from their uppercase
 * equivalents, when the small_caps flag is in effect.  See set_small_caps()
 * and set_small_caps_scale().
 */

992 5 slant 0 30 813 766 715 767 716 0 0 0 0 15 TextNode::slant 83
/**
 * Returns the factor by which the text is specified to slant to the right.
 */

993 10 underscore 0 30 818 768 770 769 771 0 0 0 0 20 TextNode::underscore 62
/**
 * Returns the underscore flag.  See set_underscore().
 */

994 17 underscore_height 0 30 813 772 774 773 775 0 0 0 0 27 TextNode::underscore_height 86
/**
 * Returns the vertical height of the underscore; see set_underscore_height().
 */

995 5 align 0 30 839 776 717 777 718 0 0 0 0 15 TextNode::align 10
/**
 *
 */

996 6 indent 0 30 813 778 719 779 720 0 0 0 0 16 TextNode::indent 10
/**
 *
 */

997 8 wordwrap 0 30 813 780 721 781 722 0 0 0 0 18 TextNode::wordwrap 10
/**
 *
 */

998 28 preserve_trailing_whitespace 0 30 818 782 784 783 785 0 0 0 0 38 TextNode::preserve_trailing_whitespace 101
/**
 * Returns the preserve_trailing_whitespace flag.  See
 * set_preserve_trailing_whitespace().
 */

999 10 text_color 0 30 830 786 723 787 724 0 0 0 0 20 TextNode::text_color 10
/**
 *
 */

1000 12 shadow_color 0 30 830 788 725 789 726 0 0 0 0 22 TextNode::shadow_color 10
/**
 *
 */

1001 6 shadow 0 30 842 790 727 791 728 0 0 0 0 16 TextNode::shadow 125
/**
 * Returns the offset of the shadow as set by set_shadow().  It is an error to
 * call this if has_shadow() is false.
 */

1002 3 bin 0 30 844 792 729 793 730 0 0 0 0 13 TextNode::bin 97
/**
 * Returns the drawing bin set with set_bin(), or empty string if no bin has
 * been set.
 */

1003 10 draw_order 0 30 809 794 731 795 732 0 0 0 0 20 TextNode::draw_order 63
/**
 * Returns the drawing order set with set_draw_order().
 */

1004 9 tab_width 0 30 813 796 733 797 734 0 0 0 0 19 TextNode::tab_width 53
/**
 * Returns the width set via set_tab_width().
 */

1005 11 glyph_scale 0 30 813 798 735 799 736 0 0 0 0 21 TextNode::glyph_scale 85
/**
 * Returns the scale factor of each letter as specified by set_glyph_scale().
 */

1006 11 glyph_shift 0 30 813 800 737 801 738 0 0 0 0 21 TextNode::glyph_shift 90
/**
 * Returns the vertical shift of each letter as specified by
 * set_glyph_shift().
 */

1007 10 text_scale 0 30 813 802 804 803 805 0 0 0 0 20 TextNode::text_scale 81
/**
 * Returns the scale factor of the text as specified by set_text_scale().
 */

1
1008 9 get_pages 0 484 485 26 DynamicTextFont::get_pages 0

