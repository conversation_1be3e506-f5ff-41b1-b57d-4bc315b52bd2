'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_robustness_video_memory_purge'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_robustness_video_memory_purge',error_checker=_errors._error_checker)
GLX_GENERATE_RESET_ON_VIDEO_MEMORY_PURGE_NV=_C('GLX_GENERATE_RESET_ON_VIDEO_MEMORY_PURGE_NV',0x20F7)

