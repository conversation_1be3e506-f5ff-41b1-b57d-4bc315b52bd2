'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_ARB_create_context_profile'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_ARB_create_context_profile',error_checker=_errors._error_checker)
ERROR_INVALID_PROFILE_ARB=_C('ERROR_INVALID_PROFILE_ARB',0x2096)
WGL_CONTEXT_COMPATIBILITY_PROFILE_BIT_ARB=_C('WGL_CONTEXT_COMPATIBILITY_PROFILE_BIT_ARB',0x00000002)
WGL_CONTEXT_CORE_PROFILE_BIT_ARB=_C('WGL_CONTEXT_CORE_PROFILE_BIT_ARB',0x00000001)
WGL_CONTEXT_PROFILE_MASK_ARB=_C('WGL_CONTEXT_PROFILE_MASK_ARB',0x9126)

