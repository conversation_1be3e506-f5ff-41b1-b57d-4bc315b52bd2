1730992849
3 3
13 libp3mathutil 4 LL_p 12 panda3d.core 
784
833 5 clear 0 4 1619 59 PointerToBase< ReferenceCountedVector< LMatrix3d > >::clear 0 1 18 0
78
inline void PointerToBase< ReferenceCountedVector< LMatrix3d > >::clear(void);

834 6 output 0 4 1619 60 PointerToBase< ReferenceCountedVector< LMatrix3d > >::output 0 1 19 0
91
void PointerToBase< ReferenceCountedVector< LMatrix3d > >::output(std::ostream &out) const;

835 19 ~PointerToArrayBase 0 516 1618 52 PointerToArrayBase< LMatrix3d >::~PointerToArrayBase 0 0 0
66
inline PointerToArrayBase< LMatrix3d >::~PointerToArrayBase(void);

836 11 operator == 0 4 1618 44 PointerToArrayBase< LMatrix3d >::operator == 0 1 16 64
// These are implemented in PointerToVoid, but expose them here.
109
inline bool PointerToArrayBase< LMatrix3d >::operator ==(PointerToArrayBase< LMatrix3d > const &other) const;

837 11 operator != 0 4 1618 44 PointerToArrayBase< LMatrix3d >::operator != 0 1 17 0
109
inline bool PointerToArrayBase< LMatrix3d >::operator !=(PointerToArrayBase< LMatrix3d > const &other) const;

838 19 ConstPointerToArray 0 260 1617 53 ConstPointerToArray< LMatrix3d >::ConstPointerToArray 0 2 1 2 0
210
inline ConstPointerToArray< LMatrix3d >::ConstPointerToArray(PointerToArray< LMatrix3d > const &copy);
inline ConstPointerToArray< LMatrix3d >::ConstPointerToArray(ConstPointerToArray< LMatrix3d > const &copy);

839 5 clear 0 4 1617 39 ConstPointerToArray< LMatrix3d >::clear 0 1 3 0
58
inline void ConstPointerToArray< LMatrix3d >::clear(void);

840 4 size 0 4 1617 38 ConstPointerToArray< LMatrix3d >::size 0 1 4 0
81
inline unsigned long long int ConstPointerToArray< LMatrix3d >::size(void) const;

841 11 get_element 0 4 1617 45 ConstPointerToArray< LMatrix3d >::get_element 0 1 5 0
102
inline LMatrix3d const &ConstPointerToArray< LMatrix3d >::get_element(unsigned long long int n) const;

842 11 __getitem__ 0 4 1617 45 ConstPointerToArray< LMatrix3d >::__getitem__ 0 1 6 0
95
LMatrix3d const &ConstPointerToArray< LMatrix3d >::__getitem__(unsigned long long int n) const;

843 8 get_data 0 4 1617 42 ConstPointerToArray< LMatrix3d >::get_data 0 1 7 0
65
PyObject *ConstPointerToArray< LMatrix3d >::get_data(void) const;

844 11 get_subdata 0 4 1617 45 ConstPointerToArray< LMatrix3d >::get_subdata 0 1 8 0
118
PyObject *ConstPointerToArray< LMatrix3d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

845 13 get_ref_count 0 4 1617 47 ConstPointerToArray< LMatrix3d >::get_ref_count 0 1 9 0
71
inline int ConstPointerToArray< LMatrix3d >::get_ref_count(void) const;

846 18 get_node_ref_count 0 4 1617 52 ConstPointerToArray< LMatrix3d >::get_node_ref_count 0 1 10 0
76
inline int ConstPointerToArray< LMatrix3d >::get_node_ref_count(void) const;

847 5 count 0 4 1617 39 ConstPointerToArray< LMatrix3d >::count 0 1 11 0
95
inline unsigned long long int ConstPointerToArray< LMatrix3d >::count(LMatrix3d const &) const;

848 10 __reduce__ 0 4 1617 44 ConstPointerToArray< LMatrix3d >::__reduce__ 0 1 12 0
77
PyObject *ConstPointerToArray< LMatrix3d >::__reduce__(PyObject *self) const;

849 13 __getbuffer__ 0 4 1617 47 ConstPointerToArray< LMatrix3d >::__getbuffer__ 0 1 13 0
102
int ConstPointerToArray< LMatrix3d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

850 17 __releasebuffer__ 0 4 1617 51 ConstPointerToArray< LMatrix3d >::__releasebuffer__ 0 1 14 0
96
void ConstPointerToArray< LMatrix3d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

851 12 __deepcopy__ 0 4 1617 46 ConstPointerToArray< LMatrix3d >::__deepcopy__ 0 1 15 0
102
ConstPointerToArray< LMatrix3d > ConstPointerToArray< LMatrix3d >::__deepcopy__(PyObject *memo) const;

852 20 ~ConstPointerToArray 0 516 1617 54 ConstPointerToArray< LMatrix3d >::~ConstPointerToArray 0 0 0
61
ConstPointerToArray< LMatrix3d >::~ConstPointerToArray(void);

853 5 clear 0 4 1623 59 PointerToBase< ReferenceCountedVector< LMatrix3f > >::clear 0 1 37 0
78
inline void PointerToBase< ReferenceCountedVector< LMatrix3f > >::clear(void);

854 6 output 0 4 1623 60 PointerToBase< ReferenceCountedVector< LMatrix3f > >::output 0 1 38 0
91
void PointerToBase< ReferenceCountedVector< LMatrix3f > >::output(std::ostream &out) const;

855 19 ~PointerToArrayBase 0 516 1622 52 PointerToArrayBase< LMatrix3f >::~PointerToArrayBase 0 0 0
66
inline PointerToArrayBase< LMatrix3f >::~PointerToArrayBase(void);

856 11 operator == 0 4 1622 44 PointerToArrayBase< LMatrix3f >::operator == 0 1 35 64
// These are implemented in PointerToVoid, but expose them here.
109
inline bool PointerToArrayBase< LMatrix3f >::operator ==(PointerToArrayBase< LMatrix3f > const &other) const;

857 11 operator != 0 4 1622 44 PointerToArrayBase< LMatrix3f >::operator != 0 1 36 0
109
inline bool PointerToArrayBase< LMatrix3f >::operator !=(PointerToArrayBase< LMatrix3f > const &other) const;

858 19 ConstPointerToArray 0 260 1621 53 ConstPointerToArray< LMatrix3f >::ConstPointerToArray 0 2 20 21 0
210
inline ConstPointerToArray< LMatrix3f >::ConstPointerToArray(PointerToArray< LMatrix3f > const &copy);
inline ConstPointerToArray< LMatrix3f >::ConstPointerToArray(ConstPointerToArray< LMatrix3f > const &copy);

859 5 clear 0 4 1621 39 ConstPointerToArray< LMatrix3f >::clear 0 1 22 0
58
inline void ConstPointerToArray< LMatrix3f >::clear(void);

860 4 size 0 4 1621 38 ConstPointerToArray< LMatrix3f >::size 0 1 23 0
81
inline unsigned long long int ConstPointerToArray< LMatrix3f >::size(void) const;

861 11 get_element 0 4 1621 45 ConstPointerToArray< LMatrix3f >::get_element 0 1 24 0
102
inline LMatrix3f const &ConstPointerToArray< LMatrix3f >::get_element(unsigned long long int n) const;

862 11 __getitem__ 0 4 1621 45 ConstPointerToArray< LMatrix3f >::__getitem__ 0 1 25 0
95
LMatrix3f const &ConstPointerToArray< LMatrix3f >::__getitem__(unsigned long long int n) const;

863 8 get_data 0 4 1621 42 ConstPointerToArray< LMatrix3f >::get_data 0 1 26 0
65
PyObject *ConstPointerToArray< LMatrix3f >::get_data(void) const;

864 11 get_subdata 0 4 1621 45 ConstPointerToArray< LMatrix3f >::get_subdata 0 1 27 0
118
PyObject *ConstPointerToArray< LMatrix3f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

865 13 get_ref_count 0 4 1621 47 ConstPointerToArray< LMatrix3f >::get_ref_count 0 1 28 0
71
inline int ConstPointerToArray< LMatrix3f >::get_ref_count(void) const;

866 18 get_node_ref_count 0 4 1621 52 ConstPointerToArray< LMatrix3f >::get_node_ref_count 0 1 29 0
76
inline int ConstPointerToArray< LMatrix3f >::get_node_ref_count(void) const;

867 5 count 0 4 1621 39 ConstPointerToArray< LMatrix3f >::count 0 1 30 0
95
inline unsigned long long int ConstPointerToArray< LMatrix3f >::count(LMatrix3f const &) const;

868 10 __reduce__ 0 4 1621 44 ConstPointerToArray< LMatrix3f >::__reduce__ 0 1 31 0
77
PyObject *ConstPointerToArray< LMatrix3f >::__reduce__(PyObject *self) const;

869 13 __getbuffer__ 0 4 1621 47 ConstPointerToArray< LMatrix3f >::__getbuffer__ 0 1 32 0
102
int ConstPointerToArray< LMatrix3f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

870 17 __releasebuffer__ 0 4 1621 51 ConstPointerToArray< LMatrix3f >::__releasebuffer__ 0 1 33 0
96
void ConstPointerToArray< LMatrix3f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

871 12 __deepcopy__ 0 4 1621 46 ConstPointerToArray< LMatrix3f >::__deepcopy__ 0 1 34 0
102
ConstPointerToArray< LMatrix3f > ConstPointerToArray< LMatrix3f >::__deepcopy__(PyObject *memo) const;

872 20 ~ConstPointerToArray 0 516 1621 54 ConstPointerToArray< LMatrix3f >::~ConstPointerToArray 0 0 0
61
ConstPointerToArray< LMatrix3f >::~ConstPointerToArray(void);

873 5 clear 0 4 1626 60 PointerToBase< ReferenceCountedVector< LVecBase2d > >::clear 0 1 56 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase2d > >::clear(void);

874 6 output 0 4 1626 61 PointerToBase< ReferenceCountedVector< LVecBase2d > >::output 0 1 57 0
92
void PointerToBase< ReferenceCountedVector< LVecBase2d > >::output(std::ostream &out) const;

875 19 ~PointerToArrayBase 0 516 1625 53 PointerToArrayBase< LVecBase2d >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase2d >::~PointerToArrayBase(void);

876 11 operator == 0 4 1625 45 PointerToArrayBase< LVecBase2d >::operator == 0 1 54 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase2d >::operator ==(PointerToArrayBase< LVecBase2d > const &other) const;

877 11 operator != 0 4 1625 45 PointerToArrayBase< LVecBase2d >::operator != 0 1 55 0
111
inline bool PointerToArrayBase< LVecBase2d >::operator !=(PointerToArrayBase< LVecBase2d > const &other) const;

878 19 ConstPointerToArray 0 260 1624 54 ConstPointerToArray< LVecBase2d >::ConstPointerToArray 0 2 39 40 0
214
inline ConstPointerToArray< LVecBase2d >::ConstPointerToArray(PointerToArray< LVecBase2d > const &copy);
inline ConstPointerToArray< LVecBase2d >::ConstPointerToArray(ConstPointerToArray< LVecBase2d > const &copy);

879 5 clear 0 4 1624 40 ConstPointerToArray< LVecBase2d >::clear 0 1 41 0
59
inline void ConstPointerToArray< LVecBase2d >::clear(void);

880 4 size 0 4 1624 39 ConstPointerToArray< LVecBase2d >::size 0 1 42 0
82
inline unsigned long long int ConstPointerToArray< LVecBase2d >::size(void) const;

881 11 get_element 0 4 1624 46 ConstPointerToArray< LVecBase2d >::get_element 0 1 43 0
104
inline LVecBase2d const &ConstPointerToArray< LVecBase2d >::get_element(unsigned long long int n) const;

882 11 __getitem__ 0 4 1624 46 ConstPointerToArray< LVecBase2d >::__getitem__ 0 1 44 0
97
LVecBase2d const &ConstPointerToArray< LVecBase2d >::__getitem__(unsigned long long int n) const;

883 8 get_data 0 4 1624 43 ConstPointerToArray< LVecBase2d >::get_data 0 1 45 0
66
PyObject *ConstPointerToArray< LVecBase2d >::get_data(void) const;

884 11 get_subdata 0 4 1624 46 ConstPointerToArray< LVecBase2d >::get_subdata 0 1 46 0
119
PyObject *ConstPointerToArray< LVecBase2d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

885 13 get_ref_count 0 4 1624 48 ConstPointerToArray< LVecBase2d >::get_ref_count 0 1 47 0
72
inline int ConstPointerToArray< LVecBase2d >::get_ref_count(void) const;

886 18 get_node_ref_count 0 4 1624 53 ConstPointerToArray< LVecBase2d >::get_node_ref_count 0 1 48 0
77
inline int ConstPointerToArray< LVecBase2d >::get_node_ref_count(void) const;

887 5 count 0 4 1624 40 ConstPointerToArray< LVecBase2d >::count 0 1 49 0
97
inline unsigned long long int ConstPointerToArray< LVecBase2d >::count(LVecBase2d const &) const;

888 10 __reduce__ 0 4 1624 45 ConstPointerToArray< LVecBase2d >::__reduce__ 0 1 50 0
78
PyObject *ConstPointerToArray< LVecBase2d >::__reduce__(PyObject *self) const;

889 13 __getbuffer__ 0 4 1624 48 ConstPointerToArray< LVecBase2d >::__getbuffer__ 0 1 51 0
103
int ConstPointerToArray< LVecBase2d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

890 17 __releasebuffer__ 0 4 1624 52 ConstPointerToArray< LVecBase2d >::__releasebuffer__ 0 1 52 0
97
void ConstPointerToArray< LVecBase2d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

891 12 __deepcopy__ 0 4 1624 47 ConstPointerToArray< LVecBase2d >::__deepcopy__ 0 1 53 0
104
ConstPointerToArray< LVecBase2d > ConstPointerToArray< LVecBase2d >::__deepcopy__(PyObject *memo) const;

892 20 ~ConstPointerToArray 0 516 1624 55 ConstPointerToArray< LVecBase2d >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase2d >::~ConstPointerToArray(void);

893 5 clear 0 4 1629 60 PointerToBase< ReferenceCountedVector< LVecBase2f > >::clear 0 1 75 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase2f > >::clear(void);

894 6 output 0 4 1629 61 PointerToBase< ReferenceCountedVector< LVecBase2f > >::output 0 1 76 0
92
void PointerToBase< ReferenceCountedVector< LVecBase2f > >::output(std::ostream &out) const;

895 19 ~PointerToArrayBase 0 516 1628 53 PointerToArrayBase< LVecBase2f >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase2f >::~PointerToArrayBase(void);

896 11 operator == 0 4 1628 45 PointerToArrayBase< LVecBase2f >::operator == 0 1 73 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase2f >::operator ==(PointerToArrayBase< LVecBase2f > const &other) const;

897 11 operator != 0 4 1628 45 PointerToArrayBase< LVecBase2f >::operator != 0 1 74 0
111
inline bool PointerToArrayBase< LVecBase2f >::operator !=(PointerToArrayBase< LVecBase2f > const &other) const;

898 19 ConstPointerToArray 0 260 1627 54 ConstPointerToArray< LVecBase2f >::ConstPointerToArray 0 2 58 59 0
214
inline ConstPointerToArray< LVecBase2f >::ConstPointerToArray(PointerToArray< LVecBase2f > const &copy);
inline ConstPointerToArray< LVecBase2f >::ConstPointerToArray(ConstPointerToArray< LVecBase2f > const &copy);

899 5 clear 0 4 1627 40 ConstPointerToArray< LVecBase2f >::clear 0 1 60 0
59
inline void ConstPointerToArray< LVecBase2f >::clear(void);

900 4 size 0 4 1627 39 ConstPointerToArray< LVecBase2f >::size 0 1 61 0
82
inline unsigned long long int ConstPointerToArray< LVecBase2f >::size(void) const;

901 11 get_element 0 4 1627 46 ConstPointerToArray< LVecBase2f >::get_element 0 1 62 0
104
inline LVecBase2f const &ConstPointerToArray< LVecBase2f >::get_element(unsigned long long int n) const;

902 11 __getitem__ 0 4 1627 46 ConstPointerToArray< LVecBase2f >::__getitem__ 0 1 63 0
97
LVecBase2f const &ConstPointerToArray< LVecBase2f >::__getitem__(unsigned long long int n) const;

903 8 get_data 0 4 1627 43 ConstPointerToArray< LVecBase2f >::get_data 0 1 64 0
66
PyObject *ConstPointerToArray< LVecBase2f >::get_data(void) const;

904 11 get_subdata 0 4 1627 46 ConstPointerToArray< LVecBase2f >::get_subdata 0 1 65 0
119
PyObject *ConstPointerToArray< LVecBase2f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

905 13 get_ref_count 0 4 1627 48 ConstPointerToArray< LVecBase2f >::get_ref_count 0 1 66 0
72
inline int ConstPointerToArray< LVecBase2f >::get_ref_count(void) const;

906 18 get_node_ref_count 0 4 1627 53 ConstPointerToArray< LVecBase2f >::get_node_ref_count 0 1 67 0
77
inline int ConstPointerToArray< LVecBase2f >::get_node_ref_count(void) const;

907 5 count 0 4 1627 40 ConstPointerToArray< LVecBase2f >::count 0 1 68 0
97
inline unsigned long long int ConstPointerToArray< LVecBase2f >::count(LVecBase2f const &) const;

908 10 __reduce__ 0 4 1627 45 ConstPointerToArray< LVecBase2f >::__reduce__ 0 1 69 0
78
PyObject *ConstPointerToArray< LVecBase2f >::__reduce__(PyObject *self) const;

909 13 __getbuffer__ 0 4 1627 48 ConstPointerToArray< LVecBase2f >::__getbuffer__ 0 1 70 0
103
int ConstPointerToArray< LVecBase2f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

910 17 __releasebuffer__ 0 4 1627 52 ConstPointerToArray< LVecBase2f >::__releasebuffer__ 0 1 71 0
97
void ConstPointerToArray< LVecBase2f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

911 12 __deepcopy__ 0 4 1627 47 ConstPointerToArray< LVecBase2f >::__deepcopy__ 0 1 72 0
104
ConstPointerToArray< LVecBase2f > ConstPointerToArray< LVecBase2f >::__deepcopy__(PyObject *memo) const;

912 20 ~ConstPointerToArray 0 516 1627 55 ConstPointerToArray< LVecBase2f >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase2f >::~ConstPointerToArray(void);

913 5 clear 0 4 1632 60 PointerToBase< ReferenceCountedVector< LVecBase2i > >::clear 0 1 94 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase2i > >::clear(void);

914 6 output 0 4 1632 61 PointerToBase< ReferenceCountedVector< LVecBase2i > >::output 0 1 95 0
92
void PointerToBase< ReferenceCountedVector< LVecBase2i > >::output(std::ostream &out) const;

915 19 ~PointerToArrayBase 0 516 1631 53 PointerToArrayBase< LVecBase2i >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase2i >::~PointerToArrayBase(void);

916 11 operator == 0 4 1631 45 PointerToArrayBase< LVecBase2i >::operator == 0 1 92 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase2i >::operator ==(PointerToArrayBase< LVecBase2i > const &other) const;

917 11 operator != 0 4 1631 45 PointerToArrayBase< LVecBase2i >::operator != 0 1 93 0
111
inline bool PointerToArrayBase< LVecBase2i >::operator !=(PointerToArrayBase< LVecBase2i > const &other) const;

918 19 ConstPointerToArray 0 260 1630 54 ConstPointerToArray< LVecBase2i >::ConstPointerToArray 0 2 77 78 0
214
inline ConstPointerToArray< LVecBase2i >::ConstPointerToArray(PointerToArray< LVecBase2i > const &copy);
inline ConstPointerToArray< LVecBase2i >::ConstPointerToArray(ConstPointerToArray< LVecBase2i > const &copy);

919 5 clear 0 4 1630 40 ConstPointerToArray< LVecBase2i >::clear 0 1 79 0
59
inline void ConstPointerToArray< LVecBase2i >::clear(void);

920 4 size 0 4 1630 39 ConstPointerToArray< LVecBase2i >::size 0 1 80 0
82
inline unsigned long long int ConstPointerToArray< LVecBase2i >::size(void) const;

921 11 get_element 0 4 1630 46 ConstPointerToArray< LVecBase2i >::get_element 0 1 81 0
104
inline LVecBase2i const &ConstPointerToArray< LVecBase2i >::get_element(unsigned long long int n) const;

922 11 __getitem__ 0 4 1630 46 ConstPointerToArray< LVecBase2i >::__getitem__ 0 1 82 0
97
LVecBase2i const &ConstPointerToArray< LVecBase2i >::__getitem__(unsigned long long int n) const;

923 8 get_data 0 4 1630 43 ConstPointerToArray< LVecBase2i >::get_data 0 1 83 0
66
PyObject *ConstPointerToArray< LVecBase2i >::get_data(void) const;

924 11 get_subdata 0 4 1630 46 ConstPointerToArray< LVecBase2i >::get_subdata 0 1 84 0
119
PyObject *ConstPointerToArray< LVecBase2i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

925 13 get_ref_count 0 4 1630 48 ConstPointerToArray< LVecBase2i >::get_ref_count 0 1 85 0
72
inline int ConstPointerToArray< LVecBase2i >::get_ref_count(void) const;

926 18 get_node_ref_count 0 4 1630 53 ConstPointerToArray< LVecBase2i >::get_node_ref_count 0 1 86 0
77
inline int ConstPointerToArray< LVecBase2i >::get_node_ref_count(void) const;

927 5 count 0 4 1630 40 ConstPointerToArray< LVecBase2i >::count 0 1 87 0
97
inline unsigned long long int ConstPointerToArray< LVecBase2i >::count(LVecBase2i const &) const;

928 10 __reduce__ 0 4 1630 45 ConstPointerToArray< LVecBase2i >::__reduce__ 0 1 88 0
78
PyObject *ConstPointerToArray< LVecBase2i >::__reduce__(PyObject *self) const;

929 13 __getbuffer__ 0 4 1630 48 ConstPointerToArray< LVecBase2i >::__getbuffer__ 0 1 89 0
103
int ConstPointerToArray< LVecBase2i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

930 17 __releasebuffer__ 0 4 1630 52 ConstPointerToArray< LVecBase2i >::__releasebuffer__ 0 1 90 0
97
void ConstPointerToArray< LVecBase2i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

931 12 __deepcopy__ 0 4 1630 47 ConstPointerToArray< LVecBase2i >::__deepcopy__ 0 1 91 0
104
ConstPointerToArray< LVecBase2i > ConstPointerToArray< LVecBase2i >::__deepcopy__(PyObject *memo) const;

932 20 ~ConstPointerToArray 0 516 1630 55 ConstPointerToArray< LVecBase2i >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase2i >::~ConstPointerToArray(void);

933 5 clear 0 4 1635 60 PointerToBase< ReferenceCountedVector< LVecBase3d > >::clear 0 1 113 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase3d > >::clear(void);

934 6 output 0 4 1635 61 PointerToBase< ReferenceCountedVector< LVecBase3d > >::output 0 1 114 0
92
void PointerToBase< ReferenceCountedVector< LVecBase3d > >::output(std::ostream &out) const;

935 19 ~PointerToArrayBase 0 516 1634 53 PointerToArrayBase< LVecBase3d >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase3d >::~PointerToArrayBase(void);

936 11 operator == 0 4 1634 45 PointerToArrayBase< LVecBase3d >::operator == 0 1 111 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase3d >::operator ==(PointerToArrayBase< LVecBase3d > const &other) const;

937 11 operator != 0 4 1634 45 PointerToArrayBase< LVecBase3d >::operator != 0 1 112 0
111
inline bool PointerToArrayBase< LVecBase3d >::operator !=(PointerToArrayBase< LVecBase3d > const &other) const;

938 19 ConstPointerToArray 0 260 1633 54 ConstPointerToArray< LVecBase3d >::ConstPointerToArray 0 2 96 97 0
214
inline ConstPointerToArray< LVecBase3d >::ConstPointerToArray(PointerToArray< LVecBase3d > const &copy);
inline ConstPointerToArray< LVecBase3d >::ConstPointerToArray(ConstPointerToArray< LVecBase3d > const &copy);

939 5 clear 0 4 1633 40 ConstPointerToArray< LVecBase3d >::clear 0 1 98 0
59
inline void ConstPointerToArray< LVecBase3d >::clear(void);

940 4 size 0 4 1633 39 ConstPointerToArray< LVecBase3d >::size 0 1 99 0
82
inline unsigned long long int ConstPointerToArray< LVecBase3d >::size(void) const;

941 11 get_element 0 4 1633 46 ConstPointerToArray< LVecBase3d >::get_element 0 1 100 0
104
inline LVecBase3d const &ConstPointerToArray< LVecBase3d >::get_element(unsigned long long int n) const;

942 11 __getitem__ 0 4 1633 46 ConstPointerToArray< LVecBase3d >::__getitem__ 0 1 101 0
97
LVecBase3d const &ConstPointerToArray< LVecBase3d >::__getitem__(unsigned long long int n) const;

943 8 get_data 0 4 1633 43 ConstPointerToArray< LVecBase3d >::get_data 0 1 102 0
66
PyObject *ConstPointerToArray< LVecBase3d >::get_data(void) const;

944 11 get_subdata 0 4 1633 46 ConstPointerToArray< LVecBase3d >::get_subdata 0 1 103 0
119
PyObject *ConstPointerToArray< LVecBase3d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

945 13 get_ref_count 0 4 1633 48 ConstPointerToArray< LVecBase3d >::get_ref_count 0 1 104 0
72
inline int ConstPointerToArray< LVecBase3d >::get_ref_count(void) const;

946 18 get_node_ref_count 0 4 1633 53 ConstPointerToArray< LVecBase3d >::get_node_ref_count 0 1 105 0
77
inline int ConstPointerToArray< LVecBase3d >::get_node_ref_count(void) const;

947 5 count 0 4 1633 40 ConstPointerToArray< LVecBase3d >::count 0 1 106 0
97
inline unsigned long long int ConstPointerToArray< LVecBase3d >::count(LVecBase3d const &) const;

948 10 __reduce__ 0 4 1633 45 ConstPointerToArray< LVecBase3d >::__reduce__ 0 1 107 0
78
PyObject *ConstPointerToArray< LVecBase3d >::__reduce__(PyObject *self) const;

949 13 __getbuffer__ 0 4 1633 48 ConstPointerToArray< LVecBase3d >::__getbuffer__ 0 1 108 0
103
int ConstPointerToArray< LVecBase3d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

950 17 __releasebuffer__ 0 4 1633 52 ConstPointerToArray< LVecBase3d >::__releasebuffer__ 0 1 109 0
97
void ConstPointerToArray< LVecBase3d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

951 12 __deepcopy__ 0 4 1633 47 ConstPointerToArray< LVecBase3d >::__deepcopy__ 0 1 110 0
104
ConstPointerToArray< LVecBase3d > ConstPointerToArray< LVecBase3d >::__deepcopy__(PyObject *memo) const;

952 20 ~ConstPointerToArray 0 516 1633 55 ConstPointerToArray< LVecBase3d >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase3d >::~ConstPointerToArray(void);

953 5 clear 0 4 1638 60 PointerToBase< ReferenceCountedVector< LVecBase3f > >::clear 0 1 132 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase3f > >::clear(void);

954 6 output 0 4 1638 61 PointerToBase< ReferenceCountedVector< LVecBase3f > >::output 0 1 133 0
92
void PointerToBase< ReferenceCountedVector< LVecBase3f > >::output(std::ostream &out) const;

955 19 ~PointerToArrayBase 0 516 1637 53 PointerToArrayBase< LVecBase3f >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase3f >::~PointerToArrayBase(void);

956 11 operator == 0 4 1637 45 PointerToArrayBase< LVecBase3f >::operator == 0 1 130 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase3f >::operator ==(PointerToArrayBase< LVecBase3f > const &other) const;

957 11 operator != 0 4 1637 45 PointerToArrayBase< LVecBase3f >::operator != 0 1 131 0
111
inline bool PointerToArrayBase< LVecBase3f >::operator !=(PointerToArrayBase< LVecBase3f > const &other) const;

958 19 ConstPointerToArray 0 260 1636 54 ConstPointerToArray< LVecBase3f >::ConstPointerToArray 0 2 115 116 0
214
inline ConstPointerToArray< LVecBase3f >::ConstPointerToArray(PointerToArray< LVecBase3f > const &copy);
inline ConstPointerToArray< LVecBase3f >::ConstPointerToArray(ConstPointerToArray< LVecBase3f > const &copy);

959 5 clear 0 4 1636 40 ConstPointerToArray< LVecBase3f >::clear 0 1 117 0
59
inline void ConstPointerToArray< LVecBase3f >::clear(void);

960 4 size 0 4 1636 39 ConstPointerToArray< LVecBase3f >::size 0 1 118 0
82
inline unsigned long long int ConstPointerToArray< LVecBase3f >::size(void) const;

961 11 get_element 0 4 1636 46 ConstPointerToArray< LVecBase3f >::get_element 0 1 119 0
104
inline LVecBase3f const &ConstPointerToArray< LVecBase3f >::get_element(unsigned long long int n) const;

962 11 __getitem__ 0 4 1636 46 ConstPointerToArray< LVecBase3f >::__getitem__ 0 1 120 0
97
LVecBase3f const &ConstPointerToArray< LVecBase3f >::__getitem__(unsigned long long int n) const;

963 8 get_data 0 4 1636 43 ConstPointerToArray< LVecBase3f >::get_data 0 1 121 0
66
PyObject *ConstPointerToArray< LVecBase3f >::get_data(void) const;

964 11 get_subdata 0 4 1636 46 ConstPointerToArray< LVecBase3f >::get_subdata 0 1 122 0
119
PyObject *ConstPointerToArray< LVecBase3f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

965 13 get_ref_count 0 4 1636 48 ConstPointerToArray< LVecBase3f >::get_ref_count 0 1 123 0
72
inline int ConstPointerToArray< LVecBase3f >::get_ref_count(void) const;

966 18 get_node_ref_count 0 4 1636 53 ConstPointerToArray< LVecBase3f >::get_node_ref_count 0 1 124 0
77
inline int ConstPointerToArray< LVecBase3f >::get_node_ref_count(void) const;

967 5 count 0 4 1636 40 ConstPointerToArray< LVecBase3f >::count 0 1 125 0
97
inline unsigned long long int ConstPointerToArray< LVecBase3f >::count(LVecBase3f const &) const;

968 10 __reduce__ 0 4 1636 45 ConstPointerToArray< LVecBase3f >::__reduce__ 0 1 126 0
78
PyObject *ConstPointerToArray< LVecBase3f >::__reduce__(PyObject *self) const;

969 13 __getbuffer__ 0 4 1636 48 ConstPointerToArray< LVecBase3f >::__getbuffer__ 0 1 127 0
103
int ConstPointerToArray< LVecBase3f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

970 17 __releasebuffer__ 0 4 1636 52 ConstPointerToArray< LVecBase3f >::__releasebuffer__ 0 1 128 0
97
void ConstPointerToArray< LVecBase3f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

971 12 __deepcopy__ 0 4 1636 47 ConstPointerToArray< LVecBase3f >::__deepcopy__ 0 1 129 0
104
ConstPointerToArray< LVecBase3f > ConstPointerToArray< LVecBase3f >::__deepcopy__(PyObject *memo) const;

972 20 ~ConstPointerToArray 0 516 1636 55 ConstPointerToArray< LVecBase3f >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase3f >::~ConstPointerToArray(void);

973 5 clear 0 4 1641 60 PointerToBase< ReferenceCountedVector< LVecBase3i > >::clear 0 1 151 0
79
inline void PointerToBase< ReferenceCountedVector< LVecBase3i > >::clear(void);

974 6 output 0 4 1641 61 PointerToBase< ReferenceCountedVector< LVecBase3i > >::output 0 1 152 0
92
void PointerToBase< ReferenceCountedVector< LVecBase3i > >::output(std::ostream &out) const;

975 19 ~PointerToArrayBase 0 516 1640 53 PointerToArrayBase< LVecBase3i >::~PointerToArrayBase 0 0 0
67
inline PointerToArrayBase< LVecBase3i >::~PointerToArrayBase(void);

976 11 operator == 0 4 1640 45 PointerToArrayBase< LVecBase3i >::operator == 0 1 149 64
// These are implemented in PointerToVoid, but expose them here.
111
inline bool PointerToArrayBase< LVecBase3i >::operator ==(PointerToArrayBase< LVecBase3i > const &other) const;

977 11 operator != 0 4 1640 45 PointerToArrayBase< LVecBase3i >::operator != 0 1 150 0
111
inline bool PointerToArrayBase< LVecBase3i >::operator !=(PointerToArrayBase< LVecBase3i > const &other) const;

978 19 ConstPointerToArray 0 260 1639 54 ConstPointerToArray< LVecBase3i >::ConstPointerToArray 0 2 134 135 0
214
inline ConstPointerToArray< LVecBase3i >::ConstPointerToArray(PointerToArray< LVecBase3i > const &copy);
inline ConstPointerToArray< LVecBase3i >::ConstPointerToArray(ConstPointerToArray< LVecBase3i > const &copy);

979 5 clear 0 4 1639 40 ConstPointerToArray< LVecBase3i >::clear 0 1 136 0
59
inline void ConstPointerToArray< LVecBase3i >::clear(void);

980 4 size 0 4 1639 39 ConstPointerToArray< LVecBase3i >::size 0 1 137 0
82
inline unsigned long long int ConstPointerToArray< LVecBase3i >::size(void) const;

981 11 get_element 0 4 1639 46 ConstPointerToArray< LVecBase3i >::get_element 0 1 138 0
104
inline LVecBase3i const &ConstPointerToArray< LVecBase3i >::get_element(unsigned long long int n) const;

982 11 __getitem__ 0 4 1639 46 ConstPointerToArray< LVecBase3i >::__getitem__ 0 1 139 0
97
LVecBase3i const &ConstPointerToArray< LVecBase3i >::__getitem__(unsigned long long int n) const;

983 8 get_data 0 4 1639 43 ConstPointerToArray< LVecBase3i >::get_data 0 1 140 0
66
PyObject *ConstPointerToArray< LVecBase3i >::get_data(void) const;

984 11 get_subdata 0 4 1639 46 ConstPointerToArray< LVecBase3i >::get_subdata 0 1 141 0
119
PyObject *ConstPointerToArray< LVecBase3i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

985 13 get_ref_count 0 4 1639 48 ConstPointerToArray< LVecBase3i >::get_ref_count 0 1 142 0
72
inline int ConstPointerToArray< LVecBase3i >::get_ref_count(void) const;

986 18 get_node_ref_count 0 4 1639 53 ConstPointerToArray< LVecBase3i >::get_node_ref_count 0 1 143 0
77
inline int ConstPointerToArray< LVecBase3i >::get_node_ref_count(void) const;

987 5 count 0 4 1639 40 ConstPointerToArray< LVecBase3i >::count 0 1 144 0
97
inline unsigned long long int ConstPointerToArray< LVecBase3i >::count(LVecBase3i const &) const;

988 10 __reduce__ 0 4 1639 45 ConstPointerToArray< LVecBase3i >::__reduce__ 0 1 145 0
78
PyObject *ConstPointerToArray< LVecBase3i >::__reduce__(PyObject *self) const;

989 13 __getbuffer__ 0 4 1639 48 ConstPointerToArray< LVecBase3i >::__getbuffer__ 0 1 146 0
103
int ConstPointerToArray< LVecBase3i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

990 17 __releasebuffer__ 0 4 1639 52 ConstPointerToArray< LVecBase3i >::__releasebuffer__ 0 1 147 0
97
void ConstPointerToArray< LVecBase3i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

991 12 __deepcopy__ 0 4 1639 47 ConstPointerToArray< LVecBase3i >::__deepcopy__ 0 1 148 0
104
ConstPointerToArray< LVecBase3i > ConstPointerToArray< LVecBase3i >::__deepcopy__(PyObject *memo) const;

992 20 ~ConstPointerToArray 0 516 1639 55 ConstPointerToArray< LVecBase3i >::~ConstPointerToArray 0 0 0
62
ConstPointerToArray< LVecBase3i >::~ConstPointerToArray(void);

993 5 clear 0 4 1644 68 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > >::clear 0 1 170 0
87
inline void PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > >::clear(void);

994 6 output 0 4 1644 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > >::output 0 1 171 0
100
void PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > >::output(std::ostream &out) const;

995 19 ~PointerToArrayBase 0 516 1643 61 PointerToArrayBase< UnalignedLMatrix4d >::~PointerToArrayBase 0 0 0
75
inline PointerToArrayBase< UnalignedLMatrix4d >::~PointerToArrayBase(void);

996 11 operator == 0 4 1643 53 PointerToArrayBase< UnalignedLMatrix4d >::operator == 0 1 168 64
// These are implemented in PointerToVoid, but expose them here.
127
inline bool PointerToArrayBase< UnalignedLMatrix4d >::operator ==(PointerToArrayBase< UnalignedLMatrix4d > const &other) const;

997 11 operator != 0 4 1643 53 PointerToArrayBase< UnalignedLMatrix4d >::operator != 0 1 169 0
127
inline bool PointerToArrayBase< UnalignedLMatrix4d >::operator !=(PointerToArrayBase< UnalignedLMatrix4d > const &other) const;

998 19 ConstPointerToArray 0 260 1642 62 ConstPointerToArray< UnalignedLMatrix4d >::ConstPointerToArray 0 2 153 154 0
246
inline ConstPointerToArray< UnalignedLMatrix4d >::ConstPointerToArray(PointerToArray< UnalignedLMatrix4d > const &copy);
inline ConstPointerToArray< UnalignedLMatrix4d >::ConstPointerToArray(ConstPointerToArray< UnalignedLMatrix4d > const &copy);

999 5 clear 0 4 1642 48 ConstPointerToArray< UnalignedLMatrix4d >::clear 0 1 155 0
67
inline void ConstPointerToArray< UnalignedLMatrix4d >::clear(void);

1000 4 size 0 4 1642 47 ConstPointerToArray< UnalignedLMatrix4d >::size 0 1 156 0
90
inline unsigned long long int ConstPointerToArray< UnalignedLMatrix4d >::size(void) const;

1001 11 get_element 0 4 1642 54 ConstPointerToArray< UnalignedLMatrix4d >::get_element 0 1 157 0
120
inline UnalignedLMatrix4d const &ConstPointerToArray< UnalignedLMatrix4d >::get_element(unsigned long long int n) const;

1002 11 __getitem__ 0 4 1642 54 ConstPointerToArray< UnalignedLMatrix4d >::__getitem__ 0 1 158 0
113
UnalignedLMatrix4d const &ConstPointerToArray< UnalignedLMatrix4d >::__getitem__(unsigned long long int n) const;

1003 8 get_data 0 4 1642 51 ConstPointerToArray< UnalignedLMatrix4d >::get_data 0 1 159 0
74
PyObject *ConstPointerToArray< UnalignedLMatrix4d >::get_data(void) const;

1004 11 get_subdata 0 4 1642 54 ConstPointerToArray< UnalignedLMatrix4d >::get_subdata 0 1 160 0
127
PyObject *ConstPointerToArray< UnalignedLMatrix4d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1005 13 get_ref_count 0 4 1642 56 ConstPointerToArray< UnalignedLMatrix4d >::get_ref_count 0 1 161 0
80
inline int ConstPointerToArray< UnalignedLMatrix4d >::get_ref_count(void) const;

1006 18 get_node_ref_count 0 4 1642 61 ConstPointerToArray< UnalignedLMatrix4d >::get_node_ref_count 0 1 162 0
85
inline int ConstPointerToArray< UnalignedLMatrix4d >::get_node_ref_count(void) const;

1007 5 count 0 4 1642 48 ConstPointerToArray< UnalignedLMatrix4d >::count 0 1 163 0
113
inline unsigned long long int ConstPointerToArray< UnalignedLMatrix4d >::count(UnalignedLMatrix4d const &) const;

1008 10 __reduce__ 0 4 1642 53 ConstPointerToArray< UnalignedLMatrix4d >::__reduce__ 0 1 164 0
86
PyObject *ConstPointerToArray< UnalignedLMatrix4d >::__reduce__(PyObject *self) const;

1009 13 __getbuffer__ 0 4 1642 56 ConstPointerToArray< UnalignedLMatrix4d >::__getbuffer__ 0 1 165 0
111
int ConstPointerToArray< UnalignedLMatrix4d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

1010 17 __releasebuffer__ 0 4 1642 60 ConstPointerToArray< UnalignedLMatrix4d >::__releasebuffer__ 0 1 166 0
105
void ConstPointerToArray< UnalignedLMatrix4d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1011 12 __deepcopy__ 0 4 1642 55 ConstPointerToArray< UnalignedLMatrix4d >::__deepcopy__ 0 1 167 0
120
ConstPointerToArray< UnalignedLMatrix4d > ConstPointerToArray< UnalignedLMatrix4d >::__deepcopy__(PyObject *memo) const;

1012 20 ~ConstPointerToArray 0 516 1642 63 ConstPointerToArray< UnalignedLMatrix4d >::~ConstPointerToArray 0 0 0
70
ConstPointerToArray< UnalignedLMatrix4d >::~ConstPointerToArray(void);

1013 5 clear 0 4 1647 68 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > >::clear 0 1 189 0
87
inline void PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > >::clear(void);

1014 6 output 0 4 1647 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > >::output 0 1 190 0
100
void PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > >::output(std::ostream &out) const;

1015 19 ~PointerToArrayBase 0 516 1646 61 PointerToArrayBase< UnalignedLMatrix4f >::~PointerToArrayBase 0 0 0
75
inline PointerToArrayBase< UnalignedLMatrix4f >::~PointerToArrayBase(void);

1016 11 operator == 0 4 1646 53 PointerToArrayBase< UnalignedLMatrix4f >::operator == 0 1 187 64
// These are implemented in PointerToVoid, but expose them here.
127
inline bool PointerToArrayBase< UnalignedLMatrix4f >::operator ==(PointerToArrayBase< UnalignedLMatrix4f > const &other) const;

1017 11 operator != 0 4 1646 53 PointerToArrayBase< UnalignedLMatrix4f >::operator != 0 1 188 0
127
inline bool PointerToArrayBase< UnalignedLMatrix4f >::operator !=(PointerToArrayBase< UnalignedLMatrix4f > const &other) const;

1018 19 ConstPointerToArray 0 260 1645 62 ConstPointerToArray< UnalignedLMatrix4f >::ConstPointerToArray 0 2 172 173 0
246
inline ConstPointerToArray< UnalignedLMatrix4f >::ConstPointerToArray(PointerToArray< UnalignedLMatrix4f > const &copy);
inline ConstPointerToArray< UnalignedLMatrix4f >::ConstPointerToArray(ConstPointerToArray< UnalignedLMatrix4f > const &copy);

1019 5 clear 0 4 1645 48 ConstPointerToArray< UnalignedLMatrix4f >::clear 0 1 174 0
67
inline void ConstPointerToArray< UnalignedLMatrix4f >::clear(void);

1020 4 size 0 4 1645 47 ConstPointerToArray< UnalignedLMatrix4f >::size 0 1 175 0
90
inline unsigned long long int ConstPointerToArray< UnalignedLMatrix4f >::size(void) const;

1021 11 get_element 0 4 1645 54 ConstPointerToArray< UnalignedLMatrix4f >::get_element 0 1 176 0
120
inline UnalignedLMatrix4f const &ConstPointerToArray< UnalignedLMatrix4f >::get_element(unsigned long long int n) const;

1022 11 __getitem__ 0 4 1645 54 ConstPointerToArray< UnalignedLMatrix4f >::__getitem__ 0 1 177 0
113
UnalignedLMatrix4f const &ConstPointerToArray< UnalignedLMatrix4f >::__getitem__(unsigned long long int n) const;

1023 8 get_data 0 4 1645 51 ConstPointerToArray< UnalignedLMatrix4f >::get_data 0 1 178 0
74
PyObject *ConstPointerToArray< UnalignedLMatrix4f >::get_data(void) const;

1024 11 get_subdata 0 4 1645 54 ConstPointerToArray< UnalignedLMatrix4f >::get_subdata 0 1 179 0
127
PyObject *ConstPointerToArray< UnalignedLMatrix4f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1025 13 get_ref_count 0 4 1645 56 ConstPointerToArray< UnalignedLMatrix4f >::get_ref_count 0 1 180 0
80
inline int ConstPointerToArray< UnalignedLMatrix4f >::get_ref_count(void) const;

1026 18 get_node_ref_count 0 4 1645 61 ConstPointerToArray< UnalignedLMatrix4f >::get_node_ref_count 0 1 181 0
85
inline int ConstPointerToArray< UnalignedLMatrix4f >::get_node_ref_count(void) const;

1027 5 count 0 4 1645 48 ConstPointerToArray< UnalignedLMatrix4f >::count 0 1 182 0
113
inline unsigned long long int ConstPointerToArray< UnalignedLMatrix4f >::count(UnalignedLMatrix4f const &) const;

1028 10 __reduce__ 0 4 1645 53 ConstPointerToArray< UnalignedLMatrix4f >::__reduce__ 0 1 183 0
86
PyObject *ConstPointerToArray< UnalignedLMatrix4f >::__reduce__(PyObject *self) const;

1029 13 __getbuffer__ 0 4 1645 56 ConstPointerToArray< UnalignedLMatrix4f >::__getbuffer__ 0 1 184 0
111
int ConstPointerToArray< UnalignedLMatrix4f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

1030 17 __releasebuffer__ 0 4 1645 60 ConstPointerToArray< UnalignedLMatrix4f >::__releasebuffer__ 0 1 185 0
105
void ConstPointerToArray< UnalignedLMatrix4f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1031 12 __deepcopy__ 0 4 1645 55 ConstPointerToArray< UnalignedLMatrix4f >::__deepcopy__ 0 1 186 0
120
ConstPointerToArray< UnalignedLMatrix4f > ConstPointerToArray< UnalignedLMatrix4f >::__deepcopy__(PyObject *memo) const;

1032 20 ~ConstPointerToArray 0 516 1645 63 ConstPointerToArray< UnalignedLMatrix4f >::~ConstPointerToArray 0 0 0
70
ConstPointerToArray< UnalignedLMatrix4f >::~ConstPointerToArray(void);

1033 5 clear 0 4 1650 69 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > >::clear 0 1 208 0
88
inline void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > >::clear(void);

1034 6 output 0 4 1650 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > >::output 0 1 209 0
101
void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > >::output(std::ostream &out) const;

1035 19 ~PointerToArrayBase 0 516 1649 62 PointerToArrayBase< UnalignedLVecBase4d >::~PointerToArrayBase 0 0 0
76
inline PointerToArrayBase< UnalignedLVecBase4d >::~PointerToArrayBase(void);

1036 11 operator == 0 4 1649 54 PointerToArrayBase< UnalignedLVecBase4d >::operator == 0 1 206 64
// These are implemented in PointerToVoid, but expose them here.
129
inline bool PointerToArrayBase< UnalignedLVecBase4d >::operator ==(PointerToArrayBase< UnalignedLVecBase4d > const &other) const;

1037 11 operator != 0 4 1649 54 PointerToArrayBase< UnalignedLVecBase4d >::operator != 0 1 207 0
129
inline bool PointerToArrayBase< UnalignedLVecBase4d >::operator !=(PointerToArrayBase< UnalignedLVecBase4d > const &other) const;

1038 19 ConstPointerToArray 0 260 1648 63 ConstPointerToArray< UnalignedLVecBase4d >::ConstPointerToArray 0 2 191 192 0
250
inline ConstPointerToArray< UnalignedLVecBase4d >::ConstPointerToArray(PointerToArray< UnalignedLVecBase4d > const &copy);
inline ConstPointerToArray< UnalignedLVecBase4d >::ConstPointerToArray(ConstPointerToArray< UnalignedLVecBase4d > const &copy);

1039 5 clear 0 4 1648 49 ConstPointerToArray< UnalignedLVecBase4d >::clear 0 1 193 0
68
inline void ConstPointerToArray< UnalignedLVecBase4d >::clear(void);

1040 4 size 0 4 1648 48 ConstPointerToArray< UnalignedLVecBase4d >::size 0 1 194 0
91
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4d >::size(void) const;

1041 11 get_element 0 4 1648 55 ConstPointerToArray< UnalignedLVecBase4d >::get_element 0 1 195 0
122
inline UnalignedLVecBase4d const &ConstPointerToArray< UnalignedLVecBase4d >::get_element(unsigned long long int n) const;

1042 11 __getitem__ 0 4 1648 55 ConstPointerToArray< UnalignedLVecBase4d >::__getitem__ 0 1 196 0
115
UnalignedLVecBase4d const &ConstPointerToArray< UnalignedLVecBase4d >::__getitem__(unsigned long long int n) const;

1043 8 get_data 0 4 1648 52 ConstPointerToArray< UnalignedLVecBase4d >::get_data 0 1 197 0
75
PyObject *ConstPointerToArray< UnalignedLVecBase4d >::get_data(void) const;

1044 11 get_subdata 0 4 1648 55 ConstPointerToArray< UnalignedLVecBase4d >::get_subdata 0 1 198 0
128
PyObject *ConstPointerToArray< UnalignedLVecBase4d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1045 13 get_ref_count 0 4 1648 57 ConstPointerToArray< UnalignedLVecBase4d >::get_ref_count 0 1 199 0
81
inline int ConstPointerToArray< UnalignedLVecBase4d >::get_ref_count(void) const;

1046 18 get_node_ref_count 0 4 1648 62 ConstPointerToArray< UnalignedLVecBase4d >::get_node_ref_count 0 1 200 0
86
inline int ConstPointerToArray< UnalignedLVecBase4d >::get_node_ref_count(void) const;

1047 5 count 0 4 1648 49 ConstPointerToArray< UnalignedLVecBase4d >::count 0 1 201 0
115
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4d >::count(UnalignedLVecBase4d const &) const;

1048 10 __reduce__ 0 4 1648 54 ConstPointerToArray< UnalignedLVecBase4d >::__reduce__ 0 1 202 0
87
PyObject *ConstPointerToArray< UnalignedLVecBase4d >::__reduce__(PyObject *self) const;

1049 13 __getbuffer__ 0 4 1648 57 ConstPointerToArray< UnalignedLVecBase4d >::__getbuffer__ 0 1 203 0
112
int ConstPointerToArray< UnalignedLVecBase4d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

1050 17 __releasebuffer__ 0 4 1648 61 ConstPointerToArray< UnalignedLVecBase4d >::__releasebuffer__ 0 1 204 0
106
void ConstPointerToArray< UnalignedLVecBase4d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1051 12 __deepcopy__ 0 4 1648 56 ConstPointerToArray< UnalignedLVecBase4d >::__deepcopy__ 0 1 205 0
122
ConstPointerToArray< UnalignedLVecBase4d > ConstPointerToArray< UnalignedLVecBase4d >::__deepcopy__(PyObject *memo) const;

1052 20 ~ConstPointerToArray 0 516 1648 64 ConstPointerToArray< UnalignedLVecBase4d >::~ConstPointerToArray 0 0 0
71
ConstPointerToArray< UnalignedLVecBase4d >::~ConstPointerToArray(void);

1053 5 clear 0 4 1653 69 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > >::clear 0 1 227 0
88
inline void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > >::clear(void);

1054 6 output 0 4 1653 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > >::output 0 1 228 0
101
void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > >::output(std::ostream &out) const;

1055 19 ~PointerToArrayBase 0 516 1652 62 PointerToArrayBase< UnalignedLVecBase4f >::~PointerToArrayBase 0 0 0
76
inline PointerToArrayBase< UnalignedLVecBase4f >::~PointerToArrayBase(void);

1056 11 operator == 0 4 1652 54 PointerToArrayBase< UnalignedLVecBase4f >::operator == 0 1 225 64
// These are implemented in PointerToVoid, but expose them here.
129
inline bool PointerToArrayBase< UnalignedLVecBase4f >::operator ==(PointerToArrayBase< UnalignedLVecBase4f > const &other) const;

1057 11 operator != 0 4 1652 54 PointerToArrayBase< UnalignedLVecBase4f >::operator != 0 1 226 0
129
inline bool PointerToArrayBase< UnalignedLVecBase4f >::operator !=(PointerToArrayBase< UnalignedLVecBase4f > const &other) const;

1058 19 ConstPointerToArray 0 260 1651 63 ConstPointerToArray< UnalignedLVecBase4f >::ConstPointerToArray 0 2 210 211 0
250
inline ConstPointerToArray< UnalignedLVecBase4f >::ConstPointerToArray(PointerToArray< UnalignedLVecBase4f > const &copy);
inline ConstPointerToArray< UnalignedLVecBase4f >::ConstPointerToArray(ConstPointerToArray< UnalignedLVecBase4f > const &copy);

1059 5 clear 0 4 1651 49 ConstPointerToArray< UnalignedLVecBase4f >::clear 0 1 212 0
68
inline void ConstPointerToArray< UnalignedLVecBase4f >::clear(void);

1060 4 size 0 4 1651 48 ConstPointerToArray< UnalignedLVecBase4f >::size 0 1 213 0
91
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4f >::size(void) const;

1061 11 get_element 0 4 1651 55 ConstPointerToArray< UnalignedLVecBase4f >::get_element 0 1 214 0
122
inline UnalignedLVecBase4f const &ConstPointerToArray< UnalignedLVecBase4f >::get_element(unsigned long long int n) const;

1062 11 __getitem__ 0 4 1651 55 ConstPointerToArray< UnalignedLVecBase4f >::__getitem__ 0 1 215 0
115
UnalignedLVecBase4f const &ConstPointerToArray< UnalignedLVecBase4f >::__getitem__(unsigned long long int n) const;

1063 8 get_data 0 4 1651 52 ConstPointerToArray< UnalignedLVecBase4f >::get_data 0 1 216 0
75
PyObject *ConstPointerToArray< UnalignedLVecBase4f >::get_data(void) const;

1064 11 get_subdata 0 4 1651 55 ConstPointerToArray< UnalignedLVecBase4f >::get_subdata 0 1 217 0
128
PyObject *ConstPointerToArray< UnalignedLVecBase4f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1065 13 get_ref_count 0 4 1651 57 ConstPointerToArray< UnalignedLVecBase4f >::get_ref_count 0 1 218 0
81
inline int ConstPointerToArray< UnalignedLVecBase4f >::get_ref_count(void) const;

1066 18 get_node_ref_count 0 4 1651 62 ConstPointerToArray< UnalignedLVecBase4f >::get_node_ref_count 0 1 219 0
86
inline int ConstPointerToArray< UnalignedLVecBase4f >::get_node_ref_count(void) const;

1067 5 count 0 4 1651 49 ConstPointerToArray< UnalignedLVecBase4f >::count 0 1 220 0
115
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4f >::count(UnalignedLVecBase4f const &) const;

1068 10 __reduce__ 0 4 1651 54 ConstPointerToArray< UnalignedLVecBase4f >::__reduce__ 0 1 221 0
87
PyObject *ConstPointerToArray< UnalignedLVecBase4f >::__reduce__(PyObject *self) const;

1069 13 __getbuffer__ 0 4 1651 57 ConstPointerToArray< UnalignedLVecBase4f >::__getbuffer__ 0 1 222 0
112
int ConstPointerToArray< UnalignedLVecBase4f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

1070 17 __releasebuffer__ 0 4 1651 61 ConstPointerToArray< UnalignedLVecBase4f >::__releasebuffer__ 0 1 223 0
106
void ConstPointerToArray< UnalignedLVecBase4f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1071 12 __deepcopy__ 0 4 1651 56 ConstPointerToArray< UnalignedLVecBase4f >::__deepcopy__ 0 1 224 0
122
ConstPointerToArray< UnalignedLVecBase4f > ConstPointerToArray< UnalignedLVecBase4f >::__deepcopy__(PyObject *memo) const;

1072 20 ~ConstPointerToArray 0 516 1651 64 ConstPointerToArray< UnalignedLVecBase4f >::~ConstPointerToArray 0 0 0
71
ConstPointerToArray< UnalignedLVecBase4f >::~ConstPointerToArray(void);

1073 5 clear 0 4 1656 69 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > >::clear 0 1 246 0
88
inline void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > >::clear(void);

1074 6 output 0 4 1656 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > >::output 0 1 247 0
101
void PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > >::output(std::ostream &out) const;

1075 19 ~PointerToArrayBase 0 516 1655 62 PointerToArrayBase< UnalignedLVecBase4i >::~PointerToArrayBase 0 0 0
76
inline PointerToArrayBase< UnalignedLVecBase4i >::~PointerToArrayBase(void);

1076 11 operator == 0 4 1655 54 PointerToArrayBase< UnalignedLVecBase4i >::operator == 0 1 244 64
// These are implemented in PointerToVoid, but expose them here.
129
inline bool PointerToArrayBase< UnalignedLVecBase4i >::operator ==(PointerToArrayBase< UnalignedLVecBase4i > const &other) const;

1077 11 operator != 0 4 1655 54 PointerToArrayBase< UnalignedLVecBase4i >::operator != 0 1 245 0
129
inline bool PointerToArrayBase< UnalignedLVecBase4i >::operator !=(PointerToArrayBase< UnalignedLVecBase4i > const &other) const;

1078 19 ConstPointerToArray 0 260 1654 63 ConstPointerToArray< UnalignedLVecBase4i >::ConstPointerToArray 0 2 229 230 0
250
inline ConstPointerToArray< UnalignedLVecBase4i >::ConstPointerToArray(PointerToArray< UnalignedLVecBase4i > const &copy);
inline ConstPointerToArray< UnalignedLVecBase4i >::ConstPointerToArray(ConstPointerToArray< UnalignedLVecBase4i > const &copy);

1079 5 clear 0 4 1654 49 ConstPointerToArray< UnalignedLVecBase4i >::clear 0 1 231 0
68
inline void ConstPointerToArray< UnalignedLVecBase4i >::clear(void);

1080 4 size 0 4 1654 48 ConstPointerToArray< UnalignedLVecBase4i >::size 0 1 232 0
91
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4i >::size(void) const;

1081 11 get_element 0 4 1654 55 ConstPointerToArray< UnalignedLVecBase4i >::get_element 0 1 233 0
122
inline UnalignedLVecBase4i const &ConstPointerToArray< UnalignedLVecBase4i >::get_element(unsigned long long int n) const;

1082 11 __getitem__ 0 4 1654 55 ConstPointerToArray< UnalignedLVecBase4i >::__getitem__ 0 1 234 0
115
UnalignedLVecBase4i const &ConstPointerToArray< UnalignedLVecBase4i >::__getitem__(unsigned long long int n) const;

1083 8 get_data 0 4 1654 52 ConstPointerToArray< UnalignedLVecBase4i >::get_data 0 1 235 0
75
PyObject *ConstPointerToArray< UnalignedLVecBase4i >::get_data(void) const;

1084 11 get_subdata 0 4 1654 55 ConstPointerToArray< UnalignedLVecBase4i >::get_subdata 0 1 236 0
128
PyObject *ConstPointerToArray< UnalignedLVecBase4i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1085 13 get_ref_count 0 4 1654 57 ConstPointerToArray< UnalignedLVecBase4i >::get_ref_count 0 1 237 0
81
inline int ConstPointerToArray< UnalignedLVecBase4i >::get_ref_count(void) const;

1086 18 get_node_ref_count 0 4 1654 62 ConstPointerToArray< UnalignedLVecBase4i >::get_node_ref_count 0 1 238 0
86
inline int ConstPointerToArray< UnalignedLVecBase4i >::get_node_ref_count(void) const;

1087 5 count 0 4 1654 49 ConstPointerToArray< UnalignedLVecBase4i >::count 0 1 239 0
115
inline unsigned long long int ConstPointerToArray< UnalignedLVecBase4i >::count(UnalignedLVecBase4i const &) const;

1088 10 __reduce__ 0 4 1654 54 ConstPointerToArray< UnalignedLVecBase4i >::__reduce__ 0 1 240 0
87
PyObject *ConstPointerToArray< UnalignedLVecBase4i >::__reduce__(PyObject *self) const;

1089 13 __getbuffer__ 0 4 1654 57 ConstPointerToArray< UnalignedLVecBase4i >::__getbuffer__ 0 1 241 0
112
int ConstPointerToArray< UnalignedLVecBase4i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags) const;

1090 17 __releasebuffer__ 0 4 1654 61 ConstPointerToArray< UnalignedLVecBase4i >::__releasebuffer__ 0 1 242 0
106
void ConstPointerToArray< UnalignedLVecBase4i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1091 12 __deepcopy__ 0 4 1654 56 ConstPointerToArray< UnalignedLVecBase4i >::__deepcopy__ 0 1 243 0
122
ConstPointerToArray< UnalignedLVecBase4i > ConstPointerToArray< UnalignedLVecBase4i >::__deepcopy__(PyObject *memo) const;

1092 20 ~ConstPointerToArray 0 516 1654 64 ConstPointerToArray< UnalignedLVecBase4i >::~ConstPointerToArray 0 0 0
71
ConstPointerToArray< UnalignedLVecBase4i >::~ConstPointerToArray(void);

1093 14 PointerToArray 0 260 1657 43 PointerToArray< LMatrix3d >::PointerToArray 0 3 248 249 250 0
294
inline PointerToArray< LMatrix3d >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LMatrix3d const *)(0))));
inline PointerToArray< LMatrix3d >::PointerToArray(PointerToArray< LMatrix3d > const &copy);
PointerToArray< LMatrix3d >::PointerToArray(PyObject *self, PyObject *source);

1094 11 empty_array 0 4 1657 40 PointerToArray< LMatrix3d >::empty_array 0 1 251 0
180
static inline PointerToArray< LMatrix3d > PointerToArray< LMatrix3d >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LMatrix3d const *)(0))));

1095 5 clear 0 4 1657 34 PointerToArray< LMatrix3d >::clear 0 1 252 0
53
inline void PointerToArray< LMatrix3d >::clear(void);

1096 4 size 0 4 1657 33 PointerToArray< LMatrix3d >::size 0 1 253 0
76
inline unsigned long long int PointerToArray< LMatrix3d >::size(void) const;

1097 9 push_back 0 4 1657 38 PointerToArray< LMatrix3d >::push_back 0 1 254 0
71
inline void PointerToArray< LMatrix3d >::push_back(LMatrix3d const &x);

1098 8 pop_back 0 4 1657 37 PointerToArray< LMatrix3d >::pop_back 0 1 255 0
56
inline void PointerToArray< LMatrix3d >::pop_back(void);

1099 11 get_element 0 4 1657 40 PointerToArray< LMatrix3d >::get_element 0 1 256 0
97
inline LMatrix3d const &PointerToArray< LMatrix3d >::get_element(unsigned long long int n) const;

1100 11 set_element 0 4 1657 40 PointerToArray< LMatrix3d >::set_element 0 1 257 0
103
inline void PointerToArray< LMatrix3d >::set_element(unsigned long long int n, LMatrix3d const &value);

1101 11 __getitem__ 0 4 1657 40 PointerToArray< LMatrix3d >::__getitem__ 0 1 258 0
90
LMatrix3d const &PointerToArray< LMatrix3d >::__getitem__(unsigned long long int n) const;

1102 11 __setitem__ 0 4 1657 40 PointerToArray< LMatrix3d >::__setitem__ 0 1 259 0
96
void PointerToArray< LMatrix3d >::__setitem__(unsigned long long int n, LMatrix3d const &value);

1103 8 get_data 0 4 1657 37 PointerToArray< LMatrix3d >::get_data 0 1 260 0
60
PyObject *PointerToArray< LMatrix3d >::get_data(void) const;

1104 8 set_data 0 4 1657 37 PointerToArray< LMatrix3d >::set_data 0 1 261 0
59
void PointerToArray< LMatrix3d >::set_data(PyObject *data);

1105 11 get_subdata 0 4 1657 40 PointerToArray< LMatrix3d >::get_subdata 0 1 262 0
113
PyObject *PointerToArray< LMatrix3d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1106 11 set_subdata 0 4 1657 40 PointerToArray< LMatrix3d >::set_subdata 0 1 263 0
148
inline void PointerToArray< LMatrix3d >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1107 13 get_ref_count 0 4 1657 42 PointerToArray< LMatrix3d >::get_ref_count 0 1 264 0
66
inline int PointerToArray< LMatrix3d >::get_ref_count(void) const;

1108 18 get_node_ref_count 0 4 1657 47 PointerToArray< LMatrix3d >::get_node_ref_count 0 1 265 0
71
inline int PointerToArray< LMatrix3d >::get_node_ref_count(void) const;

1109 5 count 0 4 1657 34 PointerToArray< LMatrix3d >::count 0 1 266 0
90
inline unsigned long long int PointerToArray< LMatrix3d >::count(LMatrix3d const &) const;

1110 10 __reduce__ 0 4 1657 39 PointerToArray< LMatrix3d >::__reduce__ 0 1 267 0
72
PyObject *PointerToArray< LMatrix3d >::__reduce__(PyObject *self) const;

1111 13 __getbuffer__ 0 4 1657 42 PointerToArray< LMatrix3d >::__getbuffer__ 0 1 268 0
91
int PointerToArray< LMatrix3d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1112 17 __releasebuffer__ 0 4 1657 46 PointerToArray< LMatrix3d >::__releasebuffer__ 0 1 269 0
91
void PointerToArray< LMatrix3d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1113 12 __deepcopy__ 0 4 1657 41 PointerToArray< LMatrix3d >::__deepcopy__ 0 1 270 0
92
PointerToArray< LMatrix3d > PointerToArray< LMatrix3d >::__deepcopy__(PyObject *memo) const;

1114 15 ~PointerToArray 0 516 1657 44 PointerToArray< LMatrix3d >::~PointerToArray 0 0 0
51
PointerToArray< LMatrix3d >::~PointerToArray(void);

1115 14 PointerToArray 0 260 1658 43 PointerToArray< LMatrix3f >::PointerToArray 0 3 271 272 273 0
294
inline PointerToArray< LMatrix3f >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LMatrix3f const *)(0))));
inline PointerToArray< LMatrix3f >::PointerToArray(PointerToArray< LMatrix3f > const &copy);
PointerToArray< LMatrix3f >::PointerToArray(PyObject *self, PyObject *source);

1116 11 empty_array 0 4 1658 40 PointerToArray< LMatrix3f >::empty_array 0 1 274 0
180
static inline PointerToArray< LMatrix3f > PointerToArray< LMatrix3f >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LMatrix3f const *)(0))));

1117 5 clear 0 4 1658 34 PointerToArray< LMatrix3f >::clear 0 1 275 0
53
inline void PointerToArray< LMatrix3f >::clear(void);

1118 4 size 0 4 1658 33 PointerToArray< LMatrix3f >::size 0 1 276 0
76
inline unsigned long long int PointerToArray< LMatrix3f >::size(void) const;

1119 9 push_back 0 4 1658 38 PointerToArray< LMatrix3f >::push_back 0 1 277 0
71
inline void PointerToArray< LMatrix3f >::push_back(LMatrix3f const &x);

1120 8 pop_back 0 4 1658 37 PointerToArray< LMatrix3f >::pop_back 0 1 278 0
56
inline void PointerToArray< LMatrix3f >::pop_back(void);

1121 11 get_element 0 4 1658 40 PointerToArray< LMatrix3f >::get_element 0 1 279 0
97
inline LMatrix3f const &PointerToArray< LMatrix3f >::get_element(unsigned long long int n) const;

1122 11 set_element 0 4 1658 40 PointerToArray< LMatrix3f >::set_element 0 1 280 0
103
inline void PointerToArray< LMatrix3f >::set_element(unsigned long long int n, LMatrix3f const &value);

1123 11 __getitem__ 0 4 1658 40 PointerToArray< LMatrix3f >::__getitem__ 0 1 281 0
90
LMatrix3f const &PointerToArray< LMatrix3f >::__getitem__(unsigned long long int n) const;

1124 11 __setitem__ 0 4 1658 40 PointerToArray< LMatrix3f >::__setitem__ 0 1 282 0
96
void PointerToArray< LMatrix3f >::__setitem__(unsigned long long int n, LMatrix3f const &value);

1125 8 get_data 0 4 1658 37 PointerToArray< LMatrix3f >::get_data 0 1 283 0
60
PyObject *PointerToArray< LMatrix3f >::get_data(void) const;

1126 8 set_data 0 4 1658 37 PointerToArray< LMatrix3f >::set_data 0 1 284 0
59
void PointerToArray< LMatrix3f >::set_data(PyObject *data);

1127 11 get_subdata 0 4 1658 40 PointerToArray< LMatrix3f >::get_subdata 0 1 285 0
113
PyObject *PointerToArray< LMatrix3f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1128 11 set_subdata 0 4 1658 40 PointerToArray< LMatrix3f >::set_subdata 0 1 286 0
148
inline void PointerToArray< LMatrix3f >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1129 13 get_ref_count 0 4 1658 42 PointerToArray< LMatrix3f >::get_ref_count 0 1 287 0
66
inline int PointerToArray< LMatrix3f >::get_ref_count(void) const;

1130 18 get_node_ref_count 0 4 1658 47 PointerToArray< LMatrix3f >::get_node_ref_count 0 1 288 0
71
inline int PointerToArray< LMatrix3f >::get_node_ref_count(void) const;

1131 5 count 0 4 1658 34 PointerToArray< LMatrix3f >::count 0 1 289 0
90
inline unsigned long long int PointerToArray< LMatrix3f >::count(LMatrix3f const &) const;

1132 10 __reduce__ 0 4 1658 39 PointerToArray< LMatrix3f >::__reduce__ 0 1 290 0
72
PyObject *PointerToArray< LMatrix3f >::__reduce__(PyObject *self) const;

1133 13 __getbuffer__ 0 4 1658 42 PointerToArray< LMatrix3f >::__getbuffer__ 0 1 291 0
91
int PointerToArray< LMatrix3f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1134 17 __releasebuffer__ 0 4 1658 46 PointerToArray< LMatrix3f >::__releasebuffer__ 0 1 292 0
91
void PointerToArray< LMatrix3f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1135 12 __deepcopy__ 0 4 1658 41 PointerToArray< LMatrix3f >::__deepcopy__ 0 1 293 0
92
PointerToArray< LMatrix3f > PointerToArray< LMatrix3f >::__deepcopy__(PyObject *memo) const;

1136 15 ~PointerToArray 0 516 1658 44 PointerToArray< LMatrix3f >::~PointerToArray 0 0 0
51
PointerToArray< LMatrix3f >::~PointerToArray(void);

1137 14 PointerToArray 0 260 1659 44 PointerToArray< LVecBase2d >::PointerToArray 0 3 294 295 296 0
299
inline PointerToArray< LVecBase2d >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase2d const *)(0))));
inline PointerToArray< LVecBase2d >::PointerToArray(PointerToArray< LVecBase2d > const &copy);
PointerToArray< LVecBase2d >::PointerToArray(PyObject *self, PyObject *source);

1138 11 empty_array 0 4 1659 41 PointerToArray< LVecBase2d >::empty_array 0 1 297 0
183
static inline PointerToArray< LVecBase2d > PointerToArray< LVecBase2d >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase2d const *)(0))));

1139 5 clear 0 4 1659 35 PointerToArray< LVecBase2d >::clear 0 1 298 0
54
inline void PointerToArray< LVecBase2d >::clear(void);

1140 4 size 0 4 1659 34 PointerToArray< LVecBase2d >::size 0 1 299 0
77
inline unsigned long long int PointerToArray< LVecBase2d >::size(void) const;

1141 9 push_back 0 4 1659 39 PointerToArray< LVecBase2d >::push_back 0 1 300 0
73
inline void PointerToArray< LVecBase2d >::push_back(LVecBase2d const &x);

1142 8 pop_back 0 4 1659 38 PointerToArray< LVecBase2d >::pop_back 0 1 301 0
57
inline void PointerToArray< LVecBase2d >::pop_back(void);

1143 11 get_element 0 4 1659 41 PointerToArray< LVecBase2d >::get_element 0 1 302 0
99
inline LVecBase2d const &PointerToArray< LVecBase2d >::get_element(unsigned long long int n) const;

1144 11 set_element 0 4 1659 41 PointerToArray< LVecBase2d >::set_element 0 1 303 0
105
inline void PointerToArray< LVecBase2d >::set_element(unsigned long long int n, LVecBase2d const &value);

1145 11 __getitem__ 0 4 1659 41 PointerToArray< LVecBase2d >::__getitem__ 0 1 304 0
92
LVecBase2d const &PointerToArray< LVecBase2d >::__getitem__(unsigned long long int n) const;

1146 11 __setitem__ 0 4 1659 41 PointerToArray< LVecBase2d >::__setitem__ 0 1 305 0
98
void PointerToArray< LVecBase2d >::__setitem__(unsigned long long int n, LVecBase2d const &value);

1147 8 get_data 0 4 1659 38 PointerToArray< LVecBase2d >::get_data 0 1 306 0
61
PyObject *PointerToArray< LVecBase2d >::get_data(void) const;

1148 8 set_data 0 4 1659 38 PointerToArray< LVecBase2d >::set_data 0 1 307 0
60
void PointerToArray< LVecBase2d >::set_data(PyObject *data);

1149 11 get_subdata 0 4 1659 41 PointerToArray< LVecBase2d >::get_subdata 0 1 308 0
114
PyObject *PointerToArray< LVecBase2d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1150 11 set_subdata 0 4 1659 41 PointerToArray< LVecBase2d >::set_subdata 0 1 309 0
149
inline void PointerToArray< LVecBase2d >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1151 13 get_ref_count 0 4 1659 43 PointerToArray< LVecBase2d >::get_ref_count 0 1 310 0
67
inline int PointerToArray< LVecBase2d >::get_ref_count(void) const;

1152 18 get_node_ref_count 0 4 1659 48 PointerToArray< LVecBase2d >::get_node_ref_count 0 1 311 0
72
inline int PointerToArray< LVecBase2d >::get_node_ref_count(void) const;

1153 5 count 0 4 1659 35 PointerToArray< LVecBase2d >::count 0 1 312 0
92
inline unsigned long long int PointerToArray< LVecBase2d >::count(LVecBase2d const &) const;

1154 10 __reduce__ 0 4 1659 40 PointerToArray< LVecBase2d >::__reduce__ 0 1 313 0
73
PyObject *PointerToArray< LVecBase2d >::__reduce__(PyObject *self) const;

1155 13 __getbuffer__ 0 4 1659 43 PointerToArray< LVecBase2d >::__getbuffer__ 0 1 314 0
92
int PointerToArray< LVecBase2d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1156 17 __releasebuffer__ 0 4 1659 47 PointerToArray< LVecBase2d >::__releasebuffer__ 0 1 315 0
92
void PointerToArray< LVecBase2d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1157 12 __deepcopy__ 0 4 1659 42 PointerToArray< LVecBase2d >::__deepcopy__ 0 1 316 0
94
PointerToArray< LVecBase2d > PointerToArray< LVecBase2d >::__deepcopy__(PyObject *memo) const;

1158 15 ~PointerToArray 0 516 1659 45 PointerToArray< LVecBase2d >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase2d >::~PointerToArray(void);

1159 14 PointerToArray 0 260 1660 44 PointerToArray< LVecBase2f >::PointerToArray 0 3 317 318 319 0
299
inline PointerToArray< LVecBase2f >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase2f const *)(0))));
inline PointerToArray< LVecBase2f >::PointerToArray(PointerToArray< LVecBase2f > const &copy);
PointerToArray< LVecBase2f >::PointerToArray(PyObject *self, PyObject *source);

1160 11 empty_array 0 4 1660 41 PointerToArray< LVecBase2f >::empty_array 0 1 320 0
183
static inline PointerToArray< LVecBase2f > PointerToArray< LVecBase2f >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase2f const *)(0))));

1161 5 clear 0 4 1660 35 PointerToArray< LVecBase2f >::clear 0 1 321 0
54
inline void PointerToArray< LVecBase2f >::clear(void);

1162 4 size 0 4 1660 34 PointerToArray< LVecBase2f >::size 0 1 322 0
77
inline unsigned long long int PointerToArray< LVecBase2f >::size(void) const;

1163 9 push_back 0 4 1660 39 PointerToArray< LVecBase2f >::push_back 0 1 323 0
73
inline void PointerToArray< LVecBase2f >::push_back(LVecBase2f const &x);

1164 8 pop_back 0 4 1660 38 PointerToArray< LVecBase2f >::pop_back 0 1 324 0
57
inline void PointerToArray< LVecBase2f >::pop_back(void);

1165 11 get_element 0 4 1660 41 PointerToArray< LVecBase2f >::get_element 0 1 325 0
99
inline LVecBase2f const &PointerToArray< LVecBase2f >::get_element(unsigned long long int n) const;

1166 11 set_element 0 4 1660 41 PointerToArray< LVecBase2f >::set_element 0 1 326 0
105
inline void PointerToArray< LVecBase2f >::set_element(unsigned long long int n, LVecBase2f const &value);

1167 11 __getitem__ 0 4 1660 41 PointerToArray< LVecBase2f >::__getitem__ 0 1 327 0
92
LVecBase2f const &PointerToArray< LVecBase2f >::__getitem__(unsigned long long int n) const;

1168 11 __setitem__ 0 4 1660 41 PointerToArray< LVecBase2f >::__setitem__ 0 1 328 0
98
void PointerToArray< LVecBase2f >::__setitem__(unsigned long long int n, LVecBase2f const &value);

1169 8 get_data 0 4 1660 38 PointerToArray< LVecBase2f >::get_data 0 1 329 0
61
PyObject *PointerToArray< LVecBase2f >::get_data(void) const;

1170 8 set_data 0 4 1660 38 PointerToArray< LVecBase2f >::set_data 0 1 330 0
60
void PointerToArray< LVecBase2f >::set_data(PyObject *data);

1171 11 get_subdata 0 4 1660 41 PointerToArray< LVecBase2f >::get_subdata 0 1 331 0
114
PyObject *PointerToArray< LVecBase2f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1172 11 set_subdata 0 4 1660 41 PointerToArray< LVecBase2f >::set_subdata 0 1 332 0
149
inline void PointerToArray< LVecBase2f >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1173 13 get_ref_count 0 4 1660 43 PointerToArray< LVecBase2f >::get_ref_count 0 1 333 0
67
inline int PointerToArray< LVecBase2f >::get_ref_count(void) const;

1174 18 get_node_ref_count 0 4 1660 48 PointerToArray< LVecBase2f >::get_node_ref_count 0 1 334 0
72
inline int PointerToArray< LVecBase2f >::get_node_ref_count(void) const;

1175 5 count 0 4 1660 35 PointerToArray< LVecBase2f >::count 0 1 335 0
92
inline unsigned long long int PointerToArray< LVecBase2f >::count(LVecBase2f const &) const;

1176 10 __reduce__ 0 4 1660 40 PointerToArray< LVecBase2f >::__reduce__ 0 1 336 0
73
PyObject *PointerToArray< LVecBase2f >::__reduce__(PyObject *self) const;

1177 13 __getbuffer__ 0 4 1660 43 PointerToArray< LVecBase2f >::__getbuffer__ 0 1 337 0
92
int PointerToArray< LVecBase2f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1178 17 __releasebuffer__ 0 4 1660 47 PointerToArray< LVecBase2f >::__releasebuffer__ 0 1 338 0
92
void PointerToArray< LVecBase2f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1179 12 __deepcopy__ 0 4 1660 42 PointerToArray< LVecBase2f >::__deepcopy__ 0 1 339 0
94
PointerToArray< LVecBase2f > PointerToArray< LVecBase2f >::__deepcopy__(PyObject *memo) const;

1180 15 ~PointerToArray 0 516 1660 45 PointerToArray< LVecBase2f >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase2f >::~PointerToArray(void);

1181 14 PointerToArray 0 260 1661 44 PointerToArray< LVecBase2i >::PointerToArray 0 3 340 341 342 0
299
inline PointerToArray< LVecBase2i >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase2i const *)(0))));
inline PointerToArray< LVecBase2i >::PointerToArray(PointerToArray< LVecBase2i > const &copy);
PointerToArray< LVecBase2i >::PointerToArray(PyObject *self, PyObject *source);

1182 11 empty_array 0 4 1661 41 PointerToArray< LVecBase2i >::empty_array 0 1 343 0
183
static inline PointerToArray< LVecBase2i > PointerToArray< LVecBase2i >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase2i const *)(0))));

1183 5 clear 0 4 1661 35 PointerToArray< LVecBase2i >::clear 0 1 344 0
54
inline void PointerToArray< LVecBase2i >::clear(void);

1184 4 size 0 4 1661 34 PointerToArray< LVecBase2i >::size 0 1 345 0
77
inline unsigned long long int PointerToArray< LVecBase2i >::size(void) const;

1185 9 push_back 0 4 1661 39 PointerToArray< LVecBase2i >::push_back 0 1 346 0
73
inline void PointerToArray< LVecBase2i >::push_back(LVecBase2i const &x);

1186 8 pop_back 0 4 1661 38 PointerToArray< LVecBase2i >::pop_back 0 1 347 0
57
inline void PointerToArray< LVecBase2i >::pop_back(void);

1187 11 get_element 0 4 1661 41 PointerToArray< LVecBase2i >::get_element 0 1 348 0
99
inline LVecBase2i const &PointerToArray< LVecBase2i >::get_element(unsigned long long int n) const;

1188 11 set_element 0 4 1661 41 PointerToArray< LVecBase2i >::set_element 0 1 349 0
105
inline void PointerToArray< LVecBase2i >::set_element(unsigned long long int n, LVecBase2i const &value);

1189 11 __getitem__ 0 4 1661 41 PointerToArray< LVecBase2i >::__getitem__ 0 1 350 0
92
LVecBase2i const &PointerToArray< LVecBase2i >::__getitem__(unsigned long long int n) const;

1190 11 __setitem__ 0 4 1661 41 PointerToArray< LVecBase2i >::__setitem__ 0 1 351 0
98
void PointerToArray< LVecBase2i >::__setitem__(unsigned long long int n, LVecBase2i const &value);

1191 8 get_data 0 4 1661 38 PointerToArray< LVecBase2i >::get_data 0 1 352 0
61
PyObject *PointerToArray< LVecBase2i >::get_data(void) const;

1192 8 set_data 0 4 1661 38 PointerToArray< LVecBase2i >::set_data 0 1 353 0
60
void PointerToArray< LVecBase2i >::set_data(PyObject *data);

1193 11 get_subdata 0 4 1661 41 PointerToArray< LVecBase2i >::get_subdata 0 1 354 0
114
PyObject *PointerToArray< LVecBase2i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1194 11 set_subdata 0 4 1661 41 PointerToArray< LVecBase2i >::set_subdata 0 1 355 0
149
inline void PointerToArray< LVecBase2i >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1195 13 get_ref_count 0 4 1661 43 PointerToArray< LVecBase2i >::get_ref_count 0 1 356 0
67
inline int PointerToArray< LVecBase2i >::get_ref_count(void) const;

1196 18 get_node_ref_count 0 4 1661 48 PointerToArray< LVecBase2i >::get_node_ref_count 0 1 357 0
72
inline int PointerToArray< LVecBase2i >::get_node_ref_count(void) const;

1197 5 count 0 4 1661 35 PointerToArray< LVecBase2i >::count 0 1 358 0
92
inline unsigned long long int PointerToArray< LVecBase2i >::count(LVecBase2i const &) const;

1198 10 __reduce__ 0 4 1661 40 PointerToArray< LVecBase2i >::__reduce__ 0 1 359 0
73
PyObject *PointerToArray< LVecBase2i >::__reduce__(PyObject *self) const;

1199 13 __getbuffer__ 0 4 1661 43 PointerToArray< LVecBase2i >::__getbuffer__ 0 1 360 0
92
int PointerToArray< LVecBase2i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1200 17 __releasebuffer__ 0 4 1661 47 PointerToArray< LVecBase2i >::__releasebuffer__ 0 1 361 0
92
void PointerToArray< LVecBase2i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1201 12 __deepcopy__ 0 4 1661 42 PointerToArray< LVecBase2i >::__deepcopy__ 0 1 362 0
94
PointerToArray< LVecBase2i > PointerToArray< LVecBase2i >::__deepcopy__(PyObject *memo) const;

1202 15 ~PointerToArray 0 516 1661 45 PointerToArray< LVecBase2i >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase2i >::~PointerToArray(void);

1203 14 PointerToArray 0 260 1662 44 PointerToArray< LVecBase3d >::PointerToArray 0 3 363 364 365 0
299
inline PointerToArray< LVecBase3d >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase3d const *)(0))));
inline PointerToArray< LVecBase3d >::PointerToArray(PointerToArray< LVecBase3d > const &copy);
PointerToArray< LVecBase3d >::PointerToArray(PyObject *self, PyObject *source);

1204 11 empty_array 0 4 1662 41 PointerToArray< LVecBase3d >::empty_array 0 1 366 0
183
static inline PointerToArray< LVecBase3d > PointerToArray< LVecBase3d >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase3d const *)(0))));

1205 5 clear 0 4 1662 35 PointerToArray< LVecBase3d >::clear 0 1 367 0
54
inline void PointerToArray< LVecBase3d >::clear(void);

1206 4 size 0 4 1662 34 PointerToArray< LVecBase3d >::size 0 1 368 0
77
inline unsigned long long int PointerToArray< LVecBase3d >::size(void) const;

1207 9 push_back 0 4 1662 39 PointerToArray< LVecBase3d >::push_back 0 1 369 0
73
inline void PointerToArray< LVecBase3d >::push_back(LVecBase3d const &x);

1208 8 pop_back 0 4 1662 38 PointerToArray< LVecBase3d >::pop_back 0 1 370 0
57
inline void PointerToArray< LVecBase3d >::pop_back(void);

1209 11 get_element 0 4 1662 41 PointerToArray< LVecBase3d >::get_element 0 1 371 0
99
inline LVecBase3d const &PointerToArray< LVecBase3d >::get_element(unsigned long long int n) const;

1210 11 set_element 0 4 1662 41 PointerToArray< LVecBase3d >::set_element 0 1 372 0
105
inline void PointerToArray< LVecBase3d >::set_element(unsigned long long int n, LVecBase3d const &value);

1211 11 __getitem__ 0 4 1662 41 PointerToArray< LVecBase3d >::__getitem__ 0 1 373 0
92
LVecBase3d const &PointerToArray< LVecBase3d >::__getitem__(unsigned long long int n) const;

1212 11 __setitem__ 0 4 1662 41 PointerToArray< LVecBase3d >::__setitem__ 0 1 374 0
98
void PointerToArray< LVecBase3d >::__setitem__(unsigned long long int n, LVecBase3d const &value);

1213 8 get_data 0 4 1662 38 PointerToArray< LVecBase3d >::get_data 0 1 375 0
61
PyObject *PointerToArray< LVecBase3d >::get_data(void) const;

1214 8 set_data 0 4 1662 38 PointerToArray< LVecBase3d >::set_data 0 1 376 0
60
void PointerToArray< LVecBase3d >::set_data(PyObject *data);

1215 11 get_subdata 0 4 1662 41 PointerToArray< LVecBase3d >::get_subdata 0 1 377 0
114
PyObject *PointerToArray< LVecBase3d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1216 11 set_subdata 0 4 1662 41 PointerToArray< LVecBase3d >::set_subdata 0 1 378 0
149
inline void PointerToArray< LVecBase3d >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1217 13 get_ref_count 0 4 1662 43 PointerToArray< LVecBase3d >::get_ref_count 0 1 379 0
67
inline int PointerToArray< LVecBase3d >::get_ref_count(void) const;

1218 18 get_node_ref_count 0 4 1662 48 PointerToArray< LVecBase3d >::get_node_ref_count 0 1 380 0
72
inline int PointerToArray< LVecBase3d >::get_node_ref_count(void) const;

1219 5 count 0 4 1662 35 PointerToArray< LVecBase3d >::count 0 1 381 0
92
inline unsigned long long int PointerToArray< LVecBase3d >::count(LVecBase3d const &) const;

1220 10 __reduce__ 0 4 1662 40 PointerToArray< LVecBase3d >::__reduce__ 0 1 382 0
73
PyObject *PointerToArray< LVecBase3d >::__reduce__(PyObject *self) const;

1221 13 __getbuffer__ 0 4 1662 43 PointerToArray< LVecBase3d >::__getbuffer__ 0 1 383 0
92
int PointerToArray< LVecBase3d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1222 17 __releasebuffer__ 0 4 1662 47 PointerToArray< LVecBase3d >::__releasebuffer__ 0 1 384 0
92
void PointerToArray< LVecBase3d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1223 12 __deepcopy__ 0 4 1662 42 PointerToArray< LVecBase3d >::__deepcopy__ 0 1 385 0
94
PointerToArray< LVecBase3d > PointerToArray< LVecBase3d >::__deepcopy__(PyObject *memo) const;

1224 15 ~PointerToArray 0 516 1662 45 PointerToArray< LVecBase3d >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase3d >::~PointerToArray(void);

1225 14 PointerToArray 0 260 1663 44 PointerToArray< LVecBase3f >::PointerToArray 0 3 386 387 388 0
299
inline PointerToArray< LVecBase3f >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase3f const *)(0))));
inline PointerToArray< LVecBase3f >::PointerToArray(PointerToArray< LVecBase3f > const &copy);
PointerToArray< LVecBase3f >::PointerToArray(PyObject *self, PyObject *source);

1226 11 empty_array 0 4 1663 41 PointerToArray< LVecBase3f >::empty_array 0 1 389 0
183
static inline PointerToArray< LVecBase3f > PointerToArray< LVecBase3f >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase3f const *)(0))));

1227 5 clear 0 4 1663 35 PointerToArray< LVecBase3f >::clear 0 1 390 0
54
inline void PointerToArray< LVecBase3f >::clear(void);

1228 4 size 0 4 1663 34 PointerToArray< LVecBase3f >::size 0 1 391 0
77
inline unsigned long long int PointerToArray< LVecBase3f >::size(void) const;

1229 9 push_back 0 4 1663 39 PointerToArray< LVecBase3f >::push_back 0 1 392 0
73
inline void PointerToArray< LVecBase3f >::push_back(LVecBase3f const &x);

1230 8 pop_back 0 4 1663 38 PointerToArray< LVecBase3f >::pop_back 0 1 393 0
57
inline void PointerToArray< LVecBase3f >::pop_back(void);

1231 11 get_element 0 4 1663 41 PointerToArray< LVecBase3f >::get_element 0 1 394 0
99
inline LVecBase3f const &PointerToArray< LVecBase3f >::get_element(unsigned long long int n) const;

1232 11 set_element 0 4 1663 41 PointerToArray< LVecBase3f >::set_element 0 1 395 0
105
inline void PointerToArray< LVecBase3f >::set_element(unsigned long long int n, LVecBase3f const &value);

1233 11 __getitem__ 0 4 1663 41 PointerToArray< LVecBase3f >::__getitem__ 0 1 396 0
92
LVecBase3f const &PointerToArray< LVecBase3f >::__getitem__(unsigned long long int n) const;

1234 11 __setitem__ 0 4 1663 41 PointerToArray< LVecBase3f >::__setitem__ 0 1 397 0
98
void PointerToArray< LVecBase3f >::__setitem__(unsigned long long int n, LVecBase3f const &value);

1235 8 get_data 0 4 1663 38 PointerToArray< LVecBase3f >::get_data 0 1 398 0
61
PyObject *PointerToArray< LVecBase3f >::get_data(void) const;

1236 8 set_data 0 4 1663 38 PointerToArray< LVecBase3f >::set_data 0 1 399 0
60
void PointerToArray< LVecBase3f >::set_data(PyObject *data);

1237 11 get_subdata 0 4 1663 41 PointerToArray< LVecBase3f >::get_subdata 0 1 400 0
114
PyObject *PointerToArray< LVecBase3f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1238 11 set_subdata 0 4 1663 41 PointerToArray< LVecBase3f >::set_subdata 0 1 401 0
149
inline void PointerToArray< LVecBase3f >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1239 13 get_ref_count 0 4 1663 43 PointerToArray< LVecBase3f >::get_ref_count 0 1 402 0
67
inline int PointerToArray< LVecBase3f >::get_ref_count(void) const;

1240 18 get_node_ref_count 0 4 1663 48 PointerToArray< LVecBase3f >::get_node_ref_count 0 1 403 0
72
inline int PointerToArray< LVecBase3f >::get_node_ref_count(void) const;

1241 5 count 0 4 1663 35 PointerToArray< LVecBase3f >::count 0 1 404 0
92
inline unsigned long long int PointerToArray< LVecBase3f >::count(LVecBase3f const &) const;

1242 10 __reduce__ 0 4 1663 40 PointerToArray< LVecBase3f >::__reduce__ 0 1 405 0
73
PyObject *PointerToArray< LVecBase3f >::__reduce__(PyObject *self) const;

1243 13 __getbuffer__ 0 4 1663 43 PointerToArray< LVecBase3f >::__getbuffer__ 0 1 406 0
92
int PointerToArray< LVecBase3f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1244 17 __releasebuffer__ 0 4 1663 47 PointerToArray< LVecBase3f >::__releasebuffer__ 0 1 407 0
92
void PointerToArray< LVecBase3f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1245 12 __deepcopy__ 0 4 1663 42 PointerToArray< LVecBase3f >::__deepcopy__ 0 1 408 0
94
PointerToArray< LVecBase3f > PointerToArray< LVecBase3f >::__deepcopy__(PyObject *memo) const;

1246 15 ~PointerToArray 0 516 1663 45 PointerToArray< LVecBase3f >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase3f >::~PointerToArray(void);

1247 14 PointerToArray 0 260 1664 44 PointerToArray< LVecBase3i >::PointerToArray 0 3 409 410 411 0
299
inline PointerToArray< LVecBase3i >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((LVecBase3i const *)(0))));
inline PointerToArray< LVecBase3i >::PointerToArray(PointerToArray< LVecBase3i > const &copy);
PointerToArray< LVecBase3i >::PointerToArray(PyObject *self, PyObject *source);

1248 11 empty_array 0 4 1664 41 PointerToArray< LVecBase3i >::empty_array 0 1 412 0
183
static inline PointerToArray< LVecBase3i > PointerToArray< LVecBase3i >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((LVecBase3i const *)(0))));

1249 5 clear 0 4 1664 35 PointerToArray< LVecBase3i >::clear 0 1 413 0
54
inline void PointerToArray< LVecBase3i >::clear(void);

1250 4 size 0 4 1664 34 PointerToArray< LVecBase3i >::size 0 1 414 0
77
inline unsigned long long int PointerToArray< LVecBase3i >::size(void) const;

1251 9 push_back 0 4 1664 39 PointerToArray< LVecBase3i >::push_back 0 1 415 0
73
inline void PointerToArray< LVecBase3i >::push_back(LVecBase3i const &x);

1252 8 pop_back 0 4 1664 38 PointerToArray< LVecBase3i >::pop_back 0 1 416 0
57
inline void PointerToArray< LVecBase3i >::pop_back(void);

1253 11 get_element 0 4 1664 41 PointerToArray< LVecBase3i >::get_element 0 1 417 0
99
inline LVecBase3i const &PointerToArray< LVecBase3i >::get_element(unsigned long long int n) const;

1254 11 set_element 0 4 1664 41 PointerToArray< LVecBase3i >::set_element 0 1 418 0
105
inline void PointerToArray< LVecBase3i >::set_element(unsigned long long int n, LVecBase3i const &value);

1255 11 __getitem__ 0 4 1664 41 PointerToArray< LVecBase3i >::__getitem__ 0 1 419 0
92
LVecBase3i const &PointerToArray< LVecBase3i >::__getitem__(unsigned long long int n) const;

1256 11 __setitem__ 0 4 1664 41 PointerToArray< LVecBase3i >::__setitem__ 0 1 420 0
98
void PointerToArray< LVecBase3i >::__setitem__(unsigned long long int n, LVecBase3i const &value);

1257 8 get_data 0 4 1664 38 PointerToArray< LVecBase3i >::get_data 0 1 421 0
61
PyObject *PointerToArray< LVecBase3i >::get_data(void) const;

1258 8 set_data 0 4 1664 38 PointerToArray< LVecBase3i >::set_data 0 1 422 0
60
void PointerToArray< LVecBase3i >::set_data(PyObject *data);

1259 11 get_subdata 0 4 1664 41 PointerToArray< LVecBase3i >::get_subdata 0 1 423 0
114
PyObject *PointerToArray< LVecBase3i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1260 11 set_subdata 0 4 1664 41 PointerToArray< LVecBase3i >::set_subdata 0 1 424 0
149
inline void PointerToArray< LVecBase3i >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1261 13 get_ref_count 0 4 1664 43 PointerToArray< LVecBase3i >::get_ref_count 0 1 425 0
67
inline int PointerToArray< LVecBase3i >::get_ref_count(void) const;

1262 18 get_node_ref_count 0 4 1664 48 PointerToArray< LVecBase3i >::get_node_ref_count 0 1 426 0
72
inline int PointerToArray< LVecBase3i >::get_node_ref_count(void) const;

1263 5 count 0 4 1664 35 PointerToArray< LVecBase3i >::count 0 1 427 0
92
inline unsigned long long int PointerToArray< LVecBase3i >::count(LVecBase3i const &) const;

1264 10 __reduce__ 0 4 1664 40 PointerToArray< LVecBase3i >::__reduce__ 0 1 428 0
73
PyObject *PointerToArray< LVecBase3i >::__reduce__(PyObject *self) const;

1265 13 __getbuffer__ 0 4 1664 43 PointerToArray< LVecBase3i >::__getbuffer__ 0 1 429 0
92
int PointerToArray< LVecBase3i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1266 17 __releasebuffer__ 0 4 1664 47 PointerToArray< LVecBase3i >::__releasebuffer__ 0 1 430 0
92
void PointerToArray< LVecBase3i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1267 12 __deepcopy__ 0 4 1664 42 PointerToArray< LVecBase3i >::__deepcopy__ 0 1 431 0
94
PointerToArray< LVecBase3i > PointerToArray< LVecBase3i >::__deepcopy__(PyObject *memo) const;

1268 15 ~PointerToArray 0 516 1664 45 PointerToArray< LVecBase3i >::~PointerToArray 0 0 0
52
PointerToArray< LVecBase3i >::~PointerToArray(void);

1269 14 PointerToArray 0 260 1665 52 PointerToArray< UnalignedLMatrix4d >::PointerToArray 0 3 432 433 434 0
339
inline PointerToArray< UnalignedLMatrix4d >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((UnalignedLMatrix4d const *)(0))));
inline PointerToArray< UnalignedLMatrix4d >::PointerToArray(PointerToArray< UnalignedLMatrix4d > const &copy);
PointerToArray< UnalignedLMatrix4d >::PointerToArray(PyObject *self, PyObject *source);

1270 11 empty_array 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::empty_array 0 1 435 0
207
static inline PointerToArray< UnalignedLMatrix4d > PointerToArray< UnalignedLMatrix4d >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((UnalignedLMatrix4d const *)(0))));

1271 5 clear 0 4 1665 43 PointerToArray< UnalignedLMatrix4d >::clear 0 1 436 0
62
inline void PointerToArray< UnalignedLMatrix4d >::clear(void);

1272 4 size 0 4 1665 42 PointerToArray< UnalignedLMatrix4d >::size 0 1 437 0
85
inline unsigned long long int PointerToArray< UnalignedLMatrix4d >::size(void) const;

1273 9 push_back 0 4 1665 47 PointerToArray< UnalignedLMatrix4d >::push_back 0 1 438 0
89
inline void PointerToArray< UnalignedLMatrix4d >::push_back(UnalignedLMatrix4d const &x);

1274 8 pop_back 0 4 1665 46 PointerToArray< UnalignedLMatrix4d >::pop_back 0 1 439 0
65
inline void PointerToArray< UnalignedLMatrix4d >::pop_back(void);

1275 11 get_element 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::get_element 0 1 440 0
115
inline UnalignedLMatrix4d const &PointerToArray< UnalignedLMatrix4d >::get_element(unsigned long long int n) const;

1276 11 set_element 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::set_element 0 1 441 0
121
inline void PointerToArray< UnalignedLMatrix4d >::set_element(unsigned long long int n, UnalignedLMatrix4d const &value);

1277 11 __getitem__ 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::__getitem__ 0 1 442 0
108
UnalignedLMatrix4d const &PointerToArray< UnalignedLMatrix4d >::__getitem__(unsigned long long int n) const;

1278 11 __setitem__ 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::__setitem__ 0 1 443 0
114
void PointerToArray< UnalignedLMatrix4d >::__setitem__(unsigned long long int n, UnalignedLMatrix4d const &value);

1279 8 get_data 0 4 1665 46 PointerToArray< UnalignedLMatrix4d >::get_data 0 1 444 0
69
PyObject *PointerToArray< UnalignedLMatrix4d >::get_data(void) const;

1280 8 set_data 0 4 1665 46 PointerToArray< UnalignedLMatrix4d >::set_data 0 1 445 0
68
void PointerToArray< UnalignedLMatrix4d >::set_data(PyObject *data);

1281 11 get_subdata 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::get_subdata 0 1 446 0
122
PyObject *PointerToArray< UnalignedLMatrix4d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1282 11 set_subdata 0 4 1665 49 PointerToArray< UnalignedLMatrix4d >::set_subdata 0 1 447 0
157
inline void PointerToArray< UnalignedLMatrix4d >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1283 13 get_ref_count 0 4 1665 51 PointerToArray< UnalignedLMatrix4d >::get_ref_count 0 1 448 0
75
inline int PointerToArray< UnalignedLMatrix4d >::get_ref_count(void) const;

1284 18 get_node_ref_count 0 4 1665 56 PointerToArray< UnalignedLMatrix4d >::get_node_ref_count 0 1 449 0
80
inline int PointerToArray< UnalignedLMatrix4d >::get_node_ref_count(void) const;

1285 5 count 0 4 1665 43 PointerToArray< UnalignedLMatrix4d >::count 0 1 450 0
108
inline unsigned long long int PointerToArray< UnalignedLMatrix4d >::count(UnalignedLMatrix4d const &) const;

1286 10 __reduce__ 0 4 1665 48 PointerToArray< UnalignedLMatrix4d >::__reduce__ 0 1 451 0
81
PyObject *PointerToArray< UnalignedLMatrix4d >::__reduce__(PyObject *self) const;

1287 13 __getbuffer__ 0 4 1665 51 PointerToArray< UnalignedLMatrix4d >::__getbuffer__ 0 1 452 0
100
int PointerToArray< UnalignedLMatrix4d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1288 17 __releasebuffer__ 0 4 1665 55 PointerToArray< UnalignedLMatrix4d >::__releasebuffer__ 0 1 453 0
100
void PointerToArray< UnalignedLMatrix4d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1289 12 __deepcopy__ 0 4 1665 50 PointerToArray< UnalignedLMatrix4d >::__deepcopy__ 0 1 454 0
110
PointerToArray< UnalignedLMatrix4d > PointerToArray< UnalignedLMatrix4d >::__deepcopy__(PyObject *memo) const;

1290 15 ~PointerToArray 0 516 1665 53 PointerToArray< UnalignedLMatrix4d >::~PointerToArray 0 0 0
60
PointerToArray< UnalignedLMatrix4d >::~PointerToArray(void);

1291 14 PointerToArray 0 260 1666 52 PointerToArray< UnalignedLMatrix4f >::PointerToArray 0 3 455 456 457 0
339
inline PointerToArray< UnalignedLMatrix4f >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((UnalignedLMatrix4f const *)(0))));
inline PointerToArray< UnalignedLMatrix4f >::PointerToArray(PointerToArray< UnalignedLMatrix4f > const &copy);
PointerToArray< UnalignedLMatrix4f >::PointerToArray(PyObject *self, PyObject *source);

1292 11 empty_array 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::empty_array 0 1 458 0
207
static inline PointerToArray< UnalignedLMatrix4f > PointerToArray< UnalignedLMatrix4f >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((UnalignedLMatrix4f const *)(0))));

1293 5 clear 0 4 1666 43 PointerToArray< UnalignedLMatrix4f >::clear 0 1 459 0
62
inline void PointerToArray< UnalignedLMatrix4f >::clear(void);

1294 4 size 0 4 1666 42 PointerToArray< UnalignedLMatrix4f >::size 0 1 460 0
85
inline unsigned long long int PointerToArray< UnalignedLMatrix4f >::size(void) const;

1295 9 push_back 0 4 1666 47 PointerToArray< UnalignedLMatrix4f >::push_back 0 1 461 0
89
inline void PointerToArray< UnalignedLMatrix4f >::push_back(UnalignedLMatrix4f const &x);

1296 8 pop_back 0 4 1666 46 PointerToArray< UnalignedLMatrix4f >::pop_back 0 1 462 0
65
inline void PointerToArray< UnalignedLMatrix4f >::pop_back(void);

1297 11 get_element 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::get_element 0 1 463 0
115
inline UnalignedLMatrix4f const &PointerToArray< UnalignedLMatrix4f >::get_element(unsigned long long int n) const;

1298 11 set_element 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::set_element 0 1 464 0
121
inline void PointerToArray< UnalignedLMatrix4f >::set_element(unsigned long long int n, UnalignedLMatrix4f const &value);

1299 11 __getitem__ 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::__getitem__ 0 1 465 0
108
UnalignedLMatrix4f const &PointerToArray< UnalignedLMatrix4f >::__getitem__(unsigned long long int n) const;

1300 11 __setitem__ 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::__setitem__ 0 1 466 0
114
void PointerToArray< UnalignedLMatrix4f >::__setitem__(unsigned long long int n, UnalignedLMatrix4f const &value);

1301 8 get_data 0 4 1666 46 PointerToArray< UnalignedLMatrix4f >::get_data 0 1 467 0
69
PyObject *PointerToArray< UnalignedLMatrix4f >::get_data(void) const;

1302 8 set_data 0 4 1666 46 PointerToArray< UnalignedLMatrix4f >::set_data 0 1 468 0
68
void PointerToArray< UnalignedLMatrix4f >::set_data(PyObject *data);

1303 11 get_subdata 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::get_subdata 0 1 469 0
122
PyObject *PointerToArray< UnalignedLMatrix4f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1304 11 set_subdata 0 4 1666 49 PointerToArray< UnalignedLMatrix4f >::set_subdata 0 1 470 0
157
inline void PointerToArray< UnalignedLMatrix4f >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1305 13 get_ref_count 0 4 1666 51 PointerToArray< UnalignedLMatrix4f >::get_ref_count 0 1 471 0
75
inline int PointerToArray< UnalignedLMatrix4f >::get_ref_count(void) const;

1306 18 get_node_ref_count 0 4 1666 56 PointerToArray< UnalignedLMatrix4f >::get_node_ref_count 0 1 472 0
80
inline int PointerToArray< UnalignedLMatrix4f >::get_node_ref_count(void) const;

1307 5 count 0 4 1666 43 PointerToArray< UnalignedLMatrix4f >::count 0 1 473 0
108
inline unsigned long long int PointerToArray< UnalignedLMatrix4f >::count(UnalignedLMatrix4f const &) const;

1308 10 __reduce__ 0 4 1666 48 PointerToArray< UnalignedLMatrix4f >::__reduce__ 0 1 474 0
81
PyObject *PointerToArray< UnalignedLMatrix4f >::__reduce__(PyObject *self) const;

1309 13 __getbuffer__ 0 4 1666 51 PointerToArray< UnalignedLMatrix4f >::__getbuffer__ 0 1 475 0
100
int PointerToArray< UnalignedLMatrix4f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1310 17 __releasebuffer__ 0 4 1666 55 PointerToArray< UnalignedLMatrix4f >::__releasebuffer__ 0 1 476 0
100
void PointerToArray< UnalignedLMatrix4f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1311 12 __deepcopy__ 0 4 1666 50 PointerToArray< UnalignedLMatrix4f >::__deepcopy__ 0 1 477 0
110
PointerToArray< UnalignedLMatrix4f > PointerToArray< UnalignedLMatrix4f >::__deepcopy__(PyObject *memo) const;

1312 15 ~PointerToArray 0 516 1666 53 PointerToArray< UnalignedLMatrix4f >::~PointerToArray 0 0 0
60
PointerToArray< UnalignedLMatrix4f >::~PointerToArray(void);

1313 14 PointerToArray 0 260 1667 53 PointerToArray< UnalignedLVecBase4d >::PointerToArray 0 3 478 479 480 0
344
inline PointerToArray< UnalignedLVecBase4d >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4d const *)(0))));
inline PointerToArray< UnalignedLVecBase4d >::PointerToArray(PointerToArray< UnalignedLVecBase4d > const &copy);
PointerToArray< UnalignedLVecBase4d >::PointerToArray(PyObject *self, PyObject *source);

1314 11 empty_array 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::empty_array 0 1 481 0
210
static inline PointerToArray< UnalignedLVecBase4d > PointerToArray< UnalignedLVecBase4d >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4d const *)(0))));

1315 5 clear 0 4 1667 44 PointerToArray< UnalignedLVecBase4d >::clear 0 1 482 0
63
inline void PointerToArray< UnalignedLVecBase4d >::clear(void);

1316 4 size 0 4 1667 43 PointerToArray< UnalignedLVecBase4d >::size 0 1 483 0
86
inline unsigned long long int PointerToArray< UnalignedLVecBase4d >::size(void) const;

1317 9 push_back 0 4 1667 48 PointerToArray< UnalignedLVecBase4d >::push_back 0 1 484 0
91
inline void PointerToArray< UnalignedLVecBase4d >::push_back(UnalignedLVecBase4d const &x);

1318 8 pop_back 0 4 1667 47 PointerToArray< UnalignedLVecBase4d >::pop_back 0 1 485 0
66
inline void PointerToArray< UnalignedLVecBase4d >::pop_back(void);

1319 11 get_element 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::get_element 0 1 486 0
117
inline UnalignedLVecBase4d const &PointerToArray< UnalignedLVecBase4d >::get_element(unsigned long long int n) const;

1320 11 set_element 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::set_element 0 1 487 0
123
inline void PointerToArray< UnalignedLVecBase4d >::set_element(unsigned long long int n, UnalignedLVecBase4d const &value);

1321 11 __getitem__ 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::__getitem__ 0 1 488 0
110
UnalignedLVecBase4d const &PointerToArray< UnalignedLVecBase4d >::__getitem__(unsigned long long int n) const;

1322 11 __setitem__ 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::__setitem__ 0 1 489 0
116
void PointerToArray< UnalignedLVecBase4d >::__setitem__(unsigned long long int n, UnalignedLVecBase4d const &value);

1323 8 get_data 0 4 1667 47 PointerToArray< UnalignedLVecBase4d >::get_data 0 1 490 0
70
PyObject *PointerToArray< UnalignedLVecBase4d >::get_data(void) const;

1324 8 set_data 0 4 1667 47 PointerToArray< UnalignedLVecBase4d >::set_data 0 1 491 0
69
void PointerToArray< UnalignedLVecBase4d >::set_data(PyObject *data);

1325 11 get_subdata 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::get_subdata 0 1 492 0
123
PyObject *PointerToArray< UnalignedLVecBase4d >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1326 11 set_subdata 0 4 1667 50 PointerToArray< UnalignedLVecBase4d >::set_subdata 0 1 493 0
158
inline void PointerToArray< UnalignedLVecBase4d >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1327 13 get_ref_count 0 4 1667 52 PointerToArray< UnalignedLVecBase4d >::get_ref_count 0 1 494 0
76
inline int PointerToArray< UnalignedLVecBase4d >::get_ref_count(void) const;

1328 18 get_node_ref_count 0 4 1667 57 PointerToArray< UnalignedLVecBase4d >::get_node_ref_count 0 1 495 0
81
inline int PointerToArray< UnalignedLVecBase4d >::get_node_ref_count(void) const;

1329 5 count 0 4 1667 44 PointerToArray< UnalignedLVecBase4d >::count 0 1 496 0
110
inline unsigned long long int PointerToArray< UnalignedLVecBase4d >::count(UnalignedLVecBase4d const &) const;

1330 10 __reduce__ 0 4 1667 49 PointerToArray< UnalignedLVecBase4d >::__reduce__ 0 1 497 0
82
PyObject *PointerToArray< UnalignedLVecBase4d >::__reduce__(PyObject *self) const;

1331 13 __getbuffer__ 0 4 1667 52 PointerToArray< UnalignedLVecBase4d >::__getbuffer__ 0 1 498 0
101
int PointerToArray< UnalignedLVecBase4d >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1332 17 __releasebuffer__ 0 4 1667 56 PointerToArray< UnalignedLVecBase4d >::__releasebuffer__ 0 1 499 0
101
void PointerToArray< UnalignedLVecBase4d >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1333 12 __deepcopy__ 0 4 1667 51 PointerToArray< UnalignedLVecBase4d >::__deepcopy__ 0 1 500 0
112
PointerToArray< UnalignedLVecBase4d > PointerToArray< UnalignedLVecBase4d >::__deepcopy__(PyObject *memo) const;

1334 15 ~PointerToArray 0 516 1667 54 PointerToArray< UnalignedLVecBase4d >::~PointerToArray 0 0 0
61
PointerToArray< UnalignedLVecBase4d >::~PointerToArray(void);

1335 14 PointerToArray 0 260 1668 53 PointerToArray< UnalignedLVecBase4f >::PointerToArray 0 3 501 502 503 0
344
inline PointerToArray< UnalignedLVecBase4f >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4f const *)(0))));
inline PointerToArray< UnalignedLVecBase4f >::PointerToArray(PointerToArray< UnalignedLVecBase4f > const &copy);
PointerToArray< UnalignedLVecBase4f >::PointerToArray(PyObject *self, PyObject *source);

1336 11 empty_array 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::empty_array 0 1 504 0
210
static inline PointerToArray< UnalignedLVecBase4f > PointerToArray< UnalignedLVecBase4f >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4f const *)(0))));

1337 5 clear 0 4 1668 44 PointerToArray< UnalignedLVecBase4f >::clear 0 1 505 0
63
inline void PointerToArray< UnalignedLVecBase4f >::clear(void);

1338 4 size 0 4 1668 43 PointerToArray< UnalignedLVecBase4f >::size 0 1 506 0
86
inline unsigned long long int PointerToArray< UnalignedLVecBase4f >::size(void) const;

1339 9 push_back 0 4 1668 48 PointerToArray< UnalignedLVecBase4f >::push_back 0 1 507 0
91
inline void PointerToArray< UnalignedLVecBase4f >::push_back(UnalignedLVecBase4f const &x);

1340 8 pop_back 0 4 1668 47 PointerToArray< UnalignedLVecBase4f >::pop_back 0 1 508 0
66
inline void PointerToArray< UnalignedLVecBase4f >::pop_back(void);

1341 11 get_element 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::get_element 0 1 509 0
117
inline UnalignedLVecBase4f const &PointerToArray< UnalignedLVecBase4f >::get_element(unsigned long long int n) const;

1342 11 set_element 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::set_element 0 1 510 0
123
inline void PointerToArray< UnalignedLVecBase4f >::set_element(unsigned long long int n, UnalignedLVecBase4f const &value);

1343 11 __getitem__ 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::__getitem__ 0 1 511 0
110
UnalignedLVecBase4f const &PointerToArray< UnalignedLVecBase4f >::__getitem__(unsigned long long int n) const;

1344 11 __setitem__ 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::__setitem__ 0 1 512 0
116
void PointerToArray< UnalignedLVecBase4f >::__setitem__(unsigned long long int n, UnalignedLVecBase4f const &value);

1345 8 get_data 0 4 1668 47 PointerToArray< UnalignedLVecBase4f >::get_data 0 1 513 0
70
PyObject *PointerToArray< UnalignedLVecBase4f >::get_data(void) const;

1346 8 set_data 0 4 1668 47 PointerToArray< UnalignedLVecBase4f >::set_data 0 1 514 0
69
void PointerToArray< UnalignedLVecBase4f >::set_data(PyObject *data);

1347 11 get_subdata 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::get_subdata 0 1 515 0
123
PyObject *PointerToArray< UnalignedLVecBase4f >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1348 11 set_subdata 0 4 1668 50 PointerToArray< UnalignedLVecBase4f >::set_subdata 0 1 516 0
158
inline void PointerToArray< UnalignedLVecBase4f >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1349 13 get_ref_count 0 4 1668 52 PointerToArray< UnalignedLVecBase4f >::get_ref_count 0 1 517 0
76
inline int PointerToArray< UnalignedLVecBase4f >::get_ref_count(void) const;

1350 18 get_node_ref_count 0 4 1668 57 PointerToArray< UnalignedLVecBase4f >::get_node_ref_count 0 1 518 0
81
inline int PointerToArray< UnalignedLVecBase4f >::get_node_ref_count(void) const;

1351 5 count 0 4 1668 44 PointerToArray< UnalignedLVecBase4f >::count 0 1 519 0
110
inline unsigned long long int PointerToArray< UnalignedLVecBase4f >::count(UnalignedLVecBase4f const &) const;

1352 10 __reduce__ 0 4 1668 49 PointerToArray< UnalignedLVecBase4f >::__reduce__ 0 1 520 0
82
PyObject *PointerToArray< UnalignedLVecBase4f >::__reduce__(PyObject *self) const;

1353 13 __getbuffer__ 0 4 1668 52 PointerToArray< UnalignedLVecBase4f >::__getbuffer__ 0 1 521 0
101
int PointerToArray< UnalignedLVecBase4f >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1354 17 __releasebuffer__ 0 4 1668 56 PointerToArray< UnalignedLVecBase4f >::__releasebuffer__ 0 1 522 0
101
void PointerToArray< UnalignedLVecBase4f >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1355 12 __deepcopy__ 0 4 1668 51 PointerToArray< UnalignedLVecBase4f >::__deepcopy__ 0 1 523 0
112
PointerToArray< UnalignedLVecBase4f > PointerToArray< UnalignedLVecBase4f >::__deepcopy__(PyObject *memo) const;

1356 15 ~PointerToArray 0 516 1668 54 PointerToArray< UnalignedLVecBase4f >::~PointerToArray 0 0 0
61
PointerToArray< UnalignedLVecBase4f >::~PointerToArray(void);

1357 14 PointerToArray 0 260 1669 53 PointerToArray< UnalignedLVecBase4i >::PointerToArray 0 3 524 525 526 0
344
inline PointerToArray< UnalignedLVecBase4i >::PointerToArray(TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4i const *)(0))));
inline PointerToArray< UnalignedLVecBase4i >::PointerToArray(PointerToArray< UnalignedLVecBase4i > const &copy);
PointerToArray< UnalignedLVecBase4i >::PointerToArray(PyObject *self, PyObject *source);

1358 11 empty_array 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::empty_array 0 1 527 0
210
static inline PointerToArray< UnalignedLVecBase4i > PointerToArray< UnalignedLVecBase4i >::empty_array(unsigned long long int n, TypeHandle type_handle = (::_get_type_handle((UnalignedLVecBase4i const *)(0))));

1359 5 clear 0 4 1669 44 PointerToArray< UnalignedLVecBase4i >::clear 0 1 528 0
63
inline void PointerToArray< UnalignedLVecBase4i >::clear(void);

1360 4 size 0 4 1669 43 PointerToArray< UnalignedLVecBase4i >::size 0 1 529 0
86
inline unsigned long long int PointerToArray< UnalignedLVecBase4i >::size(void) const;

1361 9 push_back 0 4 1669 48 PointerToArray< UnalignedLVecBase4i >::push_back 0 1 530 0
91
inline void PointerToArray< UnalignedLVecBase4i >::push_back(UnalignedLVecBase4i const &x);

1362 8 pop_back 0 4 1669 47 PointerToArray< UnalignedLVecBase4i >::pop_back 0 1 531 0
66
inline void PointerToArray< UnalignedLVecBase4i >::pop_back(void);

1363 11 get_element 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::get_element 0 1 532 0
117
inline UnalignedLVecBase4i const &PointerToArray< UnalignedLVecBase4i >::get_element(unsigned long long int n) const;

1364 11 set_element 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::set_element 0 1 533 0
123
inline void PointerToArray< UnalignedLVecBase4i >::set_element(unsigned long long int n, UnalignedLVecBase4i const &value);

1365 11 __getitem__ 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::__getitem__ 0 1 534 0
110
UnalignedLVecBase4i const &PointerToArray< UnalignedLVecBase4i >::__getitem__(unsigned long long int n) const;

1366 11 __setitem__ 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::__setitem__ 0 1 535 0
116
void PointerToArray< UnalignedLVecBase4i >::__setitem__(unsigned long long int n, UnalignedLVecBase4i const &value);

1367 8 get_data 0 4 1669 47 PointerToArray< UnalignedLVecBase4i >::get_data 0 1 536 0
70
PyObject *PointerToArray< UnalignedLVecBase4i >::get_data(void) const;

1368 8 set_data 0 4 1669 47 PointerToArray< UnalignedLVecBase4i >::set_data 0 1 537 0
69
void PointerToArray< UnalignedLVecBase4i >::set_data(PyObject *data);

1369 11 get_subdata 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::get_subdata 0 1 538 0
123
PyObject *PointerToArray< UnalignedLVecBase4i >::get_subdata(unsigned long long int n, unsigned long long int count) const;

1370 11 set_subdata 0 4 1669 50 PointerToArray< UnalignedLVecBase4i >::set_subdata 0 1 539 0
158
inline void PointerToArray< UnalignedLVecBase4i >::set_subdata(unsigned long long int n, unsigned long long int count, std::basic_string< char > const &data);

1371 13 get_ref_count 0 4 1669 52 PointerToArray< UnalignedLVecBase4i >::get_ref_count 0 1 540 0
76
inline int PointerToArray< UnalignedLVecBase4i >::get_ref_count(void) const;

1372 18 get_node_ref_count 0 4 1669 57 PointerToArray< UnalignedLVecBase4i >::get_node_ref_count 0 1 541 0
81
inline int PointerToArray< UnalignedLVecBase4i >::get_node_ref_count(void) const;

1373 5 count 0 4 1669 44 PointerToArray< UnalignedLVecBase4i >::count 0 1 542 0
110
inline unsigned long long int PointerToArray< UnalignedLVecBase4i >::count(UnalignedLVecBase4i const &) const;

1374 10 __reduce__ 0 4 1669 49 PointerToArray< UnalignedLVecBase4i >::__reduce__ 0 1 543 0
82
PyObject *PointerToArray< UnalignedLVecBase4i >::__reduce__(PyObject *self) const;

1375 13 __getbuffer__ 0 4 1669 52 PointerToArray< UnalignedLVecBase4i >::__getbuffer__ 0 1 544 0
101
int PointerToArray< UnalignedLVecBase4i >::__getbuffer__(PyObject *self, Py_buffer *view, int flags);

1376 17 __releasebuffer__ 0 4 1669 56 PointerToArray< UnalignedLVecBase4i >::__releasebuffer__ 0 1 545 0
101
void PointerToArray< UnalignedLVecBase4i >::__releasebuffer__(PyObject *self, Py_buffer *view) const;

1377 12 __deepcopy__ 0 4 1669 51 PointerToArray< UnalignedLVecBase4i >::__deepcopy__ 0 1 546 0
112
PointerToArray< UnalignedLVecBase4i > PointerToArray< UnalignedLVecBase4i >::__deepcopy__(PyObject *memo) const;

1378 15 ~PointerToArray 0 516 1669 54 PointerToArray< UnalignedLVecBase4i >::~PointerToArray 0 0 0
61
PointerToArray< UnalignedLVecBase4i >::~PointerToArray(void);

1379 9 make_copy 0 6 1670 25 BoundingVolume::make_copy 0 1 547 0
66
virtual BoundingVolume *BoundingVolume::make_copy(void) const = 0;

1380 8 is_empty 0 4 1670 24 BoundingVolume::is_empty 0 1 548 253
/**
 * Any kind of volume might be empty.  This is a degenerate volume that
 * contains no points; it's not the same as, for instance, a sphere with
 * radius zero, since that contains one point (the center).  It intersects
 * with no other volumes.
 */
49
inline bool BoundingVolume::is_empty(void) const;

1381 11 is_infinite 0 4 1670 27 BoundingVolume::is_infinite 0 1 549 536
/**
 * The other side of the empty coin is an infinite volume.  This is a
 * degenerate state of a normally finite volume that contains all points.
 * (Note that some kinds of infinite bounding volumes, like binary separating
 * planes, do not contain all points and thus correctly return is_infinite()
 * == false, even though they are technically infinite.  This is a special
 * case of the word 'infinite' meaning the volume covers all points in space.)
 *
 * It completely intersects with all other volumes except empty volumes.
 */
52
inline bool BoundingVolume::is_infinite(void) const;

1382 12 set_infinite 0 4 1670 28 BoundingVolume::set_infinite 0 1 550 135
/**
 * Marks the volume as infinite, even if it is normally finite.  You can think
 * of this as an infinite extend_by() operation.
 */
47
inline void BoundingVolume::set_infinite(void);

1383 9 extend_by 0 4 1670 25 BoundingVolume::extend_by 0 1 551 72
/**
 * Increases the size of the volume to include the given volume.
 */
65
inline bool BoundingVolume::extend_by(BoundingVolume const *vol);

1384 8 contains 0 4 1670 24 BoundingVolume::contains 0 1 552 128
/**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated volume.
 */
69
inline int BoundingVolume::contains(BoundingVolume const *vol) const;

1385 6 output 0 6 1670 22 BoundingVolume::output 0 1 553 0
65
virtual void BoundingVolume::output(std::ostream &out) const = 0;

1386 5 write 0 6 1670 21 BoundingVolume::write 0 1 554 10
/**
 *
 */
82
virtual void BoundingVolume::write(std::ostream &out, int indent_level = 0) const;

1387 14 get_class_type 0 4 1670 30 BoundingVolume::get_class_type 0 1 555 0
55
static TypeHandle BoundingVolume::get_class_type(void);

1388 15 ~BoundingVolume 0 516 1670 31 BoundingVolume::~BoundingVolume 0 0 0
38
BoundingVolume::~BoundingVolume(void);

1389 9 extend_by 0 4 1674 34 GeometricBoundingVolume::extend_by 0 2 556 557 145
/**
 * Increases the size of the volume to include the given volume.
 */

/**
 * Increases the size of the volume to include the given point.
 */
153
inline bool GeometricBoundingVolume::extend_by(GeometricBoundingVolume const *vol);
inline bool GeometricBoundingVolume::extend_by(LPoint3 const &point);

1390 8 contains 0 4 1674 33 GeometricBoundingVolume::contains 0 3 558 559 560 393
/**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated volume.
 */

/**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated point.
 */

/**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated line segment.
 */
249
inline int GeometricBoundingVolume::contains(GeometricBoundingVolume const *vol) const;
inline int GeometricBoundingVolume::contains(LPoint3 const &point) const;
inline int GeometricBoundingVolume::contains(LPoint3 const &a, LPoint3 const &b) const;

1391 17 get_approx_center 0 6 1674 42 GeometricBoundingVolume::get_approx_center 0 1 561 0
75
virtual LPoint3 GeometricBoundingVolume::get_approx_center(void) const = 0;

1392 5 xform 0 6 1674 30 GeometricBoundingVolume::xform 0 1 562 0
69
virtual void GeometricBoundingVolume::xform(LMatrix4 const &mat) = 0;

1393 14 get_class_type 0 4 1674 39 GeometricBoundingVolume::get_class_type 0 1 563 0
64
static TypeHandle GeometricBoundingVolume::get_class_type(void);

1394 24 ~GeometricBoundingVolume 0 516 1674 49 GeometricBoundingVolume::~GeometricBoundingVolume 0 0 0
56
GeometricBoundingVolume::~GeometricBoundingVolume(void);

1395 7 get_min 0 6 1675 29 FiniteBoundingVolume::get_min 0 1 564 0
62
virtual LPoint3 FiniteBoundingVolume::get_min(void) const = 0;

1396 7 get_max 0 6 1675 29 FiniteBoundingVolume::get_max 0 1 565 0
62
virtual LPoint3 FiniteBoundingVolume::get_max(void) const = 0;

1397 10 get_volume 0 6 1675 32 FiniteBoundingVolume::get_volume 0 1 566 10
/**
 *
 */
65
virtual PN_stdfloat FiniteBoundingVolume::get_volume(void) const;

1398 14 get_class_type 0 4 1675 36 FiniteBoundingVolume::get_class_type 0 1 567 0
61
static TypeHandle FiniteBoundingVolume::get_class_type(void);

1399 21 ~FiniteBoundingVolume 0 516 1675 43 FiniteBoundingVolume::~FiniteBoundingVolume 0 0 0
50
FiniteBoundingVolume::~FiniteBoundingVolume(void);

1400 10 LParabolaf 0 260 1680 22 LParabolaf::LParabolaf 0 3 568 569 570 209
/**
 * Constructs a meaningless degenerate parabola.
 */

/**
 * Constructs a parabola given the three points of the parametric equation:
 * the acceleration, initial velocity, and start point.
 */

/**
 *
 */
185
inline LParabolaf::LParabolaf(void);
inline LParabolaf::LParabolaf(LVecBase3f const &a, LVecBase3f const &b, LVecBase3f const &c);
inline LParabolaf::LParabolaf(LParabolaf const &copy);

1401 10 operator = 0 4 1680 22 LParabolaf::operator = 0 1 571 0
59
inline void LParabolaf::operator =(LParabolaf const &copy);

1402 11 ~LParabolaf 0 516 1680 23 LParabolaf::~LParabolaf 0 0 10
/**
 *
 */
37
inline LParabolaf::~LParabolaf(void);

1403 5 xform 0 4 1680 17 LParabolaf::xform 0 1 572 59
/**
 * Transforms the parabola by the indicated matrix.
 */
45
void LParabolaf::xform(LMatrix4f const &mat);

1404 5 get_a 0 4 1680 17 LParabolaf::get_a 0 1 573 94
/**
 * Returns the first point of the parabola's parametric equation: the
 * acceleration.
 */
55
inline LVecBase3f const &LParabolaf::get_a(void) const;

1405 5 get_b 0 4 1680 17 LParabolaf::get_b 0 1 574 99
/**
 * Returns the second point of the parabola's parametric equation: the initial
 * velocity.
 */
55
inline LVecBase3f const &LParabolaf::get_b(void) const;

1406 5 get_c 0 4 1680 17 LParabolaf::get_c 0 1 575 93
/**
 * Returns the third point of the parabola's parametric equation: the start
 * point.
 */
55
inline LVecBase3f const &LParabolaf::get_c(void) const;

1407 10 calc_point 0 4 1680 22 LParabolaf::calc_point 0 1 576 56
/**
 * Computes the point on the parabola at time t.
 */
54
inline LPoint3f LParabolaf::calc_point(float t) const;

1408 6 output 0 4 1680 18 LParabolaf::output 0 1 577 10
/**
 *
 */
49
void LParabolaf::output(std::ostream &out) const;

1409 5 write 0 4 1680 17 LParabolaf::write 0 1 578 10
/**
 *
 */
70
void LParabolaf::write(std::ostream &out, int indent_level = 0) const;

1410 20 write_datagram_fixed 0 4 1680 32 LParabolaf::write_datagram_fixed 0 1 579 336
/**
 * Writes the parabola to the Datagram using add_float32() or add_float64(),
 * depending on the type of floats in the parabola, regardless of the setting
 * of Datagram::set_stdfloat_double().  This is appropriate when you want to
 * write a fixed-width value to the datagram, especially when you are not
 * writing a bam file.
 */
67
void LParabolaf::write_datagram_fixed(Datagram &destination) const;

1411 19 read_datagram_fixed 0 4 1680 31 LParabolaf::read_datagram_fixed 0 1 580 116
/**
 * Reads the parabola from the Datagram using get_float32() or get_float64().
 * See write_datagram_fixed().
 */
63
void LParabolaf::read_datagram_fixed(DatagramIterator &source);

1412 14 write_datagram 0 4 1680 26 LParabolaf::write_datagram 0 1 581 207
/**
 * Writes the parabola to the Datagram using add_stdfloat().  This is
 * appropriate when you want to write the vector using the standard width
 * setting, especially when you are writing a bam file.
 */
61
void LParabolaf::write_datagram(Datagram &destination) const;

1413 13 read_datagram 0 4 1680 25 LParabolaf::read_datagram 0 1 582 69
/**
 * Reads the parabola from the Datagram using get_stdfloat().
 */
57
void LParabolaf::read_datagram(DatagramIterator &source);

1414 10 LParabolad 0 260 1681 22 LParabolad::LParabolad 0 3 583 584 585 209
/**
 * Constructs a meaningless degenerate parabola.
 */

/**
 * Constructs a parabola given the three points of the parametric equation:
 * the acceleration, initial velocity, and start point.
 */

/**
 *
 */
185
inline LParabolad::LParabolad(void);
inline LParabolad::LParabolad(LVecBase3d const &a, LVecBase3d const &b, LVecBase3d const &c);
inline LParabolad::LParabolad(LParabolad const &copy);

1415 10 operator = 0 4 1681 22 LParabolad::operator = 0 1 586 0
59
inline void LParabolad::operator =(LParabolad const &copy);

1416 11 ~LParabolad 0 516 1681 23 LParabolad::~LParabolad 0 0 10
/**
 *
 */
37
inline LParabolad::~LParabolad(void);

1417 5 xform 0 4 1681 17 LParabolad::xform 0 1 587 59
/**
 * Transforms the parabola by the indicated matrix.
 */
45
void LParabolad::xform(LMatrix4d const &mat);

1418 5 get_a 0 4 1681 17 LParabolad::get_a 0 1 588 94
/**
 * Returns the first point of the parabola's parametric equation: the
 * acceleration.
 */
55
inline LVecBase3d const &LParabolad::get_a(void) const;

1419 5 get_b 0 4 1681 17 LParabolad::get_b 0 1 589 99
/**
 * Returns the second point of the parabola's parametric equation: the initial
 * velocity.
 */
55
inline LVecBase3d const &LParabolad::get_b(void) const;

1420 5 get_c 0 4 1681 17 LParabolad::get_c 0 1 590 93
/**
 * Returns the third point of the parabola's parametric equation: the start
 * point.
 */
55
inline LVecBase3d const &LParabolad::get_c(void) const;

1421 10 calc_point 0 4 1681 22 LParabolad::calc_point 0 1 591 56
/**
 * Computes the point on the parabola at time t.
 */
55
inline LPoint3d LParabolad::calc_point(double t) const;

1422 6 output 0 4 1681 18 LParabolad::output 0 1 592 10
/**
 *
 */
49
void LParabolad::output(std::ostream &out) const;

1423 5 write 0 4 1681 17 LParabolad::write 0 1 593 10
/**
 *
 */
70
void LParabolad::write(std::ostream &out, int indent_level = 0) const;

1424 20 write_datagram_fixed 0 4 1681 32 LParabolad::write_datagram_fixed 0 1 594 336
/**
 * Writes the parabola to the Datagram using add_float32() or add_float64(),
 * depending on the type of floats in the parabola, regardless of the setting
 * of Datagram::set_stdfloat_double().  This is appropriate when you want to
 * write a fixed-width value to the datagram, especially when you are not
 * writing a bam file.
 */
67
void LParabolad::write_datagram_fixed(Datagram &destination) const;

1425 19 read_datagram_fixed 0 4 1681 31 LParabolad::read_datagram_fixed 0 1 595 116
/**
 * Reads the parabola from the Datagram using get_float32() or get_float64().
 * See write_datagram_fixed().
 */
63
void LParabolad::read_datagram_fixed(DatagramIterator &source);

1426 14 write_datagram 0 4 1681 26 LParabolad::write_datagram 0 1 596 207
/**
 * Writes the parabola to the Datagram using add_stdfloat().  This is
 * appropriate when you want to write the vector using the standard width
 * setting, especially when you are writing a bam file.
 */
61
void LParabolad::write_datagram(Datagram &destination) const;

1427 13 read_datagram 0 4 1681 25 LParabolad::read_datagram 0 1 597 69
/**
 * Reads the parabola from the Datagram using get_stdfloat().
 */
57
void LParabolad::read_datagram(DatagramIterator &source);

1428 7 LPlanef 0 260 1683 16 LPlanef::LPlanef 0 6 598 599 600 601 602 603 518
/**
 * Creates a default plane.  This plane happens to intersect the origin,
 * perpendicular to the Z axis.  It's not clear how useful a default plane is.
 */

/**
 *
 */

/**
 * Constructs a plane given three counter-clockwise points, as seen from the
 * front of the plane (that is, viewed from the end of the normal vector,
 * looking down).
 */

/**
 * Constructs a plane given a surface normal vector and a point within the
 * plane.
 */

/**
 * Constructs a plane given the four terms of the plane equation.
 */
347
inline LPlanef::LPlanef(void);
inline LPlanef::LPlanef(LVecBase4f const &copy);
inline LPlanef::LPlanef(LPoint3f const &a, LPoint3f const &b, LPoint3f const &c);
inline LPlanef::LPlanef(LVector3f const &normal, LPoint3f const &point);
inline LPlanef::LPlanef(float a, float b, float c, float d);
inline LPlanef::LPlanef(LPlanef const &) = default;

1429 10 operator * 0 4 1683 19 LPlanef::operator * 0 2 604 605 0
127
inline LPlanef LPlanef::operator *(LMatrix3f const &mat) const;
inline LPlanef LPlanef::operator *(LMatrix4f const &mat) const;

1430 11 operator *= 0 4 1683 20 LPlanef::operator *= 0 1 606 0
55
inline void LPlanef::operator *=(LMatrix4f const &mat);

1431 5 xform 0 4 1683 14 LPlanef::xform 0 1 607 56
/**
 * Transforms the plane by the indicated matrix.
 */
49
inline void LPlanef::xform(LMatrix4f const &mat);

1432 10 operator - 0 68 1683 19 LPlanef::operator - 0 1 608 0
47
inline LPlanef LPlanef::operator -(void) const;

1433 18 get_reflection_mat 0 4 1683 27 LPlanef::get_reflection_mat 0 1 609 121
/**
 * This computes a transform matrix that reflects the universe to the other
 * side of the plane, as in a mirror.
 */
50
LMatrix4f LPlanef::get_reflection_mat(void) const;

1434 10 get_normal 0 4 1683 19 LPlanef::get_normal 0 1 610 51
/**
 * Returns the surface normal of the plane.
 */
49
inline LVector3f LPlanef::get_normal(void) const;

1435 9 get_point 0 4 1683 18 LPlanef::get_point 0 1 611 145
/**
 * Returns an arbitrary point in the plane.  This can be used along with the
 * normal returned by get_normal() to reconstruct the plane.
 */
40
LPoint3f LPlanef::get_point(void) const;

1436 13 dist_to_plane 0 4 1683 22 LPlanef::dist_to_plane 0 1 612 328
/**
 * Returns the straight-line shortest distance from the point to the plane.
 * The returned value is positive if the point is in front of the plane (on
 * the side with the normal), or negative in the point is behind the plane (on
 * the opposite side from the normal). It's zero if the point is exactly in
 * the plane.
 */
65
inline float LPlanef::dist_to_plane(LPoint3f const &point) const;

1437 9 normalize 0 4 1683 18 LPlanef::normalize 0 1 613 139
/**
 * Normalizes the plane in place.  Returns true if the plane was normalized,
 * false if the plane had a zero-length normal vector.
 */
37
inline bool LPlanef::normalize(void);

1438 10 normalized 0 4 1683 19 LPlanef::normalized 0 1 614 155
/**
 * Normalizes the plane and returns the normalized plane as a copy.  If the
 * plane's normal was a zero-length vector, the same plane is returned.
 */
47
inline LPlanef LPlanef::normalized(void) const;

1439 7 project 0 4 1683 16 LPlanef::project 0 1 615 86
/**
 * Returns the point within the plane nearest to the indicated point in space.
 */
62
inline LPoint3f LPlanef::project(LPoint3f const &point) const;

1440 4 flip 0 4 1683 13 LPlanef::flip 0 1 616 115
/**
 * Convenience method that flips the plane in-place.  This is done by simply
 * flipping the normal vector.
 */
32
inline void LPlanef::flip(void);

1441 15 intersects_line 0 4 1683 24 LPlanef::intersects_line 0 1 617 968
/**
 * Returns true if the plane intersects the infinite line passing through
 * points p1 and p2, false if the line is parallel.  The points p1 and p2 are
 * used only to define the Euclidean line; they have no other bearing on the
 * intersection test.  If true, sets intersection_point to the point of
 * intersection.
 */

/**
 * This flavor of intersects_line() returns a bit more information about the
 * nature of the intersecting point.  The line is defined via the parametric
 * equation from + t * delta for all real values of t.
 *
 * If there is no intersection with the plane, the function returns false and
 * leaves t undefined.  If there is an intersection with the plane, the
 * function returns true and sets t to the parametric value that defines the
 * point of intersection.  That is, t == 0.0f implies that the intersection
 * occurred exactly at point from, and t == 1.0f implies at point from +
 * delta, with other values of t accordingly.
 */
213
inline bool LPlanef::intersects_line(LPoint3f &intersection_point, LPoint3f const &p1, LPoint3f const &p2) const;
inline bool LPlanef::intersects_line(float &t, LPoint3f const &from, LVector3f const &delta) const;

1442 16 intersects_plane 0 4 1683 25 LPlanef::intersects_plane 0 1 618 300
/**
 * Returns true if the two planes intersect, false if they do not.  If they do
 * intersect, then from and delta are filled in with the parametric
 * representation of the line of intersection: that is, from is a point on
 * that line, and delta is a vector showing the direction of the line.
 */
93
bool LPlanef::intersects_plane(LPoint3f &from, LVector3f &delta, LPlanef const &other) const;

1443 19 intersects_parabola 0 4 1683 28 LPlanef::intersects_parabola 0 0 437
/**
 * Determines whether and where the indicated parabola intersects with the
 * plane.
 *
 * If there is no intersection with the plane, the function returns false and
 * leaves t1 and t2 undefined.  If there is an intersection with the plane,
 * the function returns true and sets t1 and t2 to the parametric value that
 * defines the two points of intersection.  If the parabola is exactly tangent
 * to the plane, then t1 == t2.
 */
90
bool LPlanef::intersects_parabola(float &t1, float &t2, LParabolaf const &parabola) const;

1444 6 output 0 4 1683 15 LPlanef::output 0 1 619 10
/**
 *
 */
46
void LPlanef::output(std::ostream &out) const;

1445 5 write 0 4 1683 14 LPlanef::write 0 1 620 10
/**
 *
 */
67
void LPlanef::write(std::ostream &out, int indent_level = 0) const;

1446 8 __repr__ 0 4 1683 17 LPlanef::__repr__ 0 1 621 58
/**
 * Returns a string representation of this LPlane.
 */
42
std::string LPlanef::__repr__(void) const;

1447 8 ~LPlanef 0 516 1683 17 LPlanef::~LPlanef 0 0 0
24
LPlanef::~LPlanef(void);

1448 7 LPlaned 0 260 1685 16 LPlaned::LPlaned 0 6 622 623 624 625 626 627 518
/**
 * Creates a default plane.  This plane happens to intersect the origin,
 * perpendicular to the Z axis.  It's not clear how useful a default plane is.
 */

/**
 *
 */

/**
 * Constructs a plane given three counter-clockwise points, as seen from the
 * front of the plane (that is, viewed from the end of the normal vector,
 * looking down).
 */

/**
 * Constructs a plane given a surface normal vector and a point within the
 * plane.
 */

/**
 * Constructs a plane given the four terms of the plane equation.
 */
351
inline LPlaned::LPlaned(void);
inline LPlaned::LPlaned(LVecBase4d const &copy);
inline LPlaned::LPlaned(LPoint3d const &a, LPoint3d const &b, LPoint3d const &c);
inline LPlaned::LPlaned(LVector3d const &normal, LPoint3d const &point);
inline LPlaned::LPlaned(double a, double b, double c, double d);
inline LPlaned::LPlaned(LPlaned const &) = default;

1449 10 operator * 0 4 1685 19 LPlaned::operator * 0 2 628 629 0
127
inline LPlaned LPlaned::operator *(LMatrix3d const &mat) const;
inline LPlaned LPlaned::operator *(LMatrix4d const &mat) const;

1450 11 operator *= 0 4 1685 20 LPlaned::operator *= 0 1 630 0
55
inline void LPlaned::operator *=(LMatrix4d const &mat);

1451 5 xform 0 4 1685 14 LPlaned::xform 0 1 631 56
/**
 * Transforms the plane by the indicated matrix.
 */
49
inline void LPlaned::xform(LMatrix4d const &mat);

1452 10 operator - 0 68 1685 19 LPlaned::operator - 0 1 632 0
47
inline LPlaned LPlaned::operator -(void) const;

1453 18 get_reflection_mat 0 4 1685 27 LPlaned::get_reflection_mat 0 1 633 121
/**
 * This computes a transform matrix that reflects the universe to the other
 * side of the plane, as in a mirror.
 */
50
LMatrix4d LPlaned::get_reflection_mat(void) const;

1454 10 get_normal 0 4 1685 19 LPlaned::get_normal 0 1 634 51
/**
 * Returns the surface normal of the plane.
 */
49
inline LVector3d LPlaned::get_normal(void) const;

1455 9 get_point 0 4 1685 18 LPlaned::get_point 0 1 635 145
/**
 * Returns an arbitrary point in the plane.  This can be used along with the
 * normal returned by get_normal() to reconstruct the plane.
 */
40
LPoint3d LPlaned::get_point(void) const;

1456 13 dist_to_plane 0 4 1685 22 LPlaned::dist_to_plane 0 1 636 328
/**
 * Returns the straight-line shortest distance from the point to the plane.
 * The returned value is positive if the point is in front of the plane (on
 * the side with the normal), or negative in the point is behind the plane (on
 * the opposite side from the normal). It's zero if the point is exactly in
 * the plane.
 */
66
inline double LPlaned::dist_to_plane(LPoint3d const &point) const;

1457 9 normalize 0 4 1685 18 LPlaned::normalize 0 1 637 139
/**
 * Normalizes the plane in place.  Returns true if the plane was normalized,
 * false if the plane had a zero-length normal vector.
 */
37
inline bool LPlaned::normalize(void);

1458 10 normalized 0 4 1685 19 LPlaned::normalized 0 1 638 155
/**
 * Normalizes the plane and returns the normalized plane as a copy.  If the
 * plane's normal was a zero-length vector, the same plane is returned.
 */
47
inline LPlaned LPlaned::normalized(void) const;

1459 7 project 0 4 1685 16 LPlaned::project 0 1 639 86
/**
 * Returns the point within the plane nearest to the indicated point in space.
 */
62
inline LPoint3d LPlaned::project(LPoint3d const &point) const;

1460 4 flip 0 4 1685 13 LPlaned::flip 0 1 640 115
/**
 * Convenience method that flips the plane in-place.  This is done by simply
 * flipping the normal vector.
 */
32
inline void LPlaned::flip(void);

1461 15 intersects_line 0 4 1685 24 LPlaned::intersects_line 0 1 641 968
/**
 * Returns true if the plane intersects the infinite line passing through
 * points p1 and p2, false if the line is parallel.  The points p1 and p2 are
 * used only to define the Euclidean line; they have no other bearing on the
 * intersection test.  If true, sets intersection_point to the point of
 * intersection.
 */

/**
 * This flavor of intersects_line() returns a bit more information about the
 * nature of the intersecting point.  The line is defined via the parametric
 * equation from + t * delta for all real values of t.
 *
 * If there is no intersection with the plane, the function returns false and
 * leaves t undefined.  If there is an intersection with the plane, the
 * function returns true and sets t to the parametric value that defines the
 * point of intersection.  That is, t == 0.0f implies that the intersection
 * occurred exactly at point from, and t == 1.0f implies at point from +
 * delta, with other values of t accordingly.
 */
214
inline bool LPlaned::intersects_line(LPoint3d &intersection_point, LPoint3d const &p1, LPoint3d const &p2) const;
inline bool LPlaned::intersects_line(double &t, LPoint3d const &from, LVector3d const &delta) const;

1462 16 intersects_plane 0 4 1685 25 LPlaned::intersects_plane 0 1 642 300
/**
 * Returns true if the two planes intersect, false if they do not.  If they do
 * intersect, then from and delta are filled in with the parametric
 * representation of the line of intersection: that is, from is a point on
 * that line, and delta is a vector showing the direction of the line.
 */
93
bool LPlaned::intersects_plane(LPoint3d &from, LVector3d &delta, LPlaned const &other) const;

1463 19 intersects_parabola 0 4 1685 28 LPlaned::intersects_parabola 0 0 437
/**
 * Determines whether and where the indicated parabola intersects with the
 * plane.
 *
 * If there is no intersection with the plane, the function returns false and
 * leaves t1 and t2 undefined.  If there is an intersection with the plane,
 * the function returns true and sets t1 and t2 to the parametric value that
 * defines the two points of intersection.  If the parabola is exactly tangent
 * to the plane, then t1 == t2.
 */
92
bool LPlaned::intersects_parabola(double &t1, double &t2, LParabolad const &parabola) const;

1464 6 output 0 4 1685 15 LPlaned::output 0 1 643 10
/**
 *
 */
46
void LPlaned::output(std::ostream &out) const;

1465 5 write 0 4 1685 14 LPlaned::write 0 1 644 10
/**
 *
 */
67
void LPlaned::write(std::ostream &out, int indent_level = 0) const;

1466 8 __repr__ 0 4 1685 17 LPlaned::__repr__ 0 1 645 58
/**
 * Returns a string representation of this LPlane.
 */
42
std::string LPlaned::__repr__(void) const;

1467 8 ~LPlaned 0 516 1685 17 LPlaned::~LPlaned 0 0 0
24
LPlaned::~LPlaned(void);

1468 11 BoundingBox 0 260 1691 24 BoundingBox::BoundingBox 0 2 646 647 88
/**
 * Constructs an empty box object.
 */

/**
 * Constructs a specific box object.
 */
120
inline BoundingBox::BoundingBox(void);
inline explicit BoundingBox::BoundingBox(LPoint3 const &min, LPoint3 const &max);

1469 12 operator new 0 4 1691 25 BoundingBox::operator new 0 1 648 0
126
inline void *BoundingBox::operator new(std::size_t size);
inline void *BoundingBox::operator new(std::size_t size, void *ptr);

1470 15 operator delete 0 4 1691 28 BoundingBox::operator delete 0 0 0
110
inline void BoundingBox::operator delete(void *ptr);
inline void BoundingBox::operator delete(void *, void *);

1471 12 validate_ptr 0 4 1691 25 BoundingBox::validate_ptr 0 0 0
62
static inline bool BoundingBox::validate_ptr(void const *ptr);

1472 14 get_num_points 0 4 1691 27 BoundingBox::get_num_points 0 1 649 68
/**
 * Returns 8: the number of vertices of a rectangular solid.
 */
51
inline int BoundingBox::get_num_points(void) const;

1473 9 get_point 0 4 1691 22 BoundingBox::get_point 0 1 650 59
/**
 * Returns the nth vertex of the rectangular solid.
 */
51
inline LPoint3 BoundingBox::get_point(int n) const;

1474 14 get_num_planes 0 4 1691 27 BoundingBox::get_num_planes 0 1 651 65
/**
 * Returns 6: the number of faces of a rectangular solid.
 */
51
inline int BoundingBox::get_num_planes(void) const;

1475 9 get_plane 0 4 1691 22 BoundingBox::get_plane 0 1 652 57
/**
 * Returns the nth face of the rectangular solid.
 */
50
inline LPlane BoundingBox::get_plane(int n) const;

1476 11 set_min_max 0 4 1691 24 BoundingBox::set_min_max 0 1 653 63
/**
 * Sets the min and max point of the rectangular solid.
 */
77
inline void BoundingBox::set_min_max(LPoint3 const &min, LPoint3 const &max);

1477 14 get_class_type 0 4 1691 27 BoundingBox::get_class_type 0 1 654 0
52
static TypeHandle BoundingBox::get_class_type(void);

1478 12 ~BoundingBox 0 516 1691 25 BoundingBox::~BoundingBox 0 0 0
32
BoundingBox::~BoundingBox(void);

1479 9 LFrustumf 0 260 1692 20 LFrustumf::LFrustumf 0 2 655 656 10
/**
 *
 */
92
inline LFrustumf::LFrustumf(void);
inline LFrustumf::LFrustumf(LFrustumf const &) = default;

1480 13 make_ortho_2D 0 4 1692 24 LFrustumf::make_ortho_2D 0 2 657 658 116
/**
 * Sets up a two-dimensional orthographic frustum
 */

/**
 * Sets up a two-dimensional orthographic frustum
 */
117
inline void LFrustumf::make_ortho_2D(void);
inline void LFrustumf::make_ortho_2D(float l, float r, float t, float b);

1481 10 make_ortho 0 4 1692 21 LFrustumf::make_ortho 0 2 659 660 66
/**
 * Behaves like gluOrtho
 */

/**
 * Behaves like gluOrtho
 */
155
inline void LFrustumf::make_ortho(float fnear, float ffar);
inline void LFrustumf::make_ortho(float fnear, float ffar, float l, float r, float t, float b);

1482 21 make_perspective_hfov 0 4 1692 32 LFrustumf::make_perspective_hfov 0 1 661 79
/**
 * Behaves like gluPerspective (Aspect = width/height, Yfov in degrees)
 */
96
inline void LFrustumf::make_perspective_hfov(float xfov, float aspect, float fnear, float ffar);

1483 21 make_perspective_vfov 0 4 1692 32 LFrustumf::make_perspective_vfov 0 1 662 10
/**
 *
 */
96
inline void LFrustumf::make_perspective_vfov(float yfov, float aspect, float fnear, float ffar);

1484 16 make_perspective 0 4 1692 27 LFrustumf::make_perspective 0 1 663 10
/**
 *
 */
89
inline void LFrustumf::make_perspective(float xfov, float yfov, float fnear, float ffar);

1485 22 get_perspective_params 0 4 1692 33 LFrustumf::get_perspective_params 0 0 22
/**
 *
 */

/**
 *
 */
228
inline void LFrustumf::get_perspective_params(float &yfov, float &aspect, float &fnear, float &ffar) const;
inline void LFrustumf::get_perspective_params(float &xfov, float &yfov, float &aspect, float &fnear, float &ffar) const;

1486 10 ~LFrustumf 0 516 1692 21 LFrustumf::~LFrustumf 0 0 0
28
LFrustumf::~LFrustumf(void);

1487 9 LFrustumd 0 260 1693 20 LFrustumd::LFrustumd 0 2 664 665 10
/**
 *
 */
92
inline LFrustumd::LFrustumd(void);
inline LFrustumd::LFrustumd(LFrustumd const &) = default;

1488 13 make_ortho_2D 0 4 1693 24 LFrustumd::make_ortho_2D 0 2 666 667 116
/**
 * Sets up a two-dimensional orthographic frustum
 */

/**
 * Sets up a two-dimensional orthographic frustum
 */
121
inline void LFrustumd::make_ortho_2D(void);
inline void LFrustumd::make_ortho_2D(double l, double r, double t, double b);

1489 10 make_ortho 0 4 1693 21 LFrustumd::make_ortho 0 2 668 669 66
/**
 * Behaves like gluOrtho
 */

/**
 * Behaves like gluOrtho
 */
163
inline void LFrustumd::make_ortho(double fnear, double ffar);
inline void LFrustumd::make_ortho(double fnear, double ffar, double l, double r, double t, double b);

1490 21 make_perspective_hfov 0 4 1693 32 LFrustumd::make_perspective_hfov 0 1 670 79
/**
 * Behaves like gluPerspective (Aspect = width/height, Yfov in degrees)
 */
100
inline void LFrustumd::make_perspective_hfov(double xfov, double aspect, double fnear, double ffar);

1491 21 make_perspective_vfov 0 4 1693 32 LFrustumd::make_perspective_vfov 0 1 671 10
/**
 *
 */
100
inline void LFrustumd::make_perspective_vfov(double yfov, double aspect, double fnear, double ffar);

1492 16 make_perspective 0 4 1693 27 LFrustumd::make_perspective 0 1 672 10
/**
 *
 */
93
inline void LFrustumd::make_perspective(double xfov, double yfov, double fnear, double ffar);

1493 22 get_perspective_params 0 4 1693 33 LFrustumd::get_perspective_params 0 0 22
/**
 *
 */

/**
 *
 */
237
inline void LFrustumd::get_perspective_params(double &yfov, double &aspect, double &fnear, double &ffar) const;
inline void LFrustumd::get_perspective_params(double &xfov, double &yfov, double &aspect, double &fnear, double &ffar) const;

1494 10 ~LFrustumd 0 516 1693 21 LFrustumd::~LFrustumd 0 0 0
28
LFrustumd::~LFrustumd(void);

1495 18 BoundingHexahedron 0 260 1698 38 BoundingHexahedron::BoundingHexahedron 0 2 673 674 34
/**
 *
 */

/**
 *
 */

/**
 *
 */
315
BoundingHexahedron::BoundingHexahedron(LFrustum const &frustum, bool is_ortho, CoordinateSystem cs = ::CS_default);
BoundingHexahedron::BoundingHexahedron(LPoint3 const &fll, LPoint3 const &flr, LPoint3 const &fur, LPoint3 const &ful, LPoint3 const &nll, LPoint3 const &nlr, LPoint3 const &nur, LPoint3 const &nul);

1496 14 get_num_points 0 4 1698 34 BoundingHexahedron::get_num_points 0 1 675 61
/**
 * Returns 8: the number of vertices of a hexahedron.
 */
58
inline int BoundingHexahedron::get_num_points(void) const;

1497 9 get_point 0 4 1698 29 BoundingHexahedron::get_point 0 1 676 52
/**
 * Returns the nth vertex of the hexahedron.
 */
58
inline LPoint3 BoundingHexahedron::get_point(int n) const;

1498 14 get_num_planes 0 4 1698 34 BoundingHexahedron::get_num_planes 0 1 677 58
/**
 * Returns 6: the number of faces of a hexahedron.
 */
58
inline int BoundingHexahedron::get_num_planes(void) const;

1499 9 get_plane 0 4 1698 29 BoundingHexahedron::get_plane 0 1 678 50
/**
 * Returns the nth face of the hexahedron.
 */
57
inline LPlane BoundingHexahedron::get_plane(int n) const;

1500 14 get_class_type 0 4 1698 34 BoundingHexahedron::get_class_type 0 1 679 0
59
static TypeHandle BoundingHexahedron::get_class_type(void);

1501 19 ~BoundingHexahedron 0 516 1698 39 BoundingHexahedron::~BoundingHexahedron 0 0 0
46
BoundingHexahedron::~BoundingHexahedron(void);

1502 12 BoundingLine 0 260 1699 26 BoundingLine::BoundingLine 0 1 680 22
/**
 *
 */

/**
 *
 */
79
inline explicit BoundingLine::BoundingLine(LPoint3 const &a, LPoint3 const &b);

1503 12 operator new 0 4 1699 26 BoundingLine::operator new 0 1 681 0
128
inline void *BoundingLine::operator new(std::size_t size);
inline void *BoundingLine::operator new(std::size_t size, void *ptr);

1504 15 operator delete 0 4 1699 29 BoundingLine::operator delete 0 0 0
112
inline void BoundingLine::operator delete(void *ptr);
inline void BoundingLine::operator delete(void *, void *);

1505 12 validate_ptr 0 4 1699 26 BoundingLine::validate_ptr 0 0 0
63
static inline bool BoundingLine::validate_ptr(void const *ptr);

1506 11 get_point_a 0 4 1699 25 BoundingLine::get_point_a 0 1 682 57
/**
 * Returns the first point that defines the line.
 */
60
inline LPoint3 const &BoundingLine::get_point_a(void) const;

1507 11 get_point_b 0 4 1699 25 BoundingLine::get_point_b 0 1 683 58
/**
 * Returns the second point that defines the line.
 */
53
inline LPoint3 BoundingLine::get_point_b(void) const;

1508 14 get_class_type 0 4 1699 28 BoundingLine::get_class_type 0 1 684 0
53
static TypeHandle BoundingLine::get_class_type(void);

1509 13 ~BoundingLine 0 516 1699 27 BoundingLine::~BoundingLine 0 0 0
34
BoundingLine::~BoundingLine(void);

1510 13 BoundingPlane 0 260 1700 28 BoundingPlane::BoundingPlane 0 2 685 686 77
/**
 * Constructs an empty "plane" that has no intersections.
 */

/**
 *
 */
100
inline BoundingPlane::BoundingPlane(void);
inline BoundingPlane::BoundingPlane(LPlane const &plane);

1511 12 operator new 0 4 1700 27 BoundingPlane::operator new 0 1 687 0
130
inline void *BoundingPlane::operator new(std::size_t size);
inline void *BoundingPlane::operator new(std::size_t size, void *ptr);

1512 15 operator delete 0 4 1700 30 BoundingPlane::operator delete 0 0 0
114
inline void BoundingPlane::operator delete(void *ptr);
inline void BoundingPlane::operator delete(void *, void *);

1513 12 validate_ptr 0 4 1700 27 BoundingPlane::validate_ptr 0 0 0
64
static inline bool BoundingPlane::validate_ptr(void const *ptr);

1514 9 get_plane 0 4 1700 24 BoundingPlane::get_plane 0 1 688 10
/**
 *
 */
58
inline LPlane const &BoundingPlane::get_plane(void) const;

1515 14 get_class_type 0 4 1700 29 BoundingPlane::get_class_type 0 1 689 0
54
static TypeHandle BoundingPlane::get_class_type(void);

1516 14 ~BoundingPlane 0 516 1700 29 BoundingPlane::~BoundingPlane 0 0 0
36
BoundingPlane::~BoundingPlane(void);

1517 14 BoundingSphere 0 260 1702 30 BoundingSphere::BoundingSphere 0 2 690 691 80
/**
 * Constructs an empty sphere.
 */

/**
 * Constructs a specific sphere.
 */
135
inline BoundingSphere::BoundingSphere(void);
inline explicit BoundingSphere::BoundingSphere(LPoint3 const &center, PN_stdfloat radius);

1518 12 operator new 0 4 1702 28 BoundingSphere::operator new 0 1 692 0
132
inline void *BoundingSphere::operator new(std::size_t size);
inline void *BoundingSphere::operator new(std::size_t size, void *ptr);

1519 15 operator delete 0 4 1702 31 BoundingSphere::operator delete 0 0 0
116
inline void BoundingSphere::operator delete(void *ptr);
inline void BoundingSphere::operator delete(void *, void *);

1520 12 validate_ptr 0 4 1702 28 BoundingSphere::validate_ptr 0 0 0
65
static inline bool BoundingSphere::validate_ptr(void const *ptr);

1521 10 get_center 0 4 1702 26 BoundingSphere::get_center 0 1 693 10
/**
 *
 */
54
inline LPoint3 BoundingSphere::get_center(void) const;

1522 10 get_radius 0 4 1702 26 BoundingSphere::get_radius 0 1 694 10
/**
 *
 */
58
inline PN_stdfloat BoundingSphere::get_radius(void) const;

1523 10 set_center 0 4 1702 26 BoundingSphere::set_center 0 1 695 47
/**
 * Sets the center point of the sphere.
 */
62
inline void BoundingSphere::set_center(LPoint3 const &center);

1524 10 set_radius 0 4 1702 26 BoundingSphere::set_radius 0 1 696 41
/**
 * Sets the radius of the sphere.
 */
59
inline void BoundingSphere::set_radius(PN_stdfloat radius);

1525 14 get_class_type 0 4 1702 30 BoundingSphere::get_class_type 0 1 697 0
55
static TypeHandle BoundingSphere::get_class_type(void);

1526 15 ~BoundingSphere 0 516 1702 31 BoundingSphere::~BoundingSphere 0 0 0
38
BoundingSphere::~BoundingSphere(void);

1527 26 IntersectionBoundingVolume 0 260 1703 54 IntersectionBoundingVolume::IntersectionBoundingVolume 0 1 698 56
/**
 * Constructs an empty intersection.
 */

/**
 *
 */
68
inline IntersectionBoundingVolume::IntersectionBoundingVolume(void);

1528 12 operator new 0 4 1703 40 IntersectionBoundingVolume::operator new 0 1 699 0
156
inline void *IntersectionBoundingVolume::operator new(std::size_t size);
inline void *IntersectionBoundingVolume::operator new(std::size_t size, void *ptr);

1529 15 operator delete 0 4 1703 43 IntersectionBoundingVolume::operator delete 0 0 0
140
inline void IntersectionBoundingVolume::operator delete(void *ptr);
inline void IntersectionBoundingVolume::operator delete(void *, void *);

1530 12 validate_ptr 0 4 1703 40 IntersectionBoundingVolume::validate_ptr 0 0 0
77
static inline bool IntersectionBoundingVolume::validate_ptr(void const *ptr);

1531 18 get_num_components 0 4 1703 46 IntersectionBoundingVolume::get_num_components 0 1 700 64
/**
 * Returns the number of components in the intersection.
 */
70
inline int IntersectionBoundingVolume::get_num_components(void) const;

1532 13 get_component 0 4 1703 41 IntersectionBoundingVolume::get_component 0 1 701 57
/**
 * Returns the nth component in the intersection.
 */
93
inline GeometricBoundingVolume const *IntersectionBoundingVolume::get_component(int n) const;

1533 16 clear_components 0 4 1703 44 IntersectionBoundingVolume::clear_components 0 1 702 50
/**
 * Removes all components from the volume.
 */
56
void IntersectionBoundingVolume::clear_components(void);

1534 13 add_component 0 4 1703 41 IntersectionBoundingVolume::add_component 0 1 703 373
/**
 * Adds a new component to the volume.  This does not necessarily increase the
 * total number of components by one, and you may or may not be able to find
 * this component in the volume by a subsequent call to get_component();
 * certain optimizations may prevent the component from being added, or have
 * other unexpected effects on the total set of components.
 */
89
void IntersectionBoundingVolume::add_component(GeometricBoundingVolume const *component);

1535 14 get_class_type 0 4 1703 42 IntersectionBoundingVolume::get_class_type 0 1 704 0
67
static TypeHandle IntersectionBoundingVolume::get_class_type(void);

1536 27 ~IntersectionBoundingVolume 0 516 1703 55 IntersectionBoundingVolume::~IntersectionBoundingVolume 0 0 0
62
IntersectionBoundingVolume::~IntersectionBoundingVolume(void);

1537 8 heads_up 0 1 0 8 heads_up 0 12 805 806 807 808 809 810 811 812 813 814 815 816 0
1297
void heads_up(LMatrix3f &mat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LMatrix3f &mat, LVector3f const &fwd, CoordinateSystem cs);
inline void heads_up(LMatrix4f &mat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LMatrix4f &mat, LVector3f const &fwd, CoordinateSystem cs);
inline void heads_up(LQuaternionf &quat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LQuaternionf &quat, LVector3f const &fwd, CoordinateSystem cs);
void heads_up(LMatrix3d &mat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LMatrix3d &mat, LVector3d const &fwd, CoordinateSystem cs);
inline void heads_up(LMatrix4d &mat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LMatrix4d &mat, LVector3d const &fwd, CoordinateSystem cs);
inline void heads_up(LQuaterniond &quat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void heads_up(LQuaterniond &quat, LVector3d const &fwd, CoordinateSystem cs);

1538 7 look_at 0 1 0 7 look_at 0 12 817 818 819 820 821 822 823 824 825 826 827 828 0
1285
void look_at(LMatrix3f &mat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LMatrix3f &mat, LVector3f const &fwd, CoordinateSystem cs);
inline void look_at(LMatrix4f &mat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LMatrix4f &mat, LVector3f const &fwd, CoordinateSystem cs);
inline void look_at(LQuaternionf &quat, LVector3f const &fwd, LVector3f const &up = LVector3f::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LQuaternionf &quat, LVector3f const &fwd, CoordinateSystem cs);
void look_at(LMatrix3d &mat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LMatrix3d &mat, LVector3d const &fwd, CoordinateSystem cs);
inline void look_at(LMatrix4d &mat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LMatrix4d &mat, LVector3d const &fwd, CoordinateSystem cs);
inline void look_at(LQuaterniond &quat, LVector3d const &fwd, LVector3d const &up = LVector3d::up(), CoordinateSystem cs = ::CS_default);
inline void look_at(LQuaterniond &quat, LVector3d const &fwd, CoordinateSystem cs);

1539 8 Mersenne 0 260 1706 18 Mersenne::Mersenne 0 2 705 706 35
/* initializes mt[N] with a seed */
107
explicit Mersenne::Mersenne(unsigned long int seed);
inline Mersenne::Mersenne(Mersenne const &) = default;

1540 10 get_uint31 0 4 1706 20 Mersenne::get_uint31 0 1 707 58
/* generates a random number on [0,0x7fffffff]-interval */
45
unsigned long int Mersenne::get_uint31(void);

1541 9 ~Mersenne 0 516 1706 19 Mersenne::~Mersenne 0 0 0
26
Mersenne::~Mersenne(void);

1542 18 OmniBoundingVolume 0 260 1708 38 OmniBoundingVolume::OmniBoundingVolume 0 1 708 10
/**
 *
 */
52
inline OmniBoundingVolume::OmniBoundingVolume(void);

1543 14 get_class_type 0 4 1708 34 OmniBoundingVolume::get_class_type 0 1 709 0
59
static TypeHandle OmniBoundingVolume::get_class_type(void);

1544 19 ~OmniBoundingVolume 0 516 1708 39 OmniBoundingVolume::~OmniBoundingVolume 0 0 0
46
OmniBoundingVolume::~OmniBoundingVolume(void);

1545 19 UnionBoundingVolume 0 260 1709 40 UnionBoundingVolume::UnionBoundingVolume 0 1 710 49
/**
 * Constructs an empty union.
 */

/**
 *
 */
54
inline UnionBoundingVolume::UnionBoundingVolume(void);

1546 12 operator new 0 4 1709 33 UnionBoundingVolume::operator new 0 1 711 0
142
inline void *UnionBoundingVolume::operator new(std::size_t size);
inline void *UnionBoundingVolume::operator new(std::size_t size, void *ptr);

1547 15 operator delete 0 4 1709 36 UnionBoundingVolume::operator delete 0 0 0
126
inline void UnionBoundingVolume::operator delete(void *ptr);
inline void UnionBoundingVolume::operator delete(void *, void *);

1548 12 validate_ptr 0 4 1709 33 UnionBoundingVolume::validate_ptr 0 0 0
70
static inline bool UnionBoundingVolume::validate_ptr(void const *ptr);

1549 18 get_num_components 0 4 1709 39 UnionBoundingVolume::get_num_components 0 1 712 57
/**
 * Returns the number of components in the union.
 */
63
inline int UnionBoundingVolume::get_num_components(void) const;

1550 13 get_component 0 4 1709 34 UnionBoundingVolume::get_component 0 1 713 50
/**
 * Returns the nth component in the union.
 */
86
inline GeometricBoundingVolume const *UnionBoundingVolume::get_component(int n) const;

1551 16 clear_components 0 4 1709 37 UnionBoundingVolume::clear_components 0 1 714 50
/**
 * Removes all components from the volume.
 */
49
void UnionBoundingVolume::clear_components(void);

1552 13 add_component 0 4 1709 34 UnionBoundingVolume::add_component 0 1 715 373
/**
 * Adds a new component to the volume.  This does not necessarily increase the
 * total number of components by one, and you may or may not be able to find
 * this component in the volume by a subsequent call to get_component();
 * certain optimizations may prevent the component from being added, or have
 * other unexpected effects on the total set of components.
 */
82
void UnionBoundingVolume::add_component(GeometricBoundingVolume const *component);

1553 19 filter_intersection 0 4 1709 40 UnionBoundingVolume::filter_intersection 0 1 716 104
/**
 * Removes from the union any components that have no intersection with the
 * indicated volume.
 */
76
void UnionBoundingVolume::filter_intersection(BoundingVolume const *volume);

1554 14 get_class_type 0 4 1709 35 UnionBoundingVolume::get_class_type 0 1 717 0
60
static TypeHandle UnionBoundingVolume::get_class_type(void);

1555 20 ~UnionBoundingVolume 0 516 1709 41 UnionBoundingVolume::~UnionBoundingVolume 0 0 0
48
UnionBoundingVolume::~UnionBoundingVolume(void);

1556 10 Randomizer 0 260 1710 22 Randomizer::Randomizer 0 2 718 719 120
/**
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */

/**
 *
 */
122
inline explicit Randomizer::Randomizer(unsigned long int seed = 0);
inline Randomizer::Randomizer(Randomizer const &copy);

1557 10 operator = 0 4 1710 22 Randomizer::operator = 0 1 720 0
59
inline void Randomizer::operator =(Randomizer const &copy);

1558 10 random_int 0 4 1710 22 Randomizer::random_int 0 1 721 60
/**
 * Returns a random integer in the range [0, range).
 */
45
inline int Randomizer::random_int(int range);

1559 11 random_real 0 4 1710 23 Randomizer::random_real 0 1 722 59
/**
 * Returns a random double in the range [0, range).
 */
52
inline double Randomizer::random_real(double range);

1560 16 random_real_unit 0 4 1710 28 Randomizer::random_real_unit 0 1 723 60
/**
 * Returns a random double in the range [-0.5, 0.5).
 */
49
inline double Randomizer::random_real_unit(void);

1561 13 get_next_seed 0 4 1710 25 Randomizer::get_next_seed 0 1 724 77
/**
 * Returns a random seed value for the next global Randomizer object.
 */
64
static inline unsigned long int Randomizer::get_next_seed(void);

1562 8 get_seed 0 4 1710 20 Randomizer::get_seed 0 1 725 126
/**
 * Returns a unique seed value based on the seed value passed to this
 * Randomizer object (and on its current state).
 */
52
inline unsigned long int Randomizer::get_seed(void);

1563 11 ~Randomizer 0 516 1710 23 Randomizer::~Randomizer 0 0 0
30
Randomizer::~Randomizer(void);

1564 8 get_seed 0 4 1711 21 PerlinNoise::get_seed 0 1 726 127
/**
 * Returns a unique seed value based on the seed value passed to this
 * PerlinNoise object (and on its current state).
 */
53
inline unsigned long int PerlinNoise::get_seed(void);

1565 12 ~PerlinNoise 0 516 1711 25 PerlinNoise::~PerlinNoise 0 0 0
32
PerlinNoise::~PerlinNoise(void);

1566 12 PerlinNoise2 0 260 1712 26 PerlinNoise2::PerlinNoise2 0 3 727 728 729 400
/**
 * Randomizes the tables to make a unique noise function.  Uses a default
 * scale (noise frequency), table size, and seed.
 */

/**
 * Randomizes the tables to make a unique noise function.
 *
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */

/**
 * Makes an exact copy of the existing PerlinNoise object, including its
 * random seed.
 */
217
inline PerlinNoise2::PerlinNoise2(void);
inline explicit PerlinNoise2::PerlinNoise2(double sx, double sy, int table_size = 256, unsigned long int seed = 0);
inline PerlinNoise2::PerlinNoise2(PerlinNoise2 const &copy);

1567 10 operator = 0 4 1712 24 PerlinNoise2::operator = 0 1 730 0
63
inline void PerlinNoise2::operator =(PerlinNoise2 const &copy);

1568 9 set_scale 0 4 1712 23 PerlinNoise2::set_scale 0 4 731 732 733 734 222
/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */
233
inline void PerlinNoise2::set_scale(double scale);
inline void PerlinNoise2::set_scale(double sx, double sy);
inline void PerlinNoise2::set_scale(LVecBase2f const &scale);
inline void PerlinNoise2::set_scale(LVecBase2d const &scale);

1569 5 noise 0 4 1712 19 PerlinNoise2::noise 0 3 735 736 737 178
/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */
184
inline double PerlinNoise2::noise(double x, double y) const;
inline float PerlinNoise2::noise(LVecBase2f const &value) const;
double PerlinNoise2::noise(LVecBase2d const &value) const;

1570 11 operator () 0 4 1712 25 PerlinNoise2::operator () 0 3 738 739 740 0
209
inline double PerlinNoise2::operator ()(double x, double y) const;
inline float PerlinNoise2::operator ()(LVecBase2f const &value) const;
inline double PerlinNoise2::operator ()(LVecBase2d const &value) const;

1571 13 ~PerlinNoise2 0 516 1712 27 PerlinNoise2::~PerlinNoise2 0 0 0
34
PerlinNoise2::~PerlinNoise2(void);

1572 12 PerlinNoise3 0 260 1713 26 PerlinNoise3::PerlinNoise3 0 3 741 742 743 400
/**
 * Randomizes the tables to make a unique noise function.  Uses a default
 * scale (noise frequency), table size, and seed.
 */

/**
 * Randomizes the tables to make a unique noise function.
 *
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */

/**
 * Makes an exact copy of the existing PerlinNoise object, including its
 * random seed.
 */
228
inline PerlinNoise3::PerlinNoise3(void);
inline explicit PerlinNoise3::PerlinNoise3(double sx, double sy, double sz, int table_size = 256, unsigned long int seed = 0);
inline PerlinNoise3::PerlinNoise3(PerlinNoise3 const &copy);

1573 10 operator = 0 4 1713 24 PerlinNoise3::operator = 0 1 744 0
63
inline void PerlinNoise3::operator =(PerlinNoise3 const &copy);

1574 9 set_scale 0 4 1713 23 PerlinNoise3::set_scale 0 4 745 746 747 748 222
/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */

/**
 * Changes the scale (frequency) of the noise.
 */
244
inline void PerlinNoise3::set_scale(double scale);
inline void PerlinNoise3::set_scale(double sx, double sy, double sz);
inline void PerlinNoise3::set_scale(LVecBase3f const &scale);
inline void PerlinNoise3::set_scale(LVecBase3d const &scale);

1575 5 noise 0 4 1713 19 PerlinNoise3::noise 0 3 749 750 751 178
/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */
194
inline double PerlinNoise3::noise(double x, double y, double z) const;
inline float PerlinNoise3::noise(LVecBase3f const &value) const;
double PerlinNoise3::noise(LVecBase3d const &value) const;

1576 11 operator () 0 4 1713 25 PerlinNoise3::operator () 0 3 752 753 754 0
219
inline double PerlinNoise3::operator ()(double x, double y, double z) const;
inline float PerlinNoise3::operator ()(LVecBase3f const &value) const;
inline double PerlinNoise3::operator ()(LVecBase3d const &value) const;

1577 13 ~PerlinNoise3 0 516 1713 27 PerlinNoise3::~PerlinNoise3 0 0 0
34
PerlinNoise3::~PerlinNoise3(void);

1578 9 rotate_to 0 1 0 9 rotate_to 0 4 829 830 831 832 0
287
void rotate_to(LMatrix3f &mat, LVector3f const &a, LVector3f const &b);
void rotate_to(LMatrix3d &mat, LVector3d const &a, LVector3d const &b);
void rotate_to(LMatrix4f &mat, LVector3f const &a, LVector3f const &b);
void rotate_to(LMatrix4d &mat, LVector3d const &a, LVector3d const &b);

1579 19 StackedPerlinNoise2 0 260 1770 40 StackedPerlinNoise2::StackedPerlinNoise2 0 3 755 756 757 555
/**
 * Creates a StackedPerlinNoise2 object with no levels.  You should call
 * add_level() to add each level by hand.
 */

/**
 * Creates num_levels nested PerlinNoise2 objects.  Each stacked Perlin object
 * will have a scale of 1 scale_factor times the previous object (so that it
 * is higher-frequency, if scale_factor > 1), and an amplitude of amp_scale
 * times the previous object (so that it is less important, if amp_scale < 1).
 */

/**
 * Creates an exact duplicate of the existing StackedPerlinNoise2 object,
 * including the random seed.
 */
323
inline StackedPerlinNoise2::StackedPerlinNoise2(void);
explicit StackedPerlinNoise2::StackedPerlinNoise2(double sx, double sy, int num_levels = 2, double scale_factor = 4.0, double amp_scale = 0.5, int table_size = 256, unsigned long int seed = 0);
StackedPerlinNoise2::StackedPerlinNoise2(StackedPerlinNoise2 const &copy);

1580 10 operator = 0 4 1770 31 StackedPerlinNoise2::operator = 0 1 758 0
70
void StackedPerlinNoise2::operator =(StackedPerlinNoise2 const &copy);

1581 9 add_level 0 4 1770 30 StackedPerlinNoise2::add_level 0 1 759 95
/**
 * Adds an arbitrary PerlinNoise2 object, and an associated amplitude, to the
 * stack.
 */
81
void StackedPerlinNoise2::add_level(PerlinNoise2 const &level, double amp = 1.0);

1582 5 clear 0 4 1770 26 StackedPerlinNoise2::clear 0 1 760 98
/**
 * Removes all levels from the stack.  You must call add_level() again to
 * restore them.
 */
38
void StackedPerlinNoise2::clear(void);

1583 5 noise 0 4 1770 26 StackedPerlinNoise2::noise 0 3 761 762 763 178
/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */
187
inline double StackedPerlinNoise2::noise(double x, double y);
inline float StackedPerlinNoise2::noise(LVecBase2f const &value);
double StackedPerlinNoise2::noise(LVecBase2d const &value);

1584 11 operator () 0 4 1770 32 StackedPerlinNoise2::operator () 0 3 764 765 766 0
212
inline double StackedPerlinNoise2::operator ()(double x, double y);
inline float StackedPerlinNoise2::operator ()(LVecBase2f const &value);
inline double StackedPerlinNoise2::operator ()(LVecBase2d const &value);

1585 20 ~StackedPerlinNoise2 0 516 1770 41 StackedPerlinNoise2::~StackedPerlinNoise2 0 0 0
48
StackedPerlinNoise2::~StackedPerlinNoise2(void);

1586 19 StackedPerlinNoise3 0 260 1771 40 StackedPerlinNoise3::StackedPerlinNoise3 0 3 767 768 769 555
/**
 * Creates a StackedPerlinNoise3 object with no levels.  You should call
 * add_level() to add each level by hand.
 */

/**
 * Creates num_levels nested PerlinNoise3 objects.  Each stacked Perlin object
 * will have a scale of 1 scale_factor times the previous object (so that it
 * is higher-frequency, if scale_factor > 1), and an amplitude of amp_scale
 * times the previous object (so that it is less important, if amp_scale < 1).
 */

/**
 * Creates an exact duplicate of the existing StackedPerlinNoise3 object,
 * including the random seed.
 */
334
inline StackedPerlinNoise3::StackedPerlinNoise3(void);
explicit StackedPerlinNoise3::StackedPerlinNoise3(double sx, double sy, double sz, int num_levels = 3, double scale_factor = 4.0, double amp_scale = 0.5, int table_size = 256, unsigned long int seed = 0);
StackedPerlinNoise3::StackedPerlinNoise3(StackedPerlinNoise3 const &copy);

1587 10 operator = 0 4 1771 31 StackedPerlinNoise3::operator = 0 1 770 0
70
void StackedPerlinNoise3::operator =(StackedPerlinNoise3 const &copy);

1588 9 add_level 0 4 1771 30 StackedPerlinNoise3::add_level 0 1 771 95
/**
 * Adds an arbitrary PerlinNoise3 object, and an associated amplitude, to the
 * stack.
 */
81
void StackedPerlinNoise3::add_level(PerlinNoise3 const &level, double amp = 1.0);

1589 5 clear 0 4 1771 26 StackedPerlinNoise3::clear 0 1 772 98
/**
 * Removes all levels from the stack.  You must call add_level() again to
 * restore them.
 */
38
void StackedPerlinNoise3::clear(void);

1590 5 noise 0 4 1771 26 StackedPerlinNoise3::noise 0 3 773 774 775 178
/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */

/**
 * Returns the noise function of the three inputs.
 */
197
inline double StackedPerlinNoise3::noise(double x, double y, double z);
inline float StackedPerlinNoise3::noise(LVecBase3f const &value);
double StackedPerlinNoise3::noise(LVecBase3d const &value);

1591 11 operator () 0 4 1771 32 StackedPerlinNoise3::operator () 0 3 776 777 778 0
222
inline double StackedPerlinNoise3::operator ()(double x, double y, double z);
inline float StackedPerlinNoise3::operator ()(LVecBase3f const &value);
inline double StackedPerlinNoise3::operator ()(LVecBase3d const &value);

1592 20 ~StackedPerlinNoise3 0 516 1771 41 StackedPerlinNoise3::~StackedPerlinNoise3 0 0 0
48
StackedPerlinNoise3::~StackedPerlinNoise3(void);

1593 12 Triangulator 0 260 1772 26 Triangulator::Triangulator 0 2 779 780 10
/**
 *
 */
100
Triangulator::Triangulator(void);
inline Triangulator::Triangulator(Triangulator const &) = default;

1594 5 clear 0 4 1772 19 Triangulator::clear 0 1 781 115
/**
 * Removes all vertices and polygon specifications from the Triangulator, and
 * prepares it to start over.
 */
31
void Triangulator::clear(void);

1595 10 add_vertex 0 4 1772 24 Triangulator::add_vertex 0 2 782 783 166
/**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */

/**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */
109
int Triangulator::add_vertex(LPoint2d const &point);
inline int Triangulator::add_vertex(double x, double y);

1596 16 get_num_vertices 0 4 1772 30 Triangulator::get_num_vertices 0 1 784 267
/**
 * Returns the number of vertices in the pool.  Note that the Triangulator
 * might append new vertices, in addition to those added by the user, if any
 * of the polygon is self-intersecting, or if any of the holes intersect some
 * part of the polygon edges.
 */
54
inline int Triangulator::get_num_vertices(void) const;

1597 10 get_vertex 0 4 1772 24 Triangulator::get_vertex 0 1 785 34
/**
 * Returns the nth vertex.
 */
61
inline LPoint2d const &Triangulator::get_vertex(int n) const;

1598 13 clear_polygon 0 4 1772 27 Triangulator::clear_polygon 0 1 786 112
/**
 * Removes the current polygon definition (and its set of holes), but does not
 * clear the vertex pool.
 */
39
void Triangulator::clear_polygon(void);

1599 18 add_polygon_vertex 0 4 1772 32 Triangulator::add_polygon_vertex 0 1 787 334
/**
 * Adds the next consecutive vertex of the polygon.  This vertex should index
 * into the vertex pool established by repeated calls to add_vertex().
 *
 * The vertices may be listed in either clockwise or counterclockwise order.
 * Vertices should not be repeated.  In particular, do not repeat the first
 * vertex at the end.
 */
49
void Triangulator::add_polygon_vertex(int index);

1600 15 is_left_winding 0 4 1772 29 Triangulator::is_left_winding 0 1 788 145
/**
 * Returns true if the polygon vertices are listed in counterclockwise order,
 * or false if they appear to be listed in clockwise order.
 */
54
inline bool Triangulator::is_left_winding(void) const;

1601 10 begin_hole 0 4 1772 24 Triangulator::begin_hole 0 1 789 78
/**
 * Finishes the previous hole, if any, and prepares to add a new hole.
 */
36
void Triangulator::begin_hole(void);

1602 15 add_hole_vertex 0 4 1772 29 Triangulator::add_hole_vertex 0 1 790 277
/**
 * Adds the next consecutive vertex of the current hole.  This vertex should
 * index into the vertex pool established by repeated calls to add_vertex().
 *
 * The vertices may be listed in either clockwise or counterclockwise order.
 * Vertices should not be repeated.
 */
46
void Triangulator::add_hole_vertex(int index);

1603 11 triangulate 0 4 1772 25 Triangulator::triangulate 0 1 791 181
/**
 * Does the work of triangulating the specified polygon.  After this call, you
 * may retrieve the new triangles one at a time by iterating through
 * get_triangle_v0/1/2().
 */
37
void Triangulator::triangulate(void);

1604 17 get_num_triangles 0 4 1772 31 Triangulator::get_num_triangles 0 1 792 94
/**
 * Returns the number of triangles generated by the previous call to
 * triangulate().
 */
48
int Triangulator::get_num_triangles(void) const;

1605 15 get_triangle_v0 0 4 1772 29 Triangulator::get_triangle_v0 0 1 793 194
/**
 * Returns vertex 0 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */
47
int Triangulator::get_triangle_v0(int n) const;

1606 15 get_triangle_v1 0 4 1772 29 Triangulator::get_triangle_v1 0 1 794 194
/**
 * Returns vertex 1 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */
47
int Triangulator::get_triangle_v1(int n) const;

1607 15 get_triangle_v2 0 4 1772 29 Triangulator::get_triangle_v2 0 1 795 194
/**
 * Returns vertex 2 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */
47
int Triangulator::get_triangle_v2(int n) const;

1608 13 ~Triangulator 0 516 1772 27 Triangulator::~Triangulator 0 0 0
34
Triangulator::~Triangulator(void);

1609 13 Triangulator3 0 260 1775 28 Triangulator3::Triangulator3 0 2 796 797 10
/**
 *
 */
105
Triangulator3::Triangulator3(void);
inline Triangulator3::Triangulator3(Triangulator3 const &) = default;

1610 5 clear 0 4 1775 20 Triangulator3::clear 0 1 798 115
/**
 * Removes all vertices and polygon specifications from the Triangulator, and
 * prepares it to start over.
 */
32
void Triangulator3::clear(void);

1611 10 add_vertex 0 4 1775 25 Triangulator3::add_vertex 0 2 799 800 166
/**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */

/**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */
121
int Triangulator3::add_vertex(LPoint3d const &point);
inline int Triangulator3::add_vertex(double x, double y, double z);

1612 16 get_num_vertices 0 4 1775 31 Triangulator3::get_num_vertices 0 1 801 267
/**
 * Returns the number of vertices in the pool.  Note that the Triangulator
 * might append new vertices, in addition to those added by the user, if any
 * of the polygon is self-intersecting, or if any of the holes intersect some
 * part of the polygon edges.
 */
55
inline int Triangulator3::get_num_vertices(void) const;

1613 10 get_vertex 0 4 1775 25 Triangulator3::get_vertex 0 1 802 34
/**
 * Returns the nth vertex.
 */
62
inline LPoint3d const &Triangulator3::get_vertex(int n) const;

1614 11 triangulate 0 4 1775 26 Triangulator3::triangulate 0 1 803 181
/**
 * Does the work of triangulating the specified polygon.  After this call, you
 * may retrieve the new triangles one at a time by iterating through
 * get_triangle_v0/1/2().
 */
38
void Triangulator3::triangulate(void);

1615 9 get_plane 0 4 1775 24 Triangulator3::get_plane 0 1 804 100
/**
 * Returns the plane of the polygon.  This is only available after calling
 * triangulate().
 */
59
inline LPlaned const &Triangulator3::get_plane(void) const;

1616 14 ~Triangulator3 0 516 1775 29 Triangulator3::~Triangulator3 0 0 0
36
Triangulator3::~Triangulator3(void);

832
1 0 0 15 10 1781 852 0 0 1 4 copy 1 1779  
2 0 0 23 10 1781 852 0 0 1 4 copy 1 1782  
3 0 0 4 11 1784 0 0 0 1 4 this 3 1781  
4 0 0 6 12 1785 0 0 0 1 4 this 3 1779  
5 0 0 6 13 1786 0 0 0 2 4 this 3 1779  1 n 1 1785  
6 0 0 38 14 1786 0 0 0 2 4 this 3 1779  1 n 1 1785  
7 0 0 38 15 1789 0 0 0 1 4 this 3 1779  
8 0 0 38 16 1789 0 0 0 3 4 this 3 1779  1 n 1 1785  5 count 1 1785  
9 0 0 6 17 1792 0 0 0 1 4 this 3 1779  
10 0 0 6 18 1792 0 0 0 1 4 this 3 1779  
11 0 0 6 19 1785 0 0 0 2 4 this 3 1779  6 param0 0 1786  
12 0 0 38 20 1789 0 0 0 1 4 this 3 1779  
13 0 0 38 21 1792 0 0 0 3 4 this 3 1779  4 view 1 1793  5 flags 1 1792  
14 0 0 36 22 1784 0 0 0 2 4 this 3 1779  4 view 1 1793  
15 0 0 39 23 1781 852 0 0 2 4 this 3 1779  4 memo 1 1789  
16 0 0 6 8 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1796  5 other 1 1796  
17 0 0 6 9 1798 0 0 0 2 4 this 3 1796  5 other 1 1796  
18 0 0 4 5 1784 0 0 0 1 4 this 3 1799  
19 0 0 4 6 1784 0 0 0 2 4 this 3 1800  3 out 1 1802  
20 0 0 15 33 1806 872 0 0 1 4 copy 1 1804  
21 0 0 23 33 1806 872 0 0 1 4 copy 1 1807  
22 0 0 4 34 1784 0 0 0 1 4 this 3 1806  
23 0 0 6 35 1785 0 0 0 1 4 this 3 1804  
24 0 0 6 36 1809 0 0 0 2 4 this 3 1804  1 n 1 1785  
25 0 0 38 37 1809 0 0 0 2 4 this 3 1804  1 n 1 1785  
26 0 0 38 38 1789 0 0 0 1 4 this 3 1804  
27 0 0 38 39 1789 0 0 0 3 4 this 3 1804  1 n 1 1785  5 count 1 1785  
28 0 0 6 40 1792 0 0 0 1 4 this 3 1804  
29 0 0 6 41 1792 0 0 0 1 4 this 3 1804  
30 0 0 6 42 1785 0 0 0 2 4 this 3 1804  6 param0 0 1809  
31 0 0 38 43 1789 0 0 0 1 4 this 3 1804  
32 0 0 38 44 1792 0 0 0 3 4 this 3 1804  4 view 1 1793  5 flags 1 1792  
33 0 0 36 45 1784 0 0 0 2 4 this 3 1804  4 view 1 1793  
34 0 0 39 46 1806 872 0 0 2 4 this 3 1804  4 memo 1 1789  
35 0 0 6 31 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1812  5 other 1 1812  
36 0 0 6 32 1798 0 0 0 2 4 this 3 1812  5 other 1 1812  
37 0 0 4 28 1784 0 0 0 1 4 this 3 1814  
38 0 0 4 29 1784 0 0 0 2 4 this 3 1815  3 out 1 1802  
39 0 0 15 56 1819 892 0 0 1 4 copy 1 1817  
40 0 0 23 56 1819 892 0 0 1 4 copy 1 1820  
41 0 0 4 57 1784 0 0 0 1 4 this 3 1819  
42 0 0 6 58 1785 0 0 0 1 4 this 3 1817  
43 0 0 6 59 1822 0 0 0 2 4 this 3 1817  1 n 1 1785  
44 0 0 38 60 1822 0 0 0 2 4 this 3 1817  1 n 1 1785  
45 0 0 38 61 1789 0 0 0 1 4 this 3 1817  
46 0 0 38 62 1789 0 0 0 3 4 this 3 1817  1 n 1 1785  5 count 1 1785  
47 0 0 6 63 1792 0 0 0 1 4 this 3 1817  
48 0 0 6 64 1792 0 0 0 1 4 this 3 1817  
49 0 0 6 65 1785 0 0 0 2 4 this 3 1817  6 param0 0 1822  
50 0 0 38 66 1789 0 0 0 1 4 this 3 1817  
51 0 0 38 67 1792 0 0 0 3 4 this 3 1817  4 view 1 1793  5 flags 1 1792  
52 0 0 36 68 1784 0 0 0 2 4 this 3 1817  4 view 1 1793  
53 0 0 39 69 1819 892 0 0 2 4 this 3 1817  4 memo 1 1789  
54 0 0 6 54 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1825  5 other 1 1825  
55 0 0 6 55 1798 0 0 0 2 4 this 3 1825  5 other 1 1825  
56 0 0 4 51 1784 0 0 0 1 4 this 3 1827  
57 0 0 4 52 1784 0 0 0 2 4 this 3 1828  3 out 1 1802  
58 0 0 15 79 1832 912 0 0 1 4 copy 1 1830  
59 0 0 23 79 1832 912 0 0 1 4 copy 1 1833  
60 0 0 4 80 1784 0 0 0 1 4 this 3 1832  
61 0 0 6 81 1785 0 0 0 1 4 this 3 1830  
62 0 0 6 82 1835 0 0 0 2 4 this 3 1830  1 n 1 1785  
63 0 0 38 83 1835 0 0 0 2 4 this 3 1830  1 n 1 1785  
64 0 0 38 84 1789 0 0 0 1 4 this 3 1830  
65 0 0 38 85 1789 0 0 0 3 4 this 3 1830  1 n 1 1785  5 count 1 1785  
66 0 0 6 86 1792 0 0 0 1 4 this 3 1830  
67 0 0 6 87 1792 0 0 0 1 4 this 3 1830  
68 0 0 6 88 1785 0 0 0 2 4 this 3 1830  6 param0 0 1835  
69 0 0 38 89 1789 0 0 0 1 4 this 3 1830  
70 0 0 38 90 1792 0 0 0 3 4 this 3 1830  4 view 1 1793  5 flags 1 1792  
71 0 0 36 91 1784 0 0 0 2 4 this 3 1830  4 view 1 1793  
72 0 0 39 92 1832 912 0 0 2 4 this 3 1830  4 memo 1 1789  
73 0 0 6 77 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1838  5 other 1 1838  
74 0 0 6 78 1798 0 0 0 2 4 this 3 1838  5 other 1 1838  
75 0 0 4 74 1784 0 0 0 1 4 this 3 1840  
76 0 0 4 75 1784 0 0 0 2 4 this 3 1841  3 out 1 1802  
77 0 0 15 102 1845 932 0 0 1 4 copy 1 1843  
78 0 0 23 102 1845 932 0 0 1 4 copy 1 1846  
79 0 0 4 103 1784 0 0 0 1 4 this 3 1845  
80 0 0 6 104 1785 0 0 0 1 4 this 3 1843  
81 0 0 6 105 1848 0 0 0 2 4 this 3 1843  1 n 1 1785  
82 0 0 38 106 1848 0 0 0 2 4 this 3 1843  1 n 1 1785  
83 0 0 38 107 1789 0 0 0 1 4 this 3 1843  
84 0 0 38 108 1789 0 0 0 3 4 this 3 1843  1 n 1 1785  5 count 1 1785  
85 0 0 6 109 1792 0 0 0 1 4 this 3 1843  
86 0 0 6 110 1792 0 0 0 1 4 this 3 1843  
87 0 0 6 111 1785 0 0 0 2 4 this 3 1843  6 param0 0 1848  
88 0 0 38 112 1789 0 0 0 1 4 this 3 1843  
89 0 0 38 113 1792 0 0 0 3 4 this 3 1843  4 view 1 1793  5 flags 1 1792  
90 0 0 36 114 1784 0 0 0 2 4 this 3 1843  4 view 1 1793  
91 0 0 39 115 1845 932 0 0 2 4 this 3 1843  4 memo 1 1789  
92 0 0 6 100 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1851  5 other 1 1851  
93 0 0 6 101 1798 0 0 0 2 4 this 3 1851  5 other 1 1851  
94 0 0 4 97 1784 0 0 0 1 4 this 3 1853  
95 0 0 4 98 1784 0 0 0 2 4 this 3 1854  3 out 1 1802  
96 0 0 15 125 1858 952 0 0 1 4 copy 1 1856  
97 0 0 23 125 1858 952 0 0 1 4 copy 1 1859  
98 0 0 4 126 1784 0 0 0 1 4 this 3 1858  
99 0 0 6 127 1785 0 0 0 1 4 this 3 1856  
100 0 0 6 128 1861 0 0 0 2 4 this 3 1856  1 n 1 1785  
101 0 0 38 129 1861 0 0 0 2 4 this 3 1856  1 n 1 1785  
102 0 0 38 130 1789 0 0 0 1 4 this 3 1856  
103 0 0 38 131 1789 0 0 0 3 4 this 3 1856  1 n 1 1785  5 count 1 1785  
104 0 0 6 132 1792 0 0 0 1 4 this 3 1856  
105 0 0 6 133 1792 0 0 0 1 4 this 3 1856  
106 0 0 6 134 1785 0 0 0 2 4 this 3 1856  6 param0 0 1861  
107 0 0 38 135 1789 0 0 0 1 4 this 3 1856  
108 0 0 38 136 1792 0 0 0 3 4 this 3 1856  4 view 1 1793  5 flags 1 1792  
109 0 0 36 137 1784 0 0 0 2 4 this 3 1856  4 view 1 1793  
110 0 0 39 138 1858 952 0 0 2 4 this 3 1856  4 memo 1 1789  
111 0 0 6 123 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1864  5 other 1 1864  
112 0 0 6 124 1798 0 0 0 2 4 this 3 1864  5 other 1 1864  
113 0 0 4 120 1784 0 0 0 1 4 this 3 1866  
114 0 0 4 121 1784 0 0 0 2 4 this 3 1867  3 out 1 1802  
115 0 0 15 148 1871 972 0 0 1 4 copy 1 1869  
116 0 0 23 148 1871 972 0 0 1 4 copy 1 1872  
117 0 0 4 149 1784 0 0 0 1 4 this 3 1871  
118 0 0 6 150 1785 0 0 0 1 4 this 3 1869  
119 0 0 6 151 1874 0 0 0 2 4 this 3 1869  1 n 1 1785  
120 0 0 38 152 1874 0 0 0 2 4 this 3 1869  1 n 1 1785  
121 0 0 38 153 1789 0 0 0 1 4 this 3 1869  
122 0 0 38 154 1789 0 0 0 3 4 this 3 1869  1 n 1 1785  5 count 1 1785  
123 0 0 6 155 1792 0 0 0 1 4 this 3 1869  
124 0 0 6 156 1792 0 0 0 1 4 this 3 1869  
125 0 0 6 157 1785 0 0 0 2 4 this 3 1869  6 param0 0 1874  
126 0 0 38 158 1789 0 0 0 1 4 this 3 1869  
127 0 0 38 159 1792 0 0 0 3 4 this 3 1869  4 view 1 1793  5 flags 1 1792  
128 0 0 36 160 1784 0 0 0 2 4 this 3 1869  4 view 1 1793  
129 0 0 39 161 1871 972 0 0 2 4 this 3 1869  4 memo 1 1789  
130 0 0 6 146 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1877  5 other 1 1877  
131 0 0 6 147 1798 0 0 0 2 4 this 3 1877  5 other 1 1877  
132 0 0 4 143 1784 0 0 0 1 4 this 3 1879  
133 0 0 4 144 1784 0 0 0 2 4 this 3 1880  3 out 1 1802  
134 0 0 15 171 1884 992 0 0 1 4 copy 1 1882  
135 0 0 23 171 1884 992 0 0 1 4 copy 1 1885  
136 0 0 4 172 1784 0 0 0 1 4 this 3 1884  
137 0 0 6 173 1785 0 0 0 1 4 this 3 1882  
138 0 0 6 174 1887 0 0 0 2 4 this 3 1882  1 n 1 1785  
139 0 0 38 175 1887 0 0 0 2 4 this 3 1882  1 n 1 1785  
140 0 0 38 176 1789 0 0 0 1 4 this 3 1882  
141 0 0 38 177 1789 0 0 0 3 4 this 3 1882  1 n 1 1785  5 count 1 1785  
142 0 0 6 178 1792 0 0 0 1 4 this 3 1882  
143 0 0 6 179 1792 0 0 0 1 4 this 3 1882  
144 0 0 6 180 1785 0 0 0 2 4 this 3 1882  6 param0 0 1887  
145 0 0 38 181 1789 0 0 0 1 4 this 3 1882  
146 0 0 38 182 1792 0 0 0 3 4 this 3 1882  4 view 1 1793  5 flags 1 1792  
147 0 0 36 183 1784 0 0 0 2 4 this 3 1882  4 view 1 1793  
148 0 0 39 184 1884 992 0 0 2 4 this 3 1882  4 memo 1 1789  
149 0 0 6 169 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1890  5 other 1 1890  
150 0 0 6 170 1798 0 0 0 2 4 this 3 1890  5 other 1 1890  
151 0 0 4 166 1784 0 0 0 1 4 this 3 1892  
152 0 0 4 167 1784 0 0 0 2 4 this 3 1893  3 out 1 1802  
153 0 0 15 194 1897 1012 0 0 1 4 copy 1 1895  
154 0 0 23 194 1897 1012 0 0 1 4 copy 1 1898  
155 0 0 4 195 1784 0 0 0 1 4 this 3 1897  
156 0 0 6 196 1785 0 0 0 1 4 this 3 1895  
157 0 0 6 197 1900 0 0 0 2 4 this 3 1895  1 n 1 1785  
158 0 0 38 198 1900 0 0 0 2 4 this 3 1895  1 n 1 1785  
159 0 0 38 199 1789 0 0 0 1 4 this 3 1895  
160 0 0 38 200 1789 0 0 0 3 4 this 3 1895  1 n 1 1785  5 count 1 1785  
161 0 0 6 201 1792 0 0 0 1 4 this 3 1895  
162 0 0 6 202 1792 0 0 0 1 4 this 3 1895  
163 0 0 6 203 1785 0 0 0 2 4 this 3 1895  6 param0 0 1900  
164 0 0 38 204 1789 0 0 0 1 4 this 3 1895  
165 0 0 38 205 1792 0 0 0 3 4 this 3 1895  4 view 1 1793  5 flags 1 1792  
166 0 0 36 206 1784 0 0 0 2 4 this 3 1895  4 view 1 1793  
167 0 0 39 207 1897 1012 0 0 2 4 this 3 1895  4 memo 1 1789  
168 0 0 6 192 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1903  5 other 1 1903  
169 0 0 6 193 1798 0 0 0 2 4 this 3 1903  5 other 1 1903  
170 0 0 4 189 1784 0 0 0 1 4 this 3 1905  
171 0 0 4 190 1784 0 0 0 2 4 this 3 1906  3 out 1 1802  
172 0 0 15 217 1910 1032 0 0 1 4 copy 1 1908  
173 0 0 23 217 1910 1032 0 0 1 4 copy 1 1911  
174 0 0 4 218 1784 0 0 0 1 4 this 3 1910  
175 0 0 6 219 1785 0 0 0 1 4 this 3 1908  
176 0 0 6 220 1913 0 0 0 2 4 this 3 1908  1 n 1 1785  
177 0 0 38 221 1913 0 0 0 2 4 this 3 1908  1 n 1 1785  
178 0 0 38 222 1789 0 0 0 1 4 this 3 1908  
179 0 0 38 223 1789 0 0 0 3 4 this 3 1908  1 n 1 1785  5 count 1 1785  
180 0 0 6 224 1792 0 0 0 1 4 this 3 1908  
181 0 0 6 225 1792 0 0 0 1 4 this 3 1908  
182 0 0 6 226 1785 0 0 0 2 4 this 3 1908  6 param0 0 1913  
183 0 0 38 227 1789 0 0 0 1 4 this 3 1908  
184 0 0 38 228 1792 0 0 0 3 4 this 3 1908  4 view 1 1793  5 flags 1 1792  
185 0 0 36 229 1784 0 0 0 2 4 this 3 1908  4 view 1 1793  
186 0 0 39 230 1910 1032 0 0 2 4 this 3 1908  4 memo 1 1789  
187 0 0 6 215 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1916  5 other 1 1916  
188 0 0 6 216 1798 0 0 0 2 4 this 3 1916  5 other 1 1916  
189 0 0 4 212 1784 0 0 0 1 4 this 3 1918  
190 0 0 4 213 1784 0 0 0 2 4 this 3 1919  3 out 1 1802  
191 0 0 15 240 1923 1052 0 0 1 4 copy 1 1921  
192 0 0 23 240 1923 1052 0 0 1 4 copy 1 1924  
193 0 0 4 241 1784 0 0 0 1 4 this 3 1923  
194 0 0 6 242 1785 0 0 0 1 4 this 3 1921  
195 0 0 6 243 1926 0 0 0 2 4 this 3 1921  1 n 1 1785  
196 0 0 38 244 1926 0 0 0 2 4 this 3 1921  1 n 1 1785  
197 0 0 38 245 1789 0 0 0 1 4 this 3 1921  
198 0 0 38 246 1789 0 0 0 3 4 this 3 1921  1 n 1 1785  5 count 1 1785  
199 0 0 6 247 1792 0 0 0 1 4 this 3 1921  
200 0 0 6 248 1792 0 0 0 1 4 this 3 1921  
201 0 0 6 249 1785 0 0 0 2 4 this 3 1921  6 param0 0 1926  
202 0 0 38 250 1789 0 0 0 1 4 this 3 1921  
203 0 0 38 251 1792 0 0 0 3 4 this 3 1921  4 view 1 1793  5 flags 1 1792  
204 0 0 36 252 1784 0 0 0 2 4 this 3 1921  4 view 1 1793  
205 0 0 39 253 1923 1052 0 0 2 4 this 3 1921  4 memo 1 1789  
206 0 0 6 238 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1929  5 other 1 1929  
207 0 0 6 239 1798 0 0 0 2 4 this 3 1929  5 other 1 1929  
208 0 0 4 235 1784 0 0 0 1 4 this 3 1931  
209 0 0 4 236 1784 0 0 0 2 4 this 3 1932  3 out 1 1802  
210 0 0 15 263 1936 1072 0 0 1 4 copy 1 1934  
211 0 0 23 263 1936 1072 0 0 1 4 copy 1 1937  
212 0 0 4 264 1784 0 0 0 1 4 this 3 1936  
213 0 0 6 265 1785 0 0 0 1 4 this 3 1934  
214 0 0 6 266 1939 0 0 0 2 4 this 3 1934  1 n 1 1785  
215 0 0 38 267 1939 0 0 0 2 4 this 3 1934  1 n 1 1785  
216 0 0 38 268 1789 0 0 0 1 4 this 3 1934  
217 0 0 38 269 1789 0 0 0 3 4 this 3 1934  1 n 1 1785  5 count 1 1785  
218 0 0 6 270 1792 0 0 0 1 4 this 3 1934  
219 0 0 6 271 1792 0 0 0 1 4 this 3 1934  
220 0 0 6 272 1785 0 0 0 2 4 this 3 1934  6 param0 0 1939  
221 0 0 38 273 1789 0 0 0 1 4 this 3 1934  
222 0 0 38 274 1792 0 0 0 3 4 this 3 1934  4 view 1 1793  5 flags 1 1792  
223 0 0 36 275 1784 0 0 0 2 4 this 3 1934  4 view 1 1793  
224 0 0 39 276 1936 1072 0 0 2 4 this 3 1934  4 memo 1 1789  
225 0 0 6 261 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1942  5 other 1 1942  
226 0 0 6 262 1798 0 0 0 2 4 this 3 1942  5 other 1 1942  
227 0 0 4 258 1784 0 0 0 1 4 this 3 1944  
228 0 0 4 259 1784 0 0 0 2 4 this 3 1945  3 out 1 1802  
229 0 0 15 286 1949 1092 0 0 1 4 copy 1 1947  
230 0 0 23 286 1949 1092 0 0 1 4 copy 1 1950  
231 0 0 4 287 1784 0 0 0 1 4 this 3 1949  
232 0 0 6 288 1785 0 0 0 1 4 this 3 1947  
233 0 0 6 289 1952 0 0 0 2 4 this 3 1947  1 n 1 1785  
234 0 0 38 290 1952 0 0 0 2 4 this 3 1947  1 n 1 1785  
235 0 0 38 291 1789 0 0 0 1 4 this 3 1947  
236 0 0 38 292 1789 0 0 0 3 4 this 3 1947  1 n 1 1785  5 count 1 1785  
237 0 0 6 293 1792 0 0 0 1 4 this 3 1947  
238 0 0 6 294 1792 0 0 0 1 4 this 3 1947  
239 0 0 6 295 1785 0 0 0 2 4 this 3 1947  6 param0 0 1952  
240 0 0 38 296 1789 0 0 0 1 4 this 3 1947  
241 0 0 38 297 1792 0 0 0 3 4 this 3 1947  4 view 1 1793  5 flags 1 1792  
242 0 0 36 298 1784 0 0 0 2 4 this 3 1947  4 view 1 1793  
243 0 0 39 299 1949 1092 0 0 2 4 this 3 1947  4 memo 1 1789  
244 0 0 6 284 1798 0 0 64 // These are implemented in PointerToVoid, but expose them here. 2 4 this 3 1955  5 other 1 1955  
245 0 0 6 285 1798 0 0 0 2 4 this 3 1955  5 other 1 1955  
246 0 0 4 281 1784 0 0 0 1 4 this 3 1957  
247 0 0 4 282 1784 0 0 0 2 4 this 3 1958  3 out 1 1802  
248 0 0 15 302 1960 1114 0 0 1 4 copy 1 1782  
249 0 0 55 302 1960 1114 0 0 1 6 source 1 1789  
250 0 0 23 302 1960 1114 0 0 1 11 type_handle 5 1961  
251 0 0 7 303 1960 1114 0 0 2 1 n 1 1785  11 type_handle 5 1961  
252 0 0 4 304 1784 0 0 0 1 4 this 3 1960  
253 0 0 6 305 1785 0 0 0 1 4 this 3 1782  
254 0 0 4 306 1784 0 0 0 2 4 this 3 1960  1 x 1 1786  
255 0 0 4 307 1784 0 0 0 1 4 this 3 1960  
256 0 0 6 308 1786 0 0 0 2 4 this 3 1782  1 n 1 1785  
257 0 0 4 309 1784 0 0 0 3 4 this 3 1960  1 n 1 1785  5 value 1 1786  
258 0 0 38 310 1786 0 0 0 2 4 this 3 1782  1 n 1 1785  
259 0 0 36 311 1784 0 0 0 3 4 this 3 1960  1 n 1 1785  5 value 1 1786  
260 0 0 38 312 1789 0 0 0 1 4 this 3 1782  
261 0 0 36 313 1784 0 0 0 2 4 this 3 1960  4 data 1 1789  
262 0 0 38 314 1789 0 0 0 3 4 this 3 1782  1 n 1 1785  5 count 1 1785  
263 0 0 4 315 1784 0 0 0 4 4 this 3 1960  1 n 1 1785  5 count 1 1785  4 data 1 1963  
264 0 0 6 316 1792 0 0 0 1 4 this 3 1782  
265 0 0 6 317 1792 0 0 0 1 4 this 3 1782  
266 0 0 6 318 1785 0 0 0 2 4 this 3 1782  6 param0 0 1786  
267 0 0 38 319 1789 0 0 0 1 4 this 3 1782  
268 0 0 38 320 1792 0 0 0 3 4 this 3 1960  4 view 1 1793  5 flags 1 1792  
269 0 0 36 321 1784 0 0 0 2 4 this 3 1782  4 view 1 1793  
270 0 0 39 322 1960 1114 0 0 2 4 this 3 1782  4 memo 1 1789  
271 0 0 15 325 1964 1136 0 0 1 4 copy 1 1807  
272 0 0 55 325 1964 1136 0 0 1 6 source 1 1789  
273 0 0 23 325 1964 1136 0 0 1 11 type_handle 5 1961  
274 0 0 7 326 1964 1136 0 0 2 1 n 1 1785  11 type_handle 5 1961  
275 0 0 4 327 1784 0 0 0 1 4 this 3 1964  
276 0 0 6 328 1785 0 0 0 1 4 this 3 1807  
277 0 0 4 329 1784 0 0 0 2 4 this 3 1964  1 x 1 1809  
278 0 0 4 330 1784 0 0 0 1 4 this 3 1964  
279 0 0 6 331 1809 0 0 0 2 4 this 3 1807  1 n 1 1785  
280 0 0 4 332 1784 0 0 0 3 4 this 3 1964  1 n 1 1785  5 value 1 1809  
281 0 0 38 333 1809 0 0 0 2 4 this 3 1807  1 n 1 1785  
282 0 0 36 334 1784 0 0 0 3 4 this 3 1964  1 n 1 1785  5 value 1 1809  
283 0 0 38 335 1789 0 0 0 1 4 this 3 1807  
284 0 0 36 336 1784 0 0 0 2 4 this 3 1964  4 data 1 1789  
285 0 0 38 337 1789 0 0 0 3 4 this 3 1807  1 n 1 1785  5 count 1 1785  
286 0 0 4 338 1784 0 0 0 4 4 this 3 1964  1 n 1 1785  5 count 1 1785  4 data 1 1963  
287 0 0 6 339 1792 0 0 0 1 4 this 3 1807  
288 0 0 6 340 1792 0 0 0 1 4 this 3 1807  
289 0 0 6 341 1785 0 0 0 2 4 this 3 1807  6 param0 0 1809  
290 0 0 38 342 1789 0 0 0 1 4 this 3 1807  
291 0 0 38 343 1792 0 0 0 3 4 this 3 1964  4 view 1 1793  5 flags 1 1792  
292 0 0 36 344 1784 0 0 0 2 4 this 3 1807  4 view 1 1793  
293 0 0 39 345 1964 1136 0 0 2 4 this 3 1807  4 memo 1 1789  
294 0 0 15 348 1965 1158 0 0 1 4 copy 1 1820  
295 0 0 55 348 1965 1158 0 0 1 6 source 1 1789  
296 0 0 23 348 1965 1158 0 0 1 11 type_handle 5 1961  
297 0 0 7 349 1965 1158 0 0 2 1 n 1 1785  11 type_handle 5 1961  
298 0 0 4 350 1784 0 0 0 1 4 this 3 1965  
299 0 0 6 351 1785 0 0 0 1 4 this 3 1820  
300 0 0 4 352 1784 0 0 0 2 4 this 3 1965  1 x 1 1822  
301 0 0 4 353 1784 0 0 0 1 4 this 3 1965  
302 0 0 6 354 1822 0 0 0 2 4 this 3 1820  1 n 1 1785  
303 0 0 4 355 1784 0 0 0 3 4 this 3 1965  1 n 1 1785  5 value 1 1822  
304 0 0 38 356 1822 0 0 0 2 4 this 3 1820  1 n 1 1785  
305 0 0 36 357 1784 0 0 0 3 4 this 3 1965  1 n 1 1785  5 value 1 1822  
306 0 0 38 358 1789 0 0 0 1 4 this 3 1820  
307 0 0 36 359 1784 0 0 0 2 4 this 3 1965  4 data 1 1789  
308 0 0 38 360 1789 0 0 0 3 4 this 3 1820  1 n 1 1785  5 count 1 1785  
309 0 0 4 361 1784 0 0 0 4 4 this 3 1965  1 n 1 1785  5 count 1 1785  4 data 1 1963  
310 0 0 6 362 1792 0 0 0 1 4 this 3 1820  
311 0 0 6 363 1792 0 0 0 1 4 this 3 1820  
312 0 0 6 364 1785 0 0 0 2 4 this 3 1820  6 param0 0 1822  
313 0 0 38 365 1789 0 0 0 1 4 this 3 1820  
314 0 0 38 366 1792 0 0 0 3 4 this 3 1965  4 view 1 1793  5 flags 1 1792  
315 0 0 36 367 1784 0 0 0 2 4 this 3 1820  4 view 1 1793  
316 0 0 39 368 1965 1158 0 0 2 4 this 3 1820  4 memo 1 1789  
317 0 0 15 371 1966 1180 0 0 1 4 copy 1 1833  
318 0 0 55 371 1966 1180 0 0 1 6 source 1 1789  
319 0 0 23 371 1966 1180 0 0 1 11 type_handle 5 1961  
320 0 0 7 372 1966 1180 0 0 2 1 n 1 1785  11 type_handle 5 1961  
321 0 0 4 373 1784 0 0 0 1 4 this 3 1966  
322 0 0 6 374 1785 0 0 0 1 4 this 3 1833  
323 0 0 4 375 1784 0 0 0 2 4 this 3 1966  1 x 1 1835  
324 0 0 4 376 1784 0 0 0 1 4 this 3 1966  
325 0 0 6 377 1835 0 0 0 2 4 this 3 1833  1 n 1 1785  
326 0 0 4 378 1784 0 0 0 3 4 this 3 1966  1 n 1 1785  5 value 1 1835  
327 0 0 38 379 1835 0 0 0 2 4 this 3 1833  1 n 1 1785  
328 0 0 36 380 1784 0 0 0 3 4 this 3 1966  1 n 1 1785  5 value 1 1835  
329 0 0 38 381 1789 0 0 0 1 4 this 3 1833  
330 0 0 36 382 1784 0 0 0 2 4 this 3 1966  4 data 1 1789  
331 0 0 38 383 1789 0 0 0 3 4 this 3 1833  1 n 1 1785  5 count 1 1785  
332 0 0 4 384 1784 0 0 0 4 4 this 3 1966  1 n 1 1785  5 count 1 1785  4 data 1 1963  
333 0 0 6 385 1792 0 0 0 1 4 this 3 1833  
334 0 0 6 386 1792 0 0 0 1 4 this 3 1833  
335 0 0 6 387 1785 0 0 0 2 4 this 3 1833  6 param0 0 1835  
336 0 0 38 388 1789 0 0 0 1 4 this 3 1833  
337 0 0 38 389 1792 0 0 0 3 4 this 3 1966  4 view 1 1793  5 flags 1 1792  
338 0 0 36 390 1784 0 0 0 2 4 this 3 1833  4 view 1 1793  
339 0 0 39 391 1966 1180 0 0 2 4 this 3 1833  4 memo 1 1789  
340 0 0 15 394 1967 1202 0 0 1 4 copy 1 1846  
341 0 0 55 394 1967 1202 0 0 1 6 source 1 1789  
342 0 0 23 394 1967 1202 0 0 1 11 type_handle 5 1961  
343 0 0 7 395 1967 1202 0 0 2 1 n 1 1785  11 type_handle 5 1961  
344 0 0 4 396 1784 0 0 0 1 4 this 3 1967  
345 0 0 6 397 1785 0 0 0 1 4 this 3 1846  
346 0 0 4 398 1784 0 0 0 2 4 this 3 1967  1 x 1 1848  
347 0 0 4 399 1784 0 0 0 1 4 this 3 1967  
348 0 0 6 400 1848 0 0 0 2 4 this 3 1846  1 n 1 1785  
349 0 0 4 401 1784 0 0 0 3 4 this 3 1967  1 n 1 1785  5 value 1 1848  
350 0 0 38 402 1848 0 0 0 2 4 this 3 1846  1 n 1 1785  
351 0 0 36 403 1784 0 0 0 3 4 this 3 1967  1 n 1 1785  5 value 1 1848  
352 0 0 38 404 1789 0 0 0 1 4 this 3 1846  
353 0 0 36 405 1784 0 0 0 2 4 this 3 1967  4 data 1 1789  
354 0 0 38 406 1789 0 0 0 3 4 this 3 1846  1 n 1 1785  5 count 1 1785  
355 0 0 4 407 1784 0 0 0 4 4 this 3 1967  1 n 1 1785  5 count 1 1785  4 data 1 1963  
356 0 0 6 408 1792 0 0 0 1 4 this 3 1846  
357 0 0 6 409 1792 0 0 0 1 4 this 3 1846  
358 0 0 6 410 1785 0 0 0 2 4 this 3 1846  6 param0 0 1848  
359 0 0 38 411 1789 0 0 0 1 4 this 3 1846  
360 0 0 38 412 1792 0 0 0 3 4 this 3 1967  4 view 1 1793  5 flags 1 1792  
361 0 0 36 413 1784 0 0 0 2 4 this 3 1846  4 view 1 1793  
362 0 0 39 414 1967 1202 0 0 2 4 this 3 1846  4 memo 1 1789  
363 0 0 15 417 1968 1224 0 0 1 4 copy 1 1859  
364 0 0 55 417 1968 1224 0 0 1 6 source 1 1789  
365 0 0 23 417 1968 1224 0 0 1 11 type_handle 5 1961  
366 0 0 7 418 1968 1224 0 0 2 1 n 1 1785  11 type_handle 5 1961  
367 0 0 4 419 1784 0 0 0 1 4 this 3 1968  
368 0 0 6 420 1785 0 0 0 1 4 this 3 1859  
369 0 0 4 421 1784 0 0 0 2 4 this 3 1968  1 x 1 1861  
370 0 0 4 422 1784 0 0 0 1 4 this 3 1968  
371 0 0 6 423 1861 0 0 0 2 4 this 3 1859  1 n 1 1785  
372 0 0 4 424 1784 0 0 0 3 4 this 3 1968  1 n 1 1785  5 value 1 1861  
373 0 0 38 425 1861 0 0 0 2 4 this 3 1859  1 n 1 1785  
374 0 0 36 426 1784 0 0 0 3 4 this 3 1968  1 n 1 1785  5 value 1 1861  
375 0 0 38 427 1789 0 0 0 1 4 this 3 1859  
376 0 0 36 428 1784 0 0 0 2 4 this 3 1968  4 data 1 1789  
377 0 0 38 429 1789 0 0 0 3 4 this 3 1859  1 n 1 1785  5 count 1 1785  
378 0 0 4 430 1784 0 0 0 4 4 this 3 1968  1 n 1 1785  5 count 1 1785  4 data 1 1963  
379 0 0 6 431 1792 0 0 0 1 4 this 3 1859  
380 0 0 6 432 1792 0 0 0 1 4 this 3 1859  
381 0 0 6 433 1785 0 0 0 2 4 this 3 1859  6 param0 0 1861  
382 0 0 38 434 1789 0 0 0 1 4 this 3 1859  
383 0 0 38 435 1792 0 0 0 3 4 this 3 1968  4 view 1 1793  5 flags 1 1792  
384 0 0 36 436 1784 0 0 0 2 4 this 3 1859  4 view 1 1793  
385 0 0 39 437 1968 1224 0 0 2 4 this 3 1859  4 memo 1 1789  
386 0 0 15 440 1969 1246 0 0 1 4 copy 1 1872  
387 0 0 55 440 1969 1246 0 0 1 6 source 1 1789  
388 0 0 23 440 1969 1246 0 0 1 11 type_handle 5 1961  
389 0 0 7 441 1969 1246 0 0 2 1 n 1 1785  11 type_handle 5 1961  
390 0 0 4 442 1784 0 0 0 1 4 this 3 1969  
391 0 0 6 443 1785 0 0 0 1 4 this 3 1872  
392 0 0 4 444 1784 0 0 0 2 4 this 3 1969  1 x 1 1874  
393 0 0 4 445 1784 0 0 0 1 4 this 3 1969  
394 0 0 6 446 1874 0 0 0 2 4 this 3 1872  1 n 1 1785  
395 0 0 4 447 1784 0 0 0 3 4 this 3 1969  1 n 1 1785  5 value 1 1874  
396 0 0 38 448 1874 0 0 0 2 4 this 3 1872  1 n 1 1785  
397 0 0 36 449 1784 0 0 0 3 4 this 3 1969  1 n 1 1785  5 value 1 1874  
398 0 0 38 450 1789 0 0 0 1 4 this 3 1872  
399 0 0 36 451 1784 0 0 0 2 4 this 3 1969  4 data 1 1789  
400 0 0 38 452 1789 0 0 0 3 4 this 3 1872  1 n 1 1785  5 count 1 1785  
401 0 0 4 453 1784 0 0 0 4 4 this 3 1969  1 n 1 1785  5 count 1 1785  4 data 1 1963  
402 0 0 6 454 1792 0 0 0 1 4 this 3 1872  
403 0 0 6 455 1792 0 0 0 1 4 this 3 1872  
404 0 0 6 456 1785 0 0 0 2 4 this 3 1872  6 param0 0 1874  
405 0 0 38 457 1789 0 0 0 1 4 this 3 1872  
406 0 0 38 458 1792 0 0 0 3 4 this 3 1969  4 view 1 1793  5 flags 1 1792  
407 0 0 36 459 1784 0 0 0 2 4 this 3 1872  4 view 1 1793  
408 0 0 39 460 1969 1246 0 0 2 4 this 3 1872  4 memo 1 1789  
409 0 0 15 463 1970 1268 0 0 1 4 copy 1 1885  
410 0 0 55 463 1970 1268 0 0 1 6 source 1 1789  
411 0 0 23 463 1970 1268 0 0 1 11 type_handle 5 1961  
412 0 0 7 464 1970 1268 0 0 2 1 n 1 1785  11 type_handle 5 1961  
413 0 0 4 465 1784 0 0 0 1 4 this 3 1970  
414 0 0 6 466 1785 0 0 0 1 4 this 3 1885  
415 0 0 4 467 1784 0 0 0 2 4 this 3 1970  1 x 1 1887  
416 0 0 4 468 1784 0 0 0 1 4 this 3 1970  
417 0 0 6 469 1887 0 0 0 2 4 this 3 1885  1 n 1 1785  
418 0 0 4 470 1784 0 0 0 3 4 this 3 1970  1 n 1 1785  5 value 1 1887  
419 0 0 38 471 1887 0 0 0 2 4 this 3 1885  1 n 1 1785  
420 0 0 36 472 1784 0 0 0 3 4 this 3 1970  1 n 1 1785  5 value 1 1887  
421 0 0 38 473 1789 0 0 0 1 4 this 3 1885  
422 0 0 36 474 1784 0 0 0 2 4 this 3 1970  4 data 1 1789  
423 0 0 38 475 1789 0 0 0 3 4 this 3 1885  1 n 1 1785  5 count 1 1785  
424 0 0 4 476 1784 0 0 0 4 4 this 3 1970  1 n 1 1785  5 count 1 1785  4 data 1 1963  
425 0 0 6 477 1792 0 0 0 1 4 this 3 1885  
426 0 0 6 478 1792 0 0 0 1 4 this 3 1885  
427 0 0 6 479 1785 0 0 0 2 4 this 3 1885  6 param0 0 1887  
428 0 0 38 480 1789 0 0 0 1 4 this 3 1885  
429 0 0 38 481 1792 0 0 0 3 4 this 3 1970  4 view 1 1793  5 flags 1 1792  
430 0 0 36 482 1784 0 0 0 2 4 this 3 1885  4 view 1 1793  
431 0 0 39 483 1970 1268 0 0 2 4 this 3 1885  4 memo 1 1789  
432 0 0 15 486 1971 1290 0 0 1 4 copy 1 1898  
433 0 0 55 486 1971 1290 0 0 1 6 source 1 1789  
434 0 0 23 486 1971 1290 0 0 1 11 type_handle 5 1961  
435 0 0 7 487 1971 1290 0 0 2 1 n 1 1785  11 type_handle 5 1961  
436 0 0 4 488 1784 0 0 0 1 4 this 3 1971  
437 0 0 6 489 1785 0 0 0 1 4 this 3 1898  
438 0 0 4 490 1784 0 0 0 2 4 this 3 1971  1 x 1 1900  
439 0 0 4 491 1784 0 0 0 1 4 this 3 1971  
440 0 0 6 492 1900 0 0 0 2 4 this 3 1898  1 n 1 1785  
441 0 0 4 493 1784 0 0 0 3 4 this 3 1971  1 n 1 1785  5 value 1 1900  
442 0 0 38 494 1900 0 0 0 2 4 this 3 1898  1 n 1 1785  
443 0 0 36 495 1784 0 0 0 3 4 this 3 1971  1 n 1 1785  5 value 1 1900  
444 0 0 38 496 1789 0 0 0 1 4 this 3 1898  
445 0 0 36 497 1784 0 0 0 2 4 this 3 1971  4 data 1 1789  
446 0 0 38 498 1789 0 0 0 3 4 this 3 1898  1 n 1 1785  5 count 1 1785  
447 0 0 4 499 1784 0 0 0 4 4 this 3 1971  1 n 1 1785  5 count 1 1785  4 data 1 1963  
448 0 0 6 500 1792 0 0 0 1 4 this 3 1898  
449 0 0 6 501 1792 0 0 0 1 4 this 3 1898  
450 0 0 6 502 1785 0 0 0 2 4 this 3 1898  6 param0 0 1900  
451 0 0 38 503 1789 0 0 0 1 4 this 3 1898  
452 0 0 38 504 1792 0 0 0 3 4 this 3 1971  4 view 1 1793  5 flags 1 1792  
453 0 0 36 505 1784 0 0 0 2 4 this 3 1898  4 view 1 1793  
454 0 0 39 506 1971 1290 0 0 2 4 this 3 1898  4 memo 1 1789  
455 0 0 15 509 1972 1312 0 0 1 4 copy 1 1911  
456 0 0 55 509 1972 1312 0 0 1 6 source 1 1789  
457 0 0 23 509 1972 1312 0 0 1 11 type_handle 5 1961  
458 0 0 7 510 1972 1312 0 0 2 1 n 1 1785  11 type_handle 5 1961  
459 0 0 4 511 1784 0 0 0 1 4 this 3 1972  
460 0 0 6 512 1785 0 0 0 1 4 this 3 1911  
461 0 0 4 513 1784 0 0 0 2 4 this 3 1972  1 x 1 1913  
462 0 0 4 514 1784 0 0 0 1 4 this 3 1972  
463 0 0 6 515 1913 0 0 0 2 4 this 3 1911  1 n 1 1785  
464 0 0 4 516 1784 0 0 0 3 4 this 3 1972  1 n 1 1785  5 value 1 1913  
465 0 0 38 517 1913 0 0 0 2 4 this 3 1911  1 n 1 1785  
466 0 0 36 518 1784 0 0 0 3 4 this 3 1972  1 n 1 1785  5 value 1 1913  
467 0 0 38 519 1789 0 0 0 1 4 this 3 1911  
468 0 0 36 520 1784 0 0 0 2 4 this 3 1972  4 data 1 1789  
469 0 0 38 521 1789 0 0 0 3 4 this 3 1911  1 n 1 1785  5 count 1 1785  
470 0 0 4 522 1784 0 0 0 4 4 this 3 1972  1 n 1 1785  5 count 1 1785  4 data 1 1963  
471 0 0 6 523 1792 0 0 0 1 4 this 3 1911  
472 0 0 6 524 1792 0 0 0 1 4 this 3 1911  
473 0 0 6 525 1785 0 0 0 2 4 this 3 1911  6 param0 0 1913  
474 0 0 38 526 1789 0 0 0 1 4 this 3 1911  
475 0 0 38 527 1792 0 0 0 3 4 this 3 1972  4 view 1 1793  5 flags 1 1792  
476 0 0 36 528 1784 0 0 0 2 4 this 3 1911  4 view 1 1793  
477 0 0 39 529 1972 1312 0 0 2 4 this 3 1911  4 memo 1 1789  
478 0 0 15 532 1973 1334 0 0 1 4 copy 1 1924  
479 0 0 55 532 1973 1334 0 0 1 6 source 1 1789  
480 0 0 23 532 1973 1334 0 0 1 11 type_handle 5 1961  
481 0 0 7 533 1973 1334 0 0 2 1 n 1 1785  11 type_handle 5 1961  
482 0 0 4 534 1784 0 0 0 1 4 this 3 1973  
483 0 0 6 535 1785 0 0 0 1 4 this 3 1924  
484 0 0 4 536 1784 0 0 0 2 4 this 3 1973  1 x 1 1926  
485 0 0 4 537 1784 0 0 0 1 4 this 3 1973  
486 0 0 6 538 1926 0 0 0 2 4 this 3 1924  1 n 1 1785  
487 0 0 4 539 1784 0 0 0 3 4 this 3 1973  1 n 1 1785  5 value 1 1926  
488 0 0 38 540 1926 0 0 0 2 4 this 3 1924  1 n 1 1785  
489 0 0 36 541 1784 0 0 0 3 4 this 3 1973  1 n 1 1785  5 value 1 1926  
490 0 0 38 542 1789 0 0 0 1 4 this 3 1924  
491 0 0 36 543 1784 0 0 0 2 4 this 3 1973  4 data 1 1789  
492 0 0 38 544 1789 0 0 0 3 4 this 3 1924  1 n 1 1785  5 count 1 1785  
493 0 0 4 545 1784 0 0 0 4 4 this 3 1973  1 n 1 1785  5 count 1 1785  4 data 1 1963  
494 0 0 6 546 1792 0 0 0 1 4 this 3 1924  
495 0 0 6 547 1792 0 0 0 1 4 this 3 1924  
496 0 0 6 548 1785 0 0 0 2 4 this 3 1924  6 param0 0 1926  
497 0 0 38 549 1789 0 0 0 1 4 this 3 1924  
498 0 0 38 550 1792 0 0 0 3 4 this 3 1973  4 view 1 1793  5 flags 1 1792  
499 0 0 36 551 1784 0 0 0 2 4 this 3 1924  4 view 1 1793  
500 0 0 39 552 1973 1334 0 0 2 4 this 3 1924  4 memo 1 1789  
501 0 0 15 555 1974 1356 0 0 1 4 copy 1 1937  
502 0 0 55 555 1974 1356 0 0 1 6 source 1 1789  
503 0 0 23 555 1974 1356 0 0 1 11 type_handle 5 1961  
504 0 0 7 556 1974 1356 0 0 2 1 n 1 1785  11 type_handle 5 1961  
505 0 0 4 557 1784 0 0 0 1 4 this 3 1974  
506 0 0 6 558 1785 0 0 0 1 4 this 3 1937  
507 0 0 4 559 1784 0 0 0 2 4 this 3 1974  1 x 1 1939  
508 0 0 4 560 1784 0 0 0 1 4 this 3 1974  
509 0 0 6 561 1939 0 0 0 2 4 this 3 1937  1 n 1 1785  
510 0 0 4 562 1784 0 0 0 3 4 this 3 1974  1 n 1 1785  5 value 1 1939  
511 0 0 38 563 1939 0 0 0 2 4 this 3 1937  1 n 1 1785  
512 0 0 36 564 1784 0 0 0 3 4 this 3 1974  1 n 1 1785  5 value 1 1939  
513 0 0 38 565 1789 0 0 0 1 4 this 3 1937  
514 0 0 36 566 1784 0 0 0 2 4 this 3 1974  4 data 1 1789  
515 0 0 38 567 1789 0 0 0 3 4 this 3 1937  1 n 1 1785  5 count 1 1785  
516 0 0 4 568 1784 0 0 0 4 4 this 3 1974  1 n 1 1785  5 count 1 1785  4 data 1 1963  
517 0 0 6 569 1792 0 0 0 1 4 this 3 1937  
518 0 0 6 570 1792 0 0 0 1 4 this 3 1937  
519 0 0 6 571 1785 0 0 0 2 4 this 3 1937  6 param0 0 1939  
520 0 0 38 572 1789 0 0 0 1 4 this 3 1937  
521 0 0 38 573 1792 0 0 0 3 4 this 3 1974  4 view 1 1793  5 flags 1 1792  
522 0 0 36 574 1784 0 0 0 2 4 this 3 1937  4 view 1 1793  
523 0 0 39 575 1974 1356 0 0 2 4 this 3 1937  4 memo 1 1789  
524 0 0 15 578 1975 1378 0 0 1 4 copy 1 1950  
525 0 0 55 578 1975 1378 0 0 1 6 source 1 1789  
526 0 0 23 578 1975 1378 0 0 1 11 type_handle 5 1961  
527 0 0 7 579 1975 1378 0 0 2 1 n 1 1785  11 type_handle 5 1961  
528 0 0 4 580 1784 0 0 0 1 4 this 3 1975  
529 0 0 6 581 1785 0 0 0 1 4 this 3 1950  
530 0 0 4 582 1784 0 0 0 2 4 this 3 1975  1 x 1 1952  
531 0 0 4 583 1784 0 0 0 1 4 this 3 1975  
532 0 0 6 584 1952 0 0 0 2 4 this 3 1950  1 n 1 1785  
533 0 0 4 585 1784 0 0 0 3 4 this 3 1975  1 n 1 1785  5 value 1 1952  
534 0 0 38 586 1952 0 0 0 2 4 this 3 1950  1 n 1 1785  
535 0 0 36 587 1784 0 0 0 3 4 this 3 1975  1 n 1 1785  5 value 1 1952  
536 0 0 38 588 1789 0 0 0 1 4 this 3 1950  
537 0 0 36 589 1784 0 0 0 2 4 this 3 1975  4 data 1 1789  
538 0 0 38 590 1789 0 0 0 3 4 this 3 1950  1 n 1 1785  5 count 1 1785  
539 0 0 4 591 1784 0 0 0 4 4 this 3 1975  1 n 1 1785  5 count 1 1785  4 data 1 1963  
540 0 0 6 592 1792 0 0 0 1 4 this 3 1950  
541 0 0 6 593 1792 0 0 0 1 4 this 3 1950  
542 0 0 6 594 1785 0 0 0 2 4 this 3 1950  6 param0 0 1952  
543 0 0 38 595 1789 0 0 0 1 4 this 3 1950  
544 0 0 38 596 1792 0 0 0 3 4 this 3 1975  4 view 1 1793  5 flags 1 1792  
545 0 0 36 597 1784 0 0 0 2 4 this 3 1950  4 view 1 1793  
546 0 0 39 598 1975 1378 0 0 2 4 this 3 1950  4 memo 1 1789  
547 0 0 7 602 1978 1388 0 0 1 4 this 3 1976  
548 0 0 6 603 1798 0 0 253 /**
 * Any kind of volume might be empty.  This is a degenerate volume that
 * contains no points; it's not the same as, for instance, a sphere with
 * radius zero, since that contains one point (the center).  It intersects
 * with no other volumes.
 */ 1 4 this 3 1976  
549 0 0 6 604 1798 0 0 536 /**
 * The other side of the empty coin is an infinite volume.  This is a
 * degenerate state of a normally finite volume that contains all points.
 * (Note that some kinds of infinite bounding volumes, like binary separating
 * planes, do not contain all points and thus correctly return is_infinite()
 * == false, even though they are technically infinite.  This is a special
 * case of the word 'infinite' meaning the volume covers all points in space.)
 *
 * It completely intersects with all other volumes except empty volumes.
 */ 1 4 this 3 1976  
550 0 0 4 605 1784 0 0 135 /**
 * Marks the volume as infinite, even if it is normally finite.  You can think
 * of this as an infinite extend_by() operation.
 */ 1 4 this 3 1978  
551 0 0 6 606 1798 0 0 72 /**
 * Increases the size of the volume to include the given volume.
 */ 2 4 this 3 1978  3 vol 1 1976  
552 0 0 6 608 1792 0 0 128 /**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated volume.
 */ 2 4 this 3 1976  3 vol 1 1976  
553 0 0 4 609 1784 0 0 0 2 4 this 3 1976  3 out 1 1802  
554 0 0 4 610 1784 0 0 10 /**
 *
 */ 3 4 this 3 1976  3 out 1 1802  12 indent_level 5 1792  
555 0 0 7 612 1961 0 0 0 0 
556 0 0 6 615 1798 0 0 72 /**
 * Increases the size of the volume to include the given volume.
 */ 2 4 this 3 1979  3 vol 1 1704  
557 0 0 6 615 1798 0 0 71 /**
 * Increases the size of the volume to include the given point.
 */ 2 4 this 3 1979  5 point 1 1980  
558 0 0 6 616 1792 0 0 128 /**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated volume.
 */ 2 4 this 3 1704  3 vol 1 1704  
559 0 0 6 616 1792 0 0 127 /**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated point.
 */ 2 4 this 3 1704  5 point 1 1980  
560 0 0 6 616 1792 0 0 134 /**
 * Returns the appropriate set of IntersectionFlags to indicate the amount of
 * intersection with the indicated line segment.
 */ 3 4 this 3 1704  1 a 1 1980  1 b 1 1980  
561 0 0 7 617 1982 0 0 0 1 4 this 3 1704  
562 0 0 4 618 1784 0 0 0 2 4 this 3 1979  3 mat 1 1983  
563 0 0 7 619 1961 0 0 0 0 
564 0 0 7 622 1982 0 0 0 1 4 this 3 1987  
565 0 0 7 623 1982 0 0 0 1 4 this 3 1987  
566 0 0 6 624 1678 0 0 10 /**
 *
 */ 1 4 this 3 1987  
567 0 0 7 632 1961 0 0 0 0 
568 0 0 7 635 1989 1402 0 56 /**
 * Constructs a meaningless degenerate parabola.
 */ 0 
569 0 0 15 635 1989 1402 0 10 /**
 *
 */ 1 4 copy 1 1990  
570 0 0 23 635 1989 1402 0 139 /**
 * Constructs a parabola given the three points of the parametric equation:
 * the acceleration, initial velocity, and start point.
 */ 3 1 a 1 1874  1 b 1 1874  1 c 1 1874  
571 0 0 6 636 1989 0 0 0 2 4 this 3 1989  4 copy 1 1990  
572 0 0 4 638 1784 0 0 59 /**
 * Transforms the parabola by the indicated matrix.
 */ 2 4 this 3 1989  3 mat 1 1992  
573 0 0 6 639 1874 0 0 94 /**
 * Returns the first point of the parabola's parametric equation: the
 * acceleration.
 */ 1 4 this 3 1990  
574 0 0 6 640 1874 0 0 99 /**
 * Returns the second point of the parabola's parametric equation: the initial
 * velocity.
 */ 1 4 this 3 1990  
575 0 0 6 641 1874 0 0 93 /**
 * Returns the third point of the parabola's parametric equation: the start
 * point.
 */ 1 4 this 3 1990  
576 0 0 7 642 1994 0 0 56 /**
 * Computes the point on the parabola at time t.
 */ 2 4 this 3 1990  1 t 1 1679  
577 0 0 4 643 1784 0 0 10 /**
 *
 */ 2 4 this 3 1990  3 out 1 1802  
578 0 0 4 644 1784 0 0 10 /**
 *
 */ 3 4 this 3 1990  3 out 1 1802  12 indent_level 5 1792  
579 0 0 4 645 1784 0 0 336 /**
 * Writes the parabola to the Datagram using add_float32() or add_float64(),
 * depending on the type of floats in the parabola, regardless of the setting
 * of Datagram::set_stdfloat_double().  This is appropriate when you want to
 * write a fixed-width value to the datagram, especially when you are not
 * writing a bam file.
 */ 2 4 this 3 1990  11 destination 1 1995  
580 0 0 4 646 1784 0 0 116 /**
 * Reads the parabola from the Datagram using get_float32() or get_float64().
 * See write_datagram_fixed().
 */ 2 4 this 3 1989  6 source 1 1997  
581 0 0 4 647 1784 0 0 207 /**
 * Writes the parabola to the Datagram using add_stdfloat().  This is
 * appropriate when you want to write the vector using the standard width
 * setting, especially when you are writing a bam file.
 */ 2 4 this 3 1990  11 destination 1 1995  
582 0 0 4 648 1784 0 0 69 /**
 * Reads the parabola from the Datagram using get_stdfloat().
 */ 2 4 this 3 1989  6 source 1 1997  
583 0 0 7 650 1999 1416 0 56 /**
 * Constructs a meaningless degenerate parabola.
 */ 0 
584 0 0 15 650 1999 1416 0 10 /**
 *
 */ 1 4 copy 1 2000  
585 0 0 23 650 1999 1416 0 139 /**
 * Constructs a parabola given the three points of the parametric equation:
 * the acceleration, initial velocity, and start point.
 */ 3 1 a 1 1861  1 b 1 1861  1 c 1 1861  
586 0 0 6 651 1999 0 0 0 2 4 this 3 1999  4 copy 1 2000  
587 0 0 4 653 1784 0 0 59 /**
 * Transforms the parabola by the indicated matrix.
 */ 2 4 this 3 1999  3 mat 1 2002  
588 0 0 6 654 1861 0 0 94 /**
 * Returns the first point of the parabola's parametric equation: the
 * acceleration.
 */ 1 4 this 3 2000  
589 0 0 6 655 1861 0 0 99 /**
 * Returns the second point of the parabola's parametric equation: the initial
 * velocity.
 */ 1 4 this 3 2000  
590 0 0 6 656 1861 0 0 93 /**
 * Returns the third point of the parabola's parametric equation: the start
 * point.
 */ 1 4 this 3 2000  
591 0 0 7 657 2006 0 0 56 /**
 * Computes the point on the parabola at time t.
 */ 2 4 this 3 2000  1 t 1 2005  
592 0 0 4 658 1784 0 0 10 /**
 *
 */ 2 4 this 3 2000  3 out 1 1802  
593 0 0 4 659 1784 0 0 10 /**
 *
 */ 3 4 this 3 2000  3 out 1 1802  12 indent_level 5 1792  
594 0 0 4 660 1784 0 0 336 /**
 * Writes the parabola to the Datagram using add_float32() or add_float64(),
 * depending on the type of floats in the parabola, regardless of the setting
 * of Datagram::set_stdfloat_double().  This is appropriate when you want to
 * write a fixed-width value to the datagram, especially when you are not
 * writing a bam file.
 */ 2 4 this 3 2000  11 destination 1 1995  
595 0 0 4 661 1784 0 0 116 /**
 * Reads the parabola from the Datagram using get_float32() or get_float64().
 * See write_datagram_fixed().
 */ 2 4 this 3 1999  6 source 1 1997  
596 0 0 4 662 1784 0 0 207 /**
 * Writes the parabola to the Datagram using add_stdfloat().  This is
 * appropriate when you want to write the vector using the standard width
 * setting, especially when you are writing a bam file.
 */ 2 4 this 3 2000  11 destination 1 1995  
597 0 0 4 663 1784 0 0 69 /**
 * Reads the parabola from the Datagram using get_stdfloat().
 */ 2 4 this 3 1999  6 source 1 1997  
598 0 0 7 667 2007 1447 0 159 /**
 * Creates a default plane.  This plane happens to intersect the origin,
 * perpendicular to the Z axis.  It's not clear how useful a default plane is.
 */ 0 
599 0 0 15 667 2007 1447 0 0 1 6 param0 0 2008  
600 0 0 23 667 2007 1447 0 176 /**
 * Constructs a plane given three counter-clockwise points, as seen from the
 * front of the plane (that is, viewed from the end of the normal vector,
 * looking down).
 */ 3 1 a 1 2010  1 b 1 2010  1 c 1 2010  
601 0 0 23 667 2007 1447 0 10 /**
 *
 */ 1 4 copy 1 2012  
602 0 0 23 667 2007 1447 0 92 /**
 * Constructs a plane given a surface normal vector and a point within the
 * plane.
 */ 2 6 normal 1 2014  5 point 1 2010  
603 0 0 23 667 2007 1447 0 73 /**
 * Constructs a plane given the four terms of the plane equation.
 */ 4 1 a 1 1679  1 b 1 1679  1 c 1 1679  1 d 1 1679  
604 0 0 7 668 2007 1447 0 0 2 4 this 3 2008  3 mat 1 1809  
605 0 0 7 668 2007 1447 0 0 2 4 this 3 2008  3 mat 1 1992  
606 0 0 6 669 2007 0 0 0 2 4 this 3 2007  3 mat 1 1992  
607 0 0 4 670 1784 0 0 56 /**
 * Transforms the plane by the indicated matrix.
 */ 2 4 this 3 2007  3 mat 1 1992  
608 0 0 7 671 2007 1447 0 0 1 4 this 3 2008  
609 0 0 7 672 2017 0 0 121 /**
 * This computes a transform matrix that reflects the universe to the other
 * side of the plane, as in a mirror.
 */ 1 4 this 3 2008  
610 0 0 7 673 2018 0 0 51 /**
 * Returns the surface normal of the plane.
 */ 1 4 this 3 2008  
611 0 0 7 674 1994 0 0 145 /**
 * Returns an arbitrary point in the plane.  This can be used along with the
 * normal returned by get_normal() to reconstruct the plane.
 */ 1 4 this 3 2008  
612 0 0 6 675 1679 0 0 328 /**
 * Returns the straight-line shortest distance from the point to the plane.
 * The returned value is positive if the point is in front of the plane (on
 * the side with the normal), or negative in the point is behind the plane (on
 * the opposite side from the normal). It's zero if the point is exactly in
 * the plane.
 */ 2 4 this 3 2008  5 point 1 2010  
613 0 0 6 676 1798 0 0 139 /**
 * Normalizes the plane in place.  Returns true if the plane was normalized,
 * false if the plane had a zero-length normal vector.
 */ 1 4 this 3 2007  
614 0 0 7 677 2007 1447 0 155 /**
 * Normalizes the plane and returns the normalized plane as a copy.  If the
 * plane's normal was a zero-length vector, the same plane is returned.
 */ 1 4 this 3 2008  
615 0 0 7 678 1994 0 0 86 /**
 * Returns the point within the plane nearest to the indicated point in space.
 */ 2 4 this 3 2008  5 point 1 2010  
616 0 0 4 679 1784 0 0 115 /**
 * Convenience method that flips the plane in-place.  This is done by simply
 * flipping the normal vector.
 */ 1 4 this 3 2007  
617 0 0 6 680 1798 0 0 325 /**
 * Returns true if the plane intersects the infinite line passing through
 * points p1 and p2, false if the line is parallel.  The points p1 and p2 are
 * used only to define the Euclidean line; they have no other bearing on the
 * intersection test.  If true, sets intersection_point to the point of
 * intersection.
 */ 4 4 this 3 2008  18 intersection_point 1 1994  2 p1 1 2010  2 p2 1 2010  
618 0 0 6 681 1798 0 0 300 /**
 * Returns true if the two planes intersect, false if they do not.  If they do
 * intersect, then from and delta are filled in with the parametric
 * representation of the line of intersection: that is, from is a point on
 * that line, and delta is a vector showing the direction of the line.
 */ 4 4 this 3 2008  4 from 1 1994  5 delta 1 2018  5 other 1 2008  
619 0 0 4 683 1784 0 0 10 /**
 *
 */ 2 4 this 3 2008  3 out 1 1802  
620 0 0 4 684 1784 0 0 10 /**
 *
 */ 3 4 this 3 2008  3 out 1 1802  12 indent_level 5 1792  
621 0 0 6 685 1963 0 0 58 /**
 * Returns a string representation of this LPlane.
 */ 1 4 this 3 2008  
622 0 0 7 689 2019 1467 0 159 /**
 * Creates a default plane.  This plane happens to intersect the origin,
 * perpendicular to the Z axis.  It's not clear how useful a default plane is.
 */ 0 
623 0 0 15 689 2019 1467 0 0 1 6 param0 0 2020  
624 0 0 23 689 2019 1467 0 176 /**
 * Constructs a plane given three counter-clockwise points, as seen from the
 * front of the plane (that is, viewed from the end of the normal vector,
 * looking down).
 */ 3 1 a 1 2021  1 b 1 2021  1 c 1 2021  
625 0 0 23 689 2019 1467 0 10 /**
 *
 */ 1 4 copy 1 2022  
626 0 0 23 689 2019 1467 0 92 /**
 * Constructs a plane given a surface normal vector and a point within the
 * plane.
 */ 2 6 normal 1 2024  5 point 1 2021  
627 0 0 23 689 2019 1467 0 73 /**
 * Constructs a plane given the four terms of the plane equation.
 */ 4 1 a 1 2005  1 b 1 2005  1 c 1 2005  1 d 1 2005  
628 0 0 7 690 2019 1467 0 0 2 4 this 3 2020  3 mat 1 1786  
629 0 0 7 690 2019 1467 0 0 2 4 this 3 2020  3 mat 1 2002  
630 0 0 6 691 2019 0 0 0 2 4 this 3 2019  3 mat 1 2002  
631 0 0 4 692 1784 0 0 56 /**
 * Transforms the plane by the indicated matrix.
 */ 2 4 this 3 2019  3 mat 1 2002  
632 0 0 7 693 2019 1467 0 0 1 4 this 3 2020  
633 0 0 7 694 2027 0 0 121 /**
 * This computes a transform matrix that reflects the universe to the other
 * side of the plane, as in a mirror.
 */ 1 4 this 3 2020  
634 0 0 7 695 2028 0 0 51 /**
 * Returns the surface normal of the plane.
 */ 1 4 this 3 2020  
635 0 0 7 696 2006 0 0 145 /**
 * Returns an arbitrary point in the plane.  This can be used along with the
 * normal returned by get_normal() to reconstruct the plane.
 */ 1 4 this 3 2020  
636 0 0 6 697 2005 0 0 328 /**
 * Returns the straight-line shortest distance from the point to the plane.
 * The returned value is positive if the point is in front of the plane (on
 * the side with the normal), or negative in the point is behind the plane (on
 * the opposite side from the normal). It's zero if the point is exactly in
 * the plane.
 */ 2 4 this 3 2020  5 point 1 2021  
637 0 0 6 698 1798 0 0 139 /**
 * Normalizes the plane in place.  Returns true if the plane was normalized,
 * false if the plane had a zero-length normal vector.
 */ 1 4 this 3 2019  
638 0 0 7 699 2019 1467 0 155 /**
 * Normalizes the plane and returns the normalized plane as a copy.  If the
 * plane's normal was a zero-length vector, the same plane is returned.
 */ 1 4 this 3 2020  
639 0 0 7 700 2006 0 0 86 /**
 * Returns the point within the plane nearest to the indicated point in space.
 */ 2 4 this 3 2020  5 point 1 2021  
640 0 0 4 701 1784 0 0 115 /**
 * Convenience method that flips the plane in-place.  This is done by simply
 * flipping the normal vector.
 */ 1 4 this 3 2019  
641 0 0 6 702 1798 0 0 325 /**
 * Returns true if the plane intersects the infinite line passing through
 * points p1 and p2, false if the line is parallel.  The points p1 and p2 are
 * used only to define the Euclidean line; they have no other bearing on the
 * intersection test.  If true, sets intersection_point to the point of
 * intersection.
 */ 4 4 this 3 2020  18 intersection_point 1 2006  2 p1 1 2021  2 p2 1 2021  
642 0 0 6 703 1798 0 0 300 /**
 * Returns true if the two planes intersect, false if they do not.  If they do
 * intersect, then from and delta are filled in with the parametric
 * representation of the line of intersection: that is, from is a point on
 * that line, and delta is a vector showing the direction of the line.
 */ 4 4 this 3 2020  4 from 1 2006  5 delta 1 2028  5 other 1 2020  
643 0 0 4 705 1784 0 0 10 /**
 *
 */ 2 4 this 3 2020  3 out 1 1802  
644 0 0 4 706 1784 0 0 10 /**
 *
 */ 3 4 this 3 2020  3 out 1 1802  12 indent_level 5 1792  
645 0 0 6 707 1963 0 0 58 /**
 * Returns a string representation of this LPlane.
 */ 1 4 this 3 2020  
646 0 0 7 714 2029 1478 0 42 /**
 * Constructs an empty box object.
 */ 0 
647 0 0 7 714 2029 1478 0 44 /**
 * Constructs a specific box object.
 */ 2 3 min 1 1980  3 max 1 1980  
648 0 0 4 715 1784 0 0 0 2 4 this 3 2029  4 size 1 2030  
649 0 0 6 718 1792 0 0 68 /**
 * Returns 8: the number of vertices of a rectangular solid.
 */ 1 4 this 3 2031  
650 0 0 7 719 1982 0 0 59 /**
 * Returns the nth vertex of the rectangular solid.
 */ 2 4 this 3 2031  1 n 1 1792  
651 0 0 6 721 1792 0 0 65 /**
 * Returns 6: the number of faces of a rectangular solid.
 */ 1 4 this 3 2031  
652 0 0 7 722 2033 0 0 57 /**
 * Returns the nth face of the rectangular solid.
 */ 2 4 this 3 2031  1 n 1 1792  
653 0 0 4 726 1784 0 0 63 /**
 * Sets the min and max point of the rectangular solid.
 */ 3 4 this 3 2029  3 min 1 1980  3 max 1 1980  
654 0 0 7 727 1961 0 0 0 0 
655 0 0 7 730 2034 1486 0 10 /**
 *
 */ 0 
656 0 0 15 730 2034 1486 0 0 1 6 param0 0 2035  
657 0 0 4 731 1784 0 0 57 /**
 * Sets up a two-dimensional orthographic frustum
 */ 1 4 this 3 2034  
658 0 0 4 731 1784 0 0 57 /**
 * Sets up a two-dimensional orthographic frustum
 */ 5 4 this 3 2034  1 l 1 1679  1 r 1 1679  1 t 1 1679  1 b 1 1679  
659 0 0 4 732 1784 0 0 32 /**
 * Behaves like gluOrtho
 */ 3 4 this 3 2034  5 fnear 1 1679  4 ffar 1 1679  
660 0 0 4 732 1784 0 0 32 /**
 * Behaves like gluOrtho
 */ 7 4 this 3 2034  5 fnear 1 1679  4 ffar 1 1679  1 l 1 1679  1 r 1 1679  1 t 1 1679  1 b 1 1679  
661 0 0 4 733 1784 0 0 79 /**
 * Behaves like gluPerspective (Aspect = width/height, Yfov in degrees)
 */ 5 4 this 3 2034  4 xfov 1 1679  6 aspect 1 1679  5 fnear 1 1679  4 ffar 1 1679  
662 0 0 4 734 1784 0 0 10 /**
 *
 */ 5 4 this 3 2034  4 yfov 1 1679  6 aspect 1 1679  5 fnear 1 1679  4 ffar 1 1679  
663 0 0 4 735 1784 0 0 10 /**
 *
 */ 5 4 this 3 2034  4 xfov 1 1679  4 yfov 1 1679  5 fnear 1 1679  4 ffar 1 1679  
664 0 0 7 739 2037 1494 0 10 /**
 *
 */ 0 
665 0 0 15 739 2037 1494 0 0 1 6 param0 0 2038  
666 0 0 4 740 1784 0 0 57 /**
 * Sets up a two-dimensional orthographic frustum
 */ 1 4 this 3 2037  
667 0 0 4 740 1784 0 0 57 /**
 * Sets up a two-dimensional orthographic frustum
 */ 5 4 this 3 2037  1 l 1 2005  1 r 1 2005  1 t 1 2005  1 b 1 2005  
668 0 0 4 741 1784 0 0 32 /**
 * Behaves like gluOrtho
 */ 3 4 this 3 2037  5 fnear 1 2005  4 ffar 1 2005  
669 0 0 4 741 1784 0 0 32 /**
 * Behaves like gluOrtho
 */ 7 4 this 3 2037  5 fnear 1 2005  4 ffar 1 2005  1 l 1 2005  1 r 1 2005  1 t 1 2005  1 b 1 2005  
670 0 0 4 742 1784 0 0 79 /**
 * Behaves like gluPerspective (Aspect = width/height, Yfov in degrees)
 */ 5 4 this 3 2037  4 xfov 1 2005  6 aspect 1 2005  5 fnear 1 2005  4 ffar 1 2005  
671 0 0 4 743 1784 0 0 10 /**
 *
 */ 5 4 this 3 2037  4 yfov 1 2005  6 aspect 1 2005  5 fnear 1 2005  4 ffar 1 2005  
672 0 0 4 744 1784 0 0 10 /**
 *
 */ 5 4 this 3 2037  4 xfov 1 2005  4 yfov 1 2005  5 fnear 1 2005  4 ffar 1 2005  
673 0 0 23 752 2043 1501 0 10 /**
 *
 */ 3 7 frustum 1 2040  8 is_ortho 1 1798  2 cs 5 2042  
674 0 0 23 752 2043 1501 0 10 /**
 *
 */ 8 3 fll 1 1980  3 flr 1 1980  3 fur 1 1980  3 ful 1 1980  3 nll 1 1980  3 nlr 1 1980  3 nur 1 1980  3 nul 1 1980  
675 0 0 6 753 1792 0 0 61 /**
 * Returns 8: the number of vertices of a hexahedron.
 */ 1 4 this 3 2044  
676 0 0 7 754 1982 0 0 52 /**
 * Returns the nth vertex of the hexahedron.
 */ 2 4 this 3 2044  1 n 1 1792  
677 0 0 6 756 1792 0 0 58 /**
 * Returns 6: the number of faces of a hexahedron.
 */ 1 4 this 3 2044  
678 0 0 7 757 2033 0 0 50 /**
 * Returns the nth face of the hexahedron.
 */ 2 4 this 3 2044  1 n 1 1792  
679 0 0 7 761 1961 0 0 0 0 
680 0 0 7 764 2046 1509 0 10 /**
 *
 */ 2 1 a 1 1980  1 b 1 1980  
681 0 0 4 765 1784 0 0 0 2 4 this 3 2046  4 size 1 2030  
682 0 0 6 768 1980 0 0 57 /**
 * Returns the first point that defines the line.
 */ 1 4 this 3 2047  
683 0 0 7 769 1982 0 0 58 /**
 * Returns the second point that defines the line.
 */ 1 4 this 3 2047  
684 0 0 7 770 1961 0 0 0 0 
685 0 0 7 773 2049 1516 0 65 /**
 * Constructs an empty "plane" that has no intersections.
 */ 0 
686 0 0 23 773 2049 1516 0 10 /**
 *
 */ 1 5 plane 1 2050  
687 0 0 4 774 1784 0 0 0 2 4 this 3 2049  4 size 1 2030  
688 0 0 6 777 2050 0 0 10 /**
 *
 */ 1 4 this 3 2051  
689 0 0 7 780 1961 0 0 0 0 
690 0 0 7 783 2053 1526 0 38 /**
 * Constructs an empty sphere.
 */ 0 
691 0 0 7 783 2053 1526 0 40 /**
 * Constructs a specific sphere.
 */ 2 6 center 1 1980  6 radius 1 1678  
692 0 0 4 784 1784 0 0 0 2 4 this 3 2053  4 size 1 2030  
693 0 0 7 787 1982 0 0 10 /**
 *
 */ 1 4 this 3 2054  
694 0 0 6 788 1678 0 0 10 /**
 *
 */ 1 4 this 3 2054  
695 0 0 4 789 1784 0 0 47 /**
 * Sets the center point of the sphere.
 */ 2 4 this 3 2053  6 center 1 1980  
696 0 0 4 790 1784 0 0 41 /**
 * Sets the radius of the sphere.
 */ 2 4 this 3 2053  6 radius 1 1678  
697 0 0 7 793 1961 0 0 0 0 
698 0 0 7 796 2056 1536 0 44 /**
 * Constructs an empty intersection.
 */ 0 
699 0 0 4 797 1784 0 0 0 2 4 this 3 2056  4 size 1 2030  
700 0 0 6 800 1792 0 0 64 /**
 * Returns the number of components in the intersection.
 */ 1 4 this 3 2057  
701 0 0 7 801 1704 0 0 57 /**
 * Returns the nth component in the intersection.
 */ 2 4 this 3 2057  1 n 1 1792  
702 0 0 4 806 1784 0 0 50 /**
 * Removes all components from the volume.
 */ 1 4 this 3 2056  
703 0 0 4 807 1784 0 0 373 /**
 * Adds a new component to the volume.  This does not necessarily increase the
 * total number of components by one, and you may or may not be able to find
 * this component in the volume by a subsequent call to get_component();
 * certain optimizations may prevent the component from being added, or have
 * other unexpected effects on the total set of components.
 */ 2 4 this 3 2056  9 component 1 1704  
704 0 0 7 808 1961 0 0 0 0 
705 0 0 15 813 2061 1541 0 0 1 6 param0 0 2059  
706 0 0 7 813 2061 1541 0 35 /* initializes mt[N] with a seed */ 1 4 seed 1 2062  
707 0 0 6 814 2062 0 0 58 /* generates a random number on [0,0x7fffffff]-interval */ 1 4 this 3 2061  
708 0 0 7 818 2063 1544 0 10 /**
 *
 */ 0 
709 0 0 7 819 1961 0 0 0 0 
710 0 0 7 822 2064 1555 0 37 /**
 * Constructs an empty union.
 */ 0 
711 0 0 4 823 1784 0 0 0 2 4 this 3 2064  4 size 1 2030  
712 0 0 6 826 1792 0 0 57 /**
 * Returns the number of components in the union.
 */ 1 4 this 3 2065  
713 0 0 7 827 1704 0 0 50 /**
 * Returns the nth component in the union.
 */ 2 4 this 3 2065  1 n 1 1792  
714 0 0 4 830 1784 0 0 50 /**
 * Removes all components from the volume.
 */ 1 4 this 3 2064  
715 0 0 4 831 1784 0 0 373 /**
 * Adds a new component to the volume.  This does not necessarily increase the
 * total number of components by one, and you may or may not be able to find
 * this component in the volume by a subsequent call to get_component();
 * certain optimizations may prevent the component from being added, or have
 * other unexpected effects on the total set of components.
 */ 2 4 this 3 2064  9 component 1 1704  
716 0 0 4 832 1784 0 0 104 /**
 * Removes from the union any components that have no intersection with the
 * indicated volume.
 */ 2 4 this 3 2064  6 volume 1 1976  
717 0 0 7 833 1961 0 0 0 0 
718 0 0 15 836 2069 1563 0 10 /**
 *
 */ 1 4 copy 1 2067  
719 0 0 7 836 2069 1563 0 108 /**
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */ 1 4 seed 5 2062  
720 0 0 6 837 2069 0 0 0 2 4 this 3 2069  4 copy 1 2067  
721 0 0 6 838 1792 0 0 60 /**
 * Returns a random integer in the range [0, range).
 */ 2 4 this 3 2069  5 range 1 1792  
722 0 0 6 839 2005 0 0 59 /**
 * Returns a random double in the range [0, range).
 */ 2 4 this 3 2069  5 range 1 2005  
723 0 0 6 840 2005 0 0 60 /**
 * Returns a random double in the range [-0.5, 0.5).
 */ 1 4 this 3 2069  
724 0 0 6 841 2062 0 0 77 /**
 * Returns a random seed value for the next global Randomizer object.
 */ 0 
725 0 0 6 842 2062 0 0 126 /**
 * Returns a unique seed value based on the seed value passed to this
 * Randomizer object (and on its current state).
 */ 1 4 this 3 2069  
726 0 0 6 845 2062 0 0 127 /**
 * Returns a unique seed value based on the seed value passed to this
 * PerlinNoise object (and on its current state).
 */ 1 4 this 3 2070  
727 0 0 7 848 2071 1571 0 131 /**
 * Randomizes the tables to make a unique noise function.  Uses a default
 * scale (noise frequency), table size, and seed.
 */ 0 
728 0 0 15 848 2071 1571 0 96 /**
 * Makes an exact copy of the existing PerlinNoise object, including its
 * random seed.
 */ 1 4 copy 1 2072  
729 0 0 7 848 2071 1571 0 169 /**
 * Randomizes the tables to make a unique noise function.
 *
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */ 4 2 sx 1 2005  2 sy 1 2005  10 table_size 5 1792  4 seed 5 2062  
730 0 0 6 849 2071 0 0 0 2 4 this 3 2071  4 copy 1 2072  
731 0 0 4 850 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2071  5 scale 1 1822  
732 0 0 4 850 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2071  5 scale 1 1835  
733 0 0 4 850 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2071  5 scale 1 2005  
734 0 0 4 850 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 3 4 this 3 2071  2 sx 1 2005  2 sy 1 2005  
735 0 0 6 851 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2072  5 value 1 1822  
736 0 0 6 851 1679 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2072  5 value 1 1835  
737 0 0 6 851 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 3 4 this 3 2072  1 x 1 2005  1 y 1 2005  
738 0 0 6 852 2005 0 0 0 2 4 this 3 2072  5 value 1 1822  
739 0 0 6 852 1679 0 0 0 2 4 this 3 2072  5 value 1 1835  
740 0 0 6 852 2005 0 0 0 3 4 this 3 2072  1 x 1 2005  1 y 1 2005  
741 0 0 7 855 2074 1577 0 131 /**
 * Randomizes the tables to make a unique noise function.  Uses a default
 * scale (noise frequency), table size, and seed.
 */ 0 
742 0 0 15 855 2074 1577 0 96 /**
 * Makes an exact copy of the existing PerlinNoise object, including its
 * random seed.
 */ 1 4 copy 1 2075  
743 0 0 7 855 2074 1577 0 169 /**
 * Randomizes the tables to make a unique noise function.
 *
 * If seed is nonzero, it is used to define the tables; if it is zero a random
 * seed is generated.
 */ 5 2 sx 1 2005  2 sy 1 2005  2 sz 1 2005  10 table_size 5 1792  4 seed 5 2062  
744 0 0 6 856 2074 0 0 0 2 4 this 3 2074  4 copy 1 2075  
745 0 0 4 857 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2074  5 scale 1 1861  
746 0 0 4 857 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2074  5 scale 1 1874  
747 0 0 4 857 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 2 4 this 3 2074  5 scale 1 2005  
748 0 0 4 857 1784 0 0 54 /**
 * Changes the scale (frequency) of the noise.
 */ 4 4 this 3 2074  2 sx 1 2005  2 sy 1 2005  2 sz 1 2005  
749 0 0 6 858 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2075  5 value 1 1861  
750 0 0 6 858 1679 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2075  5 value 1 1874  
751 0 0 6 858 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 4 4 this 3 2075  1 x 1 2005  1 y 1 2005  1 z 1 2005  
752 0 0 6 859 2005 0 0 0 2 4 this 3 2075  5 value 1 1861  
753 0 0 6 859 1679 0 0 0 2 4 this 3 2075  5 value 1 1874  
754 0 0 6 859 2005 0 0 0 4 4 this 3 2075  1 x 1 2005  1 y 1 2005  1 z 1 2005  
755 0 0 7 919 2077 1585 0 122 /**
 * Creates a StackedPerlinNoise2 object with no levels.  You should call
 * add_level() to add each level by hand.
 */ 0 
756 0 0 15 919 2077 1585 0 111 /**
 * Creates an exact duplicate of the existing StackedPerlinNoise2 object,
 * including the random seed.
 */ 1 4 copy 1 2078  
757 0 0 7 919 2077 1585 0 318 /**
 * Creates num_levels nested PerlinNoise2 objects.  Each stacked Perlin object
 * will have a scale of 1 scale_factor times the previous object (so that it
 * is higher-frequency, if scale_factor > 1), and an amplitude of amp_scale
 * times the previous object (so that it is less important, if amp_scale < 1).
 */ 7 2 sx 1 2005  2 sy 1 2005  10 num_levels 5 1792  12 scale_factor 5 2005  9 amp_scale 5 2005  10 table_size 5 1792  4 seed 5 2062  
758 0 0 6 920 2077 0 0 0 2 4 this 3 2077  4 copy 1 2078  
759 0 0 4 921 1784 0 0 95 /**
 * Adds an arbitrary PerlinNoise2 object, and an associated amplitude, to the
 * stack.
 */ 3 4 this 3 2077  5 level 1 2072  3 amp 5 2005  
760 0 0 4 922 1784 0 0 98 /**
 * Removes all levels from the stack.  You must call add_level() again to
 * restore them.
 */ 1 4 this 3 2077  
761 0 0 6 923 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2077  5 value 1 1822  
762 0 0 6 923 1679 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2077  5 value 1 1835  
763 0 0 6 923 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 3 4 this 3 2077  1 x 1 2005  1 y 1 2005  
764 0 0 6 924 2005 0 0 0 2 4 this 3 2077  5 value 1 1822  
765 0 0 6 924 1679 0 0 0 2 4 this 3 2077  5 value 1 1835  
766 0 0 6 924 2005 0 0 0 3 4 this 3 2077  1 x 1 2005  1 y 1 2005  
767 0 0 7 927 2080 1592 0 122 /**
 * Creates a StackedPerlinNoise3 object with no levels.  You should call
 * add_level() to add each level by hand.
 */ 0 
768 0 0 15 927 2080 1592 0 111 /**
 * Creates an exact duplicate of the existing StackedPerlinNoise3 object,
 * including the random seed.
 */ 1 4 copy 1 2081  
769 0 0 7 927 2080 1592 0 318 /**
 * Creates num_levels nested PerlinNoise3 objects.  Each stacked Perlin object
 * will have a scale of 1 scale_factor times the previous object (so that it
 * is higher-frequency, if scale_factor > 1), and an amplitude of amp_scale
 * times the previous object (so that it is less important, if amp_scale < 1).
 */ 8 2 sx 1 2005  2 sy 1 2005  2 sz 1 2005  10 num_levels 5 1792  12 scale_factor 5 2005  9 amp_scale 5 2005  10 table_size 5 1792  4 seed 5 2062  
770 0 0 6 928 2080 0 0 0 2 4 this 3 2080  4 copy 1 2081  
771 0 0 4 929 1784 0 0 95 /**
 * Adds an arbitrary PerlinNoise3 object, and an associated amplitude, to the
 * stack.
 */ 3 4 this 3 2080  5 level 1 2075  3 amp 5 2005  
772 0 0 4 930 1784 0 0 98 /**
 * Removes all levels from the stack.  You must call add_level() again to
 * restore them.
 */ 1 4 this 3 2080  
773 0 0 6 931 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2080  5 value 1 1861  
774 0 0 6 931 1679 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 2 4 this 3 2080  5 value 1 1874  
775 0 0 6 931 2005 0 0 58 /**
 * Returns the noise function of the three inputs.
 */ 4 4 this 3 2080  1 x 1 2005  1 y 1 2005  1 z 1 2005  
776 0 0 6 932 2005 0 0 0 2 4 this 3 2080  5 value 1 1861  
777 0 0 6 932 1679 0 0 0 2 4 this 3 2080  5 value 1 1874  
778 0 0 6 932 2005 0 0 0 4 4 this 3 2080  1 x 1 2005  1 y 1 2005  1 z 1 2005  
779 0 0 7 935 2083 1608 0 10 /**
 *
 */ 0 
780 0 0 15 935 2083 1608 0 0 1 6 param0 0 2084  
781 0 0 4 936 1784 0 0 115 /**
 * Removes all vertices and polygon specifications from the Triangulator, and
 * prepares it to start over.
 */ 1 4 this 3 2083  
782 0 0 6 937 1792 0 0 82 /**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */ 2 4 this 3 2083  5 point 1 2086  
783 0 0 6 937 1792 0 0 82 /**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */ 3 4 this 3 2083  1 x 1 2005  1 y 1 2005  
784 0 0 6 938 1792 0 0 267 /**
 * Returns the number of vertices in the pool.  Note that the Triangulator
 * might append new vertices, in addition to those added by the user, if any
 * of the polygon is self-intersecting, or if any of the holes intersect some
 * part of the polygon edges.
 */ 1 4 this 3 2084  
785 0 0 6 939 2086 0 0 34 /**
 * Returns the nth vertex.
 */ 2 4 this 3 2084  1 n 1 1792  
786 0 0 4 944 1784 0 0 112 /**
 * Removes the current polygon definition (and its set of holes), but does not
 * clear the vertex pool.
 */ 1 4 this 3 2083  
787 0 0 4 945 1784 0 0 334 /**
 * Adds the next consecutive vertex of the polygon.  This vertex should index
 * into the vertex pool established by repeated calls to add_vertex().
 *
 * The vertices may be listed in either clockwise or counterclockwise order.
 * Vertices should not be repeated.  In particular, do not repeat the first
 * vertex at the end.
 */ 2 4 this 3 2083  5 index 1 1792  
788 0 0 6 946 1798 0 0 145 /**
 * Returns true if the polygon vertices are listed in counterclockwise order,
 * or false if they appear to be listed in clockwise order.
 */ 1 4 this 3 2084  
789 0 0 4 947 1784 0 0 78 /**
 * Finishes the previous hole, if any, and prepares to add a new hole.
 */ 1 4 this 3 2083  
790 0 0 4 948 1784 0 0 277 /**
 * Adds the next consecutive vertex of the current hole.  This vertex should
 * index into the vertex pool established by repeated calls to add_vertex().
 *
 * The vertices may be listed in either clockwise or counterclockwise order.
 * Vertices should not be repeated.
 */ 2 4 this 3 2083  5 index 1 1792  
791 0 0 4 949 1784 0 0 181 /**
 * Does the work of triangulating the specified polygon.  After this call, you
 * may retrieve the new triangles one at a time by iterating through
 * get_triangle_v0/1/2().
 */ 1 4 this 3 2083  
792 0 0 6 950 1792 0 0 94 /**
 * Returns the number of triangles generated by the previous call to
 * triangulate().
 */ 1 4 this 3 2084  
793 0 0 6 951 1792 0 0 194 /**
 * Returns vertex 0 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */ 2 4 this 3 2084  1 n 1 1792  
794 0 0 6 952 1792 0 0 194 /**
 * Returns vertex 1 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */ 2 4 this 3 2084  1 n 1 1792  
795 0 0 6 953 1792 0 0 194 /**
 * Returns vertex 2 of the nth triangle generated by the previous call to
 * triangulate().
 *
 * This is a zero-based index into the vertices added by repeated calls to
 * add_vertex().
 */ 2 4 this 3 2084  1 n 1 1792  
796 0 0 7 956 2087 1616 0 10 /**
 *
 */ 0 
797 0 0 15 956 2087 1616 0 0 1 6 param0 0 2088  
798 0 0 4 957 1784 0 0 115 /**
 * Removes all vertices and polygon specifications from the Triangulator, and
 * prepares it to start over.
 */ 1 4 this 3 2087  
799 0 0 6 958 1792 0 0 82 /**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */ 2 4 this 3 2087  5 point 1 2021  
800 0 0 6 958 1792 0 0 82 /**
 * Adds a new vertex to the vertex pool.  Returns the vertex index number.
 */ 4 4 this 3 2087  1 x 1 2005  1 y 1 2005  1 z 1 2005  
801 0 0 6 959 1792 0 0 267 /**
 * Returns the number of vertices in the pool.  Note that the Triangulator
 * might append new vertices, in addition to those added by the user, if any
 * of the polygon is self-intersecting, or if any of the holes intersect some
 * part of the polygon edges.
 */ 1 4 this 3 2088  
802 0 0 6 960 2021 0 0 34 /**
 * Returns the nth vertex.
 */ 2 4 this 3 2088  1 n 1 1792  
803 0 0 4 962 1784 0 0 181 /**
 * Does the work of triangulating the specified polygon.  After this call, you
 * may retrieve the new triangles one at a time by iterating through
 * get_triangle_v0/1/2().
 */ 1 4 this 3 2087  
804 0 0 6 963 2020 0 0 100 /**
 * Returns the plane of the polygon.  This is only available after calling
 * triangulate().
 */ 1 4 this 3 2088  
805 0 0 4 810 1784 0 0 0 3 3 mat 1 2090  3 fwd 1 2024  2 cs 1 2042  
806 0 0 4 810 1784 0 0 0 4 3 mat 1 2090  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
807 0 0 4 810 1784 0 0 0 3 3 mat 1 2091  3 fwd 1 2014  2 cs 1 2042  
808 0 0 4 810 1784 0 0 0 4 3 mat 1 2091  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
809 0 0 4 810 1784 0 0 0 3 3 mat 1 2027  3 fwd 1 2024  2 cs 1 2042  
810 0 0 4 810 1784 0 0 0 4 3 mat 1 2027  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
811 0 0 4 810 1784 0 0 0 3 3 mat 1 2017  3 fwd 1 2014  2 cs 1 2042  
812 0 0 4 810 1784 0 0 0 4 3 mat 1 2017  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
813 0 0 4 810 1784 0 0 0 3 4 quat 1 2092  3 fwd 1 2024  2 cs 1 2042  
814 0 0 4 810 1784 0 0 0 4 4 quat 1 2092  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
815 0 0 4 810 1784 0 0 0 3 4 quat 1 2094  3 fwd 1 2014  2 cs 1 2042  
816 0 0 4 810 1784 0 0 0 4 4 quat 1 2094  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
817 0 0 4 811 1784 0 0 0 3 3 mat 1 2090  3 fwd 1 2024  2 cs 1 2042  
818 0 0 4 811 1784 0 0 0 4 3 mat 1 2090  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
819 0 0 4 811 1784 0 0 0 3 3 mat 1 2091  3 fwd 1 2014  2 cs 1 2042  
820 0 0 4 811 1784 0 0 0 4 3 mat 1 2091  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
821 0 0 4 811 1784 0 0 0 3 3 mat 1 2027  3 fwd 1 2024  2 cs 1 2042  
822 0 0 4 811 1784 0 0 0 4 3 mat 1 2027  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
823 0 0 4 811 1784 0 0 0 3 3 mat 1 2017  3 fwd 1 2014  2 cs 1 2042  
824 0 0 4 811 1784 0 0 0 4 3 mat 1 2017  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
825 0 0 4 811 1784 0 0 0 3 4 quat 1 2092  3 fwd 1 2024  2 cs 1 2042  
826 0 0 4 811 1784 0 0 0 4 4 quat 1 2092  3 fwd 1 2024  2 up 5 2024  2 cs 5 2042  
827 0 0 4 811 1784 0 0 0 3 4 quat 1 2094  3 fwd 1 2014  2 cs 1 2042  
828 0 0 4 811 1784 0 0 0 4 4 quat 1 2094  3 fwd 1 2014  2 up 5 2014  2 cs 5 2042  
829 0 0 4 917 1784 0 0 0 3 3 mat 1 2090  1 a 1 2024  1 b 1 2024  
830 0 0 4 917 1784 0 0 0 3 3 mat 1 2091  1 a 1 2014  1 b 1 2014  
831 0 0 4 917 1784 0 0 0 3 3 mat 1 2027  1 a 1 2024  1 b 1 2024  
832 0 0 4 917 1784 0 0 0 3 3 mat 1 2017  1 a 1 2014  1 b 1 2014  
479
1617 32 ConstPointerToArray< LMatrix3d > 0 141313 32 ConstPointerToArray< LMatrix3d > 32 ConstPointerToArray< LMatrix3d > 0 0 0 1 838 852 0 13 839 840 841 842 843 844 845 846 847 848 849 850 851 0 0 1 0 1618 0 0 0 0 0

1618 31 PointerToArrayBase< LMatrix3d > 0 26625 31 PointerToArrayBase< LMatrix3d > 31 PointerToArrayBase< LMatrix3d > 0 0 0 0 835 0 2 836 837 0 0 1 0 1619 0 0 0 0 0

1619 52 PointerToBase< ReferenceCountedVector< LMatrix3d > > 0 43009 52 PointerToBase< ReferenceCountedVector< LMatrix3d > > 52 PointerToBase< ReferenceCountedVector< LMatrix3d > > 0 0 0 0 0 0 2 833 834 0 0 1 0 1620 0 0 0 0 0

1620 13 PointerToVoid 0 2048 13 PointerToVoid 13 PointerToVoid 0 0 0 0 0 0 0 0 0 0 0 0 373
/**
 * This is the non-template part of the base class for PointerTo and
 * ConstPointerTo.  It is necessary so we can keep a pointer to a non-template
 * class within the ReferenceCount object, to implement weak reference
 * pointers--we need to have something to clean up when the ReferenceCount
 * object destructs.
 *
 * This is the base class for PointerToBase<T>.
 */

1621 32 ConstPointerToArray< LMatrix3f > 0 141313 32 ConstPointerToArray< LMatrix3f > 32 ConstPointerToArray< LMatrix3f > 0 0 0 1 858 872 0 13 859 860 861 862 863 864 865 866 867 868 869 870 871 0 0 1 0 1622 0 0 0 0 0

1622 31 PointerToArrayBase< LMatrix3f > 0 26625 31 PointerToArrayBase< LMatrix3f > 31 PointerToArrayBase< LMatrix3f > 0 0 0 0 855 0 2 856 857 0 0 1 0 1623 0 0 0 0 0

1623 52 PointerToBase< ReferenceCountedVector< LMatrix3f > > 0 43009 52 PointerToBase< ReferenceCountedVector< LMatrix3f > > 52 PointerToBase< ReferenceCountedVector< LMatrix3f > > 0 0 0 0 0 0 2 853 854 0 0 1 0 1620 0 0 0 0 0

1624 33 ConstPointerToArray< LVecBase2d > 0 141313 33 ConstPointerToArray< LVecBase2d > 33 ConstPointerToArray< LVecBase2d > 0 0 0 1 878 892 0 13 879 880 881 882 883 884 885 886 887 888 889 890 891 0 0 1 0 1625 0 0 0 0 0

1625 32 PointerToArrayBase< LVecBase2d > 0 26625 32 PointerToArrayBase< LVecBase2d > 32 PointerToArrayBase< LVecBase2d > 0 0 0 0 875 0 2 876 877 0 0 1 0 1626 0 0 0 0 0

1626 53 PointerToBase< ReferenceCountedVector< LVecBase2d > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase2d > > 53 PointerToBase< ReferenceCountedVector< LVecBase2d > > 0 0 0 0 0 0 2 873 874 0 0 1 0 1620 0 0 0 0 0

1627 33 ConstPointerToArray< LVecBase2f > 0 141313 33 ConstPointerToArray< LVecBase2f > 33 ConstPointerToArray< LVecBase2f > 0 0 0 1 898 912 0 13 899 900 901 902 903 904 905 906 907 908 909 910 911 0 0 1 0 1628 0 0 0 0 0

1628 32 PointerToArrayBase< LVecBase2f > 0 26625 32 PointerToArrayBase< LVecBase2f > 32 PointerToArrayBase< LVecBase2f > 0 0 0 0 895 0 2 896 897 0 0 1 0 1629 0 0 0 0 0

1629 53 PointerToBase< ReferenceCountedVector< LVecBase2f > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase2f > > 53 PointerToBase< ReferenceCountedVector< LVecBase2f > > 0 0 0 0 0 0 2 893 894 0 0 1 0 1620 0 0 0 0 0

1630 33 ConstPointerToArray< LVecBase2i > 0 141313 33 ConstPointerToArray< LVecBase2i > 33 ConstPointerToArray< LVecBase2i > 0 0 0 1 918 932 0 13 919 920 921 922 923 924 925 926 927 928 929 930 931 0 0 1 0 1631 0 0 0 0 0

1631 32 PointerToArrayBase< LVecBase2i > 0 26625 32 PointerToArrayBase< LVecBase2i > 32 PointerToArrayBase< LVecBase2i > 0 0 0 0 915 0 2 916 917 0 0 1 0 1632 0 0 0 0 0

1632 53 PointerToBase< ReferenceCountedVector< LVecBase2i > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase2i > > 53 PointerToBase< ReferenceCountedVector< LVecBase2i > > 0 0 0 0 0 0 2 913 914 0 0 1 0 1620 0 0 0 0 0

1633 33 ConstPointerToArray< LVecBase3d > 0 141313 33 ConstPointerToArray< LVecBase3d > 33 ConstPointerToArray< LVecBase3d > 0 0 0 1 938 952 0 13 939 940 941 942 943 944 945 946 947 948 949 950 951 0 0 1 0 1634 0 0 0 0 0

1634 32 PointerToArrayBase< LVecBase3d > 0 26625 32 PointerToArrayBase< LVecBase3d > 32 PointerToArrayBase< LVecBase3d > 0 0 0 0 935 0 2 936 937 0 0 1 0 1635 0 0 0 0 0

1635 53 PointerToBase< ReferenceCountedVector< LVecBase3d > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase3d > > 53 PointerToBase< ReferenceCountedVector< LVecBase3d > > 0 0 0 0 0 0 2 933 934 0 0 1 0 1620 0 0 0 0 0

1636 33 ConstPointerToArray< LVecBase3f > 0 141313 33 ConstPointerToArray< LVecBase3f > 33 ConstPointerToArray< LVecBase3f > 0 0 0 1 958 972 0 13 959 960 961 962 963 964 965 966 967 968 969 970 971 0 0 1 0 1637 0 0 0 0 0

1637 32 PointerToArrayBase< LVecBase3f > 0 26625 32 PointerToArrayBase< LVecBase3f > 32 PointerToArrayBase< LVecBase3f > 0 0 0 0 955 0 2 956 957 0 0 1 0 1638 0 0 0 0 0

1638 53 PointerToBase< ReferenceCountedVector< LVecBase3f > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase3f > > 53 PointerToBase< ReferenceCountedVector< LVecBase3f > > 0 0 0 0 0 0 2 953 954 0 0 1 0 1620 0 0 0 0 0

1639 33 ConstPointerToArray< LVecBase3i > 0 141313 33 ConstPointerToArray< LVecBase3i > 33 ConstPointerToArray< LVecBase3i > 0 0 0 1 978 992 0 13 979 980 981 982 983 984 985 986 987 988 989 990 991 0 0 1 0 1640 0 0 0 0 0

1640 32 PointerToArrayBase< LVecBase3i > 0 26625 32 PointerToArrayBase< LVecBase3i > 32 PointerToArrayBase< LVecBase3i > 0 0 0 0 975 0 2 976 977 0 0 1 0 1641 0 0 0 0 0

1641 53 PointerToBase< ReferenceCountedVector< LVecBase3i > > 0 43009 53 PointerToBase< ReferenceCountedVector< LVecBase3i > > 53 PointerToBase< ReferenceCountedVector< LVecBase3i > > 0 0 0 0 0 0 2 973 974 0 0 1 0 1620 0 0 0 0 0

1642 41 ConstPointerToArray< UnalignedLMatrix4d > 0 141313 41 ConstPointerToArray< UnalignedLMatrix4d > 41 ConstPointerToArray< UnalignedLMatrix4d > 0 0 0 1 998 1012 0 13 999 1000 1001 1002 1003 1004 1005 1006 1007 1008 1009 1010 1011 0 0 1 0 1643 0 0 0 0 0

1643 40 PointerToArrayBase< UnalignedLMatrix4d > 0 26625 40 PointerToArrayBase< UnalignedLMatrix4d > 40 PointerToArrayBase< UnalignedLMatrix4d > 0 0 0 0 995 0 2 996 997 0 0 1 0 1644 0 0 0 0 0

1644 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > 0 43009 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > 0 0 0 0 0 0 2 993 994 0 0 1 0 1620 0 0 0 0 0

1645 41 ConstPointerToArray< UnalignedLMatrix4f > 0 141313 41 ConstPointerToArray< UnalignedLMatrix4f > 41 ConstPointerToArray< UnalignedLMatrix4f > 0 0 0 1 1018 1032 0 13 1019 1020 1021 1022 1023 1024 1025 1026 1027 1028 1029 1030 1031 0 0 1 0 1646 0 0 0 0 0

1646 40 PointerToArrayBase< UnalignedLMatrix4f > 0 26625 40 PointerToArrayBase< UnalignedLMatrix4f > 40 PointerToArrayBase< UnalignedLMatrix4f > 0 0 0 0 1015 0 2 1016 1017 0 0 1 0 1647 0 0 0 0 0

1647 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > 0 43009 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > 61 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > 0 0 0 0 0 0 2 1013 1014 0 0 1 0 1620 0 0 0 0 0

1648 42 ConstPointerToArray< UnalignedLVecBase4d > 0 141313 42 ConstPointerToArray< UnalignedLVecBase4d > 42 ConstPointerToArray< UnalignedLVecBase4d > 0 0 0 1 1038 1052 0 13 1039 1040 1041 1042 1043 1044 1045 1046 1047 1048 1049 1050 1051 0 0 1 0 1649 0 0 0 0 0

1649 41 PointerToArrayBase< UnalignedLVecBase4d > 0 26625 41 PointerToArrayBase< UnalignedLVecBase4d > 41 PointerToArrayBase< UnalignedLVecBase4d > 0 0 0 0 1035 0 2 1036 1037 0 0 1 0 1650 0 0 0 0 0

1650 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > 0 43009 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > 0 0 0 0 0 0 2 1033 1034 0 0 1 0 1620 0 0 0 0 0

1651 42 ConstPointerToArray< UnalignedLVecBase4f > 0 141313 42 ConstPointerToArray< UnalignedLVecBase4f > 42 ConstPointerToArray< UnalignedLVecBase4f > 0 0 0 1 1058 1072 0 13 1059 1060 1061 1062 1063 1064 1065 1066 1067 1068 1069 1070 1071 0 0 1 0 1652 0 0 0 0 0

1652 41 PointerToArrayBase< UnalignedLVecBase4f > 0 26625 41 PointerToArrayBase< UnalignedLVecBase4f > 41 PointerToArrayBase< UnalignedLVecBase4f > 0 0 0 0 1055 0 2 1056 1057 0 0 1 0 1653 0 0 0 0 0

1653 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > 0 43009 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > 0 0 0 0 0 0 2 1053 1054 0 0 1 0 1620 0 0 0 0 0

1654 42 ConstPointerToArray< UnalignedLVecBase4i > 0 141313 42 ConstPointerToArray< UnalignedLVecBase4i > 42 ConstPointerToArray< UnalignedLVecBase4i > 0 0 0 1 1078 1092 0 13 1079 1080 1081 1082 1083 1084 1085 1086 1087 1088 1089 1090 1091 0 0 1 0 1655 0 0 0 0 0

1655 41 PointerToArrayBase< UnalignedLVecBase4i > 0 26625 41 PointerToArrayBase< UnalignedLVecBase4i > 41 PointerToArrayBase< UnalignedLVecBase4i > 0 0 0 0 1075 0 2 1076 1077 0 0 1 0 1656 0 0 0 0 0

1656 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > 0 43009 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > 62 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > 0 0 0 0 0 0 2 1073 1074 0 0 1 0 1620 0 0 0 0 0

1657 27 PointerToArray< LMatrix3d > 0 141313 27 PointerToArray< LMatrix3d > 27 PointerToArray< LMatrix3d > 0 0 0 1 1093 1114 0 20 1094 1095 1096 1097 1098 1099 1100 1101 1102 1103 1104 1105 1106 1107 1108 1109 1110 1111 1112 1113 0 0 1 0 1618 0 0 0 0 0

1658 27 PointerToArray< LMatrix3f > 0 141313 27 PointerToArray< LMatrix3f > 27 PointerToArray< LMatrix3f > 0 0 0 1 1115 1136 0 20 1116 1117 1118 1119 1120 1121 1122 1123 1124 1125 1126 1127 1128 1129 1130 1131 1132 1133 1134 1135 0 0 1 0 1622 0 0 0 0 0

1659 28 PointerToArray< LVecBase2d > 0 141313 28 PointerToArray< LVecBase2d > 28 PointerToArray< LVecBase2d > 0 0 0 1 1137 1158 0 20 1138 1139 1140 1141 1142 1143 1144 1145 1146 1147 1148 1149 1150 1151 1152 1153 1154 1155 1156 1157 0 0 1 0 1625 0 0 0 0 0

1660 28 PointerToArray< LVecBase2f > 0 141313 28 PointerToArray< LVecBase2f > 28 PointerToArray< LVecBase2f > 0 0 0 1 1159 1180 0 20 1160 1161 1162 1163 1164 1165 1166 1167 1168 1169 1170 1171 1172 1173 1174 1175 1176 1177 1178 1179 0 0 1 0 1628 0 0 0 0 0

1661 28 PointerToArray< LVecBase2i > 0 141313 28 PointerToArray< LVecBase2i > 28 PointerToArray< LVecBase2i > 0 0 0 1 1181 1202 0 20 1182 1183 1184 1185 1186 1187 1188 1189 1190 1191 1192 1193 1194 1195 1196 1197 1198 1199 1200 1201 0 0 1 0 1631 0 0 0 0 0

1662 28 PointerToArray< LVecBase3d > 0 141313 28 PointerToArray< LVecBase3d > 28 PointerToArray< LVecBase3d > 0 0 0 1 1203 1224 0 20 1204 1205 1206 1207 1208 1209 1210 1211 1212 1213 1214 1215 1216 1217 1218 1219 1220 1221 1222 1223 0 0 1 0 1634 0 0 0 0 0

1663 28 PointerToArray< LVecBase3f > 0 141313 28 PointerToArray< LVecBase3f > 28 PointerToArray< LVecBase3f > 0 0 0 1 1225 1246 0 20 1226 1227 1228 1229 1230 1231 1232 1233 1234 1235 1236 1237 1238 1239 1240 1241 1242 1243 1244 1245 0 0 1 0 1637 0 0 0 0 0

1664 28 PointerToArray< LVecBase3i > 0 141313 28 PointerToArray< LVecBase3i > 28 PointerToArray< LVecBase3i > 0 0 0 1 1247 1268 0 20 1248 1249 1250 1251 1252 1253 1254 1255 1256 1257 1258 1259 1260 1261 1262 1263 1264 1265 1266 1267 0 0 1 0 1640 0 0 0 0 0

1665 36 PointerToArray< UnalignedLMatrix4d > 0 141313 36 PointerToArray< UnalignedLMatrix4d > 36 PointerToArray< UnalignedLMatrix4d > 0 0 0 1 1269 1290 0 20 1270 1271 1272 1273 1274 1275 1276 1277 1278 1279 1280 1281 1282 1283 1284 1285 1286 1287 1288 1289 0 0 1 0 1643 0 0 0 0 0

1666 36 PointerToArray< UnalignedLMatrix4f > 0 141313 36 PointerToArray< UnalignedLMatrix4f > 36 PointerToArray< UnalignedLMatrix4f > 0 0 0 1 1291 1312 0 20 1292 1293 1294 1295 1296 1297 1298 1299 1300 1301 1302 1303 1304 1305 1306 1307 1308 1309 1310 1311 0 0 1 0 1646 0 0 0 0 0

1667 37 PointerToArray< UnalignedLVecBase4d > 0 141313 37 PointerToArray< UnalignedLVecBase4d > 37 PointerToArray< UnalignedLVecBase4d > 0 0 0 1 1313 1334 0 20 1314 1315 1316 1317 1318 1319 1320 1321 1322 1323 1324 1325 1326 1327 1328 1329 1330 1331 1332 1333 0 0 1 0 1649 0 0 0 0 0

1668 37 PointerToArray< UnalignedLVecBase4f > 0 141313 37 PointerToArray< UnalignedLVecBase4f > 37 PointerToArray< UnalignedLVecBase4f > 0 0 0 1 1335 1356 0 20 1336 1337 1338 1339 1340 1341 1342 1343 1344 1345 1346 1347 1348 1349 1350 1351 1352 1353 1354 1355 0 0 1 0 1652 0 0 0 0 0

1669 37 PointerToArray< UnalignedLVecBase4i > 0 141313 37 PointerToArray< UnalignedLVecBase4i > 37 PointerToArray< UnalignedLVecBase4i > 0 0 0 1 1357 1378 0 20 1358 1359 1360 1361 1362 1363 1364 1365 1366 1367 1368 1369 1370 1371 1372 1373 1374 1375 1376 1377 0 0 1 0 1655 0 0 0 0 0

1670 14 BoundingVolume 0 141313 14 BoundingVolume 14 BoundingVolume 0 0 0 0 1388 0 9 1379 1380 1381 1382 1383 1384 1385 1386 1387 0 0 1 0 1671 0 0 0 2 1672 1673 330
/**
 * This is an abstract class for any volume in any sense which can be said to
 * define the locality of reference of a node in a graph, along with all of
 * its descendants.  It is not necessarily a geometric volume (although see
 * GeometricBoundingVolume); this is simply an abstract interface for bounds
 * of any sort.
 */

1671 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1672 17 IntersectionFlags 0 794624 33 BoundingVolume::IntersectionFlags 33 BoundingVolume::IntersectionFlags 1670 0 0 0 0 0 0 0 0 0 5 18 IF_no_intersection 34 BoundingVolume::IF_no_intersection 65
// If no bits are set, it is known that there is no intersection.
0 11 IF_possible 27 BoundingVolume::IF_possible 56
// IF_possible is set if there might be an intersection.
1 7 IF_some 23 BoundingVolume::IF_some 105
// IF_some is set if there is definitely an intersection.  In this case,
// IF_possible will also be set.
2 6 IF_all 22 BoundingVolume::IF_all 69
// When IF_all is set, both IF_possible and IF_some will also be set.
4 18 IF_dont_understand 34 BoundingVolume::IF_dont_understand 106
// IF_dont_understand is set if the particular volumevolume intersection
// test has not been implemented.
8 0 74
// The contains() functions return the union of one or more of these bits.

1673 10 BoundsType 0 794624 26 BoundingVolume::BoundsType 26 BoundingVolume::BoundsType 1670 0 0 0 0 0 0 0 0 0 5 10 BT_default 26 BoundingVolume::BT_default 0
0 7 BT_best 23 BoundingVolume::BT_best 0
1 9 BT_sphere 25 BoundingVolume::BT_sphere 0
2 6 BT_box 22 BoundingVolume::BT_box 0
3 10 BT_fastest 26 BoundingVolume::BT_fastest 0
4 0 80
// This enum is used to control the automatic generation of bounding
// volumes.

1674 23 GeometricBoundingVolume 0 141313 23 GeometricBoundingVolume 23 GeometricBoundingVolume 0 0 0 0 1394 0 5 1389 1390 1391 1392 1393 0 0 1 0 1670 0 0 0 0 167
/**
 * This is another abstract class, for a general class of bounding volumes
 * that actually enclose points in 3-d space, such as BSP's and bounding
 * spheres.
 */

1675 20 FiniteBoundingVolume 0 141313 20 FiniteBoundingVolume 20 FiniteBoundingVolume 0 0 0 0 1399 3 2096 2097 2098 4 1395 1396 1397 1398 0 0 1 0 1674 0 0 0 0 168
/**
 * A special kind of GeometricBoundingVolume that is known to be finite.  It
 * is possible to query this kind of volume for its minimum and maximum
 * extents.
 */

1676 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 1677 0 0 0 0 0 0 0 0 0 0

1677 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

1678 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 1679 0 0 0 0 0 0 0 0 0 0

1679 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

1680 10 LParabolaf 0 26625 10 LParabolaf 10 LParabolaf 0 0 0 1 1400 1402 0 12 1401 1403 1404 1405 1406 1407 1408 1409 1410 1411 1412 1413 0 0 0 0 0 200
/**
 * An abstract mathematical description of a parabola, particularly useful for
 * describing arcs of projectiles.
 *
 * The parabolic equation, given parametrically here, is P = At^2 + Bt + C.
 */

1681 10 LParabolad 0 26625 10 LParabolad 10 LParabolad 0 0 0 1 1414 1416 0 12 1415 1417 1418 1419 1420 1421 1422 1423 1424 1425 1426 1427 0 0 0 0 0 200
/**
 * An abstract mathematical description of a parabola, particularly useful for
 * describing arcs of projectiles.
 *
 * The parabolic equation, given parametrically here, is P = At^2 + Bt + C.
 */

1682 9 LParabola 0 2105345 9 LParabola 9 LParabola 0 0 1680 0 0 0 0 0 0 0 0 0 0

1683 7 LPlanef 0 141313 7 LPlanef 7 LPlanef 0 0 0 1 1428 1447 0 18 1429 1430 1431 1432 1433 1434 1435 1436 1437 1438 1439 1440 1441 1442 1443 1444 1445 1446 0 0 1 0 1684 0 0 0 0 120
/**
 * An abstract mathematical description of a plane.  A plane is defined by the
 * equation Ax + By + Cz + D = 0.
 */

1684 10 LVecBase4f 0 2048 10 LVecBase4f 10 LVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1685 7 LPlaned 0 141313 7 LPlaned 7 LPlaned 0 0 0 1 1448 1467 0 18 1449 1450 1451 1452 1453 1454 1455 1456 1457 1458 1459 1460 1461 1462 1463 1464 1465 1466 0 0 1 0 1686 0 0 0 0 120
/**
 * An abstract mathematical description of a plane.  A plane is defined by the
 * equation Ax + By + Cz + D = 0.
 */

1686 10 LVecBase4d 0 2048 10 LVecBase4d 10 LVecBase4d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1687 6 LPlane 0 2105345 6 LPlane 6 LPlane 0 0 1683 0 0 0 0 0 0 0 0 0 0

1688 6 PlaneF 0 2105345 6 PlaneF 6 PlaneF 0 0 1683 0 0 0 0 0 0 0 0 0 0

1689 6 PlaneD 0 2105345 6 PlaneD 6 PlaneD 0 0 1685 0 0 0 0 0 0 0 0 0 0

1690 5 Plane 0 2105345 5 Plane 5 Plane 0 0 1683 0 0 0 0 0 0 0 0 0 0

1691 11 BoundingBox 0 141313 11 BoundingBox 11 BoundingBox 0 0 0 1 1468 1478 2 2099 2100 9 1469 1470 1471 1472 1473 1474 1475 1476 1477 2 2111 2112 0 1 0 1675 0 0 0 0 201
/**
 * An axis-aligned bounding box; that is, a minimum and maximum coordinate
 * triple.
 *
 * This box is always axis-aligned.  If you need a more general bounding box,
 * try BoundingHexahedron.
 */

1692 9 LFrustumf 0 141313 9 LFrustumf 9 LFrustumf 0 0 0 1 1479 1486 0 6 1480 1481 1482 1483 1484 1485 0 0 0 0 0 10
/**
 *
 */

1693 9 LFrustumd 0 141313 9 LFrustumd 9 LFrustumd 0 0 0 1 1487 1494 0 6 1488 1489 1490 1491 1492 1493 0 0 0 0 0 10
/**
 *
 */

1694 8 LFrustum 0 2105345 8 LFrustum 8 LFrustum 0 0 1692 0 0 0 0 0 0 0 0 0 0

1695 8 FrustumF 0 2105345 8 FrustumF 8 FrustumF 0 0 1692 0 0 0 0 0 0 0 0 0 0

1696 8 FrustumD 0 2105345 8 FrustumD 8 FrustumD 0 0 1693 0 0 0 0 0 0 0 0 0 0

1697 7 Frustum 0 2105345 7 Frustum 7 Frustum 0 0 1692 0 0 0 0 0 0 0 0 0 0

1698 18 BoundingHexahedron 0 141313 18 BoundingHexahedron 18 BoundingHexahedron 0 0 0 1 1495 1501 2 2101 2102 5 1496 1497 1498 1499 1500 2 2113 2114 0 1 0 1675 0 0 0 0 300
/**
 * This defines a bounding convex hexahedron.  It is typically used to
 * represent a frustum, but may represent any enclosing convex hexahedron,
 * including simple boxes.  However, if all you want is an axis-aligned
 * bounding box, you may be better off with the simpler BoundingBox class.
 */

1699 12 BoundingLine 0 141313 12 BoundingLine 12 BoundingLine 0 0 0 1 1502 1509 0 6 1503 1504 1505 1506 1507 1508 0 0 1 0 1674 0 0 0 0 320
/**
 * This funny bounding volume is an infinite line with no thickness and
 * extending to infinity in both directions.
 *
 * Note that it *always* extends in both directions, despite the fact that you
 * specify two points to the constructor.  These are not endpoints, they are
 * two arbitrary points on the line.
 */

1700 13 BoundingPlane 0 141313 13 BoundingPlane 13 BoundingPlane 0 0 0 1 1510 1516 1 2103 5 1511 1512 1513 1514 1515 0 0 1 0 1674 0 0 0 0 248
/**
 * This funny bounding volume is an infinite plane that divides space into two
 * regions: the part behind the normal, which is "inside" the bounding volume,
 * and the part in front of the normal, which is "outside" the bounding
 * volume.
 */

1701 12 LPlane const 0 8832 12 LPlane const 12 LPlane const 0 0 1687 0 0 0 0 0 0 0 0 0 0

1702 14 BoundingSphere 0 141313 14 BoundingSphere 14 BoundingSphere 0 0 0 1 1517 1526 2 2104 2105 8 1518 1519 1520 1521 1522 1523 1524 1525 0 0 1 0 1675 0 0 0 0 147
/**
 * This defines a bounding sphere, consisting of a center and a radius.  It is
 * always a sphere, and never an ellipsoid or other quadric.
 */

1703 26 IntersectionBoundingVolume 0 141313 26 IntersectionBoundingVolume 26 IntersectionBoundingVolume 0 0 0 1 1527 1536 1 2106 8 1528 1529 1530 1531 1532 1533 1534 1535 1 2115 0 1 0 1674 0 0 0 0 232
/**
 * This special bounding volume is the intersection of all of its constituent
 * bounding volumes.
 *
 * A point is defined to be within an IntersectionBoundingVolume if it is
 * within all of its component bounding volumes.
 */

1704 31 GeometricBoundingVolume const * 0 8576 31 GeometricBoundingVolume const * 31 GeometricBoundingVolume const * 0 0 1705 0 0 0 0 0 0 0 0 0 0

1705 29 GeometricBoundingVolume const 0 8832 29 GeometricBoundingVolume const 29 GeometricBoundingVolume const 0 0 1674 0 0 0 0 0 0 0 0 0 0

1706 8 Mersenne 0 141313 8 Mersenne 8 Mersenne 0 0 0 1 1539 1541 0 1 1540 0 0 0 0 1 1707 0

1707 0 0 794624 10 Mersenne:: 0 1706 0 0 0 0 0 0 0 0 0 1 9 max_value 19 Mersenne::max_value 0
2147483647 0 0

1708 18 OmniBoundingVolume 0 141313 18 OmniBoundingVolume 18 OmniBoundingVolume 0 0 0 1 1542 1544 0 1 1543 0 0 1 0 1674 0 0 0 0 85
/**
 * This is a special kind of GeometricBoundingVolume that fills all of space.
 */

1709 19 UnionBoundingVolume 0 141313 19 UnionBoundingVolume 19 UnionBoundingVolume 0 0 0 1 1545 1555 1 2107 9 1546 1547 1548 1549 1550 1551 1552 1553 1554 1 2116 0 1 0 1674 0 0 0 0 229
/**
 * This special bounding volume is the union of all of its constituent
 * bounding volumes.
 *
 * A point is defined to be within a UnionBoundingVolume if it is within any
 * one or more of its component bounding volumes.
 */

1710 10 Randomizer 0 141313 10 Randomizer 10 Randomizer 0 0 0 1 1556 1563 0 6 1557 1558 1559 1560 1561 1562 0 0 0 0 0 50
/**
 * A handy class to return random numbers.
 */

1711 11 PerlinNoise 0 141313 11 PerlinNoise 11 PerlinNoise 0 0 0 0 1565 0 1 1564 0 0 0 0 0 183
/**
 * This is the base class for PerlinNoise2 and PerlinNoise3, different
 * dimensions of Perlin noise implementation.  The base class just collects
 * the common functionality.
 */

1712 12 PerlinNoise2 0 141313 12 PerlinNoise2 12 PerlinNoise2 0 0 0 1 1566 1571 0 4 1567 1568 1569 1570 0 0 1 0 1711 0 0 0 0 186
/**
 * This class provides an implementation of Perlin noise for 2 variables.
 * This code is loosely based on the reference implementation at
 * https://mrl.nyu.edu/~perlin/noise/ .
 */

1713 12 PerlinNoise3 0 141313 12 PerlinNoise3 12 PerlinNoise3 0 0 0 1 1572 1577 0 4 1573 1574 1575 1576 0 0 1 0 1711 0 0 0 0 185
/**
 * This class provides an implementation of Perlin noise for 3 variables.
 * This code is loosely based on the reference implementation at
 * http://mrl.nyu.edu/~perlin/noise/ .
 */

1714 13 PTA_LMatrix4f 0 2105345 13 PTA_LMatrix4f 13 PTA_LMatrix4f 0 0 1666 0 0 0 0 0 0 0 0 0 0

1715 14 CPTA_LMatrix4f 0 2105345 14 CPTA_LMatrix4f 14 CPTA_LMatrix4f 0 0 1645 0 0 0 0 0 0 0 0 0 0

1716 13 PTA_LMatrix4d 0 2105345 13 PTA_LMatrix4d 13 PTA_LMatrix4d 0 0 1665 0 0 0 0 0 0 0 0 0 0

1717 14 CPTA_LMatrix4d 0 2105345 14 CPTA_LMatrix4d 14 CPTA_LMatrix4d 0 0 1642 0 0 0 0 0 0 0 0 0 0

1718 12 PTA_LMatrix4 0 2105345 12 PTA_LMatrix4 12 PTA_LMatrix4 0 0 1714 0 0 0 0 0 0 0 0 0 0

1719 13 CPTA_LMatrix4 0 2105345 13 CPTA_LMatrix4 13 CPTA_LMatrix4 0 0 1715 0 0 0 0 0 0 0 0 0 0

1720 7 PTAMat4 0 2105345 7 PTAMat4 7 PTAMat4 0 0 1718 0 0 0 0 0 0 0 0 0 0

1721 8 CPTAMat4 0 2105345 8 CPTAMat4 8 CPTAMat4 0 0 1719 0 0 0 0 0 0 0 0 0 0

1722 8 PTAMat4d 0 2105345 8 PTAMat4d 8 PTAMat4d 0 0 1716 0 0 0 0 0 0 0 0 0 0

1723 9 CPTAMat4d 0 2105345 9 CPTAMat4d 9 CPTAMat4d 0 0 1717 0 0 0 0 0 0 0 0 0 0

1724 13 PTA_LMatrix3f 0 2105345 13 PTA_LMatrix3f 13 PTA_LMatrix3f 0 0 1658 0 0 0 0 0 0 0 0 0 0

1725 14 CPTA_LMatrix3f 0 2105345 14 CPTA_LMatrix3f 14 CPTA_LMatrix3f 0 0 1621 0 0 0 0 0 0 0 0 0 0

1726 13 PTA_LMatrix3d 0 2105345 13 PTA_LMatrix3d 13 PTA_LMatrix3d 0 0 1657 0 0 0 0 0 0 0 0 0 0

1727 14 CPTA_LMatrix3d 0 2105345 14 CPTA_LMatrix3d 14 CPTA_LMatrix3d 0 0 1617 0 0 0 0 0 0 0 0 0 0

1728 12 PTA_LMatrix3 0 2105345 12 PTA_LMatrix3 12 PTA_LMatrix3 0 0 1724 0 0 0 0 0 0 0 0 0 0

1729 13 CPTA_LMatrix3 0 2105345 13 CPTA_LMatrix3 13 CPTA_LMatrix3 0 0 1725 0 0 0 0 0 0 0 0 0 0

1730 7 PTAMat3 0 2105345 7 PTAMat3 7 PTAMat3 0 0 1728 0 0 0 0 0 0 0 0 0 0

1731 8 CPTAMat3 0 2105345 8 CPTAMat3 8 CPTAMat3 0 0 1729 0 0 0 0 0 0 0 0 0 0

1732 8 PTAMat3d 0 2105345 8 PTAMat3d 8 PTAMat3d 0 0 1726 0 0 0 0 0 0 0 0 0 0

1733 9 CPTAMat3d 0 2105345 9 CPTAMat3d 9 CPTAMat3d 0 0 1727 0 0 0 0 0 0 0 0 0 0

1734 14 PTA_LVecBase4f 0 2105345 14 PTA_LVecBase4f 14 PTA_LVecBase4f 0 0 1668 0 0 0 0 0 0 0 0 0 0

1735 15 CPTA_LVecBase4f 0 2105345 15 CPTA_LVecBase4f 15 CPTA_LVecBase4f 0 0 1651 0 0 0 0 0 0 0 0 0 0

1736 14 PTA_LVecBase4d 0 2105345 14 PTA_LVecBase4d 14 PTA_LVecBase4d 0 0 1667 0 0 0 0 0 0 0 0 0 0

1737 15 CPTA_LVecBase4d 0 2105345 15 CPTA_LVecBase4d 15 CPTA_LVecBase4d 0 0 1648 0 0 0 0 0 0 0 0 0 0

1738 14 PTA_LVecBase4i 0 2105345 14 PTA_LVecBase4i 14 PTA_LVecBase4i 0 0 1669 0 0 0 0 0 0 0 0 0 0

1739 15 CPTA_LVecBase4i 0 2105345 15 CPTA_LVecBase4i 15 CPTA_LVecBase4i 0 0 1654 0 0 0 0 0 0 0 0 0 0

1740 13 PTA_LVecBase4 0 2105345 13 PTA_LVecBase4 13 PTA_LVecBase4 0 0 1734 0 0 0 0 0 0 0 0 0 0

1741 14 CPTA_LVecBase4 0 2105345 14 CPTA_LVecBase4 14 CPTA_LVecBase4 0 0 1735 0 0 0 0 0 0 0 0 0 0

1742 12 PTAVecBase4f 0 2105345 12 PTAVecBase4f 12 PTAVecBase4f 0 0 1734 0 0 0 0 0 0 0 0 0 0

1743 13 CPTAVecBase4f 0 2105345 13 CPTAVecBase4f 13 CPTAVecBase4f 0 0 1735 0 0 0 0 0 0 0 0 0 0

1744 12 PTAVecBase4d 0 2105345 12 PTAVecBase4d 12 PTAVecBase4d 0 0 1736 0 0 0 0 0 0 0 0 0 0

1745 13 CPTAVecBase4d 0 2105345 13 CPTAVecBase4d 13 CPTAVecBase4d 0 0 1737 0 0 0 0 0 0 0 0 0 0

1746 14 PTA_LVecBase3f 0 2105345 14 PTA_LVecBase3f 14 PTA_LVecBase3f 0 0 1663 0 0 0 0 0 0 0 0 0 0

1747 15 CPTA_LVecBase3f 0 2105345 15 CPTA_LVecBase3f 15 CPTA_LVecBase3f 0 0 1636 0 0 0 0 0 0 0 0 0 0

1748 14 PTA_LVecBase3d 0 2105345 14 PTA_LVecBase3d 14 PTA_LVecBase3d 0 0 1662 0 0 0 0 0 0 0 0 0 0

1749 15 CPTA_LVecBase3d 0 2105345 15 CPTA_LVecBase3d 15 CPTA_LVecBase3d 0 0 1633 0 0 0 0 0 0 0 0 0 0

1750 14 PTA_LVecBase3i 0 2105345 14 PTA_LVecBase3i 14 PTA_LVecBase3i 0 0 1664 0 0 0 0 0 0 0 0 0 0

1751 15 CPTA_LVecBase3i 0 2105345 15 CPTA_LVecBase3i 15 CPTA_LVecBase3i 0 0 1639 0 0 0 0 0 0 0 0 0 0

1752 13 PTA_LVecBase3 0 2105345 13 PTA_LVecBase3 13 PTA_LVecBase3 0 0 1746 0 0 0 0 0 0 0 0 0 0

1753 14 CPTA_LVecBase3 0 2105345 14 CPTA_LVecBase3 14 CPTA_LVecBase3 0 0 1747 0 0 0 0 0 0 0 0 0 0

1754 12 PTAVecBase3f 0 2105345 12 PTAVecBase3f 12 PTAVecBase3f 0 0 1746 0 0 0 0 0 0 0 0 0 0

1755 13 CPTAVecBase3f 0 2105345 13 CPTAVecBase3f 13 CPTAVecBase3f 0 0 1747 0 0 0 0 0 0 0 0 0 0

1756 12 PTAVecBase3d 0 2105345 12 PTAVecBase3d 12 PTAVecBase3d 0 0 1748 0 0 0 0 0 0 0 0 0 0

1757 13 CPTAVecBase3d 0 2105345 13 CPTAVecBase3d 13 CPTAVecBase3d 0 0 1749 0 0 0 0 0 0 0 0 0 0

1758 14 PTA_LVecBase2f 0 2105345 14 PTA_LVecBase2f 14 PTA_LVecBase2f 0 0 1660 0 0 0 0 0 0 0 0 0 0

1759 15 CPTA_LVecBase2f 0 2105345 15 CPTA_LVecBase2f 15 CPTA_LVecBase2f 0 0 1627 0 0 0 0 0 0 0 0 0 0

1760 14 PTA_LVecBase2d 0 2105345 14 PTA_LVecBase2d 14 PTA_LVecBase2d 0 0 1659 0 0 0 0 0 0 0 0 0 0

1761 15 CPTA_LVecBase2d 0 2105345 15 CPTA_LVecBase2d 15 CPTA_LVecBase2d 0 0 1624 0 0 0 0 0 0 0 0 0 0

1762 14 PTA_LVecBase2i 0 2105345 14 PTA_LVecBase2i 14 PTA_LVecBase2i 0 0 1661 0 0 0 0 0 0 0 0 0 0

1763 15 CPTA_LVecBase2i 0 2105345 15 CPTA_LVecBase2i 15 CPTA_LVecBase2i 0 0 1630 0 0 0 0 0 0 0 0 0 0

1764 13 PTA_LVecBase2 0 2105345 13 PTA_LVecBase2 13 PTA_LVecBase2 0 0 1758 0 0 0 0 0 0 0 0 0 0

1765 14 CPTA_LVecBase2 0 2105345 14 CPTA_LVecBase2 14 CPTA_LVecBase2 0 0 1759 0 0 0 0 0 0 0 0 0 0

1766 12 PTAVecBase2f 0 2105345 12 PTAVecBase2f 12 PTAVecBase2f 0 0 1758 0 0 0 0 0 0 0 0 0 0

1767 13 CPTAVecBase2f 0 2105345 13 CPTAVecBase2f 13 CPTAVecBase2f 0 0 1759 0 0 0 0 0 0 0 0 0 0

1768 12 PTAVecBase2d 0 2105345 12 PTAVecBase2d 12 PTAVecBase2d 0 0 1760 0 0 0 0 0 0 0 0 0 0

1769 13 CPTAVecBase2d 0 2105345 13 CPTAVecBase2d 13 CPTAVecBase2d 0 0 1761 0 0 0 0 0 0 0 0 0 0

1770 19 StackedPerlinNoise2 0 141313 19 StackedPerlinNoise2 19 StackedPerlinNoise2 0 0 0 1 1579 1585 0 5 1580 1581 1582 1583 1584 0 0 0 0 0 147
/**
 * Implements a multi-layer PerlinNoise, with one or more high-frequency noise
 * functions added to a lower-frequency base noise function.
 */

1771 19 StackedPerlinNoise3 0 141313 19 StackedPerlinNoise3 19 StackedPerlinNoise3 0 0 0 1 1586 1592 0 5 1587 1588 1589 1590 1591 0 0 0 0 0 147
/**
 * Implements a multi-layer PerlinNoise, with one or more high-frequency noise
 * functions added to a lower-frequency base noise function.
 */

1772 12 Triangulator 0 141313 12 Triangulator 12 Triangulator 0 0 0 1 1593 1608 1 2108 14 1594 1595 1596 1597 1598 1599 1600 1601 1602 1603 1604 1605 1606 1607 1 2117 0 0 0 0 382
/**
 * This class can triangulate a convex or concave polygon, even one with
 * holes.  It is adapted from an algorithm published as:
 *
 * Narkhede A. and Manocha D., Fast polygon triangulation algorithm based on
 * Seidel's Algorithm, UNC-CH, 1994.
 *
 * http://www.cs.unc.edu/~dm/CODE/GEM/chapter.html
 *
 * It works strictly on 2-d points.  See Triangulator3 for 3-d points.
 */

1773 14 LPoint2d const 0 8832 14 LPoint2d const 14 LPoint2d const 0 0 1774 0 0 0 0 0 0 0 0 0 0

1774 8 LPoint2d 0 2048 8 LPoint2d 8 LPoint2d 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * This is a two-component point in space.
 */

1775 13 Triangulator3 0 141313 13 Triangulator3 13 Triangulator3 0 0 0 1 1609 1616 2 2109 2110 6 1610 1611 1612 1613 1614 1615 1 2118 0 1 0 1772 0 0 0 0 264
/**
 * This is an extension of Triangulator to handle polygons with three-
 * dimensional points.  It assumes all of the points lie in a single plane,
 * and internally projects the supplied points into 2-D for passing to the
 * underlying Triangulator object.
 */

1776 14 LPoint3d const 0 8832 14 LPoint3d const 14 LPoint3d const 0 0 1777 0 0 0 0 0 0 0 0 0 0

1777 8 LPoint3d 0 2048 8 LPoint3d 8 LPoint3d 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

1778 13 LPlaned const 0 8832 13 LPlaned const 13 LPlaned const 0 0 1685 0 0 0 0 0 0 0 0 0 0

1779 40 ConstPointerToArray< LMatrix3d > const * 0 8576 40 ConstPointerToArray< LMatrix3d > const * 40 ConstPointerToArray< LMatrix3d > const * 0 0 1780 0 0 0 0 0 0 0 0 0 0

1780 38 ConstPointerToArray< LMatrix3d > const 0 8832 38 ConstPointerToArray< LMatrix3d > const 38 ConstPointerToArray< LMatrix3d > const 0 0 1617 0 0 0 0 0 0 0 0 0 0

1781 34 ConstPointerToArray< LMatrix3d > * 0 8576 34 ConstPointerToArray< LMatrix3d > * 34 ConstPointerToArray< LMatrix3d > * 0 0 1617 0 0 0 0 0 0 0 0 0 0

1782 35 PointerToArray< LMatrix3d > const * 0 8576 35 PointerToArray< LMatrix3d > const * 35 PointerToArray< LMatrix3d > const * 0 0 1783 0 0 0 0 0 0 0 0 0 0

1783 33 PointerToArray< LMatrix3d > const 0 8832 33 PointerToArray< LMatrix3d > const 33 PointerToArray< LMatrix3d > const 0 0 1657 0 0 0 0 0 0 0 0 0 0

1784 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

1785 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

1786 17 LMatrix3d const * 0 8576 17 LMatrix3d const * 17 LMatrix3d const * 0 0 1787 0 0 0 0 0 0 0 0 0 0

1787 15 LMatrix3d const 0 8832 15 LMatrix3d const 15 LMatrix3d const 0 0 1788 0 0 0 0 0 0 0 0 0 0

1788 9 LMatrix3d 0 2048 9 LMatrix3d 9 LMatrix3d 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * This is a 3-by-3 transform matrix.  It typically will represent either a
 * rotation-and-scale (no translation) matrix in 3-d, or a full affine matrix
 * (rotation, scale, translation) in 2-d, e.g.  for a texture matrix.
 */

1789 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 1790 0 0 0 0 0 0 0 0 0 0

1790 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 1791 0 0 0 0 0 0 0 0 0 0

1791 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

1792 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

1793 11 Py_buffer * 0 8576 11 Py_buffer * 11 Py_buffer * 0 0 1794 0 0 0 0 0 0 0 0 0 0

1794 9 Py_buffer 0 2105344 9 Py_buffer 9 Py_buffer 0 0 1795 0 0 0 0 0 0 0 0 0 0

1795 10 bufferinfo 0 1024 10 bufferinfo 10 bufferinfo 0 0 0 0 0 0 0 0 0 0 0 0 0

1796 39 PointerToArrayBase< LMatrix3d > const * 0 8576 39 PointerToArrayBase< LMatrix3d > const * 39 PointerToArrayBase< LMatrix3d > const * 0 0 1797 0 0 0 0 0 0 0 0 0 0

1797 37 PointerToArrayBase< LMatrix3d > const 0 8832 37 PointerToArrayBase< LMatrix3d > const 37 PointerToArrayBase< LMatrix3d > const 0 0 1618 0 0 0 0 0 0 0 0 0 0

1798 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

1799 54 PointerToBase< ReferenceCountedVector< LMatrix3d > > * 0 8576 54 PointerToBase< ReferenceCountedVector< LMatrix3d > > * 54 PointerToBase< ReferenceCountedVector< LMatrix3d > > * 0 0 1619 0 0 0 0 0 0 0 0 0 0

1800 60 PointerToBase< ReferenceCountedVector< LMatrix3d > > const * 0 8576 60 PointerToBase< ReferenceCountedVector< LMatrix3d > > const * 60 PointerToBase< ReferenceCountedVector< LMatrix3d > > const * 0 0 1801 0 0 0 0 0 0 0 0 0 0

1801 58 PointerToBase< ReferenceCountedVector< LMatrix3d > > const 0 8832 58 PointerToBase< ReferenceCountedVector< LMatrix3d > > const 58 PointerToBase< ReferenceCountedVector< LMatrix3d > > const 0 0 1619 0 0 0 0 0 0 0 0 0 0

1802 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 1803 0 0 0 0 0 0 0 0 0 0

1803 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

1804 40 ConstPointerToArray< LMatrix3f > const * 0 8576 40 ConstPointerToArray< LMatrix3f > const * 40 ConstPointerToArray< LMatrix3f > const * 0 0 1805 0 0 0 0 0 0 0 0 0 0

1805 38 ConstPointerToArray< LMatrix3f > const 0 8832 38 ConstPointerToArray< LMatrix3f > const 38 ConstPointerToArray< LMatrix3f > const 0 0 1621 0 0 0 0 0 0 0 0 0 0

1806 34 ConstPointerToArray< LMatrix3f > * 0 8576 34 ConstPointerToArray< LMatrix3f > * 34 ConstPointerToArray< LMatrix3f > * 0 0 1621 0 0 0 0 0 0 0 0 0 0

1807 35 PointerToArray< LMatrix3f > const * 0 8576 35 PointerToArray< LMatrix3f > const * 35 PointerToArray< LMatrix3f > const * 0 0 1808 0 0 0 0 0 0 0 0 0 0

1808 33 PointerToArray< LMatrix3f > const 0 8832 33 PointerToArray< LMatrix3f > const 33 PointerToArray< LMatrix3f > const 0 0 1658 0 0 0 0 0 0 0 0 0 0

1809 17 LMatrix3f const * 0 8576 17 LMatrix3f const * 17 LMatrix3f const * 0 0 1810 0 0 0 0 0 0 0 0 0 0

1810 15 LMatrix3f const 0 8832 15 LMatrix3f const 15 LMatrix3f const 0 0 1811 0 0 0 0 0 0 0 0 0 0

1811 9 LMatrix3f 0 2048 9 LMatrix3f 9 LMatrix3f 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * This is a 3-by-3 transform matrix.  It typically will represent either a
 * rotation-and-scale (no translation) matrix in 3-d, or a full affine matrix
 * (rotation, scale, translation) in 2-d, e.g.  for a texture matrix.
 */

1812 39 PointerToArrayBase< LMatrix3f > const * 0 8576 39 PointerToArrayBase< LMatrix3f > const * 39 PointerToArrayBase< LMatrix3f > const * 0 0 1813 0 0 0 0 0 0 0 0 0 0

1813 37 PointerToArrayBase< LMatrix3f > const 0 8832 37 PointerToArrayBase< LMatrix3f > const 37 PointerToArrayBase< LMatrix3f > const 0 0 1622 0 0 0 0 0 0 0 0 0 0

1814 54 PointerToBase< ReferenceCountedVector< LMatrix3f > > * 0 8576 54 PointerToBase< ReferenceCountedVector< LMatrix3f > > * 54 PointerToBase< ReferenceCountedVector< LMatrix3f > > * 0 0 1623 0 0 0 0 0 0 0 0 0 0

1815 60 PointerToBase< ReferenceCountedVector< LMatrix3f > > const * 0 8576 60 PointerToBase< ReferenceCountedVector< LMatrix3f > > const * 60 PointerToBase< ReferenceCountedVector< LMatrix3f > > const * 0 0 1816 0 0 0 0 0 0 0 0 0 0

1816 58 PointerToBase< ReferenceCountedVector< LMatrix3f > > const 0 8832 58 PointerToBase< ReferenceCountedVector< LMatrix3f > > const 58 PointerToBase< ReferenceCountedVector< LMatrix3f > > const 0 0 1623 0 0 0 0 0 0 0 0 0 0

1817 41 ConstPointerToArray< LVecBase2d > const * 0 8576 41 ConstPointerToArray< LVecBase2d > const * 41 ConstPointerToArray< LVecBase2d > const * 0 0 1818 0 0 0 0 0 0 0 0 0 0

1818 39 ConstPointerToArray< LVecBase2d > const 0 8832 39 ConstPointerToArray< LVecBase2d > const 39 ConstPointerToArray< LVecBase2d > const 0 0 1624 0 0 0 0 0 0 0 0 0 0

1819 35 ConstPointerToArray< LVecBase2d > * 0 8576 35 ConstPointerToArray< LVecBase2d > * 35 ConstPointerToArray< LVecBase2d > * 0 0 1624 0 0 0 0 0 0 0 0 0 0

1820 36 PointerToArray< LVecBase2d > const * 0 8576 36 PointerToArray< LVecBase2d > const * 36 PointerToArray< LVecBase2d > const * 0 0 1821 0 0 0 0 0 0 0 0 0 0

1821 34 PointerToArray< LVecBase2d > const 0 8832 34 PointerToArray< LVecBase2d > const 34 PointerToArray< LVecBase2d > const 0 0 1659 0 0 0 0 0 0 0 0 0 0

1822 18 LVecBase2d const * 0 8576 18 LVecBase2d const * 18 LVecBase2d const * 0 0 1823 0 0 0 0 0 0 0 0 0 0

1823 16 LVecBase2d const 0 8832 16 LVecBase2d const 16 LVecBase2d const 0 0 1824 0 0 0 0 0 0 0 0 0 0

1824 10 LVecBase2d 0 2048 10 LVecBase2d 10 LVecBase2d 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

1825 40 PointerToArrayBase< LVecBase2d > const * 0 8576 40 PointerToArrayBase< LVecBase2d > const * 40 PointerToArrayBase< LVecBase2d > const * 0 0 1826 0 0 0 0 0 0 0 0 0 0

1826 38 PointerToArrayBase< LVecBase2d > const 0 8832 38 PointerToArrayBase< LVecBase2d > const 38 PointerToArrayBase< LVecBase2d > const 0 0 1625 0 0 0 0 0 0 0 0 0 0

1827 55 PointerToBase< ReferenceCountedVector< LVecBase2d > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase2d > > * 55 PointerToBase< ReferenceCountedVector< LVecBase2d > > * 0 0 1626 0 0 0 0 0 0 0 0 0 0

1828 61 PointerToBase< ReferenceCountedVector< LVecBase2d > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase2d > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase2d > > const * 0 0 1829 0 0 0 0 0 0 0 0 0 0

1829 59 PointerToBase< ReferenceCountedVector< LVecBase2d > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase2d > > const 59 PointerToBase< ReferenceCountedVector< LVecBase2d > > const 0 0 1626 0 0 0 0 0 0 0 0 0 0

1830 41 ConstPointerToArray< LVecBase2f > const * 0 8576 41 ConstPointerToArray< LVecBase2f > const * 41 ConstPointerToArray< LVecBase2f > const * 0 0 1831 0 0 0 0 0 0 0 0 0 0

1831 39 ConstPointerToArray< LVecBase2f > const 0 8832 39 ConstPointerToArray< LVecBase2f > const 39 ConstPointerToArray< LVecBase2f > const 0 0 1627 0 0 0 0 0 0 0 0 0 0

1832 35 ConstPointerToArray< LVecBase2f > * 0 8576 35 ConstPointerToArray< LVecBase2f > * 35 ConstPointerToArray< LVecBase2f > * 0 0 1627 0 0 0 0 0 0 0 0 0 0

1833 36 PointerToArray< LVecBase2f > const * 0 8576 36 PointerToArray< LVecBase2f > const * 36 PointerToArray< LVecBase2f > const * 0 0 1834 0 0 0 0 0 0 0 0 0 0

1834 34 PointerToArray< LVecBase2f > const 0 8832 34 PointerToArray< LVecBase2f > const 34 PointerToArray< LVecBase2f > const 0 0 1660 0 0 0 0 0 0 0 0 0 0

1835 18 LVecBase2f const * 0 8576 18 LVecBase2f const * 18 LVecBase2f const * 0 0 1836 0 0 0 0 0 0 0 0 0 0

1836 16 LVecBase2f const 0 8832 16 LVecBase2f const 16 LVecBase2f const 0 0 1837 0 0 0 0 0 0 0 0 0 0

1837 10 LVecBase2f 0 2048 10 LVecBase2f 10 LVecBase2f 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

1838 40 PointerToArrayBase< LVecBase2f > const * 0 8576 40 PointerToArrayBase< LVecBase2f > const * 40 PointerToArrayBase< LVecBase2f > const * 0 0 1839 0 0 0 0 0 0 0 0 0 0

1839 38 PointerToArrayBase< LVecBase2f > const 0 8832 38 PointerToArrayBase< LVecBase2f > const 38 PointerToArrayBase< LVecBase2f > const 0 0 1628 0 0 0 0 0 0 0 0 0 0

1840 55 PointerToBase< ReferenceCountedVector< LVecBase2f > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase2f > > * 55 PointerToBase< ReferenceCountedVector< LVecBase2f > > * 0 0 1629 0 0 0 0 0 0 0 0 0 0

1841 61 PointerToBase< ReferenceCountedVector< LVecBase2f > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase2f > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase2f > > const * 0 0 1842 0 0 0 0 0 0 0 0 0 0

1842 59 PointerToBase< ReferenceCountedVector< LVecBase2f > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase2f > > const 59 PointerToBase< ReferenceCountedVector< LVecBase2f > > const 0 0 1629 0 0 0 0 0 0 0 0 0 0

1843 41 ConstPointerToArray< LVecBase2i > const * 0 8576 41 ConstPointerToArray< LVecBase2i > const * 41 ConstPointerToArray< LVecBase2i > const * 0 0 1844 0 0 0 0 0 0 0 0 0 0

1844 39 ConstPointerToArray< LVecBase2i > const 0 8832 39 ConstPointerToArray< LVecBase2i > const 39 ConstPointerToArray< LVecBase2i > const 0 0 1630 0 0 0 0 0 0 0 0 0 0

1845 35 ConstPointerToArray< LVecBase2i > * 0 8576 35 ConstPointerToArray< LVecBase2i > * 35 ConstPointerToArray< LVecBase2i > * 0 0 1630 0 0 0 0 0 0 0 0 0 0

1846 36 PointerToArray< LVecBase2i > const * 0 8576 36 PointerToArray< LVecBase2i > const * 36 PointerToArray< LVecBase2i > const * 0 0 1847 0 0 0 0 0 0 0 0 0 0

1847 34 PointerToArray< LVecBase2i > const 0 8832 34 PointerToArray< LVecBase2i > const 34 PointerToArray< LVecBase2i > const 0 0 1661 0 0 0 0 0 0 0 0 0 0

1848 18 LVecBase2i const * 0 8576 18 LVecBase2i const * 18 LVecBase2i const * 0 0 1849 0 0 0 0 0 0 0 0 0 0

1849 16 LVecBase2i const 0 8832 16 LVecBase2i const 16 LVecBase2i const 0 0 1850 0 0 0 0 0 0 0 0 0 0

1850 10 LVecBase2i 0 2048 10 LVecBase2i 10 LVecBase2i 0 0 0 0 0 0 0 0 0 0 0 0 75
/**
 * This is the base class for all two-component vectors and points.
 */

1851 40 PointerToArrayBase< LVecBase2i > const * 0 8576 40 PointerToArrayBase< LVecBase2i > const * 40 PointerToArrayBase< LVecBase2i > const * 0 0 1852 0 0 0 0 0 0 0 0 0 0

1852 38 PointerToArrayBase< LVecBase2i > const 0 8832 38 PointerToArrayBase< LVecBase2i > const 38 PointerToArrayBase< LVecBase2i > const 0 0 1631 0 0 0 0 0 0 0 0 0 0

1853 55 PointerToBase< ReferenceCountedVector< LVecBase2i > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase2i > > * 55 PointerToBase< ReferenceCountedVector< LVecBase2i > > * 0 0 1632 0 0 0 0 0 0 0 0 0 0

1854 61 PointerToBase< ReferenceCountedVector< LVecBase2i > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase2i > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase2i > > const * 0 0 1855 0 0 0 0 0 0 0 0 0 0

1855 59 PointerToBase< ReferenceCountedVector< LVecBase2i > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase2i > > const 59 PointerToBase< ReferenceCountedVector< LVecBase2i > > const 0 0 1632 0 0 0 0 0 0 0 0 0 0

1856 41 ConstPointerToArray< LVecBase3d > const * 0 8576 41 ConstPointerToArray< LVecBase3d > const * 41 ConstPointerToArray< LVecBase3d > const * 0 0 1857 0 0 0 0 0 0 0 0 0 0

1857 39 ConstPointerToArray< LVecBase3d > const 0 8832 39 ConstPointerToArray< LVecBase3d > const 39 ConstPointerToArray< LVecBase3d > const 0 0 1633 0 0 0 0 0 0 0 0 0 0

1858 35 ConstPointerToArray< LVecBase3d > * 0 8576 35 ConstPointerToArray< LVecBase3d > * 35 ConstPointerToArray< LVecBase3d > * 0 0 1633 0 0 0 0 0 0 0 0 0 0

1859 36 PointerToArray< LVecBase3d > const * 0 8576 36 PointerToArray< LVecBase3d > const * 36 PointerToArray< LVecBase3d > const * 0 0 1860 0 0 0 0 0 0 0 0 0 0

1860 34 PointerToArray< LVecBase3d > const 0 8832 34 PointerToArray< LVecBase3d > const 34 PointerToArray< LVecBase3d > const 0 0 1662 0 0 0 0 0 0 0 0 0 0

1861 18 LVecBase3d const * 0 8576 18 LVecBase3d const * 18 LVecBase3d const * 0 0 1862 0 0 0 0 0 0 0 0 0 0

1862 16 LVecBase3d const 0 8832 16 LVecBase3d const 16 LVecBase3d const 0 0 1863 0 0 0 0 0 0 0 0 0 0

1863 10 LVecBase3d 0 2048 10 LVecBase3d 10 LVecBase3d 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1864 40 PointerToArrayBase< LVecBase3d > const * 0 8576 40 PointerToArrayBase< LVecBase3d > const * 40 PointerToArrayBase< LVecBase3d > const * 0 0 1865 0 0 0 0 0 0 0 0 0 0

1865 38 PointerToArrayBase< LVecBase3d > const 0 8832 38 PointerToArrayBase< LVecBase3d > const 38 PointerToArrayBase< LVecBase3d > const 0 0 1634 0 0 0 0 0 0 0 0 0 0

1866 55 PointerToBase< ReferenceCountedVector< LVecBase3d > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase3d > > * 55 PointerToBase< ReferenceCountedVector< LVecBase3d > > * 0 0 1635 0 0 0 0 0 0 0 0 0 0

1867 61 PointerToBase< ReferenceCountedVector< LVecBase3d > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase3d > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase3d > > const * 0 0 1868 0 0 0 0 0 0 0 0 0 0

1868 59 PointerToBase< ReferenceCountedVector< LVecBase3d > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase3d > > const 59 PointerToBase< ReferenceCountedVector< LVecBase3d > > const 0 0 1635 0 0 0 0 0 0 0 0 0 0

1869 41 ConstPointerToArray< LVecBase3f > const * 0 8576 41 ConstPointerToArray< LVecBase3f > const * 41 ConstPointerToArray< LVecBase3f > const * 0 0 1870 0 0 0 0 0 0 0 0 0 0

1870 39 ConstPointerToArray< LVecBase3f > const 0 8832 39 ConstPointerToArray< LVecBase3f > const 39 ConstPointerToArray< LVecBase3f > const 0 0 1636 0 0 0 0 0 0 0 0 0 0

1871 35 ConstPointerToArray< LVecBase3f > * 0 8576 35 ConstPointerToArray< LVecBase3f > * 35 ConstPointerToArray< LVecBase3f > * 0 0 1636 0 0 0 0 0 0 0 0 0 0

1872 36 PointerToArray< LVecBase3f > const * 0 8576 36 PointerToArray< LVecBase3f > const * 36 PointerToArray< LVecBase3f > const * 0 0 1873 0 0 0 0 0 0 0 0 0 0

1873 34 PointerToArray< LVecBase3f > const 0 8832 34 PointerToArray< LVecBase3f > const 34 PointerToArray< LVecBase3f > const 0 0 1663 0 0 0 0 0 0 0 0 0 0

1874 18 LVecBase3f const * 0 8576 18 LVecBase3f const * 18 LVecBase3f const * 0 0 1875 0 0 0 0 0 0 0 0 0 0

1875 16 LVecBase3f const 0 8832 16 LVecBase3f const 16 LVecBase3f const 0 0 1876 0 0 0 0 0 0 0 0 0 0

1876 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1877 40 PointerToArrayBase< LVecBase3f > const * 0 8576 40 PointerToArrayBase< LVecBase3f > const * 40 PointerToArrayBase< LVecBase3f > const * 0 0 1878 0 0 0 0 0 0 0 0 0 0

1878 38 PointerToArrayBase< LVecBase3f > const 0 8832 38 PointerToArrayBase< LVecBase3f > const 38 PointerToArrayBase< LVecBase3f > const 0 0 1637 0 0 0 0 0 0 0 0 0 0

1879 55 PointerToBase< ReferenceCountedVector< LVecBase3f > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase3f > > * 55 PointerToBase< ReferenceCountedVector< LVecBase3f > > * 0 0 1638 0 0 0 0 0 0 0 0 0 0

1880 61 PointerToBase< ReferenceCountedVector< LVecBase3f > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase3f > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase3f > > const * 0 0 1881 0 0 0 0 0 0 0 0 0 0

1881 59 PointerToBase< ReferenceCountedVector< LVecBase3f > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase3f > > const 59 PointerToBase< ReferenceCountedVector< LVecBase3f > > const 0 0 1638 0 0 0 0 0 0 0 0 0 0

1882 41 ConstPointerToArray< LVecBase3i > const * 0 8576 41 ConstPointerToArray< LVecBase3i > const * 41 ConstPointerToArray< LVecBase3i > const * 0 0 1883 0 0 0 0 0 0 0 0 0 0

1883 39 ConstPointerToArray< LVecBase3i > const 0 8832 39 ConstPointerToArray< LVecBase3i > const 39 ConstPointerToArray< LVecBase3i > const 0 0 1639 0 0 0 0 0 0 0 0 0 0

1884 35 ConstPointerToArray< LVecBase3i > * 0 8576 35 ConstPointerToArray< LVecBase3i > * 35 ConstPointerToArray< LVecBase3i > * 0 0 1639 0 0 0 0 0 0 0 0 0 0

1885 36 PointerToArray< LVecBase3i > const * 0 8576 36 PointerToArray< LVecBase3i > const * 36 PointerToArray< LVecBase3i > const * 0 0 1886 0 0 0 0 0 0 0 0 0 0

1886 34 PointerToArray< LVecBase3i > const 0 8832 34 PointerToArray< LVecBase3i > const 34 PointerToArray< LVecBase3i > const 0 0 1664 0 0 0 0 0 0 0 0 0 0

1887 18 LVecBase3i const * 0 8576 18 LVecBase3i const * 18 LVecBase3i const * 0 0 1888 0 0 0 0 0 0 0 0 0 0

1888 16 LVecBase3i const 0 8832 16 LVecBase3i const 16 LVecBase3i const 0 0 1889 0 0 0 0 0 0 0 0 0 0

1889 10 LVecBase3i 0 2048 10 LVecBase3i 10 LVecBase3i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1890 40 PointerToArrayBase< LVecBase3i > const * 0 8576 40 PointerToArrayBase< LVecBase3i > const * 40 PointerToArrayBase< LVecBase3i > const * 0 0 1891 0 0 0 0 0 0 0 0 0 0

1891 38 PointerToArrayBase< LVecBase3i > const 0 8832 38 PointerToArrayBase< LVecBase3i > const 38 PointerToArrayBase< LVecBase3i > const 0 0 1640 0 0 0 0 0 0 0 0 0 0

1892 55 PointerToBase< ReferenceCountedVector< LVecBase3i > > * 0 8576 55 PointerToBase< ReferenceCountedVector< LVecBase3i > > * 55 PointerToBase< ReferenceCountedVector< LVecBase3i > > * 0 0 1641 0 0 0 0 0 0 0 0 0 0

1893 61 PointerToBase< ReferenceCountedVector< LVecBase3i > > const * 0 8576 61 PointerToBase< ReferenceCountedVector< LVecBase3i > > const * 61 PointerToBase< ReferenceCountedVector< LVecBase3i > > const * 0 0 1894 0 0 0 0 0 0 0 0 0 0

1894 59 PointerToBase< ReferenceCountedVector< LVecBase3i > > const 0 8832 59 PointerToBase< ReferenceCountedVector< LVecBase3i > > const 59 PointerToBase< ReferenceCountedVector< LVecBase3i > > const 0 0 1641 0 0 0 0 0 0 0 0 0 0

1895 49 ConstPointerToArray< UnalignedLMatrix4d > const * 0 8576 49 ConstPointerToArray< UnalignedLMatrix4d > const * 49 ConstPointerToArray< UnalignedLMatrix4d > const * 0 0 1896 0 0 0 0 0 0 0 0 0 0

1896 47 ConstPointerToArray< UnalignedLMatrix4d > const 0 8832 47 ConstPointerToArray< UnalignedLMatrix4d > const 47 ConstPointerToArray< UnalignedLMatrix4d > const 0 0 1642 0 0 0 0 0 0 0 0 0 0

1897 43 ConstPointerToArray< UnalignedLMatrix4d > * 0 8576 43 ConstPointerToArray< UnalignedLMatrix4d > * 43 ConstPointerToArray< UnalignedLMatrix4d > * 0 0 1642 0 0 0 0 0 0 0 0 0 0

1898 44 PointerToArray< UnalignedLMatrix4d > const * 0 8576 44 PointerToArray< UnalignedLMatrix4d > const * 44 PointerToArray< UnalignedLMatrix4d > const * 0 0 1899 0 0 0 0 0 0 0 0 0 0

1899 42 PointerToArray< UnalignedLMatrix4d > const 0 8832 42 PointerToArray< UnalignedLMatrix4d > const 42 PointerToArray< UnalignedLMatrix4d > const 0 0 1665 0 0 0 0 0 0 0 0 0 0

1900 26 UnalignedLMatrix4d const * 0 8576 26 UnalignedLMatrix4d const * 26 UnalignedLMatrix4d const * 0 0 1901 0 0 0 0 0 0 0 0 0 0

1901 24 UnalignedLMatrix4d const 0 8832 24 UnalignedLMatrix4d const 24 UnalignedLMatrix4d const 0 0 1902 0 0 0 0 0 0 0 0 0 0

1902 18 UnalignedLMatrix4d 0 2048 18 UnalignedLMatrix4d 18 UnalignedLMatrix4d 0 0 0 0 0 0 0 0 0 0 0 0 385
/**
 * This is an "unaligned" LMatrix4.  It has no functionality other than to
 * store numbers, and it will pack them in as tightly as possible, avoiding
 * any SSE2 alignment requirements shared by the primary LMatrix4 class.
 *
 * Use it only when you need to pack numbers tightly without respect to
 * alignment, and then copy it to a proper LMatrix4 to get actual use from it.
 */

1903 48 PointerToArrayBase< UnalignedLMatrix4d > const * 0 8576 48 PointerToArrayBase< UnalignedLMatrix4d > const * 48 PointerToArrayBase< UnalignedLMatrix4d > const * 0 0 1904 0 0 0 0 0 0 0 0 0 0

1904 46 PointerToArrayBase< UnalignedLMatrix4d > const 0 8832 46 PointerToArrayBase< UnalignedLMatrix4d > const 46 PointerToArrayBase< UnalignedLMatrix4d > const 0 0 1643 0 0 0 0 0 0 0 0 0 0

1905 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > * 0 8576 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > * 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > * 0 0 1644 0 0 0 0 0 0 0 0 0 0

1906 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const * 0 8576 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const * 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const * 0 0 1907 0 0 0 0 0 0 0 0 0 0

1907 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const 0 8832 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4d > > const 0 0 1644 0 0 0 0 0 0 0 0 0 0

1908 49 ConstPointerToArray< UnalignedLMatrix4f > const * 0 8576 49 ConstPointerToArray< UnalignedLMatrix4f > const * 49 ConstPointerToArray< UnalignedLMatrix4f > const * 0 0 1909 0 0 0 0 0 0 0 0 0 0

1909 47 ConstPointerToArray< UnalignedLMatrix4f > const 0 8832 47 ConstPointerToArray< UnalignedLMatrix4f > const 47 ConstPointerToArray< UnalignedLMatrix4f > const 0 0 1645 0 0 0 0 0 0 0 0 0 0

1910 43 ConstPointerToArray< UnalignedLMatrix4f > * 0 8576 43 ConstPointerToArray< UnalignedLMatrix4f > * 43 ConstPointerToArray< UnalignedLMatrix4f > * 0 0 1645 0 0 0 0 0 0 0 0 0 0

1911 44 PointerToArray< UnalignedLMatrix4f > const * 0 8576 44 PointerToArray< UnalignedLMatrix4f > const * 44 PointerToArray< UnalignedLMatrix4f > const * 0 0 1912 0 0 0 0 0 0 0 0 0 0

1912 42 PointerToArray< UnalignedLMatrix4f > const 0 8832 42 PointerToArray< UnalignedLMatrix4f > const 42 PointerToArray< UnalignedLMatrix4f > const 0 0 1666 0 0 0 0 0 0 0 0 0 0

1913 26 UnalignedLMatrix4f const * 0 8576 26 UnalignedLMatrix4f const * 26 UnalignedLMatrix4f const * 0 0 1914 0 0 0 0 0 0 0 0 0 0

1914 24 UnalignedLMatrix4f const 0 8832 24 UnalignedLMatrix4f const 24 UnalignedLMatrix4f const 0 0 1915 0 0 0 0 0 0 0 0 0 0

1915 18 UnalignedLMatrix4f 0 2048 18 UnalignedLMatrix4f 18 UnalignedLMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 385
/**
 * This is an "unaligned" LMatrix4.  It has no functionality other than to
 * store numbers, and it will pack them in as tightly as possible, avoiding
 * any SSE2 alignment requirements shared by the primary LMatrix4 class.
 *
 * Use it only when you need to pack numbers tightly without respect to
 * alignment, and then copy it to a proper LMatrix4 to get actual use from it.
 */

1916 48 PointerToArrayBase< UnalignedLMatrix4f > const * 0 8576 48 PointerToArrayBase< UnalignedLMatrix4f > const * 48 PointerToArrayBase< UnalignedLMatrix4f > const * 0 0 1917 0 0 0 0 0 0 0 0 0 0

1917 46 PointerToArrayBase< UnalignedLMatrix4f > const 0 8832 46 PointerToArrayBase< UnalignedLMatrix4f > const 46 PointerToArrayBase< UnalignedLMatrix4f > const 0 0 1646 0 0 0 0 0 0 0 0 0 0

1918 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > * 0 8576 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > * 63 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > * 0 0 1647 0 0 0 0 0 0 0 0 0 0

1919 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const * 0 8576 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const * 69 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const * 0 0 1920 0 0 0 0 0 0 0 0 0 0

1920 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const 0 8832 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const 67 PointerToBase< ReferenceCountedVector< UnalignedLMatrix4f > > const 0 0 1647 0 0 0 0 0 0 0 0 0 0

1921 50 ConstPointerToArray< UnalignedLVecBase4d > const * 0 8576 50 ConstPointerToArray< UnalignedLVecBase4d > const * 50 ConstPointerToArray< UnalignedLVecBase4d > const * 0 0 1922 0 0 0 0 0 0 0 0 0 0

1922 48 ConstPointerToArray< UnalignedLVecBase4d > const 0 8832 48 ConstPointerToArray< UnalignedLVecBase4d > const 48 ConstPointerToArray< UnalignedLVecBase4d > const 0 0 1648 0 0 0 0 0 0 0 0 0 0

1923 44 ConstPointerToArray< UnalignedLVecBase4d > * 0 8576 44 ConstPointerToArray< UnalignedLVecBase4d > * 44 ConstPointerToArray< UnalignedLVecBase4d > * 0 0 1648 0 0 0 0 0 0 0 0 0 0

1924 45 PointerToArray< UnalignedLVecBase4d > const * 0 8576 45 PointerToArray< UnalignedLVecBase4d > const * 45 PointerToArray< UnalignedLVecBase4d > const * 0 0 1925 0 0 0 0 0 0 0 0 0 0

1925 43 PointerToArray< UnalignedLVecBase4d > const 0 8832 43 PointerToArray< UnalignedLVecBase4d > const 43 PointerToArray< UnalignedLVecBase4d > const 0 0 1667 0 0 0 0 0 0 0 0 0 0

1926 27 UnalignedLVecBase4d const * 0 8576 27 UnalignedLVecBase4d const * 27 UnalignedLVecBase4d const * 0 0 1927 0 0 0 0 0 0 0 0 0 0

1927 25 UnalignedLVecBase4d const 0 8832 25 UnalignedLVecBase4d const 25 UnalignedLVecBase4d const 0 0 1928 0 0 0 0 0 0 0 0 0 0

1928 19 UnalignedLVecBase4d 0 2048 19 UnalignedLVecBase4d 19 UnalignedLVecBase4d 0 0 0 0 0 0 0 0 0 0 0 0 391
/**
 * This is an "unaligned" LVecBase4.  It has no functionality other than to
 * store numbers, and it will pack them in as tightly as possible, avoiding
 * any SSE2 alignment requirements shared by the primary LVecBase4 class.
 *
 * Use it only when you need to pack numbers tightly without respect to
 * alignment, and then copy it to a proper LVecBase4 to get actual use from
 * it.
 */

1929 49 PointerToArrayBase< UnalignedLVecBase4d > const * 0 8576 49 PointerToArrayBase< UnalignedLVecBase4d > const * 49 PointerToArrayBase< UnalignedLVecBase4d > const * 0 0 1930 0 0 0 0 0 0 0 0 0 0

1930 47 PointerToArrayBase< UnalignedLVecBase4d > const 0 8832 47 PointerToArrayBase< UnalignedLVecBase4d > const 47 PointerToArrayBase< UnalignedLVecBase4d > const 0 0 1649 0 0 0 0 0 0 0 0 0 0

1931 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > * 0 8576 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > * 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > * 0 0 1650 0 0 0 0 0 0 0 0 0 0

1932 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const * 0 8576 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const * 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const * 0 0 1933 0 0 0 0 0 0 0 0 0 0

1933 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const 0 8832 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4d > > const 0 0 1650 0 0 0 0 0 0 0 0 0 0

1934 50 ConstPointerToArray< UnalignedLVecBase4f > const * 0 8576 50 ConstPointerToArray< UnalignedLVecBase4f > const * 50 ConstPointerToArray< UnalignedLVecBase4f > const * 0 0 1935 0 0 0 0 0 0 0 0 0 0

1935 48 ConstPointerToArray< UnalignedLVecBase4f > const 0 8832 48 ConstPointerToArray< UnalignedLVecBase4f > const 48 ConstPointerToArray< UnalignedLVecBase4f > const 0 0 1651 0 0 0 0 0 0 0 0 0 0

1936 44 ConstPointerToArray< UnalignedLVecBase4f > * 0 8576 44 ConstPointerToArray< UnalignedLVecBase4f > * 44 ConstPointerToArray< UnalignedLVecBase4f > * 0 0 1651 0 0 0 0 0 0 0 0 0 0

1937 45 PointerToArray< UnalignedLVecBase4f > const * 0 8576 45 PointerToArray< UnalignedLVecBase4f > const * 45 PointerToArray< UnalignedLVecBase4f > const * 0 0 1938 0 0 0 0 0 0 0 0 0 0

1938 43 PointerToArray< UnalignedLVecBase4f > const 0 8832 43 PointerToArray< UnalignedLVecBase4f > const 43 PointerToArray< UnalignedLVecBase4f > const 0 0 1668 0 0 0 0 0 0 0 0 0 0

1939 27 UnalignedLVecBase4f const * 0 8576 27 UnalignedLVecBase4f const * 27 UnalignedLVecBase4f const * 0 0 1940 0 0 0 0 0 0 0 0 0 0

1940 25 UnalignedLVecBase4f const 0 8832 25 UnalignedLVecBase4f const 25 UnalignedLVecBase4f const 0 0 1941 0 0 0 0 0 0 0 0 0 0

1941 19 UnalignedLVecBase4f 0 2048 19 UnalignedLVecBase4f 19 UnalignedLVecBase4f 0 0 0 0 0 0 0 0 0 0 0 0 391
/**
 * This is an "unaligned" LVecBase4.  It has no functionality other than to
 * store numbers, and it will pack them in as tightly as possible, avoiding
 * any SSE2 alignment requirements shared by the primary LVecBase4 class.
 *
 * Use it only when you need to pack numbers tightly without respect to
 * alignment, and then copy it to a proper LVecBase4 to get actual use from
 * it.
 */

1942 49 PointerToArrayBase< UnalignedLVecBase4f > const * 0 8576 49 PointerToArrayBase< UnalignedLVecBase4f > const * 49 PointerToArrayBase< UnalignedLVecBase4f > const * 0 0 1943 0 0 0 0 0 0 0 0 0 0

1943 47 PointerToArrayBase< UnalignedLVecBase4f > const 0 8832 47 PointerToArrayBase< UnalignedLVecBase4f > const 47 PointerToArrayBase< UnalignedLVecBase4f > const 0 0 1652 0 0 0 0 0 0 0 0 0 0

1944 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > * 0 8576 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > * 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > * 0 0 1653 0 0 0 0 0 0 0 0 0 0

1945 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const * 0 8576 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const * 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const * 0 0 1946 0 0 0 0 0 0 0 0 0 0

1946 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const 0 8832 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4f > > const 0 0 1653 0 0 0 0 0 0 0 0 0 0

1947 50 ConstPointerToArray< UnalignedLVecBase4i > const * 0 8576 50 ConstPointerToArray< UnalignedLVecBase4i > const * 50 ConstPointerToArray< UnalignedLVecBase4i > const * 0 0 1948 0 0 0 0 0 0 0 0 0 0

1948 48 ConstPointerToArray< UnalignedLVecBase4i > const 0 8832 48 ConstPointerToArray< UnalignedLVecBase4i > const 48 ConstPointerToArray< UnalignedLVecBase4i > const 0 0 1654 0 0 0 0 0 0 0 0 0 0

1949 44 ConstPointerToArray< UnalignedLVecBase4i > * 0 8576 44 ConstPointerToArray< UnalignedLVecBase4i > * 44 ConstPointerToArray< UnalignedLVecBase4i > * 0 0 1654 0 0 0 0 0 0 0 0 0 0

1950 45 PointerToArray< UnalignedLVecBase4i > const * 0 8576 45 PointerToArray< UnalignedLVecBase4i > const * 45 PointerToArray< UnalignedLVecBase4i > const * 0 0 1951 0 0 0 0 0 0 0 0 0 0

1951 43 PointerToArray< UnalignedLVecBase4i > const 0 8832 43 PointerToArray< UnalignedLVecBase4i > const 43 PointerToArray< UnalignedLVecBase4i > const 0 0 1669 0 0 0 0 0 0 0 0 0 0

1952 27 UnalignedLVecBase4i const * 0 8576 27 UnalignedLVecBase4i const * 27 UnalignedLVecBase4i const * 0 0 1953 0 0 0 0 0 0 0 0 0 0

1953 25 UnalignedLVecBase4i const 0 8832 25 UnalignedLVecBase4i const 25 UnalignedLVecBase4i const 0 0 1954 0 0 0 0 0 0 0 0 0 0

1954 19 UnalignedLVecBase4i 0 2048 19 UnalignedLVecBase4i 19 UnalignedLVecBase4i 0 0 0 0 0 0 0 0 0 0 0 0 391
/**
 * This is an "unaligned" LVecBase4.  It has no functionality other than to
 * store numbers, and it will pack them in as tightly as possible, avoiding
 * any SSE2 alignment requirements shared by the primary LVecBase4 class.
 *
 * Use it only when you need to pack numbers tightly without respect to
 * alignment, and then copy it to a proper LVecBase4 to get actual use from
 * it.
 */

1955 49 PointerToArrayBase< UnalignedLVecBase4i > const * 0 8576 49 PointerToArrayBase< UnalignedLVecBase4i > const * 49 PointerToArrayBase< UnalignedLVecBase4i > const * 0 0 1956 0 0 0 0 0 0 0 0 0 0

1956 47 PointerToArrayBase< UnalignedLVecBase4i > const 0 8832 47 PointerToArrayBase< UnalignedLVecBase4i > const 47 PointerToArrayBase< UnalignedLVecBase4i > const 0 0 1655 0 0 0 0 0 0 0 0 0 0

1957 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > * 0 8576 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > * 64 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > * 0 0 1656 0 0 0 0 0 0 0 0 0 0

1958 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const * 0 8576 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const * 70 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const * 0 0 1959 0 0 0 0 0 0 0 0 0 0

1959 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const 0 8832 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const 68 PointerToBase< ReferenceCountedVector< UnalignedLVecBase4i > > const 0 0 1656 0 0 0 0 0 0 0 0 0 0

1960 29 PointerToArray< LMatrix3d > * 0 8576 29 PointerToArray< LMatrix3d > * 29 PointerToArray< LMatrix3d > * 0 0 1657 0 0 0 0 0 0 0 0 0 0

1961 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 1962 0 0 0 0 0 0 0 0 0 0

1962 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

1963 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

1964 29 PointerToArray< LMatrix3f > * 0 8576 29 PointerToArray< LMatrix3f > * 29 PointerToArray< LMatrix3f > * 0 0 1658 0 0 0 0 0 0 0 0 0 0

1965 30 PointerToArray< LVecBase2d > * 0 8576 30 PointerToArray< LVecBase2d > * 30 PointerToArray< LVecBase2d > * 0 0 1659 0 0 0 0 0 0 0 0 0 0

1966 30 PointerToArray< LVecBase2f > * 0 8576 30 PointerToArray< LVecBase2f > * 30 PointerToArray< LVecBase2f > * 0 0 1660 0 0 0 0 0 0 0 0 0 0

1967 30 PointerToArray< LVecBase2i > * 0 8576 30 PointerToArray< LVecBase2i > * 30 PointerToArray< LVecBase2i > * 0 0 1661 0 0 0 0 0 0 0 0 0 0

1968 30 PointerToArray< LVecBase3d > * 0 8576 30 PointerToArray< LVecBase3d > * 30 PointerToArray< LVecBase3d > * 0 0 1662 0 0 0 0 0 0 0 0 0 0

1969 30 PointerToArray< LVecBase3f > * 0 8576 30 PointerToArray< LVecBase3f > * 30 PointerToArray< LVecBase3f > * 0 0 1663 0 0 0 0 0 0 0 0 0 0

1970 30 PointerToArray< LVecBase3i > * 0 8576 30 PointerToArray< LVecBase3i > * 30 PointerToArray< LVecBase3i > * 0 0 1664 0 0 0 0 0 0 0 0 0 0

1971 38 PointerToArray< UnalignedLMatrix4d > * 0 8576 38 PointerToArray< UnalignedLMatrix4d > * 38 PointerToArray< UnalignedLMatrix4d > * 0 0 1665 0 0 0 0 0 0 0 0 0 0

1972 38 PointerToArray< UnalignedLMatrix4f > * 0 8576 38 PointerToArray< UnalignedLMatrix4f > * 38 PointerToArray< UnalignedLMatrix4f > * 0 0 1666 0 0 0 0 0 0 0 0 0 0

1973 39 PointerToArray< UnalignedLVecBase4d > * 0 8576 39 PointerToArray< UnalignedLVecBase4d > * 39 PointerToArray< UnalignedLVecBase4d > * 0 0 1667 0 0 0 0 0 0 0 0 0 0

1974 39 PointerToArray< UnalignedLVecBase4f > * 0 8576 39 PointerToArray< UnalignedLVecBase4f > * 39 PointerToArray< UnalignedLVecBase4f > * 0 0 1668 0 0 0 0 0 0 0 0 0 0

1975 39 PointerToArray< UnalignedLVecBase4i > * 0 8576 39 PointerToArray< UnalignedLVecBase4i > * 39 PointerToArray< UnalignedLVecBase4i > * 0 0 1669 0 0 0 0 0 0 0 0 0 0

1976 22 BoundingVolume const * 0 8576 22 BoundingVolume const * 22 BoundingVolume const * 0 0 1977 0 0 0 0 0 0 0 0 0 0

1977 20 BoundingVolume const 0 8832 20 BoundingVolume const 20 BoundingVolume const 0 0 1670 0 0 0 0 0 0 0 0 0 0

1978 16 BoundingVolume * 0 8576 16 BoundingVolume * 16 BoundingVolume * 0 0 1670 0 0 0 0 0 0 0 0 0 0

1979 25 GeometricBoundingVolume * 0 8576 25 GeometricBoundingVolume * 25 GeometricBoundingVolume * 0 0 1674 0 0 0 0 0 0 0 0 0 0

1980 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 1981 0 0 0 0 0 0 0 0 0 0

1981 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 1676 0 0 0 0 0 0 0 0 0 0

1982 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 1676 0 0 0 0 0 0 0 0 0 0

1983 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 1984 0 0 0 0 0 0 0 0 0 0

1984 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 1985 0 0 0 0 0 0 0 0 0 0

1985 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 1986 0 0 0 0 0 0 0 0 0 0

1986 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

1987 28 FiniteBoundingVolume const * 0 8576 28 FiniteBoundingVolume const * 28 FiniteBoundingVolume const * 0 0 1988 0 0 0 0 0 0 0 0 0 0

1988 26 FiniteBoundingVolume const 0 8832 26 FiniteBoundingVolume const 26 FiniteBoundingVolume const 0 0 1675 0 0 0 0 0 0 0 0 0 0

1989 12 LParabolaf * 0 8576 12 LParabolaf * 12 LParabolaf * 0 0 1680 0 0 0 0 0 0 0 0 0 0

1990 18 LParabolaf const * 0 8576 18 LParabolaf const * 18 LParabolaf const * 0 0 1991 0 0 0 0 0 0 0 0 0 0

1991 16 LParabolaf const 0 8832 16 LParabolaf const 16 LParabolaf const 0 0 1680 0 0 0 0 0 0 0 0 0 0

1992 17 LMatrix4f const * 0 8576 17 LMatrix4f const * 17 LMatrix4f const * 0 0 1993 0 0 0 0 0 0 0 0 0 0

1993 15 LMatrix4f const 0 8832 15 LMatrix4f const 15 LMatrix4f const 0 0 1986 0 0 0 0 0 0 0 0 0 0

1994 10 LPoint3f * 0 8576 10 LPoint3f * 10 LPoint3f * 0 0 1677 0 0 0 0 0 0 0 0 0 0

1995 10 Datagram * 0 8576 10 Datagram * 10 Datagram * 0 0 1996 0 0 0 0 0 0 0 0 0 0

1996 8 Datagram 0 2048 8 Datagram 8 Datagram 0 0 0 0 0 0 0 0 0 0 0 0 565
/**
 * An ordered list of data elements, formatted in memory for transmission over
 * a socket or writing to a data file.
 *
 * Data elements should be added one at a time, in order, to the Datagram.
 * The nature and contents of the data elements are totally up to the user.
 * When a Datagram has been transmitted and received, its data elements may be
 * extracted using a DatagramIterator; it is up to the caller to know the
 * correct type of each data element in order.
 *
 * A Datagram is itself headerless; it is simply a collection of data
 * elements.
 */

1997 18 DatagramIterator * 0 8576 18 DatagramIterator * 18 DatagramIterator * 0 0 1998 0 0 0 0 0 0 0 0 0 0

1998 16 DatagramIterator 0 2048 16 DatagramIterator 16 DatagramIterator 0 0 0 0 0 0 0 0 0 0 0 0 365
/**
 * A class to retrieve the individual data elements previously stored in a
 * Datagram.  Elements may be retrieved one at a time; it is up to the caller
 * to know the correct type and order of each element.
 *
 * Note that it is the responsibility of the caller to ensure that the datagram
 * object is not destructed while this DatagramIterator is in use.
 */

1999 12 LParabolad * 0 8576 12 LParabolad * 12 LParabolad * 0 0 1681 0 0 0 0 0 0 0 0 0 0

2000 18 LParabolad const * 0 8576 18 LParabolad const * 18 LParabolad const * 0 0 2001 0 0 0 0 0 0 0 0 0 0

2001 16 LParabolad const 0 8832 16 LParabolad const 16 LParabolad const 0 0 1681 0 0 0 0 0 0 0 0 0 0

2002 17 LMatrix4d const * 0 8576 17 LMatrix4d const * 17 LMatrix4d const * 0 0 2003 0 0 0 0 0 0 0 0 0 0

2003 15 LMatrix4d const 0 8832 15 LMatrix4d const 15 LMatrix4d const 0 0 2004 0 0 0 0 0 0 0 0 0 0

2004 9 LMatrix4d 0 2048 9 LMatrix4d 9 LMatrix4d 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

2005 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

2006 10 LPoint3d * 0 8576 10 LPoint3d * 10 LPoint3d * 0 0 1777 0 0 0 0 0 0 0 0 0 0

2007 9 LPlanef * 0 8576 9 LPlanef * 9 LPlanef * 0 0 1683 0 0 0 0 0 0 0 0 0 0

2008 15 LPlanef const * 0 8576 15 LPlanef const * 15 LPlanef const * 0 0 2009 0 0 0 0 0 0 0 0 0 0

2009 13 LPlanef const 0 8832 13 LPlanef const 13 LPlanef const 0 0 1683 0 0 0 0 0 0 0 0 0 0

2010 16 LPoint3f const * 0 8576 16 LPoint3f const * 16 LPoint3f const * 0 0 2011 0 0 0 0 0 0 0 0 0 0

2011 14 LPoint3f const 0 8832 14 LPoint3f const 14 LPoint3f const 0 0 1677 0 0 0 0 0 0 0 0 0 0

2012 18 LVecBase4f const * 0 8576 18 LVecBase4f const * 18 LVecBase4f const * 0 0 2013 0 0 0 0 0 0 0 0 0 0

2013 16 LVecBase4f const 0 8832 16 LVecBase4f const 16 LVecBase4f const 0 0 1684 0 0 0 0 0 0 0 0 0 0

2014 17 LVector3f const * 0 8576 17 LVector3f const * 17 LVector3f const * 0 0 2015 0 0 0 0 0 0 0 0 0 0

2015 15 LVector3f const 0 8832 15 LVector3f const 15 LVector3f const 0 0 2016 0 0 0 0 0 0 0 0 0 0

2016 9 LVector3f 0 2048 9 LVector3f 9 LVector3f 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

2017 11 LMatrix4f * 0 8576 11 LMatrix4f * 11 LMatrix4f * 0 0 1986 0 0 0 0 0 0 0 0 0 0

2018 11 LVector3f * 0 8576 11 LVector3f * 11 LVector3f * 0 0 2016 0 0 0 0 0 0 0 0 0 0

2019 9 LPlaned * 0 8576 9 LPlaned * 9 LPlaned * 0 0 1685 0 0 0 0 0 0 0 0 0 0

2020 15 LPlaned const * 0 8576 15 LPlaned const * 15 LPlaned const * 0 0 1778 0 0 0 0 0 0 0 0 0 0

2021 16 LPoint3d const * 0 8576 16 LPoint3d const * 16 LPoint3d const * 0 0 1776 0 0 0 0 0 0 0 0 0 0

2022 18 LVecBase4d const * 0 8576 18 LVecBase4d const * 18 LVecBase4d const * 0 0 2023 0 0 0 0 0 0 0 0 0 0

2023 16 LVecBase4d const 0 8832 16 LVecBase4d const 16 LVecBase4d const 0 0 1686 0 0 0 0 0 0 0 0 0 0

2024 17 LVector3d const * 0 8576 17 LVector3d const * 17 LVector3d const * 0 0 2025 0 0 0 0 0 0 0 0 0 0

2025 15 LVector3d const 0 8832 15 LVector3d const 15 LVector3d const 0 0 2026 0 0 0 0 0 0 0 0 0 0

2026 9 LVector3d 0 2048 9 LVector3d 9 LVector3d 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

2027 11 LMatrix4d * 0 8576 11 LMatrix4d * 11 LMatrix4d * 0 0 2004 0 0 0 0 0 0 0 0 0 0

2028 11 LVector3d * 0 8576 11 LVector3d * 11 LVector3d * 0 0 2026 0 0 0 0 0 0 0 0 0 0

2029 13 BoundingBox * 0 8576 13 BoundingBox * 13 BoundingBox * 0 0 1691 0 0 0 0 0 0 0 0 0 0

2030 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 1785 0 0 0 0 0 0 0 0 0 0

2031 19 BoundingBox const * 0 8576 19 BoundingBox const * 19 BoundingBox const * 0 0 2032 0 0 0 0 0 0 0 0 0 0

2032 17 BoundingBox const 0 8832 17 BoundingBox const 17 BoundingBox const 0 0 1691 0 0 0 0 0 0 0 0 0 0

2033 8 LPlane * 0 8576 8 LPlane * 8 LPlane * 0 0 1687 0 0 0 0 0 0 0 0 0 0

2034 11 LFrustumf * 0 8576 11 LFrustumf * 11 LFrustumf * 0 0 1692 0 0 0 0 0 0 0 0 0 0

2035 17 LFrustumf const * 0 8576 17 LFrustumf const * 17 LFrustumf const * 0 0 2036 0 0 0 0 0 0 0 0 0 0

2036 15 LFrustumf const 0 8832 15 LFrustumf const 15 LFrustumf const 0 0 1692 0 0 0 0 0 0 0 0 0 0

2037 11 LFrustumd * 0 8576 11 LFrustumd * 11 LFrustumd * 0 0 1693 0 0 0 0 0 0 0 0 0 0

2038 17 LFrustumd const * 0 8576 17 LFrustumd const * 17 LFrustumd const * 0 0 2039 0 0 0 0 0 0 0 0 0 0

2039 15 LFrustumd const 0 8832 15 LFrustumd const 15 LFrustumd const 0 0 1693 0 0 0 0 0 0 0 0 0 0

2040 16 LFrustum const * 0 8576 16 LFrustum const * 16 LFrustum const * 0 0 2041 0 0 0 0 0 0 0 0 0 0

2041 14 LFrustum const 0 8832 14 LFrustum const 14 LFrustum const 0 0 1694 0 0 0 0 0 0 0 0 0 0

2042 16 CoordinateSystem 0 532480 16 CoordinateSystem 16 CoordinateSystem 0 0 0 0 0 0 0 0 0 0 6 10 CS_default 10 CS_default 210
// The CS_default entry does not refer to a particular coordinate system,
// but rather to the value stored in default_coordinate_system, which in
// turn is loaded from the config variable "coordinate-system".
0 12 CS_zup_right 12 CS_zup_right 21
// Z-Up, Right-handed
1 12 CS_yup_right 12 CS_yup_right 21
// Y-Up, Right-handed
2 11 CS_zup_left 11 CS_zup_left 20
// Z-Up, Left-handed
3 11 CS_yup_left 11 CS_yup_left 20
// Y-Up, Left-handed
4 10 CS_invalid 10 CS_invalid 156
// CS_invalid is not a coordinate system at all.  It can be used in user-
// input processing code to indicate a contradictory coordinate system
// request.
5 0 0

2043 20 BoundingHexahedron * 0 8576 20 BoundingHexahedron * 20 BoundingHexahedron * 0 0 1698 0 0 0 0 0 0 0 0 0 0

2044 26 BoundingHexahedron const * 0 8576 26 BoundingHexahedron const * 26 BoundingHexahedron const * 0 0 2045 0 0 0 0 0 0 0 0 0 0

2045 24 BoundingHexahedron const 0 8832 24 BoundingHexahedron const 24 BoundingHexahedron const 0 0 1698 0 0 0 0 0 0 0 0 0 0

2046 14 BoundingLine * 0 8576 14 BoundingLine * 14 BoundingLine * 0 0 1699 0 0 0 0 0 0 0 0 0 0

2047 20 BoundingLine const * 0 8576 20 BoundingLine const * 20 BoundingLine const * 0 0 2048 0 0 0 0 0 0 0 0 0 0

2048 18 BoundingLine const 0 8832 18 BoundingLine const 18 BoundingLine const 0 0 1699 0 0 0 0 0 0 0 0 0 0

2049 15 BoundingPlane * 0 8576 15 BoundingPlane * 15 BoundingPlane * 0 0 1700 0 0 0 0 0 0 0 0 0 0

2050 14 LPlane const * 0 8576 14 LPlane const * 14 LPlane const * 0 0 1701 0 0 0 0 0 0 0 0 0 0

2051 21 BoundingPlane const * 0 8576 21 BoundingPlane const * 21 BoundingPlane const * 0 0 2052 0 0 0 0 0 0 0 0 0 0

2052 19 BoundingPlane const 0 8832 19 BoundingPlane const 19 BoundingPlane const 0 0 1700 0 0 0 0 0 0 0 0 0 0

2053 16 BoundingSphere * 0 8576 16 BoundingSphere * 16 BoundingSphere * 0 0 1702 0 0 0 0 0 0 0 0 0 0

2054 22 BoundingSphere const * 0 8576 22 BoundingSphere const * 22 BoundingSphere const * 0 0 2055 0 0 0 0 0 0 0 0 0 0

2055 20 BoundingSphere const 0 8832 20 BoundingSphere const 20 BoundingSphere const 0 0 1702 0 0 0 0 0 0 0 0 0 0

2056 28 IntersectionBoundingVolume * 0 8576 28 IntersectionBoundingVolume * 28 IntersectionBoundingVolume * 0 0 1703 0 0 0 0 0 0 0 0 0 0

2057 34 IntersectionBoundingVolume const * 0 8576 34 IntersectionBoundingVolume const * 34 IntersectionBoundingVolume const * 0 0 2058 0 0 0 0 0 0 0 0 0 0

2058 32 IntersectionBoundingVolume const 0 8832 32 IntersectionBoundingVolume const 32 IntersectionBoundingVolume const 0 0 1703 0 0 0 0 0 0 0 0 0 0

2059 16 Mersenne const * 0 8576 16 Mersenne const * 16 Mersenne const * 0 0 2060 0 0 0 0 0 0 0 0 0 0

2060 14 Mersenne const 0 8832 14 Mersenne const 14 Mersenne const 0 0 1706 0 0 0 0 0 0 0 0 0 0

2061 10 Mersenne * 0 8576 10 Mersenne * 10 Mersenne * 0 0 1706 0 0 0 0 0 0 0 0 0 0

2062 17 unsigned long int 0 8214 17 unsigned long int 17 unsigned long int 0 1 0 0 0 0 0 0 0 0 0 0 0

2063 20 OmniBoundingVolume * 0 8576 20 OmniBoundingVolume * 20 OmniBoundingVolume * 0 0 1708 0 0 0 0 0 0 0 0 0 0

2064 21 UnionBoundingVolume * 0 8576 21 UnionBoundingVolume * 21 UnionBoundingVolume * 0 0 1709 0 0 0 0 0 0 0 0 0 0

2065 27 UnionBoundingVolume const * 0 8576 27 UnionBoundingVolume const * 27 UnionBoundingVolume const * 0 0 2066 0 0 0 0 0 0 0 0 0 0

2066 25 UnionBoundingVolume const 0 8832 25 UnionBoundingVolume const 25 UnionBoundingVolume const 0 0 1709 0 0 0 0 0 0 0 0 0 0

2067 18 Randomizer const * 0 8576 18 Randomizer const * 18 Randomizer const * 0 0 2068 0 0 0 0 0 0 0 0 0 0

2068 16 Randomizer const 0 8832 16 Randomizer const 16 Randomizer const 0 0 1710 0 0 0 0 0 0 0 0 0 0

2069 12 Randomizer * 0 8576 12 Randomizer * 12 Randomizer * 0 0 1710 0 0 0 0 0 0 0 0 0 0

2070 13 PerlinNoise * 0 8576 13 PerlinNoise * 13 PerlinNoise * 0 0 1711 0 0 0 0 0 0 0 0 0 0

2071 14 PerlinNoise2 * 0 8576 14 PerlinNoise2 * 14 PerlinNoise2 * 0 0 1712 0 0 0 0 0 0 0 0 0 0

2072 20 PerlinNoise2 const * 0 8576 20 PerlinNoise2 const * 20 PerlinNoise2 const * 0 0 2073 0 0 0 0 0 0 0 0 0 0

2073 18 PerlinNoise2 const 0 8832 18 PerlinNoise2 const 18 PerlinNoise2 const 0 0 1712 0 0 0 0 0 0 0 0 0 0

2074 14 PerlinNoise3 * 0 8576 14 PerlinNoise3 * 14 PerlinNoise3 * 0 0 1713 0 0 0 0 0 0 0 0 0 0

2075 20 PerlinNoise3 const * 0 8576 20 PerlinNoise3 const * 20 PerlinNoise3 const * 0 0 2076 0 0 0 0 0 0 0 0 0 0

2076 18 PerlinNoise3 const 0 8832 18 PerlinNoise3 const 18 PerlinNoise3 const 0 0 1713 0 0 0 0 0 0 0 0 0 0

2077 21 StackedPerlinNoise2 * 0 8576 21 StackedPerlinNoise2 * 21 StackedPerlinNoise2 * 0 0 1770 0 0 0 0 0 0 0 0 0 0

2078 27 StackedPerlinNoise2 const * 0 8576 27 StackedPerlinNoise2 const * 27 StackedPerlinNoise2 const * 0 0 2079 0 0 0 0 0 0 0 0 0 0

2079 25 StackedPerlinNoise2 const 0 8832 25 StackedPerlinNoise2 const 25 StackedPerlinNoise2 const 0 0 1770 0 0 0 0 0 0 0 0 0 0

2080 21 StackedPerlinNoise3 * 0 8576 21 StackedPerlinNoise3 * 21 StackedPerlinNoise3 * 0 0 1771 0 0 0 0 0 0 0 0 0 0

2081 27 StackedPerlinNoise3 const * 0 8576 27 StackedPerlinNoise3 const * 27 StackedPerlinNoise3 const * 0 0 2082 0 0 0 0 0 0 0 0 0 0

2082 25 StackedPerlinNoise3 const 0 8832 25 StackedPerlinNoise3 const 25 StackedPerlinNoise3 const 0 0 1771 0 0 0 0 0 0 0 0 0 0

2083 14 Triangulator * 0 8576 14 Triangulator * 14 Triangulator * 0 0 1772 0 0 0 0 0 0 0 0 0 0

2084 20 Triangulator const * 0 8576 20 Triangulator const * 20 Triangulator const * 0 0 2085 0 0 0 0 0 0 0 0 0 0

2085 18 Triangulator const 0 8832 18 Triangulator const 18 Triangulator const 0 0 1772 0 0 0 0 0 0 0 0 0 0

2086 16 LPoint2d const * 0 8576 16 LPoint2d const * 16 LPoint2d const * 0 0 1773 0 0 0 0 0 0 0 0 0 0

2087 15 Triangulator3 * 0 8576 15 Triangulator3 * 15 Triangulator3 * 0 0 1775 0 0 0 0 0 0 0 0 0 0

2088 21 Triangulator3 const * 0 8576 21 Triangulator3 const * 21 Triangulator3 const * 0 0 2089 0 0 0 0 0 0 0 0 0 0

2089 19 Triangulator3 const 0 8832 19 Triangulator3 const 19 Triangulator3 const 0 0 1775 0 0 0 0 0 0 0 0 0 0

2090 11 LMatrix3d * 0 8576 11 LMatrix3d * 11 LMatrix3d * 0 0 1788 0 0 0 0 0 0 0 0 0 0

2091 11 LMatrix3f * 0 8576 11 LMatrix3f * 11 LMatrix3f * 0 0 1811 0 0 0 0 0 0 0 0 0 0

2092 14 LQuaterniond * 0 8576 14 LQuaterniond * 14 LQuaterniond * 0 0 2093 0 0 0 0 0 0 0 0 0 0

2093 12 LQuaterniond 0 2048 12 LQuaterniond 12 LQuaterniond 0 0 0 0 0 0 0 0 0 0 0 0 44
/**
 * This is the base quaternion class
 */

2094 14 LQuaternionf * 0 8576 14 LQuaternionf * 14 LQuaternionf * 0 0 2095 0 0 0 0 0 0 0 0 0 0

2095 12 LQuaternionf 0 2048 12 LQuaternionf 12 LQuaternionf 0 0 0 0 0 0 0 0 0 0 0 0 44
/**
 * This is the base quaternion class
 */

0
15
2096 3 min 0 2 1676 1395 0 0 0 0 0 0 0 25 FiniteBoundingVolume::min 0

2097 3 max 0 2 1676 1396 0 0 0 0 0 0 0 25 FiniteBoundingVolume::max 0

2098 6 volume 0 2 1678 1397 0 0 0 0 0 0 0 28 FiniteBoundingVolume::volume 0

2099 6 points 0 66 1676 1473 0 0 0 0 1472 0 0 19 BoundingBox::points 0

2100 6 planes 0 66 1687 1475 0 0 0 0 1474 0 0 19 BoundingBox::planes 0

2101 6 points 0 66 1676 1497 0 0 0 0 1496 0 0 26 BoundingHexahedron::points 0

2102 6 planes 0 66 1687 1499 0 0 0 0 1498 0 0 26 BoundingHexahedron::planes 0

2103 5 plane 0 2 1701 1514 0 0 0 0 0 0 0 20 BoundingPlane::plane 0

2104 6 center 0 6 1676 1521 1523 0 0 0 0 0 0 22 BoundingSphere::center 0

2105 6 radius 0 6 1678 1522 1524 0 0 0 0 0 0 22 BoundingSphere::radius 0

2106 10 components 0 66 1704 1532 0 0 0 0 1531 0 0 38 IntersectionBoundingVolume::components 0

2107 10 components 0 66 1704 1550 0 0 0 0 1549 0 0 31 UnionBoundingVolume::components 0

2108 8 vertices 0 66 1773 1597 0 0 0 0 1596 0 0 22 Triangulator::vertices 0

2109 8 vertices 0 66 1776 1613 0 0 0 0 1612 0 0 23 Triangulator3::vertices 0

2110 5 plane 0 2 1778 1615 0 0 0 0 0 0 0 20 Triangulator3::plane 0

8
2111 10 get_points 0 1472 1473 23 BoundingBox::get_points 0

2112 10 get_planes 0 1474 1475 23 BoundingBox::get_planes 0

2113 10 get_points 0 1496 1497 30 BoundingHexahedron::get_points 0

2114 10 get_planes 0 1498 1499 30 BoundingHexahedron::get_planes 0

2115 14 get_components 0 1531 1532 42 IntersectionBoundingVolume::get_components 0

2116 14 get_components 0 1549 1550 35 UnionBoundingVolume::get_components 0

2117 12 get_vertices 0 1596 1597 26 Triangulator::get_vertices 0

2118 12 get_vertices 0 1612 1613 27 Triangulator3::get_vertices 0

