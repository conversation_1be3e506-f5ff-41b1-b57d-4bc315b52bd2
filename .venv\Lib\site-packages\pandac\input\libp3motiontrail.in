1730992849
3 3
16 libp3motiontrail 4 HtUQ 14 panda3d.direct 
10
12 12 CMotionTrail 0 260 22 26 CMotionTrail::CMotionTrail 0 2 1 2 22
/**
 * Constructor
 */
100
CMotionTrail::CMotionTrail(void);
inline CMotionTrail::CMotionTrail(CMotionTrail const &) = default;

13 5 reset 0 4 22 19 CMotionTrail::reset 0 1 3 42
/**
 * Reset the frame sample history.
 */
31
void CMotionTrail::reset(void);

14 17 reset_vertex_list 0 4 22 31 CMotionTrail::reset_vertex_list 0 1 4 33
/**
 * Reset the vertex list.
 */
43
void CMotionTrail::reset_vertex_list(void);

15 6 enable 0 4 22 20 CMotionTrail::enable 0 1 5 43
/**
 * Enable/disable the motion trail.
 */
39
void CMotionTrail::enable(bool enable);

16 13 set_geom_node 0 4 22 27 CMotionTrail::set_geom_node 0 1 6 28
/**
 * Set the GeomNode.
 */
54
void CMotionTrail::set_geom_node(GeomNode *geom_node);

17 10 add_vertex 0 4 22 24 CMotionTrail::add_vertex 0 1 7 24
/**
 * Add a vertex.
 */
107
void CMotionTrail::add_vertex(LVector4 *vertex, LVector4 *start_color, LVector4 *end_color, PN_stdfloat v);

18 14 set_parameters 0 4 22 28 CMotionTrail::set_parameters 0 1 8 622
/**
 * Set motion trail parameters.
 *
 * sampling_time = Can be used to specify a lower sampling rate than the frame
 * rate.  Use 0.0 with nurbs.
 *
 * time_window = a component for the "length" of the motion trail.  The motion
 * trail length = time_window * velocity of the object.
 *
 * use_texture = texture option on/off.
 *
 * calculate_relative_matrix = calculate relative matrix on/off.
 *
 * use_nurbs = nurbs option on/off
 *
 * resolution_distance = the distance used to determine the number of geometry
 * samples.  samples = motion trail length / resolution_distance.  Applicable
 * only if nurbs is on.
 */
185
void CMotionTrail::set_parameters(PN_stdfloat sampling_time, PN_stdfloat time_window, bool use_texture, bool calculate_relative_matrix, bool use_nurbs, PN_stdfloat resolution_distance);

19 16 check_for_update 0 4 22 30 CMotionTrail::check_for_update 0 1 9 46
/**
 * Check if a sample can be submitted.
 */
61
int CMotionTrail::check_for_update(PN_stdfloat current_time);

20 19 update_motion_trail 0 4 22 33 CMotionTrail::update_motion_trail 0 1 10 37
/**
 * See class header comments.
 */
86
void CMotionTrail::update_motion_trail(PN_stdfloat current_time, LMatrix4 *transform);

21 14 get_class_type 0 4 22 28 CMotionTrail::get_class_type 0 1 11 0
53
static TypeHandle CMotionTrail::get_class_type(void);

11
1 0 0 7 3 24 0 0 22 /**
 * Constructor
 */ 0 
2 0 0 15 3 24 0 0 0 1 6 param0 0 25  
3 0 0 4 4 27 0 0 42 /**
 * Reset the frame sample history.
 */ 1 4 this 3 24  
4 0 0 4 5 27 0 0 33 /**
 * Reset the vertex list.
 */ 1 4 this 3 24  
5 0 0 4 6 27 0 0 43 /**
 * Enable/disable the motion trail.
 */ 2 4 this 3 24  6 enable 1 28  
6 0 0 4 7 27 0 0 28 /**
 * Set the GeomNode.
 */ 2 4 this 3 24  9 geom_node 1 29  
7 0 0 4 8 27 0 0 24 /**
 * Add a vertex.
 */ 5 4 this 3 24  6 vertex 1 31  11 start_color 1 31  9 end_color 1 31  1 v 1 34  
8 0 0 4 9 27 0 0 622 /**
 * Set motion trail parameters.
 *
 * sampling_time = Can be used to specify a lower sampling rate than the frame
 * rate.  Use 0.0 with nurbs.
 *
 * time_window = a component for the "length" of the motion trail.  The motion
 * trail length = time_window * velocity of the object.
 *
 * use_texture = texture option on/off.
 *
 * calculate_relative_matrix = calculate relative matrix on/off.
 *
 * use_nurbs = nurbs option on/off
 *
 * resolution_distance = the distance used to determine the number of geometry
 * samples.  samples = motion trail length / resolution_distance.  Applicable
 * only if nurbs is on.
 */ 7 4 this 3 24  13 sampling_time 1 34  11 time_window 1 34  11 use_texture 1 28  25 calculate_relative_matrix 1 28  9 use_nurbs 1 28  19 resolution_distance 1 34  
9 0 0 6 10 36 0 0 46 /**
 * Check if a sample can be submitted.
 */ 2 4 this 3 24  12 current_time 1 34  
10 0 0 4 11 27 0 0 37 /**
 * See class header comments.
 */ 3 4 this 3 24  12 current_time 1 34  9 transform 1 37  
11 0 0 7 12 41 0 0 0 0 
20
22 12 CMotionTrail 0 75777 12 CMotionTrail 12 CMotionTrail 0 0 0 1 12 0 0 9 13 14 15 16 17 18 19 20 21 0 0 1 0 23 0 0 0 0 1527
/**
 * The method used in creating the motion trail is based on taking samples of
 * time and transformations (the position and orientation matrix) in real-
 * time.  The method also requires a number of vertices (positions) that
 * determines "shape" of the motion trail (i.e.  the edge of a blade).  A
 * start color and end color is also required for each vertex.  The color is
 * interpolated as function of time.  The colors are typically used to fade
 * the motion trail so the end color is typically black.
 *
 * The vertices are submitted via the "add_vertex" function.  For each frame,
 * a sample is submited via the "update_motion_trail" function.  During the
 * "update_motion_trail" function, the motion trail geometry is created
 * dynamically from the sample history and the vertices.
 *
 * The user must specifiy a GeomNode via "set_geom_node".
 *
 * The duration of the sample history is specified by a time window.  A larger
 * time window creates longer motion trails (given constant speed).  Samples
 * that are no longer within the time window are automatically discarded.
 *
 * The nurbs option can be used to create smooth interpolated curves from the
 * samples.  The nurbs option is useful for animations that lack sampling to
 * begin with, animations that move very quickly, or low frame rates.
 *
 * The texture option be used to create variation to the motion trail.  The u
 * coordinate of the texture corresponds to time and the v coordinate
 * corresponds to the "shape" of the motion trail.
 */

23 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

24 14 CMotionTrail * 0 8576 14 CMotionTrail * 14 CMotionTrail * 0 0 22 0 0 0 0 0 0 0 0 0 0

25 20 CMotionTrail const * 0 8576 20 CMotionTrail const * 20 CMotionTrail const * 0 0 26 0 0 0 0 0 0 0 0 0 0

26 18 CMotionTrail const 0 8832 18 CMotionTrail const 18 CMotionTrail const 0 0 22 0 0 0 0 0 0 0 0 0 0

27 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

28 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

29 10 GeomNode * 0 8576 10 GeomNode * 10 GeomNode * 0 0 30 0 0 0 0 0 0 0 0 0 0

30 8 GeomNode 0 2048 8 GeomNode 8 GeomNode 0 0 0 0 0 0 0 0 0 0 0 0 208
/**
 * A node that holds Geom objects, renderable pieces of geometry.  This is the
 * primary kind of leaf node in the scene graph; almost all visible objects
 * will be contained in a GeomNode somewhere.
 */

31 10 LVector4 * 0 8576 10 LVector4 * 10 LVector4 * 0 0 32 0 0 0 0 0 0 0 0 0 0

32 8 LVector4 0 2105344 8 LVector4 8 LVector4 0 0 33 0 0 0 0 0 0 0 0 0 0

33 9 LVector4f 0 2048 9 LVector4f 9 LVector4f 0 0 0 0 0 0 0 0 0 0 0 0 52
/**
 * This is a four-component vector distance.
 */

34 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 35 0 0 0 0 0 0 0 0 0 0

35 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

36 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

37 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 38 0 0 0 0 0 0 0 0 0 0

38 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 39 0 0 0 0 0 0 0 0 0 0

39 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

40 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

41 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 40 0 0 0 0 0 0 0 0 0 0

0
0
0
