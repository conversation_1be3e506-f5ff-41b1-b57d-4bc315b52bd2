1730992849
3 3
15 libp3downloader 4 _Rn_ 12 panda3d.core 
393
399 9 ~SSReader 0 518 792 19 SSReader::~SSReader 0 0 10
/**
 *
 */
34
virtual SSReader::~<PERSON>ead<PERSON>(void);

400 16 receive_datagram 0 4 792 26 SSReader::receive_datagram 0 1 1 253
/**
 * Receives a datagram over the socket by expecting a little-endian 16-bit
 * byte count as a prefix.  If the socket stream is non-blocking, may return
 * false if the data is not available; otherwise, returns false only if the
 * socket closes.
 */
53
inline bool SSReader::receive_datagram(Datagram &dg);

401 9 is_closed 0 6 792 19 SSReader::is_closed 0 1 2 0
43
virtual bool SSReader::is_closed(void) = 0;

402 5 close 0 6 792 15 SSReader::close 0 1 3 0
39
virtual void SSReader::close(void) = 0;

403 19 set_tcp_header_size 0 4 792 29 SSReader::set_tcp_header_size 0 1 4 269
/**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */
63
inline void SSReader::set_tcp_header_size(int tcp_header_size);

404 19 get_tcp_header_size 0 4 792 29 SSReader::get_tcp_header_size 0 1 5 77
/**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */
53
inline int SSReader::get_tcp_header_size(void) const;

405 9 ~SSWriter 0 518 793 19 SSWriter::~SSWriter 0 0 10
/**
 *
 */
34
virtual SSWriter::~SSWriter(void);

406 13 send_datagram 0 4 793 23 SSWriter::send_datagram 0 1 6 234
/**
 * Transmits the indicated datagram over the socket by prepending it with a
 * little-endian 16-bit byte count.  Does not return until the data is sent or
 * the connection is closed, even if the socket stream is non-blocking.
 */
49
bool SSWriter::send_datagram(Datagram const &dg);

407 9 is_closed 0 6 793 19 SSWriter::is_closed 0 1 7 0
43
virtual bool SSWriter::is_closed(void) = 0;

408 5 close 0 6 793 15 SSWriter::close 0 1 8 0
39
virtual void SSWriter::close(void) = 0;

409 15 set_collect_tcp 0 4 793 25 SSWriter::set_collect_tcp 0 1 9 719
/**
 * Enables or disables "collect-tcp" mode.  In this mode, individual TCP
 * packets are not sent immediately, but rather they are collected together
 * and accumulated to be sent periodically as one larger TCP packet.  This
 * cuts down on overhead from the TCP/IP protocol, especially if many small
 * packets need to be sent on the same connection, but it introduces
 * additional latency (since packets must be held before they can be sent).
 *
 * See set_collect_tcp_interval() to specify the interval of time for which to
 * hold packets before sending them.
 *
 * If you enable this mode, you may also need to periodically call
 * consider_flush() to flush the queue if no packets have been sent recently.
 */
56
inline void SSWriter::set_collect_tcp(bool collect_tcp);

410 15 get_collect_tcp 0 4 793 25 SSWriter::get_collect_tcp 0 1 10 85
/**
 * Returns the current setting of "collect-tcp" mode.  See set_collect_tcp().
 */
50
inline bool SSWriter::get_collect_tcp(void) const;

411 24 set_collect_tcp_interval 0 4 793 34 SSWriter::set_collect_tcp_interval 0 1 11 231
/**
 * Specifies the interval in time, in seconds, for which to hold TCP packets
 * before sending all of the recently received packets at once.  This only has
 * meaning if "collect-tcp" mode is enabled; see set_collect_tcp().
 */
64
inline void SSWriter::set_collect_tcp_interval(double interval);

412 24 get_collect_tcp_interval 0 4 793 34 SSWriter::get_collect_tcp_interval 0 1 12 229
/**
 * Returns the interval in time, in seconds, for which to hold TCP packets
 * before sending all of the recently received packets at once.  This only has
 * meaning if "collect-tcp" mode is enabled; see set_collect_tcp().
 */
61
inline double SSWriter::get_collect_tcp_interval(void) const;

413 19 set_tcp_header_size 0 4 793 29 SSWriter::set_tcp_header_size 0 1 13 269
/**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */
63
inline void SSWriter::set_tcp_header_size(int tcp_header_size);

414 19 get_tcp_header_size 0 4 793 29 SSWriter::get_tcp_header_size 0 1 14 77
/**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */
53
inline int SSWriter::get_tcp_header_size(void) const;

415 14 consider_flush 0 4 793 24 SSWriter::consider_flush 0 1 15 143
/**
 * Sends the most recently queued data if enough time has elapsed.  This only
 * has meaning if set_collect_tcp() has been set to true.
 */
43
inline bool SSWriter::consider_flush(void);

416 5 flush 0 4 793 15 SSWriter::flush 0 1 16 120
/**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */
34
inline bool SSWriter::flush(void);

417 17 upcast_to_istream 0 12 794 32 ISocketStream::upcast_to_istream 0 1 20 36
upcast from ISocketStream to istream
53
std::istream *ISocketStream::upcast_to_istream(void);

418 25 downcast_to_ISocketStream 0 12 795 39 std::istream::downcast_to_ISocketStream 0 0 38
downcast from istream to ISocketStream
61
ISocketStream *std::istream::downcast_to_ISocketStream(void);

419 18 upcast_to_SSReader 0 12 794 33 ISocketStream::upcast_to_SSReader 0 1 21 37
upcast from ISocketStream to SSReader
50
SSReader *ISocketStream::upcast_to_SSReader(void);

420 25 downcast_to_ISocketStream 0 12 792 35 SSReader::downcast_to_ISocketStream 0 0 39
downcast from SSReader to ISocketStream
57
ISocketStream *SSReader::downcast_to_ISocketStream(void);

421 14 ~ISocketStream 0 518 794 29 ISocketStream::~ISocketStream 0 0 10
/**
 *
 */
44
virtual ISocketStream::~ISocketStream(void);

422 9 is_closed 0 6 794 24 ISocketStream::is_closed 0 1 17 0
48
virtual bool ISocketStream::is_closed(void) = 0;

423 5 close 0 6 794 20 ISocketStream::close 0 1 18 0
44
virtual void ISocketStream::close(void) = 0;

424 14 get_read_state 0 6 794 29 ISocketStream::get_read_state 0 1 19 0
73
virtual ISocketStream::ReadState ISocketStream::get_read_state(void) = 0;

425 17 upcast_to_ostream 0 12 797 32 OSocketStream::upcast_to_ostream 0 1 25 36
upcast from OSocketStream to ostream
53
std::ostream *OSocketStream::upcast_to_ostream(void);

426 25 downcast_to_OSocketStream 0 12 798 39 std::ostream::downcast_to_OSocketStream 0 0 38
downcast from ostream to OSocketStream
61
OSocketStream *std::ostream::downcast_to_OSocketStream(void);

427 18 upcast_to_SSWriter 0 12 797 33 OSocketStream::upcast_to_SSWriter 0 1 26 37
upcast from OSocketStream to SSWriter
50
SSWriter *OSocketStream::upcast_to_SSWriter(void);

428 25 downcast_to_OSocketStream 0 12 793 35 SSWriter::downcast_to_OSocketStream 0 0 39
downcast from SSWriter to OSocketStream
57
OSocketStream *SSWriter::downcast_to_OSocketStream(void);

429 9 is_closed 0 6 797 24 OSocketStream::is_closed 0 1 22 0
48
virtual bool OSocketStream::is_closed(void) = 0;

430 5 close 0 6 797 20 OSocketStream::close 0 1 23 0
44
virtual void OSocketStream::close(void) = 0;

431 5 flush 0 4 797 20 OSocketStream::flush 0 1 24 120
/**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */
39
inline bool OSocketStream::flush(void);

432 14 ~OSocketStream 0 516 797 29 OSocketStream::~OSocketStream 0 0 0
36
OSocketStream::~OSocketStream(void);

433 18 upcast_to_iostream 0 12 799 32 SocketStream::upcast_to_iostream 0 1 32 36
upcast from SocketStream to iostream
54
std::iostream *SocketStream::upcast_to_iostream(void);

434 24 downcast_to_SocketStream 0 12 800 39 std::iostream::downcast_to_SocketStream 0 0 38
downcast from iostream to SocketStream
60
SocketStream *std::iostream::downcast_to_SocketStream(void);

435 18 upcast_to_SSReader 0 12 799 32 SocketStream::upcast_to_SSReader 0 1 33 36
upcast from SocketStream to SSReader
49
SSReader *SocketStream::upcast_to_SSReader(void);

436 24 downcast_to_SocketStream 0 12 792 34 SSReader::downcast_to_SocketStream 0 0 38
downcast from SSReader to SocketStream
55
SocketStream *SSReader::downcast_to_SocketStream(void);

437 18 upcast_to_SSWriter 0 12 799 32 SocketStream::upcast_to_SSWriter 0 1 34 36
upcast from SocketStream to SSWriter
49
SSWriter *SocketStream::upcast_to_SSWriter(void);

438 24 downcast_to_SocketStream 0 12 793 34 SSWriter::downcast_to_SocketStream 0 0 38
downcast from SSWriter to SocketStream
55
SocketStream *SSWriter::downcast_to_SocketStream(void);

439 9 is_closed 0 6 799 23 SocketStream::is_closed 0 1 27 0
47
virtual bool SocketStream::is_closed(void) = 0;

440 5 close 0 6 799 19 SocketStream::close 0 1 28 0
43
virtual void SocketStream::close(void) = 0;

441 19 set_tcp_header_size 0 4 799 33 SocketStream::set_tcp_header_size 0 1 29 269
/**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */
67
inline void SocketStream::set_tcp_header_size(int tcp_header_size);

442 19 get_tcp_header_size 0 4 799 33 SocketStream::get_tcp_header_size 0 1 30 77
/**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */
57
inline int SocketStream::get_tcp_header_size(void) const;

443 5 flush 0 4 799 19 SocketStream::flush 0 1 31 120
/**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */
38
inline bool SocketStream::flush(void);

444 13 ~SocketStream 0 516 799 27 SocketStream::~SocketStream 0 0 0
34
SocketStream::~SocketStream(void);

445 7 URLSpec 0 260 801 16 URLSpec::URLSpec 0 4 35 36 37 38 159
/**
 *
 */

/**
 *
 */

/**
 * Creates a URLSpec by appending a path to the end of the old URLSpec,
 * inserting an intervening forward slash if necessary.
 */
219
URLSpec::URLSpec(void);
inline URLSpec::URLSpec(std::string const &url, bool server_name_expected = false);
URLSpec::URLSpec(URLSpec const &url, Filename const &path);
inline URLSpec::URLSpec(URLSpec const &) = default;

446 10 operator = 0 4 801 19 URLSpec::operator = 0 1 39 0
56
inline void URLSpec::operator =(std::string const &url);

447 11 operator == 0 4 801 20 URLSpec::operator == 0 1 40 0
61
inline bool URLSpec::operator ==(URLSpec const &other) const;

448 11 operator != 0 4 801 20 URLSpec::operator != 0 1 41 0
61
inline bool URLSpec::operator !=(URLSpec const &other) const;

449 10 operator < 0 4 801 19 URLSpec::operator < 0 1 42 0
60
inline bool URLSpec::operator <(URLSpec const &other) const;

450 10 compare_to 0 4 801 19 URLSpec::compare_to 0 1 43 158
/**
 * Returns a number less than zero if this URLSpec sorts before the other one,
 * greater than zero if it sorts after, or zero if they are equivalent.
 */
52
int URLSpec::compare_to(URLSpec const &other) const;

451 8 get_hash 0 4 801 17 URLSpec::get_hash 0 1 44 10
/**
 *
 */
42
std::size_t URLSpec::get_hash(void) const;

452 10 has_scheme 0 4 801 19 URLSpec::has_scheme 0 1 45 90
/**
 * Returns true if the URL specifies a scheme (e.g.  "http:"), false
 * otherwise.
 */
44
inline bool URLSpec::has_scheme(void) const;

453 13 has_authority 0 4 801 22 URLSpec::has_authority 0 1 46 124
/**
 * Returns true if the URL specifies an authority (this includes username,
 * server, and/or port), false otherwise.
 */
47
inline bool URLSpec::has_authority(void) const;

454 12 has_username 0 4 801 21 URLSpec::has_username 0 1 47 94
/**
 * Returns true if the URL specifies a username (and/or password), false
 * otherwise.
 */
46
inline bool URLSpec::has_username(void) const;

455 10 has_server 0 4 801 19 URLSpec::has_server 0 1 48 76
/**
 * Returns true if the URL specifies a server name, false otherwise.
 */
44
inline bool URLSpec::has_server(void) const;

456 8 has_port 0 4 801 17 URLSpec::has_port 0 1 49 76
/**
 * Returns true if the URL specifies a port number, false otherwise.
 */
42
inline bool URLSpec::has_port(void) const;

457 8 has_path 0 4 801 17 URLSpec::has_path 0 1 50 146
/**
 * Returns true if the URL includes a path specification (that is, the
 * particular filename on the server to retrieve), false otherwise.
 */
42
inline bool URLSpec::has_path(void) const;

458 9 has_query 0 4 801 18 URLSpec::has_query 0 1 51 83
/**
 * Returns true if the URL includes a query specification, false otherwise.
 */
43
inline bool URLSpec::has_query(void) const;

459 10 get_scheme 0 4 801 19 URLSpec::get_scheme 0 1 52 97
/**
 * Returns the scheme specified by the URL, or empty string if no scheme is
 * specified.
 */
44
std::string URLSpec::get_scheme(void) const;

460 13 get_authority 0 4 801 22 URLSpec::get_authority 0 1 53 149
/**
 * Returns the authority specified by the URL (this includes username, server,
 * and/or port), or empty string if no authority is specified.
 */
54
inline std::string URLSpec::get_authority(void) const;

461 12 get_username 0 4 801 21 URLSpec::get_username 0 1 54 194
/**
 * Returns the username specified by the URL, if any.  This might also include
 * a password, e.g.  "username:password", although putting a password on the
 * URL is probably a bad idea.
 */
53
inline std::string URLSpec::get_username(void) const;

462 10 get_server 0 4 801 19 URLSpec::get_server 0 1 55 137
/**
 * Returns the server name specified by the URL, if any.  In case of an IPv6
 * address, does not include the enclosing brackets.
 */
51
inline std::string URLSpec::get_server(void) const;

463 12 get_port_str 0 4 801 21 URLSpec::get_port_str 0 1 56 204
/**
 * Returns the port specified by the URL as a string, or the empty string if
 * no port is specified.  Compare this with get_port(), which returns a
 * default port number if no port is specified.
 */
53
inline std::string URLSpec::get_port_str(void) const;

464 8 get_port 0 4 801 17 URLSpec::get_port 0 1 57 97
/**
 * Returns the port number specified by the URL, or the default port if not
 * specified.
 */
39
uint16_t URLSpec::get_port(void) const;

465 19 get_server_and_port 0 4 801 28 URLSpec::get_server_and_port 0 1 58 298
/**
 * Returns a string consisting of the server name, followed by a colon,
 * followed by the port number.  If the port number is not explicitly given in
 * the URL, this string will include the implicit port number.
 * If the server is an IPv6 address, it will be enclosed in square brackets.
 */
53
std::string URLSpec::get_server_and_port(void) const;

466 15 is_default_port 0 4 801 24 URLSpec::is_default_port 0 1 59 181
/**
 * Returns true if the port number encoded in this URL is the default port
 * number for the scheme (or if there is no port number), or false if it is a
 * nonstandard port.
 */
42
bool URLSpec::is_default_port(void) const;

467 27 get_default_port_for_scheme 0 4 801 36 URLSpec::get_default_port_for_scheme 0 1 60 106
/**
 * Returns the default port number for the indicated scheme, or 0 if there is
 * no known default.
 */
75
static int URLSpec::get_default_port_for_scheme(std::string const &scheme);

468 8 get_path 0 4 801 17 URLSpec::get_path 0 1 61 81
/**
 * Returns the path specified by the URL, or "/" if no path is specified.
 */
42
std::string URLSpec::get_path(void) const;

469 9 get_query 0 4 801 18 URLSpec::get_query 0 1 62 95
/**
 * Returns the query specified by the URL, or empty string if no query is
 * specified.
 */
50
inline std::string URLSpec::get_query(void) const;

470 18 get_path_and_query 0 4 801 27 URLSpec::get_path_and_query 0 1 63 106
/**
 * Returns the path (or "/" if no path is specified), followed by the query if
 * it is specified.
 */
52
std::string URLSpec::get_path_and_query(void) const;

471 6 is_ssl 0 4 801 15 URLSpec::is_ssl 0 1 64 115
/**
 * Returns true if the URL's scheme specifies an SSL-secured protocol such as
 * https, or false otherwise.
 */
40
inline bool URLSpec::is_ssl(void) const;

472 7 get_url 0 4 801 16 URLSpec::get_url 0 1 65 50
/**
 * Returns the complete URL specification.
 */
55
inline std::string const &URLSpec::get_url(void) const;

473 10 set_scheme 0 4 801 19 URLSpec::set_scheme 0 1 66 61
/**
 * Replaces the scheme part of the URL specification.
 */
52
void URLSpec::set_scheme(std::string const &scheme);

474 13 set_authority 0 4 801 22 URLSpec::set_authority 0 1 67 114
/**
 * Replaces the authority part of the URL specification.  This includes the
 * username, server, and port.
 */
58
void URLSpec::set_authority(std::string const &authority);

475 12 set_username 0 4 801 21 URLSpec::set_username 0 1 68 63
/**
 * Replaces the username part of the URL specification.
 */
56
void URLSpec::set_username(std::string const &username);

476 10 set_server 0 4 801 19 URLSpec::set_server 0 1 69 174
/**
 * Replaces the server part of the URL specification.
 * Unlike set_server_and_port, this method does not require IPv6 addresses to
 * be enclosed in square brackets.
 */
52
void URLSpec::set_server(std::string const &server);

477 8 set_port 0 4 801 17 URLSpec::set_port 0 2 70 71 152
/**
 * Replaces the port part of the URL specification.
 */

/**
 * Replaces the port part of the URL specification, given a numeric port
 * number.
 */
87
void URLSpec::set_port(std::string const &port);
void URLSpec::set_port(uint16_t port);

478 19 set_server_and_port 0 4 801 28 URLSpec::set_server_and_port 0 1 72 254
/**
 * Replaces the server and port parts of the URL specification simultaneously.
 * The input string should be of the form "server:port", or just "server" to
 * make the port number implicit.
 * Any IPv6 address must be enclosed in square brackets.
 */
70
void URLSpec::set_server_and_port(std::string const &server_and_port);

479 8 set_path 0 4 801 17 URLSpec::set_path 0 1 73 59
/**
 * Replaces the path part of the URL specification.
 */
48
void URLSpec::set_path(std::string const &path);

480 9 set_query 0 4 801 18 URLSpec::set_query 0 1 74 60
/**
 * Replaces the query part of the URL specification.
 */
50
void URLSpec::set_query(std::string const &query);

481 7 set_url 0 4 801 16 URLSpec::set_url 0 1 75 191
/**
 * Completely replaces the URL with the indicated string.  If
 * server_name_expected is true, it is a hint that an undecorated URL is
 * probably a server name, not a local filename.
 */
81
void URLSpec::set_url(std::string const &url, bool server_name_expected = false);

482 17 operator typecast 0 132 801 26 URLSpec::operator typecast 0 1 87 0
48
inline operator std::string const &(void) const;

483 5 c_str 0 4 801 14 URLSpec::c_str 0 1 76 10
/**
 *
 */
46
inline char const *URLSpec::c_str(void) const;

484 5 empty 0 4 801 14 URLSpec::empty 0 1 77 98
/**
 * Returns false if the URLSpec is valid (not empty), or true if it is an
 * empty string.
 */
39
inline bool URLSpec::empty(void) const;

485 22 operator typecast bool 0 132 801 31 URLSpec::operator typecast bool 0 1 88 0
34
inline operator bool (void) const;

486 6 length 0 4 801 15 URLSpec::length 0 1 78 10
/**
 *
 */
47
inline std::size_t URLSpec::length(void) const;

487 4 size 0 4 801 13 URLSpec::size 0 1 79 10
/**
 *
 */
45
inline std::size_t URLSpec::size(void) const;

488 11 operator [] 0 4 801 20 URLSpec::operator [] 0 1 80 0
54
inline char URLSpec::operator [](std::size_t n) const;

489 5 input 0 4 801 14 URLSpec::input 0 1 81 10
/**
 *
 */
38
bool URLSpec::input(std::istream &in);

490 6 output 0 4 801 15 URLSpec::output 0 1 82 10
/**
 *
 */
46
void URLSpec::output(std::ostream &out) const;

491 5 quote 0 4 801 14 URLSpec::quote 0 1 83 313
/**
 * Returns the source string with all "unsafe" characters quoted, making a
 * string suitable for placing in a URL.  Letters, digits, and the underscore,
 * comma, period, and hyphen characters, as well as any included in the safe
 * string, are left alone; all others are converted to hex representation.
 */
92
static std::string URLSpec::quote(std::string const &source, std::string const &safe = "/");

492 10 quote_plus 0 4 801 19 URLSpec::quote_plus 0 1 84 100
/**
 * Behaves like quote() with the additional behavior of replacing spaces with
 * plus signs.
 */
97
static std::string URLSpec::quote_plus(std::string const &source, std::string const &safe = "/");

493 7 unquote 0 4 801 16 URLSpec::unquote 0 1 85 121
/**
 * Reverses the operation of quote(): converts escaped characters of the form
 * "%xx" to their ascii equivalent.
 */
63
static std::string URLSpec::unquote(std::string const &source);

494 12 unquote_plus 0 4 801 21 URLSpec::unquote_plus 0 1 86 169
/**
 * Reverses the operation of quote_plus(): converts escaped characters of the
 * form "%xx" to their ascii equivalent, and also converts plus signs to
 * spaces.
 */
68
static std::string URLSpec::unquote_plus(std::string const &source);

495 8 ~URLSpec 0 516 801 17 URLSpec::~URLSpec 0 0 0
24
URLSpec::~URLSpec(void);

496 8 HTTPEnum 0 260 807 18 HTTPEnum::HTTPEnum 0 2 89 90 0
97
inline HTTPEnum::HTTPEnum(void) = default;
inline HTTPEnum::HTTPEnum(HTTPEnum const &) = default;

497 9 ~HTTPEnum 0 516 807 19 HTTPEnum::~HTTPEnum 0 0 0
26
HTTPEnum::~HTTPEnum(void);

498 8 HTTPDate 0 260 810 18 HTTPDate::HTTPDate 0 4 91 92 93 94 158
/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 * Decodes the string into a sensible date.  Returns 0 (!is_valid()) if the
 * string cannot be correctly decoded.
 */
168
inline HTTPDate::HTTPDate(void);
inline HTTPDate::HTTPDate(time_t time);
HTTPDate::HTTPDate(std::string const &format);
inline HTTPDate::HTTPDate(HTTPDate const &copy);

499 10 operator = 0 4 810 20 HTTPDate::operator = 0 1 95 0
55
inline void HTTPDate::operator =(HTTPDate const &copy);

500 3 now 0 4 810 13 HTTPDate::now 0 1 96 73
/**
 * Returns an HTTPDate that represents the current time and date.
 */
43
static inline HTTPDate HTTPDate::now(void);

501 8 is_valid 0 4 810 18 HTTPDate::is_valid 0 1 97 141
/**
 * Returns true if the date is meaningful, or false if it is -1 (which
 * generally indicates the source string could not be parsed.)
 */
43
inline bool HTTPDate::is_valid(void) const;

502 10 get_string 0 4 810 20 HTTPDate::get_string 0 1 98 10
/**
 *
 */
45
std::string HTTPDate::get_string(void) const;

503 8 get_time 0 4 810 18 HTTPDate::get_time 0 1 99 48
/**
 * Returns the date as a C time_t value.
 */
45
inline time_t HTTPDate::get_time(void) const;

504 11 operator == 0 4 810 21 HTTPDate::operator == 0 1 100 0
63
inline bool HTTPDate::operator ==(HTTPDate const &other) const;

505 11 operator != 0 4 810 21 HTTPDate::operator != 0 1 101 0
63
inline bool HTTPDate::operator !=(HTTPDate const &other) const;

506 10 operator < 0 4 810 20 HTTPDate::operator < 0 1 102 0
62
inline bool HTTPDate::operator <(HTTPDate const &other) const;

507 10 operator > 0 4 810 20 HTTPDate::operator > 0 1 103 0
62
inline bool HTTPDate::operator >(HTTPDate const &other) const;

508 10 compare_to 0 4 810 20 HTTPDate::compare_to 0 1 104 159
/**
 * Returns a number less than zero if this HTTPDate sorts before the other
 * one, greater than zero if it sorts after, or zero if they are equivalent.
 */
61
inline int HTTPDate::compare_to(HTTPDate const &other) const;

509 11 operator += 0 4 810 21 HTTPDate::operator += 0 1 105 0
47
inline void HTTPDate::operator +=(int seconds);

510 11 operator -= 0 4 810 21 HTTPDate::operator -= 0 1 106 0
47
inline void HTTPDate::operator -=(int seconds);

511 10 operator + 0 4 810 20 HTTPDate::operator + 0 1 107 0
56
inline HTTPDate HTTPDate::operator +(int seconds) const;

512 10 operator - 0 4 810 20 HTTPDate::operator - 0 2 108 109 0
118
inline HTTPDate HTTPDate::operator -(int seconds) const;
inline int HTTPDate::operator -(HTTPDate const &other) const;

513 5 input 0 4 810 15 HTTPDate::input 0 1 110 10
/**
 *
 */
39
bool HTTPDate::input(std::istream &in);

514 6 output 0 4 810 16 HTTPDate::output 0 1 111 10
/**
 *
 */
47
void HTTPDate::output(std::ostream &out) const;

515 9 ~HTTPDate 0 516 810 19 HTTPDate::~HTTPDate 0 0 0
26
HTTPDate::~HTTPDate(void);

516 10 HTTPCookie 0 260 811 22 HTTPCookie::HTTPCookie 0 4 112 113 114 115 418
/**
 * Constructs an empty cookie.
 */

/**
 * Constructs a cookie according to the indicated string, presumably the tag
 * of a Set-Cookie header.  There is no way to detect a formatting error in
 * the string with this constructor.
 */

/**
 * Constructs a cookie with the indicated name, path, and domain values, but
 * no other data.  This is most useful for looking up an existing cookie in
 * the HTTPClient.
 */
301
inline HTTPCookie::HTTPCookie(void);
inline explicit HTTPCookie::HTTPCookie(std::string const &format, URLSpec const &url);
inline explicit HTTPCookie::HTTPCookie(std::string const &name, std::string const &path, std::string const &domain);
inline HTTPCookie::HTTPCookie(HTTPCookie const &) = default;

517 11 ~HTTPCookie 0 516 811 23 HTTPCookie::~HTTPCookie 0 0 10
/**
 *
 */
37
inline HTTPCookie::~HTTPCookie(void);

518 8 set_name 0 4 811 20 HTTPCookie::set_name 0 1 116 10
/**
 *
 */
58
inline void HTTPCookie::set_name(std::string const &name);

519 8 get_name 0 4 811 20 HTTPCookie::get_name 0 1 117 93
/**
 * Returns the name of the cookie.  This is the key value specified by the
 * server.
 */
59
inline std::string const &HTTPCookie::get_name(void) const;

520 9 set_value 0 4 811 21 HTTPCookie::set_value 0 1 118 10
/**
 *
 */
60
inline void HTTPCookie::set_value(std::string const &value);

521 9 get_value 0 4 811 21 HTTPCookie::get_value 0 1 119 139
/**
 * Returns the value of the cookie.  This is the arbitrary string associated
 * with the cookie's name, as specified by the server.
 */
60
inline std::string const &HTTPCookie::get_value(void) const;

522 10 set_domain 0 4 811 22 HTTPCookie::set_domain 0 1 120 10
/**
 *
 */
62
inline void HTTPCookie::set_domain(std::string const &domain);

523 10 get_domain 0 4 811 22 HTTPCookie::get_domain 0 1 121 10
/**
 *
 */
61
inline std::string const &HTTPCookie::get_domain(void) const;

524 8 set_path 0 4 811 20 HTTPCookie::set_path 0 1 122 10
/**
 *
 */
58
inline void HTTPCookie::set_path(std::string const &path);

525 8 get_path 0 4 811 20 HTTPCookie::get_path 0 1 123 99
/**
 * Returns the prefix of the URL paths on the server for which this cookie
 * will be sent.
 */
59
inline std::string const &HTTPCookie::get_path(void) const;

526 11 set_expires 0 4 811 23 HTTPCookie::set_expires 0 1 124 10
/**
 *
 */
61
inline void HTTPCookie::set_expires(HTTPDate const &expires);

527 13 clear_expires 0 4 811 25 HTTPCookie::clear_expires 0 1 125 53
/**
 * Removes the expiration date on the cookie.
 */
44
inline void HTTPCookie::clear_expires(void);

528 11 has_expires 0 4 811 23 HTTPCookie::has_expires 0 1 126 78
/**
 * Returns true if the cookie has an expiration date, false otherwise.
 */
48
inline bool HTTPCookie::has_expires(void) const;

529 11 get_expires 0 4 811 23 HTTPCookie::get_expires 0 1 127 102
/**
 * Returns the expiration date of the cookie if it is set, or an invalid date
 * if it is not.
 */
52
inline HTTPDate HTTPCookie::get_expires(void) const;

530 10 set_secure 0 4 811 22 HTTPCookie::set_secure 0 1 128 10
/**
 *
 */
46
inline void HTTPCookie::set_secure(bool flag);

531 10 get_secure 0 4 811 22 HTTPCookie::get_secure 0 1 129 129
/**
 * Returns true if the server has indicated this is a "secure" cookie which
 * should only be sent over an HTTPS channel.
 */
47
inline bool HTTPCookie::get_secure(void) const;

532 10 operator < 0 4 811 22 HTTPCookie::operator < 0 1 130 0
59
bool HTTPCookie::operator <(HTTPCookie const &other) const;

533 11 update_from 0 4 811 23 HTTPCookie::update_from 0 1 131 358
/**
 * Assuming the operator < method, above, has already evaluated these two
 * cookies as equal, then assign the remaining values (value, expiration date,
 * secure flag) from the indicated cookie.  This is guaranteed not to change
 * the ordering of the cookie in a set, and so can be used to update an
 * existing cookie within a set with new values.
 */
54
void HTTPCookie::update_from(HTTPCookie const &other);

534 16 parse_set_cookie 0 4 811 28 HTTPCookie::parse_set_cookie 0 1 132 218
/**
 * Separates out the parameter/value pairs of the Set-Cookie header and
 * assigns the values of the cookie appropriate.  Returns true if the header
 * is parsed correctly, false if something is not understood.
 */
81
bool HTTPCookie::parse_set_cookie(std::string const &format, URLSpec const &url);

535 10 is_expired 0 4 811 22 HTTPCookie::is_expired 0 1 133 105
/**
 * Returns true if the cookie's expiration date is before the indicated date,
 * false otherwise.
 */
80
inline bool HTTPCookie::is_expired(HTTPDate const &now = HTTPDate::now()) const;

536 11 matches_url 0 4 811 23 HTTPCookie::matches_url 0 1 134 112
/**
 * Returns true if the cookie is appropriate to send with the indicated URL
 * request, false otherwise.
 */
55
bool HTTPCookie::matches_url(URLSpec const &url) const;

537 6 output 0 4 811 18 HTTPCookie::output 0 1 135 10
/**
 *
 */
49
void HTTPCookie::output(std::ostream &out) const;

538 10 HTTPClient 0 260 813 22 HTTPClient::HTTPClient 0 2 136 137 22
/**
 *
 */

/**
 *
 */
77
HTTPClient::HTTPClient(void);
HTTPClient::HTTPClient(HTTPClient const &copy);

539 10 operator = 0 4 813 22 HTTPClient::operator = 0 1 138 0
52
void HTTPClient::operator =(HTTPClient const &copy);

540 16 init_random_seed 0 4 813 28 HTTPClient::init_random_seed 0 1 139 561
/**
 * This may be called once, presumably at the beginning of an application, to
 * initialize OpenSSL's random seed.  On Windows, it is particularly important
 * to call this at startup if you are going to be performing any https
 * operations or otherwise use encryption, since the Windows algorithm for
 * getting a random seed takes 2-3 seconds at startup, but can take 30 seconds
 * or more after you have opened a 3-D graphics window and started rendering.
 *
 * There is no harm in calling this method multiple times, or in not calling
 * it at all.
 */
47
static void HTTPClient::init_random_seed(void);

541 14 set_proxy_spec 0 4 813 26 HTTPClient::set_proxy_spec 0 1 140 511
/**
 * Specifies the complete set of proxies to use for all schemes.  This is
 * either a semicolon-delimited set of hostname:ports, or a semicolon-
 * delimited set of pairs of the form "scheme=hostname:port", or a
 * combination.  Use the keyword DIRECT, or an empty string, to represent a
 * direct connection.  A particular scheme and/or proxy host may be listed
 * more than once.  This is a convenience function that can be used in place
 * of explicit calls to add_proxy() for each scheme/proxy pair.
 */
63
void HTTPClient::set_proxy_spec(std::string const &proxy_spec);

542 14 get_proxy_spec 0 4 813 26 HTTPClient::get_proxy_spec 0 1 141 371
/**
 * Returns the complete set of proxies to use for all schemes.  This is a
 * string of the form specified by set_proxy_spec(), above.  Note that the
 * string returned by this function may not be exactly the same as the string
 * passed into set_proxy_spec(), since the string is regenerated from the
 * internal storage structures and may therefore be reordered.
 */
51
std::string HTTPClient::get_proxy_spec(void) const;

543 20 set_direct_host_spec 0 4 813 32 HTTPClient::set_direct_host_spec 0 1 142 197
/**
 * Specifies the set of hosts that should be connected to directly, without
 * using a proxy.  This is a semicolon-separated list of hostnames that may
 * contain wildcard characters ("*").
 */
75
void HTTPClient::set_direct_host_spec(std::string const &direct_host_spec);

544 20 get_direct_host_spec 0 4 813 32 HTTPClient::get_direct_host_spec 0 1 143 189
/**
 * Returns the set of hosts that should be connected to directly, without
 * using a proxy, as a semicolon-separated list of hostnames that may contain
 * wildcard characters ("*").
 */
57
std::string HTTPClient::get_direct_host_spec(void) const;

545 18 set_try_all_direct 0 4 813 30 HTTPClient::set_try_all_direct 0 1 144 306
/**
 * If this is set true, then after a connection attempt through a proxy fails,
 * we always try a direct connection, regardless of whether the host is listed
 * on the direct_host_spec list.  If this is false, a direct attempt is not
 * made when we have a proxy in effect, even if the proxy fails.
 */
64
inline void HTTPClient::set_try_all_direct(bool try_all_direct);

546 18 get_try_all_direct 0 4 813 30 HTTPClient::get_try_all_direct 0 1 145 134
/**
 * Returns whether a failed connection through a proxy will be followed up by
 * a direct connection attempt, false otherwise.
 */
55
inline bool HTTPClient::get_try_all_direct(void) const;

547 11 clear_proxy 0 4 813 23 HTTPClient::clear_proxy 0 1 146 128
/**
 * Resets the proxy spec to empty.  Subsequent calls to add_proxy() may be
 * made to build up the set of proxy servers.
 */
35
void HTTPClient::clear_proxy(void);

548 9 add_proxy 0 4 813 21 HTTPClient::add_proxy 0 1 147 271
/**
 * Adds the indicated proxy host as a proxy for communications on the given
 * scheme.  Usually the scheme is "http" or "https".  It may be the empty
 * string to indicate a general proxy.  The proxy string may be the empty URL
 * to indicate a direct connection.
 */
76
void HTTPClient::add_proxy(std::string const &scheme, URLSpec const &proxy);

549 17 clear_direct_host 0 4 813 29 HTTPClient::clear_direct_host 0 1 148 178
/**
 * Resets the set of direct hosts to empty.  Subsequent calls to
 * add_direct_host() may be made to build up the list of hosts that do not
 * require a proxy connection.
 */
41
void HTTPClient::clear_direct_host(void);

550 15 add_direct_host 0 4 813 27 HTTPClient::add_direct_host 0 1 149 223
/**
 * Adds the indicated name to the set of hostnames that are connected to
 * directly, without using a proxy.  This name may be either a DNS name or an
 * IP address, and it may include the * as a wildcard character.
 */
62
void HTTPClient::add_direct_host(std::string const &hostname);

551 19 get_proxies_for_url 0 4 813 31 HTTPClient::get_proxies_for_url 0 1 150 621
/**
 * Fills up the indicated vector with the list of URLSpec objects, in the
 * order in which they should be tried, that are appropriate proxies to try
 * for the indicated URL.  The empty URL is returned for a direct connection.
 *
 * It is the user's responsibility to empty this vector before calling this
 * method; otherwise, the proxy URL's will simply be appended to the existing
 * list.
 */

/**
 * Returns a semicolon-delimited list of proxies, in the order in which they
 * should be tried, that are appropriate for the indicated URL.  The keyword
 * DIRECT indicates a direct connection should be tried.
 */
70
std::string HTTPClient::get_proxies_for_url(URLSpec const &url) const;

552 12 set_username 0 4 813 24 HTTPClient::set_username 0 1 151 433
/**
 * Specifies the username:password string corresponding to a particular server
 * and/or realm, when demanded by the server.  Either or both of the server or
 * realm may be empty; if so, they match anything.  Also, the server may be
 * set to the special string `"*proxy"`, which will match any proxy server.
 *
 * If the username is set to the empty string, this clears the password for
 * the particular server/realm pair.
 */
112
void HTTPClient::set_username(std::string const &server, std::string const &realm, std::string const &username);

553 12 get_username 0 4 813 24 HTTPClient::get_username 0 1 152 144
/**
 * Returns the username:password string set for this server/realm pair, or
 * empty string if nothing has been set.  See set_username().
 */
96
std::string HTTPClient::get_username(std::string const &server, std::string const &realm) const;

554 10 set_cookie 0 4 813 22 HTTPClient::set_cookie 0 1 153 116
/**
 * Stores the indicated cookie in the client's list of cookies, as if it had
 * been received from a server.
 */
54
void HTTPClient::set_cookie(HTTPCookie const &cookie);

555 12 clear_cookie 0 4 813 24 HTTPClient::clear_cookie 0 1 154 175
/**
 * Removes the cookie with the matching domain/path/name from the client's
 * list of cookies.  Returns true if it was removed, false if the cookie was
 * not matched.
 */
56
bool HTTPClient::clear_cookie(HTTPCookie const &cookie);

556 17 clear_all_cookies 0 4 813 29 HTTPClient::clear_all_cookies 0 1 155 58
/**
 * Removes the all stored cookies from the client.
 */
41
void HTTPClient::clear_all_cookies(void);

557 10 has_cookie 0 4 813 22 HTTPClient::has_cookie 0 1 156 124
/**
 * Returns true if there is a cookie in the client matching the given cookie's
 * domain/path/name, false otherwise.
 */
60
bool HTTPClient::has_cookie(HTTPCookie const &cookie) const;

558 10 get_cookie 0 4 813 22 HTTPClient::get_cookie 0 1 157 163
/**
 * Looks up and returns the cookie in the client matching the given cookie's
 * domain/path/name.  If there is no matching cookie, returns an empty cookie.
 */
66
HTTPCookie HTTPClient::get_cookie(HTTPCookie const &cookie) const;

559 17 copy_cookies_from 0 4 813 29 HTTPClient::copy_cookies_from 0 1 158 179
/**
 * Copies all the cookies from the indicated HTTPClient into this one.
 * Existing cookies in this client are not affected, unless they are shadowed
 * by the new cookies.
 */
60
void HTTPClient::copy_cookies_from(HTTPClient const &other);

560 13 write_cookies 0 4 813 25 HTTPClient::write_cookies 0 1 159 172
/**
 * Outputs the complete list of cookies stored on the client, for all domains,
 * including the expired cookies (which will normally not be sent back to a
 * host).
 */
56
void HTTPClient::write_cookies(std::ostream &out) const;

561 12 send_cookies 0 4 813 24 HTTPClient::send_cookies 0 1 160 192
/**
 * Writes to the indicated ostream a "Cookie" header line for sending the
 * cookies appropriate to the indicated URL along with an HTTP request.  This
 * also removes expired cookies.
 */
69
void HTTPClient::send_cookies(std::ostream &out, URLSpec const &url);

562 31 set_client_certificate_filename 0 4 813 43 HTTPClient::set_client_certificate_filename 0 1 161 250
/**
 * Sets the filename of the pem-formatted file that will be read for the
 * client public and private keys if an SSL server requests a certificate.
 * Either this or set_client_certificate_pem() may be used to specify a client
 * certificate.
 */
82
inline void HTTPClient::set_client_certificate_filename(Filename const &filename);

563 26 set_client_certificate_pem 0 4 813 38 HTTPClient::set_client_certificate_pem 0 1 162 264
/**
 * Sets the pem-formatted contents of the certificate that will be parsed for
 * the client public and private keys if an SSL server requests a certificate.
 * Either this or set_client_certificate_filename() may be used to specify a
 * client certificate.
 */
75
inline void HTTPClient::set_client_certificate_pem(std::string const &pem);

564 33 set_client_certificate_passphrase 0 4 813 45 HTTPClient::set_client_certificate_passphrase 0 1 163 160
/**
 * Sets the passphrase used to decrypt the private key in the certificate
 * named by set_client_certificate_filename() or set_client_certificate_pem().
 */
89
inline void HTTPClient::set_client_certificate_passphrase(std::string const &passphrase);

565 23 load_client_certificate 0 4 813 35 HTTPClient::load_client_certificate 0 1 164 376
/**
 * Attempts to load the certificate named by set_client_certificate_filename()
 * immediately, and returns true if successful, false otherwise.
 *
 * Normally this need not be explicitly called, since it will be called
 * automatically if the server requests a certificate, but it may be useful to
 * determine ahead of time if the certificate can be loaded correctly.
 */
47
bool HTTPClient::load_client_certificate(void);

566 43 add_preapproved_server_certificate_filename 0 4 813 55 HTTPClient::add_preapproved_server_certificate_filename 0 1 165 547
/**
 * Adds the certificate defined in the indicated PEM filename as a "pre-
 * approved" certificate for the indicated server, defined by the hostname and
 * port (only) from the given URL.
 *
 * If the server offers this particular certificate on a secure connection, it
 * will be accepted without question.  This is particularly useful for
 * communicating with a server using a known self-signed certificate.
 *
 * See also the similar add_preapproved_server_certificate_pem(), and the
 * weaker add_preapproved_server_certificate_name().
 */
107
bool HTTPClient::add_preapproved_server_certificate_filename(URLSpec const &url, Filename const &filename);

567 38 add_preapproved_server_certificate_pem 0 4 813 50 HTTPClient::add_preapproved_server_certificate_pem 0 1 166 577
/**
 * Adds the certificate defined in the indicated data string, formatted as a
 * PEM block, as a "pre-approved" certificate for the indicated server,
 * defined by the hostname and port (only) from the given URL.
 *
 * If the server offers this particular certificate on a secure connection, it
 * will be accepted without question.  This is particularly useful for
 * communicating with a server using a known self-signed certificate.
 *
 * See also the similar add_preapproved_server_certificate_filename(), and the
 * weaker add_preapproved_server_certificate_name().
 */
100
bool HTTPClient::add_preapproved_server_certificate_pem(URLSpec const &url, std::string const &pem);

568 39 add_preapproved_server_certificate_name 0 4 813 51 HTTPClient::add_preapproved_server_certificate_name 0 1 167 888
/**
 * Adds the certificate *name* only, as a "pre-approved" certificate name for
 * the indicated server, defined by the hostname and port (only) from the
 * given URL.
 *
 * This is a weaker function than
 * add_preapproved_server_certificate_filename().  This checks only the
 * subject name of the certificate, without checking for a particular
 * certificate by key.  This means that a variety of server certificates may
 * match the indicated name.
 *
 * Because this is a weaker verification, it only applies to server
 * certificates that are signed by a recognized certificate authority.  Thus,
 * it cannot be used to pre-approve self-signed certificates, but it can be
 * used to accept a server certificate offered by a different hostname than
 * the one in the cert itself.
 *
 * The certificate name should be formatted in the form
 * type0=value0/type1=value1/type2=...
 */
102
bool HTTPClient::add_preapproved_server_certificate_name(URLSpec const &url, std::string const &name);

569 37 clear_preapproved_server_certificates 0 4 813 49 HTTPClient::clear_preapproved_server_certificates 0 1 168 92
/**
 * Removes all preapproved server certificates for the indicated server and
 * port.
 */
75
void HTTPClient::clear_preapproved_server_certificates(URLSpec const &url);

570 41 clear_all_preapproved_server_certificates 0 4 813 53 HTTPClient::clear_all_preapproved_server_certificates 0 1 169 71
/**
 * Removes all preapproved server certificates for all servers.
 */
65
void HTTPClient::clear_all_preapproved_server_certificates(void);

571 16 set_http_version 0 4 813 28 HTTPClient::set_http_version 0 1 170 221
/**
 * Specifies the version of HTTP that the client uses to identify itself to
 * the server.  The default is HV_11, or HTTP 1.0; you can set this to HV_10
 * (HTTP 1.0) to request the server use the older interface.
 */
72
inline void HTTPClient::set_http_version(HTTPEnum::HTTPVersion version);

572 16 get_http_version 0 4 813 28 HTTPClient::get_http_version 0 1 171 93
/**
 * Returns the client's current setting for HTTP version.  See
 * set_http_version().
 */
70
inline HTTPEnum::HTTPVersion HTTPClient::get_http_version(void) const;

573 23 get_http_version_string 0 4 813 35 HTTPClient::get_http_version_string 0 1 172 99
/**
 * Returns the current HTTP version setting as a string, e.g.  "HTTP/1.0" or
 * "HTTP/1.1".
 */
60
std::string HTTPClient::get_http_version_string(void) const;

574 25 parse_http_version_string 0 4 813 37 HTTPClient::parse_http_version_string 0 1 173 193
/**
 * Matches the string representing a particular HTTP version against any of
 * the known versions and returns the appropriate enumerated value, or
 * HV_other if the version is unknown.
 */
95
static HTTPEnum::HTTPVersion HTTPClient::parse_http_version_string(std::string const &version);

575 17 load_certificates 0 4 813 29 HTTPClient::load_certificates 0 1 174 263
/**
 * Reads the certificate(s) (delimited by -----BEGIN CERTIFICATE----- and
 * -----END CERTIFICATE-----) from the indicated file and makes them known as
 * trusted public keys for validating future connections.  Returns true on
 * success, false otherwise.
 */
61
bool HTTPClient::load_certificates(Filename const &filename);

576 14 set_verify_ssl 0 4 813 26 HTTPClient::set_verify_ssl 0 1 175 263
/**
 * Specifies whether the client will insist on verifying the identity of the
 * servers it connects to via SSL (that is, https).
 *
 * The parameter value is an enumerated type which indicates the level of
 * security to which the client will insist upon.
 */
73
inline void HTTPClient::set_verify_ssl(HTTPClient::VerifySSL verify_ssl);

577 14 get_verify_ssl 0 4 813 26 HTTPClient::get_verify_ssl 0 1 176 157
/**
 * Returns whether the client will insist on verifying the identity of the
 * servers it connects to via SSL (that is, https).  See set_verify_ssl().
 */
68
inline HTTPClient::VerifySSL HTTPClient::get_verify_ssl(void) const;

578 15 set_cipher_list 0 4 813 27 HTTPClient::set_cipher_list 0 1 177 404
/**
 * Specifies the set of ciphers that are to be made available for SSL
 * connections.  This is a string as described in the ciphers(1) man page of
 * the OpenSSL documentation (or see
 * https://www.openssl.org/docs/man1.1.1/man1/ciphers.html ).  If this isn't
 * specified, the default is provided by the Config file.  You may also specify
 * "DEFAULT" to use the built-in OpenSSL default value.
 */
72
inline void HTTPClient::set_cipher_list(std::string const &cipher_list);

579 15 get_cipher_list 0 4 813 27 HTTPClient::get_cipher_list 0 1 178 93
/**
 * Returns the set of ciphers as set by set_cipher_list().  See
 * set_cipher_list().
 */
66
inline std::string const &HTTPClient::get_cipher_list(void) const;

580 12 make_channel 0 4 813 24 HTTPClient::make_channel 0 1 179 763
/**
 * Returns a new HTTPChannel object that may be used for reading multiple
 * documents using the same connection, for greater network efficiency than
 * calling HTTPClient::get_document() repeatedly (which would force a new
 * connection for each document).
 *
 * Also, HTTPChannel has some additional, less common interface methods than
 * the basic interface methods that exist on HTTPClient; if you wish to call
 * any of these methods you must first obtain an HTTPChannel.
 *
 * Pass true for persistent_connection to gain this network efficiency.  If,
 * on the other hand, your intention is to use the channel to retrieve only
 * one document, then pass false to inform the server that we will be dropping
 * the connection after the first document.
 */
78
PointerTo< HTTPChannel > HTTPClient::make_channel(bool persistent_connection);

581 9 post_form 0 4 813 21 HTTPClient::post_form 0 1 180 262
/**
 * Posts form data to a particular URL and retrieves the response.  Returns a
 * new HTTPChannel object whether the document is successfully read or not;
 * you can test is_valid() and get_return_code() to determine whether the
 * document was retrieved.
 */
92
PointerTo< HTTPChannel > HTTPClient::post_form(URLSpec const &url, std::string const &body);

582 12 get_document 0 4 813 24 HTTPClient::get_document 0 1 181 233
/**
 * Opens the named document for reading.  Returns a new HTTPChannel object
 * whether the document is successfully read or not; you can test is_valid()
 * and get_return_code() to determine whether the document was retrieved.
 */
70
PointerTo< HTTPChannel > HTTPClient::get_document(URLSpec const &url);

583 10 get_header 0 4 813 22 HTTPClient::get_header 0 1 182 252
/**
 * Like get_document(), except only the header associated with the document is
 * retrieved.  This may be used to test for existence of the document; it
 * might also return the size of the document (if the server gives us this
 * information).
 */
68
PointerTo< HTTPChannel > HTTPClient::get_header(URLSpec const &url);

584 13 base64_encode 0 4 813 25 HTTPClient::base64_encode 0 1 183 222
/**
 * Implements HTTPAuthorization::base64_encode().  This is provided here just
 * as a convenient place to publish it for access by the scripting language;
 * C++ code should probably use HTTPAuthorization directly.
 */
74
static inline std::string HTTPClient::base64_encode(std::string const &s);

585 13 base64_decode 0 4 813 25 HTTPClient::base64_decode 0 1 184 222
/**
 * Implements HTTPAuthorization::base64_decode().  This is provided here just
 * as a convenient place to publish it for access by the scripting language;
 * C++ code should probably use HTTPAuthorization directly.
 */
74
static inline std::string HTTPClient::base64_decode(std::string const &s);

586 14 get_global_ptr 0 4 813 26 HTTPClient::get_global_ptr 0 1 185 49
/**
 * Returns the default global HTTPClient.
 */
52
static HTTPClient *HTTPClient::get_global_ptr(void);

587 13 HTTPEntityTag 0 260 816 28 HTTPEntityTag::HTTPEntityTag 0 4 186 187 188 189 259
/**
 *
 */

/**
 * This constructor accepts an explicit weak flag and a literal (not quoted)
 * tag string.
 */

/**
 *
 */

/**
 * This constructor accepts a string as formatted from an HTTP server (e.g.
 * the tag is quoted, with an optional W/ prefix.)
 */
233
inline HTTPEntityTag::HTTPEntityTag(void);
HTTPEntityTag::HTTPEntityTag(std::string const &text);
inline HTTPEntityTag::HTTPEntityTag(bool weak, std::string const &tag);
inline HTTPEntityTag::HTTPEntityTag(HTTPEntityTag const &copy);

588 10 operator = 0 4 816 25 HTTPEntityTag::operator = 0 1 190 0
65
inline void HTTPEntityTag::operator =(HTTPEntityTag const &copy);

589 7 is_weak 0 4 816 22 HTTPEntityTag::is_weak 0 1 191 255
/**
 * Returns true if the entity tag is marked as "weak". A consistent weak
 * entity tag does not guarantee that its resource has not changed in any way,
 * but it does promise that the resource has not changed in any semantically
 * meaningful way.
 */
47
inline bool HTTPEntityTag::is_weak(void) const;

590 7 get_tag 0 4 816 22 HTTPEntityTag::get_tag 0 1 192 47
/**
 * Returns the tag as a literal string.
 */
61
inline std::string const &HTTPEntityTag::get_tag(void) const;

591 10 get_string 0 4 816 25 HTTPEntityTag::get_string 0 1 193 126
/**
 * Returns the entity tag formatted for sending to an HTTP server (the tag is
 * quoted, with a conditional W prefix).
 */
50
std::string HTTPEntityTag::get_string(void) const;

592 12 strong_equiv 0 4 816 27 HTTPEntityTag::strong_equiv 0 1 194 115
/**
 * Returns true if the two tags have "strong" equivalence: they are the same
 * tag, and both are "strong".
 */
74
inline bool HTTPEntityTag::strong_equiv(HTTPEntityTag const &other) const;

593 10 weak_equiv 0 4 816 25 HTTPEntityTag::weak_equiv 0 1 195 121
/**
 * Returns true if the two tags have "weak" equivalence: they are the same
 * tag, and one or both may be "weak".
 */
72
inline bool HTTPEntityTag::weak_equiv(HTTPEntityTag const &other) const;

594 11 operator == 0 4 816 26 HTTPEntityTag::operator == 0 1 196 0
73
inline bool HTTPEntityTag::operator ==(HTTPEntityTag const &other) const;

595 11 operator != 0 4 816 26 HTTPEntityTag::operator != 0 1 197 0
73
inline bool HTTPEntityTag::operator !=(HTTPEntityTag const &other) const;

596 10 operator < 0 4 816 25 HTTPEntityTag::operator < 0 1 198 0
72
inline bool HTTPEntityTag::operator <(HTTPEntityTag const &other) const;

597 10 compare_to 0 4 816 25 HTTPEntityTag::compare_to 0 1 199 167
/**
 * Returns a number less than zero if this HTTPEntityTag sorts before the
 * other one, greater than zero if it sorts after, or zero if they are
 * equivalent.
 */
71
inline int HTTPEntityTag::compare_to(HTTPEntityTag const &other) const;

598 6 output 0 4 816 21 HTTPEntityTag::output 0 1 200 10
/**
 *
 */
59
inline void HTTPEntityTag::output(std::ostream &out) const;

599 14 ~HTTPEntityTag 0 516 816 29 HTTPEntityTag::~HTTPEntityTag 0 0 0
36
HTTPEntityTag::~HTTPEntityTag(void);

600 12 DocumentSpec 0 260 817 26 DocumentSpec::DocumentSpec 0 4 201 202 203 204 46
/**
 *
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
215
inline DocumentSpec::DocumentSpec(void);
inline DocumentSpec::DocumentSpec(std::string const &url);
inline DocumentSpec::DocumentSpec(URLSpec const &url);
inline DocumentSpec::DocumentSpec(DocumentSpec const &copy);

601 10 operator = 0 4 817 24 DocumentSpec::operator = 0 1 205 0
63
inline void DocumentSpec::operator =(DocumentSpec const &copy);

602 11 operator == 0 4 817 25 DocumentSpec::operator == 0 1 206 0
71
inline bool DocumentSpec::operator ==(DocumentSpec const &other) const;

603 11 operator != 0 4 817 25 DocumentSpec::operator != 0 1 207 0
71
inline bool DocumentSpec::operator !=(DocumentSpec const &other) const;

604 10 operator < 0 4 817 24 DocumentSpec::operator < 0 1 208 0
70
inline bool DocumentSpec::operator <(DocumentSpec const &other) const;

605 10 compare_to 0 4 817 24 DocumentSpec::compare_to 0 1 209 10
/**
 *
 */
62
int DocumentSpec::compare_to(DocumentSpec const &other) const;

606 7 set_url 0 4 817 21 DocumentSpec::set_url 0 1 210 311
/**
 * Changes the URL of the DocumentSpec without modifying its other properties.
 * Normally this would be a strange thing to do, because the tag and date are
 * usually strongly associated with the URL.  To get a DocumentSpec pointing
 * to a new URL, you would normally create a new DocumentSpec object.
 */
54
inline void DocumentSpec::set_url(URLSpec const &url);

607 7 get_url 0 4 817 21 DocumentSpec::get_url 0 1 211 49
/**
 * Retrieves the URL of the DocumentSpec.
 */
56
inline URLSpec const &DocumentSpec::get_url(void) const;

608 7 set_tag 0 4 817 21 DocumentSpec::set_tag 0 1 212 69
/**
 * Changes the identity tag associated with the DocumentSpec.
 */
60
inline void DocumentSpec::set_tag(HTTPEntityTag const &tag);

609 7 has_tag 0 4 817 21 DocumentSpec::has_tag 0 1 213 79
/**
 * Returns true if an identity tag is associated with the DocumentSpec.
 */
46
inline bool DocumentSpec::has_tag(void) const;

610 7 get_tag 0 4 817 21 DocumentSpec::get_tag 0 1 214 254
/**
 * Returns the identity tag associated with the DocumentSpec, if there is one.
 * It is an error to call this if has_tag() returns false.
 *
 * The identity tag is set by the HTTP server to uniquely refer to a
 * particular version of a document.
 */
62
inline HTTPEntityTag const &DocumentSpec::get_tag(void) const;

611 9 clear_tag 0 4 817 23 DocumentSpec::clear_tag 0 1 215 86
/**
 * Removes the identity tag associated with the DocumentSpec, if there is one.
 */
42
inline void DocumentSpec::clear_tag(void);

612 8 set_date 0 4 817 22 DocumentSpec::set_date 0 1 216 75
/**
 * Changes the last-modified date associated with the DocumentSpec.
 */
57
inline void DocumentSpec::set_date(HTTPDate const &date);

613 8 has_date 0 4 817 22 DocumentSpec::has_date 0 1 217 84
/**
 * Returns true if a last-modified date is associated with the DocumentSpec.
 */
47
inline bool DocumentSpec::has_date(void) const;

614 8 get_date 0 4 817 22 DocumentSpec::get_date 0 1 218 153
/**
 * Returns the last-modified date associated with the DocumentSpec, if there
 * is one.  It is an error to call this if has_date() returns false.
 */
58
inline HTTPDate const &DocumentSpec::get_date(void) const;

615 10 clear_date 0 4 817 24 DocumentSpec::clear_date 0 1 219 95
/**
 * Removes the last-modified date associated with the DocumentSpec, if there
 * is one.
 */
43
inline void DocumentSpec::clear_date(void);

616 16 set_request_mode 0 4 817 30 DocumentSpec::set_request_mode 0 1 220 1335
/**
 * Sets the request mode of this DocumentSpec.  This is only relevant when
 * using the DocumentSpec to generate a request (for instance, in
 * HTTPChannel).  This specifies whether the document request will ask the
 * server for a newer version than the indicated version, or the exact
 * version, neither, or either.
 *
 * The possible values are:
 *
 * RM_any: ignore date and tag (if specified), and retrieve any document that
 * matches the URL.  For a subrange request, if the document matches the
 * version indicated exactly, retrieve the subrange only; otherwise, retrieve
 * the entire document.
 *
 * RM_equal: request only the precise version of the document that matches the
 * particular date and/or tag exactly, if specified; fail if this version is
 * not available.
 *
 * RM_newer: request any document that is newer than the version indicated by
 * the particular date and/or tag; fail if only that version (or older
 * versions) are available.
 *
 * RM_newer_or_equal: request any document that matches the version indicated
 * by the particular date and/or tag, or is a newer version; fail if only
 * older versions are available.
 *
 * In any of the above, you may specify either or both of the last-modified
 * date and the identity tag, whichever is known to the client.
 *
 * The default mode is RM_any.
 */
83
inline void DocumentSpec::set_request_mode(DocumentSpec::RequestMode request_mode);

617 16 get_request_mode 0 4 817 30 DocumentSpec::get_request_mode 0 1 221 82
/**
 * Returns the request mode of this DocumentSpec.  See set_request_mode().
 */
76
inline DocumentSpec::RequestMode DocumentSpec::get_request_mode(void) const;

618 17 set_cache_control 0 4 817 31 DocumentSpec::set_cache_control 0 1 222 642
/**
 * Specifies what kind of cached value is acceptable for this document.
 * Warning: some HTTP proxies may not respect this setting and may return a
 * cached result anyway.
 *
 * CC_allow_cache: the normal HTTP behavior; the server may return a cached
 * value if it believes it is valid.
 *
 * CC_revalidate: a proxy is forced to contact the origin server and verify
 * that is cached value is in fact still valid before it returns it.
 *
 * CC_no_cache: a proxy must not return its cached value at all, but is forced
 * to go all the way back to the origin server for the official document.
 *
 * The default mode is CC_allow_cache.
 */
86
inline void DocumentSpec::set_cache_control(DocumentSpec::CacheControl cache_control);

619 17 get_cache_control 0 4 817 31 DocumentSpec::get_cache_control 0 1 223 83
/**
 * Returns the request mode of this DocumentSpec.  See set_cache_control().
 */
78
inline DocumentSpec::CacheControl DocumentSpec::get_cache_control(void) const;

620 5 input 0 4 817 19 DocumentSpec::input 0 1 224 152
/**
 * Can be used to read in the DocumentSpec from a stream generated either by
 * output() or write().  Returns true on success, false on failure.
 */
43
bool DocumentSpec::input(std::istream &in);

621 6 output 0 4 817 20 DocumentSpec::output 0 1 225 10
/**
 *
 */
51
void DocumentSpec::output(std::ostream &out) const;

622 5 write 0 4 817 19 DocumentSpec::write 0 1 226 10
/**
 *
 */
72
void DocumentSpec::write(std::ostream &out, int indent_level = 0) const;

623 13 ~DocumentSpec 0 516 817 27 DocumentSpec::~DocumentSpec 0 0 0
34
DocumentSpec::~DocumentSpec(void);

624 10 get_client 0 4 823 23 HTTPChannel::get_client 0 1 228 64
/**
 * Returns the HTTPClient object that owns this channel.
 */
55
inline HTTPClient *HTTPChannel::get_client(void) const;

625 8 is_valid 0 4 823 21 HTTPChannel::is_valid 0 1 229 126
/**
 * Returns true if the last-requested document was successfully retrieved and
 * is ready to be read, false otherwise.
 */
46
inline bool HTTPChannel::is_valid(void) const;

626 19 is_connection_ready 0 4 823 32 HTTPChannel::is_connection_ready 0 1 230 158
/**
 * Returns true if a connection has been established to the named server in a
 * previous call to connect_to() or begin_connect_to(), false otherwise.
 */
57
inline bool HTTPChannel::is_connection_ready(void) const;

627 7 get_url 0 4 823 20 HTTPChannel::get_url 0 1 231 287
/**
 * Returns the URL that was used to retrieve the most recent document:
 * whatever URL was last passed to get_document() or get_header().  If a
 * redirect has transparently occurred, this will return the new, redirected
 * URL (the actual URL at which the document was located).
 */
55
inline URLSpec const &HTTPChannel::get_url(void) const;

628 17 get_document_spec 0 4 823 30 HTTPChannel::get_document_spec 0 1 232 380
/**
 * Returns the DocumentSpec associated with the most recent document.  This
 * includes its actual URL (following redirects) along with the identity tag
 * and last-modified date, if supplied by the server.
 *
 * This structure may be saved and used to retrieve the same version of the
 * document later, or to conditionally retrieve a newer version if it is
 * available.
 */
70
inline DocumentSpec const &HTTPChannel::get_document_spec(void) const;

629 16 get_http_version 0 4 823 29 HTTPChannel::get_http_version 0 1 233 137
/**
 * Returns the HTTP version number returned by the server, as one of the
 * HTTPClient enumerated types, e.g.  HTTPClient::HV_11.
 */
71
inline HTTPEnum::HTTPVersion HTTPChannel::get_http_version(void) const;

630 23 get_http_version_string 0 4 823 36 HTTPChannel::get_http_version_string 0 1 234 110
/**
 * Returns the HTTP version number returned by the server, formatted as a
 * string, e.g.  "HTTP/1.1".
 */
75
inline std::string const &HTTPChannel::get_http_version_string(void) const;

631 15 get_status_code 0 4 823 28 HTTPChannel::get_status_code 0 1 235 501
/**
 * Returns the HTML return code from the document retrieval request.  This
 * will be in the 200 range if the document is successfully retrieved, or some
 * other value in the case of an error.
 *
 * Some proxy errors during an https-over-proxy request would return the same
 * status code as a different error that occurred on the host server.  To
 * differentiate these cases, status codes that are returned by the proxy
 * during the CONNECT phase (except code 407) are incremented by 1000.
 */
52
inline int HTTPChannel::get_status_code(void) const;

632 17 get_status_string 0 4 823 30 HTTPChannel::get_status_string 0 1 236 133
/**
 * Returns the string as returned by the server describing the status code for
 * humans.  This may or may not be meaningful.
 */
55
std::string HTTPChannel::get_status_string(void) const;

633 13 get_www_realm 0 4 823 26 HTTPChannel::get_www_realm 0 1 237 363
/**
 * If the document failed to connect because of a 401 (Authorization
 * required), this method will return the "realm" returned by the server in
 * which the requested document must be authenticated.  This string may be
 * presented to the user to request an associated username and password (which
 * then should be stored in HTTPClient::set_username()).
 */
65
inline std::string const &HTTPChannel::get_www_realm(void) const;

634 15 get_proxy_realm 0 4 823 28 HTTPChannel::get_proxy_realm 0 1 238 311
/**
 * If the document failed to connect because of a 407 (Proxy authorization
 * required), this method will return the "realm" returned by the proxy.  This
 * string may be presented to the user to request an associated username and
 * password (which then should be stored in HTTPClient::set_username()).
 */
67
inline std::string const &HTTPChannel::get_proxy_realm(void) const;

635 12 get_redirect 0 4 823 25 HTTPChannel::get_redirect 0 1 239 366
/**
 * If the document failed with a redirect code (300 series), this will
 * generally contain the new URL the server wants us to try.  In many cases,
 * the client will automatically follow redirects; if these are successful the
 * client will return a successful code and get_redirect() will return empty,
 * but get_url() will return the new, redirected URL.
 */
60
inline URLSpec const &HTTPChannel::get_redirect(void) const;

636 16 get_header_value 0 4 823 29 HTTPChannel::get_header_value 0 1 240 160
/**
 * Returns the HTML header value associated with the indicated key, or empty
 * string if the key was not defined in the message returned by the server.
 */
72
std::string HTTPChannel::get_header_value(std::string const &key) const;

637 22 get_num_redirect_steps 0 4 823 35 HTTPChannel::get_num_redirect_steps 0 1 241 212
/**
 * If the document automatically followed one or more redirects, this will
 * return the number of redirects that were automatically followed.  Use
 * get_redirect_step() to retrieve each URL in sequence.
 */
59
inline int HTTPChannel::get_num_redirect_steps(void) const;

638 17 get_redirect_step 0 4 823 30 HTTPChannel::get_redirect_step 0 1 242 182
/**
 * Use in conjunction with get_num_redirect_steps() to extract the chain of
 * URL's that the channel was automatically redirected through to arrive at
 * the final document.
 */
66
inline URLSpec const &HTTPChannel::get_redirect_step(int n) const;

639 25 set_persistent_connection 0 4 823 38 HTTPChannel::set_persistent_connection 0 1 243 532
/**
 * Indicates whether the HTTPChannel should try to keep the connection to the
 * server open and reuse that connection for multiple documents, or whether it
 * should close the connection and open a new one for each request.  Set this
 * true to keep the connections around when possible, false to recycle them.
 *
 * It makes most sense to set this false when the HTTPChannel will be used
 * only once to retrieve a single document, true when you will be using the
 * same HTTPChannel object to retrieve multiple documents.
 */
79
inline void HTTPChannel::set_persistent_connection(bool persistent_connection);

640 25 get_persistent_connection 0 4 823 38 HTTPChannel::get_persistent_connection 0 1 244 267
/**
 * Returns whether the HTTPChannel should try to keep the connection to the
 * server open and reuse that connection for multiple documents, or whether it
 * should close the connection and open a new one for each request.  See
 * set_persistent_connection().
 */
63
inline bool HTTPChannel::get_persistent_connection(void) const;

641 21 will_close_connection 0 4 823 34 HTTPChannel::will_close_connection 0 1 245 217
/**
 * Returns true if the server has indicated it will close the connection after
 * this document has been read, or false if it will remain open (and future
 * documents may be requested on the same connection).
 */
52
bool HTTPChannel::will_close_connection(void) const;

642 15 set_allow_proxy 0 4 823 28 HTTPChannel::set_allow_proxy 0 1 246 325
/**
 * If this is true (the normal case), the HTTPClient will be consulted for
 * information about the proxy to be used for each connection via this
 * HTTPChannel.  If this has been set to false by the user, then all
 * connections will be made directly, regardless of the proxy settings
 * indicated on the HTTPClient.
 */
59
inline void HTTPChannel::set_allow_proxy(bool allow_proxy);

643 15 get_allow_proxy 0 4 823 28 HTTPChannel::get_allow_proxy 0 1 247 325
/**
 * If this is true (the normal case), the HTTPClient will be consulted for
 * information about the proxy to be used for each connection via this
 * HTTPChannel.  If this has been set to false by the user, then all
 * connections will be made directly, regardless of the proxy settings
 * indicated on the HTTPClient.
 */
53
inline bool HTTPChannel::get_allow_proxy(void) const;

644 16 set_proxy_tunnel 0 4 823 29 HTTPChannel::set_proxy_tunnel 0 1 248 795
/**
 * Normally, a proxy is itself asked for ordinary URL's, and the proxy decides
 * whether to hand the client a cached version of the document or to contact
 * the server for a fresh version.  The proxy may also modify the headers and
 * transfer encoding on the way.
 *
 * If this is set to true, then instead of asking for URL's from the proxy, we
 * will ask the proxy to open a connection to the server (for instance, on
 * port 80); if the proxy honors this request, then we contact the server
 * directly through this connection to retrieve the document.  If the proxy
 * does not honor the connect request, then the retrieve operation fails.
 *
 * SSL connections (e.g.  https), and connections through a Socks proxy, are
 * always tunneled, regardless of the setting of this flag.
 */
61
inline void HTTPChannel::set_proxy_tunnel(bool proxy_tunnel);

645 16 get_proxy_tunnel 0 4 823 29 HTTPChannel::get_proxy_tunnel 0 1 249 172
/**
 * Returns true if connections always tunnel through a proxy, or false (the
 * normal case) if we allow the proxy to serve up documents.  See
 * set_proxy_tunnel().
 */
54
inline bool HTTPChannel::get_proxy_tunnel(void) const;

646 19 set_connect_timeout 0 4 823 32 HTTPChannel::set_connect_timeout 0 1 250 322
/**
 * Sets the maximum length of time, in seconds, that the channel will wait
 * before giving up on establishing a TCP connection.
 *
 * At present, this is used only for the nonblocking interfaces (e.g.
 * begin_get_document(), begin_connect_to()), but it is used whether
 * set_blocking_connect() is true or false.
 */
69
inline void HTTPChannel::set_connect_timeout(double timeout_seconds);

647 19 get_connect_timeout 0 4 823 32 HTTPChannel::get_connect_timeout 0 1 251 130
/**
 * Returns the length of time, in seconds, to wait for a new nonblocking
 * socket to connect.  See set_connect_timeout().
 */
59
inline double HTTPChannel::get_connect_timeout(void) const;

648 20 set_blocking_connect 0 4 823 33 HTTPChannel::set_blocking_connect 0 1 252 641
/**
 * If this flag is true, a socket connect will block even for nonblocking I/O
 * calls like begin_get_document(), begin_connect_to(), etc.  If false, a
 * socket connect will not block for nonblocking I/O calls, but will block for
 * blocking I/O calls (get_document(), connect_to(), etc.).
 *
 * Setting this true is useful when you want to use non-blocking I/O once you
 * have established the connection, but you don't want to bother with polling
 * for the initial connection.  It's also useful when you don't particularly
 * care about non-blocking I/O, but you need to respect timeouts like
 * connect_timeout and http_timeout.
 */
69
inline void HTTPChannel::set_blocking_connect(bool blocking_connect);

649 20 get_blocking_connect 0 4 823 33 HTTPChannel::get_blocking_connect 0 1 253 298
/**
 * If this flag is true, a socket connect will block even for nonblocking I/O
 * calls like begin_get_document(), begin_connect_to(), etc.  If false, a
 * socket connect will not block for nonblocking I/O calls, but will block for
 * blocking I/O calls (get_document(), connect_to(), etc.).
 */
58
inline bool HTTPChannel::get_blocking_connect(void) const;

650 16 set_http_timeout 0 4 823 29 HTTPChannel::set_http_timeout 0 1 254 485
/**
 * Sets the maximum length of time, in seconds, that the channel will wait for
 * the HTTP server to finish sending its response to our request.
 *
 * The timer starts counting after the TCP connection has been established
 * (see set_connect_timeout(), above) and the request has been sent.
 *
 * At present, this is used only for the nonblocking interfaces (e.g.
 * begin_get_document(), begin_connect_to()), but it is used whether
 * set_blocking_connect() is true or false.
 */
66
inline void HTTPChannel::set_http_timeout(double timeout_seconds);

651 16 get_http_timeout 0 4 823 29 HTTPChannel::get_http_timeout 0 1 255 133
/**
 * Returns the length of time, in seconds, to wait for the HTTP server to
 * respond to our request.  See set_http_timeout().
 */
56
inline double HTTPChannel::get_http_timeout(void) const;

652 18 set_skip_body_size 0 4 823 31 HTTPChannel::set_skip_body_size 0 1 256 632
/**
 * Specifies the maximum number of bytes in a received (but unwanted) body
 * that will be skipped past, in order to reset to a new request.
 *
 * That is, if this HTTPChannel requests a file via get_document(), but does
 * not call download_to_ram(), download_to_file(), or open_read_body(), and
 * instead immediately requests a new file, then the HTTPChannel has a choice
 * whether to skip past the unwanted document, or to close the connection and
 * open a new one.  If the number of bytes to skip is more than this
 * threshold, the connection will be closed; otherwise, the data will simply
 * be read and discarded.
 */
72
inline void HTTPChannel::set_skip_body_size(std::size_t skip_body_size);

653 18 get_skip_body_size 0 4 823 31 HTTPChannel::get_skip_body_size 0 1 257 176
/**
 * Returns the maximum number of bytes in a received (but unwanted) body that
 * will be skipped past, in order to reset to a new request.  See
 * set_skip_body_size().
 */
63
inline std::size_t HTTPChannel::get_skip_body_size(void) const;

654 16 set_idle_timeout 0 4 823 29 HTTPChannel::set_idle_timeout 0 1 258 387
/**
 * Specifies the amount of time, in seconds, in which a previously-established
 * connection is allowed to remain open and unused.  If a previous connection
 * has remained unused for at least this number of seconds, it will be closed
 * and a new connection will be opened; otherwise, the same connection will be
 * reused for the next request (for this particular HTTPChannel).
 */
63
inline void HTTPChannel::set_idle_timeout(double idle_timeout);

655 16 get_idle_timeout 0 4 823 29 HTTPChannel::get_idle_timeout 0 1 259 162
/**
 * Returns the amount of time, in seconds, in which an previously-established
 * connection is allowed to remain open and unused.  See set_idle_timeout().
 */
56
inline double HTTPChannel::get_idle_timeout(void) const;

656 21 set_download_throttle 0 4 823 34 HTTPChannel::set_download_throttle 0 1 260 686
/**
 * Specifies whether nonblocking downloads (via download_to_file() or
 * download_to_ram()) will be limited so as not to use all available
 * bandwidth.
 *
 * If this is true, when a download has been started on this channel it will
 * be invoked no more frequently than get_max_updates_per_second(), and the
 * total bandwidth used by the download will be no more than
 * get_max_bytes_per_second().  If this is false, downloads will proceed as
 * fast as the server can send the data.
 *
 * This only has effect on the nonblocking I/O methods like
 * begin_get_document(), etc.  The blocking methods like get_document() always
 * use as much CPU and bandwidth as they can get.
 */
71
inline void HTTPChannel::set_download_throttle(bool download_throttle);

657 21 get_download_throttle 0 4 823 34 HTTPChannel::get_download_throttle 0 1 261 112
/**
 * Returns whether the nonblocking downloads will be bandwidth-limited.  See
 * set_download_throttle().
 */
59
inline bool HTTPChannel::get_download_throttle(void) const;

658 24 set_max_bytes_per_second 0 4 823 37 HTTPChannel::set_max_bytes_per_second 0 1 262 178
/**
 * When bandwidth throttling is in effect (see set_download_throttle()), this
 * specifies the maximum number of bytes per second that may be consumed by
 * this channel.
 */
79
inline void HTTPChannel::set_max_bytes_per_second(double max_bytes_per_second);

659 24 get_max_bytes_per_second 0 4 823 37 HTTPChannel::get_max_bytes_per_second 0 1 263 135
/**
 * Returns the maximum number of bytes per second that may be consumed by this
 * channel when get_download_throttle() is true.
 */
64
inline double HTTPChannel::get_max_bytes_per_second(void) const;

660 26 set_max_updates_per_second 0 4 823 39 HTTPChannel::set_max_updates_per_second 0 1 264 194
/**
 * When bandwidth throttling is in effect (see set_download_throttle()), this
 * specifies the maximum number of times per second that run() will attempt to
 * do any downloading at all.
 */
83
inline void HTTPChannel::set_max_updates_per_second(double max_updates_per_second);

661 26 get_max_updates_per_second 0 4 823 39 HTTPChannel::get_max_updates_per_second 0 1 265 134
/**
 * Returns the maximum number of times per second that run() will do anything
 * at all, when get_download_throttle() is true.
 */
66
inline double HTTPChannel::get_max_updates_per_second(void) const;

662 16 set_content_type 0 4 823 29 HTTPChannel::set_content_type 0 1 266 127
/**
 * Specifies the Content-Type header, useful for applications that require
 * different types of content, such as JSON.
 */
68
inline void HTTPChannel::set_content_type(std::string content_type);

663 16 get_content_type 0 4 823 29 HTTPChannel::get_content_type 0 1 267 56
/**
 * Returns the value of the Content-Type header.
 */
61
inline std::string HTTPChannel::get_content_type(void) const;

664 22 set_expected_file_size 0 4 823 35 HTTPChannel::set_expected_file_size 0 1 268 320
/**
 * This may be called immediately after a call to get_document() or some
 * related function to specify the expected size of the document we are
 * retrieving, if we happen to know.  This is used as the return value to
 * get_file_size() only in the case that the server does not tell us the
 * actual file size.
 */
71
inline void HTTPChannel::set_expected_file_size(std::size_t file_size);

665 13 get_file_size 0 4 823 26 HTTPChannel::get_file_size 0 1 269 506
/**
 * Returns the size of the file, if it is known.  Returns the value set by
 * set_expected_file_size() if the file size is not known, or 0 if this value
 * was not set.
 *
 * If the file is dynamically generated, the size may not be available until a
 * read has started (e.g.  open_read_body() has been called); and even then it
 * may increase as more of the file is read due to the nature of HTTP/1.1
 * requests which can change their minds midstream about how much data they're
 * sending you.
 */
55
std::streamsize HTTPChannel::get_file_size(void) const;

666 18 is_file_size_known 0 4 823 31 HTTPChannel::is_file_size_known 0 1 270 327
/**
 * Returns true if the size of the file we are currently retrieving was told
 * us by the server and thus is reliably known, or false if the size reported
 * by get_file_size() represents an educated guess (possibly as set by
 * set_expected_file_size(), or as inferred from a chunked transfer encoding
 * in progress).
 */
56
inline bool HTTPChannel::is_file_size_known(void) const;

667 24 get_first_byte_requested 0 4 823 37 HTTPChannel::get_first_byte_requested 0 1 271 289
/**
 * Returns the first byte of the file requested by the request.  This will
 * normally be 0 to indicate that the file is being requested from the
 * beginning, but if the file was requested via a get_subdocument() call, this
 * will contain the first_byte parameter from that call.
 */
69
inline std::size_t HTTPChannel::get_first_byte_requested(void) const;

668 23 get_last_byte_requested 0 4 823 36 HTTPChannel::get_last_byte_requested 0 1 272 285
/**
 * Returns the last byte of the file requested by the request.  This will
 * normally be 0 to indicate that the file is being requested to its last
 * byte, but if the file was requested via a get_subdocument() call, this will
 * contain the last_byte parameter from that call.
 */
68
inline std::size_t HTTPChannel::get_last_byte_requested(void) const;

669 24 get_first_byte_delivered 0 4 823 37 HTTPChannel::get_first_byte_delivered 0 1 273 367
/**
 * Returns the first byte of the file (that will be) delivered by the server
 * in response to the current request.  Normally, this is the same as
 * get_first_byte_requested(), but some servers will ignore a subdocument
 * request and always return the whole file, in which case this value will be
 * 0, regardless of what was requested to get_subdocument().
 */
69
inline std::size_t HTTPChannel::get_first_byte_delivered(void) const;

670 23 get_last_byte_delivered 0 4 823 36 HTTPChannel::get_last_byte_delivered 0 1 274 365
/**
 * Returns the last byte of the file (that will be) delivered by the server in
 * response to the current request.  Normally, this is the same as
 * get_last_byte_requested(), but some servers will ignore a subdocument
 * request and always return the whole file, in which case this value will be
 * 0, regardless of what was requested to get_subdocument().
 */
68
inline std::size_t HTTPChannel::get_last_byte_delivered(void) const;

671 13 write_headers 0 4 823 26 HTTPChannel::write_headers 0 1 275 97
/**
 * Outputs a list of all headers defined by the server to the indicated output
 * stream.
 */
57
void HTTPChannel::write_headers(std::ostream &out) const;

672 5 reset 0 4 823 18 HTTPChannel::reset 0 1 276 538
/**
 * Stops whatever file transaction is currently in progress, closes the
 * connection, and resets to begin anew.  You shouldn't ever need to call
 * this, since the channel should be able to reset itself cleanly between
 * requests, but it is provided in case you are an especially nervous type.
 *
 * Don't call this after every request unless you set
 * set_persistent_connection() to false, since calling reset() rudely closes
 * the connection regardless of whether we have told the server we intend to
 * keep it open or not.
 */
37
inline void HTTPChannel::reset(void);

673 15 preserve_status 0 4 823 28 HTTPChannel::preserve_status 0 1 277 487
/**
 * Preserves the previous status code (presumably a failure) from the previous
 * connection attempt.  If the subsequent connection attempt also fails, the
 * returned status code will be the better of the previous code and the
 * current code.
 *
 * This can be called to daisy-chain subsequent attempts to download the same
 * document from different servers.  After all servers have been attempted,
 * the final status code will reflect the attempt that most nearly succeeded.
 */
47
inline void HTTPChannel::preserve_status(void);

674 19 clear_extra_headers 0 4 823 32 HTTPChannel::clear_extra_headers 0 1 278 99
/**
 * Resets the extra headers that were previously added via calls to
 * send_extra_header().
 */
51
inline void HTTPChannel::clear_extra_headers(void);

675 17 send_extra_header 0 4 823 30 HTTPChannel::send_extra_header 0 1 279 352
/**
 * Specifies an additional key: value pair that is added into the header sent
 * to the server with the next request.  This is passed along with no
 * interpretation by the HTTPChannel code.  You may call this repeatedly to
 * append multiple headers.
 *
 * This is persistent for one request only; it must be set again for each new
 * request.
 */
93
inline void HTTPChannel::send_extra_header(std::string const &key, std::string const &value);

676 12 get_document 0 4 823 25 HTTPChannel::get_document 0 1 280 111
/**
 * Opens the named document for reading, if available.  Returns true if
 * successful, false otherwise.
 */
63
inline bool HTTPChannel::get_document(DocumentSpec const &url);

677 15 get_subdocument 0 4 823 28 HTTPChannel::get_subdocument 0 1 281 313
/**
 * Retrieves only the specified byte range of the indicated document.  If
 * last_byte is 0, it stands for the last byte of the document.  When a
 * subdocument is requested, get_file_size() and get_bytes_downloaded() will
 * report the number of bytes of the subdocument, not of the complete
 * document.
 */
113
inline bool HTTPChannel::get_subdocument(DocumentSpec const &url, std::size_t first_byte, std::size_t last_byte);

678 10 get_header 0 4 823 23 HTTPChannel::get_header 0 1 282 252
/**
 * Like get_document(), except only the header associated with the document is
 * retrieved.  This may be used to test for existence of the document; it
 * might also return the size of the document (if the server gives us this
 * information).
 */
61
inline bool HTTPChannel::get_header(DocumentSpec const &url);

679 9 post_form 0 4 823 22 HTTPChannel::post_form 0 1 283 74
/**
 * Posts form data to a particular URL and retrieves the response.
 */
85
inline bool HTTPChannel::post_form(DocumentSpec const &url, std::string const &body);

680 12 put_document 0 4 823 25 HTTPChannel::put_document 0 1 284 111
/**
 * Uploads the indicated body to the server to replace the indicated URL, if
 * the server allows this.
 */
88
inline bool HTTPChannel::put_document(DocumentSpec const &url, std::string const &body);

681 15 delete_document 0 4 823 28 HTTPChannel::delete_document 0 1 285 59
/**
 * Requests the server to remove the indicated URL.
 */
66
inline bool HTTPChannel::delete_document(DocumentSpec const &url);

682 9 get_trace 0 4 823 22 HTTPChannel::get_trace 0 1 286 159
/**
 * Sends a TRACE message to the server, which should return back the same
 * message as the server received it, allowing inspection of proxy hops, etc.
 */
60
inline bool HTTPChannel::get_trace(DocumentSpec const &url);

683 10 connect_to 0 4 823 23 HTTPChannel::connect_to 0 1 287 319
/**
 * Establish a direct connection to the server and port indicated by the URL,
 * but do not issue any HTTP requests.  If successful, the connection may then
 * be taken to use for whatever purposes you like by calling get_connection().
 *
 * This establishes a blocking I/O socket.  Also see begin_connect_to().
 */
61
inline bool HTTPChannel::connect_to(DocumentSpec const &url);

684 11 get_options 0 4 823 24 HTTPChannel::get_options 0 1 288 136
/**
 * Sends an OPTIONS message to the server, which should query the available
 * options, possibly in relation to a specified URL.
 */
62
inline bool HTTPChannel::get_options(DocumentSpec const &url);

685 18 begin_get_document 0 4 823 31 HTTPChannel::begin_get_document 0 1 289 445
/**
 * Begins a non-blocking request to retrieve a given document.  This method
 * will return immediately, even before a connection to the server has
 * necessarily been established; you must then call run() from time to time
 * until the return value of run() is false.  Then you may check is_valid()
 * and get_status_code() to determine the status of your request.
 *
 * If a previous request had been pending, that request is discarded.
 */
69
inline void HTTPChannel::begin_get_document(DocumentSpec const &url);

686 21 begin_get_subdocument 0 4 823 34 HTTPChannel::begin_get_subdocument 0 1 290 345
/**
 * Begins a non-blocking request to retrieve only the specified byte range of
 * the indicated document.  If last_byte is 0, it stands for the last byte of
 * the document.  When a subdocument is requested, get_file_size() and
 * get_bytes_downloaded() will report the number of bytes of the subdocument,
 * not of the complete document.
 */
119
inline void HTTPChannel::begin_get_subdocument(DocumentSpec const &url, std::size_t first_byte, std::size_t last_byte);

687 16 begin_get_header 0 4 823 29 HTTPChannel::begin_get_header 0 1 291 115
/**
 * Begins a non-blocking request to retrieve a given header.  See
 * begin_get_document() and get_header().
 */
67
inline void HTTPChannel::begin_get_header(DocumentSpec const &url);

688 15 begin_post_form 0 4 823 28 HTTPChannel::begin_post_form 0 1 292 431
/**
 * Posts form data to a particular URL and retrieves the response, all using
 * non-blocking I/O.  See begin_get_document() and post_form().
 *
 * It is important to note that you *must* call run() repeatedly after calling
 * this method until run() returns false, and you may not call any other
 * document posting or retrieving methods using the HTTPChannel object in the
 * interim, or your form data may not get posted.
 */
91
inline void HTTPChannel::begin_post_form(DocumentSpec const &url, std::string const &body);

689 3 run 0 4 823 16 HTTPChannel::run 0 1 293 425
/**
 * This must be called from time to time when non-blocking I/O is in use.  It
 * checks for data coming in on the socket and writes data out to the socket
 * when possible, and does whatever processing is required towards completing
 * the current task.
 *
 * The return value is true if the task is still pending (and run() will need
 * to be called again in the future), or false if the current task is
 * complete.
 */
28
bool HTTPChannel::run(void);

690 16 begin_connect_to 0 4 823 29 HTTPChannel::begin_connect_to 0 1 294 536
/**
 * Begins a non-blocking request to establish a direct connection to the
 * server and port indicated by the URL.  No HTTP requests will be issued
 * beyond what is necessary to establish the connection.  When run() has
 * finished, you may call is_connection_ready() to determine if the connection
 * was successfully established.
 *
 * If successful, the connection may then be taken to use for whatever
 * purposes you like by calling get_connection().
 *
 * This establishes a nonblocking I/O socket.  Also see connect_to().
 */
67
inline void HTTPChannel::begin_connect_to(DocumentSpec const &url);

691 14 open_read_body 0 4 823 27 HTTPChannel::open_read_body 0 1 295 569
/**
 * Returns a newly-allocated istream suitable for reading the body of the
 * document.  This may only be called immediately after a call to
 * get_document() or post_form(), or after a call to run() has returned false.
 *
 * Note that, in nonblocking mode, the returned stream may report an early
 * EOF, even before the actual end of file.  When this happens, you should
 * call stream->is_closed() to determine whether you should attempt to read
 * some more later.
 *
 * The user is responsible for passing the returned istream to
 * close_read_body() later.
 */
49
ISocketStream *HTTPChannel::open_read_body(void);

692 15 close_read_body 0 4 823 28 HTTPChannel::close_read_body 0 1 296 243
/**
 * Closes a file opened by a previous call to open_read_body().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */
62
void HTTPChannel::close_read_body(std::istream *stream) const;

693 16 download_to_file 0 4 823 29 HTTPChannel::download_to_file 0 1 297 1345
/**
 * Specifies the name of a file to download the resulting document to.  This
 * should be called immediately after get_document() or begin_get_document()
 * or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the file and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated file.
 * It returns true if the file can be opened for writing, false otherwise, but
 * the contents will not be completely downloaded until run() has returned
 * false.  At this time, it is possible that a communications error will have
 * left a partial file, so is_download_complete() may be called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the file for writing the output.  In this case, the file must
 * already exist and must have at least first_byte bytes in it.  If
 * subdocument_resumes is false, a subdocument will always be downloaded
 * beginning at the first byte of the file.
 */
94
bool HTTPChannel::download_to_file(Filename const &filename, bool subdocument_resumes = true);

694 15 download_to_ram 0 4 823 28 HTTPChannel::download_to_ram 0 1 298 1223
/**
 * Specifies a Ramfile object to download the resulting document to.  This
 * should be called immediately after get_document() or begin_get_document()
 * or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the Ramfile and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated
 * Ramfile.  It returns true if the file can be opened for writing, false
 * otherwise, but the contents will not be completely downloaded until run()
 * has returned false.  At this time, it is possible that a communications
 * error will have left a partial file, so is_download_complete() may be
 * called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the Ramfile for writing the output.  In this case, the Ramfile must
 * already have at least first_byte bytes in it.
 */
85
bool HTTPChannel::download_to_ram(Ramfile *ramfile, bool subdocument_resumes = true);

695 18 download_to_stream 0 4 823 31 HTTPChannel::download_to_stream 0 1 299 1348
/**
 * Specifies the name of an ostream to download the resulting document to.
 * This should be called immediately after get_document() or
 * begin_get_document() or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the file and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated file.
 * It returns true if the file can be opened for writing, false otherwise, but
 * the contents will not be completely downloaded until run() has returned
 * false.  At this time, it is possible that a communications error will have
 * left a partial file, so is_download_complete() may be called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the file for writing the output.  In this case, the file must
 * already exist and must have at least first_byte bytes in it.  If
 * subdocument_resumes is false, a subdocument will always be downloaded
 * beginning at the first byte of the file.
 */
90
bool HTTPChannel::download_to_stream(std::ostream *strm, bool subdocument_resumes = true);

696 14 get_connection 0 4 823 27 HTTPChannel::get_connection 0 1 300 351
/**
 * Returns the connection that was established via a previous call to
 * connect_to() or begin_connect_to(), or NULL if the connection attempt
 * failed or if those methods have not recently been called.
 *
 * This stream has been allocated from the free store.  It is the user's
 * responsibility to delete this pointer when finished with it.
 */
48
SocketStream *HTTPChannel::get_connection(void);

697 20 get_bytes_downloaded 0 4 823 33 HTTPChannel::get_bytes_downloaded 0 1 301 331
/**
 * Returns the number of bytes downloaded during the last (or current)
 * download_to_file() or download_to_ram operation().  This can be used in
 * conjunction with get_file_size() to report the percent complete (but be
 * careful, since get_file_size() may return 0 if the server has not told us
 * the size of the file).
 */
65
inline std::size_t HTTPChannel::get_bytes_downloaded(void) const;

698 19 get_bytes_requested 0 4 823 32 HTTPChannel::get_bytes_requested 0 1 302 658
/**
 * When download throttling is in effect (set_download_throttle() has been set
 * to true) and non-blocking I/O methods (like begin_get_document()) are used,
 * this returns the number of bytes "requested" from the server so far: that
 * is, the theoretical maximum value for get_bytes_downloaded(), if the server
 * has been keeping up with our demand.
 *
 * If this number is less than get_bytes_downloaded(), then the server has not
 * been supplying bytes fast enough to meet our own download throttle rate.
 *
 * When download throttling is not in effect, or when the blocking I/O methods
 * (like get_document(), etc.) are used, this returns 0.
 */
64
inline std::size_t HTTPChannel::get_bytes_requested(void) const;

699 20 is_download_complete 0 4 823 33 HTTPChannel::is_download_complete 0 1 303 600
/**
 * Returns true when a download_to() or download_to_ram() has executed and the
 * file has been fully downloaded.  If this still returns false after
 * processing has completed, there was an error in transmission.
 *
 * Note that simply testing is_download_complete() does not prove that the
 * requested document was successfully retrieved--you might have just
 * downloaded the "404 not found" stub (for instance) that a server would
 * provide in response to some error condition.  You should also check
 * is_valid() to prove that the file you expected has been successfully
 * retrieved.
 */
58
inline bool HTTPChannel::is_download_complete(void) const;

700 14 get_class_type 0 4 823 27 HTTPChannel::get_class_type 0 1 304 0
52
static TypeHandle HTTPChannel::get_class_type(void);

701 11 HTTPChannel 0 260 823 24 HTTPChannel::HTTPChannel 0 1 227 10
/**
 *
 */
63
inline HTTPChannel::HTTPChannel(HTTPChannel const &) = default;

702 12 Decompressor 0 260 826 26 Decompressor::Decompressor 0 2 305 306 10
/**
 *
 */
100
Decompressor::Decompressor(void);
inline Decompressor::Decompressor(Decompressor const &) = default;

703 13 ~Decompressor 0 516 826 27 Decompressor::~Decompressor 0 0 10
/**
 *
 */
34
Decompressor::~Decompressor(void);

704 8 initiate 0 4 826 22 Decompressor::initiate 0 2 307 308 365
/**
 * Begins a background decompression of the named file (whose filename must
 * end in ".pz") to a new file without the .pz extension.  The source file is
 * removed after successful completion.
 */

/**
 * Begins a background decompression from the named source file to the named
 * destination file.  The source file is removed after successful completion.
 */
140
int Decompressor::initiate(Filename const &source_file);
int Decompressor::initiate(Filename const &source_file, Filename const &dest_file);

705 3 run 0 4 826 17 Decompressor::run 0 1 309 227
/**
 * Called each frame to do the next bit of work in the background task.
 * Returns EU_ok if a chunk is completed but there is more to go, or
 * EU_success when we're all done.  Any other return value indicates an error.
 */
28
int Decompressor::run(void);

706 10 decompress 0 4 826 24 Decompressor::decompress 0 2 310 311 275
/**
 * Performs a foreground decompression of the named file; does not return
 * until the decompression is complete.
 */

/**
 * Does an in-memory decompression of the indicated Ramfile.  The decompressed
 * contents are written back into the same Ramfile on completion.
 */
121
bool Decompressor::decompress(Filename const &source_file);
bool Decompressor::decompress(Ramfile &source_and_dest_file);

707 12 get_progress 0 4 826 26 Decompressor::get_progress 0 1 312 78
/**
 * Returns the ratio through the decompression step in the background.
 */
51
PN_stdfloat Decompressor::get_progress(void) const;

708 10 DownloadDb 0 260 829 22 DownloadDb::DownloadDb 0 4 313 314 315 316 170
/**
 * Create a download db with these client and server dbs
 */

/**
 * Create a download db with these client and server dbs
 */

/**
 * Primarily used for testing.
 */
247
DownloadDb::DownloadDb(void);
explicit DownloadDb::DownloadDb(Ramfile &server_file, Filename &client_file);
explicit DownloadDb::DownloadDb(Filename &server_file, Filename &client_file);
inline DownloadDb::DownloadDb(DownloadDb const &) = default;

709 11 ~DownloadDb 0 516 829 23 DownloadDb::~DownloadDb 0 0 10
/**
 *
 */
30
DownloadDb::~DownloadDb(void);

710 6 output 0 4 829 18 DownloadDb::output 0 1 317 10
/**
 *
 */
49
void DownloadDb::output(std::ostream &out) const;

711 5 write 0 4 829 17 DownloadDb::write 0 1 318 10
/**
 *
 */
48
void DownloadDb::write(std::ostream &out) const;

712 17 write_version_map 0 4 829 29 DownloadDb::write_version_map 0 1 319 22
/**
 *
 */

/**
 *
 */
60
void DownloadDb::write_version_map(std::ostream &out) const;

713 15 write_client_db 0 4 829 27 DownloadDb::write_client_db 0 1 320 36
// Write a database file

/**
 *
 */
49
bool DownloadDb::write_client_db(Filename &file);

714 15 write_server_db 0 4 829 27 DownloadDb::write_server_db 0 1 321 10
/**
 *
 */
49
bool DownloadDb::write_server_db(Filename &file);

715 25 get_client_num_multifiles 0 4 829 37 DownloadDb::get_client_num_multifiles 0 1 322 10
/**
 *
 */
61
inline int DownloadDb::get_client_num_multifiles(void) const;

716 25 get_server_num_multifiles 0 4 829 37 DownloadDb::get_server_num_multifiles 0 1 323 10
/**
 *
 */
61
inline int DownloadDb::get_server_num_multifiles(void) const;

717 25 get_client_multifile_name 0 4 829 37 DownloadDb::get_client_multifile_name 0 1 324 10
/**
 *
 */
74
inline std::string DownloadDb::get_client_multifile_name(int index) const;

718 25 get_server_multifile_name 0 4 829 37 DownloadDb::get_server_multifile_name 0 1 325 10
/**
 *
 */
74
inline std::string DownloadDb::get_server_multifile_name(int index) const;

719 25 get_client_multifile_size 0 4 829 37 DownloadDb::get_client_multifile_size 0 1 326 10
/**
 *
 */
75
inline int DownloadDb::get_client_multifile_size(std::string mfname) const;

720 25 set_client_multifile_size 0 4 829 37 DownloadDb::set_client_multifile_size 0 1 327 10
/**
 *
 */
80
inline void DownloadDb::set_client_multifile_size(std::string mfname, int size);

721 31 set_client_multifile_delta_size 0 4 829 43 DownloadDb::set_client_multifile_delta_size 0 1 328 10
/**
 *
 */
85
inline int DownloadDb::set_client_multifile_delta_size(std::string mfname, int size);

722 25 get_server_multifile_size 0 4 829 37 DownloadDb::get_server_multifile_size 0 1 329 10
/**
 *
 */
75
inline int DownloadDb::get_server_multifile_size(std::string mfname) const;

723 25 set_server_multifile_size 0 4 829 37 DownloadDb::set_server_multifile_size 0 1 330 10
/**
 *
 */
80
inline void DownloadDb::set_server_multifile_size(std::string mfname, int size);

724 26 get_client_multifile_phase 0 4 829 38 DownloadDb::get_client_multifile_phase 0 1 331 10
/**
 *
 */
78
inline Phase DownloadDb::get_client_multifile_phase(std::string mfname) const;

725 26 get_server_multifile_phase 0 4 829 38 DownloadDb::get_server_multifile_phase 0 1 332 10
/**
 *
 */
78
inline Phase DownloadDb::get_server_multifile_phase(std::string mfname) const;

726 31 set_client_multifile_incomplete 0 4 829 43 DownloadDb::set_client_multifile_incomplete 0 1 333 10
/**
 *
 */
76
inline void DownloadDb::set_client_multifile_incomplete(std::string mfname);

727 29 set_client_multifile_complete 0 4 829 41 DownloadDb::set_client_multifile_complete 0 1 334 10
/**
 *
 */
74
inline void DownloadDb::set_client_multifile_complete(std::string mfname);

728 33 set_client_multifile_decompressed 0 4 829 45 DownloadDb::set_client_multifile_decompressed 0 1 335 10
/**
 *
 */
78
inline void DownloadDb::set_client_multifile_decompressed(std::string mfname);

729 30 set_client_multifile_extracted 0 4 829 42 DownloadDb::set_client_multifile_extracted 0 1 336 10
/**
 *
 */
75
inline void DownloadDb::set_client_multifile_extracted(std::string mfname);

730 20 get_server_num_files 0 4 829 32 DownloadDb::get_server_num_files 0 1 337 10
/**
 *
 */
70
inline int DownloadDb::get_server_num_files(std::string mfname) const;

731 20 get_server_file_name 0 4 829 32 DownloadDb::get_server_file_name 0 1 338 10
/**
 *
 */
89
inline std::string DownloadDb::get_server_file_name(std::string mfname, int index) const;

732 23 client_multifile_exists 0 4 829 35 DownloadDb::client_multifile_exists 0 1 339 40
// Queries from the Launcher

/**
 *
 */
67
bool DownloadDb::client_multifile_exists(std::string mfname) const;

733 25 client_multifile_complete 0 4 829 37 DownloadDb::client_multifile_complete 0 1 340 147
/**
 * A multifile is complete when it is completely downloaded.  Note: it may
 * already be decompressed or extracted and it is still complete
 */
69
bool DownloadDb::client_multifile_complete(std::string mfname) const;

734 29 client_multifile_decompressed 0 4 829 41 DownloadDb::client_multifile_decompressed 0 1 341 10
/**
 *
 */
73
bool DownloadDb::client_multifile_decompressed(std::string mfname) const;

735 26 client_multifile_extracted 0 4 829 38 DownloadDb::client_multifile_extracted 0 1 342 10
/**
 *
 */
70
bool DownloadDb::client_multifile_extracted(std::string mfname) const;

736 25 get_client_multifile_hash 0 4 829 37 DownloadDb::get_client_multifile_hash 0 1 343 122
// Ask what version (told with the hash) this multifile is

/**
 * Return the hash value of the file we are working on
 */
72
HashVal DownloadDb::get_client_multifile_hash(std::string mfname) const;

737 25 set_client_multifile_hash 0 4 829 37 DownloadDb::set_client_multifile_hash 0 1 344 55
/**
 * Set the hash value of file we are working on
 */
76
void DownloadDb::set_client_multifile_hash(std::string mfname, HashVal val);

738 25 get_server_multifile_hash 0 4 829 37 DownloadDb::get_server_multifile_hash 0 1 345 51
/**
 * Return the hash value of the server file
 */
72
HashVal DownloadDb::get_server_multifile_hash(std::string mfname) const;

739 25 set_server_multifile_hash 0 4 829 37 DownloadDb::set_server_multifile_hash 0 1 346 55
/**
 * Set the hash value of file we are working on
 */
76
void DownloadDb::set_server_multifile_hash(std::string mfname, HashVal val);

740 23 delete_client_multifile 0 4 829 35 DownloadDb::delete_client_multifile 0 1 347 39
// Operations on multifiles

/**
 *
 */
61
void DownloadDb::delete_client_multifile(std::string mfname);

741 20 add_client_multifile 0 4 829 32 DownloadDb::add_client_multifile 0 1 348 10
/**
 *
 */
65
void DownloadDb::add_client_multifile(std::string server_mfname);

742 23 expand_client_multifile 0 4 829 35 DownloadDb::expand_client_multifile 0 1 349 10
/**
 *
 */
61
void DownloadDb::expand_client_multifile(std::string mfname);

743 20 create_new_server_db 0 4 829 32 DownloadDb::create_new_server_db 0 1 350 131
// Server side operations to create multifile records

/**
 * Used on the server side makefiles to create a new clean server db
 */
44
void DownloadDb::create_new_server_db(void);

744 20 server_add_multifile 0 4 829 32 DownloadDb::server_add_multifile 0 1 351 10
/**
 *
 */
93
void DownloadDb::server_add_multifile(std::string mfname, Phase phase, int size, int status);

745 15 server_add_file 0 4 829 27 DownloadDb::server_add_file 0 1 352 10
/**
 *
 */
72
void DownloadDb::server_add_file(std::string mfname, std::string fname);

746 7 read_db 0 4 829 19 DownloadDb::read_db 0 2 353 354 22
/**
 *
 */

/**
 *
 */
148
DownloadDb::Db DownloadDb::read_db(Filename &file, bool want_server_info);
DownloadDb::Db DownloadDb::read_db(Ramfile &file, bool want_server_info);

747 8 write_db 0 4 829 20 DownloadDb::write_db 0 1 355 10
/**
 *
 */
84
bool DownloadDb::write_db(Filename &file, DownloadDb::Db db, bool want_server_info);

748 11 add_version 0 4 829 23 DownloadDb::add_version 0 1 356 176
/**
 * Appends a new version of the file onto the end of the list, or changes the
 * hash associated with a version previously added.
 *
 * Note: version numbers start at 1
 */
85
void DownloadDb::add_version(Filename const &name, HashVal const &hash, int version);

749 18 insert_new_version 0 4 829 30 DownloadDb::insert_new_version 0 1 357 97
/**
 * Inserts a new version 1 copy of the file, sliding all the other versions up
 * by one.
 */
79
void DownloadDb::insert_new_version(Filename const &name, HashVal const &hash);

750 11 has_version 0 4 829 23 DownloadDb::has_version 0 1 358 164
/**
 * Returns true if the indicated file has version information, false
 * otherwise.  Some files recorded in the database may not bother to track
 * versions.
 */
57
bool DownloadDb::has_version(Filename const &name) const;

751 16 get_num_versions 0 4 829 28 DownloadDb::get_num_versions 0 1 359 72
/**
 * Returns the number of versions stored for the indicated file.
 */
61
int DownloadDb::get_num_versions(Filename const &name) const;

752 16 set_num_versions 0 4 829 28 DownloadDb::set_num_versions 0 1 360 146
/**
 * Reduces the number of versions of a particular file stored in the ddb by
 * throwing away all versions higher than the indicated index.
 */
74
void DownloadDb::set_num_versions(Filename const &name, int num_versions);

753 11 get_version 0 4 829 23 DownloadDb::get_version 0 1 361 183
/**
 * Returns the version number of this particular file, determined by looking
 * up the hash generated from the file.  Returns -1 if the version number
 * cannot be determined.
 */
77
int DownloadDb::get_version(Filename const &name, HashVal const &hash) const;

754 8 get_hash 0 4 829 20 DownloadDb::get_hash 0 1 362 95
/**
 * Returns the MD5 hash associated with the indicated version of the indicated
 * file.
 */
77
HashVal const &DownloadDb::get_hash(Filename const &name, int version) const;

755 9 check_crc 0 1 0 9 check_crc 0 1 397 0
43
unsigned long int check_crc(Filename name);

756 11 check_adler 0 1 0 11 check_adler 0 1 398 0
45
unsigned long int check_adler(Filename name);

757 9 Extractor 0 260 831 20 Extractor::Extractor 0 1 363 10
/**
 *
 */
27
Extractor::Extractor(void);

758 10 ~Extractor 0 516 831 21 Extractor::~Extractor 0 0 10
/**
 *
 */
28
Extractor::~Extractor(void);

759 13 set_multifile 0 4 831 24 Extractor::set_multifile 0 1 364 147
/**
 * Specifies the filename of the Multifile that the Extractor will read.
 * Returns true on success, false if the mulifile name is invalid.
 */
62
bool Extractor::set_multifile(Filename const &multifile_name);

760 15 set_extract_dir 0 4 831 26 Extractor::set_extract_dir 0 1 365 192
/**
 * Specifies the directory into which all extracted subfiles will be written.
 * Relative paths of subfiles within the Multifile will be written as relative
 * paths to this directory.
 */
61
void Extractor::set_extract_dir(Filename const &extract_dir);

761 5 reset 0 4 831 16 Extractor::reset 0 1 366 132
/**
 * Interrupts the Extractor in the middle of its business and makes it ready
 * to accept a new list of subfiles to extract.
 */
28
void Extractor::reset(void);

762 15 request_subfile 0 4 831 26 Extractor::request_subfile 0 1 367 145
/**
 * Requests a particular subfile to be extracted when step() or run() is
 * called.  Returns true if the subfile exists, false otherwise.
 */
62
bool Extractor::request_subfile(Filename const &subfile_name);

763 20 request_all_subfiles 0 4 831 31 Extractor::request_all_subfiles 0 1 368 100
/**
 * Requests all subfiles in the Multifile to be extracted.  Returns the number
 * requested.
 */
42
int Extractor::request_all_subfiles(void);

764 4 step 0 4 831 15 Extractor::step 0 1 369 390
/**
 * After all of the requests have been made via request_file() or
 * request_all_subfiles(), call step() repeatedly until it stops returning
 * EU_ok.
 *
 * step() extracts the next small unit of data from the Multifile.  Returns
 * EU_ok if progress is continuing, EU_error_abort if there is a problem, or
 * EU_success when the last piece has been extracted.
 *
 * Also see run().
 */
26
int Extractor::step(void);

765 12 get_progress 0 4 831 23 Extractor::get_progress 0 1 370 66
/**
 * Returns the fraction of the Multifile extracted so far.
 */
48
PN_stdfloat Extractor::get_progress(void) const;

766 3 run 0 4 831 14 Extractor::run 0 1 371 367
/**
 * A convenience function to extract the Multifile all at once, when you don't
 * care about doing it in the background.
 *
 * First, call request_file() or request_all_files() to specify the files you
 * would like to extract, then call run() to do the extraction.  Also see
 * step() for when you would like the extraction to happen as a background
 * task.
 */
26
bool Extractor::run(void);

767 15 MultiplexStream 0 260 832 32 MultiplexStream::MultiplexStream 0 1 372 10
/**
 *
 */
46
inline MultiplexStream::MultiplexStream(void);

768 11 add_ostream 0 4 832 28 MultiplexStream::add_ostream 0 1 373 134
/**
 * Adds the indicated generic ostream to the multiplex output.  The ostream
 * will receive whatever data is sent to the pipe.
 */
87
inline void MultiplexStream::add_ostream(std::ostream *out, bool delete_later = false);

769 14 add_stdio_file 0 4 832 31 MultiplexStream::add_stdio_file 0 1 374 104
/**
 * Adds the given file, previously opened using the C stdio library, to the
 * multiplex output.
 */
78
inline bool MultiplexStream::add_stdio_file(FILE *file, bool close_when_done);

770 19 add_standard_output 0 4 832 36 MultiplexStream::add_standard_output 0 1 375 44
/**
 * Adds the standard output channel.
 */
55
inline void MultiplexStream::add_standard_output(void);

771 8 add_file 0 4 832 25 MultiplexStream::add_file 0 1 376 159
/**
 * Adds the given file to the multiplex output.  The file is opened in append
 * mode with line buffering.  Returns false if the file cannot be opened.
 */
53
inline bool MultiplexStream::add_file(Filename file);

772 16 add_system_debug 0 4 832 33 MultiplexStream::add_system_debug 0 1 377 247
/**
 * Adds the system debug output the the multiplex output.  This may map to a
 * syslog or some such os-specific output system.  It may do nothing on a
 * particular system.
 *
 * Presently, this maps only to OutputDebugString() on Windows.
 */
52
inline void MultiplexStream::add_system_debug(void);

773 5 flush 0 4 832 22 MultiplexStream::flush 0 1 378 62
/**
 * Forces out all output that hasn't yet been written.
 */
41
inline void MultiplexStream::flush(void);

774 16 ~MultiplexStream 0 516 832 33 MultiplexStream::~MultiplexStream 0 0 0
40
MultiplexStream::~MultiplexStream(void);

775 14 get_class_type 0 4 833 31 VirtualFileHTTP::get_class_type 0 1 379 0
56
static TypeHandle VirtualFileHTTP::get_class_type(void);

776 20 VirtualFileMountHTTP 0 260 835 42 VirtualFileMountHTTP::VirtualFileMountHTTP 0 1 380 10
/**
 *
 */
122
explicit VirtualFileMountHTTP::VirtualFileMountHTTP(URLSpec const &root, HTTPClient *http = HTTPClient::get_global_ptr());

777 15 get_http_client 0 4 835 37 VirtualFileMountHTTP::get_http_client 0 1 381 72
/**
 * Returns the HTTPClient object that services this mount point.
 */
69
inline HTTPClient *VirtualFileMountHTTP::get_http_client(void) const;

778 8 get_root 0 4 835 30 VirtualFileMountHTTP::get_root 0 1 382 72
/**
 * Returns the URL that represents the root of this mount point.
 */
65
inline URLSpec const &VirtualFileMountHTTP::get_root(void) const;

779 20 reload_vfs_mount_url 0 4 835 42 VirtualFileMountHTTP::reload_vfs_mount_url 0 1 383 396
/**
 * Reads all of the vfs-mount-url lines in the Config.prc file and replaces
 * the mount settings to match them.
 *
 * This will mount any url's mentioned in the config file, and unmount and
 * unmount any url's no longer mentioned in the config file.  Normally, it is
 * called automatically at startup, and need not be called again, unless you
 * have fiddled with some config settings.
 */
61
static void VirtualFileMountHTTP::reload_vfs_mount_url(void);

780 14 get_class_type 0 4 835 36 VirtualFileMountHTTP::get_class_type 0 1 384 0
61
static TypeHandle VirtualFileMountHTTP::get_class_type(void);

781 7 Patcher 0 260 837 16 Patcher::Patcher 0 3 385 386 387 22
/**
 *
 */

/**
 *
 */
130
Patcher::Patcher(void);
explicit Patcher::Patcher(PointerTo< Buffer > buffer);
inline Patcher::Patcher(Patcher const &) = default;

782 8 ~Patcher 0 518 837 17 Patcher::~Patcher 0 0 10
/**
 *
 */
32
virtual Patcher::~Patcher(void);

783 8 initiate 0 4 837 17 Patcher::initiate 0 1 388 10
/**
 *
 */
57
int Patcher::initiate(Filename &patch, Filename &infile);

784 3 run 0 4 837 12 Patcher::run 0 1 389 10
/**
 *
 */
23
int Patcher::run(void);

785 12 get_progress 0 4 837 21 Patcher::get_progress 0 1 390 10
/**
 *
 */
53
inline PN_stdfloat Patcher::get_progress(void) const;

786 12 StringStream 0 260 838 26 StringStream::StringStream 0 2 391 392 102
/**
 *
 */

/**
 * This version of the constructor preloads the buffer with the indicated
 * data.
 */
86
StringStream::StringStream(PyObject *source);
inline StringStream::StringStream(void);

787 10 clear_data 0 4 838 24 StringStream::clear_data 0 1 393 30
/**
 * Empties the buffer.
 */
43
inline void StringStream::clear_data(void);

788 13 get_data_size 0 4 838 27 StringStream::get_data_size 0 1 394 86
/**
 * Returns the number of characters available to be read from the data stream.
 */
53
inline std::size_t StringStream::get_data_size(void);

789 8 get_data 0 4 838 22 StringStream::get_data 0 1 395 63
/**
 * Returns the contents of the data stream as a string.
 */
39
PyObject *StringStream::get_data(void);

790 8 set_data 0 4 838 22 StringStream::set_data 0 1 396 168
/**
 * Replaces the contents of the data stream.  This implicitly reseeks to 0.
 */

/**
 * Replaces the contents of the data stream.  This implicitly reseeks to 0.
 */
44
void StringStream::set_data(PyObject *data);

791 13 ~StringStream 0 516 838 27 StringStream::~StringStream 0 0 0
34
StringStream::~StringStream(void);

398
1 0 0 6 3 806 0 0 253 /**
 * Receives a datagram over the socket by expecting a little-endian 16-bit
 * byte count as a prefix.  If the socket stream is non-blocking, may return
 * false if the data is not available; otherwise, returns false only if the
 * socket closes.
 */ 2 4 this 3 842  2 dg 1 843  
2 0 0 6 4 806 0 0 0 1 4 this 3 842  
3 0 0 4 5 845 0 0 0 1 4 this 3 842  
4 0 0 4 6 845 0 0 269 /**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */ 2 4 this 3 842  15 tcp_header_size 1 846  
5 0 0 6 7 846 0 0 77 /**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */ 1 4 this 3 847  
6 0 0 6 10 806 0 0 234 /**
 * Transmits the indicated datagram over the socket by prepending it with a
 * little-endian 16-bit byte count.  Does not return until the data is sent or
 * the connection is closed, even if the socket stream is non-blocking.
 */ 2 4 this 3 849  2 dg 1 850  
7 0 0 6 11 806 0 0 0 1 4 this 3 849  
8 0 0 4 12 845 0 0 0 1 4 this 3 849  
9 0 0 4 13 845 0 0 719 /**
 * Enables or disables "collect-tcp" mode.  In this mode, individual TCP
 * packets are not sent immediately, but rather they are collected together
 * and accumulated to be sent periodically as one larger TCP packet.  This
 * cuts down on overhead from the TCP/IP protocol, especially if many small
 * packets need to be sent on the same connection, but it introduces
 * additional latency (since packets must be held before they can be sent).
 *
 * See set_collect_tcp_interval() to specify the interval of time for which to
 * hold packets before sending them.
 *
 * If you enable this mode, you may also need to periodically call
 * consider_flush() to flush the queue if no packets have been sent recently.
 */ 2 4 this 3 849  11 collect_tcp 1 806  
10 0 0 6 14 806 0 0 85 /**
 * Returns the current setting of "collect-tcp" mode.  See set_collect_tcp().
 */ 1 4 this 3 852  
11 0 0 4 15 845 0 0 231 /**
 * Specifies the interval in time, in seconds, for which to hold TCP packets
 * before sending all of the recently received packets at once.  This only has
 * meaning if "collect-tcp" mode is enabled; see set_collect_tcp().
 */ 2 4 this 3 849  8 interval 1 854  
12 0 0 6 16 854 0 0 229 /**
 * Returns the interval in time, in seconds, for which to hold TCP packets
 * before sending all of the recently received packets at once.  This only has
 * meaning if "collect-tcp" mode is enabled; see set_collect_tcp().
 */ 1 4 this 3 852  
13 0 0 4 17 845 0 0 269 /**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */ 2 4 this 3 849  15 tcp_header_size 1 846  
14 0 0 6 18 846 0 0 77 /**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */ 1 4 this 3 852  
15 0 0 6 19 806 0 0 143 /**
 * Sends the most recently queued data if enough time has elapsed.  This only
 * has meaning if set_collect_tcp() has been set to true.
 */ 1 4 this 3 849  
16 0 0 6 20 806 0 0 120 /**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */ 1 4 this 3 849  
17 0 0 6 29 806 0 0 0 1 4 this 3 855  
18 0 0 4 30 845 0 0 0 1 4 this 3 855  
19 0 0 6 31 796 0 0 0 1 4 this 3 855  
20 0 0 6 23 856 0 0 0 1 4 this 3 855  
21 0 0 6 25 842 0 0 0 1 4 this 3 855  
22 0 0 6 38 806 0 0 0 1 4 this 3 857  
23 0 0 4 39 845 0 0 0 1 4 this 3 857  
24 0 0 6 40 806 0 0 120 /**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */ 1 4 this 3 857  
25 0 0 6 34 858 0 0 0 1 4 this 3 857  
26 0 0 6 36 849 0 0 0 1 4 this 3 857  
27 0 0 6 50 806 0 0 0 1 4 this 3 859  
28 0 0 4 51 845 0 0 0 1 4 this 3 859  
29 0 0 4 52 845 0 0 269 /**
 * Sets the header size for datagrams.  At the present, legal values for this
 * are 0, 2, or 4; this specifies the number of bytes to use encode the
 * datagram length at the start of each TCP datagram.  Sender and receiver
 * must independently agree on this.
 */ 2 4 this 3 859  15 tcp_header_size 1 846  
30 0 0 6 53 846 0 0 77 /**
 * Returns the header size for datagrams.  See set_tcp_header_size().
 */ 1 4 this 3 860  
31 0 0 6 54 806 0 0 120 /**
 * Sends the most recently queued data now.  This only has meaning if
 * set_collect_tcp() has been set to true.
 */ 1 4 this 3 859  
32 0 0 6 44 862 0 0 0 1 4 this 3 859  
33 0 0 6 46 842 0 0 0 1 4 this 3 859  
34 0 0 6 48 849 0 0 0 1 4 this 3 859  
35 0 0 7 57 863 495 0 10 /**
 *
 */ 0 
36 0 0 15 57 863 495 0 0 1 6 param0 0 864  
37 0 0 23 57 863 495 0 135 /**
 * Creates a URLSpec by appending a path to the end of the old URLSpec,
 * inserting an intervening forward slash if necessary.
 */ 2 3 url 1 864  4 path 1 865  
38 0 0 23 57 863 495 0 10 /**
 *
 */ 2 3 url 1 868  20 server_name_expected 5 806  
39 0 0 6 58 863 0 0 0 2 4 this 3 863  3 url 1 868  
40 0 0 6 59 806 0 0 0 2 4 this 3 864  5 other 1 864  
41 0 0 6 60 806 0 0 0 2 4 this 3 864  5 other 1 864  
42 0 0 6 61 806 0 0 0 2 4 this 3 864  5 other 1 864  
43 0 0 6 62 846 0 0 158 /**
 * Returns a number less than zero if this URLSpec sorts before the other one,
 * greater than zero if it sorts after, or zero if they are equivalent.
 */ 2 4 this 3 864  5 other 1 864  
44 0 0 6 63 869 0 0 10 /**
 *
 */ 1 4 this 3 864  
45 0 0 6 64 806 0 0 90 /**
 * Returns true if the URL specifies a scheme (e.g.  "http:"), false
 * otherwise.
 */ 1 4 this 3 864  
46 0 0 6 65 806 0 0 124 /**
 * Returns true if the URL specifies an authority (this includes username,
 * server, and/or port), false otherwise.
 */ 1 4 this 3 864  
47 0 0 6 66 806 0 0 94 /**
 * Returns true if the URL specifies a username (and/or password), false
 * otherwise.
 */ 1 4 this 3 864  
48 0 0 6 67 806 0 0 76 /**
 * Returns true if the URL specifies a server name, false otherwise.
 */ 1 4 this 3 864  
49 0 0 6 68 806 0 0 76 /**
 * Returns true if the URL specifies a port number, false otherwise.
 */ 1 4 this 3 864  
50 0 0 6 69 806 0 0 146 /**
 * Returns true if the URL includes a path specification (that is, the
 * particular filename on the server to retrieve), false otherwise.
 */ 1 4 this 3 864  
51 0 0 6 70 806 0 0 83 /**
 * Returns true if the URL includes a query specification, false otherwise.
 */ 1 4 this 3 864  
52 0 0 6 71 868 0 0 97 /**
 * Returns the scheme specified by the URL, or empty string if no scheme is
 * specified.
 */ 1 4 this 3 864  
53 0 0 6 72 868 0 0 149 /**
 * Returns the authority specified by the URL (this includes username, server,
 * and/or port), or empty string if no authority is specified.
 */ 1 4 this 3 864  
54 0 0 6 73 868 0 0 194 /**
 * Returns the username specified by the URL, if any.  This might also include
 * a password, e.g.  "username:password", although putting a password on the
 * URL is probably a bad idea.
 */ 1 4 this 3 864  
55 0 0 6 74 868 0 0 137 /**
 * Returns the server name specified by the URL, if any.  In case of an IPv6
 * address, does not include the enclosing brackets.
 */ 1 4 this 3 864  
56 0 0 6 75 868 0 0 204 /**
 * Returns the port specified by the URL as a string, or the empty string if
 * no port is specified.  Compare this with get_port(), which returns a
 * default port number if no port is specified.
 */ 1 4 this 3 864  
57 0 0 6 76 804 0 0 97 /**
 * Returns the port number specified by the URL, or the default port if not
 * specified.
 */ 1 4 this 3 864  
58 0 0 6 77 868 0 0 298 /**
 * Returns a string consisting of the server name, followed by a colon,
 * followed by the port number.  If the port number is not explicitly given in
 * the URL, this string will include the implicit port number.
 * If the server is an IPv6 address, it will be enclosed in square brackets.
 */ 1 4 this 3 864  
59 0 0 6 78 806 0 0 181 /**
 * Returns true if the port number encoded in this URL is the default port
 * number for the scheme (or if there is no port number), or false if it is a
 * nonstandard port.
 */ 1 4 this 3 864  
60 0 0 6 79 846 0 0 106 /**
 * Returns the default port number for the indicated scheme, or 0 if there is
 * no known default.
 */ 1 6 scheme 1 868  
61 0 0 6 80 868 0 0 81 /**
 * Returns the path specified by the URL, or "/" if no path is specified.
 */ 1 4 this 3 864  
62 0 0 6 81 868 0 0 95 /**
 * Returns the query specified by the URL, or empty string if no query is
 * specified.
 */ 1 4 this 3 864  
63 0 0 6 82 868 0 0 106 /**
 * Returns the path (or "/" if no path is specified), followed by the query if
 * it is specified.
 */ 1 4 this 3 864  
64 0 0 6 83 806 0 0 115 /**
 * Returns true if the URL's scheme specifies an SSL-secured protocol such as
 * https, or false otherwise.
 */ 1 4 this 3 864  
65 0 0 6 84 868 0 0 50 /**
 * Returns the complete URL specification.
 */ 1 4 this 3 864  
66 0 0 4 85 845 0 0 61 /**
 * Replaces the scheme part of the URL specification.
 */ 2 4 this 3 863  6 scheme 1 868  
67 0 0 4 86 845 0 0 114 /**
 * Replaces the authority part of the URL specification.  This includes the
 * username, server, and port.
 */ 2 4 this 3 863  9 authority 1 868  
68 0 0 4 87 845 0 0 63 /**
 * Replaces the username part of the URL specification.
 */ 2 4 this 3 863  8 username 1 868  
69 0 0 4 88 845 0 0 174 /**
 * Replaces the server part of the URL specification.
 * Unlike set_server_and_port, this method does not require IPv6 addresses to
 * be enclosed in square brackets.
 */ 2 4 this 3 863  6 server 1 868  
70 0 0 4 89 845 0 0 59 /**
 * Replaces the port part of the URL specification.
 */ 2 4 this 3 863  4 port 1 868  
71 0 0 4 89 845 0 0 91 /**
 * Replaces the port part of the URL specification, given a numeric port
 * number.
 */ 2 4 this 3 863  4 port 1 804  
72 0 0 4 90 845 0 0 254 /**
 * Replaces the server and port parts of the URL specification simultaneously.
 * The input string should be of the form "server:port", or just "server" to
 * make the port number implicit.
 * Any IPv6 address must be enclosed in square brackets.
 */ 2 4 this 3 863  15 server_and_port 1 868  
73 0 0 4 91 845 0 0 59 /**
 * Replaces the path part of the URL specification.
 */ 2 4 this 3 863  4 path 1 868  
74 0 0 4 92 845 0 0 60 /**
 * Replaces the query part of the URL specification.
 */ 2 4 this 3 863  5 query 1 868  
75 0 0 4 93 845 0 0 191 /**
 * Completely replaces the URL with the indicated string.  If
 * server_name_expected is true, it is a hint that an undecorated URL is
 * probably a server name, not a local filename.
 */ 3 4 this 3 863  3 url 1 868  20 server_name_expected 5 806  
76 0 0 6 95 868 0 0 10 /**
 *
 */ 1 4 this 3 864  
77 0 0 6 96 806 0 0 98 /**
 * Returns false if the URLSpec is valid (not empty), or true if it is an
 * empty string.
 */ 1 4 this 3 864  
78 0 0 6 98 869 0 0 10 /**
 *
 */ 1 4 this 3 864  
79 0 0 6 99 869 0 0 10 /**
 *
 */ 1 4 this 3 864  
80 0 0 6 100 871 0 0 0 2 4 this 3 864  1 n 1 869  
81 0 0 6 101 806 0 0 10 /**
 *
 */ 2 4 this 3 863  2 in 1 856  
82 0 0 4 102 845 0 0 10 /**
 *
 */ 2 4 this 3 864  3 out 1 858  
83 0 0 6 103 868 0 0 313 /**
 * Returns the source string with all "unsafe" characters quoted, making a
 * string suitable for placing in a URL.  Letters, digits, and the underscore,
 * comma, period, and hyphen characters, as well as any included in the safe
 * string, are left alone; all others are converted to hex representation.
 */ 2 6 source 1 868  4 safe 5 868  
84 0 0 6 104 868 0 0 100 /**
 * Behaves like quote() with the additional behavior of replacing spaces with
 * plus signs.
 */ 2 6 source 1 868  4 safe 5 868  
85 0 0 6 105 868 0 0 121 /**
 * Reverses the operation of quote(): converts escaped characters of the form
 * "%xx" to their ascii equivalent.
 */ 1 6 source 1 868  
86 0 0 6 106 868 0 0 169 /**
 * Reverses the operation of quote_plus(): converts escaped characters of the
 * form "%xx" to their ascii equivalent, and also converts plus signs to
 * spaces.
 */ 1 6 source 1 868  
87 0 0 6 94 868 0 0 0 1 4 this 3 864  
88 0 0 6 97 806 0 0 0 1 4 this 3 864  
89 0 0 7 125 872 497 0 0 0 
90 0 0 15 125 872 497 0 0 1 6 param0 0 873  
91 0 0 7 128 875 515 0 10 /**
 *
 */ 0 
92 0 0 15 128 875 515 0 10 /**
 *
 */ 1 4 copy 1 876  
93 0 0 23 128 875 515 0 122 /**
 * Decodes the string into a sensible date.  Returns 0 (!is_valid()) if the
 * string cannot be correctly decoded.
 */ 1 6 format 1 868  
94 0 0 23 128 875 515 0 10 /**
 *
 */ 1 4 time 1 877  
95 0 0 6 129 875 0 0 0 2 4 this 3 875  4 copy 1 876  
96 0 0 7 130 875 515 0 73 /**
 * Returns an HTTPDate that represents the current time and date.
 */ 0 
97 0 0 6 131 806 0 0 141 /**
 * Returns true if the date is meaningful, or false if it is -1 (which
 * generally indicates the source string could not be parsed.)
 */ 1 4 this 3 876  
98 0 0 6 132 868 0 0 10 /**
 *
 */ 1 4 this 3 876  
99 0 0 6 133 877 0 0 48 /**
 * Returns the date as a C time_t value.
 */ 1 4 this 3 876  
100 0 0 6 134 806 0 0 0 2 4 this 3 876  5 other 1 876  
101 0 0 6 135 806 0 0 0 2 4 this 3 876  5 other 1 876  
102 0 0 6 136 806 0 0 0 2 4 this 3 876  5 other 1 876  
103 0 0 6 137 806 0 0 0 2 4 this 3 876  5 other 1 876  
104 0 0 6 138 846 0 0 159 /**
 * Returns a number less than zero if this HTTPDate sorts before the other
 * one, greater than zero if it sorts after, or zero if they are equivalent.
 */ 2 4 this 3 876  5 other 1 876  
105 0 0 6 139 875 0 0 0 2 4 this 3 875  7 seconds 1 846  
106 0 0 6 140 875 0 0 0 2 4 this 3 875  7 seconds 1 846  
107 0 0 7 141 875 515 0 0 2 4 this 3 876  7 seconds 1 846  
108 0 0 6 142 846 0 0 0 2 4 this 3 876  5 other 1 876  
109 0 0 7 142 875 515 0 0 2 4 this 3 876  7 seconds 1 846  
110 0 0 6 143 806 0 0 10 /**
 *
 */ 2 4 this 3 875  2 in 1 856  
111 0 0 4 144 845 0 0 10 /**
 *
 */ 2 4 this 3 876  3 out 1 858  
112 0 0 7 147 879 517 0 38 /**
 * Constructs an empty cookie.
 */ 0 
113 0 0 15 147 879 517 0 0 1 6 param0 0 880  
114 0 0 7 147 879 517 0 197 /**
 * Constructs a cookie according to the indicated string, presumably the tag
 * of a Set-Cookie header.  There is no way to detect a formatting error in
 * the string with this constructor.
 */ 2 6 format 1 868  3 url 1 864  
115 0 0 7 147 879 517 0 179 /**
 * Constructs a cookie with the indicated name, path, and domain values, but
 * no other data.  This is most useful for looking up an existing cookie in
 * the HTTPClient.
 */ 3 4 name 1 868  4 path 1 868  6 domain 1 868  
116 0 0 4 149 845 0 0 10 /**
 *
 */ 2 4 this 3 879  4 name 1 868  
117 0 0 6 150 868 0 0 93 /**
 * Returns the name of the cookie.  This is the key value specified by the
 * server.
 */ 1 4 this 3 880  
118 0 0 4 151 845 0 0 10 /**
 *
 */ 2 4 this 3 879  5 value 1 868  
119 0 0 6 152 868 0 0 139 /**
 * Returns the value of the cookie.  This is the arbitrary string associated
 * with the cookie's name, as specified by the server.
 */ 1 4 this 3 880  
120 0 0 4 153 845 0 0 10 /**
 *
 */ 2 4 this 3 879  6 domain 1 868  
121 0 0 6 154 868 0 0 10 /**
 *
 */ 1 4 this 3 880  
122 0 0 4 155 845 0 0 10 /**
 *
 */ 2 4 this 3 879  4 path 1 868  
123 0 0 6 156 868 0 0 99 /**
 * Returns the prefix of the URL paths on the server for which this cookie
 * will be sent.
 */ 1 4 this 3 880  
124 0 0 4 157 845 0 0 10 /**
 *
 */ 2 4 this 3 879  7 expires 1 876  
125 0 0 4 158 845 0 0 53 /**
 * Removes the expiration date on the cookie.
 */ 1 4 this 3 879  
126 0 0 6 159 806 0 0 78 /**
 * Returns true if the cookie has an expiration date, false otherwise.
 */ 1 4 this 3 880  
127 0 0 7 160 875 515 0 102 /**
 * Returns the expiration date of the cookie if it is set, or an invalid date
 * if it is not.
 */ 1 4 this 3 880  
128 0 0 4 161 845 0 0 10 /**
 *
 */ 2 4 this 3 879  4 flag 1 806  
129 0 0 6 162 806 0 0 129 /**
 * Returns true if the server has indicated this is a "secure" cookie which
 * should only be sent over an HTTPS channel.
 */ 1 4 this 3 880  
130 0 0 6 163 806 0 0 0 2 4 this 3 880  5 other 1 880  
131 0 0 4 164 845 0 0 358 /**
 * Assuming the operator < method, above, has already evaluated these two
 * cookies as equal, then assign the remaining values (value, expiration date,
 * secure flag) from the indicated cookie.  This is guaranteed not to change
 * the ordering of the cookie in a set, and so can be used to update an
 * existing cookie within a set with new values.
 */ 2 4 this 3 879  5 other 1 880  
132 0 0 6 165 806 0 0 218 /**
 * Separates out the parameter/value pairs of the Set-Cookie header and
 * assigns the values of the cookie appropriate.  Returns true if the header
 * is parsed correctly, false if something is not understood.
 */ 3 4 this 3 879  6 format 1 868  3 url 1 864  
133 0 0 6 166 806 0 0 105 /**
 * Returns true if the cookie's expiration date is before the indicated date,
 * false otherwise.
 */ 2 4 this 3 880  3 now 5 876  
134 0 0 6 167 806 0 0 112 /**
 * Returns true if the cookie is appropriate to send with the indicated URL
 * request, false otherwise.
 */ 2 4 this 3 880  3 url 1 864  
135 0 0 4 168 845 0 0 10 /**
 *
 */ 2 4 this 3 880  3 out 1 858  
136 0 0 7 178 882 0 0 10 /**
 *
 */ 0 
137 0 0 15 178 882 0 0 10 /**
 *
 */ 1 4 copy 1 883  
138 0 0 7 179 882 0 0 0 2 4 this 3 882  4 copy 1 883  
139 0 0 4 180 845 0 0 561 /**
 * This may be called once, presumably at the beginning of an application, to
 * initialize OpenSSL's random seed.  On Windows, it is particularly important
 * to call this at startup if you are going to be performing any https
 * operations or otherwise use encryption, since the Windows algorithm for
 * getting a random seed takes 2-3 seconds at startup, but can take 30 seconds
 * or more after you have opened a 3-D graphics window and started rendering.
 *
 * There is no harm in calling this method multiple times, or in not calling
 * it at all.
 */ 0 
140 0 0 4 181 845 0 0 511 /**
 * Specifies the complete set of proxies to use for all schemes.  This is
 * either a semicolon-delimited set of hostname:ports, or a semicolon-
 * delimited set of pairs of the form "scheme=hostname:port", or a
 * combination.  Use the keyword DIRECT, or an empty string, to represent a
 * direct connection.  A particular scheme and/or proxy host may be listed
 * more than once.  This is a convenience function that can be used in place
 * of explicit calls to add_proxy() for each scheme/proxy pair.
 */ 2 4 this 3 882  10 proxy_spec 1 868  
141 0 0 6 182 868 0 0 371 /**
 * Returns the complete set of proxies to use for all schemes.  This is a
 * string of the form specified by set_proxy_spec(), above.  Note that the
 * string returned by this function may not be exactly the same as the string
 * passed into set_proxy_spec(), since the string is regenerated from the
 * internal storage structures and may therefore be reordered.
 */ 1 4 this 3 883  
142 0 0 4 183 845 0 0 197 /**
 * Specifies the set of hosts that should be connected to directly, without
 * using a proxy.  This is a semicolon-separated list of hostnames that may
 * contain wildcard characters ("*").
 */ 2 4 this 3 882  16 direct_host_spec 1 868  
143 0 0 6 184 868 0 0 189 /**
 * Returns the set of hosts that should be connected to directly, without
 * using a proxy, as a semicolon-separated list of hostnames that may contain
 * wildcard characters ("*").
 */ 1 4 this 3 883  
144 0 0 4 185 845 0 0 306 /**
 * If this is set true, then after a connection attempt through a proxy fails,
 * we always try a direct connection, regardless of whether the host is listed
 * on the direct_host_spec list.  If this is false, a direct attempt is not
 * made when we have a proxy in effect, even if the proxy fails.
 */ 2 4 this 3 882  14 try_all_direct 1 806  
145 0 0 6 186 806 0 0 134 /**
 * Returns whether a failed connection through a proxy will be followed up by
 * a direct connection attempt, false otherwise.
 */ 1 4 this 3 883  
146 0 0 4 187 845 0 0 128 /**
 * Resets the proxy spec to empty.  Subsequent calls to add_proxy() may be
 * made to build up the set of proxy servers.
 */ 1 4 this 3 882  
147 0 0 4 188 845 0 0 271 /**
 * Adds the indicated proxy host as a proxy for communications on the given
 * scheme.  Usually the scheme is "http" or "https".  It may be the empty
 * string to indicate a general proxy.  The proxy string may be the empty URL
 * to indicate a direct connection.
 */ 3 4 this 3 882  6 scheme 1 868  5 proxy 1 864  
148 0 0 4 189 845 0 0 178 /**
 * Resets the set of direct hosts to empty.  Subsequent calls to
 * add_direct_host() may be made to build up the list of hosts that do not
 * require a proxy connection.
 */ 1 4 this 3 882  
149 0 0 4 190 845 0 0 223 /**
 * Adds the indicated name to the set of hostnames that are connected to
 * directly, without using a proxy.  This name may be either a DNS name or an
 * IP address, and it may include the * as a wildcard character.
 */ 2 4 this 3 882  8 hostname 1 868  
150 0 0 6 191 868 0 0 218 /**
 * Returns a semicolon-delimited list of proxies, in the order in which they
 * should be tried, that are appropriate for the indicated URL.  The keyword
 * DIRECT indicates a direct connection should be tried.
 */ 2 4 this 3 883  3 url 1 864  
151 0 0 4 192 845 0 0 433 /**
 * Specifies the username:password string corresponding to a particular server
 * and/or realm, when demanded by the server.  Either or both of the server or
 * realm may be empty; if so, they match anything.  Also, the server may be
 * set to the special string `"*proxy"`, which will match any proxy server.
 *
 * If the username is set to the empty string, this clears the password for
 * the particular server/realm pair.
 */ 4 4 this 3 882  6 server 1 868  5 realm 1 868  8 username 1 868  
152 0 0 6 193 868 0 0 144 /**
 * Returns the username:password string set for this server/realm pair, or
 * empty string if nothing has been set.  See set_username().
 */ 3 4 this 3 883  6 server 1 868  5 realm 1 868  
153 0 0 4 194 845 0 0 116 /**
 * Stores the indicated cookie in the client's list of cookies, as if it had
 * been received from a server.
 */ 2 4 this 3 882  6 cookie 1 880  
154 0 0 6 195 806 0 0 175 /**
 * Removes the cookie with the matching domain/path/name from the client's
 * list of cookies.  Returns true if it was removed, false if the cookie was
 * not matched.
 */ 2 4 this 3 882  6 cookie 1 880  
155 0 0 4 196 845 0 0 58 /**
 * Removes the all stored cookies from the client.
 */ 1 4 this 3 882  
156 0 0 6 197 806 0 0 124 /**
 * Returns true if there is a cookie in the client matching the given cookie's
 * domain/path/name, false otherwise.
 */ 2 4 this 3 883  6 cookie 1 880  
157 0 0 7 198 879 517 0 163 /**
 * Looks up and returns the cookie in the client matching the given cookie's
 * domain/path/name.  If there is no matching cookie, returns an empty cookie.
 */ 2 4 this 3 883  6 cookie 1 880  
158 0 0 4 199 845 0 0 179 /**
 * Copies all the cookies from the indicated HTTPClient into this one.
 * Existing cookies in this client are not affected, unless they are shadowed
 * by the new cookies.
 */ 2 4 this 3 882  5 other 1 883  
159 0 0 4 200 845 0 0 172 /**
 * Outputs the complete list of cookies stored on the client, for all domains,
 * including the expired cookies (which will normally not be sent back to a
 * host).
 */ 2 4 this 3 883  3 out 1 858  
160 0 0 4 201 845 0 0 192 /**
 * Writes to the indicated ostream a "Cookie" header line for sending the
 * cookies appropriate to the indicated URL along with an HTTP request.  This
 * also removes expired cookies.
 */ 3 4 this 3 882  3 out 1 858  3 url 1 864  
161 0 0 4 202 845 0 0 250 /**
 * Sets the filename of the pem-formatted file that will be read for the
 * client public and private keys if an SSL server requests a certificate.
 * Either this or set_client_certificate_pem() may be used to specify a client
 * certificate.
 */ 2 4 this 3 882  8 filename 1 865  
162 0 0 4 203 845 0 0 264 /**
 * Sets the pem-formatted contents of the certificate that will be parsed for
 * the client public and private keys if an SSL server requests a certificate.
 * Either this or set_client_certificate_filename() may be used to specify a
 * client certificate.
 */ 2 4 this 3 882  3 pem 1 868  
163 0 0 4 204 845 0 0 160 /**
 * Sets the passphrase used to decrypt the private key in the certificate
 * named by set_client_certificate_filename() or set_client_certificate_pem().
 */ 2 4 this 3 882  10 passphrase 1 868  
164 0 0 6 205 806 0 0 376 /**
 * Attempts to load the certificate named by set_client_certificate_filename()
 * immediately, and returns true if successful, false otherwise.
 *
 * Normally this need not be explicitly called, since it will be called
 * automatically if the server requests a certificate, but it may be useful to
 * determine ahead of time if the certificate can be loaded correctly.
 */ 1 4 this 3 882  
165 0 0 6 206 806 0 0 547 /**
 * Adds the certificate defined in the indicated PEM filename as a "pre-
 * approved" certificate for the indicated server, defined by the hostname and
 * port (only) from the given URL.
 *
 * If the server offers this particular certificate on a secure connection, it
 * will be accepted without question.  This is particularly useful for
 * communicating with a server using a known self-signed certificate.
 *
 * See also the similar add_preapproved_server_certificate_pem(), and the
 * weaker add_preapproved_server_certificate_name().
 */ 3 4 this 3 882  3 url 1 864  8 filename 1 865  
166 0 0 6 207 806 0 0 577 /**
 * Adds the certificate defined in the indicated data string, formatted as a
 * PEM block, as a "pre-approved" certificate for the indicated server,
 * defined by the hostname and port (only) from the given URL.
 *
 * If the server offers this particular certificate on a secure connection, it
 * will be accepted without question.  This is particularly useful for
 * communicating with a server using a known self-signed certificate.
 *
 * See also the similar add_preapproved_server_certificate_filename(), and the
 * weaker add_preapproved_server_certificate_name().
 */ 3 4 this 3 882  3 url 1 864  3 pem 1 868  
167 0 0 6 208 806 0 0 888 /**
 * Adds the certificate *name* only, as a "pre-approved" certificate name for
 * the indicated server, defined by the hostname and port (only) from the
 * given URL.
 *
 * This is a weaker function than
 * add_preapproved_server_certificate_filename().  This checks only the
 * subject name of the certificate, without checking for a particular
 * certificate by key.  This means that a variety of server certificates may
 * match the indicated name.
 *
 * Because this is a weaker verification, it only applies to server
 * certificates that are signed by a recognized certificate authority.  Thus,
 * it cannot be used to pre-approve self-signed certificates, but it can be
 * used to accept a server certificate offered by a different hostname than
 * the one in the cert itself.
 *
 * The certificate name should be formatted in the form
 * type0=value0/type1=value1/type2=...
 */ 3 4 this 3 882  3 url 1 864  4 name 1 868  
168 0 0 4 209 845 0 0 92 /**
 * Removes all preapproved server certificates for the indicated server and
 * port.
 */ 2 4 this 3 882  3 url 1 864  
169 0 0 4 210 845 0 0 71 /**
 * Removes all preapproved server certificates for all servers.
 */ 1 4 this 3 882  
170 0 0 4 211 845 0 0 221 /**
 * Specifies the version of HTTP that the client uses to identify itself to
 * the server.  The default is HV_11, or HTTP 1.0; you can set this to HV_10
 * (HTTP 1.0) to request the server use the older interface.
 */ 2 4 this 3 882  7 version 1 808  
171 0 0 6 212 808 0 0 93 /**
 * Returns the client's current setting for HTTP version.  See
 * set_http_version().
 */ 1 4 this 3 883  
172 0 0 6 213 868 0 0 99 /**
 * Returns the current HTTP version setting as a string, e.g.  "HTTP/1.0" or
 * "HTTP/1.1".
 */ 1 4 this 3 883  
173 0 0 6 214 808 0 0 193 /**
 * Matches the string representing a particular HTTP version against any of
 * the known versions and returns the appropriate enumerated value, or
 * HV_other if the version is unknown.
 */ 1 7 version 1 868  
174 0 0 6 215 806 0 0 263 /**
 * Reads the certificate(s) (delimited by -----BEGIN CERTIFICATE----- and
 * -----END CERTIFICATE-----) from the indicated file and makes them known as
 * trusted public keys for validating future connections.  Returns true on
 * success, false otherwise.
 */ 2 4 this 3 882  8 filename 1 865  
175 0 0 4 217 845 0 0 263 /**
 * Specifies whether the client will insist on verifying the identity of the
 * servers it connects to via SSL (that is, https).
 *
 * The parameter value is an enumerated type which indicates the level of
 * security to which the client will insist upon.
 */ 2 4 this 3 882  10 verify_ssl 1 815  
176 0 0 6 218 815 0 0 157 /**
 * Returns whether the client will insist on verifying the identity of the
 * servers it connects to via SSL (that is, https).  See set_verify_ssl().
 */ 1 4 this 3 883  
177 0 0 4 219 845 0 0 404 /**
 * Specifies the set of ciphers that are to be made available for SSL
 * connections.  This is a string as described in the ciphers(1) man page of
 * the OpenSSL documentation (or see
 * https://www.openssl.org/docs/man1.1.1/man1/ciphers.html ).  If this isn't
 * specified, the default is provided by the Config file.  You may also specify
 * "DEFAULT" to use the built-in OpenSSL default value.
 */ 2 4 this 3 882  11 cipher_list 1 868  
178 0 0 6 220 868 0 0 93 /**
 * Returns the set of ciphers as set by set_cipher_list().  See
 * set_cipher_list().
 */ 1 4 this 3 883  
179 0 0 7 221 885 0 0 763 /**
 * Returns a new HTTPChannel object that may be used for reading multiple
 * documents using the same connection, for greater network efficiency than
 * calling HTTPClient::get_document() repeatedly (which would force a new
 * connection for each document).
 *
 * Also, HTTPChannel has some additional, less common interface methods than
 * the basic interface methods that exist on HTTPClient; if you wish to call
 * any of these methods you must first obtain an HTTPChannel.
 *
 * Pass true for persistent_connection to gain this network efficiency.  If,
 * on the other hand, your intention is to use the channel to retrieve only
 * one document, then pass false to inform the server that we will be dropping
 * the connection after the first document.
 */ 2 4 this 3 882  21 persistent_connection 1 806  
180 0 0 7 222 885 0 0 262 /**
 * Posts form data to a particular URL and retrieves the response.  Returns a
 * new HTTPChannel object whether the document is successfully read or not;
 * you can test is_valid() and get_return_code() to determine whether the
 * document was retrieved.
 */ 3 4 this 3 882  3 url 1 864  4 body 1 868  
181 0 0 7 223 885 0 0 233 /**
 * Opens the named document for reading.  Returns a new HTTPChannel object
 * whether the document is successfully read or not; you can test is_valid()
 * and get_return_code() to determine whether the document was retrieved.
 */ 2 4 this 3 882  3 url 1 864  
182 0 0 7 224 885 0 0 252 /**
 * Like get_document(), except only the header associated with the document is
 * retrieved.  This may be used to test for existence of the document; it
 * might also return the size of the document (if the server gives us this
 * information).
 */ 2 4 this 3 882  3 url 1 864  
183 0 0 6 225 868 0 0 222 /**
 * Implements HTTPAuthorization::base64_encode().  This is provided here just
 * as a convenient place to publish it for access by the scripting language;
 * C++ code should probably use HTTPAuthorization directly.
 */ 1 1 s 1 868  
184 0 0 6 226 868 0 0 222 /**
 * Implements HTTPAuthorization::base64_decode().  This is provided here just
 * as a convenient place to publish it for access by the scripting language;
 * C++ code should probably use HTTPAuthorization directly.
 */ 1 1 s 1 868  
185 0 0 7 227 882 0 0 49 /**
 * Returns the default global HTTPClient.
 */ 0 
186 0 0 7 229 886 599 0 10 /**
 *
 */ 0 
187 0 0 15 229 886 599 0 10 /**
 *
 */ 1 4 copy 1 887  
188 0 0 23 229 886 599 0 99 /**
 * This constructor accepts an explicit weak flag and a literal (not quoted)
 * tag string.
 */ 2 4 weak 1 806  3 tag 1 868  
189 0 0 23 229 886 599 0 134 /**
 * This constructor accepts a string as formatted from an HTTP server (e.g.
 * the tag is quoted, with an optional W/ prefix.)
 */ 1 4 text 1 868  
190 0 0 6 230 886 0 0 0 2 4 this 3 886  4 copy 1 887  
191 0 0 6 231 806 0 0 255 /**
 * Returns true if the entity tag is marked as "weak". A consistent weak
 * entity tag does not guarantee that its resource has not changed in any way,
 * but it does promise that the resource has not changed in any semantically
 * meaningful way.
 */ 1 4 this 3 887  
192 0 0 6 232 868 0 0 47 /**
 * Returns the tag as a literal string.
 */ 1 4 this 3 887  
193 0 0 6 233 868 0 0 126 /**
 * Returns the entity tag formatted for sending to an HTTP server (the tag is
 * quoted, with a conditional W prefix).
 */ 1 4 this 3 887  
194 0 0 6 234 806 0 0 115 /**
 * Returns true if the two tags have "strong" equivalence: they are the same
 * tag, and both are "strong".
 */ 2 4 this 3 887  5 other 1 887  
195 0 0 6 235 806 0 0 121 /**
 * Returns true if the two tags have "weak" equivalence: they are the same
 * tag, and one or both may be "weak".
 */ 2 4 this 3 887  5 other 1 887  
196 0 0 6 236 806 0 0 0 2 4 this 3 887  5 other 1 887  
197 0 0 6 237 806 0 0 0 2 4 this 3 887  5 other 1 887  
198 0 0 6 238 806 0 0 0 2 4 this 3 887  5 other 1 887  
199 0 0 6 239 846 0 0 167 /**
 * Returns a number less than zero if this HTTPEntityTag sorts before the
 * other one, greater than zero if it sorts after, or zero if they are
 * equivalent.
 */ 2 4 this 3 887  5 other 1 887  
200 0 0 4 240 845 0 0 10 /**
 *
 */ 2 4 this 3 887  3 out 1 858  
201 0 0 7 243 888 623 0 10 /**
 *
 */ 0 
202 0 0 15 243 888 623 0 10 /**
 *
 */ 1 4 copy 1 889  
203 0 0 23 243 888 623 0 10 /**
 *
 */ 1 3 url 1 864  
204 0 0 23 243 888 623 0 10 /**
 *
 */ 1 3 url 1 868  
205 0 0 6 244 888 0 0 0 2 4 this 3 888  4 copy 1 889  
206 0 0 6 245 806 0 0 0 2 4 this 3 889  5 other 1 889  
207 0 0 6 246 806 0 0 0 2 4 this 3 889  5 other 1 889  
208 0 0 6 247 806 0 0 0 2 4 this 3 889  5 other 1 889  
209 0 0 6 248 846 0 0 10 /**
 *
 */ 2 4 this 3 889  5 other 1 889  
210 0 0 4 249 845 0 0 311 /**
 * Changes the URL of the DocumentSpec without modifying its other properties.
 * Normally this would be a strange thing to do, because the tag and date are
 * usually strongly associated with the URL.  To get a DocumentSpec pointing
 * to a new URL, you would normally create a new DocumentSpec object.
 */ 2 4 this 3 888  3 url 1 864  
211 0 0 6 250 864 0 0 49 /**
 * Retrieves the URL of the DocumentSpec.
 */ 1 4 this 3 889  
212 0 0 4 251 845 0 0 69 /**
 * Changes the identity tag associated with the DocumentSpec.
 */ 2 4 this 3 888  3 tag 1 887  
213 0 0 6 252 806 0 0 79 /**
 * Returns true if an identity tag is associated with the DocumentSpec.
 */ 1 4 this 3 889  
214 0 0 6 253 887 0 0 254 /**
 * Returns the identity tag associated with the DocumentSpec, if there is one.
 * It is an error to call this if has_tag() returns false.
 *
 * The identity tag is set by the HTTP server to uniquely refer to a
 * particular version of a document.
 */ 1 4 this 3 889  
215 0 0 4 254 845 0 0 86 /**
 * Removes the identity tag associated with the DocumentSpec, if there is one.
 */ 1 4 this 3 888  
216 0 0 4 255 845 0 0 75 /**
 * Changes the last-modified date associated with the DocumentSpec.
 */ 2 4 this 3 888  4 date 1 876  
217 0 0 6 256 806 0 0 84 /**
 * Returns true if a last-modified date is associated with the DocumentSpec.
 */ 1 4 this 3 889  
218 0 0 6 257 876 0 0 153 /**
 * Returns the last-modified date associated with the DocumentSpec, if there
 * is one.  It is an error to call this if has_date() returns false.
 */ 1 4 this 3 889  
219 0 0 4 258 845 0 0 95 /**
 * Removes the last-modified date associated with the DocumentSpec, if there
 * is one.
 */ 1 4 this 3 888  
220 0 0 4 260 845 0 0 1335 /**
 * Sets the request mode of this DocumentSpec.  This is only relevant when
 * using the DocumentSpec to generate a request (for instance, in
 * HTTPChannel).  This specifies whether the document request will ask the
 * server for a newer version than the indicated version, or the exact
 * version, neither, or either.
 *
 * The possible values are:
 *
 * RM_any: ignore date and tag (if specified), and retrieve any document that
 * matches the URL.  For a subrange request, if the document matches the
 * version indicated exactly, retrieve the subrange only; otherwise, retrieve
 * the entire document.
 *
 * RM_equal: request only the precise version of the document that matches the
 * particular date and/or tag exactly, if specified; fail if this version is
 * not available.
 *
 * RM_newer: request any document that is newer than the version indicated by
 * the particular date and/or tag; fail if only that version (or older
 * versions) are available.
 *
 * RM_newer_or_equal: request any document that matches the version indicated
 * by the particular date and/or tag, or is a newer version; fail if only
 * older versions are available.
 *
 * In any of the above, you may specify either or both of the last-modified
 * date and the identity tag, whichever is known to the client.
 *
 * The default mode is RM_any.
 */ 2 4 this 3 888  12 request_mode 1 818  
221 0 0 6 261 818 0 0 82 /**
 * Returns the request mode of this DocumentSpec.  See set_request_mode().
 */ 1 4 this 3 889  
222 0 0 4 263 845 0 0 642 /**
 * Specifies what kind of cached value is acceptable for this document.
 * Warning: some HTTP proxies may not respect this setting and may return a
 * cached result anyway.
 *
 * CC_allow_cache: the normal HTTP behavior; the server may return a cached
 * value if it believes it is valid.
 *
 * CC_revalidate: a proxy is forced to contact the origin server and verify
 * that is cached value is in fact still valid before it returns it.
 *
 * CC_no_cache: a proxy must not return its cached value at all, but is forced
 * to go all the way back to the origin server for the official document.
 *
 * The default mode is CC_allow_cache.
 */ 2 4 this 3 888  13 cache_control 1 819  
223 0 0 6 264 819 0 0 83 /**
 * Returns the request mode of this DocumentSpec.  See set_cache_control().
 */ 1 4 this 3 889  
224 0 0 6 265 806 0 0 152 /**
 * Can be used to read in the DocumentSpec from a stream generated either by
 * output() or write().  Returns true on success, false on failure.
 */ 2 4 this 3 888  2 in 1 856  
225 0 0 4 266 845 0 0 10 /**
 *
 */ 2 4 this 3 889  3 out 1 858  
226 0 0 4 267 845 0 0 10 /**
 *
 */ 3 4 this 3 889  3 out 1 858  12 indent_level 5 846  
227 0 0 15 358 885 0 0 0 1 6 param0 0 891  
228 0 0 7 280 882 0 0 64 /**
 * Returns the HTTPClient object that owns this channel.
 */ 1 4 this 3 891  
229 0 0 6 281 806 0 0 126 /**
 * Returns true if the last-requested document was successfully retrieved and
 * is ready to be read, false otherwise.
 */ 1 4 this 3 891  
230 0 0 6 282 806 0 0 158 /**
 * Returns true if a connection has been established to the named server in a
 * previous call to connect_to() or begin_connect_to(), false otherwise.
 */ 1 4 this 3 891  
231 0 0 6 283 864 0 0 287 /**
 * Returns the URL that was used to retrieve the most recent document:
 * whatever URL was last passed to get_document() or get_header().  If a
 * redirect has transparently occurred, this will return the new, redirected
 * URL (the actual URL at which the document was located).
 */ 1 4 this 3 891  
232 0 0 6 284 889 0 0 380 /**
 * Returns the DocumentSpec associated with the most recent document.  This
 * includes its actual URL (following redirects) along with the identity tag
 * and last-modified date, if supplied by the server.
 *
 * This structure may be saved and used to retrieve the same version of the
 * document later, or to conditionally retrieve a newer version if it is
 * available.
 */ 1 4 this 3 891  
233 0 0 6 285 808 0 0 137 /**
 * Returns the HTTP version number returned by the server, as one of the
 * HTTPClient enumerated types, e.g.  HTTPClient::HV_11.
 */ 1 4 this 3 891  
234 0 0 6 286 868 0 0 110 /**
 * Returns the HTTP version number returned by the server, formatted as a
 * string, e.g.  "HTTP/1.1".
 */ 1 4 this 3 891  
235 0 0 6 287 846 0 0 501 /**
 * Returns the HTML return code from the document retrieval request.  This
 * will be in the 200 range if the document is successfully retrieved, or some
 * other value in the case of an error.
 *
 * Some proxy errors during an https-over-proxy request would return the same
 * status code as a different error that occurred on the host server.  To
 * differentiate these cases, status codes that are returned by the proxy
 * during the CONNECT phase (except code 407) are incremented by 1000.
 */ 1 4 this 3 891  
236 0 0 6 288 868 0 0 133 /**
 * Returns the string as returned by the server describing the status code for
 * humans.  This may or may not be meaningful.
 */ 1 4 this 3 891  
237 0 0 6 289 868 0 0 363 /**
 * If the document failed to connect because of a 401 (Authorization
 * required), this method will return the "realm" returned by the server in
 * which the requested document must be authenticated.  This string may be
 * presented to the user to request an associated username and password (which
 * then should be stored in HTTPClient::set_username()).
 */ 1 4 this 3 891  
238 0 0 6 290 868 0 0 311 /**
 * If the document failed to connect because of a 407 (Proxy authorization
 * required), this method will return the "realm" returned by the proxy.  This
 * string may be presented to the user to request an associated username and
 * password (which then should be stored in HTTPClient::set_username()).
 */ 1 4 this 3 891  
239 0 0 6 291 864 0 0 366 /**
 * If the document failed with a redirect code (300 series), this will
 * generally contain the new URL the server wants us to try.  In many cases,
 * the client will automatically follow redirects; if these are successful the
 * client will return a successful code and get_redirect() will return empty,
 * but get_url() will return the new, redirected URL.
 */ 1 4 this 3 891  
240 0 0 6 292 868 0 0 160 /**
 * Returns the HTML header value associated with the indicated key, or empty
 * string if the key was not defined in the message returned by the server.
 */ 2 4 this 3 891  3 key 1 868  
241 0 0 6 293 846 0 0 212 /**
 * If the document automatically followed one or more redirects, this will
 * return the number of redirects that were automatically followed.  Use
 * get_redirect_step() to retrieve each URL in sequence.
 */ 1 4 this 3 891  
242 0 0 6 294 864 0 0 182 /**
 * Use in conjunction with get_num_redirect_steps() to extract the chain of
 * URL's that the channel was automatically redirected through to arrive at
 * the final document.
 */ 2 4 this 3 891  1 n 1 846  
243 0 0 4 296 845 0 0 532 /**
 * Indicates whether the HTTPChannel should try to keep the connection to the
 * server open and reuse that connection for multiple documents, or whether it
 * should close the connection and open a new one for each request.  Set this
 * true to keep the connections around when possible, false to recycle them.
 *
 * It makes most sense to set this false when the HTTPChannel will be used
 * only once to retrieve a single document, true when you will be using the
 * same HTTPChannel object to retrieve multiple documents.
 */ 2 4 this 3 885  21 persistent_connection 1 806  
244 0 0 6 297 806 0 0 267 /**
 * Returns whether the HTTPChannel should try to keep the connection to the
 * server open and reuse that connection for multiple documents, or whether it
 * should close the connection and open a new one for each request.  See
 * set_persistent_connection().
 */ 1 4 this 3 891  
245 0 0 6 298 806 0 0 217 /**
 * Returns true if the server has indicated it will close the connection after
 * this document has been read, or false if it will remain open (and future
 * documents may be requested on the same connection).
 */ 1 4 this 3 891  
246 0 0 4 299 845 0 0 325 /**
 * If this is true (the normal case), the HTTPClient will be consulted for
 * information about the proxy to be used for each connection via this
 * HTTPChannel.  If this has been set to false by the user, then all
 * connections will be made directly, regardless of the proxy settings
 * indicated on the HTTPClient.
 */ 2 4 this 3 885  11 allow_proxy 1 806  
247 0 0 6 300 806 0 0 325 /**
 * If this is true (the normal case), the HTTPClient will be consulted for
 * information about the proxy to be used for each connection via this
 * HTTPChannel.  If this has been set to false by the user, then all
 * connections will be made directly, regardless of the proxy settings
 * indicated on the HTTPClient.
 */ 1 4 this 3 891  
248 0 0 4 301 845 0 0 795 /**
 * Normally, a proxy is itself asked for ordinary URL's, and the proxy decides
 * whether to hand the client a cached version of the document or to contact
 * the server for a fresh version.  The proxy may also modify the headers and
 * transfer encoding on the way.
 *
 * If this is set to true, then instead of asking for URL's from the proxy, we
 * will ask the proxy to open a connection to the server (for instance, on
 * port 80); if the proxy honors this request, then we contact the server
 * directly through this connection to retrieve the document.  If the proxy
 * does not honor the connect request, then the retrieve operation fails.
 *
 * SSL connections (e.g.  https), and connections through a Socks proxy, are
 * always tunneled, regardless of the setting of this flag.
 */ 2 4 this 3 885  12 proxy_tunnel 1 806  
249 0 0 6 302 806 0 0 172 /**
 * Returns true if connections always tunnel through a proxy, or false (the
 * normal case) if we allow the proxy to serve up documents.  See
 * set_proxy_tunnel().
 */ 1 4 this 3 891  
250 0 0 4 303 845 0 0 322 /**
 * Sets the maximum length of time, in seconds, that the channel will wait
 * before giving up on establishing a TCP connection.
 *
 * At present, this is used only for the nonblocking interfaces (e.g.
 * begin_get_document(), begin_connect_to()), but it is used whether
 * set_blocking_connect() is true or false.
 */ 2 4 this 3 885  15 timeout_seconds 1 854  
251 0 0 6 304 854 0 0 130 /**
 * Returns the length of time, in seconds, to wait for a new nonblocking
 * socket to connect.  See set_connect_timeout().
 */ 1 4 this 3 891  
252 0 0 4 305 845 0 0 641 /**
 * If this flag is true, a socket connect will block even for nonblocking I/O
 * calls like begin_get_document(), begin_connect_to(), etc.  If false, a
 * socket connect will not block for nonblocking I/O calls, but will block for
 * blocking I/O calls (get_document(), connect_to(), etc.).
 *
 * Setting this true is useful when you want to use non-blocking I/O once you
 * have established the connection, but you don't want to bother with polling
 * for the initial connection.  It's also useful when you don't particularly
 * care about non-blocking I/O, but you need to respect timeouts like
 * connect_timeout and http_timeout.
 */ 2 4 this 3 885  16 blocking_connect 1 806  
253 0 0 6 306 806 0 0 298 /**
 * If this flag is true, a socket connect will block even for nonblocking I/O
 * calls like begin_get_document(), begin_connect_to(), etc.  If false, a
 * socket connect will not block for nonblocking I/O calls, but will block for
 * blocking I/O calls (get_document(), connect_to(), etc.).
 */ 1 4 this 3 891  
254 0 0 4 307 845 0 0 485 /**
 * Sets the maximum length of time, in seconds, that the channel will wait for
 * the HTTP server to finish sending its response to our request.
 *
 * The timer starts counting after the TCP connection has been established
 * (see set_connect_timeout(), above) and the request has been sent.
 *
 * At present, this is used only for the nonblocking interfaces (e.g.
 * begin_get_document(), begin_connect_to()), but it is used whether
 * set_blocking_connect() is true or false.
 */ 2 4 this 3 885  15 timeout_seconds 1 854  
255 0 0 6 308 854 0 0 133 /**
 * Returns the length of time, in seconds, to wait for the HTTP server to
 * respond to our request.  See set_http_timeout().
 */ 1 4 this 3 891  
256 0 0 4 309 845 0 0 632 /**
 * Specifies the maximum number of bytes in a received (but unwanted) body
 * that will be skipped past, in order to reset to a new request.
 *
 * That is, if this HTTPChannel requests a file via get_document(), but does
 * not call download_to_ram(), download_to_file(), or open_read_body(), and
 * instead immediately requests a new file, then the HTTPChannel has a choice
 * whether to skip past the unwanted document, or to close the connection and
 * open a new one.  If the number of bytes to skip is more than this
 * threshold, the connection will be closed; otherwise, the data will simply
 * be read and discarded.
 */ 2 4 this 3 885  14 skip_body_size 1 869  
257 0 0 6 310 869 0 0 176 /**
 * Returns the maximum number of bytes in a received (but unwanted) body that
 * will be skipped past, in order to reset to a new request.  See
 * set_skip_body_size().
 */ 1 4 this 3 891  
258 0 0 4 311 845 0 0 387 /**
 * Specifies the amount of time, in seconds, in which a previously-established
 * connection is allowed to remain open and unused.  If a previous connection
 * has remained unused for at least this number of seconds, it will be closed
 * and a new connection will be opened; otherwise, the same connection will be
 * reused for the next request (for this particular HTTPChannel).
 */ 2 4 this 3 885  12 idle_timeout 1 854  
259 0 0 6 312 854 0 0 162 /**
 * Returns the amount of time, in seconds, in which an previously-established
 * connection is allowed to remain open and unused.  See set_idle_timeout().
 */ 1 4 this 3 891  
260 0 0 4 313 845 0 0 686 /**
 * Specifies whether nonblocking downloads (via download_to_file() or
 * download_to_ram()) will be limited so as not to use all available
 * bandwidth.
 *
 * If this is true, when a download has been started on this channel it will
 * be invoked no more frequently than get_max_updates_per_second(), and the
 * total bandwidth used by the download will be no more than
 * get_max_bytes_per_second().  If this is false, downloads will proceed as
 * fast as the server can send the data.
 *
 * This only has effect on the nonblocking I/O methods like
 * begin_get_document(), etc.  The blocking methods like get_document() always
 * use as much CPU and bandwidth as they can get.
 */ 2 4 this 3 885  17 download_throttle 1 806  
261 0 0 6 314 806 0 0 112 /**
 * Returns whether the nonblocking downloads will be bandwidth-limited.  See
 * set_download_throttle().
 */ 1 4 this 3 891  
262 0 0 4 315 845 0 0 178 /**
 * When bandwidth throttling is in effect (see set_download_throttle()), this
 * specifies the maximum number of bytes per second that may be consumed by
 * this channel.
 */ 2 4 this 3 885  20 max_bytes_per_second 1 854  
263 0 0 6 316 854 0 0 135 /**
 * Returns the maximum number of bytes per second that may be consumed by this
 * channel when get_download_throttle() is true.
 */ 1 4 this 3 891  
264 0 0 4 317 845 0 0 194 /**
 * When bandwidth throttling is in effect (see set_download_throttle()), this
 * specifies the maximum number of times per second that run() will attempt to
 * do any downloading at all.
 */ 2 4 this 3 885  22 max_updates_per_second 1 854  
265 0 0 6 318 854 0 0 134 /**
 * Returns the maximum number of times per second that run() will do anything
 * at all, when get_download_throttle() is true.
 */ 1 4 this 3 891  
266 0 0 4 319 845 0 0 127 /**
 * Specifies the Content-Type header, useful for applications that require
 * different types of content, such as JSON.
 */ 2 4 this 3 885  12 content_type 1 868  
267 0 0 6 320 868 0 0 56 /**
 * Returns the value of the Content-Type header.
 */ 1 4 this 3 891  
268 0 0 4 321 845 0 0 320 /**
 * This may be called immediately after a call to get_document() or some
 * related function to specify the expected size of the document we are
 * retrieving, if we happen to know.  This is used as the return value to
 * get_file_size() only in the case that the server does not tell us the
 * actual file size.
 */ 2 4 this 3 885  9 file_size 1 869  
269 0 0 6 322 893 0 0 506 /**
 * Returns the size of the file, if it is known.  Returns the value set by
 * set_expected_file_size() if the file size is not known, or 0 if this value
 * was not set.
 *
 * If the file is dynamically generated, the size may not be available until a
 * read has started (e.g.  open_read_body() has been called); and even then it
 * may increase as more of the file is read due to the nature of HTTP/1.1
 * requests which can change their minds midstream about how much data they're
 * sending you.
 */ 1 4 this 3 891  
270 0 0 6 323 806 0 0 327 /**
 * Returns true if the size of the file we are currently retrieving was told
 * us by the server and thus is reliably known, or false if the size reported
 * by get_file_size() represents an educated guess (possibly as set by
 * set_expected_file_size(), or as inferred from a chunked transfer encoding
 * in progress).
 */ 1 4 this 3 891  
271 0 0 6 324 869 0 0 289 /**
 * Returns the first byte of the file requested by the request.  This will
 * normally be 0 to indicate that the file is being requested from the
 * beginning, but if the file was requested via a get_subdocument() call, this
 * will contain the first_byte parameter from that call.
 */ 1 4 this 3 891  
272 0 0 6 325 869 0 0 285 /**
 * Returns the last byte of the file requested by the request.  This will
 * normally be 0 to indicate that the file is being requested to its last
 * byte, but if the file was requested via a get_subdocument() call, this will
 * contain the last_byte parameter from that call.
 */ 1 4 this 3 891  
273 0 0 6 326 869 0 0 367 /**
 * Returns the first byte of the file (that will be) delivered by the server
 * in response to the current request.  Normally, this is the same as
 * get_first_byte_requested(), but some servers will ignore a subdocument
 * request and always return the whole file, in which case this value will be
 * 0, regardless of what was requested to get_subdocument().
 */ 1 4 this 3 891  
274 0 0 6 327 869 0 0 365 /**
 * Returns the last byte of the file (that will be) delivered by the server in
 * response to the current request.  Normally, this is the same as
 * get_last_byte_requested(), but some servers will ignore a subdocument
 * request and always return the whole file, in which case this value will be
 * 0, regardless of what was requested to get_subdocument().
 */ 1 4 this 3 891  
275 0 0 4 328 845 0 0 97 /**
 * Outputs a list of all headers defined by the server to the indicated output
 * stream.
 */ 2 4 this 3 891  3 out 1 858  
276 0 0 4 329 845 0 0 538 /**
 * Stops whatever file transaction is currently in progress, closes the
 * connection, and resets to begin anew.  You shouldn't ever need to call
 * this, since the channel should be able to reset itself cleanly between
 * requests, but it is provided in case you are an especially nervous type.
 *
 * Don't call this after every request unless you set
 * set_persistent_connection() to false, since calling reset() rudely closes
 * the connection regardless of whether we have told the server we intend to
 * keep it open or not.
 */ 1 4 this 3 885  
277 0 0 4 330 845 0 0 487 /**
 * Preserves the previous status code (presumably a failure) from the previous
 * connection attempt.  If the subsequent connection attempt also fails, the
 * returned status code will be the better of the previous code and the
 * current code.
 *
 * This can be called to daisy-chain subsequent attempts to download the same
 * document from different servers.  After all servers have been attempted,
 * the final status code will reflect the attempt that most nearly succeeded.
 */ 1 4 this 3 885  
278 0 0 4 331 845 0 0 99 /**
 * Resets the extra headers that were previously added via calls to
 * send_extra_header().
 */ 1 4 this 3 885  
279 0 0 4 332 845 0 0 352 /**
 * Specifies an additional key: value pair that is added into the header sent
 * to the server with the next request.  This is passed along with no
 * interpretation by the HTTPChannel code.  You may call this repeatedly to
 * append multiple headers.
 *
 * This is persistent for one request only; it must be set again for each new
 * request.
 */ 3 4 this 3 885  3 key 1 868  5 value 1 868  
280 0 0 6 333 806 0 0 111 /**
 * Opens the named document for reading, if available.  Returns true if
 * successful, false otherwise.
 */ 2 4 this 3 885  3 url 1 889  
281 0 0 6 334 806 0 0 313 /**
 * Retrieves only the specified byte range of the indicated document.  If
 * last_byte is 0, it stands for the last byte of the document.  When a
 * subdocument is requested, get_file_size() and get_bytes_downloaded() will
 * report the number of bytes of the subdocument, not of the complete
 * document.
 */ 4 4 this 3 885  3 url 1 889  10 first_byte 1 869  9 last_byte 1 869  
282 0 0 6 335 806 0 0 252 /**
 * Like get_document(), except only the header associated with the document is
 * retrieved.  This may be used to test for existence of the document; it
 * might also return the size of the document (if the server gives us this
 * information).
 */ 2 4 this 3 885  3 url 1 889  
283 0 0 6 336 806 0 0 74 /**
 * Posts form data to a particular URL and retrieves the response.
 */ 3 4 this 3 885  3 url 1 889  4 body 1 868  
284 0 0 6 337 806 0 0 111 /**
 * Uploads the indicated body to the server to replace the indicated URL, if
 * the server allows this.
 */ 3 4 this 3 885  3 url 1 889  4 body 1 868  
285 0 0 6 338 806 0 0 59 /**
 * Requests the server to remove the indicated URL.
 */ 2 4 this 3 885  3 url 1 889  
286 0 0 6 339 806 0 0 159 /**
 * Sends a TRACE message to the server, which should return back the same
 * message as the server received it, allowing inspection of proxy hops, etc.
 */ 2 4 this 3 885  3 url 1 889  
287 0 0 6 340 806 0 0 319 /**
 * Establish a direct connection to the server and port indicated by the URL,
 * but do not issue any HTTP requests.  If successful, the connection may then
 * be taken to use for whatever purposes you like by calling get_connection().
 *
 * This establishes a blocking I/O socket.  Also see begin_connect_to().
 */ 2 4 this 3 885  3 url 1 889  
288 0 0 6 341 806 0 0 136 /**
 * Sends an OPTIONS message to the server, which should query the available
 * options, possibly in relation to a specified URL.
 */ 2 4 this 3 885  3 url 1 889  
289 0 0 4 342 845 0 0 445 /**
 * Begins a non-blocking request to retrieve a given document.  This method
 * will return immediately, even before a connection to the server has
 * necessarily been established; you must then call run() from time to time
 * until the return value of run() is false.  Then you may check is_valid()
 * and get_status_code() to determine the status of your request.
 *
 * If a previous request had been pending, that request is discarded.
 */ 2 4 this 3 885  3 url 1 889  
290 0 0 4 343 845 0 0 345 /**
 * Begins a non-blocking request to retrieve only the specified byte range of
 * the indicated document.  If last_byte is 0, it stands for the last byte of
 * the document.  When a subdocument is requested, get_file_size() and
 * get_bytes_downloaded() will report the number of bytes of the subdocument,
 * not of the complete document.
 */ 4 4 this 3 885  3 url 1 889  10 first_byte 1 869  9 last_byte 1 869  
291 0 0 4 344 845 0 0 115 /**
 * Begins a non-blocking request to retrieve a given header.  See
 * begin_get_document() and get_header().
 */ 2 4 this 3 885  3 url 1 889  
292 0 0 4 345 845 0 0 431 /**
 * Posts form data to a particular URL and retrieves the response, all using
 * non-blocking I/O.  See begin_get_document() and post_form().
 *
 * It is important to note that you *must* call run() repeatedly after calling
 * this method until run() returns false, and you may not call any other
 * document posting or retrieving methods using the HTTPChannel object in the
 * interim, or your form data may not get posted.
 */ 3 4 this 3 885  3 url 1 889  4 body 1 868  
293 0 0 6 346 806 0 0 425 /**
 * This must be called from time to time when non-blocking I/O is in use.  It
 * checks for data coming in on the socket and writes data out to the socket
 * when possible, and does whatever processing is required towards completing
 * the current task.
 *
 * The return value is true if the task is still pending (and run() will need
 * to be called again in the future), or false if the current task is
 * complete.
 */ 1 4 this 3 885  
294 0 0 4 347 845 0 0 536 /**
 * Begins a non-blocking request to establish a direct connection to the
 * server and port indicated by the URL.  No HTTP requests will be issued
 * beyond what is necessary to establish the connection.  When run() has
 * finished, you may call is_connection_ready() to determine if the connection
 * was successfully established.
 *
 * If successful, the connection may then be taken to use for whatever
 * purposes you like by calling get_connection().
 *
 * This establishes a nonblocking I/O socket.  Also see connect_to().
 */ 2 4 this 3 885  3 url 1 889  
295 0 0 6 348 855 0 0 569 /**
 * Returns a newly-allocated istream suitable for reading the body of the
 * document.  This may only be called immediately after a call to
 * get_document() or post_form(), or after a call to run() has returned false.
 *
 * Note that, in nonblocking mode, the returned stream may report an early
 * EOF, even before the actual end of file.  When this happens, you should
 * call stream->is_closed() to determine whether you should attempt to read
 * some more later.
 *
 * The user is responsible for passing the returned istream to
 * close_read_body() later.
 */ 1 4 this 3 885  
296 0 0 4 349 845 0 0 243 /**
 * Closes a file opened by a previous call to open_read_body().  This really
 * just deletes the istream pointer, but it is recommended to use this
 * interface instead of deleting it explicitly, to help work around compiler
 * issues.
 */ 2 4 this 3 891  6 stream 1 856  
297 0 0 6 350 806 0 0 1345 /**
 * Specifies the name of a file to download the resulting document to.  This
 * should be called immediately after get_document() or begin_get_document()
 * or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the file and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated file.
 * It returns true if the file can be opened for writing, false otherwise, but
 * the contents will not be completely downloaded until run() has returned
 * false.  At this time, it is possible that a communications error will have
 * left a partial file, so is_download_complete() may be called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the file for writing the output.  In this case, the file must
 * already exist and must have at least first_byte bytes in it.  If
 * subdocument_resumes is false, a subdocument will always be downloaded
 * beginning at the first byte of the file.
 */ 3 4 this 3 885  8 filename 1 865  19 subdocument_resumes 5 806  
298 0 0 6 351 806 0 0 1223 /**
 * Specifies a Ramfile object to download the resulting document to.  This
 * should be called immediately after get_document() or begin_get_document()
 * or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the Ramfile and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated
 * Ramfile.  It returns true if the file can be opened for writing, false
 * otherwise, but the contents will not be completely downloaded until run()
 * has returned false.  At this time, it is possible that a communications
 * error will have left a partial file, so is_download_complete() may be
 * called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the Ramfile for writing the output.  In this case, the Ramfile must
 * already have at least first_byte bytes in it.
 */ 3 4 this 3 885  7 ramfile 1 895  19 subdocument_resumes 5 806  
299 0 0 6 352 806 0 0 1348 /**
 * Specifies the name of an ostream to download the resulting document to.
 * This should be called immediately after get_document() or
 * begin_get_document() or related functions.
 *
 * In the case of the blocking I/O methods like get_document(), this function
 * will download the entire document to the file and return true if it was
 * successfully downloaded, false otherwise.
 *
 * In the case of non-blocking I/O methods like begin_get_document(), this
 * function simply indicates an intention to download to the indicated file.
 * It returns true if the file can be opened for writing, false otherwise, but
 * the contents will not be completely downloaded until run() has returned
 * false.  At this time, it is possible that a communications error will have
 * left a partial file, so is_download_complete() may be called to test this.
 *
 * If subdocument_resumes is true and the document in question was previously
 * requested as a subdocument (i.e.  get_subdocument() with a first_byte value
 * greater than zero), this will automatically seek to the appropriate byte
 * within the file for writing the output.  In this case, the file must
 * already exist and must have at least first_byte bytes in it.  If
 * subdocument_resumes is false, a subdocument will always be downloaded
 * beginning at the first byte of the file.
 */ 3 4 this 3 885  4 strm 1 858  19 subdocument_resumes 5 806  
300 0 0 6 353 859 0 0 351 /**
 * Returns the connection that was established via a previous call to
 * connect_to() or begin_connect_to(), or NULL if the connection attempt
 * failed or if those methods have not recently been called.
 *
 * This stream has been allocated from the free store.  It is the user's
 * responsibility to delete this pointer when finished with it.
 */ 1 4 this 3 885  
301 0 0 6 354 869 0 0 331 /**
 * Returns the number of bytes downloaded during the last (or current)
 * download_to_file() or download_to_ram operation().  This can be used in
 * conjunction with get_file_size() to report the percent complete (but be
 * careful, since get_file_size() may return 0 if the server has not told us
 * the size of the file).
 */ 1 4 this 3 891  
302 0 0 6 355 869 0 0 658 /**
 * When download throttling is in effect (set_download_throttle() has been set
 * to true) and non-blocking I/O methods (like begin_get_document()) are used,
 * this returns the number of bytes "requested" from the server so far: that
 * is, the theoretical maximum value for get_bytes_downloaded(), if the server
 * has been keeping up with our demand.
 *
 * If this number is less than get_bytes_downloaded(), then the server has not
 * been supplying bytes fast enough to meet our own download throttle rate.
 *
 * When download throttling is not in effect, or when the blocking I/O methods
 * (like get_document(), etc.) are used, this returns 0.
 */ 1 4 this 3 891  
303 0 0 6 356 806 0 0 600 /**
 * Returns true when a download_to() or download_to_ram() has executed and the
 * file has been fully downloaded.  If this still returns false after
 * processing has completed, there was an error in transmission.
 *
 * Note that simply testing is_download_complete() does not prove that the
 * requested document was successfully retrieved--you might have just
 * downloaded the "404 not found" stub (for instance) that a server would
 * provide in response to some error condition.  You should also check
 * is_valid() to prove that the file you expected has been successfully
 * retrieved.
 */ 1 4 this 3 891  
304 0 0 7 357 898 0 0 0 0 
305 0 0 7 360 899 703 0 10 /**
 *
 */ 0 
306 0 0 15 360 899 703 0 0 1 6 param0 0 900  
307 0 0 6 362 846 0 0 201 /**
 * Begins a background decompression of the named file (whose filename must
 * end in ".pz") to a new file without the .pz extension.  The source file is
 * removed after successful completion.
 */ 2 4 this 3 899  11 source_file 1 865  
308 0 0 6 362 846 0 0 162 /**
 * Begins a background decompression from the named source file to the named
 * destination file.  The source file is removed after successful completion.
 */ 3 4 this 3 899  11 source_file 1 865  9 dest_file 1 865  
309 0 0 6 363 846 0 0 227 /**
 * Called each frame to do the next bit of work in the background task.
 * Returns EU_ok if a chunk is completed but there is more to go, or
 * EU_success when we're all done.  Any other return value indicates an error.
 */ 1 4 this 3 899  
310 0 0 6 364 806 0 0 121 /**
 * Performs a foreground decompression of the named file; does not return
 * until the decompression is complete.
 */ 2 4 this 3 899  11 source_file 1 865  
311 0 0 6 364 806 0 0 152 /**
 * Does an in-memory decompression of the indicated Ramfile.  The decompressed
 * contents are written back into the same Ramfile on completion.
 */ 2 4 this 3 899  20 source_and_dest_file 1 895  
312 0 0 6 365 827 0 0 78 /**
 * Returns the ratio through the decompression step in the background.
 */ 1 4 this 3 900  
313 0 0 7 371 902 709 0 38 /**
 * Primarily used for testing.
 */ 0 
314 0 0 15 371 902 709 0 0 1 6 param0 0 903  
315 0 0 7 371 902 709 0 64 /**
 * Create a download db with these client and server dbs
 */ 2 11 server_file 1 905  11 client_file 1 905  
316 0 0 7 371 902 709 0 64 /**
 * Create a download db with these client and server dbs
 */ 2 11 server_file 1 895  11 client_file 1 905  
317 0 0 4 373 845 0 0 10 /**
 *
 */ 2 4 this 3 903  3 out 1 858  
318 0 0 4 374 845 0 0 10 /**
 *
 */ 2 4 this 3 903  3 out 1 858  
319 0 0 4 375 845 0 0 10 /**
 *
 */ 2 4 this 3 903  3 out 1 858  
320 0 0 6 376 806 0 0 24 // Write a database file 2 4 this 3 902  4 file 1 905  
321 0 0 6 377 806 0 0 10 /**
 *
 */ 2 4 this 3 902  4 file 1 905  
322 0 0 6 378 846 0 0 10 /**
 *
 */ 1 4 this 3 903  
323 0 0 6 379 846 0 0 10 /**
 *
 */ 1 4 this 3 903  
324 0 0 6 380 868 0 0 10 /**
 *
 */ 2 4 this 3 903  5 index 1 846  
325 0 0 6 381 868 0 0 10 /**
 *
 */ 2 4 this 3 903  5 index 1 846  
326 0 0 6 382 846 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
327 0 0 4 383 845 0 0 10 /**
 *
 */ 3 4 this 3 902  6 mfname 1 868  4 size 1 846  
328 0 0 6 384 846 0 0 10 /**
 *
 */ 3 4 this 3 902  6 mfname 1 868  4 size 1 846  
329 0 0 6 385 846 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
330 0 0 4 386 845 0 0 10 /**
 *
 */ 3 4 this 3 902  6 mfname 1 868  4 size 1 846  
331 0 0 6 387 906 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
332 0 0 6 388 906 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
333 0 0 4 389 845 0 0 10 /**
 *
 */ 2 4 this 3 902  6 mfname 1 868  
334 0 0 4 390 845 0 0 10 /**
 *
 */ 2 4 this 3 902  6 mfname 1 868  
335 0 0 4 391 845 0 0 10 /**
 *
 */ 2 4 this 3 902  6 mfname 1 868  
336 0 0 4 392 845 0 0 10 /**
 *
 */ 2 4 this 3 902  6 mfname 1 868  
337 0 0 6 393 846 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
338 0 0 6 394 868 0 0 10 /**
 *
 */ 3 4 this 3 903  6 mfname 1 868  5 index 1 846  
339 0 0 6 395 806 0 0 28 // Queries from the Launcher 2 4 this 3 903  6 mfname 1 868  
340 0 0 6 396 806 0 0 147 /**
 * A multifile is complete when it is completely downloaded.  Note: it may
 * already be decompressed or extracted and it is still complete
 */ 2 4 this 3 903  6 mfname 1 868  
341 0 0 6 397 806 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
342 0 0 6 398 806 0 0 10 /**
 *
 */ 2 4 this 3 903  6 mfname 1 868  
343 0 0 7 399 908 0 0 62 /**
 * Return the hash value of the file we are working on
 */ 2 4 this 3 903  6 mfname 1 868  
344 0 0 4 400 845 0 0 55 /**
 * Set the hash value of file we are working on
 */ 3 4 this 3 902  6 mfname 1 868  3 val 1 908  
345 0 0 7 401 908 0 0 51 /**
 * Return the hash value of the server file
 */ 2 4 this 3 903  6 mfname 1 868  
346 0 0 4 402 845 0 0 55 /**
 * Set the hash value of file we are working on
 */ 3 4 this 3 902  6 mfname 1 868  3 val 1 908  
347 0 0 4 403 845 0 0 27 // Operations on multifiles 2 4 this 3 902  6 mfname 1 868  
348 0 0 4 404 845 0 0 10 /**
 *
 */ 2 4 this 3 902  13 server_mfname 1 868  
349 0 0 4 405 845 0 0 10 /**
 *
 */ 2 4 this 3 902  6 mfname 1 868  
350 0 0 4 406 845 0 0 76 /**
 * Used on the server side makefiles to create a new clean server db
 */ 1 4 this 3 902  
351 0 0 4 407 845 0 0 10 /**
 *
 */ 5 4 this 3 902  6 mfname 1 868  5 phase 1 906  4 size 1 846  6 status 1 846  
352 0 0 4 408 845 0 0 10 /**
 *
 */ 3 4 this 3 902  6 mfname 1 868  5 fname 1 868  
353 0 0 7 409 910 0 0 10 /**
 *
 */ 3 4 this 3 902  4 file 1 905  16 want_server_info 1 806  
354 0 0 7 409 910 0 0 10 /**
 *
 */ 3 4 this 3 902  4 file 1 895  16 want_server_info 1 806  
355 0 0 6 410 806 0 0 10 /**
 *
 */ 4 4 this 3 902  4 file 1 905  2 db 1 910  16 want_server_info 1 806  
356 0 0 4 411 845 0 0 176 /**
 * Appends a new version of the file onto the end of the list, or changes the
 * hash associated with a version previously added.
 *
 * Note: version numbers start at 1
 */ 4 4 this 3 902  4 name 1 865  4 hash 1 911  7 version 1 846  
357 0 0 4 412 845 0 0 97 /**
 * Inserts a new version 1 copy of the file, sliding all the other versions up
 * by one.
 */ 3 4 this 3 902  4 name 1 865  4 hash 1 911  
358 0 0 6 413 806 0 0 164 /**
 * Returns true if the indicated file has version information, false
 * otherwise.  Some files recorded in the database may not bother to track
 * versions.
 */ 2 4 this 3 903  4 name 1 865  
359 0 0 6 414 846 0 0 72 /**
 * Returns the number of versions stored for the indicated file.
 */ 2 4 this 3 903  4 name 1 865  
360 0 0 4 415 845 0 0 146 /**
 * Reduces the number of versions of a particular file stored in the ddb by
 * throwing away all versions higher than the indicated index.
 */ 3 4 this 3 902  4 name 1 865  12 num_versions 1 846  
361 0 0 6 416 846 0 0 183 /**
 * Returns the version number of this particular file, determined by looking
 * up the hash generated from the file.  Returns -1 if the version number
 * cannot be determined.
 */ 3 4 this 3 903  4 name 1 865  4 hash 1 911  
362 0 0 6 417 911 0 0 95 /**
 * Returns the MD5 hash associated with the indicated version of the indicated
 * file.
 */ 3 4 this 3 903  4 name 1 865  7 version 1 846  
363 0 0 7 421 913 758 0 10 /**
 *
 */ 0 
364 0 0 6 423 806 0 0 147 /**
 * Specifies the filename of the Multifile that the Extractor will read.
 * Returns true on success, false if the mulifile name is invalid.
 */ 2 4 this 3 913  14 multifile_name 1 865  
365 0 0 4 424 845 0 0 192 /**
 * Specifies the directory into which all extracted subfiles will be written.
 * Relative paths of subfiles within the Multifile will be written as relative
 * paths to this directory.
 */ 2 4 this 3 913  11 extract_dir 1 865  
366 0 0 4 425 845 0 0 132 /**
 * Interrupts the Extractor in the middle of its business and makes it ready
 * to accept a new list of subfiles to extract.
 */ 1 4 this 3 913  
367 0 0 6 426 806 0 0 145 /**
 * Requests a particular subfile to be extracted when step() or run() is
 * called.  Returns true if the subfile exists, false otherwise.
 */ 2 4 this 3 913  12 subfile_name 1 865  
368 0 0 6 427 846 0 0 100 /**
 * Requests all subfiles in the Multifile to be extracted.  Returns the number
 * requested.
 */ 1 4 this 3 913  
369 0 0 6 428 846 0 0 390 /**
 * After all of the requests have been made via request_file() or
 * request_all_subfiles(), call step() repeatedly until it stops returning
 * EU_ok.
 *
 * step() extracts the next small unit of data from the Multifile.  Returns
 * EU_ok if progress is continuing, EU_error_abort if there is a problem, or
 * EU_success when the last piece has been extracted.
 *
 * Also see run().
 */ 1 4 this 3 913  
370 0 0 6 429 827 0 0 66 /**
 * Returns the fraction of the Multifile extracted so far.
 */ 1 4 this 3 914  
371 0 0 6 430 806 0 0 367 /**
 * A convenience function to extract the Multifile all at once, when you don't
 * care about doing it in the background.
 *
 * First, call request_file() or request_all_files() to specify the files you
 * would like to extract, then call run() to do the extraction.  Also see
 * step() for when you would like the extraction to happen as a background
 * task.
 */ 1 4 this 3 913  
372 0 0 7 433 916 774 0 10 /**
 *
 */ 0 
373 0 0 4 434 845 0 0 134 /**
 * Adds the indicated generic ostream to the multiplex output.  The ostream
 * will receive whatever data is sent to the pipe.
 */ 3 4 this 3 916  3 out 1 858  12 delete_later 5 806  
374 0 0 6 435 806 0 0 104 /**
 * Adds the given file, previously opened using the C stdio library, to the
 * multiplex output.
 */ 3 4 this 3 916  4 file 1 917  15 close_when_done 1 806  
375 0 0 4 436 845 0 0 44 /**
 * Adds the standard output channel.
 */ 1 4 this 3 916  
376 0 0 6 437 806 0 0 159 /**
 * Adds the given file to the multiplex output.  The file is opened in append
 * mode with line buffering.  Returns false if the file cannot be opened.
 */ 2 4 this 3 916  4 file 1 905  
377 0 0 4 438 845 0 0 247 /**
 * Adds the system debug output the the multiplex output.  This may map to a
 * syslog or some such os-specific output system.  It may do nothing on a
 * particular system.
 *
 * Presently, this maps only to OutputDebugString() on Windows.
 */ 1 4 this 3 916  
378 0 0 4 439 845 0 0 62 /**
 * Forces out all output that hasn't yet been written.
 */ 1 4 this 3 916  
379 0 0 7 443 898 0 0 0 0 
380 0 0 7 446 919 0 0 10 /**
 *
 */ 2 4 root 1 864  4 http 5 882  
381 0 0 7 447 882 0 0 72 /**
 * Returns the HTTPClient object that services this mount point.
 */ 1 4 this 3 920  
382 0 0 6 448 864 0 0 72 /**
 * Returns the URL that represents the root of this mount point.
 */ 1 4 this 3 920  
383 0 0 4 449 845 0 0 396 /**
 * Reads all of the vfs-mount-url lines in the Config.prc file and replaces
 * the mount settings to match them.
 *
 * This will mount any url's mentioned in the config file, and unmount and
 * unmount any url's no longer mentioned in the config file.  Normally, it is
 * called automatically at startup, and need not be called again, unless you
 * have fiddled with some config settings.
 */ 0 
384 0 0 7 450 898 0 0 0 0 
385 0 0 7 452 922 782 0 10 /**
 *
 */ 0 
386 0 0 15 452 922 782 0 0 1 6 param0 0 923  
387 0 0 7 452 922 782 0 10 /**
 *
 */ 1 6 buffer 1 925  
388 0 0 6 454 846 0 0 10 /**
 *
 */ 3 4 this 3 922  5 patch 1 905  6 infile 1 905  
389 0 0 6 455 846 0 0 10 /**
 *
 */ 1 4 this 3 922  
390 0 0 6 456 827 0 0 10 /**
 *
 */ 1 4 this 3 923  
391 0 0 7 458 927 791 0 10 /**
 *
 */ 0 
392 0 0 55 458 927 791 0 0 1 6 source 1 839  
393 0 0 4 459 845 0 0 30 /**
 * Empties the buffer.
 */ 1 4 this 3 927  
394 0 0 6 460 869 0 0 86 /**
 * Returns the number of characters available to be read from the data stream.
 */ 1 4 this 3 927  
395 0 0 38 461 839 0 0 63 /**
 * Returns the contents of the data stream as a string.
 */ 1 4 this 3 927  
396 0 0 36 462 845 0 0 0 2 4 this 3 927  4 data 1 839  
397 0 0 6 418 928 0 0 0 1 4 name 1 905  
398 0 0 6 419 928 0 0 0 1 4 name 1 905  
137
792 8 SSReader 0 26625 8 SSReader 8 SSReader 0 0 0 0 399 0 5 400 401 402 403 404 0 0 0 0 0 219
/**
 * An internal class for reading from a socket stream.  This serves as a base
 * class for both ISocketStream and SocketStream; its purpose is to minimize
 * redundant code between them.  Do not use it directly.
 */

793 8 SSWriter 0 26625 8 SSWriter 8 SSWriter 0 0 0 0 405 0 11 406 407 408 409 410 411 412 413 414 415 416 0 0 0 0 0 217
/**
 * An internal class for writing to a socket stream.  This serves as a base
 * class for both OSocketStream and SocketStream; its purpose is to minimize
 * redundant code between them.  Do not use it directly.
 */

794 13 ISocketStream 0 26625 13 ISocketStream 13 ISocketStream 0 0 0 0 421 0 3 422 423 424 0 0 2 3 795 417 418 3 792 419 420 0 1 796 278
/**
 * This is a base class for istreams implemented in Panda that read from a
 * (possibly non-blocking) socket.  It adds is_closed(), which can be called
 * after an eof condition to check whether the socket has been closed, or
 * whether more data may be available later.
 */

795 7 istream 0 2048 12 std::istream 12 std::istream 0 0 0 0 0 0 0 0 0 0 0 0 0

796 9 ReadState 0 794624 24 ISocketStream::ReadState 24 ISocketStream::ReadState 794 0 0 0 0 0 0 0 0 0 4 10 RS_initial 25 ISocketStream::RS_initial 0
0 10 RS_reading 25 ISocketStream::RS_reading 0
1 11 RS_complete 26 ISocketStream::RS_complete 0
2 8 RS_error 23 ISocketStream::RS_error 0
3 0 0

797 13 OSocketStream 0 141313 13 OSocketStream 13 OSocketStream 0 0 0 0 432 0 3 429 430 431 0 0 2 3 798 425 426 3 793 427 428 0 0 251
/**
 * A base class for ostreams that write to a (possibly non-blocking) socket.
 * It adds is_closed(), which can be called after any write operation fails to
 * check whether the socket has been closed, or whether more data may be sent
 * later.
 */

798 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

799 12 SocketStream 0 141313 12 SocketStream 12 SocketStream 0 0 0 0 444 0 5 439 440 441 442 443 0 0 3 3 800 433 434 3 792 435 436 3 793 437 438 0 0 97
/**
 * A base class for iostreams that read and write to a (possibly non-blocking)
 * socket.
 */

800 8 iostream 0 2048 13 std::iostream 13 std::iostream 0 0 0 0 0 0 0 0 0 0 0 0 0

801 7 URLSpec 0 141313 7 URLSpec 7 URLSpec 0 0 0 1 445 495 9 929 930 931 932 933 934 935 936 937 47 446 447 448 449 450 451 452 453 454 455 456 457 458 459 460 461 462 463 464 465 466 467 468 469 470 471 472 473 474 475 476 477 478 479 480 481 483 484 486 487 488 489 490 491 492 493 494 0 2 482 485 0 0 0 224
/**
 * A container for a URL, e.g.  "http://server:port/path".
 *
 * The URLSpec object is similar to a Filename in that it contains logic to
 * identify the various parts of a URL and return (or modify) them separately.
 */

802 6 string 0 2105344 11 std::string 11 std::string 0 0 803 0 0 0 0 0 0 0 0 0 0

803 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

804 8 uint16_t 0 2105344 8 uint16_t 8 uint16_t 0 0 805 0 0 0 0 0 0 0 0 0 0

805 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

806 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

807 8 HTTPEnum 0 141313 8 HTTPEnum 8 HTTPEnum 0 0 0 1 496 497 0 0 0 0 0 0 2 808 809 147
/**
 * This class is just used as a namespace wrapper for some of the enumerated
 * types used by various classes within the HTTPClient family.
 */

808 11 HTTPVersion 0 794624 21 HTTPEnum::HTTPVersion 21 HTTPEnum::HTTPVersion 807 0 0 0 0 0 0 0 0 0 4 5 HV_09 15 HTTPEnum::HV_09 20
// HTTP 0.9 or older
0 5 HV_10 15 HTTPEnum::HV_10 11
// HTTP 1.0
1 5 HV_11 15 HTTPEnum::HV_11 11
// HTTP 1.1
2 8 HV_other 18 HTTPEnum::HV_other 0
3 0 0

809 6 Method 0 794624 16 HTTPEnum::Method 16 HTTPEnum::Method 807 0 0 0 0 0 0 0 0 0 8 9 M_options 19 HTTPEnum::M_options 0
0 5 M_get 15 HTTPEnum::M_get 0
1 6 M_head 16 HTTPEnum::M_head 0
2 6 M_post 16 HTTPEnum::M_post 0
3 5 M_put 15 HTTPEnum::M_put 0
4 8 M_delete 18 HTTPEnum::M_delete 0
5 7 M_trace 17 HTTPEnum::M_trace 0
6 9 M_connect 19 HTTPEnum::M_connect 0
7 0 0

810 8 HTTPDate 0 141313 8 HTTPDate 8 HTTPDate 0 0 0 1 498 515 0 16 499 500 501 502 503 504 505 506 507 508 509 510 511 512 513 514 0 0 0 0 0 238
/**
 * A container for an HTTP-legal time/date indication.  This can accept a
 * string from an HTTP header and will decode it into a C time_t value;
 * conversely, it can accept a time_t value and encode it for output as a
 * string.
 */

811 10 HTTPCookie 0 26625 10 HTTPCookie 10 HTTPCookie 0 0 0 1 516 517 6 938 939 940 941 942 943 20 518 519 520 521 522 523 524 525 526 527 528 529 530 531 532 533 534 535 536 537 0 0 0 0 0 124
/**
 * A cookie sent from an HTTP server to be stored on the client and returned
 * when the path and/or domain matches.
 */

812 12 string const 0 8832 17 std::string const 17 std::string const 0 0 802 0 0 0 0 0 0 0 0 0 0

813 10 HTTPClient 0 75777 10 HTTPClient 10 HTTPClient 0 0 0 1 538 0 0 48 539 540 541 542 543 544 545 546 547 548 549 550 551 552 553 554 555 556 557 558 559 560 561 562 563 564 565 566 567 568 569 570 571 572 573 574 575 576 577 578 579 580 581 582 583 584 585 586 0 0 1 0 814 0 0 0 1 815 552
/**
 * Handles contacting an HTTP server and retrieving a document.  Each
 * HTTPClient object represents a separate context, and stores its own list of
 * cookies, passwords, and certificates; however, a given HTTPClient is
 * capable of making multiple simultaneous requests to the same or different
 * servers.
 *
 * It is up to the programmer whether one HTTPClient should be used to
 * retrieve all documents, or a separate one should be created each time.
 * There is a default, global HTTPClient available in
 * HTTPClient::get_global_ptr().
 */

814 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

815 9 VerifySSL 0 794624 21 HTTPClient::VerifySSL 21 HTTPClient::VerifySSL 813 0 0 0 0 0 0 0 0 0 3 12 VS_no_verify 24 HTTPClient::VS_no_verify 28
// Don't care who we talk to
0 16 VS_no_date_check 28 HTTPClient::VS_no_date_check 53
// Must identify certs, but old, expired certs are OK
1 9 VS_normal 21 HTTPClient::VS_normal 50
// Identify certs and also check expiration dates.
2 0 0

816 13 HTTPEntityTag 0 141313 13 HTTPEntityTag 13 HTTPEntityTag 0 0 0 1 587 599 0 11 588 589 590 591 592 593 594 595 596 597 598 0 0 0 0 0 185
/**
 * A container for an "entity tag" from an HTTP server.  This is used to
 * identify a particular version of a document or resource, particularly
 * useful for verifying caches.
 */

817 12 DocumentSpec 0 141313 12 DocumentSpec 12 DocumentSpec 0 0 0 1 600 623 5 944 945 946 947 948 22 601 602 603 604 605 606 607 608 609 610 611 612 613 614 615 616 617 618 619 620 621 622 0 0 0 0 2 818 819 315
/**
 * A descriptor that refers to a particular version of a document.  This
 * includes the URL of the document and its identity tag and last-modified
 * dates.
 *
 * The DocumentSpec may also be used to request a newer document than a
 * particular one if available, for instance to refresh a cached document.
 */

818 11 RequestMode 0 794624 25 DocumentSpec::RequestMode 25 DocumentSpec::RequestMode 817 0 0 0 0 0 0 0 0 0 4 6 RM_any 20 DocumentSpec::RM_any 0
0 8 RM_equal 22 DocumentSpec::RM_equal 0
1 8 RM_newer 22 DocumentSpec::RM_newer 0
2 17 RM_equal_or_newer 31 DocumentSpec::RM_equal_or_newer 0
3 0 0

819 12 CacheControl 0 794624 26 DocumentSpec::CacheControl 26 DocumentSpec::CacheControl 817 0 0 0 0 0 0 0 0 0 3 14 CC_allow_cache 28 DocumentSpec::CC_allow_cache 0
0 13 CC_revalidate 27 DocumentSpec::CC_revalidate 0
1 11 CC_no_cache 25 DocumentSpec::CC_no_cache 0
2 0 0

820 13 URLSpec const 0 8832 13 URLSpec const 13 URLSpec const 0 0 801 0 0 0 0 0 0 0 0 0 0

821 19 HTTPEntityTag const 0 8832 19 HTTPEntityTag const 19 HTTPEntityTag const 0 0 816 0 0 0 0 0 0 0 0 0 0

822 14 HTTPDate const 0 8832 14 HTTPDate const 14 HTTPDate const 0 0 810 0 0 0 0 0 0 0 0 0 0

823 11 HTTPChannel 0 75777 11 HTTPChannel 11 HTTPChannel 0 0 0 1 701 0 0 77 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 646 647 648 649 650 651 652 653 654 655 656 657 658 659 660 661 662 663 664 665 666 667 668 669 670 671 672 673 674 675 676 677 678 679 680 681 682 683 684 685 686 687 688 689 690 691 692 693 694 695 696 697 698 699 700 1 952 0 1 0 824 0 0 0 1 825 554
/**
 * A single channel of communication from an HTTPClient.  This is similar to
 * the concept of a 'connection', except that HTTP is technically
 * connectionless; in fact, a channel may represent one unbroken connection or
 * it may transparently close and reopen a new connection with each request.
 *
 * A channel is conceptually a single thread of I/O. One document at a time
 * may be requested using a channel; a new document may (in general) not be
 * requested from the same HTTPChannel until the first document has been fully
 * retrieved.
 */

824 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

825 10 StatusCode 0 794624 23 HTTPChannel::StatusCode 23 HTTPChannel::StatusCode 823 0 0 0 0 0 0 0 0 0 20 13 SC_incomplete 26 HTTPChannel::SC_incomplete 0
0 17 SC_internal_error 30 HTTPChannel::SC_internal_error 0
1 16 SC_no_connection 29 HTTPChannel::SC_no_connection 0
2 10 SC_timeout 23 HTTPChannel::SC_timeout 0
3 18 SC_lost_connection 31 HTTPChannel::SC_lost_connection 0
4 20 SC_non_http_response 33 HTTPChannel::SC_non_http_response 0
5 15 SC_invalid_http 28 HTTPChannel::SC_invalid_http 0
6 24 SC_socks_invalid_version 37 HTTPChannel::SC_socks_invalid_version 0
7 35 SC_socks_no_acceptable_login_method 48 HTTPChannel::SC_socks_no_acceptable_login_method 0
8 16 SC_socks_refused 29 HTTPChannel::SC_socks_refused 0
9 22 SC_socks_no_connection 35 HTTPChannel::SC_socks_no_connection 0
10 23 SC_ssl_internal_failure 36 HTTPChannel::SC_ssl_internal_failure 0
11 19 SC_ssl_no_handshake 32 HTTPChannel::SC_ssl_no_handshake 0
12 23 SC_http_error_watermark 36 HTTPChannel::SC_http_error_watermark 129
// No one returns this code, but StatusCode values higher than this are
// deemed more successful than any generic HTTP response.
13 33 SC_ssl_invalid_server_certificate 46 HTTPChannel::SC_ssl_invalid_server_certificate 0
14 37 SC_ssl_self_signed_server_certificate 50 HTTPChannel::SC_ssl_self_signed_server_certificate 0
15 24 SC_ssl_unexpected_server 37 HTTPChannel::SC_ssl_unexpected_server 0
16 22 SC_download_open_error 35 HTTPChannel::SC_download_open_error 79
// These errors are only generated after a download_to_*() call been
// issued.
17 23 SC_download_write_error 36 HTTPChannel::SC_download_write_error 0
18 25 SC_download_invalid_range 38 HTTPChannel::SC_download_invalid_range 0
19 0 195
// get_status_code() will either return an HTTP-style status code >= 100
// (e.g.  404), or one of the following values.  In general, these are
// ordered from less-successful to more-successful.

826 12 Decompressor 0 26625 12 Decompressor 12 Decompressor 0 0 0 1 702 703 1 949 4 704 705 706 707 0 0 0 0 0 114
/**
 * This manages run-time decompression of a zlib-compressed stream, as a
 * background or foreground task.
 */

827 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 828 0 0 0 0 0 0 0 0 0 0

828 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

829 10 DownloadDb 0 26625 10 DownloadDb 10 DownloadDb 0 0 0 1 708 709 0 45 710 711 712 713 714 715 716 717 718 719 720 721 722 723 724 725 726 727 728 729 730 731 732 733 734 735 736 737 738 739 740 741 742 743 744 745 746 747 748 749 750 751 752 753 754 0 0 0 0 1 830 332
/**
 * A listing of files within multifiles for management of client-side
 * synchronization with a server-provided set of files.
 *
 * This class manages one copy of the database for the client, representing
 * the files on the client system, and another copy for the server,
 * representing the files the server has available.
 */

830 6 Status 0 794624 18 DownloadDb::Status 18 DownloadDb::Status 829 0 0 0 0 0 0 0 0 0 4 17 Status_incomplete 29 DownloadDb::Status_incomplete 0
0 15 Status_complete 27 DownloadDb::Status_complete 0
1 19 Status_decompressed 31 DownloadDb::Status_decompressed 0
2 16 Status_extracted 28 DownloadDb::Status_extracted 0
3 0 210
// Status of a multifile is stored in this enum Note these values are in
// increasing order of "doneness" So if you are decompressed, you are
// complete If you are extracted, you are decompressed and complete

831 9 Extractor 0 26625 9 Extractor 9 Extractor 0 0 0 1 757 758 1 950 8 759 760 761 762 763 764 765 766 0 0 0 0 0 543
/**
 * This class automatically extracts the contents of a Multifile to the
 * current directory (or to a specified directory) in the background.
 *
 * It is designed to limit its use of system resources and run unobtrusively
 * in the background.  After specifying the files you wish to extract via
 * repeated calls to request_subfile(), begin the process by calling run()
 * repeatedly.  Each call to run() extracts another small portion of the
 * Multifile.  Call run() whenever you have spare cycles until run() returns
 * EU_success.
 */

832 15 MultiplexStream 0 141313 15 MultiplexStream 15 MultiplexStream 0 0 0 1 767 774 0 6 768 769 770 771 772 773 0 0 1 0 798 0 0 0 0 291
/**
 * This is a special ostream that forwards the data that is written to it to
 * any number of other sources, for instance other ostreams, or explicitly to
 * a disk file or to system logging utilities.  It's a very handy thing to set
 * Notify to refer to when running in batch mode.
 */

833 15 VirtualFileHTTP 0 75777 15 VirtualFileHTTP 15 VirtualFileHTTP 0 0 0 0 0 0 1 775 0 0 1 0 834 0 0 0 0 155
/**
 * This maps a document retrieved from an HTTPClient into the
 * VirtualFileSystem, allowing models etc.  to be loaded directly from a web
 * page.
 */

834 11 VirtualFile 0 2048 11 VirtualFile 11 VirtualFile 0 0 0 0 0 0 0 0 0 0 0 0 91
/**
 * The abstract base class for a file or directory within the
 * VirtualFileSystem.
 */

835 20 VirtualFileMountHTTP 0 75777 20 VirtualFileMountHTTP 20 VirtualFileMountHTTP 0 0 0 1 776 0 0 4 777 778 779 780 0 0 1 0 836 0 0 0 0 65
/**
 * Maps a web page (URL root) into the VirtualFileSystem.
 */

836 16 VirtualFileMount 0 2048 16 VirtualFileMount 16 VirtualFileMount 0 0 0 0 0 0 0 0 0 0 0 0 159
/**
 * The abstract base class for a mount definition used within a
 * VirtualFileSystem.  Normally users don't need to monkey with this class
 * directly.
 */

837 7 Patcher 0 26625 7 Patcher 7 Patcher 0 0 0 1 781 782 0 3 783 784 785 0 0 0 0 0 40
/**
 * Applies a patch synchronously
 */

838 12 StringStream 0 141313 12 StringStream 12 StringStream 0 0 0 1 786 791 1 951 4 787 788 789 790 0 0 1 0 800 0 0 0 0 186
/**
 * A bi-directional stream object that reads and writes data to an internal
 * buffer, which can be retrieved and/or set as a string in Python 2 or a
 * bytes object in Python 3.
 */

839 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 840 0 0 0 0 0 0 0 0 0 0

840 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 841 0 0 0 0 0 0 0 0 0 0

841 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

842 10 SSReader * 0 8576 10 SSReader * 10 SSReader * 0 0 792 0 0 0 0 0 0 0 0 0 0

843 10 Datagram * 0 8576 10 Datagram * 10 Datagram * 0 0 844 0 0 0 0 0 0 0 0 0 0

844 8 Datagram 0 2048 8 Datagram 8 Datagram 0 0 0 0 0 0 0 0 0 0 0 0 565
/**
 * An ordered list of data elements, formatted in memory for transmission over
 * a socket or writing to a data file.
 *
 * Data elements should be added one at a time, in order, to the Datagram.
 * The nature and contents of the data elements are totally up to the user.
 * When a Datagram has been transmitted and received, its data elements may be
 * extracted using a DatagramIterator; it is up to the caller to know the
 * correct type of each data element in order.
 *
 * A Datagram is itself headerless; it is simply a collection of data
 * elements.
 */

845 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

846 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

847 16 SSReader const * 0 8576 16 SSReader const * 16 SSReader const * 0 0 848 0 0 0 0 0 0 0 0 0 0

848 14 SSReader const 0 8832 14 SSReader const 14 SSReader const 0 0 792 0 0 0 0 0 0 0 0 0 0

849 10 SSWriter * 0 8576 10 SSWriter * 10 SSWriter * 0 0 793 0 0 0 0 0 0 0 0 0 0

850 16 Datagram const * 0 8576 16 Datagram const * 16 Datagram const * 0 0 851 0 0 0 0 0 0 0 0 0 0

851 14 Datagram const 0 8832 14 Datagram const 14 Datagram const 0 0 844 0 0 0 0 0 0 0 0 0 0

852 16 SSWriter const * 0 8576 16 SSWriter const * 16 SSWriter const * 0 0 853 0 0 0 0 0 0 0 0 0 0

853 14 SSWriter const 0 8832 14 SSWriter const 14 SSWriter const 0 0 793 0 0 0 0 0 0 0 0 0 0

854 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

855 15 ISocketStream * 0 8576 15 ISocketStream * 15 ISocketStream * 0 0 794 0 0 0 0 0 0 0 0 0 0

856 9 istream * 0 8576 14 std::istream * 14 std::istream * 0 0 795 0 0 0 0 0 0 0 0 0 0

857 15 OSocketStream * 0 8576 15 OSocketStream * 15 OSocketStream * 0 0 797 0 0 0 0 0 0 0 0 0 0

858 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 798 0 0 0 0 0 0 0 0 0 0

859 14 SocketStream * 0 8576 14 SocketStream * 14 SocketStream * 0 0 799 0 0 0 0 0 0 0 0 0 0

860 20 SocketStream const * 0 8576 20 SocketStream const * 20 SocketStream const * 0 0 861 0 0 0 0 0 0 0 0 0 0

861 18 SocketStream const 0 8832 18 SocketStream const 18 SocketStream const 0 0 799 0 0 0 0 0 0 0 0 0 0

862 10 iostream * 0 8576 15 std::iostream * 15 std::iostream * 0 0 800 0 0 0 0 0 0 0 0 0 0

863 9 URLSpec * 0 8576 9 URLSpec * 9 URLSpec * 0 0 801 0 0 0 0 0 0 0 0 0 0

864 15 URLSpec const * 0 8576 15 URLSpec const * 15 URLSpec const * 0 0 820 0 0 0 0 0 0 0 0 0 0

865 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 866 0 0 0 0 0 0 0 0 0 0

866 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 867 0 0 0 0 0 0 0 0 0 0

867 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

868 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

869 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 870 0 0 0 0 0 0 0 0 0 0

870 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

871 4 char 0 8194 4 char 4 char 0 5 0 0 0 0 0 0 0 0 0 0 0

872 10 HTTPEnum * 0 8576 10 HTTPEnum * 10 HTTPEnum * 0 0 807 0 0 0 0 0 0 0 0 0 0

873 16 HTTPEnum const * 0 8576 16 HTTPEnum const * 16 HTTPEnum const * 0 0 874 0 0 0 0 0 0 0 0 0 0

874 14 HTTPEnum const 0 8832 14 HTTPEnum const 14 HTTPEnum const 0 0 807 0 0 0 0 0 0 0 0 0 0

875 10 HTTPDate * 0 8576 10 HTTPDate * 10 HTTPDate * 0 0 810 0 0 0 0 0 0 0 0 0 0

876 16 HTTPDate const * 0 8576 16 HTTPDate const * 16 HTTPDate const * 0 0 822 0 0 0 0 0 0 0 0 0 0

877 6 time_t 0 2105344 6 time_t 6 time_t 0 0 878 0 0 0 0 0 0 0 0 0 0

878 8 long int 0 8210 8 long int 8 long int 0 1 0 0 0 0 0 0 0 0 0 0 0

879 12 HTTPCookie * 0 8576 12 HTTPCookie * 12 HTTPCookie * 0 0 811 0 0 0 0 0 0 0 0 0 0

880 18 HTTPCookie const * 0 8576 18 HTTPCookie const * 18 HTTPCookie const * 0 0 881 0 0 0 0 0 0 0 0 0 0

881 16 HTTPCookie const 0 8832 16 HTTPCookie const 16 HTTPCookie const 0 0 811 0 0 0 0 0 0 0 0 0 0

882 12 HTTPClient * 0 8576 12 HTTPClient * 12 HTTPClient * 0 0 813 0 0 0 0 0 0 0 0 0 0

883 18 HTTPClient const * 0 8576 18 HTTPClient const * 18 HTTPClient const * 0 0 884 0 0 0 0 0 0 0 0 0 0

884 16 HTTPClient const 0 8832 16 HTTPClient const 16 HTTPClient const 0 0 813 0 0 0 0 0 0 0 0 0 0

885 13 HTTPChannel * 0 8576 13 HTTPChannel * 13 HTTPChannel * 0 0 823 0 0 0 0 0 0 0 0 0 0

886 15 HTTPEntityTag * 0 8576 15 HTTPEntityTag * 15 HTTPEntityTag * 0 0 816 0 0 0 0 0 0 0 0 0 0

887 21 HTTPEntityTag const * 0 8576 21 HTTPEntityTag const * 21 HTTPEntityTag const * 0 0 821 0 0 0 0 0 0 0 0 0 0

888 14 DocumentSpec * 0 8576 14 DocumentSpec * 14 DocumentSpec * 0 0 817 0 0 0 0 0 0 0 0 0 0

889 20 DocumentSpec const * 0 8576 20 DocumentSpec const * 20 DocumentSpec const * 0 0 890 0 0 0 0 0 0 0 0 0 0

890 18 DocumentSpec const 0 8832 18 DocumentSpec const 18 DocumentSpec const 0 0 817 0 0 0 0 0 0 0 0 0 0

891 19 HTTPChannel const * 0 8576 19 HTTPChannel const * 19 HTTPChannel const * 0 0 892 0 0 0 0 0 0 0 0 0 0

892 17 HTTPChannel const 0 8832 17 HTTPChannel const 17 HTTPChannel const 0 0 823 0 0 0 0 0 0 0 0 0 0

893 10 streamsize 0 2105344 15 std::streamsize 15 std::streamsize 0 0 894 0 0 0 0 0 0 0 0 0 0

894 13 long long int 0 8226 13 long long int 13 long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

895 9 Ramfile * 0 8576 9 Ramfile * 9 Ramfile * 0 0 896 0 0 0 0 0 0 0 0 0 0

896 7 Ramfile 0 2048 7 Ramfile 7 Ramfile 0 0 0 0 0 0 0 0 0 0 0 0 0

897 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

898 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 897 0 0 0 0 0 0 0 0 0 0

899 14 Decompressor * 0 8576 14 Decompressor * 14 Decompressor * 0 0 826 0 0 0 0 0 0 0 0 0 0

900 20 Decompressor const * 0 8576 20 Decompressor const * 20 Decompressor const * 0 0 901 0 0 0 0 0 0 0 0 0 0

901 18 Decompressor const 0 8832 18 Decompressor const 18 Decompressor const 0 0 826 0 0 0 0 0 0 0 0 0 0

902 12 DownloadDb * 0 8576 12 DownloadDb * 12 DownloadDb * 0 0 829 0 0 0 0 0 0 0 0 0 0

903 18 DownloadDb const * 0 8576 18 DownloadDb const * 18 DownloadDb const * 0 0 904 0 0 0 0 0 0 0 0 0 0

904 16 DownloadDb const 0 8832 16 DownloadDb const 16 DownloadDb const 0 0 829 0 0 0 0 0 0 0 0 0 0

905 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 867 0 0 0 0 0 0 0 0 0 0

906 5 Phase 0 2105344 5 Phase 5 Phase 0 0 827 0 0 0 0 0 0 0 0 0 0

907 7 HashVal 0 2048 7 HashVal 7 HashVal 0 0 0 0 0 0 0 0 0 0 0 0 109
/**
 * Stores a 128-bit value that represents the hashed contents (typically MD5)
 * of a file or buffer.
 */

908 9 HashVal * 0 8576 9 HashVal * 9 HashVal * 0 0 907 0 0 0 0 0 0 0 0 0 0

909 2 Db 0 1312768 14 DownloadDb::Db 14 DownloadDb::Db 829 0 0 0 0 0 0 0 0 0 0 0 0

910 4 Db * 0 8576 16 DownloadDb::Db * 16 DownloadDb::Db * 0 0 909 0 0 0 0 0 0 0 0 0 0

911 15 HashVal const * 0 8576 15 HashVal const * 15 HashVal const * 0 0 912 0 0 0 0 0 0 0 0 0 0

912 13 HashVal const 0 8832 13 HashVal const 13 HashVal const 0 0 907 0 0 0 0 0 0 0 0 0 0

913 11 Extractor * 0 8576 11 Extractor * 11 Extractor * 0 0 831 0 0 0 0 0 0 0 0 0 0

914 17 Extractor const * 0 8576 17 Extractor const * 17 Extractor const * 0 0 915 0 0 0 0 0 0 0 0 0 0

915 15 Extractor const 0 8832 15 Extractor const 15 Extractor const 0 0 831 0 0 0 0 0 0 0 0 0 0

916 17 MultiplexStream * 0 8576 17 MultiplexStream * 17 MultiplexStream * 0 0 832 0 0 0 0 0 0 0 0 0 0

917 6 FILE * 0 8576 6 FILE * 6 FILE * 0 0 918 0 0 0 0 0 0 0 0 0 0

918 4 FILE 0 1024 4 FILE 4 FILE 0 0 0 0 0 0 0 0 0 0 0 0 0

919 22 VirtualFileMountHTTP * 0 8576 22 VirtualFileMountHTTP * 22 VirtualFileMountHTTP * 0 0 835 0 0 0 0 0 0 0 0 0 0

920 28 VirtualFileMountHTTP const * 0 8576 28 VirtualFileMountHTTP const * 28 VirtualFileMountHTTP const * 0 0 921 0 0 0 0 0 0 0 0 0 0

921 26 VirtualFileMountHTTP const 0 8832 26 VirtualFileMountHTTP const 26 VirtualFileMountHTTP const 0 0 835 0 0 0 0 0 0 0 0 0 0

922 9 Patcher * 0 8576 9 Patcher * 9 Patcher * 0 0 837 0 0 0 0 0 0 0 0 0 0

923 15 Patcher const * 0 8576 15 Patcher const * 15 Patcher const * 0 0 924 0 0 0 0 0 0 0 0 0 0

924 13 Patcher const 0 8832 13 Patcher const 13 Patcher const 0 0 837 0 0 0 0 0 0 0 0 0 0

925 8 Buffer * 0 8576 8 Buffer * 8 Buffer * 0 0 926 0 0 0 0 0 0 0 0 0 0

926 6 Buffer 0 2048 6 Buffer 6 Buffer 0 0 0 0 0 0 0 0 0 0 0 0 10
/**
 *
 */

927 14 StringStream * 0 8576 14 StringStream * 14 StringStream * 0 0 838 0 0 0 0 0 0 0 0 0 0

928 17 unsigned long int 0 8214 17 unsigned long int 17 unsigned long int 0 1 0 0 0 0 0 0 0 0 0 0 0

0
23
929 6 scheme 0 6 802 459 473 0 0 0 0 0 0 15 URLSpec::scheme 0

930 9 authority 0 6 802 460 474 0 0 0 0 0 0 18 URLSpec::authority 0

931 8 username 0 6 802 461 475 0 0 0 0 0 0 17 URLSpec::username 0

932 6 server 0 6 802 462 476 0 0 0 0 0 0 15 URLSpec::server 0

933 4 port 0 6 804 464 477 0 0 0 0 0 0 13 URLSpec::port 0

934 15 server_and_port 0 6 802 465 478 0 0 0 0 0 0 24 URLSpec::server_and_port 0

935 4 path 0 6 802 468 479 0 0 0 0 0 0 13 URLSpec::path 0

936 5 query 0 6 802 469 480 0 0 0 0 0 0 14 URLSpec::query 0

937 3 ssl 0 2 806 471 0 0 0 0 0 0 0 12 URLSpec::ssl 0

938 4 name 0 6 812 519 518 0 0 0 0 0 0 16 HTTPCookie::name 0

939 5 value 0 6 812 521 520 0 0 0 0 0 0 17 HTTPCookie::value 0

940 6 domain 0 6 812 523 522 0 0 0 0 0 0 18 HTTPCookie::domain 0

941 4 path 0 6 812 525 524 0 0 0 0 0 0 16 HTTPCookie::path 0

942 7 expires 0 30 810 529 526 528 527 0 0 0 0 19 HTTPCookie::expires 0

943 6 secure 0 6 806 531 530 0 0 0 0 0 0 18 HTTPCookie::secure 0

944 3 url 0 6 820 607 606 0 0 0 0 0 0 17 DocumentSpec::url 0

945 3 tag 0 30 821 610 608 609 611 0 0 0 0 17 DocumentSpec::tag 0

946 4 date 0 30 822 614 612 613 615 0 0 0 0 18 DocumentSpec::date 0

947 12 request_mode 0 6 818 617 616 0 0 0 0 0 0 26 DocumentSpec::request_mode 0

948 13 cache_control 0 6 819 619 618 0 0 0 0 0 0 27 DocumentSpec::cache_control 0

949 8 progress 0 2 827 707 0 0 0 0 0 0 0 22 Decompressor::progress 0

950 8 progress 0 2 827 765 0 0 0 0 0 0 0 19 Extractor::progress 0

951 4 data 0 6 839 789 790 0 0 0 0 0 0 18 StringStream::data 0

1
952 18 get_redirect_steps 0 637 638 31 HTTPChannel::get_redirect_steps 0

