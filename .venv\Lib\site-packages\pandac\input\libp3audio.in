1730992849
3 3
10 libp3audio 4 M_1l 12 panda3d.core 
93
94 16 FilterProperties 0 260 187 34 FilterProperties::FilterProperties 0 2 1 2 0
120
FilterProperties::FilterProperties(void);
inline FilterProperties::FilterProperties(FilterProperties const &) = default;

95 5 clear 0 4 187 23 FilterProperties::clear 0 1 3 42
/**
 * Removes all DSP postprocessing.
 */
42
inline void FilterProperties::clear(void);

96 11 add_lowpass 0 4 187 29 FilterProperties::add_lowpass 0 1 4 60
/**
 * Add a lowpass filter to the end of the DSP chain.
 */
92
inline void FilterProperties::add_lowpass(PN_stdfloat cutoff_freq, PN_stdfloat resonance_q);

97 12 add_highpass 0 4 187 30 FilterProperties::add_highpass 0 1 5 61
/**
 * Add a highpass filter to the end of the DSP chain.
 */
93
inline void FilterProperties::add_highpass(PN_stdfloat cutoff_freq, PN_stdfloat resonance_q);

98 8 add_echo 0 4 187 26 FilterProperties::add_echo 0 1 6 57
/**
 * Add a echo filter to the end of the DSP chain.
 */
122
inline void FilterProperties::add_echo(PN_stdfloat drymix, PN_stdfloat wetmix, PN_stdfloat delay, PN_stdfloat decayratio);

99 10 add_flange 0 4 187 28 FilterProperties::add_flange 0 1 7 59
/**
 * Add a flange filter to the end of the DSP chain.
 */
118
inline void FilterProperties::add_flange(PN_stdfloat drymix, PN_stdfloat wetmix, PN_stdfloat depth, PN_stdfloat rate);

100 11 add_distort 0 4 187 29 FilterProperties::add_distort 0 1 8 60
/**
 * Add a distort filter to the end of the DSP chain.
 */
61
inline void FilterProperties::add_distort(PN_stdfloat level);

101 13 add_normalize 0 4 187 31 FilterProperties::add_normalize 0 1 9 62
/**
 * Add a normalize filter to the end of the DSP chain.
 */
109
inline void FilterProperties::add_normalize(PN_stdfloat fadetime, PN_stdfloat threshold, PN_stdfloat maxamp);

102 11 add_parameq 0 4 187 29 FilterProperties::add_parameq 0 1 10 60
/**
 * Add a parameq filter to the end of the DSP chain.
 */
108
inline void FilterProperties::add_parameq(PN_stdfloat center_freq, PN_stdfloat bandwidth, PN_stdfloat gain);

103 14 add_pitchshift 0 4 187 32 FilterProperties::add_pitchshift 0 1 11 63
/**
 * Add a pitchshift filter to the end of the DSP chain.
 */
106
inline void FilterProperties::add_pitchshift(PN_stdfloat pitch, PN_stdfloat fftsize, PN_stdfloat overlap);

104 10 add_chorus 0 4 187 28 FilterProperties::add_chorus 0 1 12 59
/**
 * Add a chorus filter to the end of the DSP chain.
 */
171
inline void FilterProperties::add_chorus(PN_stdfloat drymix, PN_stdfloat wet1, PN_stdfloat wet2, PN_stdfloat wet3, PN_stdfloat delay, PN_stdfloat rate, PN_stdfloat depth);

105 13 add_sfxreverb 0 4 187 31 FilterProperties::add_sfxreverb 0 1 13 59
/**
 * Add a reverb filter to the end of the DSP chain.
 */
491
inline void FilterProperties::add_sfxreverb(PN_stdfloat drylevel = 0, PN_stdfloat room = -10000, PN_stdfloat roomhf = 0, PN_stdfloat decaytime = 1, PN_stdfloat decayhfratio = 0.5, PN_stdfloat reflectionslevel = -10000, PN_stdfloat reflectionsdelay = 0.020000000000000005, PN_stdfloat reverblevel = 0, PN_stdfloat reverbdelay = 0.04000000000000001, PN_stdfloat diffusion = 100, PN_stdfloat density = 100, PN_stdfloat hfreference = 5000, PN_stdfloat roomlf = 0, PN_stdfloat lfreference = 250);

106 12 add_compress 0 4 187 30 FilterProperties::add_compress 0 1 14 61
/**
 * Add a compress filter to the end of the DSP chain.
 */
131
inline void FilterProperties::add_compress(PN_stdfloat threshold, PN_stdfloat attack, PN_stdfloat release, PN_stdfloat gainmakeup);

107 14 get_class_type 0 4 187 32 FilterProperties::get_class_type 0 1 15 0
57
static TypeHandle FilterProperties::get_class_type(void);

108 4 play 0 6 189 16 AudioSound::play 0 1 16 350
// For best compatibility, set the loop_count, volume, and balance, prior to
// calling play().  You may set them while they're playing, but it's
// implementation specific whether you get the results.  - Calling play() a
// second time on the same sound before it is finished will start the sound
// again (creating a skipping or stuttering effect).
40
virtual void AudioSound::play(void) = 0;

109 4 stop 0 6 189 16 AudioSound::stop 0 1 17 0
40
virtual void AudioSound::stop(void) = 0;

110 8 set_loop 0 6 189 20 AudioSound::set_loop 0 1 18 65
// loop: false = play once; true = play forever.  inits to false.
56
virtual void AudioSound::set_loop(bool loop = true) = 0;

111 8 get_loop 0 6 189 20 AudioSound::get_loop 0 1 19 0
50
virtual bool AudioSound::get_loop(void) const = 0;

112 14 set_loop_count 0 6 189 26 AudioSound::set_loop_count 0 1 20 73
// loop_count: 0 = forever; 1 = play once; n = play n times.  inits to 1.
78
virtual void AudioSound::set_loop_count(unsigned long int loop_count = 1) = 0;

113 14 get_loop_count 0 6 189 26 AudioSound::get_loop_count 0 1 21 0
69
virtual unsigned long int AudioSound::get_loop_count(void) const = 0;

114 8 set_time 0 6 189 20 AudioSound::set_time 0 1 22 769
/**
   * Control time position within the sound, in seconds.  This is similar (in
   * concept) to the seek position within a file.  The value starts at 0.0 (the
   * default) and ends at the value given by the length() method.
   *
   * The current time position will not change while the sound is playing; you
   * must call play() again to effect the change.  To play the same sound from
   * a time offset a second time, explicitly set the time position again.  When
   * looping, the second and later loops will start from the beginning of the
   * sound.
   *
   * If a sound is playing, calling get_time() repeatedly will return different
   * results over time.  e.g.
   * @code
   * PN_stdfloat percent_complete = s.get_time() / s.length();
   * @endcode
   */
68
virtual void AudioSound::set_time(PN_stdfloat start_time = 0.0) = 0;

115 8 get_time 0 6 189 20 AudioSound::get_time 0 1 23 0
57
virtual PN_stdfloat AudioSound::get_time(void) const = 0;

116 10 set_volume 0 6 189 22 AudioSound::set_volume 0 1 24 45
// 0 = minimum; 1.0 = maximum.  inits to 1.0.
66
virtual void AudioSound::set_volume(PN_stdfloat volume = 1.0) = 0;

117 10 get_volume 0 6 189 22 AudioSound::get_volume 0 1 25 0
59
virtual PN_stdfloat AudioSound::get_volume(void) const = 0;

118 11 set_balance 0 6 189 23 AudioSound::set_balance 0 1 26 68
// -1.0 is hard left 0.0 is centered 1.0 is hard right inits to 0.0.
74
virtual void AudioSound::set_balance(PN_stdfloat balance_right = 0.0) = 0;

119 11 get_balance 0 6 189 23 AudioSound::get_balance 0 1 27 0
60
virtual PN_stdfloat AudioSound::get_balance(void) const = 0;

120 13 set_play_rate 0 6 189 25 AudioSound::set_play_rate 0 1 28 62
// play_rate is any positive PN_stdfloat value.  inits to 1.0.
72
virtual void AudioSound::set_play_rate(PN_stdfloat play_rate = 1.0) = 0;

121 13 get_play_rate 0 6 189 25 AudioSound::get_play_rate 0 1 29 0
62
virtual PN_stdfloat AudioSound::get_play_rate(void) const = 0;

122 10 set_active 0 6 189 22 AudioSound::set_active 0 1 30 28
// inits to manager's state.
58
virtual void AudioSound::set_active(bool flag = true) = 0;

123 10 get_active 0 6 189 22 AudioSound::get_active 0 1 31 0
52
virtual bool AudioSound::get_active(void) const = 0;

124 18 set_finished_event 0 6 189 30 AudioSound::set_finished_event 0 1 32 126
// Set (or clear) the event that will be thrown when the sound finishes
// playing.  To clear the event, pass an empty string.
74
virtual void AudioSound::set_finished_event(std::string const &event) = 0;

125 18 get_finished_event 0 6 189 30 AudioSound::get_finished_event 0 1 33 0
74
virtual std::string const &AudioSound::get_finished_event(void) const = 0;

126 8 get_name 0 6 189 20 AudioSound::get_name 0 1 34 47
// There is no set_name(), this is intentional.
64
virtual std::string const &AudioSound::get_name(void) const = 0;

127 6 length 0 6 189 18 AudioSound::length 0 1 35 35
// return: playing time in seconds.
55
virtual PN_stdfloat AudioSound::length(void) const = 0;

128 17 set_3d_attributes 0 6 189 29 AudioSound::set_3d_attributes 0 1 36 179
// Controls the position of this sound's emitter.  px, py and pz are the
// emitter's position.  vx, vy and vz are the emitter's velocity in UNITS
// PER SECOND (default: meters).
139
virtual void AudioSound::set_3d_attributes(PN_stdfloat px, PN_stdfloat py, PN_stdfloat pz, PN_stdfloat vx, PN_stdfloat vy, PN_stdfloat vz);

129 17 get_3d_attributes 0 6 189 29 AudioSound::get_3d_attributes 0 0 0
145
virtual void AudioSound::get_3d_attributes(PN_stdfloat *px, PN_stdfloat *py, PN_stdfloat *pz, PN_stdfloat *vx, PN_stdfloat *vy, PN_stdfloat *vz);

130 19 set_3d_min_distance 0 6 189 31 AudioSound::set_3d_min_distance 0 1 37 168
// Controls the distance (in units) that this sound begins to fall off.
// Also affects the rate it falls off.  Default is 1.0 CloserFaster, <1.0
// FartherSlower, >1.0
63
virtual void AudioSound::set_3d_min_distance(PN_stdfloat dist);

131 19 get_3d_min_distance 0 6 189 31 AudioSound::get_3d_min_distance 0 1 38 0
64
virtual PN_stdfloat AudioSound::get_3d_min_distance(void) const;

132 19 set_3d_max_distance 0 6 189 31 AudioSound::set_3d_max_distance 0 1 39 222
// Controls the maximum distance (in units) that this sound stops falling
// off.  The sound does not stop at that point, it just doesn't get any
// quieter.  You should rarely need to adjust this.  Default is 1000000000.0
63
virtual void AudioSound::set_3d_max_distance(PN_stdfloat dist);

133 19 get_3d_max_distance 0 6 189 31 AudioSound::get_3d_max_distance 0 1 40 0
64
virtual PN_stdfloat AudioSound::get_3d_max_distance(void) const;

134 15 get_speaker_mix 0 6 189 27 AudioSound::get_speaker_mix 0 1 41 304
// speaker_mix and speaker_level(s) serve the same purpose.
// speaker_mix is for use with FMOD. speaker_level(s) is for use with
// Miles.  Both interfaces exist because of a significant difference in the
// two APIs.  Hopefully the difference can be reconciled into a single
// interface at some point.
61
virtual PN_stdfloat AudioSound::get_speaker_mix(int speaker);

135 15 set_speaker_mix 0 6 189 27 AudioSound::set_speaker_mix 0 1 42 0
215
virtual void AudioSound::set_speaker_mix(PN_stdfloat frontleft, PN_stdfloat frontright, PN_stdfloat center, PN_stdfloat sub, PN_stdfloat backleft, PN_stdfloat backright, PN_stdfloat sideleft, PN_stdfloat sideright);

136 17 get_speaker_level 0 6 189 29 AudioSound::get_speaker_level 0 1 43 0
61
virtual PN_stdfloat AudioSound::get_speaker_level(int index);

137 18 set_speaker_levels 0 6 189 30 AudioSound::set_speaker_levels 0 1 44 0
280
virtual void AudioSound::set_speaker_levels(PN_stdfloat level1, PN_stdfloat level2 = -1.0, PN_stdfloat level3 = -1.0, PN_stdfloat level4 = -1.0, PN_stdfloat level5 = -1.0, PN_stdfloat level6 = -1.0, PN_stdfloat level7 = -1.0, PN_stdfloat level8 = -1.0, PN_stdfloat level9 = -1.0);

138 12 get_priority 0 6 189 24 AudioSound::get_priority 0 1 45 0
43
virtual int AudioSound::get_priority(void);

139 12 set_priority 0 6 189 24 AudioSound::set_priority 0 1 46 0
52
virtual void AudioSound::set_priority(int priority);

140 17 configure_filters 0 6 189 29 AudioSound::configure_filters 0 1 47 0
69
virtual bool AudioSound::configure_filters(FilterProperties *config);

141 6 status 0 6 189 18 AudioSound::status 0 1 48 0
67
virtual AudioSound::SoundStatus AudioSound::status(void) const = 0;

142 6 output 0 6 189 18 AudioSound::output 0 1 49 0
57
virtual void AudioSound::output(std::ostream &out) const;

143 5 write 0 6 189 17 AudioSound::write 0 1 50 0
56
virtual void AudioSound::write(std::ostream &out) const;

144 14 get_class_type 0 4 189 26 AudioSound::get_class_type 0 1 51 0
51
static TypeHandle AudioSound::get_class_type(void);

145 17 get_speaker_setup 0 6 191 31 AudioManager::get_speaker_setup 0 1 52 0
50
virtual int AudioManager::get_speaker_setup(void);

146 17 set_speaker_setup 0 6 191 31 AudioManager::set_speaker_setup 0 1 53 0
84
virtual void AudioManager::set_speaker_setup(AudioManager::SpeakerModeCategory cat);

147 17 configure_filters 0 6 191 31 AudioManager::configure_filters 0 1 54 0
71
virtual bool AudioManager::configure_filters(FilterProperties *config);

148 19 create_AudioManager 0 4 191 33 AudioManager::create_AudioManager 0 1 55 0
73
static PointerTo< AudioManager > AudioManager::create_AudioManager(void);

149 8 shutdown 0 6 191 22 AudioManager::shutdown 0 1 56 0
42
virtual void AudioManager::shutdown(void);

150 8 is_valid 0 6 191 22 AudioManager::is_valid 0 1 57 408
// If you're interested in knowing whether this audio manager is valid,
// here's the call to do it.  It is not necessary to check whether the audio
// manager is valid before making other calls.  You are free to use an
// invalid sound manager, you may get silent sounds from it though.  The
// sound manager and the sounds it creates should not crash the application
// even when the objects are not valid.
46
virtual bool AudioManager::is_valid(void) = 0;

151 9 get_sound 0 6 191 23 AudioManager::get_sound 0 2 58 59 15
// Get a sound:
300
virtual PointerTo< AudioSound > AudioManager::get_sound(Filename const &file_name, bool positional = false, int mode = ::AudioManager::SM_heuristic) = 0;
virtual PointerTo< AudioSound > AudioManager::get_sound(MovieAudio *source, bool positional = false, int mode = ::AudioManager::SM_heuristic) = 0;

152 14 get_null_sound 0 4 191 28 AudioManager::get_null_sound 0 1 60 0
59
PointerTo< AudioSound > AudioManager::get_null_sound(void);

153 13 uncache_sound 0 6 191 27 AudioManager::uncache_sound 0 1 61 278
// Tell the AudioManager there is no need to keep this one cached.  This
// doesn't break any connection between AudioSounds that have already given
// by get_sound() from this manager.  It's only affecting whether the
// AudioManager keeps a copy of the sound in its poolcache.
72
virtual void AudioManager::uncache_sound(Filename const &file_name) = 0;

154 11 clear_cache 0 6 191 25 AudioManager::clear_cache 0 1 62 0
49
virtual void AudioManager::clear_cache(void) = 0;

155 15 set_cache_limit 0 6 191 29 AudioManager::set_cache_limit 0 1 63 0
67
virtual void AudioManager::set_cache_limit(unsigned int count) = 0;

156 15 get_cache_limit 0 6 191 29 AudioManager::get_cache_limit 0 1 64 0
67
virtual unsigned int AudioManager::get_cache_limit(void) const = 0;

157 10 set_volume 0 6 191 24 AudioManager::set_volume 0 1 65 194
// Control volume: FYI: If you start a sound with the volume off and turn
// the volume up later, you'll hear the sound playing at that late point.  0
// = minimum; 1.0 = maximum.  inits to 1.0.
62
virtual void AudioManager::set_volume(PN_stdfloat volume) = 0;

158 10 get_volume 0 6 191 24 AudioManager::get_volume 0 1 66 0
61
virtual PN_stdfloat AudioManager::get_volume(void) const = 0;

159 10 set_active 0 6 191 24 AudioManager::set_active 0 1 67 360
// Turn the manager on or off.  If you play a sound while the manager is
// inactive, it won't start.  If you deactivate the manager while sounds are
// playing, they'll stop.  If you activate the manager while looping sounds
// are playing (those that have a loop_count of zero), they will start
// playing from the beginning of their loop.  Defaults to true.
53
virtual void AudioManager::set_active(bool flag) = 0;

160 10 get_active 0 6 191 24 AudioManager::get_active 0 1 68 0
54
virtual bool AudioManager::get_active(void) const = 0;

161 26 set_concurrent_sound_limit 0 6 191 40 AudioManager::set_concurrent_sound_limit 0 1 69 343
// This controls the number of sounds that you allow at once.  This is more
// of a user choice -- it avoids talk over and the creation of a cacophony.
// It can also be used to help performance.  0 == unlimited.  1 == mutually
// exclusive (one sound at a time).  Which is an example of: n == allow n
// sounds to be playing at the same time.
82
virtual void AudioManager::set_concurrent_sound_limit(unsigned int limit = 0) = 0;

162 26 get_concurrent_sound_limit 0 6 191 40 AudioManager::get_concurrent_sound_limit 0 1 70 0
78
virtual unsigned int AudioManager::get_concurrent_sound_limit(void) const = 0;

163 24 reduce_sounds_playing_to 0 6 191 38 AudioManager::reduce_sounds_playing_to 0 1 71 369
// This is likely to be a utility function for the concurrent_sound_limit
// options.  It is exposed as an API, because it's reasonable that it may be
// useful to be here.  It reduces the number of concurrently playing sounds
// to count by some implementation specific means.  If the number of sounds
// currently playing is at or below count then there is no effect.
76
virtual void AudioManager::reduce_sounds_playing_to(unsigned int count) = 0;

164 15 stop_all_sounds 0 6 191 29 AudioManager::stop_all_sounds 0 1 72 183
// Stop playback on all sounds managed by this manager.  This is effectively
// the same as reduce_sounds_playing_to(0), but this call may be for
// efficient on some implementations.
53
virtual void AudioManager::stop_all_sounds(void) = 0;

165 6 update 0 6 191 20 AudioManager::update 0 1 73 76
// This should be called every frame.  Failure to call could cause problems.
40
virtual void AudioManager::update(void);

166 32 audio_3d_set_listener_attributes 0 6 191 46 AudioManager::audio_3d_set_listener_attributes 0 1 74 326
// This controls the "set of ears" that listens to 3D spacialized sound px,
// py, pz are position coordinates.  vx, vy, vz are a velocity vector in
// UNITS PER SECOND (default: meters). fx, fy and fz are the respective
// components of a unit forward-vector ux, uy and uz are the respective
// components of a unit up-vector
252
virtual void AudioManager::audio_3d_set_listener_attributes(PN_stdfloat px, PN_stdfloat py, PN_stdfloat pz, PN_stdfloat vx, PN_stdfloat vy, PN_stdfloat vz, PN_stdfloat fx, PN_stdfloat fy, PN_stdfloat fz, PN_stdfloat ux, PN_stdfloat uy, PN_stdfloat uz);

167 32 audio_3d_get_listener_attributes 0 6 191 46 AudioManager::audio_3d_get_listener_attributes 0 0 0
264
virtual void AudioManager::audio_3d_get_listener_attributes(PN_stdfloat *px, PN_stdfloat *py, PN_stdfloat *pz, PN_stdfloat *vx, PN_stdfloat *vy, PN_stdfloat *vz, PN_stdfloat *fx, PN_stdfloat *fy, PN_stdfloat *fz, PN_stdfloat *ux, PN_stdfloat *uy, PN_stdfloat *uz);

168 28 audio_3d_set_distance_factor 0 6 191 42 AudioManager::audio_3d_set_distance_factor 0 1 75 305
// Control the "relative scale that sets the distance factor" units for 3D
// spacialized audio. This is a float in units-per-meter. Default value is
// 1.0, which means that Panda units are understood as meters; for e.g.
// feet, set 3.28. This factor is applied only to Fmod and OpenAL at the
// moment.
76
virtual void AudioManager::audio_3d_set_distance_factor(PN_stdfloat factor);

169 28 audio_3d_get_distance_factor 0 6 191 42 AudioManager::audio_3d_get_distance_factor 0 1 76 0
75
virtual PN_stdfloat AudioManager::audio_3d_get_distance_factor(void) const;

170 27 audio_3d_set_doppler_factor 0 6 191 41 AudioManager::audio_3d_set_doppler_factor 0 1 77 123
// Control the presence of the Doppler effect.  Default is 1.0 Exaggerated
// Doppler, use >1.0 Diminshed Doppler, use <1.0
75
virtual void AudioManager::audio_3d_set_doppler_factor(PN_stdfloat factor);

171 27 audio_3d_get_doppler_factor 0 6 191 41 AudioManager::audio_3d_get_doppler_factor 0 1 78 0
74
virtual PN_stdfloat AudioManager::audio_3d_get_doppler_factor(void) const;

172 28 audio_3d_set_drop_off_factor 0 6 191 42 AudioManager::audio_3d_set_drop_off_factor 0 1 79 155
// Exaggerate or diminish the effect of distance on sound.  Default is 1.0
// Valid range is 0 to 10 Faster drop off, use >1.0 Slower drop off, use
// <1.0
76
virtual void AudioManager::audio_3d_set_drop_off_factor(PN_stdfloat factor);

173 28 audio_3d_get_drop_off_factor 0 6 191 42 AudioManager::audio_3d_get_drop_off_factor 0 1 80 0
75
virtual PN_stdfloat AudioManager::audio_3d_get_drop_off_factor(void) const;

174 16 get_dls_pathname 0 4 191 30 AudioManager::get_dls_pathname 0 1 81 0
53
static Filename AudioManager::get_dls_pathname(void);

175 6 output 0 6 191 20 AudioManager::output 0 1 82 0
59
virtual void AudioManager::output(std::ostream &out) const;

176 5 write 0 6 191 19 AudioManager::write 0 1 83 0
58
virtual void AudioManager::write(std::ostream &out) const;

177 25 set_speaker_configuration 0 6 191 39 AudioManager::set_speaker_configuration 0 1 84 52
// set_speaker_configuration is a Miles only method.
322
virtual void AudioManager::set_speaker_configuration(LVecBase3 *speaker1, LVecBase3 *speaker2 = nullptr, LVecBase3 *speaker3 = nullptr, LVecBase3 *speaker4 = nullptr, LVecBase3 *speaker5 = nullptr, LVecBase3 *speaker6 = nullptr, LVecBase3 *speaker7 = nullptr, LVecBase3 *speaker8 = nullptr, LVecBase3 *speaker9 = nullptr);

178 14 get_class_type 0 4 191 28 AudioManager::get_class_type 0 1 85 0
53
static TypeHandle AudioManager::get_class_type(void);

179 16 AudioLoadRequest 0 260 196 34 AudioLoadRequest::AudioLoadRequest 0 2 86 87 118
/**
 * Create a new AudioLoadRequest, and add it to the loader via load_async(),
 * to begin an asynchronous load.
 */
205
inline explicit AudioLoadRequest::AudioLoadRequest(AudioManager *audio_manager, std::string const &filename, bool positional);
inline AudioLoadRequest::AudioLoadRequest(AudioLoadRequest const &) = default;

180 17 get_audio_manager 0 4 196 35 AudioLoadRequest::get_audio_manager 0 1 88 90
/**
 * Returns the AudioManager that will serve this asynchronous
 * AudioLoadRequest.
 */
69
inline AudioManager *AudioLoadRequest::get_audio_manager(void) const;

181 12 get_filename 0 4 196 30 AudioLoadRequest::get_filename 0 1 89 83
/**
 * Returns the filename associated with this asynchronous AudioLoadRequest.
 */
69
inline std::string const &AudioLoadRequest::get_filename(void) const;

182 14 get_positional 0 4 196 32 AudioLoadRequest::get_positional 0 1 90 93
/**
 * Returns the positional flag associated with this asynchronous
 * AudioLoadRequest.
 */
57
inline bool AudioLoadRequest::get_positional(void) const;

183 8 is_ready 0 4 196 26 AudioLoadRequest::is_ready 0 1 91 242
/**
 * Returns true if this request has completed, false if it is still pending.
 * When this returns true, you may retrieve the sound loaded by calling
 * get_sound().
 * Equivalent to `req.done() and not req.cancelled()`.
 * @see done()
 */
51
inline bool AudioLoadRequest::is_ready(void) const;

184 9 get_sound 0 4 196 27 AudioLoadRequest::get_sound 0 1 92 202
/**
 * Returns the sound that was loaded asynchronously, if any, or nullptr if
 * there was an error.  It is an error to call this unless done() returns
 * true.
 * @deprecated Use result() instead.
 */
59
inline AudioSound *AudioLoadRequest::get_sound(void) const;

185 14 get_class_type 0 4 196 32 AudioLoadRequest::get_class_type 0 1 93 0
57
static TypeHandle AudioLoadRequest::get_class_type(void);

186 17 ~AudioLoadRequest 0 516 196 35 AudioLoadRequest::~AudioLoadRequest 0 0 0
42
AudioLoadRequest::~AudioLoadRequest(void);

93
1 0 0 7 3 198 0 0 0 0 
2 0 0 15 3 198 0 0 0 1 6 param0 0 199  
3 0 0 4 4 201 0 0 42 /**
 * Removes all DSP postprocessing.
 */ 1 4 this 3 198  
4 0 0 4 5 201 0 0 60 /**
 * Add a lowpass filter to the end of the DSP chain.
 */ 3 4 this 3 198  11 cutoff_freq 1 202  11 resonance_q 1 202  
5 0 0 4 6 201 0 0 61 /**
 * Add a highpass filter to the end of the DSP chain.
 */ 3 4 this 3 198  11 cutoff_freq 1 202  11 resonance_q 1 202  
6 0 0 4 7 201 0 0 57 /**
 * Add a echo filter to the end of the DSP chain.
 */ 5 4 this 3 198  6 drymix 1 202  6 wetmix 1 202  5 delay 1 202  10 decayratio 1 202  
7 0 0 4 8 201 0 0 59 /**
 * Add a flange filter to the end of the DSP chain.
 */ 5 4 this 3 198  6 drymix 1 202  6 wetmix 1 202  5 depth 1 202  4 rate 1 202  
8 0 0 4 9 201 0 0 60 /**
 * Add a distort filter to the end of the DSP chain.
 */ 2 4 this 3 198  5 level 1 202  
9 0 0 4 10 201 0 0 62 /**
 * Add a normalize filter to the end of the DSP chain.
 */ 4 4 this 3 198  8 fadetime 1 202  9 threshold 1 202  6 maxamp 1 202  
10 0 0 4 11 201 0 0 60 /**
 * Add a parameq filter to the end of the DSP chain.
 */ 4 4 this 3 198  11 center_freq 1 202  9 bandwidth 1 202  4 gain 1 202  
11 0 0 4 12 201 0 0 63 /**
 * Add a pitchshift filter to the end of the DSP chain.
 */ 4 4 this 3 198  5 pitch 1 202  7 fftsize 1 202  7 overlap 1 202  
12 0 0 4 13 201 0 0 59 /**
 * Add a chorus filter to the end of the DSP chain.
 */ 8 4 this 3 198  6 drymix 1 202  4 wet1 1 202  4 wet2 1 202  4 wet3 1 202  5 delay 1 202  4 rate 1 202  5 depth 1 202  
13 0 0 4 14 201 0 0 59 /**
 * Add a reverb filter to the end of the DSP chain.
 */ 15 4 this 3 198  8 drylevel 5 202  4 room 5 202  6 roomhf 5 202  9 decaytime 5 202  12 decayhfratio 5 202  16 reflectionslevel 5 202  16 reflectionsdelay 5 202  11 reverblevel 5 202  11 reverbdelay 5 202  9 diffusion 5 202  7 density 5 202  11 hfreference 5 202  6 roomlf 5 202  11 lfreference 5 202  
14 0 0 4 15 201 0 0 61 /**
 * Add a compress filter to the end of the DSP chain.
 */ 5 4 this 3 198  9 threshold 1 202  6 attack 1 202  7 release 1 202  10 gainmakeup 1 202  
15 0 0 7 16 205 0 0 0 0 
16 0 0 4 18 201 0 0 350 // For best compatibility, set the loop_count, volume, and balance, prior to
// calling play().  You may set them while they're playing, but it's
// implementation specific whether you get the results.  - Calling play() a
// second time on the same sound before it is finished will start the sound
// again (creating a skipping or stuttering effect). 1 4 this 3 206  
17 0 0 4 19 201 0 0 0 1 4 this 3 206  
18 0 0 4 20 201 0 0 65 // loop: false = play once; true = play forever.  inits to false. 2 4 this 3 206  4 loop 5 207  
19 0 0 6 21 207 0 0 0 1 4 this 3 208  
20 0 0 4 22 201 0 0 73 // loop_count: 0 = forever; 1 = play once; n = play n times.  inits to 1. 2 4 this 3 206  10 loop_count 5 210  
21 0 0 6 23 210 0 0 0 1 4 this 3 208  
22 0 0 4 24 201 0 0 769 /**
   * Control time position within the sound, in seconds.  This is similar (in
   * concept) to the seek position within a file.  The value starts at 0.0 (the
   * default) and ends at the value given by the length() method.
   *
   * The current time position will not change while the sound is playing; you
   * must call play() again to effect the change.  To play the same sound from
   * a time offset a second time, explicitly set the time position again.  When
   * looping, the second and later loops will start from the beginning of the
   * sound.
   *
   * If a sound is playing, calling get_time() repeatedly will return different
   * results over time.  e.g.
   * @code
   * PN_stdfloat percent_complete = s.get_time() / s.length();
   * @endcode
   */ 2 4 this 3 206  10 start_time 5 202  
23 0 0 6 25 202 0 0 0 1 4 this 3 208  
24 0 0 4 26 201 0 0 45 // 0 = minimum; 1.0 = maximum.  inits to 1.0. 2 4 this 3 206  6 volume 5 202  
25 0 0 6 27 202 0 0 0 1 4 this 3 208  
26 0 0 4 28 201 0 0 68 // -1.0 is hard left 0.0 is centered 1.0 is hard right inits to 0.0. 2 4 this 3 206  13 balance_right 5 202  
27 0 0 6 29 202 0 0 0 1 4 this 3 208  
28 0 0 4 30 201 0 0 62 // play_rate is any positive PN_stdfloat value.  inits to 1.0. 2 4 this 3 206  9 play_rate 5 202  
29 0 0 6 31 202 0 0 0 1 4 this 3 208  
30 0 0 4 32 201 0 0 28 // inits to manager's state. 2 4 this 3 206  4 flag 5 207  
31 0 0 6 33 207 0 0 0 1 4 this 3 208  
32 0 0 4 34 201 0 0 126 // Set (or clear) the event that will be thrown when the sound finishes
// playing.  To clear the event, pass an empty string. 2 4 this 3 206  5 event 1 211  
33 0 0 6 35 211 0 0 0 1 4 this 3 208  
34 0 0 6 36 211 0 0 47 // There is no set_name(), this is intentional. 1 4 this 3 208  
35 0 0 6 37 202 0 0 35 // return: playing time in seconds. 1 4 this 3 208  
36 0 0 4 38 201 0 0 179 // Controls the position of this sound's emitter.  px, py and pz are the
// emitter's position.  vx, vy and vz are the emitter's velocity in UNITS
// PER SECOND (default: meters). 7 4 this 3 206  2 px 1 202  2 py 1 202  2 pz 1 202  2 vx 1 202  2 vy 1 202  2 vz 1 202  
37 0 0 4 40 201 0 0 168 // Controls the distance (in units) that this sound begins to fall off.
// Also affects the rate it falls off.  Default is 1.0 CloserFaster, <1.0
// FartherSlower, >1.0 2 4 this 3 206  4 dist 1 202  
38 0 0 6 41 202 0 0 0 1 4 this 3 208  
39 0 0 4 42 201 0 0 222 // Controls the maximum distance (in units) that this sound stops falling
// off.  The sound does not stop at that point, it just doesn't get any
// quieter.  You should rarely need to adjust this.  Default is 1000000000.0 2 4 this 3 206  4 dist 1 202  
40 0 0 6 43 202 0 0 0 1 4 this 3 208  
41 0 0 6 44 202 0 0 304 // speaker_mix and speaker_level(s) serve the same purpose.
// speaker_mix is for use with FMOD. speaker_level(s) is for use with
// Miles.  Both interfaces exist because of a significant difference in the
// two APIs.  Hopefully the difference can be reconciled into a single
// interface at some point. 2 4 this 3 206  7 speaker 1 212  
42 0 0 4 45 201 0 0 0 9 4 this 3 206  9 frontleft 1 202  10 frontright 1 202  6 center 1 202  3 sub 1 202  8 backleft 1 202  9 backright 1 202  8 sideleft 1 202  9 sideright 1 202  
43 0 0 6 46 202 0 0 0 2 4 this 3 206  5 index 1 212  
44 0 0 4 47 201 0 0 0 10 4 this 3 206  6 level1 1 202  6 level2 5 202  6 level3 5 202  6 level4 5 202  6 level5 5 202  6 level6 5 202  6 level7 5 202  6 level8 5 202  6 level9 5 202  
45 0 0 6 48 212 0 0 0 1 4 this 3 206  
46 0 0 4 49 201 0 0 0 2 4 this 3 206  8 priority 1 212  
47 0 0 6 50 207 0 0 0 2 4 this 3 206  6 config 1 198  
48 0 0 6 52 190 0 0 0 1 4 this 3 208  
49 0 0 4 53 201 0 0 0 2 4 this 3 208  3 out 1 213  
50 0 0 4 54 201 0 0 0 2 4 this 3 208  3 out 1 213  
51 0 0 7 55 205 0 0 0 0 
52 0 0 6 60 212 0 0 0 1 4 this 3 215  
53 0 0 4 61 201 0 0 0 2 4 this 3 215  3 cat 1 192  
54 0 0 6 62 207 0 0 0 2 4 this 3 215  6 config 1 198  
55 0 0 7 63 215 0 0 0 0 
56 0 0 4 64 201 0 0 0 1 4 this 3 215  
57 0 0 6 65 207 0 0 408 // If you're interested in knowing whether this audio manager is valid,
// here's the call to do it.  It is not necessary to check whether the audio
// manager is valid before making other calls.  You are free to use an
// invalid sound manager, you may get silent sounds from it though.  The
// sound manager and the sounds it creates should not crash the application
// even when the objects are not valid. 1 4 this 3 215  
58 0 0 7 66 206 0 0 15 // Get a sound: 4 4 this 3 215  9 file_name 1 216  10 positional 5 207  4 mode 5 212  
59 0 0 7 66 206 0 0 0 4 4 this 3 215  6 source 1 218  10 positional 5 207  4 mode 5 212  
60 0 0 7 67 206 0 0 0 1 4 this 3 215  
61 0 0 4 68 201 0 0 278 // Tell the AudioManager there is no need to keep this one cached.  This
// doesn't break any connection between AudioSounds that have already given
// by get_sound() from this manager.  It's only affecting whether the
// AudioManager keeps a copy of the sound in its poolcache. 2 4 this 3 215  9 file_name 1 216  
62 0 0 4 69 201 0 0 0 1 4 this 3 215  
63 0 0 4 70 201 0 0 0 2 4 this 3 215  5 count 1 220  
64 0 0 6 71 220 0 0 0 1 4 this 3 221  
65 0 0 4 72 201 0 0 194 // Control volume: FYI: If you start a sound with the volume off and turn
// the volume up later, you'll hear the sound playing at that late point.  0
// = minimum; 1.0 = maximum.  inits to 1.0. 2 4 this 3 215  6 volume 1 202  
66 0 0 6 73 202 0 0 0 1 4 this 3 221  
67 0 0 4 74 201 0 0 360 // Turn the manager on or off.  If you play a sound while the manager is
// inactive, it won't start.  If you deactivate the manager while sounds are
// playing, they'll stop.  If you activate the manager while looping sounds
// are playing (those that have a loop_count of zero), they will start
// playing from the beginning of their loop.  Defaults to true. 2 4 this 3 215  4 flag 1 207  
68 0 0 6 75 207 0 0 0 1 4 this 3 221  
69 0 0 4 76 201 0 0 343 // This controls the number of sounds that you allow at once.  This is more
// of a user choice -- it avoids talk over and the creation of a cacophony.
// It can also be used to help performance.  0 == unlimited.  1 == mutually
// exclusive (one sound at a time).  Which is an example of: n == allow n
// sounds to be playing at the same time. 2 4 this 3 215  5 limit 5 220  
70 0 0 6 77 220 0 0 0 1 4 this 3 221  
71 0 0 4 78 201 0 0 369 // This is likely to be a utility function for the concurrent_sound_limit
// options.  It is exposed as an API, because it's reasonable that it may be
// useful to be here.  It reduces the number of concurrently playing sounds
// to count by some implementation specific means.  If the number of sounds
// currently playing is at or below count then there is no effect. 2 4 this 3 215  5 count 1 220  
72 0 0 4 79 201 0 0 183 // Stop playback on all sounds managed by this manager.  This is effectively
// the same as reduce_sounds_playing_to(0), but this call may be for
// efficient on some implementations. 1 4 this 3 215  
73 0 0 4 80 201 0 0 76 // This should be called every frame.  Failure to call could cause problems. 1 4 this 3 215  
74 0 0 4 81 201 0 0 326 // This controls the "set of ears" that listens to 3D spacialized sound px,
// py, pz are position coordinates.  vx, vy, vz are a velocity vector in
// UNITS PER SECOND (default: meters). fx, fy and fz are the respective
// components of a unit forward-vector ux, uy and uz are the respective
// components of a unit up-vector 13 4 this 3 215  2 px 1 202  2 py 1 202  2 pz 1 202  2 vx 1 202  2 vy 1 202  2 vz 1 202  2 fx 1 202  2 fy 1 202  2 fz 1 202  2 ux 1 202  2 uy 1 202  2 uz 1 202  
75 0 0 4 83 201 0 0 305 // Control the "relative scale that sets the distance factor" units for 3D
// spacialized audio. This is a float in units-per-meter. Default value is
// 1.0, which means that Panda units are understood as meters; for e.g.
// feet, set 3.28. This factor is applied only to Fmod and OpenAL at the
// moment. 2 4 this 3 215  6 factor 1 202  
76 0 0 6 84 202 0 0 0 1 4 this 3 221  
77 0 0 4 85 201 0 0 123 // Control the presence of the Doppler effect.  Default is 1.0 Exaggerated
// Doppler, use >1.0 Diminshed Doppler, use <1.0 2 4 this 3 215  6 factor 1 202  
78 0 0 6 86 202 0 0 0 1 4 this 3 221  
79 0 0 4 87 201 0 0 155 // Exaggerate or diminish the effect of distance on sound.  Default is 1.0
// Valid range is 0 to 10 Faster drop off, use >1.0 Slower drop off, use
// <1.0 2 4 this 3 215  6 factor 1 202  
80 0 0 6 88 202 0 0 0 1 4 this 3 221  
81 0 0 7 89 223 0 0 0 0 
82 0 0 4 92 201 0 0 0 2 4 this 3 221  3 out 1 213  
83 0 0 4 93 201 0 0 0 2 4 this 3 221  3 out 1 213  
84 0 0 4 94 201 0 0 52 // set_speaker_configuration is a Miles only method. 10 4 this 3 215  8 speaker1 1 224  8 speaker2 5 224  8 speaker3 5 224  8 speaker4 5 224  8 speaker5 5 224  8 speaker6 5 224  8 speaker7 5 224  8 speaker8 5 224  8 speaker9 5 224  
85 0 0 7 95 205 0 0 0 0 
86 0 0 15 98 229 186 0 0 1 6 param0 0 227  
87 0 0 7 98 229 186 0 118 /**
 * Create a new AudioLoadRequest, and add it to the loader via load_async(),
 * to begin an asynchronous load.
 */ 3 13 audio_manager 1 215  8 filename 1 211  10 positional 1 207  
88 0 0 7 99 215 0 0 90 /**
 * Returns the AudioManager that will serve this asynchronous
 * AudioLoadRequest.
 */ 1 4 this 3 227  
89 0 0 6 100 211 0 0 83 /**
 * Returns the filename associated with this asynchronous AudioLoadRequest.
 */ 1 4 this 3 227  
90 0 0 6 101 207 0 0 93 /**
 * Returns the positional flag associated with this asynchronous
 * AudioLoadRequest.
 */ 1 4 this 3 227  
91 0 0 6 102 207 0 0 242 /**
 * Returns true if this request has completed, false if it is still pending.
 * When this returns true, you may retrieve the sound loaded by calling
 * get_sound().
 * Equivalent to `req.done() and not req.cancelled()`.
 * @see done()
 */ 1 4 this 3 227  
92 0 0 7 103 206 0 0 202 /**
 * Returns the sound that was loaded asynchronously, if any, or nullptr if
 * there was an error.  It is an error to call this unless done() returns
 * true.
 * @deprecated Use result() instead.
 */ 1 4 this 3 227  
93 0 0 7 104 205 0 0 0 0 
43
187 16 FilterProperties 0 75777 16 FilterProperties 16 FilterProperties 0 0 0 1 94 0 0 13 95 96 97 98 99 100 101 102 103 104 105 106 107 0 0 1 0 188 0 0 0 0 0

188 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

189 10 AudioSound 0 75777 10 AudioSound 10 AudioSound 0 0 0 0 0 0 37 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143 144 0 0 1 0 188 0 0 0 1 190 0

190 11 SoundStatus 0 794624 23 AudioSound::SoundStatus 23 AudioSound::SoundStatus 189 0 0 0 0 0 0 0 0 0 3 3 BAD 15 AudioSound::BAD 0
0 5 READY 17 AudioSound::READY 0
1 7 PLAYING 19 AudioSound::PLAYING 0
2 0 0

191 12 AudioManager 0 75777 12 AudioManager 12 AudioManager 0 0 0 0 0 1 230 34 145 146 147 148 149 150 151 152 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167 168 169 170 171 172 173 174 175 176 177 178 0 0 1 0 188 0 0 0 3 192 193 194 0

192 19 SpeakerModeCategory 0 794624 33 AudioManager::SpeakerModeCategory 33 AudioManager::SpeakerModeCategory 191 0 0 0 0 0 0 0 0 0 9 15 SPEAKERMODE_raw 29 AudioManager::SPEAKERMODE_raw 79
// These enumerants line up one-to-one with the FMOD SPEAKERMODE
// enumerants.
0 16 SPEAKERMODE_mono 30 AudioManager::SPEAKERMODE_mono 0
1 18 SPEAKERMODE_stereo 32 AudioManager::SPEAKERMODE_stereo 0
2 16 SPEAKERMODE_quad 30 AudioManager::SPEAKERMODE_quad 0
3 20 SPEAKERMODE_surround 34 AudioManager::SPEAKERMODE_surround 0
4 19 SPEAKERMODE_5point1 33 AudioManager::SPEAKERMODE_5point1 0
5 19 SPEAKERMODE_7point1 33 AudioManager::SPEAKERMODE_7point1 0
6 15 SPEAKERMODE_max 29 AudioManager::SPEAKERMODE_max 0
7 17 SPEAKERMODE_COUNT 31 AudioManager::SPEAKERMODE_COUNT 0
8 0 0

193 9 SpeakerId 0 794624 23 AudioManager::SpeakerId 23 AudioManager::SpeakerId 191 0 0 0 0 0 0 0 0 0 10 8 SPK_none 22 AudioManager::SPK_none 0
0 13 SPK_frontleft 27 AudioManager::SPK_frontleft 0
1 14 SPK_frontright 28 AudioManager::SPK_frontright 0
2 10 SPK_center 24 AudioManager::SPK_center 0
3 7 SPK_sub 21 AudioManager::SPK_sub 0
4 12 SPK_backleft 26 AudioManager::SPK_backleft 0
5 13 SPK_backright 27 AudioManager::SPK_backright 0
6 12 SPK_sideleft 26 AudioManager::SPK_sideleft 0
7 13 SPK_sideright 27 AudioManager::SPK_sideright 0
8 9 SPK_COUNT 23 AudioManager::SPK_COUNT 0
9 0 0

194 10 StreamMode 0 794624 24 AudioManager::StreamMode 24 AudioManager::StreamMode 191 0 0 0 0 0 0 0 0 0 3 12 SM_heuristic 26 AudioManager::SM_heuristic 0
0 9 SM_sample 23 AudioManager::SM_sample 0
1 9 SM_stream 23 AudioManager::SM_stream 0
2 0 0

195 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

196 16 AudioLoadRequest 0 141313 16 AudioLoadRequest 16 AudioLoadRequest 0 0 0 1 179 186 0 6 180 181 182 183 184 185 0 0 1 0 197 0 0 0 0 300
/**
 * A class object that manages a single asynchronous audio load request.  This
 * works in conjunction with the Loader class defined in pgraph, or really
 * with any AsyncTaskManager.  Create a new AudioLoadRequest, and add it to
 * the loader via load_async(), to begin an asynchronous load.
 */

197 9 AsyncTask 0 2048 9 AsyncTask 9 AsyncTask 0 0 0 0 0 0 0 0 0 0 0 0 217
/**
 * This class represents a concrete task performed by an AsyncManager.
 * Normally, you would subclass from this class, and override do_task(), to
 * define the functionality you wish to have the task perform.
 */

198 18 FilterProperties * 0 8576 18 FilterProperties * 18 FilterProperties * 0 0 187 0 0 0 0 0 0 0 0 0 0

199 24 FilterProperties const * 0 8576 24 FilterProperties const * 24 FilterProperties const * 0 0 200 0 0 0 0 0 0 0 0 0 0

200 22 FilterProperties const 0 8832 22 FilterProperties const 22 FilterProperties const 0 0 187 0 0 0 0 0 0 0 0 0 0

201 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

202 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 203 0 0 0 0 0 0 0 0 0 0

203 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

204 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

205 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 204 0 0 0 0 0 0 0 0 0 0

206 12 AudioSound * 0 8576 12 AudioSound * 12 AudioSound * 0 0 189 0 0 0 0 0 0 0 0 0 0

207 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

208 18 AudioSound const * 0 8576 18 AudioSound const * 18 AudioSound const * 0 0 209 0 0 0 0 0 0 0 0 0 0

209 16 AudioSound const 0 8832 16 AudioSound const 16 AudioSound const 0 0 189 0 0 0 0 0 0 0 0 0 0

210 17 unsigned long int 0 8214 17 unsigned long int 17 unsigned long int 0 1 0 0 0 0 0 0 0 0 0 0 0

211 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

212 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

213 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 214 0 0 0 0 0 0 0 0 0 0

214 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

215 14 AudioManager * 0 8576 14 AudioManager * 14 AudioManager * 0 0 191 0 0 0 0 0 0 0 0 0 0

216 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 217 0 0 0 0 0 0 0 0 0 0

217 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 195 0 0 0 0 0 0 0 0 0 0

218 12 MovieAudio * 0 8576 12 MovieAudio * 12 MovieAudio * 0 0 219 0 0 0 0 0 0 0 0 0 0

219 10 MovieAudio 0 2048 10 MovieAudio 10 MovieAudio 0 0 0 0 0 0 0 0 0 0 0 0 397
/**
 * A MovieAudio is actually any source that provides a sequence of audio
 * samples.  That could include an AVI file, a microphone, or an internet TV
 * station.
 *
 * The difference between a MovieAudio and a MovieAudioCursor is like the
 * difference between a filename and a file handle.  The MovieAudio just
 * indicates a particular movie.  The MovieAudioCursor is what allows access.
 */

220 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

221 20 AudioManager const * 0 8576 20 AudioManager const * 20 AudioManager const * 0 0 222 0 0 0 0 0 0 0 0 0 0

222 18 AudioManager const 0 8832 18 AudioManager const 18 AudioManager const 0 0 191 0 0 0 0 0 0 0 0 0 0

223 10 Filename * 0 8576 10 Filename * 10 Filename * 0 0 195 0 0 0 0 0 0 0 0 0 0

224 11 LVecBase3 * 0 8576 11 LVecBase3 * 11 LVecBase3 * 0 0 225 0 0 0 0 0 0 0 0 0 0

225 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 226 0 0 0 0 0 0 0 0 0 0

226 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

227 24 AudioLoadRequest const * 0 8576 24 AudioLoadRequest const * 24 AudioLoadRequest const * 0 0 228 0 0 0 0 0 0 0 0 0 0

228 22 AudioLoadRequest const 0 8832 22 AudioLoadRequest const 22 AudioLoadRequest const 0 0 196 0 0 0 0 0 0 0 0 0 0

229 18 AudioLoadRequest * 0 8576 18 AudioLoadRequest * 18 AudioLoadRequest * 0 0 196 0 0 0 0 0 0 0 0 0 0

0
1
230 12 dls_pathname 0 2 195 174 0 0 0 0 0 0 0 26 AudioManager::dls_pathname 0

0
