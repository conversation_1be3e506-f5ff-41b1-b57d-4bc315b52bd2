1730992849
3 3
14 libp3dtoolbase 4 NSJh 12 panda3d.core 
60
56 15 get_total_alloc 0 4 116 32 NeverFreeMemory::get_total_alloc 0 1 1 110
/**
 * Returns the total number of bytes consumed by all the pages allocated
 * internally by this object.
 */
65
static inline std::size_t NeverFreeMemory::get_total_alloc(void);

57 14 get_total_used 0 4 116 31 NeverFreeMemory::get_total_used 0 1 2 114
/**
 * Returns the total number of bytes requested by the application in calls to
 * NeverFreeMemory::alloc().
 */
64
static inline std::size_t NeverFreeMemory::get_total_used(void);

58 16 get_total_unused 0 4 116 33 NeverFreeMemory::get_total_unused 0 1 3 178
/**
 * Returns the difference between get_total_alloc() and get_total_used().
 * This represents bytes in allocated pages that have not (yet) been used by
 * the application.
 */
66
static inline std::size_t NeverFreeMemory::get_total_unused(void);

59 16 ~NeverFreeMemory 0 516 116 33 NeverFreeMemory::~NeverFreeMemory 0 0 0
40
NeverFreeMemory::~NeverFreeMemory(void);

60 10 TypeHandle 0 260 117 22 TypeHandle::TypeHandle 0 2 4 5 114
/**
 * Private constructor for initializing a TypeHandle from an index, used by
 * none() and by from_index().
 */
109
TypeHandle::TypeHandle(void) noexcept = default;
inline TypeHandle::TypeHandle(TypeHandle const &) = default;

61 4 make 0 4 117 16 TypeHandle::make 0 1 6 0
59
static TypeHandle TypeHandle::make(PyTypeObject *classobj);

62 11 operator == 0 4 117 23 TypeHandle::operator == 0 1 7 0
67
inline bool TypeHandle::operator ==(TypeHandle const &other) const;

63 11 operator != 0 4 117 23 TypeHandle::operator != 0 1 8 0
67
inline bool TypeHandle::operator !=(TypeHandle const &other) const;

64 10 operator < 0 4 117 22 TypeHandle::operator < 0 1 9 0
66
inline bool TypeHandle::operator <(TypeHandle const &other) const;

65 11 operator <= 0 4 117 23 TypeHandle::operator <= 0 1 10 0
67
inline bool TypeHandle::operator <=(TypeHandle const &other) const;

66 10 operator > 0 4 117 22 TypeHandle::operator > 0 1 11 0
66
inline bool TypeHandle::operator >(TypeHandle const &other) const;

67 11 operator >= 0 4 117 23 TypeHandle::operator >= 0 1 12 0
67
inline bool TypeHandle::operator >=(TypeHandle const &other) const;

68 10 compare_to 0 4 117 22 TypeHandle::compare_to 0 1 13 207
/**
 * Sorts TypeHandles arbitrarily (according to <, >, etc.).  Returns a number
 * less than 0 if this type sorts before the other one, greater than zero if
 * it sorts after, 0 if they are equivalent.
 */
65
inline int TypeHandle::compare_to(TypeHandle const &other) const;

69 8 get_hash 0 4 117 20 TypeHandle::get_hash 0 1 14 54
/**
 * Returns a hash code suitable for phash_map.
 */
52
inline std::size_t TypeHandle::get_hash(void) const;

70 8 get_name 0 4 117 20 TypeHandle::get_name 0 1 15 216
/**
 * Returns the name of the type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
77
inline std::string TypeHandle::get_name(TypedObject *object = nullptr) const;

71 15 is_derived_from 0 4 117 27 TypeHandle::is_derived_from 0 1 16 268
/**
 * Returns true if this type is derived from the indicated type, false
 * otherwise.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
96
inline bool TypeHandle::is_derived_from(TypeHandle parent, TypedObject *object = nullptr) const;

72 22 get_num_parent_classes 0 4 117 34 TypeHandle::get_num_parent_classes 0 1 17 506
/**
 * Returns the number of parent classes that this type is known to have.  This
 * may then be used to index into get_parent_class().  The result will be 0 if
 * this class does not inherit from any other classes, 1 if normal, single
 * inheritance is in effect, or greater than one if multiple inheritance is in
 * effect.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
83
inline int TypeHandle::get_num_parent_classes(TypedObject *object = nullptr) const;

73 16 get_parent_class 0 4 117 28 TypeHandle::get_parent_class 0 1 18 129
/**
 * Returns the nth parent class of this type.  The index should be in the
 * range 0 <= index < get_num_parent_classes().
 */
64
inline TypeHandle TypeHandle::get_parent_class(int index) const;

74 21 get_num_child_classes 0 4 117 33 TypeHandle::get_num_child_classes 0 1 19 314
/**
 * Returns the number of child classes that this type is known to have.  This
 * may then be used to index into get_child_class().
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
82
inline int TypeHandle::get_num_child_classes(TypedObject *object = nullptr) const;

75 15 get_child_class 0 4 117 27 TypeHandle::get_child_class 0 1 20 127
/**
 * Returns the nth child class of this type.  The index should be in the range
 * 0 <= index < get_num_child_classes().
 */
63
inline TypeHandle TypeHandle::get_child_class(int index) const;

76 18 get_parent_towards 0 4 117 30 TypeHandle::get_parent_towards 0 1 21 577
/**
 * Returns the parent class that is in a direct line of inheritance to the
 * indicated ancestor class.  This is useful in the presence of multiple
 * inheritance to try to determine what properties an unknown type may have.
 *
 * The return value is TypeHandle::none() if the type does not inherit from
 * the ancestor.  If ancestor is the same as this type, the return value is
 * this type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
107
inline TypeHandle TypeHandle::get_parent_towards(TypeHandle ancestor, TypedObject *object = nullptr) const;

77 24 get_best_parent_from_Set 0 4 117 36 TypeHandle::get_best_parent_from_Set 0 1 22 61
/**
 * Return the Index of the BEst fit Classs from a set
 */
77
int TypeHandle::get_best_parent_from_Set(set< int > const &legal_vals) const;

78 16 get_memory_usage 0 4 117 28 TypeHandle::get_memory_usage 0 1 23 194
/**
 * Returns the total allocated memory used by objects of this type, for the
 * indicated memory class.  This is only updated if track-memory-usage is set
 * true in your Config.prc file.
 */
85
std::size_t TypeHandle::get_memory_usage(TypeHandle::MemoryClass memory_class) const;

79 16 inc_memory_usage 0 4 117 28 TypeHandle::inc_memory_usage 0 1 24 110
/**
 * Adds the indicated amount to the record for the total allocated memory for
 * objects of this type.
 */
90
void TypeHandle::inc_memory_usage(TypeHandle::MemoryClass memory_class, std::size_t size);

80 16 dec_memory_usage 0 4 117 28 TypeHandle::dec_memory_usage 0 1 25 117
/**
 * Subtracts the indicated amount from the record for the total allocated
 * memory for objects of this type.
 */
90
void TypeHandle::dec_memory_usage(TypeHandle::MemoryClass memory_class, std::size_t size);

81 9 get_index 0 4 117 21 TypeHandle::get_index 0 1 26 354
/**
 * Returns the integer index associated with this TypeHandle.  Each different
 * TypeHandle will have a different index.  However, you probably shouldn't be
 * using this method; you should just treat the TypeHandles as opaque classes.
 * This is provided for the convenience of non-C++ scripting languages to
 * build a hashtable of TypeHandles.
 */
45
inline int TypeHandle::get_index(void) const;

82 6 output 0 4 117 18 TypeHandle::output 0 1 27 10
/**
 *
 */
56
inline void TypeHandle::output(std::ostream &out) const;

83 4 none 0 4 117 16 TypeHandle::none 0 1 28 0
51
static constexpr TypeHandle TypeHandle::none(void);

84 22 operator typecast bool 0 132 117 34 TypeHandle::operator typecast bool 0 1 29 0
34
inline operator bool (void) const;

85 11 ~TypeHandle 0 516 117 23 TypeHandle::~TypeHandle 0 0 0
30
TypeHandle::~TypeHandle(void);

86 21 register_dynamic_type 0 4 122 35 TypeRegistry::register_dynamic_type 0 1 31 219
/**
 * Registers a new type on-the-fly, presumably at runtime.  A new TypeHandle
 * is returned if the typename was not seen before; otherwise the same
 * TypeHandle that was last used for this typename is returned.
 */
72
TypeHandle TypeRegistry::register_dynamic_type(std::string const &name);

87 17 record_derivation 0 4 122 31 TypeRegistry::record_derivation 0 1 32 201
/**
 * Records that the type referenced by child inherits directly from the type
 * referenced by parent.  In the event of multiple inheritance, this should be
 * called once for each parent class.
 */
74
void TypeRegistry::record_derivation(TypeHandle child, TypeHandle parent);

88 21 record_alternate_name 0 4 122 35 TypeRegistry::record_alternate_name 0 1 33 278
/**
 * Indicates an alternate name for the same type.  This is particularly useful
 * when a type has changed names, since the type is stored in a Bam file by
 * name; setting the original name as the alternate will allow the type to be
 * correctly read from old Bam files.
 */
83
void TypeRegistry::record_alternate_name(TypeHandle type, std::string const &name);

89 18 record_python_type 0 4 122 32 TypeRegistry::record_python_type 0 1 34 157
/**
 * Records the given Python type pointer in the type registry for the benefit
 * of interrogate, which expects this to contain a Dtool_PyTypedObject.
 */
78
void TypeRegistry::record_python_type(TypeHandle type, PyObject *python_type);

90 9 find_type 0 4 122 23 TypeRegistry::find_type 0 1 35 157
/**
 * Looks for a previously-registered type of the given name.  Returns its
 * TypeHandle if it exists, or TypeHandle::none() if there is no such type.
 */
66
TypeHandle TypeRegistry::find_type(std::string const &name) const;

91 15 find_type_by_id 0 4 122 29 TypeRegistry::find_type_by_id 0 1 36 207
/**
 * Looks for a previously-registered type with the given id number (as
 * returned by TypeHandle::get_index()). Returns its TypeHandle if it exists,
 * or TypeHandle::none() if there is no such type.
 */
55
TypeHandle TypeRegistry::find_type_by_id(int id) const;

92 8 get_name 0 4 122 22 TypeRegistry::get_name 0 1 37 226
/**
 * Returns the name of the indicated type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
79
std::string TypeRegistry::get_name(TypeHandle type, TypedObject *object) const;

93 15 is_derived_from 0 4 122 29 TypeRegistry::is_derived_from 0 1 38 571
/**
 * Returns true if the first type is derived from the second type, false
 * otherwise.
 *
 * The "child_object" pointer is an optional pointer to the TypedObject class
 * that owns the child TypeHandle.  It is only used in case the TypeHandle is
 * inadvertently undefined.
 *
 * This function definition follows the definitions for look_up() and
 * freshen_derivations() just to maximize the chance the the compiler will be
 * able to inline the above functions.  Yeah, a compiler shouldn't care, but
 * there's a big different between "shouldn't" and "doesn't".
 */
97
bool TypeRegistry::is_derived_from(TypeHandle child, TypeHandle base, TypedObject *child_object);

94 19 get_num_typehandles 0 4 122 33 TypeRegistry::get_num_typehandles 0 1 39 72
/**
 * Returns the total number of unique TypeHandles in the system.
 */
44
int TypeRegistry::get_num_typehandles(void);

95 14 get_typehandle 0 4 122 28 TypeRegistry::get_typehandle 0 1 40 80
/**
 * Returns the nth TypeHandle in the system.  See get_num_typehandles().
 */
47
TypeHandle TypeRegistry::get_typehandle(int n);

96 20 get_num_root_classes 0 4 122 34 TypeRegistry::get_num_root_classes 0 1 41 131
/**
 * Returns the number of root classes--that is, classes that do not inherit
 * from any other classes--known in the system.
 */
45
int TypeRegistry::get_num_root_classes(void);

97 14 get_root_class 0 4 122 28 TypeRegistry::get_root_class 0 1 42 81
/**
 * Returns the nth root class in the system.  See get_num_root_classes().
 */
47
TypeHandle TypeRegistry::get_root_class(int n);

98 22 get_num_parent_classes 0 4 122 36 TypeRegistry::get_num_parent_classes 0 1 43 515
/**
 * Returns the number of parent classes that the indicated type is known to
 * have.  This may then be used to index into get_parent_class().  The result
 * will be 0 if this class does not inherit from any other classes, 1 if
 * normal, single inheritance is in effect, or greater than one if multiple
 * inheritance is in effect.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
92
int TypeRegistry::get_num_parent_classes(TypeHandle child, TypedObject *child_object) const;

99 16 get_parent_class 0 4 122 30 TypeRegistry::get_parent_class 0 1 44 129
/**
 * Returns the nth parent class of this type.  The index should be in the
 * range 0 <= index < get_num_parent_classes().
 */
77
TypeHandle TypeRegistry::get_parent_class(TypeHandle child, int index) const;

100 21 get_num_child_classes 0 4 122 35 TypeRegistry::get_num_child_classes 0 1 45 323
/**
 * Returns the number of child classes that the indicated type is known to
 * have.  This may then be used to index into get_child_class().
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
91
int TypeRegistry::get_num_child_classes(TypeHandle child, TypedObject *child_object) const;

101 15 get_child_class 0 4 122 29 TypeRegistry::get_child_class 0 1 46 127
/**
 * Returns the nth child class of this type.  The index should be in the range
 * 0 <= index < get_num_child_classes().
 */
76
TypeHandle TypeRegistry::get_child_class(TypeHandle child, int index) const;

102 18 get_parent_towards 0 4 122 32 TypeRegistry::get_parent_towards 0 1 47 434
/**
 * Returns the parent of the indicated child class that is in a direct line of
 * inheritance to the indicated ancestor class.  This is useful in the
 * presence of multiple inheritance to try to determine what properties an
 * unknown type may have.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */
106
TypeHandle TypeRegistry::get_parent_towards(TypeHandle child, TypeHandle base, TypedObject *child_object);

103 16 reregister_types 0 4 122 30 TypeRegistry::reregister_types 0 1 48 336
/**
 * Walks through the TypeRegistry tree and makes sure that each type that was
 * previously registered is *still* registered.  This seems to get broken in
 * certain circumstances when compiled against libc5--it is as if the static
 * initializer stomps on the _type_handle values of each class after they've
 * been registered.
 */
49
static void TypeRegistry::reregister_types(void);

104 5 write 0 4 122 19 TypeRegistry::write 0 1 49 139
/**
 * Makes an attempt to format the entire TypeRegistry in a nice way that shows
 * the derivation tree as intelligently as possible.
 */
50
void TypeRegistry::write(std::ostream &out) const;

105 3 ptr 0 4 122 17 TypeRegistry::ptr 0 1 50 130
// ptr() returns the pointer to the global TypeRegistry object.

/**
 * Returns the pointer to the global TypeRegistry object.
 */
52
static inline TypeRegistry *TypeRegistry::ptr(void);

106 12 TypeRegistry 0 260 122 26 TypeRegistry::TypeRegistry 0 1 30 10
/**
 *
 */
66
inline TypeRegistry::TypeRegistry(TypeRegistry const &) = default;

107 13 ~TypeRegistry 0 516 122 27 TypeRegistry::~TypeRegistry 0 0 0
34
TypeRegistry::~TypeRegistry(void);

108 20 upcast_to_MemoryBase 0 12 124 33 TypedObject::upcast_to_MemoryBase 0 0 37
upcast from TypedObject to MemoryBase
52
MemoryBase *TypedObject::upcast_to_MemoryBase(void);

109 23 downcast_to_TypedObject 0 12 123 35 MemoryBase::downcast_to_TypedObject 0 0 39
downcast from MemoryBase to TypedObject
55
TypedObject *MemoryBase::downcast_to_TypedObject(void);

110 12 ~TypedObject 0 518 124 25 TypedObject::~TypedObject 0 0 56
// A virtual destructor is just a good idea.

/**
 *
 */
40
virtual TypedObject::~TypedObject(void);

111 8 get_type 0 6 124 21 TypedObject::get_type 0 1 51 166
// Derived classes should override this function to return get_class_type().

// Derived classes should override this function to return get_class_type().

/**
 *
 */
57
virtual TypeHandle TypedObject::get_type(void) const = 0;

112 14 get_type_index 0 4 124 27 TypedObject::get_type_index 0 1 52 180
/**
 * Returns the internal index number associated with this object's TypeHandle,
 * a unique number for each different type.  This is equivalent to
 * get_type().get_index().
 */
51
inline int TypedObject::get_type_index(void) const;

113 10 is_of_type 0 4 124 23 TypedObject::is_of_type 0 1 53 84
/**
 * Returns true if the current object is or derives from the indicated type.
 */
61
inline bool TypedObject::is_of_type(TypeHandle handle) const;

114 13 is_exact_type 0 4 124 26 TypedObject::is_exact_type 0 1 54 76
/**
 * Returns true if the current object is the indicated type exactly.
 */
64
inline bool TypedObject::is_exact_type(TypeHandle handle) const;

115 14 get_class_type 0 4 124 27 TypedObject::get_class_type 0 1 55 0
52
static TypeHandle TypedObject::get_class_type(void);

55
1 0 0 6 2 125 0 0 110 /**
 * Returns the total number of bytes consumed by all the pages allocated
 * internally by this object.
 */ 0 
2 0 0 6 3 125 0 0 114 /**
 * Returns the total number of bytes requested by the application in calls to
 * NeverFreeMemory::alloc().
 */ 0 
3 0 0 6 4 125 0 0 178 /**
 * Returns the difference between get_total_alloc() and get_total_used().
 * This represents bytes in allocated pages that have not (yet) been used by
 * the application.
 */ 0 
4 0 0 7 7 127 85 0 0 0 
5 0 0 15 7 127 85 0 0 1 6 param0 0 128  
6 0 0 55 9 127 85 0 0 1 8 classobj 1 130  
7 0 0 6 10 133 0 0 0 2 4 this 3 128  5 other 1 128  
8 0 0 6 11 133 0 0 0 2 4 this 3 128  5 other 1 128  
9 0 0 6 12 133 0 0 0 2 4 this 3 128  5 other 1 128  
10 0 0 6 13 133 0 0 0 2 4 this 3 128  5 other 1 128  
11 0 0 6 14 133 0 0 0 2 4 this 3 128  5 other 1 128  
12 0 0 6 15 133 0 0 0 2 4 this 3 128  5 other 1 128  
13 0 0 6 16 119 0 0 207 /**
 * Sorts TypeHandles arbitrarily (according to <, >, etc.).  Returns a number
 * less than 0 if this type sorts before the other one, greater than zero if
 * it sorts after, 0 if they are equivalent.
 */ 2 4 this 3 128  5 other 1 128  
14 0 0 6 17 125 0 0 54 /**
 * Returns a hash code suitable for phash_map.
 */ 1 4 this 3 128  
15 0 0 6 18 135 0 0 216 /**
 * Returns the name of the type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 2 4 this 3 128  6 object 5 134  
16 0 0 6 19 133 0 0 268 /**
 * Returns true if this type is derived from the indicated type, false
 * otherwise.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 3 4 this 3 128  6 parent 1 127  6 object 5 134  
17 0 0 6 20 119 0 0 506 /**
 * Returns the number of parent classes that this type is known to have.  This
 * may then be used to index into get_parent_class().  The result will be 0 if
 * this class does not inherit from any other classes, 1 if normal, single
 * inheritance is in effect, or greater than one if multiple inheritance is in
 * effect.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 2 4 this 3 128  6 object 5 134  
18 0 0 7 21 127 85 0 129 /**
 * Returns the nth parent class of this type.  The index should be in the
 * range 0 <= index < get_num_parent_classes().
 */ 2 4 this 3 128  5 index 1 119  
19 0 0 6 22 119 0 0 314 /**
 * Returns the number of child classes that this type is known to have.  This
 * may then be used to index into get_child_class().
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 2 4 this 3 128  6 object 5 134  
20 0 0 7 23 127 85 0 127 /**
 * Returns the nth child class of this type.  The index should be in the range
 * 0 <= index < get_num_child_classes().
 */ 2 4 this 3 128  5 index 1 119  
21 0 0 7 24 127 85 0 577 /**
 * Returns the parent class that is in a direct line of inheritance to the
 * indicated ancestor class.  This is useful in the presence of multiple
 * inheritance to try to determine what properties an unknown type may have.
 *
 * The return value is TypeHandle::none() if the type does not inherit from
 * the ancestor.  If ancestor is the same as this type, the return value is
 * this type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 3 4 this 3 128  8 ancestor 1 127  6 object 5 134  
22 0 0 6 25 119 0 0 61 /**
 * Return the Index of the BEst fit Classs from a set
 */ 2 4 this 3 128  10 legal_vals 1 136  
23 0 0 6 26 125 0 0 194 /**
 * Returns the total allocated memory used by objects of this type, for the
 * indicated memory class.  This is only updated if track-memory-usage is set
 * true in your Config.prc file.
 */ 2 4 this 3 128  12 memory_class 1 118  
24 0 0 4 27 139 0 0 110 /**
 * Adds the indicated amount to the record for the total allocated memory for
 * objects of this type.
 */ 3 4 this 3 127  12 memory_class 1 118  4 size 1 125  
25 0 0 4 28 139 0 0 117 /**
 * Subtracts the indicated amount from the record for the total allocated
 * memory for objects of this type.
 */ 3 4 this 3 127  12 memory_class 1 118  4 size 1 125  
26 0 0 6 29 119 0 0 354 /**
 * Returns the integer index associated with this TypeHandle.  Each different
 * TypeHandle will have a different index.  However, you probably shouldn't be
 * using this method; you should just treat the TypeHandles as opaque classes.
 * This is provided for the convenience of non-C++ scripting languages to
 * build a hashtable of TypeHandles.
 */ 1 4 this 3 128  
27 0 0 4 30 139 0 0 10 /**
 *
 */ 2 4 this 3 128  3 out 1 140  
28 0 0 7 31 127 85 0 0 0 
29 0 0 6 32 133 0 0 0 1 4 this 3 128  
30 0 0 15 67 144 107 0 0 1 6 param0 0 142  
31 0 0 7 43 127 85 0 219 /**
 * Registers a new type on-the-fly, presumably at runtime.  A new TypeHandle
 * is returned if the typename was not seen before; otherwise the same
 * TypeHandle that was last used for this typename is returned.
 */ 2 4 this 3 144  4 name 1 135  
32 0 0 4 44 139 0 0 201 /**
 * Records that the type referenced by child inherits directly from the type
 * referenced by parent.  In the event of multiple inheritance, this should be
 * called once for each parent class.
 */ 3 4 this 3 144  5 child 1 127  6 parent 1 127  
33 0 0 4 45 139 0 0 278 /**
 * Indicates an alternate name for the same type.  This is particularly useful
 * when a type has changed names, since the type is stored in a Bam file by
 * name; setting the original name as the alternate will allow the type to be
 * correctly read from old Bam files.
 */ 3 4 this 3 144  4 type 1 127  4 name 1 135  
34 0 0 4 46 139 0 0 157 /**
 * Records the given Python type pointer in the type registry for the benefit
 * of interrogate, which expects this to contain a Dtool_PyTypedObject.
 */ 3 4 this 3 144  4 type 1 127  11 python_type 1 145  
35 0 0 7 47 127 85 0 157 /**
 * Looks for a previously-registered type of the given name.  Returns its
 * TypeHandle if it exists, or TypeHandle::none() if there is no such type.
 */ 2 4 this 3 142  4 name 1 135  
36 0 0 7 48 127 85 0 207 /**
 * Looks for a previously-registered type with the given id number (as
 * returned by TypeHandle::get_index()). Returns its TypeHandle if it exists,
 * or TypeHandle::none() if there is no such type.
 */ 2 4 this 3 142  2 id 1 119  
37 0 0 6 49 135 0 0 226 /**
 * Returns the name of the indicated type.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 3 4 this 3 142  4 type 1 127  6 object 1 134  
38 0 0 6 50 133 0 0 571 /**
 * Returns true if the first type is derived from the second type, false
 * otherwise.
 *
 * The "child_object" pointer is an optional pointer to the TypedObject class
 * that owns the child TypeHandle.  It is only used in case the TypeHandle is
 * inadvertently undefined.
 *
 * This function definition follows the definitions for look_up() and
 * freshen_derivations() just to maximize the chance the the compiler will be
 * able to inline the above functions.  Yeah, a compiler shouldn't care, but
 * there's a big different between "shouldn't" and "doesn't".
 */ 4 4 this 3 144  5 child 1 127  4 base 1 127  12 child_object 1 134  
39 0 0 6 51 119 0 0 72 /**
 * Returns the total number of unique TypeHandles in the system.
 */ 1 4 this 3 144  
40 0 0 7 52 127 85 0 80 /**
 * Returns the nth TypeHandle in the system.  See get_num_typehandles().
 */ 2 4 this 3 144  1 n 1 119  
41 0 0 6 54 119 0 0 131 /**
 * Returns the number of root classes--that is, classes that do not inherit
 * from any other classes--known in the system.
 */ 1 4 this 3 144  
42 0 0 7 55 127 85 0 81 /**
 * Returns the nth root class in the system.  See get_num_root_classes().
 */ 2 4 this 3 144  1 n 1 119  
43 0 0 6 57 119 0 0 515 /**
 * Returns the number of parent classes that the indicated type is known to
 * have.  This may then be used to index into get_parent_class().  The result
 * will be 0 if this class does not inherit from any other classes, 1 if
 * normal, single inheritance is in effect, or greater than one if multiple
 * inheritance is in effect.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 3 4 this 3 142  5 child 1 127  12 child_object 1 134  
44 0 0 7 58 127 85 0 129 /**
 * Returns the nth parent class of this type.  The index should be in the
 * range 0 <= index < get_num_parent_classes().
 */ 3 4 this 3 142  5 child 1 127  5 index 1 119  
45 0 0 6 59 119 0 0 323 /**
 * Returns the number of child classes that the indicated type is known to
 * have.  This may then be used to index into get_child_class().
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 3 4 this 3 142  5 child 1 127  12 child_object 1 134  
46 0 0 7 60 127 85 0 127 /**
 * Returns the nth child class of this type.  The index should be in the range
 * 0 <= index < get_num_child_classes().
 */ 3 4 this 3 142  5 child 1 127  5 index 1 119  
47 0 0 7 61 127 85 0 434 /**
 * Returns the parent of the indicated child class that is in a direct line of
 * inheritance to the indicated ancestor class.  This is useful in the
 * presence of multiple inheritance to try to determine what properties an
 * unknown type may have.
 *
 * The "object" pointer is an optional pointer to the TypedObject class that
 * owns this TypeHandle.  It is only used in case the TypeHandle is
 * inadvertantly undefined.
 */ 4 4 this 3 144  5 child 1 127  4 base 1 127  12 child_object 1 134  
48 0 0 4 62 139 0 0 336 /**
 * Walks through the TypeRegistry tree and makes sure that each type that was
 * previously registered is *still* registered.  This seems to get broken in
 * certain circumstances when compiled against libc5--it is as if the static
 * initializer stomps on the _type_handle values of each class after they've
 * been registered.
 */ 0 
49 0 0 4 63 139 0 0 139 /**
 * Makes an attempt to format the entire TypeRegistry in a nice way that shows
 * the derivation tree as intelligently as possible.
 */ 2 4 this 3 142  3 out 1 140  
50 0 0 6 64 144 0 0 65 /**
 * Returns the pointer to the global TypeRegistry object.
 */ 0 
51 0 0 7 73 127 85 0 76 // Derived classes should override this function to return get_class_type(). 1 4 this 3 148  
52 0 0 6 75 119 0 0 180 /**
 * Returns the internal index number associated with this object's TypeHandle,
 * a unique number for each different type.  This is equivalent to
 * get_type().get_index().
 */ 1 4 this 3 148  
53 0 0 6 76 133 0 0 84 /**
 * Returns true if the current object is or derives from the indicated type.
 */ 2 4 this 3 148  6 handle 1 127  
54 0 0 6 77 133 0 0 76 /**
 * Returns true if the current object is the indicated type exactly.
 */ 2 4 this 3 148  6 handle 1 127  
55 0 0 7 78 127 85 0 0 0 
34
116 15 NeverFreeMemory 0 141313 15 NeverFreeMemory 15 NeverFreeMemory 0 0 0 0 59 0 3 56 57 58 0 0 0 0 0 505
/**
 * This class is used to allocate bytes of memory from a pool that is never
 * intended to be freed.  It is particularly useful to support DeletedChain,
 * which allocates memory in just such a fashion.
 *
 * When it is known that memory will not be freed, it is preferable to use
 * this instead of the standard malloc() (or global_operator_new()) call,
 * since this will help reduce fragmentation problems in the dynamic heap.
 * Also, memory allocated from here will exhibit less wasted space.
 */

117 10 TypeHandle 0 16918529 10 TypeHandle 10 TypeHandle 0 0 0 1 60 85 4 151 152 153 154 23 61 62 63 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 0 1 84 0 0 1 118 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

118 11 MemoryClass 0 794624 23 TypeHandle::MemoryClass 23 TypeHandle::MemoryClass 117 0 0 0 0 0 0 0 0 0 5 12 MC_singleton 24 TypeHandle::MC_singleton 0
0 8 MC_array 20 TypeHandle::MC_array 0
1 23 MC_deleted_chain_active 35 TypeHandle::MC_deleted_chain_active 0
2 25 MC_deleted_chain_inactive 37 TypeHandle::MC_deleted_chain_inactive 0
3 8 MC_limit 20 TypeHandle::MC_limit 70
// Not a real value, just a placeholder for the maximum
// enum value.
4 0 0

119 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

120 6 string 0 2105344 11 std::string 11 std::string 0 0 121 0 0 0 0 0 0 0 0 0 0

121 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

122 12 TypeRegistry 0 141313 12 TypeRegistry 12 TypeRegistry 0 0 0 1 106 107 2 155 156 20 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 2 158 159 0 1 0 123 0 0 0 0 324
/**
 * The TypeRegistry class maintains all the assigned TypeHandles in a given
 * system.  There should be only one TypeRegistry class during the lifetime of
 * the application.  It will be created on the local heap initially, and it
 * should be migrated to shared memory as soon as shared memory becomes
 * available.
 */

123 10 MemoryBase 0 1050624 10 MemoryBase 10 MemoryBase 0 0 0 0 0 0 0 0 0 0 0 0 428
/**
 * This class is intended to be the base class of all objects in Panda that
 * might be allocated and deleted via the new and delete operators.  It
 * redefines these operators to provide some memory tracking support.
 *
 * We used to try to override the global operator new and delete methods, but
 * that seems to cause problems when including header files for C++-based
 * system libraries (such as are found on OSX).
 */

124 11 TypedObject 0 26625 11 TypedObject 11 TypedObject 0 0 0 0 110 1 157 5 111 112 113 114 115 0 0 1 3 123 108 109 0 0 2508
/**
 * This is an abstract class that all classes which use TypeHandle, and also
 * provide virtual functions to support polymorphism, should inherit from.
 * Each derived class should define get_type(), which should return the
 * specific type of the derived class.  Inheriting from this automatically
 * provides support for is_of_type() and is_exact_type().
 *
 * All classes that inherit directly or indirectly from TypedObject should
 * redefine get_type() and force_init_type(), as shown below.  Some classes
 * that do not inherit from TypedObject may still declare TypeHandles for
 * themselves by defining methods called get_class_type() and init_type().
 * Classes such as these may serve as base classes, but the dynamic type
 * identification system will be limited.  Classes that do not inherit from
 * TypedObject need not define the virtual functions get_type() and
 * force_init_type() (or any other virtual functions).
 *
 * There is a specific layout for defining the overrides from this class.
 * Keeping the definitions formatted just like these examples will allow
 * someone in the future to use a sed (or similar) script to make global
 * changes, if necessary.  Avoid rearranging the braces or the order of the
 * functions unless you're ready to change them in every file all at once.
 *
 * What follows are some examples that can be used in new classes that you
 * create.
 *
 * @par In the class definition (.h file):
 * @code
 * public:
 *   static TypeHandle get_class_type() {
 *     return _type_handle;
 *   }
 *   static void init_type() {
 *     <<<BaseClassOne>>>::init_type();
 *     <<<BaseClassTwo>>>::init_type();
 *     <<<BaseClassN>>>::init_type();
 *     register_type(_type_handle, "<<<ThisClassStringName>>>",
 *                   <<<BaseClassOne>>>::get_class_type(),
 *                   <<<BaseClassTwo>>>::get_class_type(),
 *                   <<<BaseClassN>>>::get_class_type());
 *   }
 *   virtual TypeHandle get_type() const {
 *     return get_class_type();
 *   }
 *   virtual TypeHandle force_init_type() {init_type(); return get_class_type();}
 *
 * private:
 *   static TypeHandle _type_handle;
 * @endcode
 *
 * @par In the class .cxx file:
 * @code
 * TypeHandle <<<ThisClassStringName>>>::_type_handle;
 * @endcode
 *
 * @par In the class config_<<<PackageName>>>.cxx file:
 * @code
 * ConfigureFn(config_<<<PackageName>>>) {
 *   <<<ClassOne>>>::init_type();
 *   <<<ClassTwo>>>::init_type();
 *   <<<ClassN>>>::init_type();
 * }
 * @endcode
 */

125 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 126 0 0 0 0 0 0 0 0 0 0

126 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

127 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 117 0 0 0 0 0 0 0 0 0 0

128 18 TypeHandle const * 0 8576 18 TypeHandle const * 18 TypeHandle const * 0 0 129 0 0 0 0 0 0 0 0 0 0

129 16 TypeHandle const 0 8832 16 TypeHandle const 16 TypeHandle const 0 0 117 0 0 0 0 0 0 0 0 0 0

130 14 PyTypeObject * 0 8576 14 PyTypeObject * 14 PyTypeObject * 0 0 131 0 0 0 0 0 0 0 0 0 0

131 12 PyTypeObject 0 2105344 12 PyTypeObject 12 PyTypeObject 0 0 132 0 0 0 0 0 0 0 0 0 0

132 11 _typeobject 0 1024 11 _typeobject 11 _typeobject 0 0 0 0 0 0 0 0 0 0 0 0 0

133 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

134 13 TypedObject * 0 8576 13 TypedObject * 13 TypedObject * 0 0 124 0 0 0 0 0 0 0 0 0 0

135 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

136 18 set< int > const * 0 8576 18 set< int > const * 18 set< int > const * 0 0 137 0 0 0 0 0 0 0 0 0 0

137 16 set< int > const 0 8832 16 set< int > const 16 set< int > const 0 0 138 0 0 0 0 0 0 0 0 0 0

138 10 set< int > 0 2048 10 set< int > 10 set< int > 0 0 0 0 0 0 0 0 0 0 0 0 0

139 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

140 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 141 0 0 0 0 0 0 0 0 0 0

141 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

142 20 TypeRegistry const * 0 8576 20 TypeRegistry const * 20 TypeRegistry const * 0 0 143 0 0 0 0 0 0 0 0 0 0

143 18 TypeRegistry const 0 8832 18 TypeRegistry const 18 TypeRegistry const 0 0 122 0 0 0 0 0 0 0 0 0 0

144 14 TypeRegistry * 0 8576 14 TypeRegistry * 14 TypeRegistry * 0 0 122 0 0 0 0 0 0 0 0 0 0

145 10 PyObject * 0 8576 10 PyObject * 10 PyObject * 0 0 146 0 0 0 0 0 0 0 0 0 0

146 8 PyObject 0 2105344 8 PyObject 8 PyObject 0 0 147 0 0 0 0 0 0 0 0 0 0

147 7 _object 0 1024 7 _object 7 _object 0 0 0 0 0 0 0 0 0 0 0 0 0

148 19 TypedObject const * 0 8576 19 TypedObject const * 19 TypedObject const * 0 0 149 0 0 0 0 0 0 0 0 0 0

149 17 TypedObject const 0 8832 17 TypedObject const 17 TypedObject const 0 0 124 0 0 0 0 0 0 0 0 0 0

1
150 11 END_PUBLISH 0 0 0 0 0 13 __end_publish 
7
151 5 index 0 2 119 81 0 0 0 0 0 0 0 17 TypeHandle::index 0

152 4 name 0 2 120 70 0 0 0 0 0 0 0 16 TypeHandle::name 0

153 14 parent_classes 0 66 117 73 0 0 0 0 72 0 0 26 TypeHandle::parent_classes 0

154 13 child_classes 0 66 117 75 0 0 0 0 74 0 0 25 TypeHandle::child_classes 0

155 11 typehandles 0 66 117 95 0 0 0 0 94 0 0 25 TypeRegistry::typehandles 0

156 12 root_classes 0 66 117 97 0 0 0 0 96 0 0 26 TypeRegistry::root_classes 0

157 4 type 0 2 117 111 0 0 0 0 0 0 0 17 TypedObject::type 58
// Returns the TypeHandle representing this object's type.

2
158 15 get_typehandles 0 94 95 29 TypeRegistry::get_typehandles 0

159 16 get_root_classes 0 96 97 30 TypeRegistry::get_root_classes 0

