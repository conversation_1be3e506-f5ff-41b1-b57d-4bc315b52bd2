'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_VERSION_WGL_1_0'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_VERSION_WGL_1_0',error_checker=_errors._error_checker)
WGL_FONT_LINES=_C('WGL_FONT_LINES',0)
WGL_FONT_POLYGONS=_C('WGL_FONT_POLYGONS',1)
WGL_SWAP_MAIN_PLANE=_C('WGL_SWAP_MAIN_PLANE',0x00000001)
WGL_SWAP_OVERLAY1=_C('WGL_SWAP_OVERLAY1',0x00000002)
WGL_SWAP_OVERLAY10=_C('WGL_SWAP_OVERLAY10',0x00000400)
WGL_SWAP_OVERLAY11=_C('WGL_SWAP_OVERLAY11',0x00000800)
WGL_SWAP_OVERLAY12=_C('WGL_SWAP_OVERLAY12',0x00001000)
WGL_SWAP_OVERLAY13=_C('WGL_SWAP_OVERLAY13',0x00002000)
WGL_SWAP_OVERLAY14=_C('WGL_SWAP_OVERLAY14',0x00004000)
WGL_SWAP_OVERLAY15=_C('WGL_SWAP_OVERLAY15',0x00008000)
WGL_SWAP_OVERLAY2=_C('WGL_SWAP_OVERLAY2',0x00000004)
WGL_SWAP_OVERLAY3=_C('WGL_SWAP_OVERLAY3',0x00000008)
WGL_SWAP_OVERLAY4=_C('WGL_SWAP_OVERLAY4',0x00000010)
WGL_SWAP_OVERLAY5=_C('WGL_SWAP_OVERLAY5',0x00000020)
WGL_SWAP_OVERLAY6=_C('WGL_SWAP_OVERLAY6',0x00000040)
WGL_SWAP_OVERLAY7=_C('WGL_SWAP_OVERLAY7',0x00000080)
WGL_SWAP_OVERLAY8=_C('WGL_SWAP_OVERLAY8',0x00000100)
WGL_SWAP_OVERLAY9=_C('WGL_SWAP_OVERLAY9',0x00000200)
WGL_SWAP_UNDERLAY1=_C('WGL_SWAP_UNDERLAY1',0x00010000)
WGL_SWAP_UNDERLAY10=_C('WGL_SWAP_UNDERLAY10',0x02000000)
WGL_SWAP_UNDERLAY11=_C('WGL_SWAP_UNDERLAY11',0x04000000)
WGL_SWAP_UNDERLAY12=_C('WGL_SWAP_UNDERLAY12',0x08000000)
WGL_SWAP_UNDERLAY13=_C('WGL_SWAP_UNDERLAY13',0x10000000)
WGL_SWAP_UNDERLAY14=_C('WGL_SWAP_UNDERLAY14',0x20000000)
WGL_SWAP_UNDERLAY15=_C('WGL_SWAP_UNDERLAY15',0x40000000)
WGL_SWAP_UNDERLAY2=_C('WGL_SWAP_UNDERLAY2',0x00020000)
WGL_SWAP_UNDERLAY3=_C('WGL_SWAP_UNDERLAY3',0x00040000)
WGL_SWAP_UNDERLAY4=_C('WGL_SWAP_UNDERLAY4',0x00080000)
WGL_SWAP_UNDERLAY5=_C('WGL_SWAP_UNDERLAY5',0x00100000)
WGL_SWAP_UNDERLAY6=_C('WGL_SWAP_UNDERLAY6',0x00200000)
WGL_SWAP_UNDERLAY7=_C('WGL_SWAP_UNDERLAY7',0x00400000)
WGL_SWAP_UNDERLAY8=_C('WGL_SWAP_UNDERLAY8',0x00800000)
WGL_SWAP_UNDERLAY9=_C('WGL_SWAP_UNDERLAY9',0x01000000)
@_f
@_p.types(_cs.c_int,_cs.HDC,ctypes.POINTER(_cs.PIXELFORMATDESCRIPTOR))
def ChoosePixelFormat(hDc,pPfd):pass
@_f
@_p.types(_cs.c_int,_cs.HDC,_cs.c_int,_cs.UINT,ctypes.POINTER(_cs.PIXELFORMATDESCRIPTOR))
def DescribePixelFormat(hdc,ipfd,cjpfd,ppfd):pass
@_f
@_p.types(_cs.UINT,_cs.HENHMETAFILE,_cs.UINT,ctypes.POINTER(_cs.PIXELFORMATDESCRIPTOR))
def GetEnhMetaFilePixelFormat(hemf,cbBuffer,ppfd):pass
@_f
@_p.types(_cs.c_int,_cs.HDC)
def GetPixelFormat(hdc):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.c_int,ctypes.POINTER(_cs.PIXELFORMATDESCRIPTOR))
def SetPixelFormat(hdc,ipfd,ppfd):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC)
def SwapBuffers(hdc):pass
@_f
@_p.types(_cs.BOOL,_cs.HGLRC,_cs.HGLRC,_cs.UINT)
def wglCopyContext(hglrcSrc,hglrcDst,mask):pass
@_f
@_p.types(_cs.HGLRC,_cs.HDC)
def wglCreateContext(hDc):pass
@_f
@_p.types(_cs.HGLRC,_cs.HDC,_cs.c_int)
def wglCreateLayerContext(hDc,level):pass
@_f
@_p.types(_cs.BOOL,_cs.HGLRC)
def wglDeleteContext(oldContext):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.c_int,_cs.c_int,_cs.UINT,ctypes.POINTER(_cs.LAYERPLANEDESCRIPTOR))
def wglDescribeLayerPlane(hDc,pixelFormat,layerPlane,nBytes,plpd):pass
@_f
@_p.types(_cs.HGLRC,)
def wglGetCurrentContext():pass
@_f
@_p.types(_cs.HDC,)
def wglGetCurrentDC():pass
@_f
@_p.types(_cs.c_int,_cs.HDC,_cs.c_int,_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.COLORREF))
def wglGetLayerPaletteEntries(hdc,iLayerPlane,iStart,cEntries,pcr):pass
@_f
@_p.types(_cs.PROC,_cs.LPCSTR)
def wglGetProcAddress(lpszProc):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.HGLRC)
def wglMakeCurrent(hDc,newContext):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.c_int,_cs.BOOL)
def wglRealizeLayerPalette(hdc,iLayerPlane,bRealize):pass
@_f
@_p.types(_cs.c_int,_cs.HDC,_cs.c_int,_cs.c_int,_cs.c_int,ctypes.POINTER(_cs.COLORREF))
def wglSetLayerPaletteEntries(hdc,iLayerPlane,iStart,cEntries,pcr):pass
@_f
@_p.types(_cs.BOOL,_cs.HGLRC,_cs.HGLRC)
def wglShareLists(hrcSrvShare,hrcSrvSource):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.UINT)
def wglSwapLayerBuffers(hdc,fuFlags):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD)
def wglUseFontBitmaps(hDC,first,count,listBase):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD)
def wglUseFontBitmapsA(hDC,first,count,listBase):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD)
def wglUseFontBitmapsW(hDC,first,count,listBase):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD,_cs.FLOAT,_cs.FLOAT,_cs.c_int,_cs.LPGLYPHMETRICSFLOAT)
def wglUseFontOutlines(hDC,first,count,listBase,deviation,extrusion,format,lpgmf):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD,_cs.FLOAT,_cs.FLOAT,_cs.c_int,_cs.LPGLYPHMETRICSFLOAT)
def wglUseFontOutlinesA(hDC,first,count,listBase,deviation,extrusion,format,lpgmf):pass
@_f
@_p.types(_cs.BOOL,_cs.HDC,_cs.DWORD,_cs.DWORD,_cs.DWORD,_cs.FLOAT,_cs.FLOAT,_cs.c_int,_cs.LPGLYPHMETRICSFLOAT)
def wglUseFontOutlinesW(hDC,first,count,listBase,deviation,extrusion,format,lpgmf):pass
