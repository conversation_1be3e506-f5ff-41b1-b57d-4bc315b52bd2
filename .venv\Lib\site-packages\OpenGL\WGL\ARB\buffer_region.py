'''OpenGL extension ARB.buffer_region

This module customises the behaviour of the 
OpenGL.raw.WGL.ARB.buffer_region to provide a more 
Python-friendly API

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/ARB/buffer_region.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.ARB.buffer_region import *
from OpenGL.raw.WGL.ARB.buffer_region import _EXTENSION_NAME

def glInitBufferRegionARB():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION