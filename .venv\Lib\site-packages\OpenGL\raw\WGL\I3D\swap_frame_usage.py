'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_I3D_swap_frame_usage'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_I3D_swap_frame_usage',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.BOOL,)
def wglBeginFrameTrackingI3D():pass
@_f
@_p.types(_cs.BOOL,)
def wglEndFrameTrackingI3D():pass
@_f
@_p.types(_cs.BOOL,arrays.GLfloatArray)
def wglGetFrameUsageI3D(pUsage):pass
@_f
@_p.types(_cs.BOOL,ctypes.POINTER(_cs.DWORD),ctypes.POINTER(_cs.DWORD),arrays.GLfloatArray)
def wglQueryFrameTrackingI3D(pFrameCount,pMissedFrames,pLastMissedUsage):pass
