1730992849
3 3
9 libp3chan 4 8NZB 12 panda3d.core 
221
211 37 upcast_to_TypedWritableReferenceCount 0 12 432 48 AnimGroup::upcast_to_TypedWritableReferenceCount 0 1 11 52
upcast from AnimGroup to TypedWritableReferenceCount
84
TypedWritableReferenceCount *AnimGroup::upcast_to_TypedWritableReferenceCount(void);

212 21 downcast_to_AnimGroup 0 12 433 50 TypedWritableReferenceCount::downcast_to_AnimGroup 0 0 54
downcast from TypedWritableReferenceCount to AnimGroup
68
AnimGroup *TypedWritableReferenceCount::downcast_to_AnimGroup(void);

213 17 upcast_to_Namable 0 12 432 28 AnimGroup::upcast_to_Namable 0 1 12 32
upcast from AnimGroup to Namable
44
Namable *AnimGroup::upcast_to_Namable(void);

214 21 downcast_to_AnimGroup 0 12 434 30 Namable::downcast_to_AnimGroup 0 0 34
downcast from Namable to AnimGroup
48
AnimGroup *Namable::downcast_to_AnimGroup(void);

215 9 AnimGroup 0 260 432 20 AnimGroup::AnimGroup 0 2 1 2 607
// This is the normal AnimGroup constructor.

/**
 * The default constructor is protected: don't try to create an AnimGroup
 * without a parent.  To create an AnimChannel hierarchy, you must first
 * create an AnimBundle, and use that to create any subsequent children.
 */

/**
 * Creates a new AnimGroup, just like this one, without copying any children.
 * The new copy is added to the indicated parent.  Intended to be called by
 * make_copy() only.
 */

/**
 * Creates the AnimGroup, and adds it to the indicated parent.  The only way
 * to delete it subsequently is to delete the entire hierarchy.
 */
132
explicit AnimGroup::AnimGroup(AnimGroup *parent, std::string const &name);
inline AnimGroup::AnimGroup(AnimGroup const &) = default;

216 10 ~AnimGroup 0 518 432 21 AnimGroup::~AnimGroup 0 0 10
/**
 *
 */
36
virtual AnimGroup::~AnimGroup(void);

217 16 get_num_children 0 4 432 27 AnimGroup::get_num_children 0 1 3 58
/**
 * Returns the number of child nodes of the group.
 */
44
int AnimGroup::get_num_children(void) const;

218 9 get_child 0 4 432 20 AnimGroup::get_child 0 1 4 46
/**
 * Returns the nth child of the group.
 */
45
AnimGroup *AnimGroup::get_child(int n) const;

219 15 get_child_named 0 4 432 26 AnimGroup::get_child_named 0 1 5 250
/**
 * Returns the first child found with the indicated name, or NULL if no such
 * child exists.  This method searches only the children of this particular
 * AnimGroup; it does not recursively search the entire graph.  See also
 * find_child().
 */
69
AnimGroup *AnimGroup::get_child_named(std::string const &name) const;

220 10 find_child 0 4 432 21 AnimGroup::find_child 0 1 6 210
/**
 * Returns the first descendant found with the indicated name, or NULL if no
 * such descendant exists.  This method searches the entire graph beginning at
 * this AnimGroup; see also get_child_named().
 */
64
AnimGroup *AnimGroup::find_child(std::string const &name) const;

221 16 sort_descendants 0 4 432 27 AnimGroup::sort_descendants 0 1 7 262
/**
 * Sorts the children nodes at each level of the hierarchy into alphabetical
 * order.  This should be done after creating the hierarchy, to guarantee that
 * the correct names will match up together when the AnimBundle is later bound
 * to a PlayerRoot.
 */
39
void AnimGroup::sort_descendants(void);

222 6 output 0 6 432 17 AnimGroup::output 0 1 8 54
/**
 * Writes a one-line description of the group.
 */
56
virtual void AnimGroup::output(std::ostream &out) const;

223 5 write 0 6 432 16 AnimGroup::write 0 1 9 78
/**
 * Writes a brief description of the group and all of its descendants.
 */
73
virtual void AnimGroup::write(std::ostream &out, int indent_level) const;

224 14 get_class_type 0 4 432 25 AnimGroup::get_class_type 0 1 10 0
50
static TypeHandle AnimGroup::get_class_type(void);

225 10 AnimBundle 0 260 436 22 AnimBundle::AnimBundle 0 2 13 14 207
/**
 *
 */

/**
 *
 */

/**
 * Creates a new AnimBundle, just like this one, without copying any children.
 * The new copy is added to the indicated parent.  Intended to be called by
 * make_copy() only.
 */
158
inline explicit AnimBundle::AnimBundle(std::string const &name, PN_stdfloat fps, int num_frames);
inline AnimBundle::AnimBundle(AnimBundle const &) = default;

226 11 copy_bundle 0 4 436 23 AnimBundle::copy_bundle 0 1 15 236
/**
 * Returns a full copy of the bundle and its entire tree of nested AnimGroups.
 * However, the actual data stored in the leaves--that is, animation tables,
 * such as those stored in an AnimChannelMatrixXfmTable--will be shared.
 */
60
PointerTo< AnimBundle > AnimBundle::copy_bundle(void) const;

227 19 get_base_frame_rate 0 4 436 31 AnimBundle::get_base_frame_rate 0 1 16 307
/**
 * Returns the ideal number of frames per second of the animation, when it is
 * running at normal speed.  This may not be the same as the actual playing
 * frame rate, as it might have been adjusted through set_play_rate() on the
 * AnimControl object.  See AnimControl::get_effective_frame_rate().
 */
58
inline double AnimBundle::get_base_frame_rate(void) const;

228 14 get_num_frames 0 4 436 26 AnimBundle::get_num_frames 0 1 17 109
/**
 * Returns the number of frames of animation, or 0 if the animation has no
 * fixed number of frames.
 */
50
inline int AnimBundle::get_num_frames(void) const;

229 14 get_class_type 0 4 436 26 AnimBundle::get_class_type 0 1 18 0
51
static TypeHandle AnimBundle::get_class_type(void);

230 11 ~AnimBundle 0 516 436 23 AnimBundle::~AnimBundle 0 0 0
30
AnimBundle::~AnimBundle(void);

231 14 AnimBundleNode 0 260 439 30 AnimBundleNode::AnimBundleNode 0 1 19 330
/**
 * The AnimBundle and its node should be constructed together.  Generally, the
 * derived classes of AnimBundleNode will automatically create a AnimBundle of
 * the appropriate type, and pass it up to this constructor.
 */

/**
 * For internal use only.
 */

/**
 * Use make_copy() or copy_subgraph() to copy one of these.
 */
92
inline explicit AnimBundleNode::AnimBundleNode(std::string const &name, AnimBundle *bundle);

232 10 get_bundle 0 4 439 26 AnimBundleNode::get_bundle 0 1 20 10
/**
 *
 */
58
inline AnimBundle *AnimBundleNode::get_bundle(void) const;

233 16 find_anim_bundle 0 4 439 32 AnimBundleNode::find_anim_bundle 0 1 21 205
/**
 * Recursively walks the scene graph beginning at the indicated node (which
 * need not be an AnimBundleNode), and returns the first AnimBundle found.
 * Returns NULL if no AnimBundle can be found.
 */
69
static AnimBundle *AnimBundleNode::find_anim_bundle(PandaNode *root);

234 14 get_class_type 0 4 439 30 AnimBundleNode::get_class_type 0 1 22 0
55
static TypeHandle AnimBundleNode::get_class_type(void);

235 15 ~AnimBundleNode 0 516 439 31 AnimBundleNode::~AnimBundleNode 0 0 0
38
AnimBundleNode::~AnimBundleNode(void);

236 37 upcast_to_TypedWritableReferenceCount 0 12 442 48 PartGroup::upcast_to_TypedWritableReferenceCount 0 1 41 52
upcast from PartGroup to TypedWritableReferenceCount
84
TypedWritableReferenceCount *PartGroup::upcast_to_TypedWritableReferenceCount(void);

237 21 downcast_to_PartGroup 0 12 433 50 TypedWritableReferenceCount::downcast_to_PartGroup 0 0 54
downcast from TypedWritableReferenceCount to PartGroup
68
PartGroup *TypedWritableReferenceCount::downcast_to_PartGroup(void);

238 17 upcast_to_Namable 0 12 442 28 PartGroup::upcast_to_Namable 0 1 42 32
upcast from PartGroup to Namable
44
Namable *PartGroup::upcast_to_Namable(void);

239 21 downcast_to_PartGroup 0 12 434 30 Namable::downcast_to_PartGroup 0 0 34
downcast from Namable to PartGroup
48
PartGroup *Namable::downcast_to_PartGroup(void);

240 9 PartGroup 0 260 442 20 PartGroup::PartGroup 0 1 23 501
// This is the normal PartGroup constructor.

/**
 * This constructor is only intended for interal use and for derived classes.
 * You should normally use the non-default constructor, below.
 */

/**
 * This constructor is only intended for interal use and for derived classes.
 * You should normally use the make_copy() interface to make copies..
 */

/**
 * Creates the PartGroup, and adds it to the indicated parent.  The only way
 * to delete it subsequently is to delete the entire hierarchy.
 */
74
explicit PartGroup::PartGroup(PartGroup *parent, std::string const &name);

241 10 ~PartGroup 0 518 442 21 PartGroup::~PartGroup 0 0 10
/**
 *
 */
36
virtual PartGroup::~PartGroup(void);

242 18 is_character_joint 0 6 442 29 PartGroup::is_character_joint 0 1 24 156
/**
 * Returns true if this part is a CharacterJoint, false otherwise.  This is a
 * tiny optimization over is_of_type(CharacterType::get_class_type()).
 */
55
virtual bool PartGroup::is_character_joint(void) const;

243 9 make_copy 0 6 442 20 PartGroup::make_copy 0 1 25 110
/**
 * Allocates and returns a new copy of the node.  Children are not copied, but
 * see copy_subgraph().
 */
52
virtual PartGroup *PartGroup::make_copy(void) const;

244 13 copy_subgraph 0 4 442 24 PartGroup::copy_subgraph 0 1 26 84
/**
 * Allocates and returns a new copy of this node and of all of its children.
 */
48
PartGroup *PartGroup::copy_subgraph(void) const;

245 16 get_num_children 0 4 442 27 PartGroup::get_num_children 0 1 27 58
/**
 * Returns the number of child nodes of the group.
 */
44
int PartGroup::get_num_children(void) const;

246 9 get_child 0 4 442 20 PartGroup::get_child 0 1 28 46
/**
 * Returns the nth child of the group.
 */
45
PartGroup *PartGroup::get_child(int n) const;

247 15 get_child_named 0 4 442 26 PartGroup::get_child_named 0 1 29 250
/**
 * Returns the first child found with the indicated name, or NULL if no such
 * child exists.  This method searches only the children of this particular
 * PartGroup; it does not recursively search the entire graph.  See also
 * find_child().
 */
69
PartGroup *PartGroup::get_child_named(std::string const &name) const;

248 10 find_child 0 4 442 21 PartGroup::find_child 0 1 30 210
/**
 * Returns the first descendant found with the indicated name, or NULL if no
 * such descendant exists.  This method searches the entire graph beginning at
 * this PartGroup; see also get_child_named().
 */
64
PartGroup *PartGroup::find_child(std::string const &name) const;

249 16 sort_descendants 0 4 442 27 PartGroup::sort_descendants 0 1 31 262
/**
 * Sorts the children nodes at each level of the hierarchy into alphabetical
 * order.  This should be done after creating the hierarchy, to guarantee that
 * the correct names will match up together when the AnimBundle is later bound
 * to a PlayerRoot.
 */
39
void PartGroup::sort_descendants(void);

250 12 apply_freeze 0 4 442 23 PartGroup::apply_freeze 0 1 32 287
/**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */
62
bool PartGroup::apply_freeze(TransformState const *transform);

251 19 apply_freeze_matrix 0 6 442 30 PartGroup::apply_freeze_matrix 0 1 33 287
/**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */
112
virtual bool PartGroup::apply_freeze_matrix(LVecBase3 const &pos, LVecBase3 const &hpr, LVecBase3 const &scale);

252 19 apply_freeze_scalar 0 6 442 30 PartGroup::apply_freeze_scalar 0 1 34 287
/**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */
63
virtual bool PartGroup::apply_freeze_scalar(PN_stdfloat value);

253 13 apply_control 0 6 442 24 PartGroup::apply_control 0 1 35 311
/**
 * Specifies a node to influence this particular joint so that it will always
 * hold the node's transform.  Returns true if this is a joint that can be so
 * controlled, false otherwise.
 *
 * This is normally only called internally by PartBundle::control_joint(), but
 * you may also call it directly.
 */
55
virtual bool PartGroup::apply_control(PandaNode *node);

254 20 clear_forced_channel 0 6 442 31 PartGroup::clear_forced_channel 0 1 36 261
/**
 * Undoes the effect of a previous call to apply_freeze() or apply_control().
 * Returns true if the joint was modified, false otherwise.
 *
 * This is normally only called internally by PartBundle::release_joint(), but
 * you may also call it directly.
 */
51
virtual bool PartGroup::clear_forced_channel(void);

255 18 get_forced_channel 0 6 442 29 PartGroup::get_forced_channel 0 1 37 181
/**
 * Returns the AnimChannelBase that has been forced to this joint by a
 * previous call to apply_freeze() or apply_control(), or NULL if no such
 * channel has been applied.
 */
67
virtual AnimChannelBase *PartGroup::get_forced_channel(void) const;

256 5 write 0 6 442 16 PartGroup::write 0 1 38 78
/**
 * Writes a brief description of the group and all of its descendants.
 */
73
virtual void PartGroup::write(std::ostream &out, int indent_level) const;

257 16 write_with_value 0 6 442 27 PartGroup::write_with_value 0 1 39 117
/**
 * Writes a brief description of the group, showing its current value, and
 * that of all of its descendants.
 */
84
virtual void PartGroup::write_with_value(std::ostream &out, int indent_level) const;

258 14 get_class_type 0 4 442 25 PartGroup::get_class_type 0 1 40 0
50
static TypeHandle PartGroup::get_class_type(void);

259 29 upcast_to_TypedReferenceCount 0 12 445 42 AnimControl::upcast_to_TypedReferenceCount 0 1 56 46
upcast from AnimControl to TypedReferenceCount
70
TypedReferenceCount *AnimControl::upcast_to_TypedReferenceCount(void);

260 23 downcast_to_AnimControl 0 12 446 44 TypedReferenceCount::downcast_to_AnimControl 0 0 48
downcast from TypedReferenceCount to AnimControl
64
AnimControl *TypedReferenceCount::downcast_to_AnimControl(void);

261 23 upcast_to_AnimInterface 0 12 445 36 AnimControl::upcast_to_AnimInterface 0 1 57 40
upcast from AnimControl to AnimInterface
58
AnimInterface *AnimControl::upcast_to_AnimInterface(void);

262 23 downcast_to_AnimControl 0 12 447 38 AnimInterface::downcast_to_AnimControl 0 0 42
downcast from AnimInterface to AnimControl
58
AnimControl *AnimInterface::downcast_to_AnimControl(void);

263 17 upcast_to_Namable 0 12 445 30 AnimControl::upcast_to_Namable 0 1 58 34
upcast from AnimControl to Namable
46
Namable *AnimControl::upcast_to_Namable(void);

264 23 downcast_to_AnimControl 0 12 434 32 Namable::downcast_to_AnimControl 0 0 36
downcast from Namable to AnimControl
52
AnimControl *Namable::downcast_to_AnimControl(void);

265 12 ~AnimControl 0 518 445 25 AnimControl::~AnimControl 0 0 10
/**
 *
 */
40
virtual AnimControl::~AnimControl(void);

266 10 is_pending 0 4 445 23 AnimControl::is_pending 0 1 43 286
/**
 * Returns true if the AnimControl is being bound asynchronously, and has not
 * yet finished.  If this is true, the AnimControl's interface is still
 * available and will be perfectly useful (though get_anim() might return
 * NULL), but nothing visible will happen immediately.
 */
48
inline bool AnimControl::is_pending(void) const;

267 12 wait_pending 0 4 445 25 AnimControl::wait_pending 0 1 44 102
/**
 * Blocks the current thread until the AnimControl has finished loading and is
 * fully bound.
 */
37
void AnimControl::wait_pending(void);

268 8 has_anim 0 4 445 21 AnimControl::has_anim 0 1 45 154
/**
 * Returns true if the AnimControl was successfully loaded, or false if there
 * was a problem.  This may return false while is_pending() is true.
 */
46
inline bool AnimControl::has_anim(void) const;

269 22 set_pending_done_event 0 4 445 35 AnimControl::set_pending_done_event 0 1 46 205
/**
 * Specifies an event name that will be thrown when the AnimControl is
 * finished binding asynchronously.  If the AnimControl has already finished
 * binding, the event will be thrown immediately.
 */
72
void AnimControl::set_pending_done_event(std::string const &done_event);

270 22 get_pending_done_event 0 4 445 35 AnimControl::get_pending_done_event 0 1 47 113
/**
 * Returns the event name that will be thrown when the AnimControl is finished
 * binding asynchronously.
 */
60
std::string AnimControl::get_pending_done_event(void) const;

271 8 get_part 0 4 445 21 AnimControl::get_part 0 1 48 65
/**
 * Returns the PartBundle bound in with this AnimControl.
 */
46
PartBundle *AnimControl::get_part(void) const;

272 8 get_anim 0 4 445 21 AnimControl::get_anim 0 1 49 65
/**
 * Returns the AnimBundle bound in with this AnimControl.
 */
53
inline AnimBundle *AnimControl::get_anim(void) const;

273 17 get_channel_index 0 4 445 30 AnimControl::get_channel_index 0 1 50 358
/**
 * Returns the particular channel index associated with this AnimControl.
 * This channel index is the slot on which each AnimGroup is bound to its
 * associated PartGroup, for each joint in the animation.
 *
 * It will be true that
 * get_part()->find_child("n")->get_bound(get_channel_index()) ==
 * get_anim()->find_child("n"), for each joint "n".
 */
54
inline int AnimControl::get_channel_index(void) const;

274 16 get_bound_joints 0 4 445 29 AnimControl::get_bound_joints 0 1 51 369
/**
 * Returns the subset of joints controlled by this AnimControl.  Most of the
 * time, this will be BitArray::all_on(), for a normal full-body animation.
 * For a subset animation, however, this will be just a subset of those bits,
 * corresponding to the set of joints and sliders actually bound (as
 * enumerated by bind_hierarchy() in depth-first LIFO order).
 */
65
inline BitArray const &AnimControl::get_bound_joints(void) const;

275 14 set_anim_model 0 4 445 27 AnimControl::set_anim_model 0 1 52 515
/**
 * Associates the indicated PandaNode with the AnimControl.  By convention,
 * this node represents the root node of the model file that corresponds to
 * this AnimControl's animation file, though nothing in this code makes this
 * assumption or indeed does anything with this node.
 *
 * The purpose of this is simply to allow the AnimControl to keep a reference
 * count on the ModelRoot node that generated it, so that the model will not
 * disappear from the model pool until it is no longer referenced.
 */
58
inline void AnimControl::set_anim_model(PandaNode *model);

276 14 get_anim_model 0 4 445 27 AnimControl::get_anim_model 0 1 53 81
/**
 * Retrieves the pointer set via set_anim_model().  See set_anim_model().
 */
58
inline PandaNode *AnimControl::get_anim_model(void) const;

277 6 output 0 6 445 19 AnimControl::output 0 1 54 10
/**
 *
 */
58
virtual void AnimControl::output(std::ostream &out) const;

278 14 get_class_type 0 4 445 27 AnimControl::get_class_type 0 1 55 0
52
static TypeHandle AnimControl::get_class_type(void);

279 8 get_type 0 6 448 25 AnimChannelBase::get_type 0 1 59 0
57
virtual TypeHandle AnimChannelBase::get_type(void) const;

280 14 get_class_type 0 4 448 31 AnimChannelBase::get_class_type 0 1 60 0
56
static TypeHandle AnimChannelBase::get_class_type(void);

281 16 ~AnimChannelBase 0 516 448 33 AnimChannelBase::~AnimChannelBase 0 0 0
40
AnimChannelBase::~AnimChannelBase(void);

282 9 get_value 0 6 449 44 AnimChannel< ACMatrixSwitchType >::get_value 0 1 61 0
90
virtual void AnimChannel< ACMatrixSwitchType >::get_value(int frame, LMatrix4 &value) = 0;

283 24 get_value_no_scale_shear 0 6 449 59 AnimChannel< ACMatrixSwitchType >::get_value_no_scale_shear 0 1 62 72
// These transform-component methods only have meaning for matrix types.
101
virtual void AnimChannel< ACMatrixSwitchType >::get_value_no_scale_shear(int frame, LMatrix4 &value);

284 9 get_scale 0 6 449 44 AnimChannel< ACMatrixSwitchType >::get_scale 0 1 63 0
87
virtual void AnimChannel< ACMatrixSwitchType >::get_scale(int frame, LVecBase3 &scale);

285 7 get_hpr 0 6 449 42 AnimChannel< ACMatrixSwitchType >::get_hpr 0 1 64 0
83
virtual void AnimChannel< ACMatrixSwitchType >::get_hpr(int frame, LVecBase3 &hpr);

286 8 get_quat 0 6 449 43 AnimChannel< ACMatrixSwitchType >::get_quat 0 1 65 0
87
virtual void AnimChannel< ACMatrixSwitchType >::get_quat(int frame, LQuaternion &quat);

287 7 get_pos 0 6 449 42 AnimChannel< ACMatrixSwitchType >::get_pos 0 1 66 0
83
virtual void AnimChannel< ACMatrixSwitchType >::get_pos(int frame, LVecBase3 &pos);

288 9 get_shear 0 6 449 44 AnimChannel< ACMatrixSwitchType >::get_shear 0 1 67 0
87
virtual void AnimChannel< ACMatrixSwitchType >::get_shear(int frame, LVecBase3 &shear);

289 14 get_value_type 0 6 449 49 AnimChannel< ACMatrixSwitchType >::get_value_type 0 1 68 0
81
virtual TypeHandle AnimChannel< ACMatrixSwitchType >::get_value_type(void) const;

290 14 get_class_type 0 4 449 49 AnimChannel< ACMatrixSwitchType >::get_class_type 0 1 69 0
74
static TypeHandle AnimChannel< ACMatrixSwitchType >::get_class_type(void);

291 9 get_value 0 6 453 44 AnimChannel< ACScalarSwitchType >::get_value 0 0 0
93
virtual void AnimChannel< ACScalarSwitchType >::get_value(int frame, PN_stdfloat &value) = 0;

292 24 get_value_no_scale_shear 0 6 453 59 AnimChannel< ACScalarSwitchType >::get_value_no_scale_shear 0 0 72
// These transform-component methods only have meaning for matrix types.
104
virtual void AnimChannel< ACScalarSwitchType >::get_value_no_scale_shear(int frame, PN_stdfloat &value);

293 9 get_scale 0 6 453 44 AnimChannel< ACScalarSwitchType >::get_scale 0 1 70 0
87
virtual void AnimChannel< ACScalarSwitchType >::get_scale(int frame, LVecBase3 &scale);

294 7 get_hpr 0 6 453 42 AnimChannel< ACScalarSwitchType >::get_hpr 0 1 71 0
83
virtual void AnimChannel< ACScalarSwitchType >::get_hpr(int frame, LVecBase3 &hpr);

295 8 get_quat 0 6 453 43 AnimChannel< ACScalarSwitchType >::get_quat 0 1 72 0
87
virtual void AnimChannel< ACScalarSwitchType >::get_quat(int frame, LQuaternion &quat);

296 7 get_pos 0 6 453 42 AnimChannel< ACScalarSwitchType >::get_pos 0 1 73 0
83
virtual void AnimChannel< ACScalarSwitchType >::get_pos(int frame, LVecBase3 &pos);

297 9 get_shear 0 6 453 44 AnimChannel< ACScalarSwitchType >::get_shear 0 1 74 0
87
virtual void AnimChannel< ACScalarSwitchType >::get_shear(int frame, LVecBase3 &shear);

298 14 get_value_type 0 6 453 49 AnimChannel< ACScalarSwitchType >::get_value_type 0 1 75 0
81
virtual TypeHandle AnimChannel< ACScalarSwitchType >::get_value_type(void) const;

299 14 get_class_type 0 4 453 49 AnimChannel< ACScalarSwitchType >::get_class_type 0 1 76 0
74
static TypeHandle AnimChannel< ACScalarSwitchType >::get_class_type(void);

300 9 set_value 0 4 455 35 AnimChannelMatrixDynamic::set_value 0 2 77 78 153
/**
 * Explicitly sets the matrix value.
 */

/**
 * Explicitly sets the matrix value, using the indicated TransformState object
 * as a convenience.
 */
135
void AnimChannelMatrixDynamic::set_value(LMatrix4 const &value);
void AnimChannelMatrixDynamic::set_value(TransformState const *value);

301 14 set_value_node 0 4 455 40 AnimChannelMatrixDynamic::set_value_node 0 1 79 124
/**
 * Specifies a node whose transform will be queried each frame to implicitly
 * specify the transform of this joint.
 */
63
void AnimChannelMatrixDynamic::set_value_node(PandaNode *node);

302 19 get_value_transform 0 4 455 45 AnimChannelMatrixDynamic::get_value_transform 0 1 80 93
/**
 * Returns the explicit TransformState value that was set via set_value(), if
 * any.
 */
87
inline TransformState const *AnimChannelMatrixDynamic::get_value_transform(void) const;

303 14 get_value_node 0 4 455 40 AnimChannelMatrixDynamic::get_value_node 0 1 81 70
/**
 * Returns the node that was set via set_value_node(), if any.
 */
71
inline PandaNode *AnimChannelMatrixDynamic::get_value_node(void) const;

304 14 get_class_type 0 4 455 40 AnimChannelMatrixDynamic::get_class_type 0 1 82 0
65
static TypeHandle AnimChannelMatrixDynamic::get_class_type(void);

305 25 ~AnimChannelMatrixDynamic 0 516 455 51 AnimChannelMatrixDynamic::~AnimChannelMatrixDynamic 0 0 0
58
AnimChannelMatrixDynamic::~AnimChannelMatrixDynamic(void);

306 25 AnimChannelMatrixXfmTable 0 260 457 52 AnimChannelMatrixXfmTable::AnimChannelMatrixXfmTable 0 1 83 248
/**
 * Used only for bam loader.
 */

/**
 * Creates a new AnimChannelMatrixXfmTable, just like this one, without
 * copying any children.  The new copy is added to the indicated parent.
 * Intended to be called by make_copy() only.
 */

/**
 *
 */
106
explicit AnimChannelMatrixXfmTable::AnimChannelMatrixXfmTable(AnimGroup *parent, std::string const &name);

307 11 is_valid_id 0 4 457 38 AnimChannelMatrixXfmTable::is_valid_id 0 1 84 80
/**
 * Returns true if the given letter is one of the nine valid table id's.
 */
73
static inline bool AnimChannelMatrixXfmTable::is_valid_id(char table_id);

308 9 set_table 0 4 457 36 AnimChannelMatrixXfmTable::set_table 0 1 85 255
/**
 * Assigns the indicated table.  table_id is one of 'i', 'j', 'k', for scale,
 * 'a', 'b', 'c' for shear, 'h', 'p', 'r', for rotation, and 'x', 'y', 'z',
 * for translation.  The new table must have either zero, one, or
 * get_num_frames() frames.
 */
85
void AnimChannelMatrixXfmTable::set_table(char table_id, CPTA_stdfloat const &table);

309 9 get_table 0 4 457 36 AnimChannelMatrixXfmTable::get_table 0 1 86 103
/**
 * Returns a pointer to the indicated subtable's data, if it exists, or NULL
 * if it does not.
 */
79
inline CPTA_stdfloat AnimChannelMatrixXfmTable::get_table(char table_id) const;

310 16 clear_all_tables 0 4 457 43 AnimChannelMatrixXfmTable::clear_all_tables 0 1 87 90
/**
 * Removes all the tables from the channel, and resets it to its initial
 * state.
 */
55
void AnimChannelMatrixXfmTable::clear_all_tables(void);

311 9 has_table 0 4 457 36 AnimChannelMatrixXfmTable::has_table 0 1 88 68
/**
 * Returns true if the indicated subtable has been assigned.
 */
70
inline bool AnimChannelMatrixXfmTable::has_table(char table_id) const;

312 11 clear_table 0 4 457 38 AnimChannelMatrixXfmTable::clear_table 0 1 89 59
/**
 * Removes the indicated table from the definition.
 */
66
inline void AnimChannelMatrixXfmTable::clear_table(char table_id);

313 14 get_class_type 0 4 457 41 AnimChannelMatrixXfmTable::get_class_type 0 1 90 0
66
static TypeHandle AnimChannelMatrixXfmTable::get_class_type(void);

314 9 set_value 0 4 461 35 AnimChannelScalarDynamic::set_value 0 1 91 98
/**
 * Explicitly sets the value.  This will remove any node assigned via
 * set_value_node().
 */
60
void AnimChannelScalarDynamic::set_value(PN_stdfloat value);

315 14 set_value_node 0 4 461 40 AnimChannelScalarDynamic::set_value_node 0 1 92 178
/**
 * Specifies a node whose transform will be queried each frame to implicitly
 * specify the transform of this joint.  This will override the values set by
 * set_value().
 */
63
void AnimChannelScalarDynamic::set_value_node(PandaNode *node);

316 9 get_value 0 4 461 35 AnimChannelScalarDynamic::get_value 0 0 235
/**
 * Gets the value of the channel.  This will return the value explicitly
 * specified by set_value() unless a value node was specified using
 * set_value_node().
 */

/**
 * Gets the value of the channel at the indicated frame.
 */
67
inline PN_stdfloat AnimChannelScalarDynamic::get_value(void) const;

317 14 get_value_node 0 4 461 40 AnimChannelScalarDynamic::get_value_node 0 0 70
/**
 * Returns the node that was set via set_value_node(), if any.
 */
71
inline PandaNode *AnimChannelScalarDynamic::get_value_node(void) const;

318 14 get_class_type 0 4 461 40 AnimChannelScalarDynamic::get_class_type 0 1 93 0
65
static TypeHandle AnimChannelScalarDynamic::get_class_type(void);

319 25 ~AnimChannelScalarDynamic 0 516 461 51 AnimChannelScalarDynamic::~AnimChannelScalarDynamic 0 0 0
58
AnimChannelScalarDynamic::~AnimChannelScalarDynamic(void);

320 22 AnimChannelScalarTable 0 260 464 46 AnimChannelScalarTable::AnimChannelScalarTable 0 1 94 220
/**
 *
 */

/**
 * Creates a new AnimChannelScalarTable, just like this one, without copying
 * any children.  The new copy is added to the indicated parent.  Intended to
 * be called by make_copy() only.
 */

/**
 *
 */
91
AnimChannelScalarTable::AnimChannelScalarTable(AnimGroup *parent, std::string const &name);

321 9 set_table 0 4 464 33 AnimChannelScalarTable::set_table 0 1 95 34
/**
 * Assigns the data table.
 */
67
void AnimChannelScalarTable::set_table(CPTA_stdfloat const &table);

322 9 get_table 0 4 464 33 AnimChannelScalarTable::get_table 0 1 96 90
/**
 * Returns a pointer to the table's data, if it exists, or NULL if it does
 * not.
 */
67
inline CPTA_stdfloat AnimChannelScalarTable::get_table(void) const;

323 9 has_table 0 4 464 33 AnimChannelScalarTable::has_table 0 1 97 60
/**
 * Returns true if the data table has been assigned.
 */
58
inline bool AnimChannelScalarTable::has_table(void) const;

324 11 clear_table 0 4 464 35 AnimChannelScalarTable::clear_table 0 1 98 34
/**
 * Empties the data table.
 */
54
inline void AnimChannelScalarTable::clear_table(void);

325 14 get_class_type 0 4 464 38 AnimChannelScalarTable::get_class_type 0 1 99 0
63
static TypeHandle AnimChannelScalarTable::get_class_type(void);

326 23 ~AnimChannelScalarTable 0 516 464 47 AnimChannelScalarTable::~AnimChannelScalarTable 0 0 0
54
AnimChannelScalarTable::~AnimChannelScalarTable(void);

327 21 AnimControlCollection 0 260 465 44 AnimControlCollection::AnimControlCollection 0 2 100 101 117
/**
 * Returns the AnimControl associated with the given name, or NULL if no such
 * control has been associated.
 */
145
AnimControlCollection::AnimControlCollection(void);
inline AnimControlCollection::AnimControlCollection(AnimControlCollection const &) = default;

328 22 ~AnimControlCollection 0 516 465 45 AnimControlCollection::~AnimControlCollection 0 0 10
/**
 *
 */
52
AnimControlCollection::~AnimControlCollection(void);

329 10 store_anim 0 4 465 33 AnimControlCollection::store_anim 0 1 102 247
/**
 * Associates the given AnimControl with this collection under the given name.
 * The AnimControl will remain associated until a new AnimControl is
 * associated with the same name later, or until unbind_anim() is called with
 * this name.
 */
86
void AnimControlCollection::store_anim(AnimControl *control, std::string const &name);

330 9 find_anim 0 4 465 32 AnimControlCollection::find_anim 0 1 103 117
/**
 * Returns the AnimControl associated with the given name, or NULL if no such
 * control has been associated.
 */
77
AnimControl *AnimControlCollection::find_anim(std::string const &name) const;

331 11 unbind_anim 0 4 465 34 AnimControlCollection::unbind_anim 0 1 104 184
/**
 * Removes the AnimControl associated with the given name, if any.  Returns
 * true if an AnimControl was removed, false if there was no AnimControl with
 * the indicated name.
 */
65
bool AnimControlCollection::unbind_anim(std::string const &name);

332 13 get_num_anims 0 4 465 36 AnimControlCollection::get_num_anims 0 1 105 78
/**
 * Returns the number of AnimControls associated with this collection.
 */
53
int AnimControlCollection::get_num_anims(void) const;

333 8 get_anim 0 4 465 31 AnimControlCollection::get_anim 0 1 106 71
/**
 * Returns the nth AnimControl associated with this collection.
 */
58
AnimControl *AnimControlCollection::get_anim(int n) const;

334 13 get_anim_name 0 4 465 36 AnimControlCollection::get_anim_name 0 1 107 83
/**
 * Returns the name of the nth AnimControl associated with this collection.
 */
62
std::string AnimControlCollection::get_anim_name(int n) const;

335 11 clear_anims 0 4 465 34 AnimControlCollection::clear_anims 0 1 108 56
/**
 * Disassociates all anims from this collection.
 */
46
void AnimControlCollection::clear_anims(void);

336 4 play 0 4 465 27 AnimControlCollection::play 0 2 109 110 94
/**
 * Starts the named animation playing.
 */

/**
 * Starts the named animation playing.
 */
165
inline bool AnimControlCollection::play(std::string const &anim_name);
inline bool AnimControlCollection::play(std::string const &anim_name, double from, double to);

337 4 loop 0 4 465 27 AnimControlCollection::loop 0 2 111 112 94
/**
 * Starts the named animation looping.
 */

/**
 * Starts the named animation looping.
 */
193
inline bool AnimControlCollection::loop(std::string const &anim_name, bool restart);
inline bool AnimControlCollection::loop(std::string const &anim_name, bool restart, double from, double to);

338 4 stop 0 4 465 27 AnimControlCollection::stop 0 1 113 37
/**
 * Stops the named animation.
 */
70
inline bool AnimControlCollection::stop(std::string const &anim_name);

339 4 pose 0 4 465 27 AnimControlCollection::pose 0 1 114 61
/**
 * Sets to a particular frame in the named animation.
 */
84
inline bool AnimControlCollection::pose(std::string const &anim_name, double frame);

340 8 play_all 0 4 465 31 AnimControlCollection::play_all 0 2 115 116 134
// These functions operate on all anims at once.

/**
 * Starts all animations playing.
 */

/**
 * Starts all animations playing.
 */
105
void AnimControlCollection::play_all(void);
void AnimControlCollection::play_all(double from, double to);

341 8 loop_all 0 4 465 31 AnimControlCollection::loop_all 0 2 117 118 84
/**
 * Starts all animations looping.
 */

/**
 * Starts all animations looping.
 */
127
void AnimControlCollection::loop_all(bool restart);
void AnimControlCollection::loop_all(bool restart, double from, double to);

342 8 stop_all 0 4 465 31 AnimControlCollection::stop_all 0 1 119 127
/**
 * Stops all currently playing animations.  Returns true if any animations
 * were stopped, false if none were playing.
 */
43
bool AnimControlCollection::stop_all(void);

343 8 pose_all 0 4 465 31 AnimControlCollection::pose_all 0 1 120 54
/**
 * Sets all animations to the indicated frame.
 */
51
void AnimControlCollection::pose_all(double frame);

344 9 get_frame 0 4 465 32 AnimControlCollection::get_frame 0 2 121 122 168
/**
 * Returns the current frame in the named animation, or 0 if the animation is
 * not found.
 */

/**
 * Returns the current frame in the last-started animation.
 */
137
inline int AnimControlCollection::get_frame(std::string const &anim_name) const;
inline int AnimControlCollection::get_frame(void) const;

345 14 get_num_frames 0 4 465 37 AnimControlCollection::get_num_frames 0 2 123 124 186
/**
 * Returns the total number of frames in the named animation, or 0 if the
 * animation is not found.
 */

/**
 * Returns the total number of frames in the last-started animation.
 */
147
inline int AnimControlCollection::get_num_frames(std::string const &anim_name) const;
inline int AnimControlCollection::get_num_frames(void) const;

346 10 is_playing 0 4 465 33 AnimControlCollection::is_playing 0 2 125 126 182
/**
 * Returns true if the named animation is currently playing, false otherwise.
 */

/**
 * Returns true if the last-started animation is currently playing, false
 * otherwise.
 */
141
inline bool AnimControlCollection::is_playing(std::string const &anim_name) const;
inline bool AnimControlCollection::is_playing(void) const;

347 18 which_anim_playing 0 4 465 41 AnimControlCollection::which_anim_playing 0 1 127 183
/**
 * Returns the name of the bound AnimControl currently playing, if any.  If
 * more than one AnimControl is currently playing, returns all of the names
 * separated by spaces.
 */
66
std::string AnimControlCollection::which_anim_playing(void) const;

348 6 output 0 4 465 29 AnimControlCollection::output 0 1 128 10
/**
 *
 */
60
void AnimControlCollection::output(std::ostream &out) const;

349 5 write 0 4 465 28 AnimControlCollection::write 0 1 129 10
/**
 *
 */
59
void AnimControlCollection::write(std::ostream &out) const;

350 16 AnimPreloadTable 0 260 466 34 AnimPreloadTable::AnimPreloadTable 0 1 130 10
/**
 *
 */
41
AnimPreloadTable::AnimPreloadTable(void);

351 13 get_num_anims 0 4 466 31 AnimPreloadTable::get_num_anims 0 1 131 64
/**
 * Returns the number of animation records in the table.
 */
48
int AnimPreloadTable::get_num_anims(void) const;

352 9 find_anim 0 4 466 27 AnimPreloadTable::find_anim 0 1 132 321
/**
 * Returns the index number in the table of the animation record with the
 * indicated name, or -1 if the name is not present.  By convention, the
 * basename is the filename of the egg or bam file, without the directory part
 * and without the extension.  That is, it is
 * Filename::get_basename_wo_extension().
 */
67
int AnimPreloadTable::find_anim(std::string const &basename) const;

353 12 get_basename 0 4 466 30 AnimPreloadTable::get_basename 0 1 133 86
/**
 * Returns the basename stored for the nth animation record.  See find_anim().
 */
63
inline std::string AnimPreloadTable::get_basename(int n) const;

354 19 get_base_frame_rate 0 4 466 37 AnimPreloadTable::get_base_frame_rate 0 1 134 70
/**
 * Returns the frame rate stored for the nth animation record.
 */
70
inline PN_stdfloat AnimPreloadTable::get_base_frame_rate(int n) const;

355 14 get_num_frames 0 4 466 32 AnimPreloadTable::get_num_frames 0 1 135 76
/**
 * Returns the number of frames stored for the nth animation record.
 */
57
inline int AnimPreloadTable::get_num_frames(int n) const;

356 11 clear_anims 0 4 466 29 AnimPreloadTable::clear_anims 0 1 136 56
/**
 * Removes all animation records from the table.
 */
41
void AnimPreloadTable::clear_anims(void);

357 11 remove_anim 0 4 466 29 AnimPreloadTable::remove_anim 0 1 137 113
/**
 * Removes the nth animation records from the table.  This renumbers indexes
 * for following animations.
 */
42
void AnimPreloadTable::remove_anim(int n);

358 8 add_anim 0 4 466 26 AnimPreloadTable::add_anim 0 1 138 227
/**
 * Adds a new animation record to the table.  If there is already a record of
 * this name, no operation is performed (the original record is unchanged).
 * See find_anim().  This will invalidate existing index numbers.
 */
106
void AnimPreloadTable::add_anim(std::string const &basename, PN_stdfloat base_frame_rate, int num_frames);

359 14 add_anims_from 0 4 466 32 AnimPreloadTable::add_anims_from 0 1 139 160
/**
 * Copies the animation records from the other table into this one.  If a
 * given record name exists in both tables, the record in this one supercedes.
 */
69
void AnimPreloadTable::add_anims_from(AnimPreloadTable const *other);

360 6 output 0 6 466 24 AnimPreloadTable::output 0 1 140 10
/**
 *
 */
63
virtual void AnimPreloadTable::output(std::ostream &out) const;

361 5 write 0 6 466 23 AnimPreloadTable::write 0 1 141 10
/**
 *
 */
80
virtual void AnimPreloadTable::write(std::ostream &out, int indent_level) const;

362 14 get_class_type 0 4 466 32 AnimPreloadTable::get_class_type 0 1 142 0
57
static TypeHandle AnimPreloadTable::get_class_type(void);

363 9 auto_bind 0 1 0 9 auto_bind 0 1 210 0
101
void auto_bind(PandaNode *root_node, AnimControlCollection &controls, int hierarchy_match_flags = 0);

364 10 PartSubset 0 260 468 22 PartSubset::PartSubset 0 2 143 144 22
/**
 *
 */

/**
 *
 */
77
PartSubset::PartSubset(void);
PartSubset::PartSubset(PartSubset const &copy);

365 10 operator = 0 4 468 22 PartSubset::operator = 0 1 145 0
52
void PartSubset::operator =(PartSubset const &copy);

366 17 add_include_joint 0 4 468 29 PartSubset::add_include_joint 0 1 146 342
/**
 * Adds the named joint to the list of joints that will be explicitly included
 * in the subset.  Any joint at or below a named node will be included in the
 * subset (unless a lower node is also listed in the exclude list).
 *
 * Since the name is a GlobPattern, it may of course include filename globbing
 * characters like * and ?.
 */
60
void PartSubset::add_include_joint(GlobPattern const &name);

367 17 add_exclude_joint 0 4 468 29 PartSubset::add_exclude_joint 0 1 147 352
/**
 * Adds the named joint to the list of joints that will be explicitly
 * exlcluded from the subset.  Any joint at or below a named node will not be
 * included in the subset (unless a lower node is also listed in the include
 * list).
 *
 * Since the name is a GlobPattern, it may of course include filename globbing
 * characters like * and ?.
 */
60
void PartSubset::add_exclude_joint(GlobPattern const &name);

368 6 append 0 4 468 18 PartSubset::append 0 1 148 98
/**
 * Appends the include and exclude list from the other object onto this
 * object's lists.
 */
49
void PartSubset::append(PartSubset const &other);

369 6 output 0 4 468 18 PartSubset::output 0 1 149 10
/**
 *
 */
49
void PartSubset::output(std::ostream &out) const;

370 16 is_include_empty 0 4 468 28 PartSubset::is_include_empty 0 1 150 147
/**
 * Returns true if the include list is completely empty, false otherwise.  If
 * it is empty, it is the same thing as including all joints.
 */
46
bool PartSubset::is_include_empty(void) const;

371 15 matches_include 0 4 468 27 PartSubset::matches_include 0 1 151 101
/**
 * Returns true if the indicated name matches a name on the include list,
 * false otherwise.
 */
70
bool PartSubset::matches_include(std::string const &joint_name) const;

372 15 matches_exclude 0 4 468 27 PartSubset::matches_exclude 0 1 152 101
/**
 * Returns true if the indicated name matches a name on the exclude list,
 * false otherwise.
 */
70
bool PartSubset::matches_exclude(std::string const &joint_name) const;

373 11 ~PartSubset 0 516 468 23 PartSubset::~PartSubset 0 0 0
30
PartSubset::~PartSubset(void);

374 15 BindAnimRequest 0 260 469 32 BindAnimRequest::BindAnimRequest 0 2 153 154 10
/**
 *
 */
290
explicit BindAnimRequest::BindAnimRequest(std::string const &name, Filename const &filename, LoaderOptions const &options, Loader *loader, AnimControl *control, int hierarchy_match_flags, PartSubset const &subset);
inline BindAnimRequest::BindAnimRequest(BindAnimRequest const &) = default;

375 14 get_class_type 0 4 469 31 BindAnimRequest::get_class_type 0 1 155 0
56
static TypeHandle BindAnimRequest::get_class_type(void);

376 16 ~BindAnimRequest 0 516 469 33 BindAnimRequest::~BindAnimRequest 0 0 0
40
BindAnimRequest::~BindAnimRequest(void);

377 10 PartBundle 0 260 471 22 PartBundle::PartBundle 0 1 156 220
/**
 * Normally, you'd use make_copy() or copy_subgraph() to make a copy of this.
 */

/**
 * Normally, a PartBundle constructor should not be called directly--it will
 * get created when a PartBundleNode is created.
 */
62
explicit PartBundle::PartBundle(std::string const &name = "");

378 16 get_anim_preload 0 4 471 28 PartBundle::get_anim_preload 0 1 157 181
/**
 * Returns the AnimPreloadTable associated with the PartBundle.  This table,
 * if present, can be used for the benefit of load_bind_anim() to allow
 * asynchronous binding.
 */
83
inline ConstPointerTo< AnimPreloadTable > PartBundle::get_anim_preload(void) const;

379 19 modify_anim_preload 0 4 471 31 PartBundle::modify_anim_preload 0 1 158 106
/**
 * Returns a modifiable pointer to the AnimPreloadTable associated with the
 * PartBundle, if any.
 */
75
inline PointerTo< AnimPreloadTable > PartBundle::modify_anim_preload(void);

380 16 set_anim_preload 0 4 471 28 PartBundle::set_anim_preload 0 1 159 72
/**
 * Replaces the AnimPreloadTable associated with the PartBundle.
 */
66
inline void PartBundle::set_anim_preload(AnimPreloadTable *table);

381 18 clear_anim_preload 0 4 471 30 PartBundle::clear_anim_preload 0 1 160 71
/**
 * Removes any AnimPreloadTable associated with the PartBundle.
 */
49
inline void PartBundle::clear_anim_preload(void);

382 19 merge_anim_preloads 0 4 471 31 PartBundle::merge_anim_preloads 0 1 161 85
/**
 * Copies the contents of the other PartBundle's preload table into this one.
 */
62
void PartBundle::merge_anim_preloads(PartBundle const *other);

383 14 set_blend_type 0 4 471 26 PartBundle::set_blend_type 0 1 162 270
/**
 * Defines the algorithm that is used when blending multiple frames or
 * multiple animations together, when either anim_blend_flag or
 * frame_blend_flag is set to true.
 *
 * See partBundle.h for a description of the meaning of each of the BlendType
 * values.
 */
65
inline void PartBundle::set_blend_type(PartBundle::BlendType bt);

384 14 get_blend_type 0 4 471 26 PartBundle::get_blend_type 0 1 163 178
/**
 * Returns the algorithm that is used when blending multiple frames or
 * multiple animations together, when either anim_blend_flag or
 * frame_blend_flag is set to true.
 */
68
inline PartBundle::BlendType PartBundle::get_blend_type(void) const;

385 19 set_anim_blend_flag 0 4 471 31 PartBundle::set_anim_blend_flag 0 1 164 581
/**
 * Defines the way the character responds to multiple calls to
 * set_control_effect()).  By default, this flag is set false, which disallows
 * multiple animations.  When this flag is false, it is not necessary to
 * explicitly set the control_effect when starting an animation; starting the
 * animation will implicitly remove the control_effect from the previous
 * animation and set it on the current one.
 *
 * However, if this flag is set true, the control_effect must be explicitly
 * set via set_control_effect() whenever an animation is to affect the
 * character.
 */
59
void PartBundle::set_anim_blend_flag(bool anim_blend_flag);

386 19 get_anim_blend_flag 0 4 471 31 PartBundle::get_anim_blend_flag 0 1 165 136
/**
 * Returns whether the character allows multiple different animations to be
 * bound simultaneously.  See set_anim_blend_flag().
 */
56
inline bool PartBundle::get_anim_blend_flag(void) const;

387 20 set_frame_blend_flag 0 4 471 32 PartBundle::set_frame_blend_flag 0 1 166 775
/**
 * Specifies whether the character interpolates (blends) between two
 * sequential frames of an active animation, showing a smooth intra-frame
 * motion, or whether it holds each frame until the next frame is ready,
 * showing precisely the specified animation.
 *
 * When this value is false, the character holds each frame until the next is
 * ready.  When this is true, the character will interpolate between two
 * consecutive frames of animation for each frame the animation is onscreen,
 * according to the amount of time elapsed between the frames.
 *
 * The default value of this flag is determined by the interpolate-frames
 * Config.prc variable.
 *
 * Use set_blend_type() to change the algorithm that the character uses to
 * interpolate matrix positions.
 */
68
inline void PartBundle::set_frame_blend_flag(bool frame_blend_flag);

388 20 get_frame_blend_flag 0 4 471 32 PartBundle::get_frame_blend_flag 0 1 167 205
/**
 * Returns whether the character interpolates (blends) between two sequential
 * animation frames, or whether it holds the current frame until the next one
 * is ready.  See set_frame_blend_flag().
 */
57
inline bool PartBundle::get_frame_blend_flag(void) const;

389 14 set_root_xform 0 4 471 26 PartBundle::set_root_xform 0 1 168 111
/**
 * Specifies the transform matrix which is implicitly applied at the root of
 * the animated hierarchy.
 */
67
inline void PartBundle::set_root_xform(LMatrix4 const &root_xform);

390 5 xform 0 4 471 17 PartBundle::xform 0 1 169 81
/**
 * Applies the indicated transform to the root of the animated hierarchy.
 */
51
inline void PartBundle::xform(LMatrix4 const &mat);

391 14 get_root_xform 0 4 471 26 PartBundle::get_root_xform 0 1 170 109
/**
 * Returns the transform matrix which is implicitly applied at the root of the
 * animated hierarchy.
 */
62
inline LMatrix4 const &PartBundle::get_root_xform(void) const;

392 15 apply_transform 0 4 471 27 PartBundle::apply_transform 0 1 171 226
/**
 * Returns a PartBundle that is a duplicate of this one, but with the
 * indicated transform applied.  If this is called multiple times with the
 * same TransformState pointer, it returns the same PartBundle each time.
 */
85
PointerTo< PartBundle > PartBundle::apply_transform(TransformState const *transform);

393 13 get_num_nodes 0 4 471 25 PartBundle::get_num_nodes 0 1 172 94
/**
 * Returns the number of PartBundleNodes that contain a pointer to this
 * PartBundle.
 */
49
inline int PartBundle::get_num_nodes(void) const;

394 8 get_node 0 4 471 20 PartBundle::get_node 0 1 173 74
/**
 * Returns the nth PartBundleNode associated with this PartBundle.
 */
57
inline PartBundleNode *PartBundle::get_node(int n) const;

395 14 PartBundleNode 0 260 476 30 PartBundleNode::PartBundleNode 0 1 189 388
/**
 * The PartBundle and its node should be constructed together.  Generally, the
 * derived classes of PartBundleNode will automatically create a PartBundle of
 * the appropriate type, and pass it up to this constructor.
 */

/**
 * For internal use only.
 */

/**
 * Use make_copy() or copy_subgraph() to copy one of these.
 *
 * This constructor does not copy the bundle pointers.
 */
92
inline explicit PartBundleNode::PartBundleNode(std::string const &name, PartBundle *bundle);

396 15 get_num_bundles 0 4 476 31 PartBundleNode::get_num_bundles 0 1 190 10
/**
 *
 */
55
inline int PartBundleNode::get_num_bundles(void) const;

397 10 get_bundle 0 4 476 26 PartBundleNode::get_bundle 0 1 191 10
/**
 *
 */
59
inline PartBundle *PartBundleNode::get_bundle(int n) const;

398 17 get_bundle_handle 0 4 476 33 PartBundleNode::get_bundle_handle 0 1 192 204
/**
 * Returns the PartBundleHandle that wraps around the actual nth PartBundle.
 * While the PartBundle pointer might later change due to a future flatten
 * operation, the PartBundleHandle will not.
 */
72
inline PartBundleHandle *PartBundleNode::get_bundle_handle(int n) const;

399 16 PartBundleHandle 0 260 479 34 PartBundleHandle::PartBundleHandle 0 2 194 195 10
/**
 *
 */
141
inline PartBundleHandle::PartBundleHandle(PartBundle *bundle);
inline PartBundleHandle::PartBundleHandle(PartBundleHandle const &) = default;

400 10 get_bundle 0 4 479 28 PartBundleHandle::get_bundle 0 1 196 68
/**
 * Returns the actual PartBundle embedded within the handle.
 */
54
inline PartBundle *PartBundleHandle::get_bundle(void);

401 10 set_bundle 0 4 479 28 PartBundleHandle::set_bundle 0 1 197 68
/**
 * Changes the actual PartBundle embedded within the handle.
 */
61
inline void PartBundleHandle::set_bundle(PartBundle *bundle);

402 14 get_class_type 0 4 476 30 PartBundleNode::get_class_type 0 1 193 0
55
static TypeHandle PartBundleNode::get_class_type(void);

403 21 clear_control_effects 0 4 471 33 PartBundle::clear_control_effects 0 1 174 518
/**
 * Sets the control effect of all AnimControls to zero (but does not "stop"
 * the AnimControls).  The character will no longer be affected by any
 * animation, and will return to its default pose (unless restore-initial-pose
 * is false).
 *
 * The AnimControls which are no longer associated will not be using any CPU
 * cycles, but they may still be in the "playing" state; if they are later
 * reassociated with the PartBundle they will resume at their current frame as
 * if they'd been running all along.
 */
45
void PartBundle::clear_control_effects(void);

404 18 set_control_effect 0 4 471 30 PartBundle::set_control_effect 0 1 175 530
/**
 * Sets the amount by which the character is affected by the indicated
 * AnimControl (and its associated animation).  Normally, this will only be
 * zero or one.  Zero indicates the animation does not affect the character,
 * and one means it does.
 *
 * If the _anim_blend_flag is not false (see set_anim_blend_flag()), it is
 * possible to have multiple AnimControls in effect simultaneously.  In this
 * case, the effect is a weight that indicates the relative importance of each
 * AnimControl to the final animation.
 */
85
inline void PartBundle::set_control_effect(AnimControl *control, PN_stdfloat effect);

405 18 get_control_effect 0 4 471 30 PartBundle::get_control_effect 0 1 176 153
/**
 * Returns the amount by which the character is affected by the indicated
 * AnimControl and its associated animation.  See set_control_effect().
 */
78
inline PN_stdfloat PartBundle::get_control_effect(AnimControl *control) const;

406 6 output 0 6 471 18 PartBundle::output 0 1 177 55
/**
 * Writes a one-line description of the bundle.
 */
57
virtual void PartBundle::output(std::ostream &out) const;

407 9 bind_anim 0 4 471 21 PartBundle::bind_anim 0 1 178 790
/**
 * Binds the animation to the bundle, if possible, and returns a new
 * AnimControl that can be used to start and stop the animation.  If the anim
 * hierarchy does not match the part hierarchy, returns NULL.
 *
 * If hierarchy_match_flags is 0, only an exact match is accepted; otherwise,
 * it may contain a union of PartGroup::HierarchyMatchFlags values indicating
 * conditions that will be tolerated (but warnings will still be issued).
 *
 * If subset is specified, it restricts the binding only to the named subtree
 * of joints.
 *
 * The AnimControl is not stored within the PartBundle; it is the user's
 * responsibility to maintain the pointer.  The animation will automatically
 * unbind itself when the AnimControl destructs (i.e.  its reference count
 * goes to zero).
 */
137
PointerTo< AnimControl > PartBundle::bind_anim(AnimBundle *anim, int hierarchy_match_flags = 0, PartSubset const &subset = PartSubset());

408 14 load_bind_anim 0 4 471 26 PartBundle::load_bind_anim 0 1 179 1088
/**
 * Binds an animation to the bundle.  The animation is loaded from the disk
 * via the indicated Loader object.  In other respects, this behaves similarly
 * to bind_anim(), with the addition of asynchronous support.
 *
 * If allow_aysnc is true, the load will be asynchronous if possible.  This
 * requires that the animation basename can be found in the PartBundle's
 * preload table (see get_anim_preload()).
 *
 * In an asynchronous load, the animation file will be loaded and bound in a
 * sub-thread.  This means that the animation will not necessarily be
 * available at the time this method returns.  You may still use the returned
 * AnimControl immediately, though, but no visible effect will occur until the
 * animation eventually becomes available.
 *
 * You can test AnimControl::is_pending() to see if the animation has been
 * loaded yet, or wait for it to finish with AnimControl::wait_pending() or
 * even PartBundle::wait_pending().  You can also set an event to be triggered
 * when the animation finishes loading with
 * AnimControl::set_pending_done_event().
 */
165
PointerTo< AnimControl > PartBundle::load_bind_anim(Loader *loader, Filename const &filename, int hierarchy_match_flags, PartSubset const &subset, bool allow_async);

409 12 wait_pending 0 4 471 24 PartBundle::wait_pending 0 1 180 153
/**
 * Blocks the current thread until all currently-pending AnimControls, with a
 * nonzero control effect, have been loaded and are properly bound.
 */
36
void PartBundle::wait_pending(void);

410 12 freeze_joint 0 4 471 24 PartBundle::freeze_joint 0 3 181 ************
/**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */

/**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */

/**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */
305
bool PartBundle::freeze_joint(std::string const &joint_name, TransformState const *transform);
bool PartBundle::freeze_joint(std::string const &joint_name, LVecBase3 const &pos, LVecBase3 const &hpr, LVecBase3 const &scale);
bool PartBundle::freeze_joint(std::string const &joint_name, PN_stdfloat value);

411 13 control_joint 0 4 471 25 PartBundle::control_joint 0 1 184 396
/**
 * Specifies that the joint with the indicated name should be animated with
 * the transform on the indicated node.  It will henceforth always follow the
 * node's transform, regardless of any animations that may subsequently be
 * bound to the joint.
 *
 * Returns true if the joint is successfully controlled, or false if the named
 * child is not a joint (or slider) or does not exist.
 */
79
bool PartBundle::control_joint(std::string const &joint_name, PandaNode *node);

412 13 release_joint 0 4 471 25 PartBundle::release_joint 0 1 185 342
/**
 * Releases the named joint from the effects of a previous call to
 * freeze_joint() or control_joint(). It will henceforth once again follow
 * whatever transforms are dictated by the animation.
 *
 * Returns true if the joint is released, or false if the named child was not
 * previously controlled or frozen, or it does not exist.
 */
62
bool PartBundle::release_joint(std::string const &joint_name);

413 6 update 0 4 471 18 PartBundle::update 0 1 186 216
/**
 * Updates all the parts in the bundle to reflect the data for the current
 * frame (as set in each of the AnimControls).
 *
 * Returns true if any part has changed as a result of this, or false
 * otherwise.
 */
30
bool PartBundle::update(void);

414 12 force_update 0 4 471 24 PartBundle::force_update 0 1 187 131
/**
 * Updates all the parts in the bundle to reflect the data for the current
 * frame, whether we believe it needs it or not.
 */
36
bool PartBundle::force_update(void);

415 14 get_class_type 0 4 471 26 PartBundle::get_class_type 0 1 188 0
51
static TypeHandle PartBundle::get_class_type(void);

416 11 ~PartBundle 0 516 471 23 PartBundle::~PartBundle 0 0 0
30
PartBundle::~PartBundle(void);

417 13 get_max_bound 0 4 481 29 MovingPartBase::get_max_bound 0 1 198 310
/**
 * Returns the number of channels that might be bound to this PartGroup.  This
 * might not be the actual number of channels, since there might be holes in
 * the list; it is one more than the index number of the highest bound
 * channel.  Thus, it is called get_max_bound() instead of get_num_bound().
 */
53
inline int MovingPartBase::get_max_bound(void) const;

418 9 get_bound 0 4 481 25 MovingPartBase::get_bound 0 1 199 371
/**
 * Returns the nth bound channel on this PartGroup.  n can be determined by
 * iterating from 0 to one less than get_max_bound(); or n might be
 * AnimControl::get_channel_index().
 *
 * This will return NULL if there is no channel bound on the indicated index.
 * It is an error to call this if n is less than zero or greater than or equal
 * to get_max_bound().
 */
63
inline AnimChannelBase *MovingPartBase::get_bound(int n) const;

419 12 output_value 0 6 481 28 MovingPartBase::output_value 0 1 200 0
71
virtual void MovingPartBase::output_value(std::ostream &out) const = 0;

420 14 get_class_type 0 4 481 30 MovingPartBase::get_class_type 0 1 201 0
55
static TypeHandle MovingPartBase::get_class_type(void);

421 15 ~MovingPartBase 0 516 481 31 MovingPartBase::~MovingPartBase 0 0 0
38
MovingPartBase::~MovingPartBase(void);

422 14 get_class_type 0 4 483 48 MovingPart< ACMatrixSwitchType >::get_class_type 0 1 203 0
73
static TypeHandle MovingPart< ACMatrixSwitchType >::get_class_type(void);

423 9 get_value 0 4 483 43 MovingPart< ACMatrixSwitchType >::get_value 0 1 204 0
72
inline LMatrix4 MovingPart< ACMatrixSwitchType >::get_value(void) const;

424 17 get_default_value 0 4 483 51 MovingPart< ACMatrixSwitchType >::get_default_value 0 1 205 0
80
inline LMatrix4 MovingPart< ACMatrixSwitchType >::get_default_value(void) const;

425 11 ~MovingPart 0 516 483 45 MovingPart< ACMatrixSwitchType >::~MovingPart 0 0 0
52
MovingPart< ACMatrixSwitchType >::~MovingPart(void);

426 14 get_class_type 0 4 482 32 MovingPartMatrix::get_class_type 0 1 202 0
57
static TypeHandle MovingPartMatrix::get_class_type(void);

427 14 get_class_type 0 4 485 48 MovingPart< ACScalarSwitchType >::get_class_type 0 1 207 0
73
static TypeHandle MovingPart< ACScalarSwitchType >::get_class_type(void);

428 9 get_value 0 4 485 43 MovingPart< ACScalarSwitchType >::get_value 0 1 208 0
75
inline PN_stdfloat MovingPart< ACScalarSwitchType >::get_value(void) const;

429 17 get_default_value 0 4 485 51 MovingPart< ACScalarSwitchType >::get_default_value 0 1 209 0
83
inline PN_stdfloat MovingPart< ACScalarSwitchType >::get_default_value(void) const;

430 11 ~MovingPart 0 516 485 45 MovingPart< ACScalarSwitchType >::~MovingPart 0 0 0
52
MovingPart< ACScalarSwitchType >::~MovingPart(void);

431 14 get_class_type 0 4 484 32 MovingPartScalar::get_class_type 0 1 206 0
57
static TypeHandle MovingPartScalar::get_class_type(void);

210
1 0 0 7 8 435 216 0 148 /**
 * Creates the AnimGroup, and adds it to the indicated parent.  The only way
 * to delete it subsequently is to delete the entire hierarchy.
 */ 2 6 parent 1 435  4 name 1 486  
2 0 0 15 8 435 216 0 0 1 6 param0 0 487  
3 0 0 6 10 438 0 0 58 /**
 * Returns the number of child nodes of the group.
 */ 1 4 this 3 487  
4 0 0 7 11 435 216 0 46 /**
 * Returns the nth child of the group.
 */ 2 4 this 3 487  1 n 1 438  
5 0 0 7 13 435 216 0 250 /**
 * Returns the first child found with the indicated name, or NULL if no such
 * child exists.  This method searches only the children of this particular
 * AnimGroup; it does not recursively search the entire graph.  See also
 * find_child().
 */ 2 4 this 3 487  4 name 1 486  
6 0 0 7 14 435 216 0 210 /**
 * Returns the first descendant found with the indicated name, or NULL if no
 * such descendant exists.  This method searches the entire graph beginning at
 * this AnimGroup; see also get_child_named().
 */ 2 4 this 3 487  4 name 1 486  
7 0 0 4 15 489 0 0 262 /**
 * Sorts the children nodes at each level of the hierarchy into alphabetical
 * order.  This should be done after creating the hierarchy, to guarantee that
 * the correct names will match up together when the AnimBundle is later bound
 * to a PlayerRoot.
 */ 1 4 this 3 435  
8 0 0 4 18 489 0 0 54 /**
 * Writes a one-line description of the group.
 */ 2 4 this 3 487  3 out 1 490  
9 0 0 4 19 489 0 0 78 /**
 * Writes a brief description of the group and all of its descendants.
 */ 3 4 this 3 487  3 out 1 490  12 indent_level 1 438  
10 0 0 7 20 493 0 0 0 0 
11 0 0 7 3 494 0 0 0 1 4 this 3 435  
12 0 0 6 6 495 0 0 0 1 4 this 3 435  
13 0 0 15 22 441 230 0 0 1 6 param0 0 496  
14 0 0 7 22 441 230 0 10 /**
 *
 */ 3 4 name 1 486  3 fps 1 462  10 num_frames 1 438  
15 0 0 7 23 441 230 0 236 /**
 * Returns a full copy of the bundle and its entire tree of nested AnimGroups.
 * However, the actual data stored in the leaves--that is, animation tables,
 * such as those stored in an AnimChannelMatrixXfmTable--will be shared.
 */ 1 4 this 3 496  
16 0 0 6 24 437 0 0 307 /**
 * Returns the ideal number of frames per second of the animation, when it is
 * running at normal speed.  This may not be the same as the actual playing
 * frame rate, as it might have been adjusted through set_play_rate() on the
 * AnimControl object.  See AnimControl::get_effective_frame_rate().
 */ 1 4 this 3 496  
17 0 0 6 25 438 0 0 109 /**
 * Returns the number of frames of animation, or 0 if the animation has no
 * fixed number of frames.
 */ 1 4 this 3 496  
18 0 0 7 30 493 0 0 0 0 
19 0 0 7 34 498 235 0 226 /**
 * The AnimBundle and its node should be constructed together.  Generally, the
 * derived classes of AnimBundleNode will automatically create a AnimBundle of
 * the appropriate type, and pass it up to this constructor.
 */ 2 4 name 1 486  6 bundle 1 441  
20 0 0 7 35 441 230 0 10 /**
 *
 */ 1 4 this 3 499  
21 0 0 7 38 441 230 0 205 /**
 * Recursively walks the scene graph beginning at the indicated node (which
 * need not be an AnimBundleNode), and returns the first AnimBundle found.
 * Returns NULL if no AnimBundle can be found.
 */ 1 4 root 1 456  
22 0 0 7 39 493 0 0 0 0 
23 0 0 7 47 444 241 0 148 /**
 * Creates the PartGroup, and adds it to the indicated parent.  The only way
 * to delete it subsequently is to delete the entire hierarchy.
 */ 2 6 parent 1 444  4 name 1 486  
24 0 0 6 49 473 0 0 156 /**
 * Returns true if this part is a CharacterJoint, false otherwise.  This is a
 * tiny optimization over is_of_type(CharacterType::get_class_type()).
 */ 1 4 this 3 501  
25 0 0 7 50 444 241 0 110 /**
 * Allocates and returns a new copy of the node.  Children are not copied, but
 * see copy_subgraph().
 */ 1 4 this 3 501  
26 0 0 7 51 444 241 0 84 /**
 * Allocates and returns a new copy of this node and of all of its children.
 */ 1 4 this 3 501  
27 0 0 6 52 438 0 0 58 /**
 * Returns the number of child nodes of the group.
 */ 1 4 this 3 501  
28 0 0 7 53 444 241 0 46 /**
 * Returns the nth child of the group.
 */ 2 4 this 3 501  1 n 1 438  
29 0 0 7 55 444 241 0 250 /**
 * Returns the first child found with the indicated name, or NULL if no such
 * child exists.  This method searches only the children of this particular
 * PartGroup; it does not recursively search the entire graph.  See also
 * find_child().
 */ 2 4 this 3 501  4 name 1 486  
30 0 0 7 56 444 241 0 210 /**
 * Returns the first descendant found with the indicated name, or NULL if no
 * such descendant exists.  This method searches the entire graph beginning at
 * this PartGroup; see also get_child_named().
 */ 2 4 this 3 501  4 name 1 486  
31 0 0 4 57 489 0 0 262 /**
 * Sorts the children nodes at each level of the hierarchy into alphabetical
 * order.  This should be done after creating the hierarchy, to guarantee that
 * the correct names will match up together when the AnimBundle is later bound
 * to a PlayerRoot.
 */ 1 4 this 3 444  
32 0 0 6 60 473 0 0 287 /**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */ 2 4 this 3 444  9 transform 1 503  
33 0 0 6 61 473 0 0 287 /**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */ 4 4 this 3 444  3 pos 1 506  3 hpr 1 506  5 scale 1 506  
34 0 0 6 62 473 0 0 287 /**
 * Freezes this particular joint so that it will always hold the specified
 * transform.  Returns true if this is a joint that can be so frozen, false
 * otherwise.
 *
 * This is normally only called internally by PartBundle::freeze_joint(), but
 * you may also call it directly.
 */ 2 4 this 3 444  5 value 1 462  
35 0 0 6 63 473 0 0 311 /**
 * Specifies a node to influence this particular joint so that it will always
 * hold the node's transform.  Returns true if this is a joint that can be so
 * controlled, false otherwise.
 *
 * This is normally only called internally by PartBundle::control_joint(), but
 * you may also call it directly.
 */ 2 4 this 3 444  4 node 1 456  
36 0 0 6 64 473 0 0 261 /**
 * Undoes the effect of a previous call to apply_freeze() or apply_control().
 * Returns true if the joint was modified, false otherwise.
 *
 * This is normally only called internally by PartBundle::release_joint(), but
 * you may also call it directly.
 */ 1 4 this 3 444  
37 0 0 7 65 510 281 0 181 /**
 * Returns the AnimChannelBase that has been forced to this joint by a
 * previous call to apply_freeze() or apply_control(), or NULL if no such
 * channel has been applied.
 */ 1 4 this 3 501  
38 0 0 4 66 489 0 0 78 /**
 * Writes a brief description of the group and all of its descendants.
 */ 3 4 this 3 501  3 out 1 490  12 indent_level 1 438  
39 0 0 4 67 489 0 0 117 /**
 * Writes a brief description of the group, showing its current value, and
 * that of all of its descendants.
 */ 3 4 this 3 501  3 out 1 490  12 indent_level 1 438  
40 0 0 7 68 493 0 0 0 0 
41 0 0 7 42 494 0 0 0 1 4 this 3 444  
42 0 0 6 44 495 0 0 0 1 4 this 3 444  
43 0 0 6 79 473 0 0 286 /**
 * Returns true if the AnimControl is being bound asynchronously, and has not
 * yet finished.  If this is true, the AnimControl's interface is still
 * available and will be perfectly useful (though get_anim() might return
 * NULL), but nothing visible will happen immediately.
 */ 1 4 this 3 511  
44 0 0 4 80 489 0 0 102 /**
 * Blocks the current thread until the AnimControl has finished loading and is
 * fully bound.
 */ 1 4 this 3 513  
45 0 0 6 81 473 0 0 154 /**
 * Returns true if the AnimControl was successfully loaded, or false if there
 * was a problem.  This may return false while is_pending() is true.
 */ 1 4 this 3 511  
46 0 0 4 82 489 0 0 205 /**
 * Specifies an event name that will be thrown when the AnimControl is
 * finished binding asynchronously.  If the AnimControl has already finished
 * binding, the event will be thrown immediately.
 */ 2 4 this 3 513  10 done_event 1 486  
47 0 0 6 83 486 0 0 113 /**
 * Returns the event name that will be thrown when the AnimControl is finished
 * binding asynchronously.
 */ 1 4 this 3 511  
48 0 0 7 84 477 416 0 65 /**
 * Returns the PartBundle bound in with this AnimControl.
 */ 1 4 this 3 511  
49 0 0 7 85 441 230 0 65 /**
 * Returns the AnimBundle bound in with this AnimControl.
 */ 1 4 this 3 511  
50 0 0 6 86 438 0 0 358 /**
 * Returns the particular channel index associated with this AnimControl.
 * This channel index is the slot on which each AnimGroup is bound to its
 * associated PartGroup, for each joint in the animation.
 *
 * It will be true that
 * get_part()->find_child("n")->get_bound(get_channel_index()) ==
 * get_anim()->find_child("n"), for each joint "n".
 */ 1 4 this 3 511  
51 0 0 6 87 514 0 0 369 /**
 * Returns the subset of joints controlled by this AnimControl.  Most of the
 * time, this will be BitArray::all_on(), for a normal full-body animation.
 * For a subset animation, however, this will be just a subset of those bits,
 * corresponding to the set of joints and sliders actually bound (as
 * enumerated by bind_hierarchy() in depth-first LIFO order).
 */ 1 4 this 3 511  
52 0 0 4 88 489 0 0 515 /**
 * Associates the indicated PandaNode with the AnimControl.  By convention,
 * this node represents the root node of the model file that corresponds to
 * this AnimControl's animation file, though nothing in this code makes this
 * assumption or indeed does anything with this node.
 *
 * The purpose of this is simply to allow the AnimControl to keep a reference
 * count on the ModelRoot node that generated it, so that the model will not
 * disappear from the model pool until it is no longer referenced.
 */ 2 4 this 3 513  5 model 1 456  
53 0 0 7 89 456 0 0 81 /**
 * Retrieves the pointer set via set_anim_model().  See set_anim_model().
 */ 1 4 this 3 511  
54 0 0 4 90 489 0 0 10 /**
 *
 */ 2 4 this 3 511  3 out 1 490  
55 0 0 7 91 493 0 0 0 0 
56 0 0 7 71 517 0 0 0 1 4 this 3 513  
57 0 0 6 74 518 0 0 0 1 4 this 3 513  
58 0 0 6 76 495 0 0 0 1 4 this 3 513  
59 0 0 7 93 493 0 0 0 1 4 this 3 519  
60 0 0 7 94 493 0 0 0 0 
61 0 0 4 99 489 0 0 0 3 4 this 3 521  5 frame 1 438  5 value 1 522  
62 0 0 4 100 489 0 0 72 // These transform-component methods only have meaning for matrix types. 3 4 this 3 521  5 frame 1 438  5 value 1 522  
63 0 0 4 101 489 0 0 0 3 4 this 3 521  5 frame 1 438  5 scale 1 523  
64 0 0 4 102 489 0 0 0 3 4 this 3 521  5 frame 1 438  3 hpr 1 523  
65 0 0 4 103 489 0 0 0 3 4 this 3 521  5 frame 1 438  4 quat 1 524  
66 0 0 4 104 489 0 0 0 3 4 this 3 521  5 frame 1 438  3 pos 1 523  
67 0 0 4 105 489 0 0 0 3 4 this 3 521  5 frame 1 438  5 shear 1 523  
68 0 0 7 106 493 0 0 0 1 4 this 3 527  
69 0 0 7 107 493 0 0 0 0 
70 0 0 4 112 489 0 0 0 3 4 this 3 529  5 frame 1 438  5 scale 1 523  
71 0 0 4 113 489 0 0 0 3 4 this 3 529  5 frame 1 438  3 hpr 1 523  
72 0 0 4 114 489 0 0 0 3 4 this 3 529  5 frame 1 438  4 quat 1 524  
73 0 0 4 115 489 0 0 0 3 4 this 3 529  5 frame 1 438  3 pos 1 523  
74 0 0 4 116 489 0 0 0 3 4 this 3 529  5 frame 1 438  5 shear 1 523  
75 0 0 7 117 493 0 0 0 1 4 this 3 530  
76 0 0 7 118 493 0 0 0 0 
77 0 0 4 121 489 0 0 44 /**
 * Explicitly sets the matrix value.
 */ 2 4 this 3 532  5 value 1 533  
78 0 0 4 121 489 0 0 107 /**
 * Explicitly sets the matrix value, using the indicated TransformState object
 * as a convenience.
 */ 2 4 this 3 532  5 value 1 503  
79 0 0 4 122 489 0 0 124 /**
 * Specifies a node whose transform will be queried each frame to implicitly
 * specify the transform of this joint.
 */ 2 4 this 3 532  4 node 1 456  
80 0 0 7 123 503 0 0 93 /**
 * Returns the explicit TransformState value that was set via set_value(), if
 * any.
 */ 1 4 this 3 534  
81 0 0 7 124 456 0 0 70 /**
 * Returns the node that was set via set_value_node(), if any.
 */ 1 4 this 3 534  
82 0 0 7 127 493 0 0 0 0 
83 0 0 7 130 536 281 0 10 /**
 *
 */ 2 6 parent 1 435  4 name 1 486  
84 0 0 6 131 473 0 0 80 /**
 * Returns true if the given letter is one of the nine valid table id's.
 */ 1 8 table_id 1 537  
85 0 0 4 132 489 0 0 255 /**
 * Assigns the indicated table.  table_id is one of 'i', 'j', 'k', for scale,
 * 'a', 'b', 'c' for shear, 'h', 'p', 'r', for rotation, and 'x', 'y', 'z',
 * for translation.  The new table must have either zero, one, or
 * get_num_frames() frames.
 */ 3 4 this 3 536  8 table_id 1 537  5 table 1 538  
86 0 0 7 133 542 0 0 103 /**
 * Returns a pointer to the indicated subtable's data, if it exists, or NULL
 * if it does not.
 */ 2 4 this 3 540  8 table_id 1 537  
87 0 0 4 134 489 0 0 90 /**
 * Removes all the tables from the channel, and resets it to its initial
 * state.
 */ 1 4 this 3 536  
88 0 0 6 135 473 0 0 68 /**
 * Returns true if the indicated subtable has been assigned.
 */ 2 4 this 3 540  8 table_id 1 537  
89 0 0 4 136 489 0 0 59 /**
 * Removes the indicated table from the definition.
 */ 2 4 this 3 536  8 table_id 1 537  
90 0 0 7 141 493 0 0 0 0 
91 0 0 4 143 489 0 0 98 /**
 * Explicitly sets the value.  This will remove any node assigned via
 * set_value_node().
 */ 2 4 this 3 543  5 value 1 462  
92 0 0 4 144 489 0 0 178 /**
 * Specifies a node whose transform will be queried each frame to implicitly
 * specify the transform of this joint.  This will override the values set by
 * set_value().
 */ 2 4 this 3 543  4 node 1 456  
93 0 0 7 151 493 0 0 0 0 
94 0 0 23 154 544 326 0 10 /**
 *
 */ 2 6 parent 1 435  4 name 1 486  
95 0 0 4 155 489 0 0 34 /**
 * Assigns the data table.
 */ 2 4 this 3 544  5 table 1 538  
96 0 0 7 156 542 0 0 90 /**
 * Returns a pointer to the table's data, if it exists, or NULL if it does
 * not.
 */ 1 4 this 3 545  
97 0 0 6 157 473 0 0 60 /**
 * Returns true if the data table has been assigned.
 */ 1 4 this 3 545  
98 0 0 4 158 489 0 0 34 /**
 * Empties the data table.
 */ 1 4 this 3 544  
99 0 0 7 160 493 0 0 0 0 
100 0 0 7 163 547 328 0 117 /**
 * Returns the AnimControl associated with the given name, or NULL if no such
 * control has been associated.
 */ 0 
101 0 0 15 163 547 328 0 0 1 6 param0 0 548  
102 0 0 4 165 489 0 0 247 /**
 * Associates the given AnimControl with this collection under the given name.
 * The AnimControl will remain associated until a new AnimControl is
 * associated with the same name later, or until unbind_anim() is called with
 * this name.
 */ 3 4 this 3 547  7 control 1 513  4 name 1 486  
103 0 0 7 166 513 265 0 117 /**
 * Returns the AnimControl associated with the given name, or NULL if no such
 * control has been associated.
 */ 2 4 this 3 548  4 name 1 486  
104 0 0 6 167 473 0 0 184 /**
 * Removes the AnimControl associated with the given name, if any.  Returns
 * true if an AnimControl was removed, false if there was no AnimControl with
 * the indicated name.
 */ 2 4 this 3 547  4 name 1 486  
105 0 0 6 168 438 0 0 78 /**
 * Returns the number of AnimControls associated with this collection.
 */ 1 4 this 3 548  
106 0 0 7 169 513 265 0 71 /**
 * Returns the nth AnimControl associated with this collection.
 */ 2 4 this 3 548  1 n 1 438  
107 0 0 6 170 486 0 0 83 /**
 * Returns the name of the nth AnimControl associated with this collection.
 */ 2 4 this 3 548  1 n 1 438  
108 0 0 4 173 489 0 0 56 /**
 * Disassociates all anims from this collection.
 */ 1 4 this 3 547  
109 0 0 6 174 473 0 0 46 /**
 * Starts the named animation playing.
 */ 2 4 this 3 547  9 anim_name 1 486  
110 0 0 6 174 473 0 0 46 /**
 * Starts the named animation playing.
 */ 4 4 this 3 547  9 anim_name 1 486  4 from 1 437  2 to 1 437  
111 0 0 6 175 473 0 0 46 /**
 * Starts the named animation looping.
 */ 3 4 this 3 547  9 anim_name 1 486  7 restart 1 473  
112 0 0 6 175 473 0 0 46 /**
 * Starts the named animation looping.
 */ 5 4 this 3 547  9 anim_name 1 486  7 restart 1 473  4 from 1 437  2 to 1 437  
113 0 0 6 176 473 0 0 37 /**
 * Stops the named animation.
 */ 2 4 this 3 547  9 anim_name 1 486  
114 0 0 6 177 473 0 0 61 /**
 * Sets to a particular frame in the named animation.
 */ 3 4 this 3 547  9 anim_name 1 486  5 frame 1 437  
115 0 0 4 178 489 0 0 48 // These functions operate on all anims at once. 1 4 this 3 547  
116 0 0 4 178 489 0 0 41 /**
 * Starts all animations playing.
 */ 3 4 this 3 547  4 from 1 437  2 to 1 437  
117 0 0 4 179 489 0 0 41 /**
 * Starts all animations looping.
 */ 2 4 this 3 547  7 restart 1 473  
118 0 0 4 179 489 0 0 41 /**
 * Starts all animations looping.
 */ 4 4 this 3 547  7 restart 1 473  4 from 1 437  2 to 1 437  
119 0 0 6 180 473 0 0 127 /**
 * Stops all currently playing animations.  Returns true if any animations
 * were stopped, false if none were playing.
 */ 1 4 this 3 547  
120 0 0 4 181 489 0 0 54 /**
 * Sets all animations to the indicated frame.
 */ 2 4 this 3 547  5 frame 1 437  
121 0 0 6 182 438 0 0 67 /**
 * Returns the current frame in the last-started animation.
 */ 1 4 this 3 548  
122 0 0 6 182 438 0 0 99 /**
 * Returns the current frame in the named animation, or 0 if the animation is
 * not found.
 */ 2 4 this 3 548  9 anim_name 1 486  
123 0 0 6 183 438 0 0 76 /**
 * Returns the total number of frames in the last-started animation.
 */ 1 4 this 3 548  
124 0 0 6 183 438 0 0 108 /**
 * Returns the total number of frames in the named animation, or 0 if the
 * animation is not found.
 */ 2 4 this 3 548  9 anim_name 1 486  
125 0 0 6 184 473 0 0 95 /**
 * Returns true if the last-started animation is currently playing, false
 * otherwise.
 */ 1 4 this 3 548  
126 0 0 6 184 473 0 0 85 /**
 * Returns true if the named animation is currently playing, false otherwise.
 */ 2 4 this 3 548  9 anim_name 1 486  
127 0 0 6 185 486 0 0 183 /**
 * Returns the name of the bound AnimControl currently playing, if any.  If
 * more than one AnimControl is currently playing, returns all of the names
 * separated by spaces.
 */ 1 4 this 3 548  
128 0 0 4 186 489 0 0 10 /**
 *
 */ 2 4 this 3 548  3 out 1 490  
129 0 0 4 187 489 0 0 10 /**
 *
 */ 2 4 this 3 548  3 out 1 490  
130 0 0 7 190 550 0 0 10 /**
 *
 */ 0 
131 0 0 6 191 438 0 0 64 /**
 * Returns the number of animation records in the table.
 */ 1 4 this 3 551  
132 0 0 6 192 438 0 0 321 /**
 * Returns the index number in the table of the animation record with the
 * indicated name, or -1 if the name is not present.  By convention, the
 * basename is the filename of the egg or bam file, without the directory part
 * and without the extension.  That is, it is
 * Filename::get_basename_wo_extension().
 */ 2 4 this 3 551  8 basename 1 486  
133 0 0 6 193 486 0 0 86 /**
 * Returns the basename stored for the nth animation record.  See find_anim().
 */ 2 4 this 3 551  1 n 1 438  
134 0 0 6 194 462 0 0 70 /**
 * Returns the frame rate stored for the nth animation record.
 */ 2 4 this 3 551  1 n 1 438  
135 0 0 6 195 438 0 0 76 /**
 * Returns the number of frames stored for the nth animation record.
 */ 2 4 this 3 551  1 n 1 438  
136 0 0 4 196 489 0 0 56 /**
 * Removes all animation records from the table.
 */ 1 4 this 3 550  
137 0 0 4 197 489 0 0 113 /**
 * Removes the nth animation records from the table.  This renumbers indexes
 * for following animations.
 */ 2 4 this 3 550  1 n 1 438  
138 0 0 4 198 489 0 0 227 /**
 * Adds a new animation record to the table.  If there is already a record of
 * this name, no operation is performed (the original record is unchanged).
 * See find_anim().  This will invalidate existing index numbers.
 */ 4 4 this 3 550  8 basename 1 486  15 base_frame_rate 1 462  10 num_frames 1 438  
139 0 0 4 199 489 0 0 160 /**
 * Copies the animation records from the other table into this one.  If a
 * given record name exists in both tables, the record in this one supercedes.
 */ 2 4 this 3 550  5 other 1 551  
140 0 0 4 200 489 0 0 10 /**
 *
 */ 2 4 this 3 551  3 out 1 490  
141 0 0 4 201 489 0 0 10 /**
 *
 */ 3 4 this 3 551  3 out 1 490  12 indent_level 1 438  
142 0 0 7 202 493 0 0 0 0 
143 0 0 7 205 553 373 0 10 /**
 *
 */ 0 
144 0 0 15 205 553 373 0 10 /**
 *
 */ 1 4 copy 1 554  
145 0 0 6 206 553 0 0 0 2 4 this 3 553  4 copy 1 554  
146 0 0 4 207 489 0 0 342 /**
 * Adds the named joint to the list of joints that will be explicitly included
 * in the subset.  Any joint at or below a named node will be included in the
 * subset (unless a lower node is also listed in the exclude list).
 *
 * Since the name is a GlobPattern, it may of course include filename globbing
 * characters like * and ?.
 */ 2 4 this 3 553  4 name 1 556  
147 0 0 4 208 489 0 0 352 /**
 * Adds the named joint to the list of joints that will be explicitly
 * exlcluded from the subset.  Any joint at or below a named node will not be
 * included in the subset (unless a lower node is also listed in the include
 * list).
 *
 * Since the name is a GlobPattern, it may of course include filename globbing
 * characters like * and ?.
 */ 2 4 this 3 553  4 name 1 556  
148 0 0 4 209 489 0 0 98 /**
 * Appends the include and exclude list from the other object onto this
 * object's lists.
 */ 2 4 this 3 553  5 other 1 554  
149 0 0 4 210 489 0 0 10 /**
 *
 */ 2 4 this 3 554  3 out 1 490  
150 0 0 6 211 473 0 0 147 /**
 * Returns true if the include list is completely empty, false otherwise.  If
 * it is empty, it is the same thing as including all joints.
 */ 1 4 this 3 554  
151 0 0 6 212 473 0 0 101 /**
 * Returns true if the indicated name matches a name on the include list,
 * false otherwise.
 */ 2 4 this 3 554  10 joint_name 1 486  
152 0 0 6 213 473 0 0 101 /**
 * Returns true if the indicated name matches a name on the exclude list,
 * false otherwise.
 */ 2 4 this 3 554  10 joint_name 1 486  
153 0 0 15 217 561 376 0 0 1 6 param0 0 559  
154 0 0 7 217 561 376 0 10 /**
 *
 */ 7 4 name 1 486  8 filename 1 562  7 options 1 565  6 loader 1 568  7 control 1 513  21 hierarchy_match_flags 1 438  6 subset 1 554  
155 0 0 7 218 493 0 0 0 0 
156 0 0 7 221 477 416 0 133 /**
 * Normally, a PartBundle constructor should not be called directly--it will
 * get created when a PartBundleNode is created.
 */ 1 4 name 5 486  
157 0 0 7 222 551 0 0 181 /**
 * Returns the AnimPreloadTable associated with the PartBundle.  This table,
 * if present, can be used for the benefit of load_bind_anim() to allow
 * asynchronous binding.
 */ 1 4 this 3 570  
158 0 0 7 223 550 0 0 106 /**
 * Returns a modifiable pointer to the AnimPreloadTable associated with the
 * PartBundle, if any.
 */ 1 4 this 3 477  
159 0 0 4 224 489 0 0 72 /**
 * Replaces the AnimPreloadTable associated with the PartBundle.
 */ 2 4 this 3 477  5 table 1 550  
160 0 0 4 225 489 0 0 71 /**
 * Removes any AnimPreloadTable associated with the PartBundle.
 */ 1 4 this 3 477  
161 0 0 4 226 489 0 0 85 /**
 * Copies the contents of the other PartBundle's preload table into this one.
 */ 2 4 this 3 477  5 other 1 570  
162 0 0 4 228 489 0 0 270 /**
 * Defines the algorithm that is used when blending multiple frames or
 * multiple animations together, when either anim_blend_flag or
 * frame_blend_flag is set to true.
 *
 * See partBundle.h for a description of the meaning of each of the BlendType
 * values.
 */ 2 4 this 3 477  2 bt 1 472  
163 0 0 6 229 472 0 0 178 /**
 * Returns the algorithm that is used when blending multiple frames or
 * multiple animations together, when either anim_blend_flag or
 * frame_blend_flag is set to true.
 */ 1 4 this 3 570  
164 0 0 4 230 489 0 0 581 /**
 * Defines the way the character responds to multiple calls to
 * set_control_effect()).  By default, this flag is set false, which disallows
 * multiple animations.  When this flag is false, it is not necessary to
 * explicitly set the control_effect when starting an animation; starting the
 * animation will implicitly remove the control_effect from the previous
 * animation and set it on the current one.
 *
 * However, if this flag is set true, the control_effect must be explicitly
 * set via set_control_effect() whenever an animation is to affect the
 * character.
 */ 2 4 this 3 477  15 anim_blend_flag 1 473  
165 0 0 6 231 473 0 0 136 /**
 * Returns whether the character allows multiple different animations to be
 * bound simultaneously.  See set_anim_blend_flag().
 */ 1 4 this 3 570  
166 0 0 4 232 489 0 0 775 /**
 * Specifies whether the character interpolates (blends) between two
 * sequential frames of an active animation, showing a smooth intra-frame
 * motion, or whether it holds each frame until the next frame is ready,
 * showing precisely the specified animation.
 *
 * When this value is false, the character holds each frame until the next is
 * ready.  When this is true, the character will interpolate between two
 * consecutive frames of animation for each frame the animation is onscreen,
 * according to the amount of time elapsed between the frames.
 *
 * The default value of this flag is determined by the interpolate-frames
 * Config.prc variable.
 *
 * Use set_blend_type() to change the algorithm that the character uses to
 * interpolate matrix positions.
 */ 2 4 this 3 477  16 frame_blend_flag 1 473  
167 0 0 6 233 473 0 0 205 /**
 * Returns whether the character interpolates (blends) between two sequential
 * animation frames, or whether it holds the current frame until the next one
 * is ready.  See set_frame_blend_flag().
 */ 1 4 this 3 570  
168 0 0 4 234 489 0 0 111 /**
 * Specifies the transform matrix which is implicitly applied at the root of
 * the animated hierarchy.
 */ 2 4 this 3 477  10 root_xform 1 533  
169 0 0 4 235 489 0 0 81 /**
 * Applies the indicated transform to the root of the animated hierarchy.
 */ 2 4 this 3 477  3 mat 1 533  
170 0 0 6 236 533 0 0 109 /**
 * Returns the transform matrix which is implicitly applied at the root of the
 * animated hierarchy.
 */ 1 4 this 3 570  
171 0 0 7 237 477 416 0 226 /**
 * Returns a PartBundle that is a duplicate of this one, but with the
 * indicated transform applied.  If this is called multiple times with the
 * same TransformState pointer, it returns the same PartBundle each time.
 */ 2 4 this 3 477  9 transform 1 503  
172 0 0 6 238 438 0 0 94 /**
 * Returns the number of PartBundleNodes that contain a pointer to this
 * PartBundle.
 */ 1 4 this 3 570  
173 0 0 7 239 475 0 0 74 /**
 * Returns the nth PartBundleNode associated with this PartBundle.
 */ 2 4 this 3 570  1 n 1 438  
174 0 0 4 267 489 0 0 518 /**
 * Sets the control effect of all AnimControls to zero (but does not "stop"
 * the AnimControls).  The character will no longer be affected by any
 * animation, and will return to its default pose (unless restore-initial-pose
 * is false).
 *
 * The AnimControls which are no longer associated will not be using any CPU
 * cycles, but they may still be in the "playing" state; if they are later
 * reassociated with the PartBundle they will resume at their current frame as
 * if they'd been running all along.
 */ 1 4 this 3 477  
175 0 0 4 268 489 0 0 530 /**
 * Sets the amount by which the character is affected by the indicated
 * AnimControl (and its associated animation).  Normally, this will only be
 * zero or one.  Zero indicates the animation does not affect the character,
 * and one means it does.
 *
 * If the _anim_blend_flag is not false (see set_anim_blend_flag()), it is
 * possible to have multiple AnimControls in effect simultaneously.  In this
 * case, the effect is a weight that indicates the relative importance of each
 * AnimControl to the final animation.
 */ 3 4 this 3 477  7 control 1 513  6 effect 1 462  
176 0 0 6 269 462 0 0 153 /**
 * Returns the amount by which the character is affected by the indicated
 * AnimControl and its associated animation.  See set_control_effect().
 */ 2 4 this 3 570  7 control 1 513  
177 0 0 4 270 489 0 0 55 /**
 * Writes a one-line description of the bundle.
 */ 2 4 this 3 570  3 out 1 490  
178 0 0 7 271 513 265 0 790 /**
 * Binds the animation to the bundle, if possible, and returns a new
 * AnimControl that can be used to start and stop the animation.  If the anim
 * hierarchy does not match the part hierarchy, returns NULL.
 *
 * If hierarchy_match_flags is 0, only an exact match is accepted; otherwise,
 * it may contain a union of PartGroup::HierarchyMatchFlags values indicating
 * conditions that will be tolerated (but warnings will still be issued).
 *
 * If subset is specified, it restricts the binding only to the named subtree
 * of joints.
 *
 * The AnimControl is not stored within the PartBundle; it is the user's
 * responsibility to maintain the pointer.  The animation will automatically
 * unbind itself when the AnimControl destructs (i.e.  its reference count
 * goes to zero).
 */ 4 4 this 3 477  4 anim 1 441  21 hierarchy_match_flags 5 438  6 subset 5 554  
179 0 0 7 272 513 265 0 1088 /**
 * Binds an animation to the bundle.  The animation is loaded from the disk
 * via the indicated Loader object.  In other respects, this behaves similarly
 * to bind_anim(), with the addition of asynchronous support.
 *
 * If allow_aysnc is true, the load will be asynchronous if possible.  This
 * requires that the animation basename can be found in the PartBundle's
 * preload table (see get_anim_preload()).
 *
 * In an asynchronous load, the animation file will be loaded and bound in a
 * sub-thread.  This means that the animation will not necessarily be
 * available at the time this method returns.  You may still use the returned
 * AnimControl immediately, though, but no visible effect will occur until the
 * animation eventually becomes available.
 *
 * You can test AnimControl::is_pending() to see if the animation has been
 * loaded yet, or wait for it to finish with AnimControl::wait_pending() or
 * even PartBundle::wait_pending().  You can also set an event to be triggered
 * when the animation finishes loading with
 * AnimControl::set_pending_done_event().
 */ 6 4 this 3 477  6 loader 1 568  8 filename 1 562  21 hierarchy_match_flags 1 438  6 subset 1 554  11 allow_async 1 473  
180 0 0 4 273 489 0 0 153 /**
 * Blocks the current thread until all currently-pending AnimControls, with a
 * nonzero control effect, have been loaded and are properly bound.
 */ 1 4 this 3 477  
181 0 0 6 274 473 0 0 373 /**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */ 5 4 this 3 477  10 joint_name 1 486  3 pos 1 506  3 hpr 1 506  5 scale 1 506  
182 0 0 6 274 473 0 0 373 /**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */ 3 4 this 3 477  10 joint_name 1 486  5 value 1 462  
183 0 0 6 274 473 0 0 373 /**
 * Specifies that the joint with the indicated name should be frozen with the
 * specified transform.  It will henceforth always hold this fixed transform,
 * regardless of any animations that may subsequently be bound to the joint.
 *
 * Returns true if the joint is successfully frozen, or false if the named
 * child is not a joint (or slider) or does not exist.
 */ 3 4 this 3 477  10 joint_name 1 486  9 transform 1 503  
184 0 0 6 275 473 0 0 396 /**
 * Specifies that the joint with the indicated name should be animated with
 * the transform on the indicated node.  It will henceforth always follow the
 * node's transform, regardless of any animations that may subsequently be
 * bound to the joint.
 *
 * Returns true if the joint is successfully controlled, or false if the named
 * child is not a joint (or slider) or does not exist.
 */ 3 4 this 3 477  10 joint_name 1 486  4 node 1 456  
185 0 0 6 276 473 0 0 342 /**
 * Releases the named joint from the effects of a previous call to
 * freeze_joint() or control_joint(). It will henceforth once again follow
 * whatever transforms are dictated by the animation.
 *
 * Returns true if the joint is released, or false if the named child was not
 * previously controlled or frozen, or it does not exist.
 */ 2 4 this 3 477  10 joint_name 1 486  
186 0 0 6 277 473 0 0 216 /**
 * Updates all the parts in the bundle to reflect the data for the current
 * frame (as set in each of the AnimControls).
 *
 * Returns true if any part has changed as a result of this, or false
 * otherwise.
 */ 1 4 this 3 477  
187 0 0 6 278 473 0 0 131 /**
 * Updates all the parts in the bundle to reflect the data for the current
 * frame, whether we believe it needs it or not.
 */ 1 4 this 3 477  
188 0 0 7 279 493 0 0 0 0 
189 0 0 7 250 475 0 0 226 /**
 * The PartBundle and its node should be constructed together.  Generally, the
 * derived classes of PartBundleNode will automatically create a PartBundle of
 * the appropriate type, and pass it up to this constructor.
 */ 2 4 name 1 486  6 bundle 1 477  
190 0 0 6 251 438 0 0 10 /**
 *
 */ 1 4 this 3 572  
191 0 0 7 252 477 416 0 10 /**
 *
 */ 2 4 this 3 572  1 n 1 438  
192 0 0 7 254 478 0 0 204 /**
 * Returns the PartBundleHandle that wraps around the actual nth PartBundle.
 * While the PartBundle pointer might later change due to a future flatten
 * operation, the PartBundleHandle will not.
 */ 2 4 this 3 572  1 n 1 438  
193 0 0 7 266 493 0 0 0 0 
194 0 0 23 262 478 0 0 10 /**
 *
 */ 1 6 bundle 1 477  
195 0 0 15 262 478 0 0 0 1 6 param0 0 574  
196 0 0 7 263 477 416 0 68 /**
 * Returns the actual PartBundle embedded within the handle.
 */ 1 4 this 3 478  
197 0 0 4 264 489 0 0 68 /**
 * Changes the actual PartBundle embedded within the handle.
 */ 2 4 this 3 478  6 bundle 1 477  
198 0 0 6 282 438 0 0 310 /**
 * Returns the number of channels that might be bound to this PartGroup.  This
 * might not be the actual number of channels, since there might be holes in
 * the list; it is one more than the index number of the highest bound
 * channel.  Thus, it is called get_max_bound() instead of get_num_bound().
 */ 1 4 this 3 576  
199 0 0 7 283 510 281 0 371 /**
 * Returns the nth bound channel on this PartGroup.  n can be determined by
 * iterating from 0 to one less than get_max_bound(); or n might be
 * AnimControl::get_channel_index().
 *
 * This will return NULL if there is no channel bound on the indicated index.
 * It is an error to call this if n is less than zero or greater than or equal
 * to get_max_bound().
 */ 2 4 this 3 576  1 n 1 438  
200 0 0 4 284 489 0 0 0 2 4 this 3 576  3 out 1 490  
201 0 0 7 285 493 0 0 0 0 
202 0 0 7 293 493 0 0 0 0 
203 0 0 7 289 493 0 0 0 0 
204 0 0 7 290 522 0 0 0 1 4 this 3 578  
205 0 0 7 291 522 0 0 0 1 4 this 3 578  
206 0 0 7 300 493 0 0 0 0 
207 0 0 7 296 493 0 0 0 0 
208 0 0 6 297 462 0 0 0 1 4 this 3 580  
209 0 0 6 298 462 0 0 0 1 4 this 3 580  
210 0 0 4 203 489 0 0 0 3 9 root_node 1 456  8 controls 1 547  21 hierarchy_match_flags 5 438  
150
432 9 AnimGroup 0 26625 9 AnimGroup 9 AnimGroup 0 0 0 1 215 216 1 582 8 217 218 219 220 221 222 223 224 1 600 0 2 3 433 211 212 3 434 213 214 0 0 165
/**
 * This is the base class for AnimChannel and AnimBundle.  It implements a
 * hierarchy of AnimChannels.  The root of the hierarchy must be an
 * AnimBundle.
 */

433 27 TypedWritableReferenceCount 0 2048 27 TypedWritableReferenceCount 27 TypedWritableReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 414
/**
 * A base class for things which need to inherit from both TypedWritable and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedWritables and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

434 7 Namable 0 2048 7 Namable 7 Namable 0 0 0 0 0 0 0 0 0 0 0 0 125
/**
 * A base class for all things which can have a name.  The name is either
 * empty or nonempty, but it is never NULL.
 */

435 11 AnimGroup * 0 8576 11 AnimGroup * 11 AnimGroup * 0 0 432 0 0 0 0 0 0 0 0 0 0

436 10 AnimBundle 0 141313 10 AnimBundle 10 AnimBundle 0 0 0 1 225 230 2 583 584 4 226 227 228 229 0 0 1 0 432 0 0 0 0 169
/**
 * This is the root of an AnimChannel hierarchy.  It knows the frame rate and
 * number of frames of all the channels in the hierarchy (which must all
 * match).
 */

437 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

438 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

439 14 AnimBundleNode 0 141313 14 AnimBundleNode 14 AnimBundleNode 0 0 0 1 231 235 1 585 3 232 233 234 0 0 1 0 440 0 0 0 0 169
/**
 * This is a node that contains a pointer to an AnimBundle.  Like
 * PartBundleNode, it exists solely to make it easy to store AnimBundles in
 * the scene graph.
 */

440 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

441 12 AnimBundle * 0 8576 12 AnimBundle * 12 AnimBundle * 0 0 436 0 0 0 0 0 0 0 0 0 0

442 9 PartGroup 0 26625 9 PartGroup 9 PartGroup 0 0 0 1 240 241 1 586 17 242 243 244 245 246 247 248 249 250 251 252 253 254 255 256 257 258 1 601 0 2 3 433 236 237 3 434 238 239 0 1 443 105
/**
 * This is the base class for PartRoot and MovingPart.  It defines a hierarchy
 * of MovingParts.
 */

443 19 HierarchyMatchFlags 0 794624 30 PartGroup::HierarchyMatchFlags 30 PartGroup::HierarchyMatchFlags 442 0 0 0 0 0 0 0 0 0 3 17 HMF_ok_part_extra 28 PartGroup::HMF_ok_part_extra 0
1 17 HMF_ok_anim_extra 28 PartGroup::HMF_ok_anim_extra 0
2 22 HMF_ok_wrong_root_name 33 PartGroup::HMF_ok_wrong_root_name 0
4 0 214
// This enum defines bits which may be passed into check_hierarchy() and
// PartBundle::bind_anim() to allow an inexact match of channel hierarchies.
// This specifies conditions that we don't care about enforcing.

444 11 PartGroup * 0 8576 11 PartGroup * 11 PartGroup * 0 0 442 0 0 0 0 0 0 0 0 0 0

445 11 AnimControl 0 26625 11 AnimControl 11 AnimControl 0 0 0 0 265 0 13 266 267 268 269 270 271 272 273 274 275 276 277 278 0 0 3 3 446 259 260 3 447 261 262 3 434 263 264 0 0 255
/**
 * Controls the timing of a character animation.  An AnimControl object is
 * created for each character/bundle binding and manages the state of the
 * animation: whether started, stopped, or looping, and the current frame
 * number and play rate.
 */

446 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

447 13 AnimInterface 0 2048 13 AnimInterface 13 AnimInterface 0 0 0 0 0 0 0 0 0 0 0 0 226
/**
 * This is the fundamental interface for things that have a play/loop/stop
 * type interface for frame-based animation, such as animated characters.
 * This is the base class for AnimControl and other, similar classes.
 */

448 15 AnimChannelBase 0 141313 15 AnimChannelBase 15 AnimChannelBase 0 0 0 0 281 0 2 279 280 0 0 1 0 432 0 0 0 0 253
/**
 * Parent class for all animation channels.  An AnimChannel is an arbitrary
 * function that changes over time (actually, over frames), usually defined by
 * a table read from an egg file (but possibly computed or generated in any
 * other way).
 */

449 33 AnimChannel< ACMatrixSwitchType > 0 75777 33 AnimChannel< ACMatrixSwitchType > 33 AnimChannel< ACMatrixSwitchType > 0 0 0 0 281 0 9 282 283 284 285 286 287 288 289 290 0 0 1 0 448 0 0 0 1 450 0

450 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 451 0 0 0 0 0 0 0 0 0 0

451 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

452 17 AnimChannelMatrix 0 2105345 17 AnimChannelMatrix 17 AnimChannelMatrix 0 0 449 0 0 0 0 0 0 0 0 0 0

453 33 AnimChannel< ACScalarSwitchType > 0 75777 33 AnimChannel< ACScalarSwitchType > 33 AnimChannel< ACScalarSwitchType > 0 0 0 0 281 0 9 291 292 293 294 295 296 297 298 299 0 0 1 0 448 0 0 0 0 0

454 17 AnimChannelScalar 0 2105345 17 AnimChannelScalar 17 AnimChannelScalar 0 0 453 0 0 0 0 0 0 0 0 0 0

455 24 AnimChannelMatrixDynamic 0 141313 24 AnimChannelMatrixDynamic 24 AnimChannelMatrixDynamic 0 0 0 0 305 1 587 5 300 301 302 303 304 0 0 1 0 449 0 0 0 0 393
/**
 * An animation channel that accepts a matrix each frame from some dynamic
 * input provided by code.
 *
 * This object operates in two modes: in explicit mode, the programmer should
 * call set_value() each frame to indicate the new value; in implicit mode,
 * the programmer should call set_value_node() to indicate the node whose
 * transform will be copied to the joint each frame.
 */

456 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 440 0 0 0 0 0 0 0 0 0 0

457 25 AnimChannelMatrixXfmTable 0 75777 25 AnimChannelMatrixXfmTable 25 AnimChannelMatrixXfmTable 0 0 0 1 306 281 1 588 7 307 308 309 310 311 312 313 0 0 1 0 449 0 0 0 0 263
/**
 * An animation channel that issues a matrix each frame, read from a table
 * such as might have been read from an egg file.  The table actually consists
 * of nine sub-tables, each representing one component of the transform:
 * scale, rotate, translate.
 */

458 13 CPTA_stdfloat 0 2105344 13 CPTA_stdfloat 13 CPTA_stdfloat 0 0 459 0 0 0 0 0 0 0 0 0 0

459 10 CPTA_float 0 2105344 10 CPTA_float 10 CPTA_float 0 0 460 0 0 0 0 0 0 0 0 0 0

460 28 ConstPointerToArray< float > 0 2048 28 ConstPointerToArray< float > 28 ConstPointerToArray< float > 0 0 0 0 0 0 0 0 0 0 0 0 0

461 24 AnimChannelScalarDynamic 0 141313 24 AnimChannelScalarDynamic 24 AnimChannelScalarDynamic 0 0 0 0 319 2 589 590 3 314 315 318 0 0 1 0 453 0 0 0 0 396
/**
 * An animation channel that accepts a scalar each frame from some dynamic
 * input provided by code.
 *
 * This object operates in two modes: in explicit mode, the programmer should
 * call set_value() each frame to indicate the new value; in implicit mode,
 * the programmer should call set_value_node() to indicate the node whose X
 * component will be copied to the scalar each frame.
 */

462 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 463 0 0 0 0 0 0 0 0 0 0

463 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

464 22 AnimChannelScalarTable 0 141313 22 AnimChannelScalarTable 22 AnimChannelScalarTable 0 0 0 1 320 326 1 591 5 321 322 323 324 325 0 0 1 0 453 0 0 0 0 132
/**
 * An animation channel that issues a scalar each frame, read from a table
 * such as might have been read from an egg file.
 */

465 21 AnimControlCollection 0 26625 21 AnimControlCollection 21 AnimControlCollection 0 0 0 1 327 328 0 21 329 330 331 332 333 334 335 336 337 338 339 340 341 342 343 344 345 346 347 348 349 2 602 603 0 0 0 0 356
/**
 * This is a named collection of AnimControl pointers.  An AnimControl may be
 * added to the collection by name.  While an AnimControl is associated, its
 * reference count is maintained; associating a new AnimControl with the same
 * name will decrement the previous control's reference count (and possibly
 * delete it, unbinding its animation).
 */

466 16 AnimPreloadTable 0 75777 16 AnimPreloadTable 16 AnimPreloadTable 0 0 0 1 350 0 0 12 351 352 353 354 355 356 357 358 359 360 361 362 0 0 1 0 467 0 0 0 0 260
/**
 * This table records data about a list of animations for a particular model,
 * such as number of frames and frame rate.  It's used for implementating
 * asynchronous binding.
 *
 * This table is normally built by an offline tool, such as egg-optchar.
 */

467 17 CopyOnWriteObject 0 2048 17 CopyOnWriteObject 17 CopyOnWriteObject 0 0 0 0 0 0 0 0 0 0 0 0 169
/**
 * This base class provides basic reference counting, but also can be used
 * with a CopyOnWritePointer to provide get_read_pointer() and
 * get_write_pointer().
 */

468 10 PartSubset 0 141313 10 PartSubset 10 PartSubset 0 0 0 1 364 373 0 8 365 366 367 368 369 370 371 372 0 0 0 0 0 190
/**
 * This class is used to define a subset of part names to apply to the
 * PartBundle::bind_anim() operation.  Only those part names within the subset
 * will be included in the bind.
 */

469 15 BindAnimRequest 0 141313 15 BindAnimRequest 15 BindAnimRequest 0 0 0 1 374 376 0 1 375 0 0 1 0 470 0 0 0 0 136
/**
 * This class object manages an asynchronous load-and-bind animation request,
 * as issued through PartBundle::load_bind_anim().
 */

470 16 ModelLoadRequest 0 2048 16 ModelLoadRequest 16 ModelLoadRequest 0 0 0 0 0 0 0 0 0 0 0 0 191
/**
 * A class object that manages a single asynchronous model load request.
 * Create a new ModelLoadRequest, and add it to the loader via load_async(),
 * to begin an asynchronous load.
 */

471 10 PartBundle 0 141313 10 PartBundle 10 PartBundle 0 0 0 1 377 416 5 592 593 594 595 596 30 378 379 380 381 382 383 384 385 386 387 388 389 390 391 392 393 394 403 404 405 406 407 408 409 410 411 412 413 414 415 1 604 0 1 0 442 0 0 0 1 472 134
/**
 * This is the root of a MovingPart hierarchy.  It defines the hierarchy of
 * moving parts that make up an animatable object.
 */

472 9 BlendType 0 794624 21 PartBundle::BlendType 21 PartBundle::BlendType 471 0 0 0 0 0 0 0 0 0 4 9 BT_linear 21 PartBundle::BT_linear 387
// BT_linear does a componentwise average of all blended matrices, which
// is a linear blend.  The result of this is that if a particular vertex
// would have been at point P in one animation and point Q in another one,
// it will end up on the line in between them in the resulting blend
// animation.  However, this tends to stretch and squash limbs in strange
// and disturbing ways.
0 20 BT_normalized_linear 32 PartBundle::BT_normalized_linear 452
// BT_normalized_linear is a compromise on BT_linear.  The matrix is
// blended linearly without the scale and shear components, and the
// blended scale and shear components are applied separately.  This keeps
// all of the character's body parts in the correct size and shape.
// However, if the hierarchy is disconnected, body parts can fly off.
// It's essential the skeleton hierarchy be completely connected to use
// this blend mode successully.
1 16 BT_componentwise 28 PartBundle::BT_componentwise 115
// BT_componentwise linearly blends all components separately, including
// H, P, and R, and recomposes the matrix.
2 21 BT_componentwise_quat 33 PartBundle::BT_componentwise_quat 124
// BT_componentwise_quat linearly blends all components separately, except
// for rotation which is blended as a quaternion.
3 0 240
// This is the parameter to set_blend_type() and specifies the kind of
// blending operation to be performed when multiple controls are in effect
// simultaneously (see set_control_effect()) or between sequential frames of
// the animation.

473 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

474 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 450 0 0 0 0 0 0 0 0 0 0

475 16 PartBundleNode * 0 8576 16 PartBundleNode * 16 PartBundleNode * 0 0 476 0 0 0 0 0 0 0 0 0 0

476 14 PartBundleNode 0 75777 14 PartBundleNode 14 PartBundleNode 0 0 0 1 395 0 2 597 598 4 396 397 398 402 2 605 606 0 1 0 440 0 0 0 0 342
/**
 * This is a node that contains a pointer to an PartBundle.  Like
 * AnimBundleNode, it exists to make it easy to store PartBundles in the scene
 * graph.
 *
 * (Unlike AnimBundleNode, however, PartBundleNode has an additional function:
 * it is also the base class of the Character node type, which adds additional
 * functionality.)
 */

477 12 PartBundle * 0 8576 12 PartBundle * 12 PartBundle * 0 0 471 0 0 0 0 0 0 0 0 0 0

478 18 PartBundleHandle * 0 8576 18 PartBundleHandle * 18 PartBundleHandle * 0 0 479 0 0 0 0 0 0 0 0 0 0

479 16 PartBundleHandle 0 75777 16 PartBundleHandle 16 PartBundleHandle 0 0 0 1 399 0 1 599 2 400 401 0 0 1 0 480 0 0 0 0 546
/**
 * This is a trivial class returned by PartBundleNode::get_bundle().  Its
 * purpose is to hold the actual PartBundle pointer contained within the
 * PartBundleNode, so that scene graph flatten operations can safely combine
 * or duplicate PartBundles as necessary without affecting high-level bundle
 * operations.
 *
 * The high-level Actor class defined in direct/src/actor, for instance, will
 * store a list of PartBundleHandles instead of on actual PartBundles, so that
 * it will be immune to changes from these flatten operations.
 */

480 14 ReferenceCount 0 2048 14 ReferenceCount 14 ReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 203
/**
 * A base class for all things that want to be reference-counted.
 * ReferenceCount works in conjunction with PointerTo to automatically delete
 * objects when the last pointer to them goes away.
 */

481 14 MovingPartBase 0 141313 14 MovingPartBase 14 MovingPartBase 0 0 0 0 421 0 4 417 418 419 420 0 0 1 0 442 0 0 0 0 332
/**
 * This is the base class for a single animatable piece that may be bound to
 * one channel (or more, if blending is in effect).  It corresponds to, for
 * instance, a single joint or slider of a character.
 *
 * MovingPartBase does not have a particular value type.  See the derived
 * template class, MovingPart, for this.
 */

482 16 MovingPartMatrix 0 75777 16 MovingPartMatrix 16 MovingPartMatrix 0 0 0 0 425 0 1 426 0 0 1 0 483 0 0 0 0 84
/**
 * This is a particular kind of MovingPart that accepts a matrix each frame.
 */

483 32 MovingPart< ACMatrixSwitchType > 0 141313 32 MovingPart< ACMatrixSwitchType > 32 MovingPart< ACMatrixSwitchType > 0 0 0 0 425 0 3 422 423 424 0 0 1 0 481 0 0 0 1 450 0

484 16 MovingPartScalar 0 75777 16 MovingPartScalar 16 MovingPartScalar 0 0 0 0 430 0 1 431 0 0 1 0 485 0 0 0 0 84
/**
 * This is a particular kind of MovingPart that accepts a scalar each frame.
 */

485 32 MovingPart< ACScalarSwitchType > 0 141313 32 MovingPart< ACScalarSwitchType > 32 MovingPart< ACScalarSwitchType > 0 0 0 0 430 0 3 427 428 429 0 0 1 0 481 0 0 0 0 0

486 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

487 17 AnimGroup const * 0 8576 17 AnimGroup const * 17 AnimGroup const * 0 0 488 0 0 0 0 0 0 0 0 0 0

488 15 AnimGroup const 0 8832 15 AnimGroup const 15 AnimGroup const 0 0 432 0 0 0 0 0 0 0 0 0 0

489 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

490 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 491 0 0 0 0 0 0 0 0 0 0

491 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

492 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

493 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 492 0 0 0 0 0 0 0 0 0 0

494 29 TypedWritableReferenceCount * 0 8576 29 TypedWritableReferenceCount * 29 TypedWritableReferenceCount * 0 0 433 0 0 0 0 0 0 0 0 0 0

495 9 Namable * 0 8576 9 Namable * 9 Namable * 0 0 434 0 0 0 0 0 0 0 0 0 0

496 18 AnimBundle const * 0 8576 18 AnimBundle const * 18 AnimBundle const * 0 0 497 0 0 0 0 0 0 0 0 0 0

497 16 AnimBundle const 0 8832 16 AnimBundle const 16 AnimBundle const 0 0 436 0 0 0 0 0 0 0 0 0 0

498 16 AnimBundleNode * 0 8576 16 AnimBundleNode * 16 AnimBundleNode * 0 0 439 0 0 0 0 0 0 0 0 0 0

499 22 AnimBundleNode const * 0 8576 22 AnimBundleNode const * 22 AnimBundleNode const * 0 0 500 0 0 0 0 0 0 0 0 0 0

500 20 AnimBundleNode const 0 8832 20 AnimBundleNode const 20 AnimBundleNode const 0 0 439 0 0 0 0 0 0 0 0 0 0

501 17 PartGroup const * 0 8576 17 PartGroup const * 17 PartGroup const * 0 0 502 0 0 0 0 0 0 0 0 0 0

502 15 PartGroup const 0 8832 15 PartGroup const 15 PartGroup const 0 0 442 0 0 0 0 0 0 0 0 0 0

503 22 TransformState const * 0 8576 22 TransformState const * 22 TransformState const * 0 0 504 0 0 0 0 0 0 0 0 0 0

504 20 TransformState const 0 8832 20 TransformState const 20 TransformState const 0 0 505 0 0 0 0 0 0 0 0 0 0

505 14 TransformState 0 16779264 14 TransformState 14 TransformState 0 0 0 0 0 0 0 0 0 0 0 0 734
/**
 * Indicates a coordinate-system transform on vertices.  TransformStates are
 * the primary means for storing transformations on the scene graph.
 *
 * Transforms may be specified in one of two ways: componentwise, with a pos-
 * hpr-scale, or with an arbitrary transform matrix.  If you specify a
 * transform componentwise, it will remember its original components.
 *
 * TransformState objects are managed very much like RenderState objects.
 * They are immutable and reference-counted automatically.
 *
 * You should not attempt to create or modify a TransformState object
 * directly.  Instead, call one of the make() functions to create one for you.
 * And instead of modifying a TransformState object, create a new one.
 */

506 17 LVecBase3 const * 0 8576 17 LVecBase3 const * 17 LVecBase3 const * 0 0 507 0 0 0 0 0 0 0 0 0 0

507 15 LVecBase3 const 0 8832 15 LVecBase3 const 15 LVecBase3 const 0 0 508 0 0 0 0 0 0 0 0 0 0

508 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 509 0 0 0 0 0 0 0 0 0 0

509 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

510 17 AnimChannelBase * 0 8576 17 AnimChannelBase * 17 AnimChannelBase * 0 0 448 0 0 0 0 0 0 0 0 0 0

511 19 AnimControl const * 0 8576 19 AnimControl const * 19 AnimControl const * 0 0 512 0 0 0 0 0 0 0 0 0 0

512 17 AnimControl const 0 8832 17 AnimControl const 17 AnimControl const 0 0 445 0 0 0 0 0 0 0 0 0 0

513 13 AnimControl * 0 8576 13 AnimControl * 13 AnimControl * 0 0 445 0 0 0 0 0 0 0 0 0 0

514 16 BitArray const * 0 8576 16 BitArray const * 16 BitArray const * 0 0 515 0 0 0 0 0 0 0 0 0 0

515 14 BitArray const 0 8832 14 BitArray const 14 BitArray const 0 0 516 0 0 0 0 0 0 0 0 0 0

516 8 BitArray 0 2048 8 BitArray 8 BitArray 0 0 0 0 0 0 0 0 0 0 0 0 210
/**
 * A dynamic array with an unlimited number of bits.
 *
 * This is similar to a BitMask, except it appears to contain an infinite
 * number of bits.  You can use it very much as you would use a BitMask.
 */

517 21 TypedReferenceCount * 0 8576 21 TypedReferenceCount * 21 TypedReferenceCount * 0 0 446 0 0 0 0 0 0 0 0 0 0

518 15 AnimInterface * 0 8576 15 AnimInterface * 15 AnimInterface * 0 0 447 0 0 0 0 0 0 0 0 0 0

519 23 AnimChannelBase const * 0 8576 23 AnimChannelBase const * 23 AnimChannelBase const * 0 0 520 0 0 0 0 0 0 0 0 0 0

520 21 AnimChannelBase const 0 8832 21 AnimChannelBase const 21 AnimChannelBase const 0 0 448 0 0 0 0 0 0 0 0 0 0

521 35 AnimChannel< ACMatrixSwitchType > * 0 8576 35 AnimChannel< ACMatrixSwitchType > * 35 AnimChannel< ACMatrixSwitchType > * 0 0 449 0 0 0 0 0 0 0 0 0 0

522 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 450 0 0 0 0 0 0 0 0 0 0

523 11 LVecBase3 * 0 8576 11 LVecBase3 * 11 LVecBase3 * 0 0 508 0 0 0 0 0 0 0 0 0 0

524 13 LQuaternion * 0 8576 13 LQuaternion * 13 LQuaternion * 0 0 525 0 0 0 0 0 0 0 0 0 0

525 11 LQuaternion 0 2105344 11 LQuaternion 11 LQuaternion 0 0 526 0 0 0 0 0 0 0 0 0 0

526 12 LQuaternionf 0 2048 12 LQuaternionf 12 LQuaternionf 0 0 0 0 0 0 0 0 0 0 0 0 44
/**
 * This is the base quaternion class
 */

527 41 AnimChannel< ACMatrixSwitchType > const * 0 8576 41 AnimChannel< ACMatrixSwitchType > const * 41 AnimChannel< ACMatrixSwitchType > const * 0 0 528 0 0 0 0 0 0 0 0 0 0

528 39 AnimChannel< ACMatrixSwitchType > const 0 8832 39 AnimChannel< ACMatrixSwitchType > const 39 AnimChannel< ACMatrixSwitchType > const 0 0 449 0 0 0 0 0 0 0 0 0 0

529 35 AnimChannel< ACScalarSwitchType > * 0 8576 35 AnimChannel< ACScalarSwitchType > * 35 AnimChannel< ACScalarSwitchType > * 0 0 453 0 0 0 0 0 0 0 0 0 0

530 41 AnimChannel< ACScalarSwitchType > const * 0 8576 41 AnimChannel< ACScalarSwitchType > const * 41 AnimChannel< ACScalarSwitchType > const * 0 0 531 0 0 0 0 0 0 0 0 0 0

531 39 AnimChannel< ACScalarSwitchType > const 0 8832 39 AnimChannel< ACScalarSwitchType > const 39 AnimChannel< ACScalarSwitchType > const 0 0 453 0 0 0 0 0 0 0 0 0 0

532 26 AnimChannelMatrixDynamic * 0 8576 26 AnimChannelMatrixDynamic * 26 AnimChannelMatrixDynamic * 0 0 455 0 0 0 0 0 0 0 0 0 0

533 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 474 0 0 0 0 0 0 0 0 0 0

534 32 AnimChannelMatrixDynamic const * 0 8576 32 AnimChannelMatrixDynamic const * 32 AnimChannelMatrixDynamic const * 0 0 535 0 0 0 0 0 0 0 0 0 0

535 30 AnimChannelMatrixDynamic const 0 8832 30 AnimChannelMatrixDynamic const 30 AnimChannelMatrixDynamic const 0 0 455 0 0 0 0 0 0 0 0 0 0

536 27 AnimChannelMatrixXfmTable * 0 8576 27 AnimChannelMatrixXfmTable * 27 AnimChannelMatrixXfmTable * 0 0 457 0 0 0 0 0 0 0 0 0 0

537 4 char 0 8194 4 char 4 char 0 5 0 0 0 0 0 0 0 0 0 0 0

538 21 CPTA_stdfloat const * 0 8576 21 CPTA_stdfloat const * 21 CPTA_stdfloat const * 0 0 539 0 0 0 0 0 0 0 0 0 0

539 19 CPTA_stdfloat const 0 8832 19 CPTA_stdfloat const 19 CPTA_stdfloat const 0 0 458 0 0 0 0 0 0 0 0 0 0

540 33 AnimChannelMatrixXfmTable const * 0 8576 33 AnimChannelMatrixXfmTable const * 33 AnimChannelMatrixXfmTable const * 0 0 541 0 0 0 0 0 0 0 0 0 0

541 31 AnimChannelMatrixXfmTable const 0 8832 31 AnimChannelMatrixXfmTable const 31 AnimChannelMatrixXfmTable const 0 0 457 0 0 0 0 0 0 0 0 0 0

542 15 CPTA_stdfloat * 0 8576 15 CPTA_stdfloat * 15 CPTA_stdfloat * 0 0 458 0 0 0 0 0 0 0 0 0 0

543 26 AnimChannelScalarDynamic * 0 8576 26 AnimChannelScalarDynamic * 26 AnimChannelScalarDynamic * 0 0 461 0 0 0 0 0 0 0 0 0 0

544 24 AnimChannelScalarTable * 0 8576 24 AnimChannelScalarTable * 24 AnimChannelScalarTable * 0 0 464 0 0 0 0 0 0 0 0 0 0

545 30 AnimChannelScalarTable const * 0 8576 30 AnimChannelScalarTable const * 30 AnimChannelScalarTable const * 0 0 546 0 0 0 0 0 0 0 0 0 0

546 28 AnimChannelScalarTable const 0 8832 28 AnimChannelScalarTable const 28 AnimChannelScalarTable const 0 0 464 0 0 0 0 0 0 0 0 0 0

547 23 AnimControlCollection * 0 8576 23 AnimControlCollection * 23 AnimControlCollection * 0 0 465 0 0 0 0 0 0 0 0 0 0

548 29 AnimControlCollection const * 0 8576 29 AnimControlCollection const * 29 AnimControlCollection const * 0 0 549 0 0 0 0 0 0 0 0 0 0

549 27 AnimControlCollection const 0 8832 27 AnimControlCollection const 27 AnimControlCollection const 0 0 465 0 0 0 0 0 0 0 0 0 0

550 18 AnimPreloadTable * 0 8576 18 AnimPreloadTable * 18 AnimPreloadTable * 0 0 466 0 0 0 0 0 0 0 0 0 0

551 24 AnimPreloadTable const * 0 8576 24 AnimPreloadTable const * 24 AnimPreloadTable const * 0 0 552 0 0 0 0 0 0 0 0 0 0

552 22 AnimPreloadTable const 0 8832 22 AnimPreloadTable const 22 AnimPreloadTable const 0 0 466 0 0 0 0 0 0 0 0 0 0

553 12 PartSubset * 0 8576 12 PartSubset * 12 PartSubset * 0 0 468 0 0 0 0 0 0 0 0 0 0

554 18 PartSubset const * 0 8576 18 PartSubset const * 18 PartSubset const * 0 0 555 0 0 0 0 0 0 0 0 0 0

555 16 PartSubset const 0 8832 16 PartSubset const 16 PartSubset const 0 0 468 0 0 0 0 0 0 0 0 0 0

556 19 GlobPattern const * 0 8576 19 GlobPattern const * 19 GlobPattern const * 0 0 557 0 0 0 0 0 0 0 0 0 0

557 17 GlobPattern const 0 8832 17 GlobPattern const 17 GlobPattern const 0 0 558 0 0 0 0 0 0 0 0 0 0

558 11 GlobPattern 0 2048 11 GlobPattern 11 GlobPattern 0 0 0 0 0 0 0 0 0 0 0 0 538
/**
 * This class can be used to test for string matches against standard Unix-
 * shell filename globbing conventions.  It serves as a portable standin for
 * the Posix fnmatch() call.
 *
 * A GlobPattern is given a pattern string, which can contain operators like
 * *, ?, and [].  Then it can be tested against any number of candidate
 * strings; for each candidate, it will indicate whether the string matches
 * the pattern or not.  It can be used, for example, to scan a directory for
 * all files matching a particular pattern.
 */

559 23 BindAnimRequest const * 0 8576 23 BindAnimRequest const * 23 BindAnimRequest const * 0 0 560 0 0 0 0 0 0 0 0 0 0

560 21 BindAnimRequest const 0 8832 21 BindAnimRequest const 21 BindAnimRequest const 0 0 469 0 0 0 0 0 0 0 0 0 0

561 17 BindAnimRequest * 0 8576 17 BindAnimRequest * 17 BindAnimRequest * 0 0 469 0 0 0 0 0 0 0 0 0 0

562 16 Filename const * 0 8576 16 Filename const * 16 Filename const * 0 0 563 0 0 0 0 0 0 0 0 0 0

563 14 Filename const 0 8832 14 Filename const 14 Filename const 0 0 564 0 0 0 0 0 0 0 0 0 0

564 8 Filename 0 2048 8 Filename 8 Filename 0 0 0 0 0 0 0 0 0 0 0 0 839
/**
 * The name of a file, such as a texture file or an Egg file.  Stores the full
 * pathname, and includes functions for extracting out the directory prefix
 * part and the file extension and stuff.
 *
 * A Filename is also aware of the mapping between the Unix-like filename
 * convention we use internally, and the local OS's specific filename
 * convention, and it knows how to perform basic OS-specific I/O, like testing
 * for file existence and searching a searchpath, as well as the best way to
 * open an fstream for reading or writing.
 *
 * Note that the methods of Filename that interact with the filesystem (such
 * as exists(), open_read(), etc.) directly interface with the operating system
 * and are not aware of Panda's virtual file system.  To interact with the VFS,
 * use the methods on VirtualFileSystem instead.
 */

565 21 LoaderOptions const * 0 8576 21 LoaderOptions const * 21 LoaderOptions const * 0 0 566 0 0 0 0 0 0 0 0 0 0

566 19 LoaderOptions const 0 8832 19 LoaderOptions const 19 LoaderOptions const 0 0 567 0 0 0 0 0 0 0 0 0 0

567 13 LoaderOptions 0 2048 13 LoaderOptions 13 LoaderOptions 0 0 0 0 0 0 0 0 0 0 0 0 65
/**
 * Specifies parameters that may be passed to the loader.
 */

568 8 Loader * 0 8576 8 Loader * 8 Loader * 0 0 569 0 0 0 0 0 0 0 0 0 0

569 6 Loader 0 2048 6 Loader 6 Loader 0 0 0 0 0 0 0 0 0 0 0 0 503
/**
 * A convenient class for loading models from disk, in bam or egg format (or
 * any of a number of other formats implemented by a LoaderFileType, such as
 * ptloader).
 *
 * This class supports synchronous as well as asynchronous loading.  In
 * asynchronous loading, the model is loaded in the background by a thread,
 * and an event will be generated when the model is available.  If threading
 * is not available, the asynchronous loading interface may be used, but it
 * loads synchronously.
 */

570 18 PartBundle const * 0 8576 18 PartBundle const * 18 PartBundle const * 0 0 571 0 0 0 0 0 0 0 0 0 0

571 16 PartBundle const 0 8832 16 PartBundle const 16 PartBundle const 0 0 471 0 0 0 0 0 0 0 0 0 0

572 22 PartBundleNode const * 0 8576 22 PartBundleNode const * 22 PartBundleNode const * 0 0 573 0 0 0 0 0 0 0 0 0 0

573 20 PartBundleNode const 0 8832 20 PartBundleNode const 20 PartBundleNode const 0 0 476 0 0 0 0 0 0 0 0 0 0

574 24 PartBundleHandle const * 0 8576 24 PartBundleHandle const * 24 PartBundleHandle const * 0 0 575 0 0 0 0 0 0 0 0 0 0

575 22 PartBundleHandle const 0 8832 22 PartBundleHandle const 22 PartBundleHandle const 0 0 479 0 0 0 0 0 0 0 0 0 0

576 22 MovingPartBase const * 0 8576 22 MovingPartBase const * 22 MovingPartBase const * 0 0 577 0 0 0 0 0 0 0 0 0 0

577 20 MovingPartBase const 0 8832 20 MovingPartBase const 20 MovingPartBase const 0 0 481 0 0 0 0 0 0 0 0 0 0

578 40 MovingPart< ACMatrixSwitchType > const * 0 8576 40 MovingPart< ACMatrixSwitchType > const * 40 MovingPart< ACMatrixSwitchType > const * 0 0 579 0 0 0 0 0 0 0 0 0 0

579 38 MovingPart< ACMatrixSwitchType > const 0 8832 38 MovingPart< ACMatrixSwitchType > const 38 MovingPart< ACMatrixSwitchType > const 0 0 483 0 0 0 0 0 0 0 0 0 0

580 40 MovingPart< ACScalarSwitchType > const * 0 8576 40 MovingPart< ACScalarSwitchType > const * 40 MovingPart< ACScalarSwitchType > const * 0 0 581 0 0 0 0 0 0 0 0 0 0

581 38 MovingPart< ACScalarSwitchType > const 0 8832 38 MovingPart< ACScalarSwitchType > const 38 MovingPart< ACScalarSwitchType > const 0 0 485 0 0 0 0 0 0 0 0 0 0

0
18
582 8 children 0 66 435 218 0 0 0 0 217 0 0 19 AnimGroup::children 0

583 15 base_frame_rate 0 2 437 227 0 0 0 0 0 0 0 27 AnimBundle::base_frame_rate 0

584 10 num_frames 0 2 438 228 0 0 0 0 0 0 0 22 AnimBundle::num_frames 0

585 6 bundle 0 2 441 232 0 0 0 0 0 0 0 22 AnimBundleNode::bundle 0

586 8 children 0 66 444 246 0 0 0 0 245 0 0 19 PartGroup::children 0

587 10 value_node 0 6 456 303 301 0 0 0 0 0 0 36 AnimChannelMatrixDynamic::value_node 0

588 6 tables 0 174 458 309 308 311 0 312 0 0 0 33 AnimChannelMatrixXfmTable::tables 0

589 5 value 0 6 462 316 314 0 0 0 0 0 0 31 AnimChannelScalarDynamic::value 0

590 10 value_node 0 6 456 317 315 0 0 0 0 0 0 36 AnimChannelScalarDynamic::value_node 0

591 5 table 0 30 458 322 321 323 324 0 0 0 0 29 AnimChannelScalarTable::table 0

592 10 blend_type 0 6 472 384 383 0 0 0 0 0 0 22 PartBundle::blend_type 0

593 15 anim_blend_flag 0 6 473 386 385 0 0 0 0 0 0 27 PartBundle::anim_blend_flag 0

594 16 frame_blend_flag 0 6 473 388 387 0 0 0 0 0 0 28 PartBundle::frame_blend_flag 0

595 10 root_xform 0 6 474 391 389 0 0 0 0 0 0 22 PartBundle::root_xform 0

596 5 nodes 0 66 475 394 0 0 0 0 393 0 0 17 PartBundle::nodes 0

597 7 bundles 0 66 477 397 0 0 0 0 396 0 0 23 PartBundleNode::bundles 0

598 14 bundle_handles 0 66 478 398 0 0 0 0 396 0 0 30 PartBundleNode::bundle_handles 0

599 6 bundle 0 6 477 400 401 0 0 0 0 0 0 24 PartBundleHandle::bundle 0

7
600 12 get_children 0 217 218 23 AnimGroup::get_children 0

601 12 get_children 0 245 246 23 PartGroup::get_children 0

602 9 get_anims 0 332 333 32 AnimControlCollection::get_anims 0

603 14 get_anim_names 0 332 334 37 AnimControlCollection::get_anim_names 0

604 9 get_nodes 0 393 394 21 PartBundle::get_nodes 0

605 11 get_bundles 0 396 397 27 PartBundleNode::get_bundles 0

606 18 get_bundle_handles 0 396 398 34 PartBundleNode::get_bundle_handles 0

