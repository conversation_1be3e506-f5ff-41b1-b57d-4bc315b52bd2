'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_swap_group'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_swap_group',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLuint,_cs.GLuint)
def glXBindSwapBarrierNV(dpy,group,barrier):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.<PERSON>Luint)
def glXJoinSwapGroupNV(dpy,drawable,group):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.c_int,arrays.GLuintArray)
def glXQueryFrameCountNV(dpy,screen,count):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.c_int,arrays.GLuintArray,arrays.GLuintArray)
def glXQueryMaxSwapGroupsNV(dpy,screen,maxGroups,maxBarriers):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,arrays.GLuintArray,arrays.GLuintArray)
def glXQuerySwapGroupNV(dpy,drawable,group,barrier):pass
@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.c_int)
def glXResetFrameCountNV(dpy,screen):pass
