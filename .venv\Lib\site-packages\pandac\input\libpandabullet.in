1730992849
3 3
14 libpandabullet 4 fCJ_ 14 panda3d.bullet 
747
755 19 get_default_up_axis 0 1 0 19 get_default_up_axis 0 1 753 10
/**
 *
 */
39
BulletUpAxis get_default_up_axis(void);

756 18 get_bullet_version 0 1 0 18 get_bullet_version 0 1 754 60
/**
 * Returns the version of the linked Bullet library.
 */
29
int get_bullet_version(void);

757 5 empty 0 4 1503 19 BulletRayHit::empty 0 1 3 101
/**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */
53
static inline BulletRayHit BulletRayHit::empty(void);

758 8 get_node 0 4 1503 22 BulletRayHit::get_node 0 1 4 10
/**
 *
 */
46
PandaNode *BulletRayHit::get_node(void) const;

759 11 get_hit_pos 0 4 1503 25 BulletRayHit::get_hit_pos 0 1 5 10
/**
 *
 */
46
LPoint3 BulletRayHit::get_hit_pos(void) const;

760 14 get_hit_normal 0 4 1503 28 BulletRayHit::get_hit_normal 0 1 6 10
/**
 *
 */
50
LVector3 BulletRayHit::get_hit_normal(void) const;

761 16 get_hit_fraction 0 4 1503 30 BulletRayHit::get_hit_fraction 0 1 7 10
/**
 *
 */
55
PN_stdfloat BulletRayHit::get_hit_fraction(void) const;

762 14 get_shape_part 0 4 1503 28 BulletRayHit::get_shape_part 0 1 8 10
/**
 *
 */
45
int BulletRayHit::get_shape_part(void) const;

763 18 get_triangle_index 0 4 1503 32 BulletRayHit::get_triangle_index 0 1 9 10
/**
 *
 */
49
int BulletRayHit::get_triangle_index(void) const;

764 12 BulletRayHit 0 260 1503 26 BulletRayHit::BulletRayHit 0 2 1 2 0
117
inline BulletRayHit::BulletRayHit(void) = default;
inline BulletRayHit::BulletRayHit(BulletRayHit const &) = default;

765 13 ~BulletRayHit 0 516 1503 27 BulletRayHit::~BulletRayHit 0 0 0
34
BulletRayHit::~BulletRayHit(void);

766 34 upcast_to_AllHitsRayResultCallback 0 12 1513 58 BulletAllHitsRayResult::upcast_to_AllHitsRayResultCallback 0 1 17 62
upcast from BulletAllHitsRayResult to AllHitsRayResultCallback
109
btCollisionWorld::AllHitsRayResultCallback *BulletAllHitsRayResult::upcast_to_AllHitsRayResultCallback(void);

767 59 downcast_AllHitsRayResultCallback_to_BulletAllHitsRayResult 0 8 0 59 downcast_AllHitsRayResultCallback_to_BulletAllHitsRayResult 0 0 64
downcast from AllHitsRayResultCallback to BulletAllHitsRayResult
134
BulletAllHitsRayResult *downcast_AllHitsRayResultCallback_to_BulletAllHitsRayResult(btCollisionWorld::AllHitsRayResultCallback *this);

768 5 empty 0 4 1513 29 BulletAllHitsRayResult::empty 0 1 10 101
/**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */
73
static inline BulletAllHitsRayResult BulletAllHitsRayResult::empty(void);

769 12 get_from_pos 0 4 1513 36 BulletAllHitsRayResult::get_from_pos 0 1 11 10
/**
 *
 */
57
LPoint3 BulletAllHitsRayResult::get_from_pos(void) const;

770 10 get_to_pos 0 4 1513 34 BulletAllHitsRayResult::get_to_pos 0 1 12 10
/**
 *
 */
55
LPoint3 BulletAllHitsRayResult::get_to_pos(void) const;

771 8 has_hits 0 4 1513 32 BulletAllHitsRayResult::has_hits 0 1 13 10
/**
 *
 */
50
bool BulletAllHitsRayResult::has_hits(void) const;

772 24 get_closest_hit_fraction 0 4 1513 48 BulletAllHitsRayResult::get_closest_hit_fraction 0 1 14 10
/**
 *
 */
73
PN_stdfloat BulletAllHitsRayResult::get_closest_hit_fraction(void) const;

773 12 get_num_hits 0 4 1513 36 BulletAllHitsRayResult::get_num_hits 0 1 15 10
/**
 *
 */
53
int BulletAllHitsRayResult::get_num_hits(void) const;

774 7 get_hit 0 4 1513 31 BulletAllHitsRayResult::get_hit 0 1 16 10
/**
 *
 */
66
BulletRayHit const BulletAllHitsRayResult::get_hit(int idx) const;

775 23 ~BulletAllHitsRayResult 0 516 1513 47 BulletAllHitsRayResult::~BulletAllHitsRayResult 0 0 0
54
BulletAllHitsRayResult::~BulletAllHitsRayResult(void);

776 13 is_polyhedral 0 4 1516 26 BulletShape::is_polyhedral 0 1 18 10
/**
 *
 */
44
bool BulletShape::is_polyhedral(void) const;

777 9 is_convex 0 4 1516 22 BulletShape::is_convex 0 1 19 10
/**
 *
 */
40
bool BulletShape::is_convex(void) const;

778 12 is_convex_2d 0 4 1516 25 BulletShape::is_convex_2d 0 1 20 10
/**
 *
 */
43
bool BulletShape::is_convex_2d(void) const;

779 10 is_concave 0 4 1516 23 BulletShape::is_concave 0 1 21 10
/**
 *
 */
41
bool BulletShape::is_concave(void) const;

780 11 is_infinite 0 4 1516 24 BulletShape::is_infinite 0 1 22 10
/**
 *
 */
42
bool BulletShape::is_infinite(void) const;

781 13 is_non_moving 0 4 1516 26 BulletShape::is_non_moving 0 1 23 10
/**
 *
 */
44
bool BulletShape::is_non_moving(void) const;

782 12 is_soft_body 0 4 1516 25 BulletShape::is_soft_body 0 1 24 10
/**
 *
 */
43
bool BulletShape::is_soft_body(void) const;

783 10 set_margin 0 4 1516 23 BulletShape::set_margin 0 1 25 10
/**
 *
 */
49
void BulletShape::set_margin(PN_stdfloat margin);

784 8 get_name 0 4 1516 21 BulletShape::get_name 0 1 26 10
/**
 *
 */
46
char const *BulletShape::get_name(void) const;

785 10 get_margin 0 4 1516 23 BulletShape::get_margin 0 1 27 10
/**
 *
 */
48
PN_stdfloat BulletShape::get_margin(void) const;

786 16 get_shape_bounds 0 4 1516 29 BulletShape::get_shape_bounds 0 1 28 62
/**
 * Returns the current bounds of this collision shape.
 */
57
BoundingSphere BulletShape::get_shape_bounds(void) const;

787 14 get_class_type 0 4 1516 27 BulletShape::get_class_type 0 1 29 0
52
static TypeHandle BulletShape::get_class_type(void);

788 14 get_class_type 0 4 1523 49 BulletBaseCharacterControllerNode::get_class_type 0 1 30 0
74
static TypeHandle BulletBaseCharacterControllerNode::get_class_type(void);

789 34 ~BulletBaseCharacterControllerNode 0 516 1523 69 BulletBaseCharacterControllerNode::~BulletBaseCharacterControllerNode 0 0 0
76
BulletBaseCharacterControllerNode::~BulletBaseCharacterControllerNode(void);

790 9 add_shape 0 4 1524 25 BulletBodyNode::add_shape 0 1 31 21
// Shapes

/**
 *
 */
114
void BulletBodyNode::add_shape(BulletShape *shape, TransformState const *xform = TransformState::make_identity());

791 12 remove_shape 0 4 1524 28 BulletBodyNode::remove_shape 0 1 32 10
/**
 *
 */
54
void BulletBodyNode::remove_shape(BulletShape *shape);

792 14 get_num_shapes 0 4 1524 30 BulletBodyNode::get_num_shapes 0 1 33 10
/**
 *
 */
47
int BulletBodyNode::get_num_shapes(void) const;

793 9 get_shape 0 4 1524 25 BulletBodyNode::get_shape 0 1 34 10
/**
 *
 */
54
BulletShape *BulletBodyNode::get_shape(int idx) const;

794 13 get_shape_pos 0 4 1524 29 BulletBodyNode::get_shape_pos 0 1 35 10
/**
 *
 */
53
LPoint3 BulletBodyNode::get_shape_pos(int idx) const;

795 13 get_shape_mat 0 4 1524 29 BulletBodyNode::get_shape_mat 0 1 36 10
/**
 *
 */
54
LMatrix4 BulletBodyNode::get_shape_mat(int idx) const;

796 19 get_shape_transform 0 4 1524 35 BulletBodyNode::get_shape_transform 0 1 37 10
/**
 *
 */
84
ConstPointerTo< TransformState > BulletBodyNode::get_shape_transform(int idx) const;

797 16 get_shape_bounds 0 4 1524 32 BulletBodyNode::get_shape_bounds 0 1 38 81
/**
 * Returns the current bounds of all collision shapes owned by this body.
 */
60
BoundingSphere BulletBodyNode::get_shape_bounds(void) const;

798 32 add_shapes_from_collision_solids 0 4 1524 48 BulletBodyNode::add_shapes_from_collision_solids 0 1 39 10
/**
 *
 */
76
void BulletBodyNode::add_shapes_from_collision_solids(CollisionNode *cnode);

799 9 is_static 0 4 1524 25 BulletBodyNode::is_static 0 1 40 60
// Static and kinematic

// Static and kinematic

/**
 *
 */
43
bool BulletBodyNode::is_static(void) const;

800 12 is_kinematic 0 4 1524 28 BulletBodyNode::is_kinematic 0 1 41 10
/**
 *
 */
46
bool BulletBodyNode::is_kinematic(void) const;

801 10 set_static 0 4 1524 26 BulletBodyNode::set_static 0 1 42 10
/**
 *
 */
51
inline void BulletBodyNode::set_static(bool value);

802 13 set_kinematic 0 4 1524 29 BulletBodyNode::set_kinematic 0 1 43 10
/**
 *
 */
54
inline void BulletBodyNode::set_kinematic(bool value);

803 21 set_into_collide_mask 0 4 1524 37 BulletBodyNode::set_into_collide_mask 0 1 44 23
// Contacts

/**
 *
 */
68
inline void BulletBodyNode::set_into_collide_mask(CollideMask mask);

804 17 notify_collisions 0 4 1524 33 BulletBodyNode::notify_collisions 0 1 45 10
/**
 *
 */
58
inline void BulletBodyNode::notify_collisions(bool value);

805 19 notifies_collisions 0 4 1524 35 BulletBodyNode::notifies_collisions 0 1 46 10
/**
 *
 */
60
inline bool BulletBodyNode::notifies_collisions(void) const;

806 22 set_collision_response 0 4 1524 38 BulletBodyNode::set_collision_response 0 1 47 10
/**
 *
 */
63
inline void BulletBodyNode::set_collision_response(bool value);

807 22 get_collision_response 0 4 1524 38 BulletBodyNode::get_collision_response 0 1 48 10
/**
 *
 */
63
inline bool BulletBodyNode::get_collision_response(void) const;

808 20 check_collision_with 0 4 1524 36 BulletBodyNode::check_collision_with 0 1 49 10
/**
 *
 */
59
bool BulletBodyNode::check_collision_with(PandaNode *node);

809 20 has_contact_response 0 4 1524 36 BulletBodyNode::has_contact_response 0 1 50 10
/**
 *
 */
54
bool BulletBodyNode::has_contact_response(void) const;

810 32 get_contact_processing_threshold 0 4 1524 48 BulletBodyNode::get_contact_processing_threshold 0 1 51 10
/**
 *
 */
73
PN_stdfloat BulletBodyNode::get_contact_processing_threshold(void) const;

811 32 set_contact_processing_threshold 0 4 1524 48 BulletBodyNode::set_contact_processing_threshold 0 1 52 106
/**
 * The constraint solver can discard solving contacts, if the distance is
 * above this threshold.
 */
77
void BulletBodyNode::set_contact_processing_threshold(PN_stdfloat threshold);

812 9 is_active 0 4 1524 25 BulletBodyNode::is_active 0 1 53 44
// Deactivation

// Deactivation

/**
 *
 */
43
bool BulletBodyNode::is_active(void) const;

813 10 set_active 0 4 1524 26 BulletBodyNode::set_active 0 1 54 10
/**
 *
 */
65
void BulletBodyNode::set_active(bool active, bool force = false);

814 12 force_active 0 4 1524 28 BulletBodyNode::force_active 0 1 55 10
/**
 *
 */
47
void BulletBodyNode::force_active(bool active);

815 21 set_deactivation_time 0 4 1524 37 BulletBodyNode::set_deactivation_time 0 1 56 10
/**
 *
 */
59
void BulletBodyNode::set_deactivation_time(PN_stdfloat dt);

816 21 get_deactivation_time 0 4 1524 37 BulletBodyNode::get_deactivation_time 0 1 57 10
/**
 *
 */
62
PN_stdfloat BulletBodyNode::get_deactivation_time(void) const;

817 24 set_deactivation_enabled 0 4 1524 40 BulletBodyNode::set_deactivation_enabled 0 1 58 163
/**
 * If true, this object will be deactivated after a certain amount of time has
 * passed without movement.  If false, the object will always remain active.
 */
60
void BulletBodyNode::set_deactivation_enabled(bool enabled);

818 23 is_deactivation_enabled 0 4 1524 39 BulletBodyNode::is_deactivation_enabled 0 1 59 10
/**
 *
 */
57
bool BulletBodyNode::is_deactivation_enabled(void) const;

819 17 set_debug_enabled 0 4 1524 33 BulletBodyNode::set_debug_enabled 0 1 60 180
// Debug Visualisation

// Debug Visualisation

/**
 * Enables or disables the debug visualisation for this collision object.  By
 * default the debug visualisation is enabled.
 */
66
inline void BulletBodyNode::set_debug_enabled(bool const enabled);

820 16 is_debug_enabled 0 4 1524 32 BulletBodyNode::is_debug_enabled 0 1 61 141
/**
 * Returns TRUE if the debug visualisation is enabled for this collision
 * object, and FALSE if the debug visualisation is disabled.
 */
57
inline bool BulletBodyNode::is_debug_enabled(void) const;

821 15 get_restitution 0 4 1524 31 BulletBodyNode::get_restitution 0 1 62 68
// Friction and Restitution

// Friction and Restitution

/**
 *
 */
56
PN_stdfloat BulletBodyNode::get_restitution(void) const;

822 15 set_restitution 0 4 1524 31 BulletBodyNode::set_restitution 0 1 63 10
/**
 *
 */
62
void BulletBodyNode::set_restitution(PN_stdfloat restitution);

823 12 get_friction 0 4 1524 28 BulletBodyNode::get_friction 0 1 64 10
/**
 *
 */
53
PN_stdfloat BulletBodyNode::get_friction(void) const;

824 12 set_friction 0 4 1524 28 BulletBodyNode::set_friction 0 1 65 10
/**
 *
 */
56
void BulletBodyNode::set_friction(PN_stdfloat friction);

825 24 has_anisotropic_friction 0 4 1524 40 BulletBodyNode::has_anisotropic_friction 0 1 66 10
/**
 *
 */
58
bool BulletBodyNode::has_anisotropic_friction(void) const;

826 24 set_anisotropic_friction 0 4 1524 40 BulletBodyNode::set_anisotropic_friction 0 1 67 10
/**
 *
 */
73
void BulletBodyNode::set_anisotropic_friction(LVecBase3 const &friction);

827 24 get_anisotropic_friction 0 4 1524 40 BulletBodyNode::get_anisotropic_friction 0 1 68 10
/**
 *
 */
63
LVecBase3 BulletBodyNode::get_anisotropic_friction(void) const;

828 27 get_ccd_swept_sphere_radius 0 4 1524 43 BulletBodyNode::get_ccd_swept_sphere_radius 0 1 69 26
// CCD

// CCD

/**
 *
 */
68
PN_stdfloat BulletBodyNode::get_ccd_swept_sphere_radius(void) const;

829 24 get_ccd_motion_threshold 0 4 1524 40 BulletBodyNode::get_ccd_motion_threshold 0 1 70 10
/**
 *
 */
65
PN_stdfloat BulletBodyNode::get_ccd_motion_threshold(void) const;

830 27 set_ccd_swept_sphere_radius 0 4 1524 43 BulletBodyNode::set_ccd_swept_sphere_radius 0 1 71 10
/**
 *
 */
69
void BulletBodyNode::set_ccd_swept_sphere_radius(PN_stdfloat radius);

831 24 set_ccd_motion_threshold 0 4 1524 40 BulletBodyNode::set_ccd_motion_threshold 0 1 72 10
/**
 *
 */
69
void BulletBodyNode::set_ccd_motion_threshold(PN_stdfloat threshold);

832 19 set_transform_dirty 0 4 1524 35 BulletBodyNode::set_transform_dirty 0 1 73 228
// Special

/**
 * This method enforces an update of the Bullet transform, that is copies the
 * scene graph transform to the Bullet transform.  This is achieved by alling
 * the protected PandaNode hook 'transform_changed'.
 */
47
void BulletBodyNode::set_transform_dirty(void);

833 14 get_class_type 0 4 1524 30 BulletBodyNode::get_class_type 0 1 74 0
55
static TypeHandle BulletBodyNode::get_class_type(void);

834 14 BulletBoxShape 0 260 1531 30 BulletBoxShape::BulletBoxShape 0 2 75 76 62
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */
130
explicit BulletBoxShape::BulletBoxShape(LVecBase3 const &halfExtents);
BulletBoxShape::BulletBoxShape(BulletBoxShape const &copy);

835 31 get_half_extents_without_margin 0 4 1531 47 BulletBoxShape::get_half_extents_without_margin 0 1 77 10
/**
 *
 */
70
LVecBase3 BulletBoxShape::get_half_extents_without_margin(void) const;

836 28 get_half_extents_with_margin 0 4 1531 44 BulletBoxShape::get_half_extents_with_margin 0 1 78 10
/**
 *
 */
67
LVecBase3 BulletBoxShape::get_half_extents_with_margin(void) const;

837 15 make_from_solid 0 4 1531 31 BulletBoxShape::make_from_solid 0 1 79 10
/**
 *
 */
82
static BulletBoxShape *BulletBoxShape::make_from_solid(CollisionBox const *solid);

838 14 get_class_type 0 4 1531 30 BulletBoxShape::get_class_type 0 1 80 0
55
static TypeHandle BulletBoxShape::get_class_type(void);

839 18 BulletCapsuleShape 0 260 1532 38 BulletCapsuleShape::BulletCapsuleShape 0 2 81 82 62
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */
186
explicit BulletCapsuleShape::BulletCapsuleShape(PN_stdfloat radius, PN_stdfloat height, BulletUpAxis up = ::Z_up);
BulletCapsuleShape::BulletCapsuleShape(BulletCapsuleShape const &copy);

840 15 make_from_solid 0 4 1532 35 BulletCapsuleShape::make_from_solid 0 1 83 130
/**
 * Constructs a new BulletCapsuleShape using the information from a
 * CollisionCapsule from the builtin collision system.
 */
94
static BulletCapsuleShape *BulletCapsuleShape::make_from_solid(CollisionCapsule const *solid);

841 10 get_radius 0 4 1532 30 BulletCapsuleShape::get_radius 0 1 84 70
/**
 * Returns the radius that was used to construct this capsule.
 */
62
inline PN_stdfloat BulletCapsuleShape::get_radius(void) const;

842 15 get_half_height 0 4 1532 35 BulletCapsuleShape::get_half_height 0 1 85 81
/**
 * Returns half of get_height().
 * @deprecated see get_height() instead.
 */
67
inline PN_stdfloat BulletCapsuleShape::get_half_height(void) const;

843 10 get_height 0 4 1532 30 BulletCapsuleShape::get_height 0 0 70
/**
 * Returns the height that was used to construct this capsule.
 */
62
inline PN_stdfloat BulletCapsuleShape::get_height(void) const;

844 14 get_class_type 0 4 1532 34 BulletCapsuleShape::get_class_type 0 1 86 0
59
static TypeHandle BulletCapsuleShape::get_class_type(void);

845 29 BulletCharacterControllerNode 0 260 1533 60 BulletCharacterControllerNode::BulletCharacterControllerNode 0 1 87 10
/**
 *
 */
147
explicit BulletCharacterControllerNode::BulletCharacterControllerNode(BulletShape *shape, PN_stdfloat step_height, char const *name = "character");

846 19 set_linear_movement 0 4 1533 50 BulletCharacterControllerNode::set_linear_movement 0 1 88 10
/**
 *
 */
97
void BulletCharacterControllerNode::set_linear_movement(LVector3 const &velocity, bool is_local);

847 20 set_angular_movement 0 4 1533 51 BulletCharacterControllerNode::set_angular_movement 0 1 89 10
/**
 *
 */
76
void BulletCharacterControllerNode::set_angular_movement(PN_stdfloat omega);

848 9 get_shape 0 4 1533 40 BulletCharacterControllerNode::get_shape 0 1 90 10
/**
 *
 */
66
BulletShape *BulletCharacterControllerNode::get_shape(void) const;

849 11 set_gravity 0 4 1533 42 BulletCharacterControllerNode::set_gravity 0 1 91 10
/**
 *
 */
69
void BulletCharacterControllerNode::set_gravity(PN_stdfloat gravity);

850 11 get_gravity 0 4 1533 42 BulletCharacterControllerNode::get_gravity 0 1 92 10
/**
 *
 */
67
PN_stdfloat BulletCharacterControllerNode::get_gravity(void) const;

851 14 set_fall_speed 0 4 1533 45 BulletCharacterControllerNode::set_fall_speed 0 1 93 10
/**
 *
 */
75
void BulletCharacterControllerNode::set_fall_speed(PN_stdfloat fall_speed);

852 14 set_jump_speed 0 4 1533 45 BulletCharacterControllerNode::set_jump_speed 0 1 94 10
/**
 *
 */
75
void BulletCharacterControllerNode::set_jump_speed(PN_stdfloat jump_speed);

853 19 set_max_jump_height 0 4 1533 50 BulletCharacterControllerNode::set_max_jump_height 0 1 95 10
/**
 *
 */
85
void BulletCharacterControllerNode::set_max_jump_height(PN_stdfloat max_jump_height);

854 13 set_max_slope 0 4 1533 44 BulletCharacterControllerNode::set_max_slope 0 1 96 10
/**
 *
 */
73
void BulletCharacterControllerNode::set_max_slope(PN_stdfloat max_slope);

855 13 get_max_slope 0 4 1533 44 BulletCharacterControllerNode::get_max_slope 0 1 97 10
/**
 *
 */
69
PN_stdfloat BulletCharacterControllerNode::get_max_slope(void) const;

856 24 set_use_ghost_sweep_test 0 4 1533 55 BulletCharacterControllerNode::set_use_ghost_sweep_test 0 1 98 10
/**
 *
 */
73
void BulletCharacterControllerNode::set_use_ghost_sweep_test(bool value);

857 12 is_on_ground 0 4 1533 43 BulletCharacterControllerNode::is_on_ground 0 1 99 10
/**
 *
 */
61
bool BulletCharacterControllerNode::is_on_ground(void) const;

858 8 can_jump 0 4 1533 39 BulletCharacterControllerNode::can_jump 0 1 100 10
/**
 *
 */
57
bool BulletCharacterControllerNode::can_jump(void) const;

859 7 do_jump 0 4 1533 38 BulletCharacterControllerNode::do_jump 0 1 101 10
/**
 *
 */
50
void BulletCharacterControllerNode::do_jump(void);

860 14 get_class_type 0 4 1533 45 BulletCharacterControllerNode::get_class_type 0 1 102 0
70
static TypeHandle BulletCharacterControllerNode::get_class_type(void);

861 34 upcast_to_ClosestRayResultCallback 0 12 1534 61 BulletClosestHitRayResult::upcast_to_ClosestRayResultCallback 0 1 114 65
upcast from BulletClosestHitRayResult to ClosestRayResultCallback
112
btCollisionWorld::ClosestRayResultCallback *BulletClosestHitRayResult::upcast_to_ClosestRayResultCallback(void);

862 62 downcast_ClosestRayResultCallback_to_BulletClosestHitRayResult 0 8 0 62 downcast_ClosestRayResultCallback_to_BulletClosestHitRayResult 0 0 67
downcast from ClosestRayResultCallback to BulletClosestHitRayResult
140
BulletClosestHitRayResult *downcast_ClosestRayResultCallback_to_BulletClosestHitRayResult(btCollisionWorld::ClosestRayResultCallback *this);

863 5 empty 0 4 1534 32 BulletClosestHitRayResult::empty 0 1 104 101
/**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */
79
static inline BulletClosestHitRayResult BulletClosestHitRayResult::empty(void);

864 12 get_from_pos 0 4 1534 39 BulletClosestHitRayResult::get_from_pos 0 1 105 10
/**
 *
 */
60
LPoint3 BulletClosestHitRayResult::get_from_pos(void) const;

865 10 get_to_pos 0 4 1534 37 BulletClosestHitRayResult::get_to_pos 0 1 106 10
/**
 *
 */
58
LPoint3 BulletClosestHitRayResult::get_to_pos(void) const;

866 7 has_hit 0 4 1534 34 BulletClosestHitRayResult::has_hit 0 1 107 10
/**
 *
 */
52
bool BulletClosestHitRayResult::has_hit(void) const;

867 8 get_node 0 4 1534 35 BulletClosestHitRayResult::get_node 0 1 108 10
/**
 *
 */
59
PandaNode *BulletClosestHitRayResult::get_node(void) const;

868 11 get_hit_pos 0 4 1534 38 BulletClosestHitRayResult::get_hit_pos 0 1 109 10
/**
 *
 */
59
LPoint3 BulletClosestHitRayResult::get_hit_pos(void) const;

869 14 get_hit_normal 0 4 1534 41 BulletClosestHitRayResult::get_hit_normal 0 1 110 10
/**
 *
 */
63
LVector3 BulletClosestHitRayResult::get_hit_normal(void) const;

870 16 get_hit_fraction 0 4 1534 43 BulletClosestHitRayResult::get_hit_fraction 0 1 111 10
/**
 *
 */
68
PN_stdfloat BulletClosestHitRayResult::get_hit_fraction(void) const;

871 14 get_shape_part 0 4 1534 41 BulletClosestHitRayResult::get_shape_part 0 1 112 10
/**
 *
 */
58
int BulletClosestHitRayResult::get_shape_part(void) const;

872 18 get_triangle_index 0 4 1534 45 BulletClosestHitRayResult::get_triangle_index 0 1 113 10
/**
 *
 */
62
int BulletClosestHitRayResult::get_triangle_index(void) const;

873 25 BulletClosestHitRayResult 0 260 1534 52 BulletClosestHitRayResult::BulletClosestHitRayResult 0 1 103 10
/**
 *
 */
105
inline BulletClosestHitRayResult::BulletClosestHitRayResult(BulletClosestHitRayResult const &) = default;

874 26 ~BulletClosestHitRayResult 0 516 1534 53 BulletClosestHitRayResult::~BulletClosestHitRayResult 0 0 0
60
BulletClosestHitRayResult::~BulletClosestHitRayResult(void);

875 37 upcast_to_ClosestConvexResultCallback 0 12 1536 66 BulletClosestHitSweepResult::upcast_to_ClosestConvexResultCallback 0 1 124 70
upcast from BulletClosestHitSweepResult to ClosestConvexResultCallback
120
btCollisionWorld::ClosestConvexResultCallback *BulletClosestHitSweepResult::upcast_to_ClosestConvexResultCallback(void);

876 67 downcast_ClosestConvexResultCallback_to_BulletClosestHitSweepResult 0 8 0 67 downcast_ClosestConvexResultCallback_to_BulletClosestHitSweepResult 0 0 72
downcast from ClosestConvexResultCallback to BulletClosestHitSweepResult
150
BulletClosestHitSweepResult *downcast_ClosestConvexResultCallback_to_BulletClosestHitSweepResult(btCollisionWorld::ClosestConvexResultCallback *this);

877 5 empty 0 4 1536 34 BulletClosestHitSweepResult::empty 0 1 116 101
/**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */
83
static inline BulletClosestHitSweepResult BulletClosestHitSweepResult::empty(void);

878 12 get_from_pos 0 4 1536 41 BulletClosestHitSweepResult::get_from_pos 0 1 117 10
/**
 *
 */
62
LPoint3 BulletClosestHitSweepResult::get_from_pos(void) const;

879 10 get_to_pos 0 4 1536 39 BulletClosestHitSweepResult::get_to_pos 0 1 118 10
/**
 *
 */
60
LPoint3 BulletClosestHitSweepResult::get_to_pos(void) const;

880 7 has_hit 0 4 1536 36 BulletClosestHitSweepResult::has_hit 0 1 119 10
/**
 *
 */
54
bool BulletClosestHitSweepResult::has_hit(void) const;

881 8 get_node 0 4 1536 37 BulletClosestHitSweepResult::get_node 0 1 120 10
/**
 *
 */
61
PandaNode *BulletClosestHitSweepResult::get_node(void) const;

882 11 get_hit_pos 0 4 1536 40 BulletClosestHitSweepResult::get_hit_pos 0 1 121 10
/**
 *
 */
61
LPoint3 BulletClosestHitSweepResult::get_hit_pos(void) const;

883 14 get_hit_normal 0 4 1536 43 BulletClosestHitSweepResult::get_hit_normal 0 1 122 10
/**
 *
 */
65
LVector3 BulletClosestHitSweepResult::get_hit_normal(void) const;

884 16 get_hit_fraction 0 4 1536 45 BulletClosestHitSweepResult::get_hit_fraction 0 1 123 10
/**
 *
 */
70
PN_stdfloat BulletClosestHitSweepResult::get_hit_fraction(void) const;

885 27 BulletClosestHitSweepResult 0 260 1536 56 BulletClosestHitSweepResult::BulletClosestHitSweepResult 0 1 115 10
/**
 *
 */
111
inline BulletClosestHitSweepResult::BulletClosestHitSweepResult(BulletClosestHitSweepResult const &) = default;

886 28 ~BulletClosestHitSweepResult 0 516 1536 57 BulletClosestHitSweepResult::~BulletClosestHitSweepResult 0 0 0
64
BulletClosestHitSweepResult::~BulletClosestHitSweepResult(void);

887 15 BulletConeShape 0 260 1538 32 BulletConeShape::BulletConeShape 0 2 125 126 62
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */
171
explicit BulletConeShape::BulletConeShape(PN_stdfloat radius, PN_stdfloat height, BulletUpAxis up = ::Z_up);
BulletConeShape::BulletConeShape(BulletConeShape const &copy);

888 10 get_radius 0 4 1538 27 BulletConeShape::get_radius 0 1 127 67
/**
 * Returns the radius that was passed into the constructor.
 */
59
inline PN_stdfloat BulletConeShape::get_radius(void) const;

889 10 get_height 0 4 1538 27 BulletConeShape::get_height 0 1 128 67
/**
 * Returns the height that was passed into the constructor.
 */
59
inline PN_stdfloat BulletConeShape::get_height(void) const;

890 14 get_class_type 0 4 1538 31 BulletConeShape::get_class_type 0 1 129 0
56
static TypeHandle BulletConeShape::get_class_type(void);

891 16 get_rigid_body_a 0 4 1539 34 BulletConstraint::get_rigid_body_a 0 1 130 10
/**
 *
 */
62
BulletRigidBodyNode *BulletConstraint::get_rigid_body_a(void);

892 16 get_rigid_body_b 0 4 1539 34 BulletConstraint::get_rigid_body_b 0 1 131 10
/**
 *
 */
62
BulletRigidBodyNode *BulletConstraint::get_rigid_body_b(void);

893 15 enable_feedback 0 4 1539 33 BulletConstraint::enable_feedback 0 1 132 10
/**
 *
 */
51
void BulletConstraint::enable_feedback(bool value);

894 19 set_debug_draw_size 0 4 1539 37 BulletConstraint::set_debug_draw_size 0 1 133 10
/**
 *
 */
61
void BulletConstraint::set_debug_draw_size(PN_stdfloat size);

895 19 get_debug_draw_size 0 4 1539 37 BulletConstraint::get_debug_draw_size 0 1 134 10
/**
 *
 */
56
PN_stdfloat BulletConstraint::get_debug_draw_size(void);

896 19 get_applied_impulse 0 4 1539 37 BulletConstraint::get_applied_impulse 0 1 135 10
/**
 *
 */
62
PN_stdfloat BulletConstraint::get_applied_impulse(void) const;

897 22 set_breaking_threshold 0 4 1539 40 BulletConstraint::set_breaking_threshold 0 1 136 212
/**
 * Sets the applied impulse limit for breaking the constraint.  If the limit
 * is exceeded the constraint will be disabled.  Disabled constraints are not
 * removed from the world, and can be re-enabled.
 */
76
inline void BulletConstraint::set_breaking_threshold(PN_stdfloat threshold);

898 22 get_breaking_threshold 0 4 1539 40 BulletConstraint::get_breaking_threshold 0 1 137 73
/**
 * Returns the applied impluse limit for breaking the constraint.
 */
72
inline PN_stdfloat BulletConstraint::get_breaking_threshold(void) const;

899 11 set_enabled 0 4 1539 29 BulletConstraint::set_enabled 0 1 138 10
/**
 *
 */
56
inline void BulletConstraint::set_enabled(bool enabled);

900 10 is_enabled 0 4 1539 28 BulletConstraint::is_enabled 0 1 139 53
/**
 * Returns TRUE if the constraint is enabled.
 */
53
inline bool BulletConstraint::is_enabled(void) const;

901 9 set_param 0 4 1539 27 BulletConstraint::set_param 0 1 140 10
/**
 *
 */
106
void BulletConstraint::set_param(BulletConstraint::ConstraintParam num, PN_stdfloat value, int axis = -1);

902 9 get_param 0 4 1539 27 BulletConstraint::get_param 0 1 141 10
/**
 *
 */
94
PN_stdfloat BulletConstraint::get_param(BulletConstraint::ConstraintParam num, int axis = -1);

903 19 BulletRigidBodyNode 0 260 1543 40 BulletRigidBodyNode::BulletRigidBodyNode 0 1 143 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
78
explicit BulletRigidBodyNode::BulletRigidBodyNode(char const *name = "rigid");

904 8 set_mass 0 4 1543 29 BulletRigidBodyNode::set_mass 0 1 144 295
// Mass & inertia

// Mass & inertia

/**
 * Sets the mass of a rigid body.  This also modifies the inertia, which is
 * automatically computed from the shape of the body.  Setting a value of zero
 * for mass will make the body static.  A value of zero can be considered an
 * infinite mass.
 */
53
void BulletRigidBodyNode::set_mass(PN_stdfloat mass);

905 8 get_mass 0 4 1543 29 BulletRigidBodyNode::get_mass 0 1 145 128
/**
 * Returns the total mass of a rigid body.  A value of zero means that the
 * body is staic, i.e.  has an infinite mass.
 */
54
PN_stdfloat BulletRigidBodyNode::get_mass(void) const;

906 12 get_inv_mass 0 4 1543 33 BulletRigidBodyNode::get_inv_mass 0 1 146 52
/**
 * Returns the inverse mass of a rigid body.
 */
58
PN_stdfloat BulletRigidBodyNode::get_inv_mass(void) const;

907 11 set_inertia 0 4 1543 32 BulletRigidBodyNode::set_inertia 0 1 147 591
/**
 * Sets the inertia of a rigid body.  Inertia is given as a three-component
 * vector.  A component value of zero means infinite inertia along this
 * direction.  Setting the intertia will override the value which is
 * automatically calculated from the rigid bodies shape.  However, it is
 * possible that automatic calculation of intertia is trigger after calling
 * this method, and thus overwriting the explicitly set value again.  This
 * happens when: (a) the mass is set after the inertia.  (b) a shape is added
 * or removed from the body.  (c) the scale of the body changed.
 */
64
void BulletRigidBodyNode::set_inertia(LVecBase3 const &inertia);

908 11 get_inertia 0 4 1543 32 BulletRigidBodyNode::get_inertia 0 1 148 174
/**
 * Returns the inertia of the rigid body.  Inertia is given as a three
 * component vector.  A component value of zero means infinite inertia along
 * this direction.
 */
54
LVector3 BulletRigidBodyNode::get_inertia(void) const;

909 26 get_inv_inertia_diag_local 0 4 1543 47 BulletRigidBodyNode::get_inv_inertia_diag_local 0 1 149 10
/**
 *
 */
69
LVector3 BulletRigidBodyNode::get_inv_inertia_diag_local(void) const;

910 28 get_inv_inertia_tensor_world 0 4 1543 49 BulletRigidBodyNode::get_inv_inertia_tensor_world 0 1 150 10
/**
 *
 */
71
LMatrix3 BulletRigidBodyNode::get_inv_inertia_tensor_world(void) const;

911 19 get_linear_velocity 0 4 1543 40 BulletRigidBodyNode::get_linear_velocity 0 1 151 36
// Velocity

// Velocity

/**
 *
 */
62
LVector3 BulletRigidBodyNode::get_linear_velocity(void) const;

912 20 get_angular_velocity 0 4 1543 41 BulletRigidBodyNode::get_angular_velocity 0 1 152 10
/**
 *
 */
63
LVector3 BulletRigidBodyNode::get_angular_velocity(void) const;

913 19 set_linear_velocity 0 4 1543 40 BulletRigidBodyNode::set_linear_velocity 0 1 153 10
/**
 *
 */
72
void BulletRigidBodyNode::set_linear_velocity(LVector3 const &velocity);

914 20 set_angular_velocity 0 4 1543 41 BulletRigidBodyNode::set_angular_velocity 0 1 154 10
/**
 *
 */
73
void BulletRigidBodyNode::set_angular_velocity(LVector3 const &velocity);

915 18 get_linear_damping 0 4 1543 39 BulletRigidBodyNode::get_linear_damping 0 1 155 34
// Damping

// Damping

/**
 *
 */
64
PN_stdfloat BulletRigidBodyNode::get_linear_damping(void) const;

916 19 get_angular_damping 0 4 1543 40 BulletRigidBodyNode::get_angular_damping 0 1 156 10
/**
 *
 */
65
PN_stdfloat BulletRigidBodyNode::get_angular_damping(void) const;

917 18 set_linear_damping 0 4 1543 39 BulletRigidBodyNode::set_linear_damping 0 1 157 10
/**
 *
 */
64
void BulletRigidBodyNode::set_linear_damping(PN_stdfloat value);

918 19 set_angular_damping 0 4 1543 40 BulletRigidBodyNode::set_angular_damping 0 1 158 10
/**
 *
 */
65
void BulletRigidBodyNode::set_angular_damping(PN_stdfloat value);

919 12 clear_forces 0 4 1543 33 BulletRigidBodyNode::clear_forces 0 1 159 21
// Forces

/**
 *
 */
45
void BulletRigidBodyNode::clear_forces(void);

920 11 apply_force 0 4 1543 32 BulletRigidBodyNode::apply_force 0 1 160 10
/**
 *
 */
81
void BulletRigidBodyNode::apply_force(LVector3 const &force, LPoint3 const &pos);

921 19 apply_central_force 0 4 1543 40 BulletRigidBodyNode::apply_central_force 0 1 161 10
/**
 *
 */
69
void BulletRigidBodyNode::apply_central_force(LVector3 const &force);

922 13 apply_impulse 0 4 1543 34 BulletRigidBodyNode::apply_impulse 0 1 162 10
/**
 *
 */
85
void BulletRigidBodyNode::apply_impulse(LVector3 const &impulse, LPoint3 const &pos);

923 21 apply_central_impulse 0 4 1543 42 BulletRigidBodyNode::apply_central_impulse 0 1 163 10
/**
 *
 */
73
void BulletRigidBodyNode::apply_central_impulse(LVector3 const &impulse);

924 12 apply_torque 0 4 1543 33 BulletRigidBodyNode::apply_torque 0 1 164 10
/**
 *
 */
63
void BulletRigidBodyNode::apply_torque(LVector3 const &torque);

925 20 apply_torque_impulse 0 4 1543 41 BulletRigidBodyNode::apply_torque_impulse 0 1 165 10
/**
 *
 */
71
void BulletRigidBodyNode::apply_torque_impulse(LVector3 const &torque);

926 15 get_total_force 0 4 1543 36 BulletRigidBodyNode::get_total_force 0 1 166 10
/**
 *
 */
58
LVector3 BulletRigidBodyNode::get_total_force(void) const;

927 16 get_total_torque 0 4 1543 37 BulletRigidBodyNode::get_total_torque 0 1 167 10
/**
 *
 */
59
LVector3 BulletRigidBodyNode::get_total_torque(void) const;

928 26 get_linear_sleep_threshold 0 4 1543 47 BulletRigidBodyNode::get_linear_sleep_threshold 0 1 168 66
// Deactivation thresholds

// Deactivation thresholds

/**
 *
 */
72
PN_stdfloat BulletRigidBodyNode::get_linear_sleep_threshold(void) const;

929 27 get_angular_sleep_threshold 0 4 1543 48 BulletRigidBodyNode::get_angular_sleep_threshold 0 1 169 10
/**
 *
 */
73
PN_stdfloat BulletRigidBodyNode::get_angular_sleep_threshold(void) const;

930 26 set_linear_sleep_threshold 0 4 1543 47 BulletRigidBodyNode::set_linear_sleep_threshold 0 1 170 10
/**
 *
 */
76
void BulletRigidBodyNode::set_linear_sleep_threshold(PN_stdfloat threshold);

931 27 set_angular_sleep_threshold 0 4 1543 48 BulletRigidBodyNode::set_angular_sleep_threshold 0 1 171 10
/**
 *
 */
77
void BulletRigidBodyNode::set_angular_sleep_threshold(PN_stdfloat threshold);

932 11 set_gravity 0 4 1543 32 BulletRigidBodyNode::set_gravity 0 1 172 34
// Gravity

// Gravity

/**
 *
 */
63
void BulletRigidBodyNode::set_gravity(LVector3 const &gravity);

933 11 get_gravity 0 4 1543 32 BulletRigidBodyNode::get_gravity 0 1 173 10
/**
 *
 */
54
LVector3 BulletRigidBodyNode::get_gravity(void) const;

934 17 get_linear_factor 0 4 1543 38 BulletRigidBodyNode::get_linear_factor 0 1 174 54
// Restrict movement

// Restrict movement

/**
 *
 */
60
LVector3 BulletRigidBodyNode::get_linear_factor(void) const;

935 18 get_angular_factor 0 4 1543 39 BulletRigidBodyNode::get_angular_factor 0 1 175 10
/**
 *
 */
61
LVector3 BulletRigidBodyNode::get_angular_factor(void) const;

936 17 set_linear_factor 0 4 1543 38 BulletRigidBodyNode::set_linear_factor 0 1 176 10
/**
 *
 */
68
void BulletRigidBodyNode::set_linear_factor(LVector3 const &factor);

937 18 set_angular_factor 0 4 1543 39 BulletRigidBodyNode::set_angular_factor 0 1 177 10
/**
 *
 */
69
void BulletRigidBodyNode::set_angular_factor(LVector3 const &factor);

938 15 pick_dirty_flag 0 4 1543 36 BulletRigidBodyNode::pick_dirty_flag 0 1 178 135
// Special

/**
 * Returns TRUE if the transform of the rigid body has changed at least once
 * since the last call to this method.
 */
48
bool BulletRigidBodyNode::pick_dirty_flag(void);

939 14 get_class_type 0 4 1543 35 BulletRigidBodyNode::get_class_type 0 1 179 0
60
static TypeHandle BulletRigidBodyNode::get_class_type(void);

940 14 get_class_type 0 4 1539 32 BulletConstraint::get_class_type 0 1 142 0
57
static TypeHandle BulletConstraint::get_class_type(void);

941 25 BulletConeTwistConstraint 0 260 1546 52 BulletConeTwistConstraint::BulletConeTwistConstraint 0 2 180 181 22
/**
 *
 */

/**
 *
 */
323
explicit BulletConeTwistConstraint::BulletConeTwistConstraint(BulletRigidBodyNode const *node_a, TransformState const *frame_a);
explicit BulletConeTwistConstraint::BulletConeTwistConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, TransformState const *frame_a, TransformState const *frame_b);

942 9 set_limit 0 4 1546 36 BulletConeTwistConstraint::set_limit 0 2 182 183 22
/**
 *
 */

/**
 *
 */
272
void BulletConeTwistConstraint::set_limit(int index, PN_stdfloat value);
void BulletConeTwistConstraint::set_limit(PN_stdfloat swing1, PN_stdfloat swing2, PN_stdfloat twist, PN_stdfloat softness = 1.0, PN_stdfloat bias = 0.30000000000000007, PN_stdfloat relaxation = 1.0);

943 11 set_damping 0 4 1546 38 BulletConeTwistConstraint::set_damping 0 1 184 10
/**
 *
 */
65
void BulletConeTwistConstraint::set_damping(PN_stdfloat damping);

944 17 get_fix_threshold 0 4 1546 44 BulletConeTwistConstraint::get_fix_threshold 0 1 185 10
/**
 *
 */
69
PN_stdfloat BulletConeTwistConstraint::get_fix_threshold(void) const;

945 17 set_fix_threshold 0 4 1546 44 BulletConeTwistConstraint::set_fix_threshold 0 1 186 10
/**
 *
 */
73
void BulletConeTwistConstraint::set_fix_threshold(PN_stdfloat threshold);

946 12 enable_motor 0 4 1546 39 BulletConeTwistConstraint::enable_motor 0 1 187 10
/**
 *
 */
58
void BulletConeTwistConstraint::enable_motor(bool enable);

947 21 set_max_motor_impulse 0 4 1546 48 BulletConeTwistConstraint::set_max_motor_impulse 0 1 188 10
/**
 *
 */
79
void BulletConeTwistConstraint::set_max_motor_impulse(PN_stdfloat max_impulse);

948 32 set_max_motor_impulse_normalized 0 4 1546 59 BulletConeTwistConstraint::set_max_motor_impulse_normalized 0 1 189 10
/**
 *
 */
90
void BulletConeTwistConstraint::set_max_motor_impulse_normalized(PN_stdfloat max_impulse);

949 16 set_motor_target 0 4 1546 43 BulletConeTwistConstraint::set_motor_target 0 1 190 10
/**
 *
 */
74
void BulletConeTwistConstraint::set_motor_target(LQuaternion const &quat);

950 36 set_motor_target_in_constraint_space 0 4 1546 63 BulletConeTwistConstraint::set_motor_target_in_constraint_space 0 1 191 10
/**
 *
 */
94
void BulletConeTwistConstraint::set_motor_target_in_constraint_space(LQuaternion const &quat);

951 10 set_frames 0 4 1546 37 BulletConeTwistConstraint::set_frames 0 1 192 10
/**
 *
 */
99
void BulletConeTwistConstraint::set_frames(TransformState const *ts_a, TransformState const *ts_b);

952 11 get_frame_a 0 4 1546 38 BulletConeTwistConstraint::get_frame_a 0 1 193 10
/**
 *
 */
84
ConstPointerTo< TransformState > BulletConeTwistConstraint::get_frame_a(void) const;

953 11 get_frame_b 0 4 1546 38 BulletConeTwistConstraint::get_frame_b 0 1 194 10
/**
 *
 */
84
ConstPointerTo< TransformState > BulletConeTwistConstraint::get_frame_b(void) const;

954 14 get_class_type 0 4 1546 41 BulletConeTwistConstraint::get_class_type 0 1 195 0
66
static TypeHandle BulletConeTwistConstraint::get_class_type(void);

955 20 ~BulletManifoldPoint 0 516 1547 41 BulletManifoldPoint::~BulletManifoldPoint 0 0 10
/**
 *
 */
55
inline BulletManifoldPoint::~BulletManifoldPoint(void);

956 13 get_life_time 0 4 1547 34 BulletManifoldPoint::get_life_time 0 1 196 10
/**
 *
 */
51
int BulletManifoldPoint::get_life_time(void) const;

957 12 get_distance 0 4 1547 33 BulletManifoldPoint::get_distance 0 1 197 10
/**
 *
 */
58
PN_stdfloat BulletManifoldPoint::get_distance(void) const;

958 19 get_applied_impulse 0 4 1547 40 BulletManifoldPoint::get_applied_impulse 0 1 198 10
/**
 *
 */
65
PN_stdfloat BulletManifoldPoint::get_applied_impulse(void) const;

959 23 get_position_world_on_a 0 4 1547 44 BulletManifoldPoint::get_position_world_on_a 0 1 199 10
/**
 *
 */
65
LPoint3 BulletManifoldPoint::get_position_world_on_a(void) const;

960 23 get_position_world_on_b 0 4 1547 44 BulletManifoldPoint::get_position_world_on_b 0 1 200 10
/**
 *
 */
65
LPoint3 BulletManifoldPoint::get_position_world_on_b(void) const;

961 21 get_normal_world_on_b 0 4 1547 42 BulletManifoldPoint::get_normal_world_on_b 0 1 201 10
/**
 *
 */
64
LVector3 BulletManifoldPoint::get_normal_world_on_b(void) const;

962 17 get_local_point_a 0 4 1547 38 BulletManifoldPoint::get_local_point_a 0 1 202 10
/**
 *
 */
59
LPoint3 BulletManifoldPoint::get_local_point_a(void) const;

963 17 get_local_point_b 0 4 1547 38 BulletManifoldPoint::get_local_point_b 0 1 203 10
/**
 *
 */
59
LPoint3 BulletManifoldPoint::get_local_point_b(void) const;

964 12 get_part_id0 0 4 1547 33 BulletManifoldPoint::get_part_id0 0 1 204 10
/**
 *
 */
50
int BulletManifoldPoint::get_part_id0(void) const;

965 12 get_part_id1 0 4 1547 33 BulletManifoldPoint::get_part_id1 0 1 205 10
/**
 *
 */
50
int BulletManifoldPoint::get_part_id1(void) const;

966 10 get_index0 0 4 1547 31 BulletManifoldPoint::get_index0 0 1 206 10
/**
 *
 */
48
int BulletManifoldPoint::get_index0(void) const;

967 10 get_index1 0 4 1547 31 BulletManifoldPoint::get_index1 0 1 207 10
/**
 *
 */
48
int BulletManifoldPoint::get_index1(void) const;

968 32 set_lateral_friction_initialized 0 4 1547 53 BulletManifoldPoint::set_lateral_friction_initialized 0 1 208 10
/**
 *
 */
71
void BulletManifoldPoint::set_lateral_friction_initialized(bool value);

969 25 set_lateral_friction_dir1 0 4 1547 46 BulletManifoldPoint::set_lateral_friction_dir1 0 1 209 10
/**
 *
 */
74
void BulletManifoldPoint::set_lateral_friction_dir1(LVecBase3 const &dir);

970 25 set_lateral_friction_dir2 0 4 1547 46 BulletManifoldPoint::set_lateral_friction_dir2 0 1 210 10
/**
 *
 */
74
void BulletManifoldPoint::set_lateral_friction_dir2(LVecBase3 const &dir);

971 19 set_contact_motion1 0 4 1547 40 BulletManifoldPoint::set_contact_motion1 0 1 211 10
/**
 *
 */
65
void BulletManifoldPoint::set_contact_motion1(PN_stdfloat value);

972 19 set_contact_motion2 0 4 1547 40 BulletManifoldPoint::set_contact_motion2 0 1 212 10
/**
 *
 */
65
void BulletManifoldPoint::set_contact_motion2(PN_stdfloat value);

973 21 set_combined_friction 0 4 1547 42 BulletManifoldPoint::set_combined_friction 0 1 213 10
/**
 *
 */
67
void BulletManifoldPoint::set_combined_friction(PN_stdfloat value);

974 24 set_combined_restitution 0 4 1547 45 BulletManifoldPoint::set_combined_restitution 0 1 214 10
/**
 *
 */
70
void BulletManifoldPoint::set_combined_restitution(PN_stdfloat value);

975 19 set_applied_impulse 0 4 1547 40 BulletManifoldPoint::set_applied_impulse 0 1 215 10
/**
 *
 */
65
void BulletManifoldPoint::set_applied_impulse(PN_stdfloat value);

976 28 set_applied_impulse_lateral1 0 4 1547 49 BulletManifoldPoint::set_applied_impulse_lateral1 0 1 216 10
/**
 *
 */
74
void BulletManifoldPoint::set_applied_impulse_lateral1(PN_stdfloat value);

977 28 set_applied_impulse_lateral2 0 4 1547 49 BulletManifoldPoint::set_applied_impulse_lateral2 0 1 217 10
/**
 *
 */
74
void BulletManifoldPoint::set_applied_impulse_lateral2(PN_stdfloat value);

978 16 set_contact_cfm1 0 4 1547 37 BulletManifoldPoint::set_contact_cfm1 0 1 218 10
/**
 *
 */
62
void BulletManifoldPoint::set_contact_cfm1(PN_stdfloat value);

979 16 set_contact_cfm2 0 4 1547 37 BulletManifoldPoint::set_contact_cfm2 0 1 219 10
/**
 *
 */
62
void BulletManifoldPoint::set_contact_cfm2(PN_stdfloat value);

980 32 get_lateral_friction_initialized 0 4 1547 53 BulletManifoldPoint::get_lateral_friction_initialized 0 1 220 10
/**
 *
 */
71
bool BulletManifoldPoint::get_lateral_friction_initialized(void) const;

981 25 get_lateral_friction_dir1 0 4 1547 46 BulletManifoldPoint::get_lateral_friction_dir1 0 1 221 10
/**
 *
 */
68
LVector3 BulletManifoldPoint::get_lateral_friction_dir1(void) const;

982 25 get_lateral_friction_dir2 0 4 1547 46 BulletManifoldPoint::get_lateral_friction_dir2 0 1 222 10
/**
 *
 */
68
LVector3 BulletManifoldPoint::get_lateral_friction_dir2(void) const;

983 19 get_contact_motion1 0 4 1547 40 BulletManifoldPoint::get_contact_motion1 0 1 223 10
/**
 *
 */
65
PN_stdfloat BulletManifoldPoint::get_contact_motion1(void) const;

984 19 get_contact_motion2 0 4 1547 40 BulletManifoldPoint::get_contact_motion2 0 1 224 10
/**
 *
 */
65
PN_stdfloat BulletManifoldPoint::get_contact_motion2(void) const;

985 21 get_combined_friction 0 4 1547 42 BulletManifoldPoint::get_combined_friction 0 1 225 10
/**
 *
 */
67
PN_stdfloat BulletManifoldPoint::get_combined_friction(void) const;

986 24 get_combined_restitution 0 4 1547 45 BulletManifoldPoint::get_combined_restitution 0 1 226 10
/**
 *
 */
70
PN_stdfloat BulletManifoldPoint::get_combined_restitution(void) const;

987 28 get_applied_impulse_lateral1 0 4 1547 49 BulletManifoldPoint::get_applied_impulse_lateral1 0 1 227 10
/**
 *
 */
74
PN_stdfloat BulletManifoldPoint::get_applied_impulse_lateral1(void) const;

988 28 get_applied_impulse_lateral2 0 4 1547 49 BulletManifoldPoint::get_applied_impulse_lateral2 0 1 228 10
/**
 *
 */
74
PN_stdfloat BulletManifoldPoint::get_applied_impulse_lateral2(void) const;

989 16 get_contact_cfm1 0 4 1547 37 BulletManifoldPoint::get_contact_cfm1 0 1 229 10
/**
 *
 */
62
PN_stdfloat BulletManifoldPoint::get_contact_cfm1(void) const;

990 16 get_contact_cfm2 0 4 1547 37 BulletManifoldPoint::get_contact_cfm2 0 1 230 10
/**
 *
 */
62
PN_stdfloat BulletManifoldPoint::get_contact_cfm2(void) const;

991 25 BulletContactCallbackData 0 260 1548 52 BulletContactCallbackData::BulletContactCallbackData 0 1 231 10
/**
 *
 */
163
inline BulletContactCallbackData::BulletContactCallbackData(BulletManifoldPoint &mp, PandaNode *node0, PandaNode *node1, int id0, int id1, int index0, int index1);

992 12 get_manifold 0 4 1548 39 BulletContactCallbackData::get_manifold 0 1 232 10
/**
 *
 */
80
inline BulletManifoldPoint &BulletContactCallbackData::get_manifold(void) const;

993 9 get_node0 0 4 1548 36 BulletContactCallbackData::get_node0 0 1 233 10
/**
 *
 */
67
inline PandaNode *BulletContactCallbackData::get_node0(void) const;

994 9 get_node1 0 4 1548 36 BulletContactCallbackData::get_node1 0 1 234 10
/**
 *
 */
67
inline PandaNode *BulletContactCallbackData::get_node1(void) const;

995 12 get_part_id0 0 4 1548 39 BulletContactCallbackData::get_part_id0 0 1 235 10
/**
 *
 */
63
inline int BulletContactCallbackData::get_part_id0(void) const;

996 12 get_part_id1 0 4 1548 39 BulletContactCallbackData::get_part_id1 0 1 236 10
/**
 *
 */
63
inline int BulletContactCallbackData::get_part_id1(void) const;

997 10 get_index0 0 4 1548 37 BulletContactCallbackData::get_index0 0 1 237 10
/**
 *
 */
61
inline int BulletContactCallbackData::get_index0(void) const;

998 10 get_index1 0 4 1548 37 BulletContactCallbackData::get_index1 0 1 238 10
/**
 *
 */
61
inline int BulletContactCallbackData::get_index1(void) const;

999 14 get_class_type 0 4 1548 41 BulletContactCallbackData::get_class_type 0 1 239 0
66
static TypeHandle BulletContactCallbackData::get_class_type(void);

1000 18 get_manifold_point 0 4 1550 33 BulletContact::get_manifold_point 0 1 240 10
/**
 *
 */
68
inline BulletManifoldPoint &BulletContact::get_manifold_point(void);

1001 9 get_node0 0 4 1550 24 BulletContact::get_node0 0 1 241 10
/**
 *
 */
55
inline PandaNode *BulletContact::get_node0(void) const;

1002 9 get_node1 0 4 1550 24 BulletContact::get_node1 0 1 242 10
/**
 *
 */
55
inline PandaNode *BulletContact::get_node1(void) const;

1003 8 get_idx0 0 4 1550 23 BulletContact::get_idx0 0 1 243 10
/**
 *
 */
47
inline int BulletContact::get_idx0(void) const;

1004 8 get_idx1 0 4 1550 23 BulletContact::get_idx1 0 1 244 10
/**
 *
 */
47
inline int BulletContact::get_idx1(void) const;

1005 12 get_part_id0 0 4 1550 27 BulletContact::get_part_id0 0 1 245 10
/**
 *
 */
51
inline int BulletContact::get_part_id0(void) const;

1006 12 get_part_id1 0 4 1550 27 BulletContact::get_part_id1 0 1 246 10
/**
 *
 */
51
inline int BulletContact::get_part_id1(void) const;

1007 14 ~BulletContact 0 516 1550 29 BulletContact::~BulletContact 0 0 0
36
BulletContact::~BulletContact(void);

1008 31 upcast_to_ContactResultCallback 0 12 1551 52 BulletContactResult::upcast_to_ContactResultCallback 0 1 249 56
upcast from BulletContactResult to ContactResultCallback
100
btCollisionWorld::ContactResultCallback *BulletContactResult::upcast_to_ContactResultCallback(void);

1009 53 downcast_ContactResultCallback_to_BulletContactResult 0 8 0 53 downcast_ContactResultCallback_to_BulletContactResult 0 0 58
downcast from ContactResultCallback to BulletContactResult
122
BulletContactResult *downcast_ContactResultCallback_to_BulletContactResult(btCollisionWorld::ContactResultCallback *this);

1010 16 get_num_contacts 0 4 1551 37 BulletContactResult::get_num_contacts 0 1 247 10
/**
 *
 */
61
inline int BulletContactResult::get_num_contacts(void) const;

1011 11 get_contact 0 4 1551 32 BulletContactResult::get_contact 0 1 248 10
/**
 *
 */
63
inline BulletContact BulletContactResult::get_contact(int idx);

1012 20 ~BulletContactResult 0 516 1551 41 BulletContactResult::~BulletContactResult 0 0 0
48
BulletContactResult::~BulletContactResult(void);

1013 15 BulletDebugNode 0 260 1553 32 BulletDebugNode::BulletDebugNode 0 2 250 251 10
/**
 *
 */
146
explicit BulletDebugNode::BulletDebugNode(char const *name = "debug");
inline BulletDebugNode::BulletDebugNode(BulletDebugNode const &) = default;

1014 17 draw_mask_changed 0 6 1553 34 BulletDebugNode::draw_mask_changed 0 1 252 10
/**
 *
 */
54
virtual void BulletDebugNode::draw_mask_changed(void);

1015 14 show_wireframe 0 4 1553 31 BulletDebugNode::show_wireframe 0 1 253 64
/**
 * If true, displays collision shapes in wireframe mode.
 */
55
inline void BulletDebugNode::show_wireframe(bool show);

1016 16 show_constraints 0 4 1553 33 BulletDebugNode::show_constraints 0 1 254 102
/**
 * If true, display limits defined for constraints, e.g. a pivot axis or maximum
 * amplitude.
 */
57
inline void BulletDebugNode::show_constraints(bool show);

1017 19 show_bounding_boxes 0 4 1553 36 BulletDebugNode::show_bounding_boxes 0 1 255 69
/**
 * If true, displays axis aligned bounding boxes for objects.
 */
60
inline void BulletDebugNode::show_bounding_boxes(bool show);

1018 12 show_normals 0 4 1553 29 BulletDebugNode::show_normals 0 1 256 84
/**
 * If true, displays normal vectors for triangle mesh and heightfield faces.
 */
53
inline void BulletDebugNode::show_normals(bool show);

1019 18 get_show_wireframe 0 4 1553 35 BulletDebugNode::get_show_wireframe 0 1 257 10
/**
 *
 */
60
inline bool BulletDebugNode::get_show_wireframe(void) const;

1020 20 get_show_constraints 0 4 1553 37 BulletDebugNode::get_show_constraints 0 1 258 10
/**
 *
 */
62
inline bool BulletDebugNode::get_show_constraints(void) const;

1021 23 get_show_bounding_boxes 0 4 1553 40 BulletDebugNode::get_show_bounding_boxes 0 1 259 10
/**
 *
 */
65
inline bool BulletDebugNode::get_show_bounding_boxes(void) const;

1022 16 get_show_normals 0 4 1553 33 BulletDebugNode::get_show_normals 0 1 260 10
/**
 *
 */
58
inline bool BulletDebugNode::get_show_normals(void) const;

1023 14 get_class_type 0 4 1553 31 BulletDebugNode::get_class_type 0 1 261 0
56
static TypeHandle BulletDebugNode::get_class_type(void);

1024 15 BulletGhostNode 0 260 1554 32 BulletGhostNode::BulletGhostNode 0 1 262 138
/**
 *
 */

/**
 * Do not call the copy constructor directly; instead, use make_copy() or
 * copy_subgraph() to make a copy of a node.
 */
70
explicit BulletGhostNode::BulletGhostNode(char const *name = "ghost");

1025 25 get_num_overlapping_nodes 0 4 1554 42 BulletGhostNode::get_num_overlapping_nodes 0 1 263 58
// Overlapping

// Overlapping

// Overlapping

/**
 *
 */
59
int BulletGhostNode::get_num_overlapping_nodes(void) const;

1026 20 get_overlapping_node 0 4 1554 37 BulletGhostNode::get_overlapping_node 0 1 264 10
/**
 *
 */
64
PandaNode *BulletGhostNode::get_overlapping_node(int idx) const;

1027 14 get_class_type 0 4 1554 31 BulletGhostNode::get_class_type 0 1 265 0
56
static TypeHandle BulletGhostNode::get_class_type(void);

1028 26 ~BulletSoftBodyNodeElement 0 516 1555 53 BulletSoftBodyNodeElement::~BulletSoftBodyNodeElement 0 0 10
/**
 *
 */
67
inline BulletSoftBodyNodeElement::~BulletSoftBodyNodeElement(void);

1029 5 empty 0 4 1555 32 BulletSoftBodyNodeElement::empty 0 1 267 101
/**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */
79
static inline BulletSoftBodyNodeElement BulletSoftBodyNodeElement::empty(void);

1030 7 get_pos 0 4 1555 34 BulletSoftBodyNodeElement::get_pos 0 1 268 10
/**
 *
 */
55
LPoint3 BulletSoftBodyNodeElement::get_pos(void) const;

1031 12 get_velocity 0 4 1555 39 BulletSoftBodyNodeElement::get_velocity 0 1 269 10
/**
 *
 */
61
LVector3 BulletSoftBodyNodeElement::get_velocity(void) const;

1032 10 get_normal 0 4 1555 37 BulletSoftBodyNodeElement::get_normal 0 1 270 10
/**
 *
 */
59
LVector3 BulletSoftBodyNodeElement::get_normal(void) const;

1033 12 get_inv_mass 0 4 1555 39 BulletSoftBodyNodeElement::get_inv_mass 0 1 271 10
/**
 *
 */
64
PN_stdfloat BulletSoftBodyNodeElement::get_inv_mass(void) const;

1034 8 get_area 0 4 1555 35 BulletSoftBodyNodeElement::get_area 0 1 272 10
/**
 *
 */
60
PN_stdfloat BulletSoftBodyNodeElement::get_area(void) const;

1035 11 is_attached 0 4 1555 38 BulletSoftBodyNodeElement::is_attached 0 1 273 10
/**
 *
 */
55
int BulletSoftBodyNodeElement::is_attached(void) const;

1036 25 BulletSoftBodyNodeElement 0 260 1555 52 BulletSoftBodyNodeElement::BulletSoftBodyNodeElement 0 1 266 10
/**
 *
 */
105
inline BulletSoftBodyNodeElement::BulletSoftBodyNodeElement(BulletSoftBodyNodeElement const &) = default;

1037 7 get_cfg 0 4 1556 27 BulletSoftBodyNode::get_cfg 0 1 274 10
/**
 *
 */
55
BulletSoftBodyConfig BulletSoftBodyNode::get_cfg(void);

1038 14 get_world_info 0 4 1556 34 BulletSoftBodyNode::get_world_info 0 1 275 10
/**
 *
 */
65
BulletSoftBodyWorldInfo BulletSoftBodyNode::get_world_info(void);

1039 28 generate_bending_constraints 0 4 1556 48 BulletSoftBodyNode::generate_bending_constraints 0 1 276 10
/**
 *
 */
112
void BulletSoftBodyNode::generate_bending_constraints(int distance, BulletSoftBodyMaterial *material = nullptr);

1040 21 randomize_constraints 0 4 1556 41 BulletSoftBodyNode::randomize_constraints 0 1 277 10
/**
 *
 */
53
void BulletSoftBodyNode::randomize_constraints(void);

1041 15 set_volume_mass 0 4 1556 35 BulletSoftBodyNode::set_volume_mass 0 1 278 36
// Mass, volume, density

/**
 *
 */
59
void BulletSoftBodyNode::set_volume_mass(PN_stdfloat mass);

1042 18 set_volume_density 0 4 1556 38 BulletSoftBodyNode::set_volume_density 0 1 279 10
/**
 *
 */
65
void BulletSoftBodyNode::set_volume_density(PN_stdfloat density);

1043 14 set_total_mass 0 4 1556 34 BulletSoftBodyNode::set_total_mass 0 1 280 10
/**
 *
 */
82
void BulletSoftBodyNode::set_total_mass(PN_stdfloat mass, bool fromfaces = false);

1044 17 set_total_density 0 4 1556 37 BulletSoftBodyNode::set_total_density 0 1 281 10
/**
 *
 */
64
void BulletSoftBodyNode::set_total_density(PN_stdfloat density);

1045 8 set_mass 0 4 1556 28 BulletSoftBodyNode::set_mass 0 1 282 10
/**
 *
 */
62
void BulletSoftBodyNode::set_mass(int node, PN_stdfloat mass);

1046 8 get_mass 0 4 1556 28 BulletSoftBodyNode::get_mass 0 1 283 10
/**
 *
 */
57
PN_stdfloat BulletSoftBodyNode::get_mass(int node) const;

1047 14 get_total_mass 0 4 1556 34 BulletSoftBodyNode::get_total_mass 0 1 284 10
/**
 *
 */
59
PN_stdfloat BulletSoftBodyNode::get_total_mass(void) const;

1048 10 get_volume 0 4 1556 30 BulletSoftBodyNode::get_volume 0 1 285 10
/**
 *
 */
55
PN_stdfloat BulletSoftBodyNode::get_volume(void) const;

1049 9 add_force 0 4 1556 29 BulletSoftBodyNode::add_force 0 2 286 287 32
// Force

/**
 *
 */

/**
 *
 */
127
void BulletSoftBodyNode::add_force(LVector3 const &force);
void BulletSoftBodyNode::add_force(LVector3 const &force, int node);

1050 12 set_velocity 0 4 1556 32 BulletSoftBodyNode::set_velocity 0 1 288 10
/**
 *
 */
64
void BulletSoftBodyNode::set_velocity(LVector3 const &velocity);

1051 12 add_velocity 0 4 1556 32 BulletSoftBodyNode::add_velocity 0 2 289 290 22
/**
 *
 */

/**
 *
 */
139
void BulletSoftBodyNode::add_velocity(LVector3 const &velocity);
void BulletSoftBodyNode::add_velocity(LVector3 const &velocity, int node);

1052 17 set_wind_velocity 0 4 1556 37 BulletSoftBodyNode::set_wind_velocity 0 1 291 10
/**
 *
 */
69
void BulletSoftBodyNode::set_wind_velocity(LVector3 const &velocity);

1053 17 get_wind_velocity 0 4 1556 37 BulletSoftBodyNode::get_wind_velocity 0 1 292 10
/**
 *
 */
59
LVector3 BulletSoftBodyNode::get_wind_velocity(void) const;

1054 8 set_pose 0 4 1556 28 BulletSoftBodyNode::set_pose 0 1 293 10
/**
 *
 */
61
void BulletSoftBodyNode::set_pose(bool bvolume, bool bframe);

1055 8 get_aabb 0 4 1556 28 BulletSoftBodyNode::get_aabb 0 1 294 10
/**
 *
 */
53
BoundingBox BulletSoftBodyNode::get_aabb(void) const;

1056 17 generate_clusters 0 4 1556 37 BulletSoftBodyNode::generate_clusters 0 1 295 22
// Cluster

/**
 *
 */
76
void BulletSoftBodyNode::generate_clusters(int k, int maxiterations = 8192);

1057 15 release_cluster 0 4 1556 35 BulletSoftBodyNode::release_cluster 0 1 296 10
/**
 *
 */
52
void BulletSoftBodyNode::release_cluster(int index);

1058 16 release_clusters 0 4 1556 36 BulletSoftBodyNode::release_clusters 0 1 297 10
/**
 *
 */
48
void BulletSoftBodyNode::release_clusters(void);

1059 16 get_num_clusters 0 4 1556 36 BulletSoftBodyNode::get_num_clusters 0 1 298 10
/**
 *
 */
53
int BulletSoftBodyNode::get_num_clusters(void) const;

1060 11 cluster_com 0 4 1556 31 BulletSoftBodyNode::cluster_com 0 1 299 10
/**
 *
 */
61
LVecBase3 BulletSoftBodyNode::cluster_com(int cluster) const;

1061 9 link_geom 0 4 1556 29 BulletSoftBodyNode::link_geom 0 1 300 24
// Rendering

/**
 *
 */
47
void BulletSoftBodyNode::link_geom(Geom *geom);

1062 11 unlink_geom 0 4 1556 31 BulletSoftBodyNode::unlink_geom 0 1 301 10
/**
 *
 */
43
void BulletSoftBodyNode::unlink_geom(void);

1063 10 link_curve 0 4 1556 30 BulletSoftBodyNode::link_curve 0 1 302 10
/**
 *
 */
64
void BulletSoftBodyNode::link_curve(NurbsCurveEvaluator *curve);

1064 12 unlink_curve 0 4 1556 32 BulletSoftBodyNode::unlink_curve 0 1 303 10
/**
 *
 */
44
void BulletSoftBodyNode::unlink_curve(void);

1065 12 link_surface 0 4 1556 32 BulletSoftBodyNode::link_surface 0 1 304 10
/**
 *
 */
70
void BulletSoftBodyNode::link_surface(NurbsSurfaceEvaluator *surface);

1066 14 unlink_surface 0 4 1556 34 BulletSoftBodyNode::unlink_surface 0 1 305 10
/**
 *
 */
46
void BulletSoftBodyNode::unlink_surface(void);

1067 13 append_anchor 0 4 1556 33 BulletSoftBodyNode::append_anchor 0 2 306 307 34
// Anchors

/**
 *
 */

/**
 *
 */
220
void BulletSoftBodyNode::append_anchor(int node, BulletRigidBodyNode *body, bool disable = false);
void BulletSoftBodyNode::append_anchor(int node, BulletRigidBodyNode *body, LVector3 const &pivot, bool disable = false);

1068 19 append_linear_joint 0 4 1556 39 BulletSoftBodyNode::append_linear_joint 0 2 308 309 32
// Links

/**
 *
 */

/**
 *
 */
310
void BulletSoftBodyNode::append_linear_joint(BulletBodyNode *body, int cluster, PN_stdfloat erp = 1.0, PN_stdfloat cfm = 1.0, PN_stdfloat split = 1.0);
void BulletSoftBodyNode::append_linear_joint(BulletBodyNode *body, LPoint3 const &pos, PN_stdfloat erp = 1.0, PN_stdfloat cfm = 1.0, PN_stdfloat split = 1.0);

1069 20 append_angular_joint 0 4 1556 40 BulletSoftBodyNode::append_angular_joint 0 1 310 10
/**
 *
 */
203
void BulletSoftBodyNode::append_angular_joint(BulletBodyNode *body, LVector3 const &axis, PN_stdfloat erp = 1.0, PN_stdfloat cfm = 1.0, PN_stdfloat split = 1.0, BulletSoftBodyControl *control = nullptr);

1070 17 get_num_materials 0 4 1556 37 BulletSoftBodyNode::get_num_materials 0 1 311 52
// Materials

// Materials

// Materials

/**
 *
 */
54
int BulletSoftBodyNode::get_num_materials(void) const;

1071 12 get_material 0 4 1556 32 BulletSoftBodyNode::get_material 0 1 312 10
/**
 *
 */
71
BulletSoftBodyMaterial BulletSoftBodyNode::get_material(int idx) const;

1072 15 append_material 0 4 1556 35 BulletSoftBodyNode::append_material 0 1 313 10
/**
 *
 */
65
BulletSoftBodyMaterial BulletSoftBodyNode::append_material(void);

1073 13 get_num_nodes 0 4 1556 33 BulletSoftBodyNode::get_num_nodes 0 1 314 40
// Nodes

// Nodes

// Nodes

/**
 *
 */
50
int BulletSoftBodyNode::get_num_nodes(void) const;

1074 8 get_node 0 4 1556 28 BulletSoftBodyNode::get_node 0 1 315 10
/**
 *
 */
70
BulletSoftBodyNodeElement BulletSoftBodyNode::get_node(int idx) const;

1075 22 get_closest_node_index 0 4 1556 42 BulletSoftBodyNode::get_closest_node_index 0 1 316 212
/**
 * Returns the index of the node which is closest to the given point.  The
 * distance between each node and the given point is computed in world space
 * if local=false, and in local space if local=true.
 */
76
int BulletSoftBodyNode::get_closest_node_index(LVecBase3 point, bool local);

1076 9 make_rope 0 4 1556 29 BulletSoftBodyNode::make_rope 0 1 317 22
// Factory

/**
 *
 */
161
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_rope(BulletSoftBodyWorldInfo &info, LPoint3 const &from, LPoint3 const &to, int res, int fixeds);

1077 10 make_patch 0 4 1556 30 BulletSoftBodyNode::make_patch 0 1 318 10
/**
 *
 */
248
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_patch(BulletSoftBodyWorldInfo &info, LPoint3 const &corner00, LPoint3 const &corner10, LPoint3 const &corner01, LPoint3 const &corner11, int resx, int resy, int fixeds, bool gendiags);

1078 14 make_ellipsoid 0 4 1556 34 BulletSoftBodyNode::make_ellipsoid 0 1 319 10
/**
 *
 */
162
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_ellipsoid(BulletSoftBodyWorldInfo &info, LPoint3 const &center, LVecBase3 const &radius, int res);

1079 13 make_tri_mesh 0 4 1556 33 BulletSoftBodyNode::make_tri_mesh 0 2 320 321 22
/**
 *
 */

/**
 *
 */
334
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_tri_mesh(BulletSoftBodyWorldInfo &info, Geom const *geom, bool randomizeConstraints = true);
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_tri_mesh(BulletSoftBodyWorldInfo &info, PTA_LVecBase3 points, PTA_int indices, bool randomizeConstraints = true);

1080 13 make_tet_mesh 0 4 1556 33 BulletSoftBodyNode::make_tet_mesh 0 2 322 323 22
/**
 *
 */

/**
 *
 */
325
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_tet_mesh(BulletSoftBodyWorldInfo &info, PTA_LVecBase3 points, PTA_int indices, bool tetralinks = true);
static PointerTo< BulletSoftBodyNode > BulletSoftBodyNode::make_tet_mesh(BulletSoftBodyWorldInfo &info, char const *ele, char const *face, char const *node);

1081 21 ~BulletSoftBodyConfig 0 516 1557 43 BulletSoftBodyConfig::~BulletSoftBodyConfig 0 0 10
/**
 *
 */
57
inline BulletSoftBodyConfig::~BulletSoftBodyConfig(void);

1082 25 clear_all_collision_flags 0 4 1557 47 BulletSoftBodyConfig::clear_all_collision_flags 0 1 326 10
/**
 *
 */
59
void BulletSoftBodyConfig::clear_all_collision_flags(void);

1083 18 set_collision_flag 0 4 1557 40 BulletSoftBodyConfig::set_collision_flag 0 1 327 10
/**
 *
 */
100
void BulletSoftBodyConfig::set_collision_flag(BulletSoftBodyConfig::CollisionFlag flag, bool value);

1084 18 get_collision_flag 0 4 1557 40 BulletSoftBodyConfig::get_collision_flag 0 1 328 10
/**
 *
 */
94
bool BulletSoftBodyConfig::get_collision_flag(BulletSoftBodyConfig::CollisionFlag flag) const;

1085 14 set_aero_model 0 4 1557 36 BulletSoftBodyConfig::set_aero_model 0 1 329 10
/**
 *
 */
81
void BulletSoftBodyConfig::set_aero_model(BulletSoftBodyConfig::AeroModel value);

1086 14 get_aero_model 0 4 1557 36 BulletSoftBodyConfig::get_aero_model 0 1 330 10
/**
 *
 */
81
BulletSoftBodyConfig::AeroModel BulletSoftBodyConfig::get_aero_model(void) const;

1087 32 set_velocities_correction_factor 0 4 1557 54 BulletSoftBodyConfig::set_velocities_correction_factor 0 1 331 36
/**
 * Setter for property kVCF.
 */
79
void BulletSoftBodyConfig::set_velocities_correction_factor(PN_stdfloat value);

1088 23 set_damping_coefficient 0 4 1557 45 BulletSoftBodyConfig::set_damping_coefficient 0 1 332 35
/**
 * Setter for property kDP.
 */
70
void BulletSoftBodyConfig::set_damping_coefficient(PN_stdfloat value);

1089 20 set_drag_coefficient 0 4 1557 42 BulletSoftBodyConfig::set_drag_coefficient 0 1 333 35
/**
 * Setter for property kDG.
 */
67
void BulletSoftBodyConfig::set_drag_coefficient(PN_stdfloat value);

1090 20 set_lift_coefficient 0 4 1557 42 BulletSoftBodyConfig::set_lift_coefficient 0 1 334 35
/**
 * Setter for property kLF.
 */
67
void BulletSoftBodyConfig::set_lift_coefficient(PN_stdfloat value);

1091 24 set_pressure_coefficient 0 4 1557 46 BulletSoftBodyConfig::set_pressure_coefficient 0 1 335 35
/**
 * Setter for property kPR.
 */
71
void BulletSoftBodyConfig::set_pressure_coefficient(PN_stdfloat value);

1092 35 set_volume_conservation_coefficient 0 4 1557 57 BulletSoftBodyConfig::set_volume_conservation_coefficient 0 1 336 35
/**
 * Setter for property kVC.
 */
82
void BulletSoftBodyConfig::set_volume_conservation_coefficient(PN_stdfloat value);

1093 32 set_dynamic_friction_coefficient 0 4 1557 54 BulletSoftBodyConfig::set_dynamic_friction_coefficient 0 1 337 35
/**
 * Setter for property kDF.
 */
79
void BulletSoftBodyConfig::set_dynamic_friction_coefficient(PN_stdfloat value);

1094 29 set_pose_matching_coefficient 0 4 1557 51 BulletSoftBodyConfig::set_pose_matching_coefficient 0 1 338 35
/**
 * Setter for property kMT.
 */
76
void BulletSoftBodyConfig::set_pose_matching_coefficient(PN_stdfloat value);

1095 27 set_rigid_contacts_hardness 0 4 1557 49 BulletSoftBodyConfig::set_rigid_contacts_hardness 0 1 339 36
/**
 * Setter for property kCHR.
 */
74
void BulletSoftBodyConfig::set_rigid_contacts_hardness(PN_stdfloat value);

1096 29 set_kinetic_contacts_hardness 0 4 1557 51 BulletSoftBodyConfig::set_kinetic_contacts_hardness 0 1 340 36
/**
 * Setter for property kKHR.
 */
76
void BulletSoftBodyConfig::set_kinetic_contacts_hardness(PN_stdfloat value);

1097 26 set_soft_contacts_hardness 0 4 1557 48 BulletSoftBodyConfig::set_soft_contacts_hardness 0 1 341 36
/**
 * Setter for property kSHR.
 */
73
void BulletSoftBodyConfig::set_soft_contacts_hardness(PN_stdfloat value);

1098 20 set_anchors_hardness 0 4 1557 42 BulletSoftBodyConfig::set_anchors_hardness 0 1 342 36
/**
 * Setter for property kAHR.
 */
67
void BulletSoftBodyConfig::set_anchors_hardness(PN_stdfloat value);

1099 26 set_soft_vs_rigid_hardness 0 4 1557 48 BulletSoftBodyConfig::set_soft_vs_rigid_hardness 0 1 343 40
/**
 * Setter for property kSRHR_CL.
 */
73
void BulletSoftBodyConfig::set_soft_vs_rigid_hardness(PN_stdfloat value);

1100 28 set_soft_vs_kinetic_hardness 0 4 1557 50 BulletSoftBodyConfig::set_soft_vs_kinetic_hardness 0 1 344 40
/**
 * Setter for property kSKHR_CL.
 */
75
void BulletSoftBodyConfig::set_soft_vs_kinetic_hardness(PN_stdfloat value);

1101 25 set_soft_vs_soft_hardness 0 4 1557 47 BulletSoftBodyConfig::set_soft_vs_soft_hardness 0 1 345 40
/**
 * Setter for property kSSHR_CL.
 */
72
void BulletSoftBodyConfig::set_soft_vs_soft_hardness(PN_stdfloat value);

1102 31 set_soft_vs_rigid_impulse_split 0 4 1557 53 BulletSoftBodyConfig::set_soft_vs_rigid_impulse_split 0 1 346 43
/**
 * Setter for property kSR_SPLT_CL.
 */
78
void BulletSoftBodyConfig::set_soft_vs_rigid_impulse_split(PN_stdfloat value);

1103 33 set_soft_vs_kinetic_impulse_split 0 4 1557 55 BulletSoftBodyConfig::set_soft_vs_kinetic_impulse_split 0 1 347 43
/**
 * Setter for property kSK_SPLT_CL.
 */
80
void BulletSoftBodyConfig::set_soft_vs_kinetic_impulse_split(PN_stdfloat value);

1104 30 set_soft_vs_soft_impulse_split 0 4 1557 52 BulletSoftBodyConfig::set_soft_vs_soft_impulse_split 0 1 348 43
/**
 * Setter for property kSS_SPLT_CL.
 */
77
void BulletSoftBodyConfig::set_soft_vs_soft_impulse_split(PN_stdfloat value);

1105 13 set_maxvolume 0 4 1557 35 BulletSoftBodyConfig::set_maxvolume 0 1 349 41
/**
 * Setter for property maxvolume.
 */
60
void BulletSoftBodyConfig::set_maxvolume(PN_stdfloat value);

1106 13 set_timescale 0 4 1557 35 BulletSoftBodyConfig::set_timescale 0 1 350 41
/**
 * Setter for property timescale.
 */
60
void BulletSoftBodyConfig::set_timescale(PN_stdfloat value);

1107 31 set_positions_solver_iterations 0 4 1557 53 BulletSoftBodyConfig::set_positions_solver_iterations 0 1 351 43
/**
 * Setter for property piterations.
 */
70
void BulletSoftBodyConfig::set_positions_solver_iterations(int value);

1108 32 set_velocities_solver_iterations 0 4 1557 54 BulletSoftBodyConfig::set_velocities_solver_iterations 0 1 352 43
/**
 * Setter for property viterations.
 */
71
void BulletSoftBodyConfig::set_velocities_solver_iterations(int value);

1109 27 set_drift_solver_iterations 0 4 1557 49 BulletSoftBodyConfig::set_drift_solver_iterations 0 1 353 43
/**
 * Setter for property diterations.
 */
66
void BulletSoftBodyConfig::set_drift_solver_iterations(int value);

1110 29 set_cluster_solver_iterations 0 4 1557 51 BulletSoftBodyConfig::set_cluster_solver_iterations 0 1 354 43
/**
 * Setter for property citerations.
 */
68
void BulletSoftBodyConfig::set_cluster_solver_iterations(int value);

1111 32 get_velocities_correction_factor 0 4 1557 54 BulletSoftBodyConfig::get_velocities_correction_factor 0 1 355 36
/**
 * Getter for property kVCF.
 */
79
PN_stdfloat BulletSoftBodyConfig::get_velocities_correction_factor(void) const;

1112 23 get_damping_coefficient 0 4 1557 45 BulletSoftBodyConfig::get_damping_coefficient 0 1 356 35
/**
 * Getter for property kDP.
 */
70
PN_stdfloat BulletSoftBodyConfig::get_damping_coefficient(void) const;

1113 20 get_drag_coefficient 0 4 1557 42 BulletSoftBodyConfig::get_drag_coefficient 0 1 357 35
/**
 * Getter for property kDG.
 */
67
PN_stdfloat BulletSoftBodyConfig::get_drag_coefficient(void) const;

1114 20 get_lift_coefficient 0 4 1557 42 BulletSoftBodyConfig::get_lift_coefficient 0 1 358 35
/**
 * Getter for property kLF.
 */
67
PN_stdfloat BulletSoftBodyConfig::get_lift_coefficient(void) const;

1115 24 get_pressure_coefficient 0 4 1557 46 BulletSoftBodyConfig::get_pressure_coefficient 0 1 359 35
/**
 * Getter for property kPR.
 */
71
PN_stdfloat BulletSoftBodyConfig::get_pressure_coefficient(void) const;

1116 35 get_volume_conservation_coefficient 0 4 1557 57 BulletSoftBodyConfig::get_volume_conservation_coefficient 0 1 360 35
/**
 * Getter for property kVC.
 */
82
PN_stdfloat BulletSoftBodyConfig::get_volume_conservation_coefficient(void) const;

1117 32 get_dynamic_friction_coefficient 0 4 1557 54 BulletSoftBodyConfig::get_dynamic_friction_coefficient 0 1 361 35
/**
 * Getter for property kDF.
 */
79
PN_stdfloat BulletSoftBodyConfig::get_dynamic_friction_coefficient(void) const;

1118 29 get_pose_matching_coefficient 0 4 1557 51 BulletSoftBodyConfig::get_pose_matching_coefficient 0 1 362 35
/**
 * Getter for property kMT.
 */
76
PN_stdfloat BulletSoftBodyConfig::get_pose_matching_coefficient(void) const;

1119 27 get_rigid_contacts_hardness 0 4 1557 49 BulletSoftBodyConfig::get_rigid_contacts_hardness 0 1 363 36
/**
 * Getter for property kCHR.
 */
74
PN_stdfloat BulletSoftBodyConfig::get_rigid_contacts_hardness(void) const;

1120 29 get_kinetic_contacts_hardness 0 4 1557 51 BulletSoftBodyConfig::get_kinetic_contacts_hardness 0 1 364 36
/**
 * Getter for property kKHR.
 */
76
PN_stdfloat BulletSoftBodyConfig::get_kinetic_contacts_hardness(void) const;

1121 26 get_soft_contacts_hardness 0 4 1557 48 BulletSoftBodyConfig::get_soft_contacts_hardness 0 1 365 36
/**
 * Getter for property kSHR.
 */
73
PN_stdfloat BulletSoftBodyConfig::get_soft_contacts_hardness(void) const;

1122 20 get_anchors_hardness 0 4 1557 42 BulletSoftBodyConfig::get_anchors_hardness 0 1 366 36
/**
 * Getter for property kAHR.
 */
67
PN_stdfloat BulletSoftBodyConfig::get_anchors_hardness(void) const;

1123 26 get_soft_vs_rigid_hardness 0 4 1557 48 BulletSoftBodyConfig::get_soft_vs_rigid_hardness 0 1 367 40
/**
 * Getter for property kSRHR_CL.
 */
73
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_rigid_hardness(void) const;

1124 28 get_soft_vs_kinetic_hardness 0 4 1557 50 BulletSoftBodyConfig::get_soft_vs_kinetic_hardness 0 1 368 40
/**
 * Getter for property kSKHR_CL.
 */
75
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_kinetic_hardness(void) const;

1125 25 get_soft_vs_soft_hardness 0 4 1557 47 BulletSoftBodyConfig::get_soft_vs_soft_hardness 0 1 369 40
/**
 * Getter for property kSSHR_CL.
 */
72
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_soft_hardness(void) const;

1126 31 get_soft_vs_rigid_impulse_split 0 4 1557 53 BulletSoftBodyConfig::get_soft_vs_rigid_impulse_split 0 1 370 43
/**
 * Getter for property kSR_SPLT_CL.
 */
78
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_rigid_impulse_split(void) const;

1127 33 get_soft_vs_kinetic_impulse_split 0 4 1557 55 BulletSoftBodyConfig::get_soft_vs_kinetic_impulse_split 0 1 371 43
/**
 * Getter for property kSK_SPLT_CL.
 */
80
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_kinetic_impulse_split(void) const;

1128 30 get_soft_vs_soft_impulse_split 0 4 1557 52 BulletSoftBodyConfig::get_soft_vs_soft_impulse_split 0 1 372 43
/**
 * Getter for property kSS_SPLT_CL.
 */
77
PN_stdfloat BulletSoftBodyConfig::get_soft_vs_soft_impulse_split(void) const;

1129 13 get_maxvolume 0 4 1557 35 BulletSoftBodyConfig::get_maxvolume 0 1 373 41
/**
 * Getter for property maxvolume.
 */
60
PN_stdfloat BulletSoftBodyConfig::get_maxvolume(void) const;

1130 13 get_timescale 0 4 1557 35 BulletSoftBodyConfig::get_timescale 0 1 374 41
/**
 * Getter for property timescale.
 */
60
PN_stdfloat BulletSoftBodyConfig::get_timescale(void) const;

1131 31 get_positions_solver_iterations 0 4 1557 53 BulletSoftBodyConfig::get_positions_solver_iterations 0 1 375 43
/**
 * Getter for property piterations.
 */
70
int BulletSoftBodyConfig::get_positions_solver_iterations(void) const;

1132 32 get_velocities_solver_iterations 0 4 1557 54 BulletSoftBodyConfig::get_velocities_solver_iterations 0 1 376 43
/**
 * Getter for property viterations.
 */
71
int BulletSoftBodyConfig::get_velocities_solver_iterations(void) const;

1133 27 get_drift_solver_iterations 0 4 1557 49 BulletSoftBodyConfig::get_drift_solver_iterations 0 1 377 43
/**
 * Getter for property diterations.
 */
66
int BulletSoftBodyConfig::get_drift_solver_iterations(void) const;

1134 29 get_cluster_solver_iterations 0 4 1557 51 BulletSoftBodyConfig::get_cluster_solver_iterations 0 1 378 43
/**
 * Getter for property citerations.
 */
68
int BulletSoftBodyConfig::get_cluster_solver_iterations(void) const;

1135 20 BulletSoftBodyConfig 0 260 1557 42 BulletSoftBodyConfig::BulletSoftBodyConfig 0 1 325 10
/**
 *
 */
90
inline BulletSoftBodyConfig::BulletSoftBodyConfig(BulletSoftBodyConfig const &) = default;

1136 24 ~BulletSoftBodyWorldInfo 0 516 1560 49 BulletSoftBodyWorldInfo::~BulletSoftBodyWorldInfo 0 0 10
/**
 *
 */
63
inline BulletSoftBodyWorldInfo::~BulletSoftBodyWorldInfo(void);

1137 15 set_air_density 0 4 1560 40 BulletSoftBodyWorldInfo::set_air_density 0 1 380 10
/**
 *
 */
67
void BulletSoftBodyWorldInfo::set_air_density(PN_stdfloat density);

1138 17 set_water_density 0 4 1560 42 BulletSoftBodyWorldInfo::set_water_density 0 1 381 10
/**
 *
 */
69
void BulletSoftBodyWorldInfo::set_water_density(PN_stdfloat density);

1139 16 set_water_offset 0 4 1560 41 BulletSoftBodyWorldInfo::set_water_offset 0 1 382 10
/**
 *
 */
67
void BulletSoftBodyWorldInfo::set_water_offset(PN_stdfloat offset);

1140 16 set_water_normal 0 4 1560 41 BulletSoftBodyWorldInfo::set_water_normal 0 1 383 10
/**
 *
 */
71
void BulletSoftBodyWorldInfo::set_water_normal(LVector3 const &normal);

1141 11 set_gravity 0 4 1560 36 BulletSoftBodyWorldInfo::set_gravity 0 1 384 10
/**
 *
 */
67
void BulletSoftBodyWorldInfo::set_gravity(LVector3 const &gravity);

1142 15 get_air_density 0 4 1560 40 BulletSoftBodyWorldInfo::get_air_density 0 1 385 10
/**
 *
 */
65
PN_stdfloat BulletSoftBodyWorldInfo::get_air_density(void) const;

1143 17 get_water_density 0 4 1560 42 BulletSoftBodyWorldInfo::get_water_density 0 1 386 10
/**
 *
 */
67
PN_stdfloat BulletSoftBodyWorldInfo::get_water_density(void) const;

1144 16 get_water_offset 0 4 1560 41 BulletSoftBodyWorldInfo::get_water_offset 0 1 387 10
/**
 *
 */
66
PN_stdfloat BulletSoftBodyWorldInfo::get_water_offset(void) const;

1145 16 get_water_normal 0 4 1560 41 BulletSoftBodyWorldInfo::get_water_normal 0 1 388 10
/**
 *
 */
63
LVector3 BulletSoftBodyWorldInfo::get_water_normal(void) const;

1146 11 get_gravity 0 4 1560 36 BulletSoftBodyWorldInfo::get_gravity 0 1 389 10
/**
 *
 */
58
LVector3 BulletSoftBodyWorldInfo::get_gravity(void) const;

1147 15 garbage_collect 0 4 1560 40 BulletSoftBodyWorldInfo::garbage_collect 0 1 390 10
/**
 *
 */
66
void BulletSoftBodyWorldInfo::garbage_collect(int lifetime = 256);

1148 23 BulletSoftBodyWorldInfo 0 260 1560 48 BulletSoftBodyWorldInfo::BulletSoftBodyWorldInfo 0 1 379 10
/**
 *
 */
99
inline BulletSoftBodyWorldInfo::BulletSoftBodyWorldInfo(BulletSoftBodyWorldInfo const &) = default;

1149 23 ~BulletSoftBodyMaterial 0 516 1562 47 BulletSoftBodyMaterial::~BulletSoftBodyMaterial 0 0 10
/**
 *
 */
61
inline BulletSoftBodyMaterial::~BulletSoftBodyMaterial(void);

1150 5 empty 0 4 1562 29 BulletSoftBodyMaterial::empty 0 1 392 102
/**
 * Named constructor intended to be used for asserts which have to return a
 * concrete value.
 */
73
static inline BulletSoftBodyMaterial BulletSoftBodyMaterial::empty(void);

1151 20 get_linear_stiffness 0 4 1562 44 BulletSoftBodyMaterial::get_linear_stiffness 0 1 393 42
/**
 * Getter for the property m_kLST.
 */
69
PN_stdfloat BulletSoftBodyMaterial::get_linear_stiffness(void) const;

1152 20 set_linear_stiffness 0 4 1562 44 BulletSoftBodyMaterial::set_linear_stiffness 0 1 394 42
/**
 * Setter for the property m_kLST.
 */
69
void BulletSoftBodyMaterial::set_linear_stiffness(PN_stdfloat value);

1153 21 get_angular_stiffness 0 4 1562 45 BulletSoftBodyMaterial::get_angular_stiffness 0 1 395 42
/**
 * Getter for the property m_kAST.
 */
70
PN_stdfloat BulletSoftBodyMaterial::get_angular_stiffness(void) const;

1154 21 set_angular_stiffness 0 4 1562 45 BulletSoftBodyMaterial::set_angular_stiffness 0 1 396 42
/**
 * Setter for the property m_kAST.
 */
70
void BulletSoftBodyMaterial::set_angular_stiffness(PN_stdfloat value);

1155 23 get_volume_preservation 0 4 1562 47 BulletSoftBodyMaterial::get_volume_preservation 0 1 397 42
/**
 * Getter for the property m_kVST.
 */
72
PN_stdfloat BulletSoftBodyMaterial::get_volume_preservation(void) const;

1156 23 set_volume_preservation 0 4 1562 47 BulletSoftBodyMaterial::set_volume_preservation 0 1 398 42
/**
 * Setter for the property m_kVST.
 */
72
void BulletSoftBodyMaterial::set_volume_preservation(PN_stdfloat value);

1157 22 BulletSoftBodyMaterial 0 260 1562 46 BulletSoftBodyMaterial::BulletSoftBodyMaterial 0 1 391 10
/**
 *
 */
96
inline BulletSoftBodyMaterial::BulletSoftBodyMaterial(BulletSoftBodyMaterial const &) = default;

1158 14 get_class_type 0 4 1556 34 BulletSoftBodyNode::get_class_type 0 1 324 0
59
static TypeHandle BulletSoftBodyNode::get_class_type(void);

1159 24 set_suspension_stiffness 0 4 1563 45 BulletVehicleTuning::set_suspension_stiffness 0 1 399 10
/**
 *
 */
70
void BulletVehicleTuning::set_suspension_stiffness(PN_stdfloat value);

1160 26 set_suspension_compression 0 4 1563 47 BulletVehicleTuning::set_suspension_compression 0 1 400 10
/**
 *
 */
72
void BulletVehicleTuning::set_suspension_compression(PN_stdfloat value);

1161 22 set_suspension_damping 0 4 1563 43 BulletVehicleTuning::set_suspension_damping 0 1 401 10
/**
 *
 */
68
void BulletVehicleTuning::set_suspension_damping(PN_stdfloat value);

1162 28 set_max_suspension_travel_cm 0 4 1563 49 BulletVehicleTuning::set_max_suspension_travel_cm 0 1 402 10
/**
 *
 */
74
void BulletVehicleTuning::set_max_suspension_travel_cm(PN_stdfloat value);

1163 17 set_friction_slip 0 4 1563 38 BulletVehicleTuning::set_friction_slip 0 1 403 10
/**
 *
 */
63
void BulletVehicleTuning::set_friction_slip(PN_stdfloat value);

1164 24 set_max_suspension_force 0 4 1563 45 BulletVehicleTuning::set_max_suspension_force 0 1 404 10
/**
 *
 */
70
void BulletVehicleTuning::set_max_suspension_force(PN_stdfloat value);

1165 24 get_suspension_stiffness 0 4 1563 45 BulletVehicleTuning::get_suspension_stiffness 0 1 405 10
/**
 *
 */
70
PN_stdfloat BulletVehicleTuning::get_suspension_stiffness(void) const;

1166 26 get_suspension_compression 0 4 1563 47 BulletVehicleTuning::get_suspension_compression 0 1 406 10
/**
 *
 */
72
PN_stdfloat BulletVehicleTuning::get_suspension_compression(void) const;

1167 22 get_suspension_damping 0 4 1563 43 BulletVehicleTuning::get_suspension_damping 0 1 407 10
/**
 *
 */
68
PN_stdfloat BulletVehicleTuning::get_suspension_damping(void) const;

1168 28 get_max_suspension_travel_cm 0 4 1563 49 BulletVehicleTuning::get_max_suspension_travel_cm 0 1 408 10
/**
 *
 */
74
PN_stdfloat BulletVehicleTuning::get_max_suspension_travel_cm(void) const;

1169 17 get_friction_slip 0 4 1563 38 BulletVehicleTuning::get_friction_slip 0 1 409 10
/**
 *
 */
63
PN_stdfloat BulletVehicleTuning::get_friction_slip(void) const;

1170 24 get_max_suspension_force 0 4 1563 45 BulletVehicleTuning::get_max_suspension_force 0 1 410 10
/**
 *
 */
70
PN_stdfloat BulletVehicleTuning::get_max_suspension_force(void) const;

1171 13 BulletVehicle 0 260 1564 28 BulletVehicle::BulletVehicle 0 1 411 94
/**
 * Creates a new BulletVehicle instance in the given world and with a chassis
 * node.
 */
79
BulletVehicle::BulletVehicle(BulletWorld *world, BulletRigidBodyNode *chassis);

1172 21 set_coordinate_system 0 4 1564 36 BulletVehicle::set_coordinate_system 0 1 412 100
/**
 * Specifies which axis is "up". Nessecary for the vehicle's suspension to
 * work properly!
 */
59
void BulletVehicle::set_coordinate_system(BulletUpAxis up);

1173 18 set_steering_value 0 4 1564 33 BulletVehicle::set_steering_value 0 1 413 76
/**
 * Sets the steering value (in degrees) of the wheel with index idx.
 */
70
void BulletVehicle::set_steering_value(PN_stdfloat steering, int idx);

1174 9 set_brake 0 4 1564 24 BulletVehicle::set_brake 0 1 414 61
/**
 * Applies braking force to the wheel with index idx.
 */
58
void BulletVehicle::set_brake(PN_stdfloat brake, int idx);

1175 17 set_pitch_control 0 4 1564 32 BulletVehicle::set_pitch_control 0 1 415 10
/**
 *
 */
57
void BulletVehicle::set_pitch_control(PN_stdfloat pitch);

1176 11 get_chassis 0 4 1564 26 BulletVehicle::get_chassis 0 1 416 82
/**
 * Returns the chassis of this vehicle.  The chassis is a rigid body node.
 */
54
BulletRigidBodyNode *BulletVehicle::get_chassis(void);

1177 25 get_current_speed_km_hour 0 4 1564 40 BulletVehicle::get_current_speed_km_hour 0 1 417 106
/**
 * Returns the current speed in kilometers per hour.  Convert to miles using:
 * km/h * 0.62 = mph
 */
65
PN_stdfloat BulletVehicle::get_current_speed_km_hour(void) const;

1178 18 get_steering_value 0 4 1564 33 BulletVehicle::get_steering_value 0 1 418 77
/**
 * Returns the steering angle of the wheel with index idx in degrees.
 */
61
PN_stdfloat BulletVehicle::get_steering_value(int idx) const;

1179 18 get_forward_vector 0 4 1564 33 BulletVehicle::get_forward_vector 0 1 419 145
/**
 * Returns the forward vector representing the car's actual direction of
 * movement.  The forward vetcor is given in global coordinates.
 */
55
LVector3 BulletVehicle::get_forward_vector(void) const;

1180 16 reset_suspension 0 4 1564 31 BulletVehicle::reset_suspension 0 1 420 43
/**
 * Resets the vehicle's suspension.
 */
43
void BulletVehicle::reset_suspension(void);

1181 18 apply_engine_force 0 4 1564 33 BulletVehicle::apply_engine_force 0 1 421 70
/**
 * Applies force at the wheel with index idx for acceleration.
 */
67
void BulletVehicle::apply_engine_force(PN_stdfloat force, int idx);

1182 12 create_wheel 0 4 1564 27 BulletVehicle::create_wheel 0 1 422 83
// Wheels

/**
 * Factory method for creating wheels for this vehicle instance.
 */
46
BulletWheel BulletVehicle::create_wheel(void);

1183 14 get_num_wheels 0 4 1564 29 BulletVehicle::get_num_wheels 0 1 423 57
/**
 * Returns the number of wheels this vehicle has.
 */
46
int BulletVehicle::get_num_wheels(void) const;

1184 9 get_wheel 0 4 1564 24 BulletVehicle::get_wheel 0 1 424 132
/**
 * Returns the BulletWheel with index idx.  Causes an AssertionError if idx is
 * equal or larger than the number of wheels.
 */
52
BulletWheel BulletVehicle::get_wheel(int idx) const;

1185 10 get_tuning 0 4 1564 25 BulletVehicle::get_tuning 0 1 425 211
// Tuning

// Tuning

/**
 * Returns a reference to the BulletVehicleTuning object of this vehicle which
 * offers various vehicle-global tuning options.  Make sure to configure this
 * before adding wheels!
 */
60
inline BulletVehicleTuning &BulletVehicle::get_tuning(void);

1186 12 ~BulletWheel 0 516 1565 25 BulletWheel::~BulletWheel 0 0 10
/**
 *
 */
39
inline BulletWheel::~BulletWheel(void);

1187 24 set_suspension_stiffness 0 4 1565 37 BulletWheel::set_suspension_stiffness 0 1 428 50
/**
 * Sets how stiff the suspension shall be.
 */
62
void BulletWheel::set_suspension_stiffness(PN_stdfloat value);

1188 28 set_max_suspension_travel_cm 0 4 1565 41 BulletWheel::set_max_suspension_travel_cm 0 1 429 109
/**
 * Sets the maximum distance the suspension can travel out of the resting
 * position in centimeters.
 */
66
void BulletWheel::set_max_suspension_travel_cm(PN_stdfloat value);

1189 17 set_friction_slip 0 4 1565 30 BulletWheel::set_friction_slip 0 1 430 45
/**
 * Sets the slipperyness of the tyre.
 */
55
void BulletWheel::set_friction_slip(PN_stdfloat value);

1190 24 set_max_suspension_force 0 4 1565 37 BulletWheel::set_max_suspension_force 0 1 431 66
/**
 * Sets the maximum suspension force the wheel can handle.
 */
62
void BulletWheel::set_max_suspension_force(PN_stdfloat value);

1191 30 set_wheels_damping_compression 0 4 1565 43 BulletWheel::set_wheels_damping_compression 0 1 432 79
/**
 * Sets the damping forces applied when the suspension gets compressed.
 */
68
void BulletWheel::set_wheels_damping_compression(PN_stdfloat value);

1192 29 set_wheels_damping_relaxation 0 4 1565 42 BulletWheel::set_wheels_damping_relaxation 0 1 433 71
/**
 * Sets the damping forces applied when the suspension relaxes.
 */
67
void BulletWheel::set_wheels_damping_relaxation(PN_stdfloat value);

1193 18 set_roll_influence 0 4 1565 31 BulletWheel::set_roll_influence 0 1 434 249
/**
 * Defines a scaling factor for roll forces that affect the chassis.  0.0
 * means no roll - the chassis won't ever flip over - while 1.0 means original
 * physical behaviour.  Basically, this allows moving the center of mass up
 * and down.
 */
56
void BulletWheel::set_roll_influence(PN_stdfloat value);

1194 16 set_wheel_radius 0 4 1565 29 BulletWheel::set_wheel_radius 0 1 435 33
/**
 * Sets the wheel radius.
 */
54
void BulletWheel::set_wheel_radius(PN_stdfloat value);

1195 12 set_steering 0 4 1565 25 BulletWheel::set_steering 0 1 436 35
/**
 * Sets the steering angle.
 */
50
void BulletWheel::set_steering(PN_stdfloat value);

1196 12 set_rotation 0 4 1565 25 BulletWheel::set_rotation 0 1 437 10
/**
 *
 */
50
void BulletWheel::set_rotation(PN_stdfloat value);

1197 18 set_delta_rotation 0 4 1565 31 BulletWheel::set_delta_rotation 0 1 438 10
/**
 *
 */
56
void BulletWheel::set_delta_rotation(PN_stdfloat value);

1198 16 set_engine_force 0 4 1565 29 BulletWheel::set_engine_force 0 1 439 69
/**
 * Defines how much force should be used to rotate the wheel.
 */
54
void BulletWheel::set_engine_force(PN_stdfloat value);

1199 9 set_brake 0 4 1565 22 BulletWheel::set_brake 0 1 440 10
/**
 *
 */
47
void BulletWheel::set_brake(PN_stdfloat value);

1200 13 set_skid_info 0 4 1565 26 BulletWheel::set_skid_info 0 1 441 10
/**
 *
 */
51
void BulletWheel::set_skid_info(PN_stdfloat value);

1201 27 set_wheels_suspension_force 0 4 1565 40 BulletWheel::set_wheels_suspension_force 0 1 442 10
/**
 *
 */
65
void BulletWheel::set_wheels_suspension_force(PN_stdfloat value);

1202 32 set_suspension_relative_velocity 0 4 1565 45 BulletWheel::set_suspension_relative_velocity 0 1 443 10
/**
 *
 */
70
void BulletWheel::set_suspension_relative_velocity(PN_stdfloat value);

1203 35 set_clipped_inv_connection_point_cs 0 4 1565 48 BulletWheel::set_clipped_inv_connection_point_cs 0 1 444 10
/**
 *
 */
73
void BulletWheel::set_clipped_inv_connection_point_cs(PN_stdfloat value);

1204 31 set_chassis_connection_point_cs 0 4 1565 44 BulletWheel::set_chassis_connection_point_cs 0 1 445 70
/**
 * Sets the point where the wheel is connected to the chassis.
 */
70
void BulletWheel::set_chassis_connection_point_cs(LPoint3 const &pos);

1205 22 set_wheel_direction_cs 0 4 1565 35 BulletWheel::set_wheel_direction_cs 0 1 446 92
/**
 * Sets the wheel's forward vector.  (Most likely orthogonal to the axle
 * vector.)
 */
62
void BulletWheel::set_wheel_direction_cs(LVector3 const &dir);

1206 17 set_wheel_axle_cs 0 4 1565 30 BulletWheel::set_wheel_axle_cs 0 1 447 51
/**
 * Determines the wheel axle normal vector.
 */
58
void BulletWheel::set_wheel_axle_cs(LVector3 const &axle);

1207 19 set_world_transform 0 4 1565 32 BulletWheel::set_world_transform 0 1 448 10
/**
 *
 */
59
void BulletWheel::set_world_transform(LMatrix4 const &mat);

1208 15 set_front_wheel 0 4 1565 28 BulletWheel::set_front_wheel 0 1 449 42
/**
 * Sets if the wheel is steerable.
 */
46
void BulletWheel::set_front_wheel(bool value);

1209 8 set_node 0 4 1565 21 BulletWheel::set_node 0 1 450 86
/**
 * Sets the PandaNode which representates the visual appearance of this wheel.
 */
44
void BulletWheel::set_node(PandaNode *node);

1210 26 get_suspension_rest_length 0 4 1565 39 BulletWheel::get_suspension_rest_length 0 1 451 83
/**
 * Returns the length of the suspension when the vehicle is standing still.
 */
64
PN_stdfloat BulletWheel::get_suspension_rest_length(void) const;

1211 24 get_suspension_stiffness 0 4 1565 37 BulletWheel::get_suspension_stiffness 0 1 452 51
/**
 * Returns the stiffness of the suspension.
 */
62
PN_stdfloat BulletWheel::get_suspension_stiffness(void) const;

1212 28 get_max_suspension_travel_cm 0 4 1565 41 BulletWheel::get_max_suspension_travel_cm 0 1 453 10
/**
 *
 */
66
PN_stdfloat BulletWheel::get_max_suspension_travel_cm(void) const;

1213 17 get_friction_slip 0 4 1565 30 BulletWheel::get_friction_slip 0 1 454 46
/**
 * Returns how slippery the tyres are.
 */
55
PN_stdfloat BulletWheel::get_friction_slip(void) const;

1214 24 get_max_suspension_force 0 4 1565 37 BulletWheel::get_max_suspension_force 0 1 455 72
/**
 * Returns the maximum force (weight) the suspension can handle.
 */
62
PN_stdfloat BulletWheel::get_max_suspension_force(void) const;

1215 30 get_wheels_damping_compression 0 4 1565 43 BulletWheel::get_wheels_damping_compression 0 1 456 70
/**
 * Returns the  damping applied to the compressing suspension.
 */
68
PN_stdfloat BulletWheel::get_wheels_damping_compression(void) const;

1216 29 get_wheels_damping_relaxation 0 4 1565 42 BulletWheel::get_wheels_damping_relaxation 0 1 457 66
/**
 * Returns the damping applied to the relaxing suspension.
 */
67
PN_stdfloat BulletWheel::get_wheels_damping_relaxation(void) const;

1217 18 get_roll_influence 0 4 1565 31 BulletWheel::get_roll_influence 0 1 458 90
/**
 * Returns the factor by which roll forces are scaled.  See
 * set_roll_influence.
 */
56
PN_stdfloat BulletWheel::get_roll_influence(void) const;

1218 16 get_wheel_radius 0 4 1565 29 BulletWheel::get_wheel_radius 0 1 459 36
/**
 * Returns the wheel radius.
 */
54
PN_stdfloat BulletWheel::get_wheel_radius(void) const;

1219 12 get_steering 0 4 1565 25 BulletWheel::get_steering 0 1 460 49
/**
 * Returns the steering angle in degrees.
 */
50
PN_stdfloat BulletWheel::get_steering(void) const;

1220 12 get_rotation 0 4 1565 25 BulletWheel::get_rotation 0 1 461 10
/**
 *
 */
50
PN_stdfloat BulletWheel::get_rotation(void) const;

1221 18 get_delta_rotation 0 4 1565 31 BulletWheel::get_delta_rotation 0 1 462 10
/**
 *
 */
56
PN_stdfloat BulletWheel::get_delta_rotation(void) const;

1222 16 get_engine_force 0 4 1565 29 BulletWheel::get_engine_force 0 1 463 71
/**
 * Returns the amount of accelleration force currently applied.
 */
54
PN_stdfloat BulletWheel::get_engine_force(void) const;

1223 9 get_brake 0 4 1565 22 BulletWheel::get_brake 0 1 464 65
/**
 * Returns the amount of braking force currently applied.
 */
47
PN_stdfloat BulletWheel::get_brake(void) const;

1224 13 get_skid_info 0 4 1565 26 BulletWheel::get_skid_info 0 1 465 10
/**
 *
 */
51
PN_stdfloat BulletWheel::get_skid_info(void) const;

1225 27 get_wheels_suspension_force 0 4 1565 40 BulletWheel::get_wheels_suspension_force 0 1 466 10
/**
 *
 */
65
PN_stdfloat BulletWheel::get_wheels_suspension_force(void) const;

1226 32 get_suspension_relative_velocity 0 4 1565 45 BulletWheel::get_suspension_relative_velocity 0 1 467 10
/**
 *
 */
70
PN_stdfloat BulletWheel::get_suspension_relative_velocity(void) const;

1227 35 get_clipped_inv_connection_point_cs 0 4 1565 48 BulletWheel::get_clipped_inv_connection_point_cs 0 1 468 10
/**
 *
 */
73
PN_stdfloat BulletWheel::get_clipped_inv_connection_point_cs(void) const;

1228 31 get_chassis_connection_point_cs 0 4 1565 44 BulletWheel::get_chassis_connection_point_cs 0 1 469 73
/**
 * Returns the point where the wheel is connected to the chassis.
 */
65
LPoint3 BulletWheel::get_chassis_connection_point_cs(void) const;

1229 22 get_wheel_direction_cs 0 4 1565 35 BulletWheel::get_wheel_direction_cs 0 1 470 70
/**
 * Returns the wheel's forward vector relative to the chassis.
 */
57
LVector3 BulletWheel::get_wheel_direction_cs(void) const;

1230 17 get_wheel_axle_cs 0 4 1565 30 BulletWheel::get_wheel_axle_cs 0 1 471 55
/**
 * Returns the normal vector of the wheel axle.
 */
52
LVector3 BulletWheel::get_wheel_axle_cs(void) const;

1231 19 get_world_transform 0 4 1565 32 BulletWheel::get_world_transform 0 1 472 10
/**
 *
 */
54
LMatrix4 BulletWheel::get_world_transform(void) const;

1232 14 is_front_wheel 0 4 1565 27 BulletWheel::is_front_wheel 0 1 473 46
/**
 * Determines if a wheel is steerable.
 */
45
bool BulletWheel::is_front_wheel(void) const;

1233 8 get_node 0 4 1565 21 BulletWheel::get_node 0 1 474 142
/**
 * Returns the PandaNode which representates the visual appearance of this
 * wheel, if such a representation has been set previously.
 */
45
PandaNode *BulletWheel::get_node(void) const;

1234 16 get_raycast_info 0 4 1565 29 BulletWheel::get_raycast_info 0 1 475 10
/**
 *
 */
65
BulletWheelRaycastInfo BulletWheel::get_raycast_info(void) const;

1235 23 ~BulletWheelRaycastInfo 0 516 1566 47 BulletWheelRaycastInfo::~BulletWheelRaycastInfo 0 0 10
/**
 *
 */
61
inline BulletWheelRaycastInfo::~BulletWheelRaycastInfo(void);

1236 13 is_in_contact 0 4 1566 37 BulletWheelRaycastInfo::is_in_contact 0 1 477 10
/**
 *
 */
55
bool BulletWheelRaycastInfo::is_in_contact(void) const;

1237 21 get_suspension_length 0 4 1566 45 BulletWheelRaycastInfo::get_suspension_length 0 1 478 10
/**
 *
 */
70
PN_stdfloat BulletWheelRaycastInfo::get_suspension_length(void) const;

1238 21 get_contact_normal_ws 0 4 1566 45 BulletWheelRaycastInfo::get_contact_normal_ws 0 1 479 10
/**
 *
 */
67
LVector3 BulletWheelRaycastInfo::get_contact_normal_ws(void) const;

1239 22 get_wheel_direction_ws 0 4 1566 46 BulletWheelRaycastInfo::get_wheel_direction_ws 0 1 480 10
/**
 *
 */
68
LVector3 BulletWheelRaycastInfo::get_wheel_direction_ws(void) const;

1240 17 get_wheel_axle_ws 0 4 1566 41 BulletWheelRaycastInfo::get_wheel_axle_ws 0 1 481 10
/**
 *
 */
63
LVector3 BulletWheelRaycastInfo::get_wheel_axle_ws(void) const;

1241 20 get_contact_point_ws 0 4 1566 44 BulletWheelRaycastInfo::get_contact_point_ws 0 1 482 10
/**
 *
 */
65
LPoint3 BulletWheelRaycastInfo::get_contact_point_ws(void) const;

1242 17 get_hard_point_ws 0 4 1566 41 BulletWheelRaycastInfo::get_hard_point_ws 0 1 483 10
/**
 *
 */
62
LPoint3 BulletWheelRaycastInfo::get_hard_point_ws(void) const;

1243 17 get_ground_object 0 4 1566 41 BulletWheelRaycastInfo::get_ground_object 0 1 484 10
/**
 *
 */
65
PandaNode *BulletWheelRaycastInfo::get_ground_object(void) const;

1244 22 BulletWheelRaycastInfo 0 260 1566 46 BulletWheelRaycastInfo::BulletWheelRaycastInfo 0 1 476 10
/**
 *
 */
96
inline BulletWheelRaycastInfo::BulletWheelRaycastInfo(BulletWheelRaycastInfo const &) = default;

1245 11 BulletWheel 0 260 1565 24 BulletWheel::BulletWheel 0 1 427 10
/**
 *
 */
63
inline BulletWheel::BulletWheel(BulletWheel const &) = default;

1246 14 get_class_type 0 4 1564 29 BulletVehicle::get_class_type 0 1 426 0
54
static TypeHandle BulletVehicle::get_class_type(void);

1247 11 BulletWorld 0 260 1567 24 BulletWorld::BulletWorld 0 1 485 10
/**
 *
 */
31
BulletWorld::BulletWorld(void);

1248 11 set_gravity 0 4 1567 24 BulletWorld::set_gravity 0 2 486 487 22
/**
 *
 */

/**
 *
 */
134
void BulletWorld::set_gravity(LVector3 const &gravity);
void BulletWorld::set_gravity(PN_stdfloat gx, PN_stdfloat gy, PN_stdfloat gz);

1249 11 get_gravity 0 4 1567 24 BulletWorld::get_gravity 0 1 488 10
/**
 *
 */
52
LVector3 const BulletWorld::get_gravity(void) const;

1250 10 do_physics 0 4 1567 23 BulletWorld::do_physics 0 1 489 10
/**
 *
 */
103
int BulletWorld::do_physics(PN_stdfloat dt, int max_substeps = 1, PN_stdfloat stepsize = (1.0 / 60.0));

1251 14 get_world_info 0 4 1567 27 BulletWorld::get_world_info 0 1 490 10
/**
 *
 */
58
BulletSoftBodyWorldInfo BulletWorld::get_world_info(void);

1252 14 set_debug_node 0 4 1567 27 BulletWorld::set_debug_node 0 1 491 30
// Debug

// Debug

/**
 *
 */
56
void BulletWorld::set_debug_node(BulletDebugNode *node);

1253 16 clear_debug_node 0 4 1567 29 BulletWorld::clear_debug_node 0 1 492 75
/**
 * Removes a debug node that has been assigned to this BulletWorld.
 */
41
void BulletWorld::clear_debug_node(void);

1254 14 get_debug_node 0 4 1567 27 BulletWorld::get_debug_node 0 1 493 10
/**
 *
 */
64
inline BulletDebugNode *BulletWorld::get_debug_node(void) const;

1255 14 has_debug_node 0 4 1567 27 BulletWorld::has_debug_node 0 1 494 10
/**
 *
 */
52
inline bool BulletWorld::has_debug_node(void) const;

1256 6 attach 0 4 1567 19 BulletWorld::attach 0 1 495 27
// AttachRemove

/**
 *
 */
46
void BulletWorld::attach(TypedObject *object);

1257 6 remove 0 4 1567 19 BulletWorld::remove 0 1 496 10
/**
 *
 */
46
void BulletWorld::remove(TypedObject *object);

1258 17 attach_constraint 0 4 1567 30 BulletWorld::attach_constraint 0 1 497 156
/**
 * Attaches a single constraint to a world.  Collision checks between the
 * linked objects will be disabled if the second parameter is set to TRUE.
 */
97
void BulletWorld::attach_constraint(BulletConstraint *constraint, bool linked_collision = false);

1259 14 get_num_ghosts 0 4 1567 27 BulletWorld::get_num_ghosts 0 1 498 61
// Ghost object

// Ghost object

// Ghost object

/**
 *
 */
44
int BulletWorld::get_num_ghosts(void) const;

1260 9 get_ghost 0 4 1567 22 BulletWorld::get_ghost 0 1 499 10
/**
 *
 */
55
BulletGhostNode *BulletWorld::get_ghost(int idx) const;

1261 20 get_num_rigid_bodies 0 4 1567 33 BulletWorld::get_num_rigid_bodies 0 1 500 55
// Rigid body

// Rigid body

// Rigid body

/**
 *
 */
50
int BulletWorld::get_num_rigid_bodies(void) const;

1262 14 get_rigid_body 0 4 1567 27 BulletWorld::get_rigid_body 0 1 501 10
/**
 *
 */
64
BulletRigidBodyNode *BulletWorld::get_rigid_body(int idx) const;

1263 19 get_num_soft_bodies 0 4 1567 32 BulletWorld::get_num_soft_bodies 0 1 502 52
// Soft body

// Soft body

// Soft body

/**
 *
 */
49
int BulletWorld::get_num_soft_bodies(void) const;

1264 13 get_soft_body 0 4 1567 26 BulletWorld::get_soft_body 0 1 503 10
/**
 *
 */
62
BulletSoftBodyNode *BulletWorld::get_soft_body(int idx) const;

1265 18 get_num_characters 0 4 1567 31 BulletWorld::get_num_characters 0 1 504 85
// Character controller

// Character controller

// Character controller

/**
 *
 */
48
int BulletWorld::get_num_characters(void) const;

1266 13 get_character 0 4 1567 26 BulletWorld::get_character 0 1 505 10
/**
 *
 */
77
BulletBaseCharacterControllerNode *BulletWorld::get_character(int idx) const;

1267 16 get_num_vehicles 0 4 1567 29 BulletWorld::get_num_vehicles 0 1 506 10
/**
 *
 */
46
int BulletWorld::get_num_vehicles(void) const;

1268 11 get_vehicle 0 4 1567 24 BulletWorld::get_vehicle 0 1 507 10
/**
 *
 */
55
BulletVehicle *BulletWorld::get_vehicle(int idx) const;

1269 19 get_num_constraints 0 4 1567 32 BulletWorld::get_num_constraints 0 1 508 55
// Constraint

// Constraint

// Constraint

/**
 *
 */
49
int BulletWorld::get_num_constraints(void) const;

1270 14 get_constraint 0 4 1567 27 BulletWorld::get_constraint 0 1 509 10
/**
 *
 */
61
BulletConstraint *BulletWorld::get_constraint(int idx) const;

1271 16 ray_test_closest 0 4 1567 29 BulletWorld::ray_test_closest 0 1 510 40
// Raycast and other queries

/**
 *
 */
159
BulletClosestHitRayResult BulletWorld::ray_test_closest(LPoint3 const &from_pos, LPoint3 const &to_pos, CollideMask const &mask = CollideMask::all_on()) const;

1272 12 ray_test_all 0 4 1567 25 BulletWorld::ray_test_all 0 1 511 10
/**
 *
 */
152
BulletAllHitsRayResult BulletWorld::ray_test_all(LPoint3 const &from_pos, LPoint3 const &to_pos, CollideMask const &mask = CollideMask::all_on()) const;

1273 18 sweep_test_closest 0 4 1567 31 BulletWorld::sweep_test_closest 0 1 512 203
/**
 * Performs a sweep test against all other shapes that match the given group
 * mask.  The provided shape must be a convex shape; it is an error to invoke
 * this method using a non-convex shape.
 */
226
BulletClosestHitSweepResult BulletWorld::sweep_test_closest(BulletShape *shape, TransformState const &from_ts, TransformState const &to_ts, CollideMask const &mask = CollideMask::all_on(), PN_stdfloat penetration = 0.0) const;

1274 12 contact_test 0 4 1567 25 BulletWorld::contact_test 0 1 513 386
/**
 * Performas a test for all bodies which are currently in contact with the
 * given body.  The test returns a BulletContactResult object which may
 * contain zero, one or more contacts.
 *
 * If the optional parameter use_filter is set to TRUE this test will consider
 * filter settings.  Otherwise all objects in contact are reported, no matter
 * if they would collide or not.
 */
94
BulletContactResult BulletWorld::contact_test(PandaNode *node, bool use_filter = false) const;

1275 17 contact_test_pair 0 4 1567 30 BulletWorld::contact_test_pair 0 1 514 181
/**
 * Performas a test if the two bodies given as parameters are in contact or
 * not.  The test returns a BulletContactResult object which may contain zero
 * or one contacts.
 */
93
BulletContactResult BulletWorld::contact_test_pair(PandaNode *node0, PandaNode *node1) const;

1276 11 filter_test 0 4 1567 24 BulletWorld::filter_test 0 1 515 105
/**
 * Performs a test if two bodies should collide or not, based on the collision
 * filter setting.
 */
72
bool BulletWorld::filter_test(PandaNode *node0, PandaNode *node1) const;

1277 17 get_num_manifolds 0 4 1567 30 BulletWorld::get_num_manifolds 0 1 516 52
// Manifolds

// Manifolds

// Manifolds

/**
 *
 */
47
int BulletWorld::get_num_manifolds(void) const;

1278 12 get_manifold 0 4 1567 25 BulletWorld::get_manifold 0 1 517 10
/**
 *
 */
67
BulletPersistentManifold *BulletWorld::get_manifold(int idx) const;

1279 14 __get_manifold 0 4 1567 27 BulletWorld::__get_manifold 0 1 518 10
/**
 *
 */
68
BulletPersistentManifold BulletWorld::__get_manifold(int idx) const;

1280 24 set_group_collision_flag 0 4 1567 37 BulletWorld::set_group_collision_flag 0 1 519 34
// Collision filtering

/**
 *
 */
98
void BulletWorld::set_group_collision_flag(unsigned int group1, unsigned int group2, bool enable);

1281 24 get_group_collision_flag 0 4 1567 37 BulletWorld::get_group_collision_flag 0 1 520 10
/**
 *
 */
91
bool BulletWorld::get_group_collision_flag(unsigned int group1, unsigned int group2) const;

1282 26 set_force_update_all_aabbs 0 4 1567 39 BulletWorld::set_force_update_all_aabbs 0 1 521 10
/**
 *
 */
57
void BulletWorld::set_force_update_all_aabbs(bool force);

1283 26 get_force_update_all_aabbs 0 4 1567 39 BulletWorld::get_force_update_all_aabbs 0 1 522 10
/**
 *
 */
57
bool BulletWorld::get_force_update_all_aabbs(void) const;

1284 26 set_contact_added_callback 0 4 1567 39 BulletWorld::set_contact_added_callback 0 1 523 24
// Callbacks

/**
 *
 */
66
void BulletWorld::set_contact_added_callback(CallbackObject *obj);

1285 28 clear_contact_added_callback 0 4 1567 41 BulletWorld::clear_contact_added_callback 0 1 524 10
/**
 *
 */
53
void BulletWorld::clear_contact_added_callback(void);

1286 17 set_tick_callback 0 4 1567 30 BulletWorld::set_tick_callback 0 1 525 10
/**
 *
 */
82
void BulletWorld::set_tick_callback(CallbackObject *obj, bool is_pretick = false);

1287 19 clear_tick_callback 0 4 1567 32 BulletWorld::clear_tick_callback 0 1 526 10
/**
 *
 */
44
void BulletWorld::clear_tick_callback(void);

1288 19 set_filter_callback 0 4 1567 32 BulletWorld::set_filter_callback 0 1 527 10
/**
 *
 */
59
void BulletWorld::set_filter_callback(CallbackObject *obj);

1289 21 clear_filter_callback 0 4 1567 34 BulletWorld::clear_filter_callback 0 1 528 10
/**
 *
 */
46
void BulletWorld::clear_filter_callback(void);

1290 25 ~BulletPersistentManifold 0 516 1576 51 BulletPersistentManifold::~BulletPersistentManifold 0 0 10
/**
 *
 */
65
inline BulletPersistentManifold::~BulletPersistentManifold(void);

1291 9 get_node0 0 4 1576 35 BulletPersistentManifold::get_node0 0 1 542 10
/**
 *
 */
53
PandaNode *BulletPersistentManifold::get_node0(void);

1292 9 get_node1 0 4 1576 35 BulletPersistentManifold::get_node1 0 1 543 10
/**
 *
 */
53
PandaNode *BulletPersistentManifold::get_node1(void);

1293 23 get_num_manifold_points 0 4 1576 49 BulletPersistentManifold::get_num_manifold_points 0 1 544 10
/**
 *
 */
66
int BulletPersistentManifold::get_num_manifold_points(void) const;

1294 18 get_manifold_point 0 4 1576 44 BulletPersistentManifold::get_manifold_point 0 1 545 10
/**
 *
 */
81
BulletManifoldPoint *BulletPersistentManifold::get_manifold_point(int idx) const;

1295 20 __get_manifold_point 0 4 1576 46 BulletPersistentManifold::__get_manifold_point 0 1 546 10
/**
 *
 */
82
BulletManifoldPoint BulletPersistentManifold::__get_manifold_point(int idx) const;

1296 30 get_contact_breaking_threshold 0 4 1576 56 BulletPersistentManifold::get_contact_breaking_threshold 0 1 547 10
/**
 *
 */
81
PN_stdfloat BulletPersistentManifold::get_contact_breaking_threshold(void) const;

1297 32 get_contact_processing_threshold 0 4 1576 58 BulletPersistentManifold::get_contact_processing_threshold 0 1 548 10
/**
 *
 */
83
PN_stdfloat BulletPersistentManifold::get_contact_processing_threshold(void) const;

1298 14 clear_manifold 0 4 1576 40 BulletPersistentManifold::clear_manifold 0 1 549 10
/**
 *
 */
52
void BulletPersistentManifold::clear_manifold(void);

1299 24 BulletPersistentManifold 0 260 1576 50 BulletPersistentManifold::BulletPersistentManifold 0 1 541 10
/**
 *
 */
102
inline BulletPersistentManifold::BulletPersistentManifold(BulletPersistentManifold const &) = default;

1300 12 attach_ghost 0 4 1567 25 BulletWorld::attach_ghost 0 1 529 98
// Deprecated methods, will be removed soon

/**
 * @deprecated Please use BulletWorld::attach
 */
54
void BulletWorld::attach_ghost(BulletGhostNode *node);

1301 12 remove_ghost 0 4 1567 25 BulletWorld::remove_ghost 0 1 530 53
/**
 * @deprecated Please use BulletWorld::remove
 */
54
void BulletWorld::remove_ghost(BulletGhostNode *node);

1302 17 attach_rigid_body 0 4 1567 30 BulletWorld::attach_rigid_body 0 1 531 53
/**
 * @deprecated Please use BulletWorld::attach
 */
63
void BulletWorld::attach_rigid_body(BulletRigidBodyNode *node);

1303 17 remove_rigid_body 0 4 1567 30 BulletWorld::remove_rigid_body 0 1 532 53
/**
 * @deprecated Please use BulletWorld::remove
 */
63
void BulletWorld::remove_rigid_body(BulletRigidBodyNode *node);

1304 16 attach_soft_body 0 4 1567 29 BulletWorld::attach_soft_body 0 1 533 53
/**
 * @deprecated Please use BulletWorld::attach
 */
61
void BulletWorld::attach_soft_body(BulletSoftBodyNode *node);

1305 16 remove_soft_body 0 4 1567 29 BulletWorld::remove_soft_body 0 1 534 53
/**
 * @deprecated Please use BulletWorld::remove
 */
61
void BulletWorld::remove_soft_body(BulletSoftBodyNode *node);

1306 16 attach_character 0 4 1567 29 BulletWorld::attach_character 0 1 535 53
/**
 * @deprecated Please use BulletWorld::attach
 */
76
void BulletWorld::attach_character(BulletBaseCharacterControllerNode *node);

1307 16 remove_character 0 4 1567 29 BulletWorld::remove_character 0 1 536 53
/**
 * @deprecated Please use BulletWorld::remove
 */
76
void BulletWorld::remove_character(BulletBaseCharacterControllerNode *node);

1308 14 attach_vehicle 0 4 1567 27 BulletWorld::attach_vehicle 0 1 537 53
/**
 * @deprecated Please use BulletWorld::attach
 */
57
void BulletWorld::attach_vehicle(BulletVehicle *vehicle);

1309 14 remove_vehicle 0 4 1567 27 BulletWorld::remove_vehicle 0 1 538 53
/**
 * @deprecated Please use BulletWorld::remove
 */
57
void BulletWorld::remove_vehicle(BulletVehicle *vehicle);

1310 17 remove_constraint 0 4 1567 30 BulletWorld::remove_constraint 0 1 539 53
/**
 * @deprecated Please use BulletWorld::remove
 */
66
void BulletWorld::remove_constraint(BulletConstraint *constraint);

1311 14 get_class_type 0 4 1567 27 BulletWorld::get_class_type 0 1 540 0
52
static TypeHandle BulletWorld::get_class_type(void);

1312 21 BulletConvexHullShape 0 260 1577 44 BulletConvexHullShape::BulletConvexHullShape 0 2 550 551 22
/**
 *
 */

/**
 *
 */
132
BulletConvexHullShape::BulletConvexHullShape(void);
BulletConvexHullShape::BulletConvexHullShape(BulletConvexHullShape const &copy);

1313 9 add_point 0 4 1577 32 BulletConvexHullShape::add_point 0 1 552 10
/**
 *
 */
56
void BulletConvexHullShape::add_point(LPoint3 const &p);

1314 9 add_array 0 4 1577 32 BulletConvexHullShape::add_array 0 1 553 10
/**
 *
 */
67
void BulletConvexHullShape::add_array(PTA_LVecBase3 const &points);

1315 8 add_geom 0 4 1577 31 BulletConvexHullShape::add_geom 0 1 554 10
/**
 *
 */
115
void BulletConvexHullShape::add_geom(Geom const *geom, TransformState const *ts = TransformState::make_identity());

1316 14 get_class_type 0 4 1577 37 BulletConvexHullShape::get_class_type 0 1 555 0
62
static TypeHandle BulletConvexHullShape::get_class_type(void);

1317 27 BulletConvexPointCloudShape 0 260 1578 56 BulletConvexPointCloudShape::BulletConvexPointCloudShape 0 3 556 557 558 74
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
347
explicit BulletConvexPointCloudShape::BulletConvexPointCloudShape(PTA_LVecBase3 const &points, LVecBase3 scale = LVecBase3(1.0));
explicit BulletConvexPointCloudShape::BulletConvexPointCloudShape(Geom const *geom, LVecBase3 scale = LVecBase3(1.0));
BulletConvexPointCloudShape::BulletConvexPointCloudShape(BulletConvexPointCloudShape const &copy);

1318 14 get_num_points 0 4 1578 43 BulletConvexPointCloudShape::get_num_points 0 1 559 10
/**
 *
 */
60
int BulletConvexPointCloudShape::get_num_points(void) const;

1319 14 get_class_type 0 4 1578 43 BulletConvexPointCloudShape::get_class_type 0 1 560 0
68
static TypeHandle BulletConvexPointCloudShape::get_class_type(void);

1320 19 BulletCylinderShape 0 260 1579 40 BulletCylinderShape::BulletCylinderShape 0 3 561 562 563 74
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */

/**
 *
 */
298
explicit BulletCylinderShape::BulletCylinderShape(PN_stdfloat radius, PN_stdfloat height, BulletUpAxis up = ::Z_up);
explicit BulletCylinderShape::BulletCylinderShape(LVector3 const &half_extents, BulletUpAxis up = ::Z_up);
BulletCylinderShape::BulletCylinderShape(BulletCylinderShape const &copy);

1321 10 get_radius 0 4 1579 31 BulletCylinderShape::get_radius 0 1 564 10
/**
 *
 */
56
PN_stdfloat BulletCylinderShape::get_radius(void) const;

1322 31 get_half_extents_without_margin 0 4 1579 52 BulletCylinderShape::get_half_extents_without_margin 0 1 565 10
/**
 *
 */
75
LVecBase3 BulletCylinderShape::get_half_extents_without_margin(void) const;

1323 28 get_half_extents_with_margin 0 4 1579 49 BulletCylinderShape::get_half_extents_with_margin 0 1 566 10
/**
 *
 */
72
LVecBase3 BulletCylinderShape::get_half_extents_with_margin(void) const;

1324 14 get_class_type 0 4 1579 35 BulletCylinderShape::get_class_type 0 1 567 0
60
static TypeHandle BulletCylinderShape::get_class_type(void);

1325 24 BulletFilterCallbackData 0 260 1580 50 BulletFilterCallbackData::BulletFilterCallbackData 0 1 568 10
/**
 *
 */
94
inline BulletFilterCallbackData::BulletFilterCallbackData(PandaNode *node0, PandaNode *node1);

1326 10 get_node_0 0 4 1580 36 BulletFilterCallbackData::get_node_0 0 1 569 10
/**
 *
 */
67
inline PandaNode *BulletFilterCallbackData::get_node_0(void) const;

1327 10 get_node_1 0 4 1580 36 BulletFilterCallbackData::get_node_1 0 1 570 10
/**
 *
 */
67
inline PandaNode *BulletFilterCallbackData::get_node_1(void) const;

1328 11 set_collide 0 4 1580 37 BulletFilterCallbackData::set_collide 0 1 571 10
/**
 *
 */
64
inline void BulletFilterCallbackData::set_collide(bool collide);

1329 11 get_collide 0 4 1580 37 BulletFilterCallbackData::get_collide 0 1 572 10
/**
 *
 */
62
inline bool BulletFilterCallbackData::get_collide(void) const;

1330 14 get_class_type 0 4 1580 40 BulletFilterCallbackData::get_class_type 0 1 573 0
65
static TypeHandle BulletFilterCallbackData::get_class_type(void);

1331 25 ~BulletFilterCallbackData 0 516 1580 51 BulletFilterCallbackData::~BulletFilterCallbackData 0 0 0
58
BulletFilterCallbackData::~BulletFilterCallbackData(void);

1332 26 BulletRotationalLimitMotor 0 260 1581 54 BulletRotationalLimitMotor::BulletRotationalLimitMotor 0 1 574 22
/**
 *
 */

/**
 *
 */
95
BulletRotationalLimitMotor::BulletRotationalLimitMotor(BulletRotationalLimitMotor const &copy);

1333 27 ~BulletRotationalLimitMotor 0 516 1581 55 BulletRotationalLimitMotor::~BulletRotationalLimitMotor 0 0 10
/**
 *
 */
69
inline BulletRotationalLimitMotor::~BulletRotationalLimitMotor(void);

1334 17 set_motor_enabled 0 4 1581 45 BulletRotationalLimitMotor::set_motor_enabled 0 1 575 10
/**
 *
 */
64
void BulletRotationalLimitMotor::set_motor_enabled(bool enable);

1335 13 set_low_limit 0 4 1581 41 BulletRotationalLimitMotor::set_low_limit 0 1 576 10
/**
 *
 */
66
void BulletRotationalLimitMotor::set_low_limit(PN_stdfloat limit);

1336 14 set_high_limit 0 4 1581 42 BulletRotationalLimitMotor::set_high_limit 0 1 577 10
/**
 *
 */
67
void BulletRotationalLimitMotor::set_high_limit(PN_stdfloat limit);

1337 19 set_target_velocity 0 4 1581 47 BulletRotationalLimitMotor::set_target_velocity 0 1 578 10
/**
 *
 */
75
void BulletRotationalLimitMotor::set_target_velocity(PN_stdfloat velocity);

1338 19 set_max_motor_force 0 4 1581 47 BulletRotationalLimitMotor::set_max_motor_force 0 1 579 10
/**
 *
 */
72
void BulletRotationalLimitMotor::set_max_motor_force(PN_stdfloat force);

1339 19 set_max_limit_force 0 4 1581 47 BulletRotationalLimitMotor::set_max_limit_force 0 1 580 10
/**
 *
 */
72
void BulletRotationalLimitMotor::set_max_limit_force(PN_stdfloat force);

1340 11 set_damping 0 4 1581 39 BulletRotationalLimitMotor::set_damping 0 1 581 10
/**
 *
 */
66
void BulletRotationalLimitMotor::set_damping(PN_stdfloat damping);

1341 12 set_softness 0 4 1581 40 BulletRotationalLimitMotor::set_softness 0 1 582 10
/**
 *
 */
68
void BulletRotationalLimitMotor::set_softness(PN_stdfloat softness);

1342 10 set_bounce 0 4 1581 38 BulletRotationalLimitMotor::set_bounce 0 1 583 10
/**
 *
 */
64
void BulletRotationalLimitMotor::set_bounce(PN_stdfloat bounce);

1343 14 set_normal_cfm 0 4 1581 42 BulletRotationalLimitMotor::set_normal_cfm 0 1 584 10
/**
 *
 */
65
void BulletRotationalLimitMotor::set_normal_cfm(PN_stdfloat cfm);

1344 12 set_stop_cfm 0 4 1581 40 BulletRotationalLimitMotor::set_stop_cfm 0 1 585 10
/**
 *
 */
63
void BulletRotationalLimitMotor::set_stop_cfm(PN_stdfloat cfm);

1345 12 set_stop_erp 0 4 1581 40 BulletRotationalLimitMotor::set_stop_erp 0 1 586 10
/**
 *
 */
63
void BulletRotationalLimitMotor::set_stop_erp(PN_stdfloat erp);

1346 10 is_limited 0 4 1581 38 BulletRotationalLimitMotor::is_limited 0 1 587 10
/**
 *
 */
56
bool BulletRotationalLimitMotor::is_limited(void) const;

1347 17 get_motor_enabled 0 4 1581 45 BulletRotationalLimitMotor::get_motor_enabled 0 1 588 10
/**
 *
 */
63
bool BulletRotationalLimitMotor::get_motor_enabled(void) const;

1348 17 get_current_limit 0 4 1581 45 BulletRotationalLimitMotor::get_current_limit 0 1 589 98
/**
 * Retrieves the current value of angle: 0 = free, 1 = at low limit, 2 = at
 * high limit.
 */
62
int BulletRotationalLimitMotor::get_current_limit(void) const;

1349 17 get_current_error 0 4 1581 45 BulletRotationalLimitMotor::get_current_error 0 1 590 10
/**
 *
 */
70
PN_stdfloat BulletRotationalLimitMotor::get_current_error(void) const;

1350 20 get_current_position 0 4 1581 48 BulletRotationalLimitMotor::get_current_position 0 1 591 10
/**
 *
 */
73
PN_stdfloat BulletRotationalLimitMotor::get_current_position(void) const;

1351 23 get_accumulated_impulse 0 4 1581 51 BulletRotationalLimitMotor::get_accumulated_impulse 0 1 592 10
/**
 *
 */
76
PN_stdfloat BulletRotationalLimitMotor::get_accumulated_impulse(void) const;

1352 29 BulletTranslationalLimitMotor 0 260 1582 60 BulletTranslationalLimitMotor::BulletTranslationalLimitMotor 0 1 593 22
/**
 *
 */

/**
 *
 */
104
BulletTranslationalLimitMotor::BulletTranslationalLimitMotor(BulletTranslationalLimitMotor const &copy);

1353 30 ~BulletTranslationalLimitMotor 0 516 1582 61 BulletTranslationalLimitMotor::~BulletTranslationalLimitMotor 0 0 10
/**
 *
 */
75
inline BulletTranslationalLimitMotor::~BulletTranslationalLimitMotor(void);

1354 17 set_motor_enabled 0 4 1582 48 BulletTranslationalLimitMotor::set_motor_enabled 0 1 594 10
/**
 *
 */
77
void BulletTranslationalLimitMotor::set_motor_enabled(int axis, bool enable);

1355 13 set_low_limit 0 4 1582 44 BulletTranslationalLimitMotor::set_low_limit 0 1 595 10
/**
 *
 */
74
void BulletTranslationalLimitMotor::set_low_limit(LVecBase3 const &limit);

1356 14 set_high_limit 0 4 1582 45 BulletTranslationalLimitMotor::set_high_limit 0 1 596 10
/**
 *
 */
75
void BulletTranslationalLimitMotor::set_high_limit(LVecBase3 const &limit);

1357 19 set_target_velocity 0 4 1582 50 BulletTranslationalLimitMotor::set_target_velocity 0 1 597 10
/**
 *
 */
83
void BulletTranslationalLimitMotor::set_target_velocity(LVecBase3 const &velocity);

1358 19 set_max_motor_force 0 4 1582 50 BulletTranslationalLimitMotor::set_max_motor_force 0 1 598 10
/**
 *
 */
80
void BulletTranslationalLimitMotor::set_max_motor_force(LVecBase3 const &force);

1359 11 set_damping 0 4 1582 42 BulletTranslationalLimitMotor::set_damping 0 1 599 10
/**
 *
 */
69
void BulletTranslationalLimitMotor::set_damping(PN_stdfloat damping);

1360 12 set_softness 0 4 1582 43 BulletTranslationalLimitMotor::set_softness 0 1 600 10
/**
 *
 */
71
void BulletTranslationalLimitMotor::set_softness(PN_stdfloat softness);

1361 15 set_restitution 0 4 1582 46 BulletTranslationalLimitMotor::set_restitution 0 1 601 10
/**
 *
 */
77
void BulletTranslationalLimitMotor::set_restitution(PN_stdfloat restitution);

1362 14 set_normal_cfm 0 4 1582 45 BulletTranslationalLimitMotor::set_normal_cfm 0 1 602 10
/**
 *
 */
73
void BulletTranslationalLimitMotor::set_normal_cfm(LVecBase3 const &cfm);

1363 12 set_stop_erp 0 4 1582 43 BulletTranslationalLimitMotor::set_stop_erp 0 1 603 10
/**
 *
 */
71
void BulletTranslationalLimitMotor::set_stop_erp(LVecBase3 const &erp);

1364 12 set_stop_cfm 0 4 1582 43 BulletTranslationalLimitMotor::set_stop_cfm 0 1 604 10
/**
 *
 */
71
void BulletTranslationalLimitMotor::set_stop_cfm(LVecBase3 const &cfm);

1365 10 is_limited 0 4 1582 41 BulletTranslationalLimitMotor::is_limited 0 1 605 10
/**
 *
 */
63
bool BulletTranslationalLimitMotor::is_limited(int axis) const;

1366 17 get_motor_enabled 0 4 1582 48 BulletTranslationalLimitMotor::get_motor_enabled 0 1 606 10
/**
 *
 */
70
bool BulletTranslationalLimitMotor::get_motor_enabled(int axis) const;

1367 17 get_current_limit 0 4 1582 48 BulletTranslationalLimitMotor::get_current_limit 0 1 607 98
/**
 * Retrieves the current value of angle: 0 = free, 1 = at low limit, 2 = at
 * high limit.
 */
69
int BulletTranslationalLimitMotor::get_current_limit(int axis) const;

1368 17 get_current_error 0 4 1582 48 BulletTranslationalLimitMotor::get_current_error 0 1 608 10
/**
 *
 */
70
LVector3 BulletTranslationalLimitMotor::get_current_error(void) const;

1369 16 get_current_diff 0 4 1582 47 BulletTranslationalLimitMotor::get_current_diff 0 1 609 10
/**
 *
 */
68
LPoint3 BulletTranslationalLimitMotor::get_current_diff(void) const;

1370 23 get_accumulated_impulse 0 4 1582 54 BulletTranslationalLimitMotor::get_accumulated_impulse 0 1 610 10
/**
 *
 */
76
LVector3 BulletTranslationalLimitMotor::get_accumulated_impulse(void) const;

1371 23 BulletGenericConstraint 0 260 1583 48 BulletGenericConstraint::BulletGenericConstraint 0 2 611 612 22
/**
 *
 */

/**
 *
 */
351
explicit BulletGenericConstraint::BulletGenericConstraint(BulletRigidBodyNode const *node_a, TransformState const *frame_a, bool use_frame_a);
explicit BulletGenericConstraint::BulletGenericConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, TransformState const *frame_a, TransformState const *frame_b, bool use_frame_a);

1372 8 get_axis 0 4 1583 33 BulletGenericConstraint::get_axis 0 1 613 23
// Geometry

/**
 *
 */
59
LVector3 BulletGenericConstraint::get_axis(int axis) const;

1373 9 get_pivot 0 4 1583 34 BulletGenericConstraint::get_pivot 0 1 614 10
/**
 *
 */
63
PN_stdfloat BulletGenericConstraint::get_pivot(int axis) const;

1374 9 get_angle 0 4 1583 34 BulletGenericConstraint::get_angle 0 1 615 10
/**
 *
 */
63
PN_stdfloat BulletGenericConstraint::get_angle(int axis) const;

1375 16 set_linear_limit 0 4 1583 41 BulletGenericConstraint::set_linear_limit 0 1 616 20
// Limit

/**
 *
 */
92
void BulletGenericConstraint::set_linear_limit(int axis, PN_stdfloat low, PN_stdfloat high);

1376 17 set_angular_limit 0 4 1583 42 BulletGenericConstraint::set_angular_limit 0 1 617 10
/**
 *
 */
93
void BulletGenericConstraint::set_angular_limit(int axis, PN_stdfloat low, PN_stdfloat high);

1377 26 get_rotational_limit_motor 0 4 1583 51 BulletGenericConstraint::get_rotational_limit_motor 0 1 618 21
// Motors

/**
 *
 */
89
BulletRotationalLimitMotor BulletGenericConstraint::get_rotational_limit_motor(int axis);

1378 29 get_translational_limit_motor 0 4 1583 54 BulletGenericConstraint::get_translational_limit_motor 0 1 619 10
/**
 *
 */
91
BulletTranslationalLimitMotor BulletGenericConstraint::get_translational_limit_motor(void);

1379 10 set_frames 0 4 1583 35 BulletGenericConstraint::set_frames 0 1 620 21
// Frames

/**
 *
 */
97
void BulletGenericConstraint::set_frames(TransformState const *ts_a, TransformState const *ts_b);

1380 11 get_frame_a 0 4 1583 36 BulletGenericConstraint::get_frame_a 0 1 621 10
/**
 *
 */
82
ConstPointerTo< TransformState > BulletGenericConstraint::get_frame_a(void) const;

1381 11 get_frame_b 0 4 1583 36 BulletGenericConstraint::get_frame_b 0 1 622 10
/**
 *
 */
82
ConstPointerTo< TransformState > BulletGenericConstraint::get_frame_b(void) const;

1382 14 get_class_type 0 4 1583 39 BulletGenericConstraint::get_class_type 0 1 623 0
64
static TypeHandle BulletGenericConstraint::get_class_type(void);

1383 22 BulletHeightfieldShape 0 260 1584 46 BulletHeightfieldShape::BulletHeightfieldShape 0 3 624 625 626 579
/**
 * Only used by make_from_bam
 */

/**
 * @brief Creates a collision shape suited for terrains from a rectangular image.
 * @details Stores the image's brightness values in a vector Bullet can use, 
 *   while rotating it 90 degrees to the right.
 */

/**
 * @brief Creates a collision shape suited for terrains from a rectangular texture.
 * @details Alternative constructor intended for use with ShaderTerrainMesh. This will
 *   do bilinear sampling at the corners of all texels. Also works with textures 
 *   that are non-power-of-two and/or rectangular.
 */

/**
 *
 */
334
explicit BulletHeightfieldShape::BulletHeightfieldShape(PNMImage const &image, PN_stdfloat max_height, BulletUpAxis up = ::Z_up);
explicit BulletHeightfieldShape::BulletHeightfieldShape(Texture *tex, PN_stdfloat max_height, BulletUpAxis up = ::Z_up);
BulletHeightfieldShape::BulletHeightfieldShape(BulletHeightfieldShape const &copy);

1384 27 set_use_diamond_subdivision 0 4 1584 51 BulletHeightfieldShape::set_use_diamond_subdivision 0 1 627 10
/**
 *
 */
75
void BulletHeightfieldShape::set_use_diamond_subdivision(bool flag = true);

1385 14 get_class_type 0 4 1584 38 BulletHeightfieldShape::get_class_type 0 1 628 0
63
static TypeHandle BulletHeightfieldShape::get_class_type(void);

1386 21 from_collision_solids 0 4 1585 35 BulletHelper::from_collision_solids 0 1 631 31
// Collision shapes

/**
 *
 */
96
static NodePathCollection BulletHelper::from_collision_solids(NodePath &np, bool clear = false);

1387 12 get_sb_index 0 4 1585 26 BulletHelper::get_sb_index 0 1 632 48
// Internal names

// Internal names

/**
 *
 */
73
static inline PointerTo< InternalName > BulletHelper::get_sb_index(void);

1388 11 get_sb_flip 0 4 1585 25 BulletHelper::get_sb_flip 0 1 633 10
/**
 *
 */
72
static inline PointerTo< InternalName > BulletHelper::get_sb_flip(void);

1389 19 add_sb_index_column 0 4 1585 33 BulletHelper::add_sb_index_column 0 1 634 31
// Geom vertex data

/**
 *
 */
108
static ConstPointerTo< GeomVertexFormat > BulletHelper::add_sb_index_column(GeomVertexFormat const *format);

1390 18 add_sb_flip_column 0 4 1585 32 BulletHelper::add_sb_flip_column 0 1 635 10
/**
 *
 */
107
static ConstPointerTo< GeomVertexFormat > BulletHelper::add_sb_flip_column(GeomVertexFormat const *format);

1391 20 make_geom_from_faces 0 4 1585 34 BulletHelper::make_geom_from_faces 0 1 636 25
// Geom utils

/**
 *
 */
152
static PointerTo< Geom > BulletHelper::make_geom_from_faces(BulletSoftBodyNode *node, GeomVertexFormat const *format = nullptr, bool two_sided = false);

1392 20 make_geom_from_links 0 4 1585 34 BulletHelper::make_geom_from_links 0 1 637 10
/**
 *
 */
128
static PointerTo< Geom > BulletHelper::make_geom_from_links(BulletSoftBodyNode *node, GeomVertexFormat const *format = nullptr);

1393 24 make_texcoords_for_patch 0 4 1585 38 BulletHelper::make_texcoords_for_patch 0 1 638 10
/**
 *
 */
83
static void BulletHelper::make_texcoords_for_patch(Geom *geom, int resx, int resy);

1394 12 BulletHelper 0 260 1585 26 BulletHelper::BulletHelper 0 2 629 630 0
117
inline BulletHelper::BulletHelper(void) = default;
inline BulletHelper::BulletHelper(BulletHelper const &) = default;

1395 13 ~BulletHelper 0 516 1585 27 BulletHelper::~BulletHelper 0 0 0
34
BulletHelper::~BulletHelper(void);

1396 21 BulletHingeConstraint 0 260 1587 44 BulletHingeConstraint::BulletHingeConstraint 0 4 639 640 641 642 709
/**
 * Creates a hinge constraint which connects one rigid body with some fixe
 * dpoint in the world.
 */

/**
 * Constructs a hinge constraint which connects two rigid bodies.
 */

/**
 * Creates a hinge constraint in the same way as the other constructor, but
 * uses the world as second body so that node_a is fixed to some point in mid-
 * air for example.
 */

/**
 * Creates a hinge connecting node_a to node_b.  The pivot point is the point
 * at which the body is fixed to the constraint.  In other words: It specifies
 * where on each body the rotation axis should be.  This axis is specified
 * using axis_a and axis_b.  Remember, everything is specified in the bodies
 * own coordinate system!
 */
761
explicit BulletHingeConstraint::BulletHingeConstraint(BulletRigidBodyNode const *node_a, LPoint3 const &pivot_a, LVector3 const &axis_a, bool use_frame_a = false);
explicit BulletHingeConstraint::BulletHingeConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, LPoint3 const &pivot_a, LPoint3 const &pivot_b, LVector3 const &axis_a, LVector3 const &axis_b, bool use_frame_a = false);
explicit BulletHingeConstraint::BulletHingeConstraint(BulletRigidBodyNode const *node_a, TransformState const *ts_a, bool use_frame_a = false);
explicit BulletHingeConstraint::BulletHingeConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, TransformState const *ts_a, TransformState const *ts_b, bool use_frame_a = false);

1397 15 get_hinge_angle 0 4 1587 38 BulletHingeConstraint::get_hinge_angle 0 1 643 66
/**
 * Returns the angle between node_a and node_b in degrees.
 */
57
PN_stdfloat BulletHingeConstraint::get_hinge_angle(void);

1398 15 get_lower_limit 0 4 1587 38 BulletHingeConstraint::get_lower_limit 0 1 644 54
/**
 * Returns the lower angular limit in degrees.
 */
63
PN_stdfloat BulletHingeConstraint::get_lower_limit(void) const;

1399 15 get_upper_limit 0 4 1587 38 BulletHingeConstraint::get_upper_limit 0 1 645 54
/**
 * Returns the upper angular limit in degrees.
 */
63
PN_stdfloat BulletHingeConstraint::get_upper_limit(void) const;

1400 16 get_angular_only 0 4 1587 39 BulletHingeConstraint::get_angular_only 0 1 646 10
/**
 *
 */
57
bool BulletHingeConstraint::get_angular_only(void) const;

1401 16 set_angular_only 0 4 1587 39 BulletHingeConstraint::set_angular_only 0 1 647 10
/**
 *
 */
57
void BulletHingeConstraint::set_angular_only(bool value);

1402 9 set_limit 0 4 1587 32 BulletHingeConstraint::set_limit 0 1 648 65
/**
 * Sets the lower and upper rotational limits in degrees.
 */
171
void BulletHingeConstraint::set_limit(PN_stdfloat low, PN_stdfloat high, PN_stdfloat softness = 0.9, PN_stdfloat bias = 0.30000000000000007, PN_stdfloat relaxation = 1.0);

1403 8 set_axis 0 4 1587 31 BulletHingeConstraint::set_axis 0 1 649 63
/**
 * Sets the hinge's rotation axis in world coordinates.
 */
59
void BulletHingeConstraint::set_axis(LVector3 const &axis);

1404 20 enable_angular_motor 0 4 1587 43 BulletHingeConstraint::enable_angular_motor 0 1 650 261
/**
 * Applies an impulse to the constraint so that the angle changes at
 * target_velocity where max_impulse is the maximum impulse that is used for
 * achieving the specified velocity.
 *
 * Note that the target_velocity is in radians/second, not degrees.
 */
116
void BulletHingeConstraint::enable_angular_motor(bool enable, PN_stdfloat target_velocity, PN_stdfloat max_impulse);

1405 12 enable_motor 0 4 1587 35 BulletHingeConstraint::enable_motor 0 1 651 10
/**
 *
 */
54
void BulletHingeConstraint::enable_motor(bool enable);

1406 21 set_max_motor_impulse 0 4 1587 44 BulletHingeConstraint::set_max_motor_impulse 0 1 652 96
/**
 * Sets the maximum impulse used to achieve the velocity set in
 * enable_angular_motor.
 */
75
void BulletHingeConstraint::set_max_motor_impulse(PN_stdfloat max_impulse);

1407 16 set_motor_target 0 4 1587 39 BulletHingeConstraint::set_motor_target 0 2 653 654 22
/**
 *
 */

/**
 *
 */
174
void BulletHingeConstraint::set_motor_target(LQuaternion const &quat, PN_stdfloat dt);
void BulletHingeConstraint::set_motor_target(PN_stdfloat target_angle, PN_stdfloat dt);

1408 10 set_frames 0 4 1587 33 BulletHingeConstraint::set_frames 0 1 655 10
/**
 *
 */
95
void BulletHingeConstraint::set_frames(TransformState const *ts_a, TransformState const *ts_b);

1409 11 get_frame_a 0 4 1587 34 BulletHingeConstraint::get_frame_a 0 1 656 10
/**
 *
 */
80
ConstPointerTo< TransformState > BulletHingeConstraint::get_frame_a(void) const;

1410 11 get_frame_b 0 4 1587 34 BulletHingeConstraint::get_frame_b 0 1 657 10
/**
 *
 */
80
ConstPointerTo< TransformState > BulletHingeConstraint::get_frame_b(void) const;

1411 14 get_class_type 0 4 1587 37 BulletHingeConstraint::get_class_type 0 1 658 0
62
static TypeHandle BulletHingeConstraint::get_class_type(void);

1412 23 BulletMinkowskiSumShape 0 260 1588 48 BulletMinkowskiSumShape::BulletMinkowskiSumShape 0 2 659 660 62
/**
 * Only used by make_from_bam.
 */

/**
 *
 */

/**
 *
 */
201
explicit BulletMinkowskiSumShape::BulletMinkowskiSumShape(BulletShape const *shape_a, BulletShape const *shape_b);
BulletMinkowskiSumShape::BulletMinkowskiSumShape(BulletMinkowskiSumShape const &copy);

1413 15 set_transform_a 0 4 1588 40 BulletMinkowskiSumShape::set_transform_a 0 1 661 10
/**
 *
 */
72
void BulletMinkowskiSumShape::set_transform_a(TransformState const *ts);

1414 15 set_transform_b 0 4 1588 40 BulletMinkowskiSumShape::set_transform_b 0 1 662 10
/**
 *
 */
72
void BulletMinkowskiSumShape::set_transform_b(TransformState const *ts);

1415 15 get_transform_a 0 4 1588 40 BulletMinkowskiSumShape::get_transform_a 0 1 663 10
/**
 *
 */
86
ConstPointerTo< TransformState > BulletMinkowskiSumShape::get_transform_a(void) const;

1416 15 get_transform_b 0 4 1588 40 BulletMinkowskiSumShape::get_transform_b 0 1 664 10
/**
 *
 */
86
ConstPointerTo< TransformState > BulletMinkowskiSumShape::get_transform_b(void) const;

1417 11 get_shape_a 0 4 1588 36 BulletMinkowskiSumShape::get_shape_a 0 1 665 10
/**
 *
 */
75
inline BulletShape const *BulletMinkowskiSumShape::get_shape_a(void) const;

1418 11 get_shape_b 0 4 1588 36 BulletMinkowskiSumShape::get_shape_b 0 1 666 10
/**
 *
 */
75
inline BulletShape const *BulletMinkowskiSumShape::get_shape_b(void) const;

1419 14 get_class_type 0 4 1588 39 BulletMinkowskiSumShape::get_class_type 0 1 667 0
64
static TypeHandle BulletMinkowskiSumShape::get_class_type(void);

1420 22 BulletMultiSphereShape 0 260 1591 46 BulletMultiSphereShape::BulletMultiSphereShape 0 2 668 669 22
/**
 *
 */

/**
 *
 */
196
explicit BulletMultiSphereShape::BulletMultiSphereShape(PTA_LVecBase3 const &points, PTA_stdfloat const &radii);
BulletMultiSphereShape::BulletMultiSphereShape(BulletMultiSphereShape const &copy);

1421 10 operator = 0 4 1591 34 BulletMultiSphereShape::operator = 0 1 670 0
76
void BulletMultiSphereShape::operator =(BulletMultiSphereShape const &copy);

1422 16 get_sphere_count 0 4 1591 40 BulletMultiSphereShape::get_sphere_count 0 1 671 10
/**
 *
 */
57
int BulletMultiSphereShape::get_sphere_count(void) const;

1423 14 get_sphere_pos 0 4 1591 38 BulletMultiSphereShape::get_sphere_pos 0 1 672 10
/**
 *
 */
64
LPoint3 BulletMultiSphereShape::get_sphere_pos(int index) const;

1424 17 get_sphere_radius 0 4 1591 41 BulletMultiSphereShape::get_sphere_radius 0 1 673 10
/**
 *
 */
71
PN_stdfloat BulletMultiSphereShape::get_sphere_radius(int index) const;

1425 14 get_class_type 0 4 1591 38 BulletMultiSphereShape::get_class_type 0 1 674 0
63
static TypeHandle BulletMultiSphereShape::get_class_type(void);

1426 16 BulletPlaneShape 0 260 1592 34 BulletPlaneShape::BulletPlaneShape 0 3 675 676 677 81
/**
 * Creates a plane shape from a plane definition.
 */

/**
 *
 */

/**
 *
 */
215
explicit BulletPlaneShape::BulletPlaneShape(LPlane plane);
explicit BulletPlaneShape::BulletPlaneShape(LVector3 const &normal, PN_stdfloat constant);
BulletPlaneShape::BulletPlaneShape(BulletPlaneShape const &copy);

1427 9 get_plane 0 4 1592 27 BulletPlaneShape::get_plane 0 1 678 10
/**
 *
 */
47
LPlane BulletPlaneShape::get_plane(void) const;

1428 16 get_plane_normal 0 4 1592 34 BulletPlaneShape::get_plane_normal 0 1 679 10
/**
 *
 */
56
LVector3 BulletPlaneShape::get_plane_normal(void) const;

1429 18 get_plane_constant 0 4 1592 36 BulletPlaneShape::get_plane_constant 0 1 680 10
/**
 *
 */
61
PN_stdfloat BulletPlaneShape::get_plane_constant(void) const;

1430 15 make_from_solid 0 4 1592 33 BulletPlaneShape::make_from_solid 0 1 681 10
/**
 *
 */
88
static BulletPlaneShape *BulletPlaneShape::make_from_solid(CollisionPlane const *solid);

1431 14 get_class_type 0 4 1592 32 BulletPlaneShape::get_class_type 0 1 682 0
57
static TypeHandle BulletPlaneShape::get_class_type(void);

1432 22 BulletSliderConstraint 0 260 1595 46 BulletSliderConstraint::BulletSliderConstraint 0 2 683 684 22
/**
 *
 */

/**
 *
 */
346
explicit BulletSliderConstraint::BulletSliderConstraint(BulletRigidBodyNode const *node_a, TransformState const *frame_a, bool useFrame_a);
explicit BulletSliderConstraint::BulletSliderConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, TransformState const *frame_a, TransformState const *frame_b, bool use_frame_a);

1433 14 get_linear_pos 0 4 1595 38 BulletSliderConstraint::get_linear_pos 0 1 685 10
/**
 *
 */
63
PN_stdfloat BulletSliderConstraint::get_linear_pos(void) const;

1434 15 get_angular_pos 0 4 1595 39 BulletSliderConstraint::get_angular_pos 0 1 686 10
/**
 *
 */
64
PN_stdfloat BulletSliderConstraint::get_angular_pos(void) const;

1435 22 get_lower_linear_limit 0 4 1595 46 BulletSliderConstraint::get_lower_linear_limit 0 1 687 32
// Limits

// Limits

/**
 *
 */
71
PN_stdfloat BulletSliderConstraint::get_lower_linear_limit(void) const;

1436 22 get_upper_linear_limit 0 4 1595 46 BulletSliderConstraint::get_upper_linear_limit 0 1 688 10
/**
 *
 */
71
PN_stdfloat BulletSliderConstraint::get_upper_linear_limit(void) const;

1437 23 get_lower_angular_limit 0 4 1595 47 BulletSliderConstraint::get_lower_angular_limit 0 1 689 10
/**
 *
 */
72
PN_stdfloat BulletSliderConstraint::get_lower_angular_limit(void) const;

1438 23 get_upper_angular_limit 0 4 1595 47 BulletSliderConstraint::get_upper_angular_limit 0 1 690 10
/**
 *
 */
72
PN_stdfloat BulletSliderConstraint::get_upper_angular_limit(void) const;

1439 22 set_lower_linear_limit 0 4 1595 46 BulletSliderConstraint::set_lower_linear_limit 0 1 691 10
/**
 *
 */
71
void BulletSliderConstraint::set_lower_linear_limit(PN_stdfloat value);

1440 22 set_upper_linear_limit 0 4 1595 46 BulletSliderConstraint::set_upper_linear_limit 0 1 692 10
/**
 *
 */
71
void BulletSliderConstraint::set_upper_linear_limit(PN_stdfloat value);

1441 23 set_lower_angular_limit 0 4 1595 47 BulletSliderConstraint::set_lower_angular_limit 0 1 693 10
/**
 *
 */
72
void BulletSliderConstraint::set_lower_angular_limit(PN_stdfloat value);

1442 23 set_upper_angular_limit 0 4 1595 47 BulletSliderConstraint::set_upper_angular_limit 0 1 694 10
/**
 *
 */
72
void BulletSliderConstraint::set_upper_angular_limit(PN_stdfloat value);

1443 24 set_powered_linear_motor 0 4 1595 48 BulletSliderConstraint::set_powered_linear_motor 0 1 695 44
// Linear motor

// Linear motor

/**
 *
 */
63
void BulletSliderConstraint::set_powered_linear_motor(bool on);

1444 32 set_target_linear_motor_velocity 0 4 1595 56 BulletSliderConstraint::set_target_linear_motor_velocity 0 1 696 10
/**
 *
 */
91
void BulletSliderConstraint::set_target_linear_motor_velocity(PN_stdfloat target_velocity);

1445 26 set_max_linear_motor_force 0 4 1595 50 BulletSliderConstraint::set_max_linear_motor_force 0 1 697 10
/**
 *
 */
79
void BulletSliderConstraint::set_max_linear_motor_force(PN_stdfloat max_force);

1446 24 get_powered_linear_motor 0 4 1595 48 BulletSliderConstraint::get_powered_linear_motor 0 1 698 10
/**
 *
 */
66
bool BulletSliderConstraint::get_powered_linear_motor(void) const;

1447 32 get_target_linear_motor_velocity 0 4 1595 56 BulletSliderConstraint::get_target_linear_motor_velocity 0 1 699 10
/**
 *
 */
81
PN_stdfloat BulletSliderConstraint::get_target_linear_motor_velocity(void) const;

1448 26 get_max_linear_motor_force 0 4 1595 50 BulletSliderConstraint::get_max_linear_motor_force 0 1 700 10
/**
 *
 */
75
PN_stdfloat BulletSliderConstraint::get_max_linear_motor_force(void) const;

1449 25 set_powered_angular_motor 0 4 1595 49 BulletSliderConstraint::set_powered_angular_motor 0 1 701 46
// Angular motor

// Angular motor

/**
 *
 */
64
void BulletSliderConstraint::set_powered_angular_motor(bool on);

1450 33 set_target_angular_motor_velocity 0 4 1595 57 BulletSliderConstraint::set_target_angular_motor_velocity 0 1 702 10
/**
 *
 */
92
void BulletSliderConstraint::set_target_angular_motor_velocity(PN_stdfloat target_velocity);

1451 27 set_max_angular_motor_force 0 4 1595 51 BulletSliderConstraint::set_max_angular_motor_force 0 1 703 10
/**
 *
 */
80
void BulletSliderConstraint::set_max_angular_motor_force(PN_stdfloat max_force);

1452 25 get_powered_angular_motor 0 4 1595 49 BulletSliderConstraint::get_powered_angular_motor 0 1 704 10
/**
 *
 */
67
bool BulletSliderConstraint::get_powered_angular_motor(void) const;

1453 33 get_target_angular_motor_velocity 0 4 1595 57 BulletSliderConstraint::get_target_angular_motor_velocity 0 1 705 10
/**
 *
 */
82
PN_stdfloat BulletSliderConstraint::get_target_angular_motor_velocity(void) const;

1454 27 get_max_angular_motor_force 0 4 1595 51 BulletSliderConstraint::get_max_angular_motor_force 0 1 706 10
/**
 *
 */
76
PN_stdfloat BulletSliderConstraint::get_max_angular_motor_force(void) const;

1455 10 set_frames 0 4 1595 34 BulletSliderConstraint::set_frames 0 1 707 21
// Frames

/**
 *
 */
96
void BulletSliderConstraint::set_frames(TransformState const *ts_a, TransformState const *ts_b);

1456 11 get_frame_a 0 4 1595 35 BulletSliderConstraint::get_frame_a 0 1 708 10
/**
 *
 */
81
ConstPointerTo< TransformState > BulletSliderConstraint::get_frame_a(void) const;

1457 11 get_frame_b 0 4 1595 35 BulletSliderConstraint::get_frame_b 0 1 709 10
/**
 *
 */
81
ConstPointerTo< TransformState > BulletSliderConstraint::get_frame_b(void) const;

1458 14 get_class_type 0 4 1595 38 BulletSliderConstraint::get_class_type 0 1 710 0
63
static TypeHandle BulletSliderConstraint::get_class_type(void);

1459 18 upcast_to_IControl 0 12 1596 41 BulletSoftBodyControl::upcast_to_IControl 0 1 717 45
upcast from BulletSoftBodyControl to IControl
78
btSoftBody::AJoint::IControl *BulletSoftBodyControl::upcast_to_IControl(void);

1460 42 downcast_IControl_to_BulletSoftBodyControl 0 8 0 42 downcast_IControl_to_BulletSoftBodyControl 0 0 47
downcast from IControl to BulletSoftBodyControl
102
BulletSoftBodyControl *downcast_IControl_to_BulletSoftBodyControl(btSoftBody::AJoint::IControl *this);

1461 21 BulletSoftBodyControl 0 260 1596 44 BulletSoftBodyControl::BulletSoftBodyControl 0 2 711 712 10
/**
 *
 */
145
BulletSoftBodyControl::BulletSoftBodyControl(void);
inline BulletSoftBodyControl::BulletSoftBodyControl(BulletSoftBodyControl const &) = default;

1462 22 ~BulletSoftBodyControl 0 518 1596 45 BulletSoftBodyControl::~BulletSoftBodyControl 0 0 10
/**
 *
 */
60
virtual BulletSoftBodyControl::~BulletSoftBodyControl(void);

1463 8 set_goal 0 4 1596 31 BulletSoftBodyControl::set_goal 0 1 713 20
// Motor

/**
 *
 */
62
inline void BulletSoftBodyControl::set_goal(PN_stdfloat goal);

1464 14 set_max_torque 0 4 1596 37 BulletSoftBodyControl::set_max_torque 0 1 714 10
/**
 *
 */
73
inline void BulletSoftBodyControl::set_max_torque(PN_stdfloat maxtorque);

1465 9 set_angle 0 4 1596 32 BulletSoftBodyControl::set_angle 0 1 715 20
// Steer

/**
 *
 */
64
inline void BulletSoftBodyControl::set_angle(PN_stdfloat angle);

1466 8 set_sign 0 4 1596 31 BulletSoftBodyControl::set_sign 0 1 716 10
/**
 *
 */
62
inline void BulletSoftBodyControl::set_sign(PN_stdfloat sign);

1467 8 get_body 0 4 1600 29 BulletSoftBodyShape::get_body 0 1 718 10
/**
 *
 */
62
BulletSoftBodyNode *BulletSoftBodyShape::get_body(void) const;

1468 14 get_class_type 0 4 1600 35 BulletSoftBodyShape::get_class_type 0 1 719 0
60
static TypeHandle BulletSoftBodyShape::get_class_type(void);

1469 17 BulletSphereShape 0 260 1601 36 BulletSphereShape::BulletSphereShape 0 2 720 721 22
/**
 *
 */

/**
 *
 */
135
explicit BulletSphereShape::BulletSphereShape(PN_stdfloat radius);
BulletSphereShape::BulletSphereShape(BulletSphereShape const &copy);

1470 10 get_radius 0 4 1601 29 BulletSphereShape::get_radius 0 1 722 69
/**
 * Returns the radius that was used to construct this sphere.
 */
61
inline PN_stdfloat BulletSphereShape::get_radius(void) const;

1471 15 make_from_solid 0 4 1601 34 BulletSphereShape::make_from_solid 0 1 723 10
/**
 *
 */
91
static BulletSphereShape *BulletSphereShape::make_from_solid(CollisionSphere const *solid);

1472 14 get_class_type 0 4 1601 33 BulletSphereShape::get_class_type 0 1 724 0
58
static TypeHandle BulletSphereShape::get_class_type(void);

1473 25 BulletSphericalConstraint 0 260 1602 52 BulletSphericalConstraint::BulletSphericalConstraint 0 2 725 726 22
/**
 *
 */

/**
 *
 */
302
explicit BulletSphericalConstraint::BulletSphericalConstraint(BulletRigidBodyNode const *node_a, LPoint3 const &pivot_a);
explicit BulletSphericalConstraint::BulletSphericalConstraint(BulletRigidBodyNode const *node_a, BulletRigidBodyNode const *node_b, LPoint3 const &pivot_a, LPoint3 const &pivot_b);

1474 11 set_pivot_a 0 4 1602 38 BulletSphericalConstraint::set_pivot_a 0 1 727 32
// Pivots

// Pivots

/**
 *
 */
68
void BulletSphericalConstraint::set_pivot_a(LPoint3 const &pivot_a);

1475 11 set_pivot_b 0 4 1602 38 BulletSphericalConstraint::set_pivot_b 0 1 728 10
/**
 *
 */
68
void BulletSphericalConstraint::set_pivot_b(LPoint3 const &pivot_b);

1476 14 get_pivot_in_a 0 4 1602 41 BulletSphericalConstraint::get_pivot_in_a 0 1 729 10
/**
 *
 */
62
LPoint3 BulletSphericalConstraint::get_pivot_in_a(void) const;

1477 14 get_pivot_in_b 0 4 1602 41 BulletSphericalConstraint::get_pivot_in_b 0 1 730 10
/**
 *
 */
62
LPoint3 BulletSphericalConstraint::get_pivot_in_b(void) const;

1478 14 get_class_type 0 4 1602 41 BulletSphericalConstraint::get_class_type 0 1 731 0
66
static TypeHandle BulletSphericalConstraint::get_class_type(void);

1479 22 BulletTickCallbackData 0 260 1603 46 BulletTickCallbackData::BulletTickCallbackData 0 1 732 10
/**
 *
 */
73
inline BulletTickCallbackData::BulletTickCallbackData(btScalar timestep);

1480 12 get_timestep 0 4 1603 36 BulletTickCallbackData::get_timestep 0 1 733 10
/**
 *
 */
68
inline PN_stdfloat BulletTickCallbackData::get_timestep(void) const;

1481 14 get_class_type 0 4 1603 38 BulletTickCallbackData::get_class_type 0 1 734 0
63
static TypeHandle BulletTickCallbackData::get_class_type(void);

1482 23 ~BulletTickCallbackData 0 516 1603 47 BulletTickCallbackData::~BulletTickCallbackData 0 0 0
54
BulletTickCallbackData::~BulletTickCallbackData(void);

1483 18 BulletTriangleMesh 0 260 1604 38 BulletTriangleMesh::BulletTriangleMesh 0 1 735 10
/**
 *
 */
45
BulletTriangleMesh::BulletTriangleMesh(void);

1484 12 add_triangle 0 4 1604 32 BulletTriangleMesh::add_triangle 0 1 736 347
/**
 * Adds a triangle with the indicated coordinates.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */
135
void BulletTriangleMesh::add_triangle(LPoint3 const &p0, LPoint3 const &p1, LPoint3 const &p2, bool remove_duplicate_vertices = false);

1485 9 add_array 0 4 1604 29 BulletTriangleMesh::add_array 0 1 737 452
/**
 * Adds triangle information from an array of points and indices referring to
 * these points.  This is more efficient than adding triangles one at a time.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */
128
void BulletTriangleMesh::add_array(PTA_LVecBase3 const &points, PTA_int const &indices, bool remove_duplicate_vertices = false);

1486 8 add_geom 0 4 1604 28 BulletTriangleMesh::add_geom 0 1 738 461
/**
 * Adds the geometry from the indicated Geom from the triangle mesh.  This is
 * a one-time copy operation, and future updates to the Geom will not be
 * reflected.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */
152
void BulletTriangleMesh::add_geom(Geom const *geom, bool remove_duplicate_vertices = false, TransformState const *ts = TransformState::make_identity());

1487 20 set_welding_distance 0 4 1604 40 BulletTriangleMesh::set_welding_distance 0 1 739 254
/**
 * Sets the square of the distance at which vertices will be merged
 * together when adding geometry with remove_duplicate_vertices set to true.
 *
 * The default is 0, meaning vertices will only be merged if they have the
 * exact same position.
 */
68
void BulletTriangleMesh::set_welding_distance(PN_stdfloat distance);

1488 11 preallocate 0 4 1604 31 BulletTriangleMesh::preallocate 0 1 740 239
/**
 * Used to reserve memory in anticipation of the given amount of vertices and
 * indices being added to the triangle mesh.  This is useful if you are about
 * to call add_triangle() many times, to prevent unnecessary reallocations.
 */
69
void BulletTriangleMesh::preallocate(int num_verts, int num_indices);

1489 17 get_num_triangles 0 4 1604 37 BulletTriangleMesh::get_num_triangles 0 1 741 65
/**
 * Returns the number of triangles in this triangle mesh.
 */
62
std::size_t BulletTriangleMesh::get_num_triangles(void) const;

1490 20 get_welding_distance 0 4 1604 40 BulletTriangleMesh::get_welding_distance 0 1 742 110
/**
 * Returns the value previously set with set_welding_distance(), or the
 * value of 0 if none was set.
 */
65
PN_stdfloat BulletTriangleMesh::get_welding_distance(void) const;

1491 6 output 0 6 1604 26 BulletTriangleMesh::output 0 1 743 10
/**
 *
 */
65
virtual void BulletTriangleMesh::output(std::ostream &out) const;

1492 5 write 0 6 1604 25 BulletTriangleMesh::write 0 1 744 10
/**
 *
 */
82
virtual void BulletTriangleMesh::write(std::ostream &out, int indent_level) const;

1493 16 get_num_vertices 0 4 1604 36 BulletTriangleMesh::get_num_vertices 0 1 746 64
/**
 * Returns the number of vertices in this triangle mesh.
 */
61
std::size_t BulletTriangleMesh::get_num_vertices(void) const;

1494 10 get_vertex 0 4 1604 30 BulletTriangleMesh::get_vertex 0 0 56
/**
 * Returns the vertex at the given vertex index.
 */
64
LPoint3 BulletTriangleMesh::get_vertex(std::size_t index) const;

1495 12 get_triangle 0 4 1604 32 BulletTriangleMesh::get_triangle 0 0 73
/**
 * Returns the vertex indices making up the given triangle index.
 */
69
LVecBase3i BulletTriangleMesh::get_triangle(std::size_t index) const;

1496 14 get_class_type 0 4 1604 34 BulletTriangleMesh::get_class_type 0 1 745 0
59
static TypeHandle BulletTriangleMesh::get_class_type(void);

1497 23 BulletTriangleMeshShape 0 260 1606 48 BulletTriangleMeshShape::BulletTriangleMeshShape 0 2 747 748 207
/**
 * Only used by make_from_bam.
 */

/**
 * The parameters 'compress' and 'bvh' are only used if 'dynamic' is set to
 * FALSE.
 * Assumes the lock(bullet global lock) is held by the caller
 */

/**
 *
 */
224
explicit BulletTriangleMeshShape::BulletTriangleMeshShape(BulletTriangleMesh *mesh, bool dynamic, bool compress = true, bool bvh = true);
BulletTriangleMeshShape::BulletTriangleMeshShape(BulletTriangleMeshShape const &copy);

1498 10 refit_tree 0 4 1606 35 BulletTriangleMeshShape::refit_tree 0 1 749 10
/**
 *
 */
91
void BulletTriangleMeshShape::refit_tree(LPoint3 const &aabb_min, LPoint3 const &aabb_max);

1499 9 is_static 0 4 1606 34 BulletTriangleMeshShape::is_static 0 1 750 10
/**
 *
 */
59
inline bool BulletTriangleMeshShape::is_static(void) const;

1500 10 is_dynamic 0 4 1606 35 BulletTriangleMeshShape::is_dynamic 0 1 751 10
/**
 *
 */
60
inline bool BulletTriangleMeshShape::is_dynamic(void) const;

1501 14 get_class_type 0 4 1606 39 BulletTriangleMeshShape::get_class_type 0 1 752 0
64
static TypeHandle BulletTriangleMeshShape::get_class_type(void);

754
1 0 0 7 27 1607 765 0 0 0 
2 0 0 15 27 1607 765 0 0 1 6 param0 0 1608  
3 0 0 7 5 1607 765 0 101 /**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */ 0 
4 0 0 7 6 1504 0 0 10 /**
 *
 */ 1 4 this 3 1608  
5 0 0 7 7 1610 0 0 10 /**
 *
 */ 1 4 this 3 1608  
6 0 0 7 8 1611 0 0 10 /**
 *
 */ 1 4 this 3 1608  
7 0 0 6 9 1510 0 0 10 /**
 *
 */ 1 4 this 3 1608  
8 0 0 6 10 1512 0 0 10 /**
 *
 */ 1 4 this 3 1608  
9 0 0 6 11 1512 0 0 10 /**
 *
 */ 1 4 this 3 1608  
10 0 0 7 34 1612 775 0 101 /**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */ 0 
11 0 0 7 35 1610 0 0 10 /**
 *
 */ 1 4 this 3 1613  
12 0 0 7 36 1610 0 0 10 /**
 *
 */ 1 4 this 3 1613  
13 0 0 6 37 1518 0 0 10 /**
 *
 */ 1 4 this 3 1613  
14 0 0 6 38 1510 0 0 10 /**
 *
 */ 1 4 this 3 1613  
15 0 0 6 39 1512 0 0 10 /**
 *
 */ 1 4 this 3 1613  
16 0 0 7 40 1608 0 0 10 /**
 *
 */ 2 4 this 3 1613  3 idx 1 1512  
17 0 0 6 32 1615 0 0 0 1 4 this 3 1612  
18 0 0 6 49 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
19 0 0 6 50 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
20 0 0 6 51 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
21 0 0 6 52 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
22 0 0 6 53 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
23 0 0 6 54 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
24 0 0 6 55 1518 0 0 10 /**
 *
 */ 1 4 this 3 1589  
25 0 0 4 56 1616 0 0 10 /**
 *
 */ 2 4 this 3 1525  6 margin 1 1510  
26 0 0 6 57 1617 0 0 10 /**
 *
 */ 1 4 this 3 1589  
27 0 0 6 58 1510 0 0 10 /**
 *
 */ 1 4 this 3 1589  
28 0 0 7 59 1618 0 0 62 /**
 * Returns the current bounds of this collision shape.
 */ 1 4 this 3 1589  
29 0 0 7 75 1620 0 0 0 0 
30 0 0 7 77 1620 0 0 0 0 
31 0 0 4 80 1616 0 0 9 // Shapes 3 4 this 3 1621  5 shape 1 1525  5 xform 5 1622  
32 0 0 4 81 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 shape 1 1525  
33 0 0 6 82 1512 0 0 10 /**
 *
 */ 1 4 this 3 1625  
34 0 0 7 83 1525 0 0 10 /**
 *
 */ 2 4 this 3 1625  3 idx 1 1512  
35 0 0 7 85 1610 0 0 10 /**
 *
 */ 2 4 this 3 1625  3 idx 1 1512  
36 0 0 7 86 1627 0 0 10 /**
 *
 */ 2 4 this 3 1625  3 idx 1 1512  
37 0 0 7 87 1622 0 0 10 /**
 *
 */ 2 4 this 3 1625  3 idx 1 1512  
38 0 0 7 88 1618 0 0 81 /**
 * Returns the current bounds of all collision shapes owned by this body.
 */ 1 4 this 3 1625  
39 0 0 4 89 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 cnode 1 1628  
40 0 0 6 90 1518 0 0 23 // Static and kinematic 1 4 this 3 1625  
41 0 0 6 91 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
42 0 0 4 92 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 value 1 1518  
43 0 0 4 93 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 value 1 1518  
44 0 0 4 94 1616 0 0 11 // Contacts 2 4 this 3 1621  4 mask 1 1630  
45 0 0 4 95 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 value 1 1518  
46 0 0 6 96 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
47 0 0 4 97 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  5 value 1 1518  
48 0 0 6 98 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
49 0 0 6 99 1518 0 0 10 /**
 *
 */ 2 4 this 3 1621  4 node 1 1504  
50 0 0 6 100 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
51 0 0 6 101 1510 0 0 10 /**
 *
 */ 1 4 this 3 1625  
52 0 0 4 102 1616 0 0 106 /**
 * The constraint solver can discard solving contacts, if the distance is
 * above this threshold.
 */ 2 4 this 3 1621  9 threshold 1 1510  
53 0 0 6 103 1518 0 0 15 // Deactivation 1 4 this 3 1625  
54 0 0 4 104 1616 0 0 10 /**
 *
 */ 3 4 this 3 1621  6 active 1 1518  5 force 5 1518  
55 0 0 4 105 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  6 active 1 1518  
56 0 0 4 106 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  2 dt 1 1510  
57 0 0 6 107 1510 0 0 10 /**
 *
 */ 1 4 this 3 1625  
58 0 0 4 108 1616 0 0 163 /**
 * If true, this object will be deactivated after a certain amount of time has
 * passed without movement.  If false, the object will always remain active.
 */ 2 4 this 3 1621  7 enabled 1 1518  
59 0 0 6 109 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
60 0 0 4 110 1616 0 0 132 /**
 * Enables or disables the debug visualisation for this collision object.  By
 * default the debug visualisation is enabled.
 */ 2 4 this 3 1621  7 enabled 1 1634  
61 0 0 6 111 1518 0 0 141 /**
 * Returns TRUE if the debug visualisation is enabled for this collision
 * object, and FALSE if the debug visualisation is disabled.
 */ 1 4 this 3 1625  
62 0 0 6 112 1510 0 0 27 // Friction and Restitution 1 4 this 3 1625  
63 0 0 4 113 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  11 restitution 1 1510  
64 0 0 6 114 1510 0 0 10 /**
 *
 */ 1 4 this 3 1625  
65 0 0 4 115 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  8 friction 1 1510  
66 0 0 6 116 1518 0 0 10 /**
 *
 */ 1 4 this 3 1625  
67 0 0 4 117 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  8 friction 1 1635  
68 0 0 7 118 1637 0 0 10 /**
 *
 */ 1 4 this 3 1625  
69 0 0 6 119 1510 0 0 10 /**
 *
 */ 1 4 this 3 1625  
70 0 0 6 120 1510 0 0 10 /**
 *
 */ 1 4 this 3 1625  
71 0 0 4 121 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  6 radius 1 1510  
72 0 0 4 122 1616 0 0 10 /**
 *
 */ 2 4 this 3 1621  9 threshold 1 1510  
73 0 0 4 123 1616 0 0 216 /**
 * This method enforces an update of the Bullet transform, that is copies the
 * scene graph transform to the Bullet transform.  This is achieved by alling
 * the protected PandaNode hook 'transform_changed'.
 */ 1 4 this 3 1621  
74 0 0 7 150 1620 0 0 0 0 
75 0 0 15 152 1640 0 0 10 /**
 *
 */ 1 4 copy 1 1638  
76 0 0 7 152 1640 0 0 10 /**
 *
 */ 1 11 halfExtents 1 1635  
77 0 0 7 153 1637 0 0 10 /**
 *
 */ 1 4 this 3 1638  
78 0 0 7 154 1637 0 0 10 /**
 *
 */ 1 4 this 3 1638  
79 0 0 7 155 1640 0 0 10 /**
 *
 */ 1 5 solid 1 1641  
80 0 0 7 158 1620 0 0 0 0 
81 0 0 15 160 1646 0 0 10 /**
 *
 */ 1 4 copy 1 1644  
82 0 0 7 160 1646 0 0 10 /**
 *
 */ 3 6 radius 1 1510  6 height 1 1510  2 up 5 1502  
83 0 0 7 161 1646 0 0 130 /**
 * Constructs a new BulletCapsuleShape using the information from a
 * CollisionCapsule from the builtin collision system.
 */ 1 5 solid 1 1647  
84 0 0 6 162 1510 0 0 70 /**
 * Returns the radius that was used to construct this capsule.
 */ 1 4 this 3 1644  
85 0 0 6 163 1510 0 0 81 /**
 * Returns half of get_height().
 * @deprecated see get_height() instead.
 */ 1 4 this 3 1644  
86 0 0 7 167 1620 0 0 0 0 
87 0 0 7 169 1650 789 0 10 /**
 *
 */ 3 5 shape 1 1525  11 step_height 1 1510  4 name 5 1617  
88 0 0 4 170 1616 0 0 10 /**
 *
 */ 3 4 this 3 1650  8 velocity 1 1651  8 is_local 1 1518  
89 0 0 4 171 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  5 omega 1 1510  
90 0 0 7 172 1525 0 0 10 /**
 *
 */ 1 4 this 3 1653  
91 0 0 4 173 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  7 gravity 1 1510  
92 0 0 6 174 1510 0 0 10 /**
 *
 */ 1 4 this 3 1653  
93 0 0 4 175 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  10 fall_speed 1 1510  
94 0 0 4 176 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  10 jump_speed 1 1510  
95 0 0 4 177 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  15 max_jump_height 1 1510  
96 0 0 4 178 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  9 max_slope 1 1510  
97 0 0 6 179 1510 0 0 10 /**
 *
 */ 1 4 this 3 1653  
98 0 0 4 180 1616 0 0 10 /**
 *
 */ 2 4 this 3 1650  5 value 1 1518  
99 0 0 6 181 1518 0 0 10 /**
 *
 */ 1 4 this 3 1653  
100 0 0 6 182 1518 0 0 10 /**
 *
 */ 1 4 this 3 1653  
101 0 0 4 183 1616 0 0 10 /**
 *
 */ 1 4 this 3 1650  
102 0 0 7 188 1620 0 0 0 0 
103 0 0 15 211 1657 874 0 0 1 6 param0 0 1655  
104 0 0 7 193 1657 874 0 101 /**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */ 0 
105 0 0 7 194 1610 0 0 10 /**
 *
 */ 1 4 this 3 1655  
106 0 0 7 195 1610 0 0 10 /**
 *
 */ 1 4 this 3 1655  
107 0 0 6 196 1518 0 0 10 /**
 *
 */ 1 4 this 3 1655  
108 0 0 7 197 1504 0 0 10 /**
 *
 */ 1 4 this 3 1655  
109 0 0 7 198 1610 0 0 10 /**
 *
 */ 1 4 this 3 1655  
110 0 0 7 199 1611 0 0 10 /**
 *
 */ 1 4 this 3 1655  
111 0 0 6 200 1510 0 0 10 /**
 *
 */ 1 4 this 3 1655  
112 0 0 6 201 1512 0 0 10 /**
 *
 */ 1 4 this 3 1655  
113 0 0 6 202 1512 0 0 10 /**
 *
 */ 1 4 this 3 1655  
114 0 0 6 191 1658 0 0 0 1 4 this 3 1657  
115 0 0 15 231 1661 886 0 0 1 6 param0 0 1659  
116 0 0 7 217 1661 886 0 101 /**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */ 0 
117 0 0 7 218 1610 0 0 10 /**
 *
 */ 1 4 this 3 1659  
118 0 0 7 219 1610 0 0 10 /**
 *
 */ 1 4 this 3 1659  
119 0 0 6 220 1518 0 0 10 /**
 *
 */ 1 4 this 3 1659  
120 0 0 7 221 1504 0 0 10 /**
 *
 */ 1 4 this 3 1659  
121 0 0 7 222 1610 0 0 10 /**
 *
 */ 1 4 this 3 1659  
122 0 0 7 223 1611 0 0 10 /**
 *
 */ 1 4 this 3 1659  
123 0 0 6 224 1510 0 0 10 /**
 *
 */ 1 4 this 3 1659  
124 0 0 6 215 1662 0 0 0 1 4 this 3 1661  
125 0 0 15 234 1665 0 0 10 /**
 *
 */ 1 4 copy 1 1663  
126 0 0 7 234 1665 0 0 10 /**
 *
 */ 3 6 radius 1 1510  6 height 1 1510  2 up 5 1502  
127 0 0 6 235 1510 0 0 67 /**
 * Returns the radius that was passed into the constructor.
 */ 1 4 this 3 1663  
128 0 0 6 236 1510 0 0 67 /**
 * Returns the height that was passed into the constructor.
 */ 1 4 this 3 1663  
129 0 0 7 239 1620 0 0 0 0 
130 0 0 7 242 1542 0 0 10 /**
 *
 */ 1 4 this 3 1575  
131 0 0 7 243 1542 0 0 10 /**
 *
 */ 1 4 this 3 1575  
132 0 0 4 244 1616 0 0 10 /**
 *
 */ 2 4 this 3 1575  5 value 1 1518  
133 0 0 4 245 1616 0 0 10 /**
 *
 */ 2 4 this 3 1575  4 size 1 1510  
134 0 0 6 246 1510 0 0 10 /**
 *
 */ 1 4 this 3 1575  
135 0 0 6 247 1510 0 0 10 /**
 *
 */ 1 4 this 3 1666  
136 0 0 4 248 1616 0 0 212 /**
 * Sets the applied impulse limit for breaking the constraint.  If the limit
 * is exceeded the constraint will be disabled.  Disabled constraints are not
 * removed from the world, and can be re-enabled.
 */ 2 4 this 3 1575  9 threshold 1 1510  
137 0 0 6 249 1510 0 0 73 /**
 * Returns the applied impluse limit for breaking the constraint.
 */ 1 4 this 3 1666  
138 0 0 4 250 1616 0 0 10 /**
 *
 */ 2 4 this 3 1575  7 enabled 1 1518  
139 0 0 6 251 1518 0 0 53 /**
 * Returns TRUE if the constraint is enabled.
 */ 1 4 this 3 1666  
140 0 0 4 253 1616 0 0 10 /**
 *
 */ 4 4 this 3 1575  3 num 1 1541  5 value 1 1510  4 axis 5 1512  
141 0 0 6 254 1510 0 0 10 /**
 *
 */ 3 4 this 3 1575  3 num 1 1541  4 axis 5 1512  
142 0 0 7 318 1620 0 0 0 0 
143 0 0 7 258 1542 0 0 10 /**
 *
 */ 1 4 name 5 1617  
144 0 0 4 259 1616 0 0 257 /**
 * Sets the mass of a rigid body.  This also modifies the inertia, which is
 * automatically computed from the shape of the body.  Setting a value of zero
 * for mass will make the body static.  A value of zero can be considered an
 * infinite mass.
 */ 2 4 this 3 1542  4 mass 1 1510  
145 0 0 6 260 1510 0 0 128 /**
 * Returns the total mass of a rigid body.  A value of zero means that the
 * body is staic, i.e.  has an infinite mass.
 */ 1 4 this 3 1668  
146 0 0 6 261 1510 0 0 52 /**
 * Returns the inverse mass of a rigid body.
 */ 1 4 this 3 1668  
147 0 0 4 262 1616 0 0 591 /**
 * Sets the inertia of a rigid body.  Inertia is given as a three-component
 * vector.  A component value of zero means infinite inertia along this
 * direction.  Setting the intertia will override the value which is
 * automatically calculated from the rigid bodies shape.  However, it is
 * possible that automatic calculation of intertia is trigger after calling
 * this method, and thus overwriting the explicitly set value again.  This
 * happens when: (a) the mass is set after the inertia.  (b) a shape is added
 * or removed from the body.  (c) the scale of the body changed.
 */ 2 4 this 3 1542  7 inertia 1 1635  
148 0 0 7 263 1611 0 0 174 /**
 * Returns the inertia of the rigid body.  Inertia is given as a three
 * component vector.  A component value of zero means infinite inertia along
 * this direction.
 */ 1 4 this 3 1668  
149 0 0 7 264 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
150 0 0 7 265 1670 0 0 10 /**
 *
 */ 1 4 this 3 1668  
151 0 0 7 266 1611 0 0 11 // Velocity 1 4 this 3 1668  
152 0 0 7 267 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
153 0 0 4 268 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  8 velocity 1 1651  
154 0 0 4 269 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  8 velocity 1 1651  
155 0 0 6 270 1510 0 0 10 // Damping 1 4 this 3 1668  
156 0 0 6 271 1510 0 0 10 /**
 *
 */ 1 4 this 3 1668  
157 0 0 4 272 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  5 value 1 1510  
158 0 0 4 273 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  5 value 1 1510  
159 0 0 4 274 1616 0 0 9 // Forces 1 4 this 3 1542  
160 0 0 4 275 1616 0 0 10 /**
 *
 */ 3 4 this 3 1542  5 force 1 1651  3 pos 1 1671  
161 0 0 4 276 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  5 force 1 1651  
162 0 0 4 277 1616 0 0 10 /**
 *
 */ 3 4 this 3 1542  7 impulse 1 1651  3 pos 1 1671  
163 0 0 4 278 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  7 impulse 1 1651  
164 0 0 4 279 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  6 torque 1 1651  
165 0 0 4 280 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  6 torque 1 1651  
166 0 0 7 281 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
167 0 0 7 282 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
168 0 0 6 283 1510 0 0 26 // Deactivation thresholds 1 4 this 3 1668  
169 0 0 6 284 1510 0 0 10 /**
 *
 */ 1 4 this 3 1668  
170 0 0 4 285 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  9 threshold 1 1510  
171 0 0 4 286 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  9 threshold 1 1510  
172 0 0 4 287 1616 0 0 10 // Gravity 2 4 this 3 1542  7 gravity 1 1651  
173 0 0 7 288 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
174 0 0 7 289 1611 0 0 20 // Restrict movement 1 4 this 3 1668  
175 0 0 7 290 1611 0 0 10 /**
 *
 */ 1 4 this 3 1668  
176 0 0 4 291 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  6 factor 1 1651  
177 0 0 4 292 1616 0 0 10 /**
 *
 */ 2 4 this 3 1542  6 factor 1 1651  
178 0 0 6 293 1518 0 0 123 /**
 * Returns TRUE if the transform of the rigid body has changed at least once
 * since the last call to this method.
 */ 1 4 this 3 1542  
179 0 0 7 312 1620 0 0 0 0 
180 0 0 7 320 1673 0 0 10 /**
 *
 */ 4 6 node_a 1 1668  6 node_b 1 1668  7 frame_a 1 1622  7 frame_b 1 1622  
181 0 0 7 320 1673 0 0 10 /**
 *
 */ 2 6 node_a 1 1668  7 frame_a 1 1622  
182 0 0 4 321 1616 0 0 10 /**
 *
 */ 7 4 this 3 1673  6 swing1 1 1510  6 swing2 1 1510  5 twist 1 1510  8 softness 5 1510  4 bias 5 1510  10 relaxation 5 1510  
183 0 0 4 321 1616 0 0 10 /**
 *
 */ 3 4 this 3 1673  5 index 1 1512  5 value 1 1510  
184 0 0 4 322 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  7 damping 1 1510  
185 0 0 6 323 1510 0 0 10 /**
 *
 */ 1 4 this 3 1674  
186 0 0 4 324 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  9 threshold 1 1510  
187 0 0 4 325 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  6 enable 1 1518  
188 0 0 4 326 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  11 max_impulse 1 1510  
189 0 0 4 327 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  11 max_impulse 1 1510  
190 0 0 4 328 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  4 quat 1 1676  
191 0 0 4 329 1616 0 0 10 /**
 *
 */ 2 4 this 3 1673  4 quat 1 1676  
192 0 0 4 330 1616 0 0 10 /**
 *
 */ 3 4 this 3 1673  4 ts_a 1 1622  4 ts_b 1 1622  
193 0 0 7 331 1622 0 0 10 /**
 *
 */ 1 4 this 3 1674  
194 0 0 7 332 1622 0 0 10 /**
 *
 */ 1 4 this 3 1674  
195 0 0 7 336 1620 0 0 0 0 
196 0 0 6 339 1512 0 0 10 /**
 *
 */ 1 4 this 3 1680  
197 0 0 6 340 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
198 0 0 6 341 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
199 0 0 7 342 1610 0 0 10 /**
 *
 */ 1 4 this 3 1680  
200 0 0 7 343 1610 0 0 10 /**
 *
 */ 1 4 this 3 1680  
201 0 0 7 344 1611 0 0 10 /**
 *
 */ 1 4 this 3 1680  
202 0 0 7 345 1610 0 0 10 /**
 *
 */ 1 4 this 3 1680  
203 0 0 7 346 1610 0 0 10 /**
 *
 */ 1 4 this 3 1680  
204 0 0 6 347 1512 0 0 10 /**
 *
 */ 1 4 this 3 1680  
205 0 0 6 348 1512 0 0 10 /**
 *
 */ 1 4 this 3 1680  
206 0 0 6 349 1512 0 0 10 /**
 *
 */ 1 4 this 3 1680  
207 0 0 6 350 1512 0 0 10 /**
 *
 */ 1 4 this 3 1680  
208 0 0 4 351 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1518  
209 0 0 4 352 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  3 dir 1 1635  
210 0 0 4 353 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  3 dir 1 1635  
211 0 0 4 354 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
212 0 0 4 355 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
213 0 0 4 356 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
214 0 0 4 357 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
215 0 0 4 358 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
216 0 0 4 359 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
217 0 0 4 360 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
218 0 0 4 361 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
219 0 0 4 362 1616 0 0 10 /**
 *
 */ 2 4 this 3 1682  5 value 1 1510  
220 0 0 6 363 1518 0 0 10 /**
 *
 */ 1 4 this 3 1680  
221 0 0 7 364 1611 0 0 10 /**
 *
 */ 1 4 this 3 1680  
222 0 0 7 365 1611 0 0 10 /**
 *
 */ 1 4 this 3 1680  
223 0 0 6 366 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
224 0 0 6 367 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
225 0 0 6 368 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
226 0 0 6 369 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
227 0 0 6 370 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
228 0 0 6 371 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
229 0 0 6 372 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
230 0 0 6 373 1510 0 0 10 /**
 *
 */ 1 4 this 3 1680  
231 0 0 23 399 1683 0 0 10 /**
 *
 */ 7 2 mp 1 1682  5 node0 1 1504  5 node1 1 1504  3 id0 1 1512  3 id1 1 1512  6 index0 1 1512  6 index1 1 1512  
232 0 0 6 400 1682 0 0 10 /**
 *
 */ 1 4 this 3 1684  
233 0 0 7 401 1504 0 0 10 /**
 *
 */ 1 4 this 3 1684  
234 0 0 7 402 1504 0 0 10 /**
 *
 */ 1 4 this 3 1684  
235 0 0 6 403 1512 0 0 10 /**
 *
 */ 1 4 this 3 1684  
236 0 0 6 404 1512 0 0 10 /**
 *
 */ 1 4 this 3 1684  
237 0 0 6 405 1512 0 0 10 /**
 *
 */ 1 4 this 3 1684  
238 0 0 6 406 1512 0 0 10 /**
 *
 */ 1 4 this 3 1684  
239 0 0 7 414 1620 0 0 0 0 
240 0 0 6 416 1682 0 0 10 /**
 *
 */ 1 4 this 3 1686  
241 0 0 7 417 1504 0 0 10 /**
 *
 */ 1 4 this 3 1687  
242 0 0 7 418 1504 0 0 10 /**
 *
 */ 1 4 this 3 1687  
243 0 0 6 419 1512 0 0 10 /**
 *
 */ 1 4 this 3 1687  
244 0 0 6 420 1512 0 0 10 /**
 *
 */ 1 4 this 3 1687  
245 0 0 6 421 1512 0 0 10 /**
 *
 */ 1 4 this 3 1687  
246 0 0 6 422 1512 0 0 10 /**
 *
 */ 1 4 this 3 1687  
247 0 0 6 435 1512 0 0 10 /**
 *
 */ 1 4 this 3 1689  
248 0 0 7 436 1686 1007 0 10 /**
 *
 */ 2 4 this 3 1691  3 idx 1 1512  
249 0 0 6 433 1692 0 0 0 1 4 this 3 1691  
250 0 0 15 441 1570 0 0 0 1 6 param0 0 1693  
251 0 0 7 441 1570 0 0 10 /**
 *
 */ 1 4 name 5 1617  
252 0 0 4 442 1616 0 0 10 /**
 *
 */ 1 4 this 3 1570  
253 0 0 4 443 1616 0 0 64 /**
 * If true, displays collision shapes in wireframe mode.
 */ 2 4 this 3 1570  4 show 1 1518  
254 0 0 4 444 1616 0 0 102 /**
 * If true, display limits defined for constraints, e.g. a pivot axis or maximum
 * amplitude.
 */ 2 4 this 3 1570  4 show 1 1518  
255 0 0 4 445 1616 0 0 69 /**
 * If true, displays axis aligned bounding boxes for objects.
 */ 2 4 this 3 1570  4 show 1 1518  
256 0 0 4 446 1616 0 0 84 /**
 * If true, displays normal vectors for triangle mesh and heightfield faces.
 */ 2 4 this 3 1570  4 show 1 1518  
257 0 0 6 447 1518 0 0 10 /**
 *
 */ 1 4 this 3 1693  
258 0 0 6 448 1518 0 0 10 /**
 *
 */ 1 4 this 3 1693  
259 0 0 6 449 1518 0 0 10 /**
 *
 */ 1 4 this 3 1693  
260 0 0 6 450 1518 0 0 10 /**
 *
 */ 1 4 this 3 1693  
261 0 0 7 455 1620 0 0 0 0 
262 0 0 7 457 1571 0 0 10 /**
 *
 */ 1 4 name 5 1617  
263 0 0 6 458 1512 0 0 14 // Overlapping 1 4 this 3 1695  
264 0 0 7 459 1504 0 0 10 /**
 *
 */ 2 4 this 3 1695  3 idx 1 1512  
265 0 0 7 462 1620 0 0 0 0 
266 0 0 15 478 1699 1028 0 0 1 6 param0 0 1697  
267 0 0 7 465 1699 1028 0 101 /**
 * Named constructor intended to be used for asserts with have to return a
 * concrete value.
 */ 0 
268 0 0 7 466 1610 0 0 10 /**
 *
 */ 1 4 this 3 1697  
269 0 0 7 467 1611 0 0 10 /**
 *
 */ 1 4 this 3 1697  
270 0 0 7 468 1611 0 0 10 /**
 *
 */ 1 4 this 3 1697  
271 0 0 6 469 1510 0 0 10 /**
 *
 */ 1 4 this 3 1697  
272 0 0 6 470 1510 0 0 10 /**
 *
 */ 1 4 this 3 1697  
273 0 0 6 471 1512 0 0 10 /**
 *
 */ 1 4 this 3 1697  
274 0 0 7 480 1700 1081 0 10 /**
 *
 */ 1 4 this 3 1572  
275 0 0 7 481 1701 1136 0 10 /**
 *
 */ 1 4 this 3 1572  
276 0 0 4 482 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  8 distance 1 1512  8 material 5 1702  
277 0 0 4 483 1616 0 0 10 /**
 *
 */ 1 4 this 3 1572  
278 0 0 4 484 1616 0 0 24 // Mass, volume, density 2 4 this 3 1572  4 mass 1 1510  
279 0 0 4 485 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  7 density 1 1510  
280 0 0 4 486 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  4 mass 1 1510  9 fromfaces 5 1518  
281 0 0 4 487 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  7 density 1 1510  
282 0 0 4 488 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  4 node 1 1512  4 mass 1 1510  
283 0 0 6 489 1510 0 0 10 /**
 *
 */ 2 4 this 3 1703  4 node 1 1512  
284 0 0 6 490 1510 0 0 10 /**
 *
 */ 1 4 this 3 1703  
285 0 0 6 491 1510 0 0 10 /**
 *
 */ 1 4 this 3 1703  
286 0 0 4 492 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  5 force 1 1651  
287 0 0 4 492 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  5 force 1 1651  4 node 1 1512  
288 0 0 4 493 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  8 velocity 1 1651  
289 0 0 4 494 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  8 velocity 1 1651  
290 0 0 4 494 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  8 velocity 1 1651  4 node 1 1512  
291 0 0 4 495 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  8 velocity 1 1651  
292 0 0 7 496 1611 0 0 10 /**
 *
 */ 1 4 this 3 1703  
293 0 0 4 497 1616 0 0 10 /**
 *
 */ 3 4 this 3 1572  7 bvolume 1 1518  6 bframe 1 1518  
294 0 0 7 498 1705 0 0 10 /**
 *
 */ 1 4 this 3 1703  
295 0 0 4 499 1616 0 0 10 // Cluster 3 4 this 3 1572  1 k 1 1512  13 maxiterations 5 1512  
296 0 0 4 500 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  5 index 1 1512  
297 0 0 4 501 1616 0 0 10 /**
 *
 */ 1 4 this 3 1572  
298 0 0 6 502 1512 0 0 10 /**
 *
 */ 1 4 this 3 1703  
299 0 0 7 503 1637 0 0 10 /**
 *
 */ 2 4 this 3 1703  7 cluster 1 1512  
300 0 0 4 504 1616 0 0 12 // Rendering 2 4 this 3 1572  4 geom 1 1706  
301 0 0 4 505 1616 0 0 10 /**
 *
 */ 1 4 this 3 1572  
302 0 0 4 506 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  5 curve 1 1708  
303 0 0 4 507 1616 0 0 10 /**
 *
 */ 1 4 this 3 1572  
304 0 0 4 508 1616 0 0 10 /**
 *
 */ 2 4 this 3 1572  7 surface 1 1710  
305 0 0 4 509 1616 0 0 10 /**
 *
 */ 1 4 this 3 1572  
306 0 0 4 510 1616 0 0 10 /**
 *
 */ 5 4 this 3 1572  4 node 1 1512  4 body 1 1542  5 pivot 1 1651  7 disable 5 1518  
307 0 0 4 510 1616 0 0 10 // Anchors 4 4 this 3 1572  4 node 1 1512  4 body 1 1542  7 disable 5 1518  
308 0 0 4 511 1616 0 0 10 /**
 *
 */ 6 4 this 3 1572  4 body 1 1621  3 pos 1 1671  3 erp 5 1510  3 cfm 5 1510  5 split 5 1510  
309 0 0 4 511 1616 0 0 10 /**
 *
 */ 6 4 this 3 1572  4 body 1 1621  7 cluster 1 1512  3 erp 5 1510  3 cfm 5 1510  5 split 5 1510  
310 0 0 4 512 1616 0 0 10 /**
 *
 */ 7 4 this 3 1572  4 body 1 1621  4 axis 1 1651  3 erp 5 1510  3 cfm 5 1510  5 split 5 1510  7 control 5 1712  
311 0 0 6 513 1512 0 0 12 // Materials 1 4 this 3 1703  
312 0 0 7 514 1702 1149 0 10 /**
 *
 */ 2 4 this 3 1703  3 idx 1 1512  
313 0 0 7 516 1702 1149 0 10 /**
 *
 */ 1 4 this 3 1572  
314 0 0 6 517 1512 0 0 10 /**
 *
 */ 1 4 this 3 1703  
315 0 0 7 518 1699 1028 0 10 /**
 *
 */ 2 4 this 3 1703  3 idx 1 1512  
316 0 0 6 520 1512 0 0 212 /**
 * Returns the index of the node which is closest to the given point.  The
 * distance between each node and the given point is computed in world space
 * if local=false, and in local space if local=true.
 */ 3 4 this 3 1572  5 point 1 1637  5 local 1 1518  
317 0 0 7 521 1572 0 0 10 // Factory 5 4 info 1 1701  4 from 1 1671  2 to 1 1671  3 res 1 1512  6 fixeds 1 1512  
318 0 0 7 522 1572 0 0 10 /**
 *
 */ 9 4 info 1 1701  8 corner00 1 1671  8 corner10 1 1671  8 corner01 1 1671  8 corner11 1 1671  4 resx 1 1512  4 resy 1 1512  6 fixeds 1 1512  8 gendiags 1 1518  
319 0 0 7 523 1572 0 0 10 /**
 *
 */ 4 4 info 1 1701  6 center 1 1671  6 radius 1 1635  3 res 1 1512  
320 0 0 7 524 1572 0 0 10 /**
 *
 */ 3 4 info 1 1701  4 geom 1 1713  20 randomizeConstraints 5 1518  
321 0 0 7 524 1572 0 0 10 /**
 *
 */ 4 4 info 1 1701  6 points 1 1715  7 indices 1 1719  20 randomizeConstraints 5 1518  
322 0 0 7 525 1572 0 0 10 /**
 *
 */ 4 4 info 1 1701  6 points 1 1715  7 indices 1 1719  10 tetralinks 5 1518  
323 0 0 7 525 1572 0 0 10 /**
 *
 */ 4 4 info 1 1701  3 ele 1 1617  4 face 1 1617  4 node 1 1617  
324 0 0 7 649 1620 0 0 0 0 
325 0 0 15 609 1700 1081 0 0 1 6 param0 0 1722  
326 0 0 4 531 1616 0 0 10 /**
 *
 */ 1 4 this 3 1700  
327 0 0 4 532 1616 0 0 10 /**
 *
 */ 3 4 this 3 1700  4 flag 1 1558  5 value 1 1518  
328 0 0 6 533 1518 0 0 10 /**
 *
 */ 2 4 this 3 1722  4 flag 1 1558  
329 0 0 4 534 1616 0 0 10 /**
 *
 */ 2 4 this 3 1700  5 value 1 1559  
330 0 0 6 535 1559 0 0 10 /**
 *
 */ 1 4 this 3 1722  
331 0 0 4 536 1616 0 0 36 /**
 * Setter for property kVCF.
 */ 2 4 this 3 1700  5 value 1 1510  
332 0 0 4 537 1616 0 0 35 /**
 * Setter for property kDP.
 */ 2 4 this 3 1700  5 value 1 1510  
333 0 0 4 538 1616 0 0 35 /**
 * Setter for property kDG.
 */ 2 4 this 3 1700  5 value 1 1510  
334 0 0 4 539 1616 0 0 35 /**
 * Setter for property kLF.
 */ 2 4 this 3 1700  5 value 1 1510  
335 0 0 4 540 1616 0 0 35 /**
 * Setter for property kPR.
 */ 2 4 this 3 1700  5 value 1 1510  
336 0 0 4 541 1616 0 0 35 /**
 * Setter for property kVC.
 */ 2 4 this 3 1700  5 value 1 1510  
337 0 0 4 542 1616 0 0 35 /**
 * Setter for property kDF.
 */ 2 4 this 3 1700  5 value 1 1510  
338 0 0 4 543 1616 0 0 35 /**
 * Setter for property kMT.
 */ 2 4 this 3 1700  5 value 1 1510  
339 0 0 4 544 1616 0 0 36 /**
 * Setter for property kCHR.
 */ 2 4 this 3 1700  5 value 1 1510  
340 0 0 4 545 1616 0 0 36 /**
 * Setter for property kKHR.
 */ 2 4 this 3 1700  5 value 1 1510  
341 0 0 4 546 1616 0 0 36 /**
 * Setter for property kSHR.
 */ 2 4 this 3 1700  5 value 1 1510  
342 0 0 4 547 1616 0 0 36 /**
 * Setter for property kAHR.
 */ 2 4 this 3 1700  5 value 1 1510  
343 0 0 4 548 1616 0 0 40 /**
 * Setter for property kSRHR_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
344 0 0 4 549 1616 0 0 40 /**
 * Setter for property kSKHR_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
345 0 0 4 550 1616 0 0 40 /**
 * Setter for property kSSHR_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
346 0 0 4 551 1616 0 0 43 /**
 * Setter for property kSR_SPLT_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
347 0 0 4 552 1616 0 0 43 /**
 * Setter for property kSK_SPLT_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
348 0 0 4 553 1616 0 0 43 /**
 * Setter for property kSS_SPLT_CL.
 */ 2 4 this 3 1700  5 value 1 1510  
349 0 0 4 554 1616 0 0 41 /**
 * Setter for property maxvolume.
 */ 2 4 this 3 1700  5 value 1 1510  
350 0 0 4 555 1616 0 0 41 /**
 * Setter for property timescale.
 */ 2 4 this 3 1700  5 value 1 1510  
351 0 0 4 556 1616 0 0 43 /**
 * Setter for property piterations.
 */ 2 4 this 3 1700  5 value 1 1512  
352 0 0 4 557 1616 0 0 43 /**
 * Setter for property viterations.
 */ 2 4 this 3 1700  5 value 1 1512  
353 0 0 4 558 1616 0 0 43 /**
 * Setter for property diterations.
 */ 2 4 this 3 1700  5 value 1 1512  
354 0 0 4 559 1616 0 0 43 /**
 * Setter for property citerations.
 */ 2 4 this 3 1700  5 value 1 1512  
355 0 0 6 560 1510 0 0 36 /**
 * Getter for property kVCF.
 */ 1 4 this 3 1722  
356 0 0 6 561 1510 0 0 35 /**
 * Getter for property kDP.
 */ 1 4 this 3 1722  
357 0 0 6 562 1510 0 0 35 /**
 * Getter for property kDG.
 */ 1 4 this 3 1722  
358 0 0 6 563 1510 0 0 35 /**
 * Getter for property kLF.
 */ 1 4 this 3 1722  
359 0 0 6 564 1510 0 0 35 /**
 * Getter for property kPR.
 */ 1 4 this 3 1722  
360 0 0 6 565 1510 0 0 35 /**
 * Getter for property kVC.
 */ 1 4 this 3 1722  
361 0 0 6 566 1510 0 0 35 /**
 * Getter for property kDF.
 */ 1 4 this 3 1722  
362 0 0 6 567 1510 0 0 35 /**
 * Getter for property kMT.
 */ 1 4 this 3 1722  
363 0 0 6 568 1510 0 0 36 /**
 * Getter for property kCHR.
 */ 1 4 this 3 1722  
364 0 0 6 569 1510 0 0 36 /**
 * Getter for property kKHR.
 */ 1 4 this 3 1722  
365 0 0 6 570 1510 0 0 36 /**
 * Getter for property kSHR.
 */ 1 4 this 3 1722  
366 0 0 6 571 1510 0 0 36 /**
 * Getter for property kAHR.
 */ 1 4 this 3 1722  
367 0 0 6 572 1510 0 0 40 /**
 * Getter for property kSRHR_CL.
 */ 1 4 this 3 1722  
368 0 0 6 573 1510 0 0 40 /**
 * Getter for property kSKHR_CL.
 */ 1 4 this 3 1722  
369 0 0 6 574 1510 0 0 40 /**
 * Getter for property kSSHR_CL.
 */ 1 4 this 3 1722  
370 0 0 6 575 1510 0 0 43 /**
 * Getter for property kSR_SPLT_CL.
 */ 1 4 this 3 1722  
371 0 0 6 576 1510 0 0 43 /**
 * Getter for property kSK_SPLT_CL.
 */ 1 4 this 3 1722  
372 0 0 6 577 1510 0 0 43 /**
 * Getter for property kSS_SPLT_CL.
 */ 1 4 this 3 1722  
373 0 0 6 578 1510 0 0 41 /**
 * Getter for property maxvolume.
 */ 1 4 this 3 1722  
374 0 0 6 579 1510 0 0 41 /**
 * Getter for property timescale.
 */ 1 4 this 3 1722  
375 0 0 6 580 1512 0 0 43 /**
 * Getter for property piterations.
 */ 1 4 this 3 1722  
376 0 0 6 581 1512 0 0 43 /**
 * Getter for property viterations.
 */ 1 4 this 3 1722  
377 0 0 6 582 1512 0 0 43 /**
 * Getter for property diterations.
 */ 1 4 this 3 1722  
378 0 0 6 583 1512 0 0 43 /**
 * Getter for property citerations.
 */ 1 4 this 3 1722  
379 0 0 15 629 1701 1136 0 0 1 6 param0 0 1724  
380 0 0 4 613 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  7 density 1 1510  
381 0 0 4 614 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  7 density 1 1510  
382 0 0 4 615 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  6 offset 1 1510  
383 0 0 4 616 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  6 normal 1 1651  
384 0 0 4 617 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  7 gravity 1 1651  
385 0 0 6 618 1510 0 0 10 /**
 *
 */ 1 4 this 3 1724  
386 0 0 6 619 1510 0 0 10 /**
 *
 */ 1 4 this 3 1724  
387 0 0 6 620 1510 0 0 10 /**
 *
 */ 1 4 this 3 1724  
388 0 0 7 621 1611 0 0 10 /**
 *
 */ 1 4 this 3 1724  
389 0 0 7 622 1611 0 0 10 /**
 *
 */ 1 4 this 3 1724  
390 0 0 4 623 1616 0 0 10 /**
 *
 */ 2 4 this 3 1701  8 lifetime 5 1512  
391 0 0 15 647 1702 1149 0 0 1 6 param0 0 1726  
392 0 0 7 637 1702 1149 0 102 /**
 * Named constructor intended to be used for asserts which have to return a
 * concrete value.
 */ 0 
393 0 0 6 638 1510 0 0 42 /**
 * Getter for the property m_kLST.
 */ 1 4 this 3 1726  
394 0 0 4 639 1616 0 0 42 /**
 * Setter for the property m_kLST.
 */ 2 4 this 3 1702  5 value 1 1510  
395 0 0 6 640 1510 0 0 42 /**
 * Getter for the property m_kAST.
 */ 1 4 this 3 1726  
396 0 0 4 641 1616 0 0 42 /**
 * Setter for the property m_kAST.
 */ 2 4 this 3 1702  5 value 1 1510  
397 0 0 6 642 1510 0 0 42 /**
 * Getter for the property m_kVST.
 */ 1 4 this 3 1726  
398 0 0 4 643 1616 0 0 42 /**
 * Setter for the property m_kVST.
 */ 2 4 this 3 1702  5 value 1 1510  
399 0 0 4 651 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
400 0 0 4 652 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
401 0 0 4 653 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
402 0 0 4 654 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
403 0 0 4 655 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
404 0 0 4 656 1616 0 0 10 /**
 *
 */ 2 4 this 3 1728  5 value 1 1510  
405 0 0 6 657 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
406 0 0 6 658 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
407 0 0 6 659 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
408 0 0 6 660 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
409 0 0 6 661 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
410 0 0 6 662 1510 0 0 10 /**
 *
 */ 1 4 this 3 1729  
411 0 0 23 670 1574 0 0 94 /**
 * Creates a new BulletVehicle instance in the given world and with a chassis
 * node.
 */ 2 5 world 1 1731  7 chassis 1 1542  
412 0 0 4 671 1616 0 0 100 /**
 * Specifies which axis is "up". Nessecary for the vehicle's suspension to
 * work properly!
 */ 2 4 this 3 1574  2 up 1 1502  
413 0 0 4 672 1616 0 0 76 /**
 * Sets the steering value (in degrees) of the wheel with index idx.
 */ 3 4 this 3 1574  8 steering 1 1510  3 idx 1 1512  
414 0 0 4 673 1616 0 0 61 /**
 * Applies braking force to the wheel with index idx.
 */ 3 4 this 3 1574  5 brake 1 1510  3 idx 1 1512  
415 0 0 4 674 1616 0 0 10 /**
 *
 */ 2 4 this 3 1574  5 pitch 1 1510  
416 0 0 7 675 1542 0 0 82 /**
 * Returns the chassis of this vehicle.  The chassis is a rigid body node.
 */ 1 4 this 3 1574  
417 0 0 6 676 1510 0 0 106 /**
 * Returns the current speed in kilometers per hour.  Convert to miles using:
 * km/h * 0.62 = mph
 */ 1 4 this 3 1732  
418 0 0 6 677 1510 0 0 77 /**
 * Returns the steering angle of the wheel with index idx in degrees.
 */ 2 4 this 3 1732  3 idx 1 1512  
419 0 0 7 678 1611 0 0 145 /**
 * Returns the forward vector representing the car's actual direction of
 * movement.  The forward vetcor is given in global coordinates.
 */ 1 4 this 3 1732  
420 0 0 4 679 1616 0 0 43 /**
 * Resets the vehicle's suspension.
 */ 1 4 this 3 1574  
421 0 0 4 680 1616 0 0 70 /**
 * Applies force at the wheel with index idx for acceleration.
 */ 3 4 this 3 1574  5 force 1 1510  3 idx 1 1512  
422 0 0 7 681 1734 1186 0 72 /**
 * Factory method for creating wheels for this vehicle instance.
 */ 1 4 this 3 1574  
423 0 0 6 682 1512 0 0 57 /**
 * Returns the number of wheels this vehicle has.
 */ 1 4 this 3 1732  
424 0 0 7 683 1734 1186 0 132 /**
 * Returns the BulletWheel with index idx.  Causes an AssertionError if idx is
 * equal or larger than the number of wheels.
 */ 2 4 this 3 1732  3 idx 1 1512  
425 0 0 6 685 1728 0 0 189 /**
 * Returns a reference to the BulletVehicleTuning object of this vehicle which
 * offers various vehicle-global tuning options.  Make sure to configure this
 * before adding wheels!
 */ 1 4 this 3 1574  
426 0 0 7 786 1620 0 0 0 0 
427 0 0 15 784 1734 1186 0 0 1 6 param0 0 1735  
428 0 0 4 692 1616 0 0 50 /**
 * Sets how stiff the suspension shall be.
 */ 2 4 this 3 1734  5 value 1 1510  
429 0 0 4 693 1616 0 0 109 /**
 * Sets the maximum distance the suspension can travel out of the resting
 * position in centimeters.
 */ 2 4 this 3 1734  5 value 1 1510  
430 0 0 4 694 1616 0 0 45 /**
 * Sets the slipperyness of the tyre.
 */ 2 4 this 3 1734  5 value 1 1510  
431 0 0 4 695 1616 0 0 66 /**
 * Sets the maximum suspension force the wheel can handle.
 */ 2 4 this 3 1734  5 value 1 1510  
432 0 0 4 696 1616 0 0 79 /**
 * Sets the damping forces applied when the suspension gets compressed.
 */ 2 4 this 3 1734  5 value 1 1510  
433 0 0 4 697 1616 0 0 71 /**
 * Sets the damping forces applied when the suspension relaxes.
 */ 2 4 this 3 1734  5 value 1 1510  
434 0 0 4 698 1616 0 0 249 /**
 * Defines a scaling factor for roll forces that affect the chassis.  0.0
 * means no roll - the chassis won't ever flip over - while 1.0 means original
 * physical behaviour.  Basically, this allows moving the center of mass up
 * and down.
 */ 2 4 this 3 1734  5 value 1 1510  
435 0 0 4 699 1616 0 0 33 /**
 * Sets the wheel radius.
 */ 2 4 this 3 1734  5 value 1 1510  
436 0 0 4 700 1616 0 0 35 /**
 * Sets the steering angle.
 */ 2 4 this 3 1734  5 value 1 1510  
437 0 0 4 701 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
438 0 0 4 702 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
439 0 0 4 703 1616 0 0 69 /**
 * Defines how much force should be used to rotate the wheel.
 */ 2 4 this 3 1734  5 value 1 1510  
440 0 0 4 704 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
441 0 0 4 705 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
442 0 0 4 706 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
443 0 0 4 707 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
444 0 0 4 708 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  5 value 1 1510  
445 0 0 4 709 1616 0 0 70 /**
 * Sets the point where the wheel is connected to the chassis.
 */ 2 4 this 3 1734  3 pos 1 1671  
446 0 0 4 710 1616 0 0 92 /**
 * Sets the wheel's forward vector.  (Most likely orthogonal to the axle
 * vector.)
 */ 2 4 this 3 1734  3 dir 1 1651  
447 0 0 4 711 1616 0 0 51 /**
 * Determines the wheel axle normal vector.
 */ 2 4 this 3 1734  4 axle 1 1651  
448 0 0 4 712 1616 0 0 10 /**
 *
 */ 2 4 this 3 1734  3 mat 1 1737  
449 0 0 4 713 1616 0 0 42 /**
 * Sets if the wheel is steerable.
 */ 2 4 this 3 1734  5 value 1 1518  
450 0 0 4 714 1616 0 0 86 /**
 * Sets the PandaNode which representates the visual appearance of this wheel.
 */ 2 4 this 3 1734  4 node 1 1504  
451 0 0 6 715 1510 0 0 83 /**
 * Returns the length of the suspension when the vehicle is standing still.
 */ 1 4 this 3 1735  
452 0 0 6 716 1510 0 0 51 /**
 * Returns the stiffness of the suspension.
 */ 1 4 this 3 1735  
453 0 0 6 717 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
454 0 0 6 718 1510 0 0 46 /**
 * Returns how slippery the tyres are.
 */ 1 4 this 3 1735  
455 0 0 6 719 1510 0 0 72 /**
 * Returns the maximum force (weight) the suspension can handle.
 */ 1 4 this 3 1735  
456 0 0 6 720 1510 0 0 70 /**
 * Returns the  damping applied to the compressing suspension.
 */ 1 4 this 3 1735  
457 0 0 6 721 1510 0 0 66 /**
 * Returns the damping applied to the relaxing suspension.
 */ 1 4 this 3 1735  
458 0 0 6 722 1510 0 0 90 /**
 * Returns the factor by which roll forces are scaled.  See
 * set_roll_influence.
 */ 1 4 this 3 1735  
459 0 0 6 723 1510 0 0 36 /**
 * Returns the wheel radius.
 */ 1 4 this 3 1735  
460 0 0 6 724 1510 0 0 49 /**
 * Returns the steering angle in degrees.
 */ 1 4 this 3 1735  
461 0 0 6 725 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
462 0 0 6 726 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
463 0 0 6 727 1510 0 0 71 /**
 * Returns the amount of accelleration force currently applied.
 */ 1 4 this 3 1735  
464 0 0 6 728 1510 0 0 65 /**
 * Returns the amount of braking force currently applied.
 */ 1 4 this 3 1735  
465 0 0 6 729 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
466 0 0 6 730 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
467 0 0 6 731 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
468 0 0 6 732 1510 0 0 10 /**
 *
 */ 1 4 this 3 1735  
469 0 0 7 733 1610 0 0 73 /**
 * Returns the point where the wheel is connected to the chassis.
 */ 1 4 this 3 1735  
470 0 0 7 734 1611 0 0 70 /**
 * Returns the wheel's forward vector relative to the chassis.
 */ 1 4 this 3 1735  
471 0 0 7 735 1611 0 0 55 /**
 * Returns the normal vector of the wheel axle.
 */ 1 4 this 3 1735  
472 0 0 7 736 1627 0 0 10 /**
 *
 */ 1 4 this 3 1735  
473 0 0 6 737 1518 0 0 46 /**
 * Determines if a wheel is steerable.
 */ 1 4 this 3 1735  
474 0 0 7 738 1504 0 0 142 /**
 * Returns the PandaNode which representates the visual appearance of this
 * wheel, if such a representation has been set previously.
 */ 1 4 this 3 1735  
475 0 0 7 739 1739 1235 0 10 /**
 *
 */ 1 4 this 3 1735  
476 0 0 15 759 1739 1235 0 0 1 6 param0 0 1740  
477 0 0 6 743 1518 0 0 10 /**
 *
 */ 1 4 this 3 1740  
478 0 0 6 744 1510 0 0 10 /**
 *
 */ 1 4 this 3 1740  
479 0 0 7 745 1611 0 0 10 /**
 *
 */ 1 4 this 3 1740  
480 0 0 7 746 1611 0 0 10 /**
 *
 */ 1 4 this 3 1740  
481 0 0 7 747 1611 0 0 10 /**
 *
 */ 1 4 this 3 1740  
482 0 0 7 748 1610 0 0 10 /**
 *
 */ 1 4 this 3 1740  
483 0 0 7 749 1610 0 0 10 /**
 *
 */ 1 4 this 3 1740  
484 0 0 7 750 1504 0 0 10 /**
 *
 */ 1 4 this 3 1740  
485 0 0 7 788 1731 0 0 10 /**
 *
 */ 0 
486 0 0 4 789 1616 0 0 10 /**
 *
 */ 2 4 this 3 1731  7 gravity 1 1651  
487 0 0 4 789 1616 0 0 10 /**
 *
 */ 4 4 this 3 1731  2 gx 1 1510  2 gy 1 1510  2 gz 1 1510  
488 0 0 7 790 1651 0 0 10 /**
 *
 */ 1 4 this 3 1742  
489 0 0 6 791 1512 0 0 10 /**
 *
 */ 4 4 this 3 1731  2 dt 1 1510  12 max_substeps 5 1512  8 stepsize 5 1510  
490 0 0 7 792 1701 1136 0 10 /**
 *
 */ 1 4 this 3 1731  
491 0 0 4 793 1616 0 0 10 /**
 *
 */ 2 4 this 3 1731  4 node 1 1570  
492 0 0 4 794 1616 0 0 75 /**
 * Removes a debug node that has been assigned to this BulletWorld.
 */ 1 4 this 3 1731  
493 0 0 7 795 1570 0 0 10 /**
 *
 */ 1 4 this 3 1742  
494 0 0 6 796 1518 0 0 10 /**
 *
 */ 1 4 this 3 1742  
495 0 0 4 797 1616 0 0 15 // AttachRemove 2 4 this 3 1731  6 object 1 1744  
496 0 0 4 798 1616 0 0 10 /**
 *
 */ 2 4 this 3 1731  6 object 1 1744  
497 0 0 4 799 1616 0 0 156 /**
 * Attaches a single constraint to a world.  Collision checks between the
 * linked objects will be disabled if the second parameter is set to TRUE.
 */ 3 4 this 3 1731  10 constraint 1 1575  16 linked_collision 5 1518  
498 0 0 6 800 1512 0 0 15 // Ghost object 1 4 this 3 1742  
499 0 0 7 801 1571 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
500 0 0 6 803 1512 0 0 13 // Rigid body 1 4 this 3 1742  
501 0 0 7 804 1542 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
502 0 0 6 806 1512 0 0 12 // Soft body 1 4 this 3 1742  
503 0 0 7 807 1572 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
504 0 0 6 809 1512 0 0 23 // Character controller 1 4 this 3 1742  
505 0 0 7 810 1573 789 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
506 0 0 6 812 1512 0 0 10 /**
 *
 */ 1 4 this 3 1742  
507 0 0 7 813 1574 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
508 0 0 6 815 1512 0 0 13 // Constraint 1 4 this 3 1742  
509 0 0 7 816 1575 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
510 0 0 7 818 1657 874 0 28 // Raycast and other queries 4 4 this 3 1742  8 from_pos 1 1671  6 to_pos 1 1671  4 mask 5 1746  
511 0 0 7 819 1612 775 0 10 /**
 *
 */ 4 4 this 3 1742  8 from_pos 1 1671  6 to_pos 1 1671  4 mask 5 1746  
512 0 0 7 820 1661 886 0 203 /**
 * Performs a sweep test against all other shapes that match the given group
 * mask.  The provided shape must be a convex shape; it is an error to invoke
 * this method using a non-convex shape.
 */ 6 4 this 3 1742  5 shape 1 1525  7 from_ts 1 1622  5 to_ts 1 1622  4 mask 5 1746  11 penetration 5 1510  
513 0 0 7 821 1691 1012 0 386 /**
 * Performas a test for all bodies which are currently in contact with the
 * given body.  The test returns a BulletContactResult object which may
 * contain zero, one or more contacts.
 *
 * If the optional parameter use_filter is set to TRUE this test will consider
 * filter settings.  Otherwise all objects in contact are reported, no matter
 * if they would collide or not.
 */ 3 4 this 3 1742  4 node 1 1504  10 use_filter 5 1518  
514 0 0 7 822 1691 1012 0 181 /**
 * Performas a test if the two bodies given as parameters are in contact or
 * not.  The test returns a BulletContactResult object which may contain zero
 * or one contacts.
 */ 3 4 this 3 1742  5 node0 1 1504  5 node1 1 1504  
515 0 0 6 823 1518 0 0 105 /**
 * Performs a test if two bodies should collide or not, based on the collision
 * filter setting.
 */ 3 4 this 3 1742  5 node0 1 1504  5 node1 1 1504  
516 0 0 6 824 1512 0 0 12 // Manifolds 1 4 this 3 1742  
517 0 0 6 825 1748 0 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
518 0 0 7 826 1748 1290 0 10 /**
 *
 */ 2 4 this 3 1742  3 idx 1 1512  
519 0 0 4 828 1616 0 0 22 // Collision filtering 4 4 this 3 1731  6 group1 1 1749  6 group2 1 1749  6 enable 1 1518  
520 0 0 6 829 1518 0 0 10 /**
 *
 */ 3 4 this 3 1742  6 group1 1 1749  6 group2 1 1749  
521 0 0 4 830 1616 0 0 10 /**
 *
 */ 2 4 this 3 1731  5 force 1 1518  
522 0 0 6 831 1518 0 0 10 /**
 *
 */ 1 4 this 3 1742  
523 0 0 4 832 1616 0 0 12 // Callbacks 2 4 this 3 1731  3 obj 1 1750  
524 0 0 4 833 1616 0 0 10 /**
 *
 */ 1 4 this 3 1731  
525 0 0 4 834 1616 0 0 10 /**
 *
 */ 3 4 this 3 1731  3 obj 1 1750  10 is_pretick 5 1518  
526 0 0 4 835 1616 0 0 10 /**
 *
 */ 1 4 this 3 1731  
527 0 0 4 836 1616 0 0 10 /**
 *
 */ 2 4 this 3 1731  3 obj 1 1750  
528 0 0 4 837 1616 0 0 10 /**
 *
 */ 1 4 this 3 1731  
529 0 0 4 874 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::attach
 */ 2 4 this 3 1731  4 node 1 1571  
530 0 0 4 875 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  4 node 1 1571  
531 0 0 4 876 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::attach
 */ 2 4 this 3 1731  4 node 1 1542  
532 0 0 4 877 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  4 node 1 1542  
533 0 0 4 878 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::attach
 */ 2 4 this 3 1731  4 node 1 1572  
534 0 0 4 879 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  4 node 1 1572  
535 0 0 4 880 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::attach
 */ 2 4 this 3 1731  4 node 1 1573  
536 0 0 4 881 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  4 node 1 1573  
537 0 0 4 882 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::attach
 */ 2 4 this 3 1731  7 vehicle 1 1574  
538 0 0 4 883 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  7 vehicle 1 1574  
539 0 0 4 884 1616 0 0 53 /**
 * @deprecated Please use BulletWorld::remove
 */ 2 4 this 3 1731  10 constraint 1 1575  
540 0 0 7 885 1620 0 0 0 0 
541 0 0 15 872 1748 1290 0 0 1 6 param0 0 1752  
542 0 0 7 858 1504 0 0 10 /**
 *
 */ 1 4 this 3 1748  
543 0 0 7 859 1504 0 0 10 /**
 *
 */ 1 4 this 3 1748  
544 0 0 6 860 1512 0 0 10 /**
 *
 */ 1 4 this 3 1752  
545 0 0 6 861 1682 0 0 10 /**
 *
 */ 2 4 this 3 1752  3 idx 1 1512  
546 0 0 7 862 1682 955 0 10 /**
 *
 */ 2 4 this 3 1752  3 idx 1 1512  
547 0 0 6 864 1510 0 0 10 /**
 *
 */ 1 4 this 3 1752  
548 0 0 6 865 1510 0 0 10 /**
 *
 */ 1 4 this 3 1752  
549 0 0 4 866 1616 0 0 10 /**
 *
 */ 1 4 this 3 1748  
550 0 0 7 887 1754 0 0 10 /**
 *
 */ 0 
551 0 0 15 887 1754 0 0 10 /**
 *
 */ 1 4 copy 1 1755  
552 0 0 4 888 1616 0 0 10 /**
 *
 */ 2 4 this 3 1754  1 p 1 1671  
553 0 0 4 889 1616 0 0 10 /**
 *
 */ 2 4 this 3 1754  6 points 1 1757  
554 0 0 4 890 1616 0 0 10 /**
 *
 */ 3 4 this 3 1754  4 geom 1 1713  2 ts 5 1622  
555 0 0 7 891 1620 0 0 0 0 
556 0 0 15 893 1761 0 0 10 /**
 *
 */ 1 4 copy 1 1759  
557 0 0 7 893 1761 0 0 10 /**
 *
 */ 2 4 geom 1 1713  5 scale 5 1637  
558 0 0 7 893 1761 0 0 10 /**
 *
 */ 2 6 points 1 1757  5 scale 5 1637  
559 0 0 6 894 1512 0 0 10 /**
 *
 */ 1 4 this 3 1759  
560 0 0 7 896 1620 0 0 0 0 
561 0 0 15 898 1764 0 0 10 /**
 *
 */ 1 4 copy 1 1762  
562 0 0 7 898 1764 0 0 10 /**
 *
 */ 2 12 half_extents 1 1651  2 up 5 1502  
563 0 0 7 898 1764 0 0 10 /**
 *
 */ 3 6 radius 1 1510  6 height 1 1510  2 up 5 1502  
564 0 0 6 899 1510 0 0 10 /**
 *
 */ 1 4 this 3 1762  
565 0 0 7 900 1637 0 0 10 /**
 *
 */ 1 4 this 3 1762  
566 0 0 7 901 1637 0 0 10 /**
 *
 */ 1 4 this 3 1762  
567 0 0 7 905 1620 0 0 0 0 
568 0 0 23 907 1765 1331 0 10 /**
 *
 */ 2 5 node0 1 1504  5 node1 1 1504  
569 0 0 7 908 1504 0 0 10 /**
 *
 */ 1 4 this 3 1766  
570 0 0 7 909 1504 0 0 10 /**
 *
 */ 1 4 this 3 1766  
571 0 0 4 910 1616 0 0 10 /**
 *
 */ 2 4 this 3 1765  7 collide 1 1518  
572 0 0 6 911 1518 0 0 10 /**
 *
 */ 1 4 this 3 1766  
573 0 0 7 915 1620 0 0 0 0 
574 0 0 15 918 1770 1333 0 10 /**
 *
 */ 1 4 copy 1 1768  
575 0 0 4 920 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  6 enable 1 1518  
576 0 0 4 921 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  5 limit 1 1510  
577 0 0 4 922 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  5 limit 1 1510  
578 0 0 4 923 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  8 velocity 1 1510  
579 0 0 4 924 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  5 force 1 1510  
580 0 0 4 925 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  5 force 1 1510  
581 0 0 4 926 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  7 damping 1 1510  
582 0 0 4 927 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  8 softness 1 1510  
583 0 0 4 928 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  6 bounce 1 1510  
584 0 0 4 929 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  3 cfm 1 1510  
585 0 0 4 930 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  3 cfm 1 1510  
586 0 0 4 931 1616 0 0 10 /**
 *
 */ 2 4 this 3 1770  3 erp 1 1510  
587 0 0 6 932 1518 0 0 10 /**
 *
 */ 1 4 this 3 1768  
588 0 0 6 933 1518 0 0 10 /**
 *
 */ 1 4 this 3 1768  
589 0 0 6 934 1512 0 0 98 /**
 * Retrieves the current value of angle: 0 = free, 1 = at low limit, 2 = at
 * high limit.
 */ 1 4 this 3 1768  
590 0 0 6 935 1510 0 0 10 /**
 *
 */ 1 4 this 3 1768  
591 0 0 6 936 1510 0 0 10 /**
 *
 */ 1 4 this 3 1768  
592 0 0 6 937 1510 0 0 10 /**
 *
 */ 1 4 this 3 1768  
593 0 0 15 945 1773 1353 0 10 /**
 *
 */ 1 4 copy 1 1771  
594 0 0 4 947 1616 0 0 10 /**
 *
 */ 3 4 this 3 1773  4 axis 1 1512  6 enable 1 1518  
595 0 0 4 948 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  5 limit 1 1635  
596 0 0 4 949 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  5 limit 1 1635  
597 0 0 4 950 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  8 velocity 1 1635  
598 0 0 4 951 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  5 force 1 1635  
599 0 0 4 952 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  7 damping 1 1510  
600 0 0 4 953 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  8 softness 1 1510  
601 0 0 4 954 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  11 restitution 1 1510  
602 0 0 4 955 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  3 cfm 1 1635  
603 0 0 4 956 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  3 erp 1 1635  
604 0 0 4 957 1616 0 0 10 /**
 *
 */ 2 4 this 3 1773  3 cfm 1 1635  
605 0 0 6 958 1518 0 0 10 /**
 *
 */ 2 4 this 3 1771  4 axis 1 1512  
606 0 0 6 959 1518 0 0 10 /**
 *
 */ 2 4 this 3 1771  4 axis 1 1512  
607 0 0 6 960 1512 0 0 98 /**
 * Retrieves the current value of angle: 0 = free, 1 = at low limit, 2 = at
 * high limit.
 */ 2 4 this 3 1771  4 axis 1 1512  
608 0 0 7 961 1611 0 0 10 /**
 *
 */ 1 4 this 3 1771  
609 0 0 7 962 1610 0 0 10 /**
 *
 */ 1 4 this 3 1771  
610 0 0 7 963 1611 0 0 10 /**
 *
 */ 1 4 this 3 1771  
611 0 0 7 968 1774 0 0 10 /**
 *
 */ 5 6 node_a 1 1668  6 node_b 1 1668  7 frame_a 1 1622  7 frame_b 1 1622  11 use_frame_a 1 1518  
612 0 0 7 968 1774 0 0 10 /**
 *
 */ 3 6 node_a 1 1668  7 frame_a 1 1622  11 use_frame_a 1 1518  
613 0 0 7 969 1611 0 0 11 // Geometry 2 4 this 3 1775  4 axis 1 1512  
614 0 0 6 970 1510 0 0 10 /**
 *
 */ 2 4 this 3 1775  4 axis 1 1512  
615 0 0 6 971 1510 0 0 10 /**
 *
 */ 2 4 this 3 1775  4 axis 1 1512  
616 0 0 4 972 1616 0 0 10 /**
 *
 */ 4 4 this 3 1774  4 axis 1 1512  3 low 1 1510  4 high 1 1510  
617 0 0 4 973 1616 0 0 10 /**
 *
 */ 4 4 this 3 1774  4 axis 1 1512  3 low 1 1510  4 high 1 1510  
618 0 0 7 974 1770 1333 0 9 // Motors 2 4 this 3 1774  4 axis 1 1512  
619 0 0 7 975 1773 1353 0 10 /**
 *
 */ 1 4 this 3 1774  
620 0 0 4 976 1616 0 0 9 // Frames 3 4 this 3 1774  4 ts_a 1 1622  4 ts_b 1 1622  
621 0 0 7 977 1622 0 0 10 /**
 *
 */ 1 4 this 3 1775  
622 0 0 7 978 1622 0 0 10 /**
 *
 */ 1 4 this 3 1775  
623 0 0 7 982 1620 0 0 0 0 
624 0 0 15 984 1779 0 0 10 /**
 *
 */ 1 4 copy 1 1777  
625 0 0 7 984 1779 0 0 215 /**
 * @brief Creates a collision shape suited for terrains from a rectangular image.
 * @details Stores the image's brightness values in a vector Bullet can use, 
 *   while rotating it 90 degrees to the right.
 */ 3 5 image 1 1780  10 max_height 1 1510  2 up 5 1502  
626 0 0 7 984 1779 0 0 311 /**
 * @brief Creates a collision shape suited for terrains from a rectangular texture.
 * @details Alternative constructor intended for use with ShaderTerrainMesh. This will
 *   do bilinear sampling at the corners of all texels. Also works with textures 
 *   that are non-power-of-two and/or rectangular.
 */ 3 3 tex 1 1783  10 max_height 1 1510  2 up 5 1502  
627 0 0 4 985 1616 0 0 10 /**
 *
 */ 2 4 this 3 1779  4 flag 5 1518  
628 0 0 7 986 1620 0 0 0 0 
629 0 0 7 999 1785 1395 0 0 0 
630 0 0 15 999 1785 1395 0 0 1 6 param0 0 1786  
631 0 0 7 988 1791 0 0 19 // Collision shapes 2 2 np 1 1789  5 clear 5 1518  
632 0 0 7 989 1793 0 0 17 // Internal names 0 
633 0 0 7 990 1793 0 0 10 /**
 *
 */ 0 
634 0 0 7 991 1796 0 0 19 // Geom vertex data 1 6 format 1 1796  
635 0 0 7 992 1796 0 0 10 /**
 *
 */ 1 6 format 1 1796  
636 0 0 7 993 1706 0 0 13 // Geom utils 3 4 node 1 1572  6 format 5 1796  9 two_sided 5 1518  
637 0 0 7 994 1706 0 0 10 /**
 *
 */ 2 4 node 1 1572  6 format 5 1796  
638 0 0 4 995 1616 0 0 10 /**
 *
 */ 3 4 geom 1 1706  4 resx 1 1512  4 resy 1 1512  
639 0 0 7 1002 1797 0 0 342 /**
 * Creates a hinge connecting node_a to node_b.  The pivot point is the point
 * at which the body is fixed to the constraint.  In other words: It specifies
 * where on each body the rotation axis should be.  This axis is specified
 * using axis_a and axis_b.  Remember, everything is specified in the bodies
 * own coordinate system!
 */ 7 6 node_a 1 1668  6 node_b 1 1668  7 pivot_a 1 1671  7 pivot_b 1 1671  6 axis_a 1 1651  6 axis_b 1 1651  11 use_frame_a 5 1518  
640 0 0 7 1002 1797 0 0 73 /**
 * Constructs a hinge constraint which connects two rigid bodies.
 */ 5 6 node_a 1 1668  6 node_b 1 1668  4 ts_a 1 1622  4 ts_b 1 1622  11 use_frame_a 5 1518  
641 0 0 7 1002 1797 0 0 182 /**
 * Creates a hinge constraint in the same way as the other constructor, but
 * uses the world as second body so that node_a is fixed to some point in mid-
 * air for example.
 */ 4 6 node_a 1 1668  7 pivot_a 1 1671  6 axis_a 1 1651  11 use_frame_a 5 1518  
642 0 0 7 1002 1797 0 0 106 /**
 * Creates a hinge constraint which connects one rigid body with some fixe
 * dpoint in the world.
 */ 3 6 node_a 1 1668  4 ts_a 1 1622  11 use_frame_a 5 1518  
643 0 0 6 1003 1510 0 0 66 /**
 * Returns the angle between node_a and node_b in degrees.
 */ 1 4 this 3 1797  
644 0 0 6 1004 1510 0 0 54 /**
 * Returns the lower angular limit in degrees.
 */ 1 4 this 3 1798  
645 0 0 6 1005 1510 0 0 54 /**
 * Returns the upper angular limit in degrees.
 */ 1 4 this 3 1798  
646 0 0 6 1006 1518 0 0 10 /**
 *
 */ 1 4 this 3 1798  
647 0 0 4 1007 1616 0 0 10 /**
 *
 */ 2 4 this 3 1797  5 value 1 1518  
648 0 0 4 1008 1616 0 0 65 /**
 * Sets the lower and upper rotational limits in degrees.
 */ 6 4 this 3 1797  3 low 1 1510  4 high 1 1510  8 softness 5 1510  4 bias 5 1510  10 relaxation 5 1510  
649 0 0 4 1009 1616 0 0 63 /**
 * Sets the hinge's rotation axis in world coordinates.
 */ 2 4 this 3 1797  4 axis 1 1651  
650 0 0 4 1010 1616 0 0 261 /**
 * Applies an impulse to the constraint so that the angle changes at
 * target_velocity where max_impulse is the maximum impulse that is used for
 * achieving the specified velocity.
 *
 * Note that the target_velocity is in radians/second, not degrees.
 */ 4 4 this 3 1797  6 enable 1 1518  15 target_velocity 1 1510  11 max_impulse 1 1510  
651 0 0 4 1011 1616 0 0 10 /**
 *
 */ 2 4 this 3 1797  6 enable 1 1518  
652 0 0 4 1012 1616 0 0 96 /**
 * Sets the maximum impulse used to achieve the velocity set in
 * enable_angular_motor.
 */ 2 4 this 3 1797  11 max_impulse 1 1510  
653 0 0 4 1013 1616 0 0 10 /**
 *
 */ 3 4 this 3 1797  4 quat 1 1676  2 dt 1 1510  
654 0 0 4 1013 1616 0 0 10 /**
 *
 */ 3 4 this 3 1797  12 target_angle 1 1510  2 dt 1 1510  
655 0 0 4 1014 1616 0 0 10 /**
 *
 */ 3 4 this 3 1797  4 ts_a 1 1622  4 ts_b 1 1622  
656 0 0 7 1015 1622 0 0 10 /**
 *
 */ 1 4 this 3 1798  
657 0 0 7 1016 1622 0 0 10 /**
 *
 */ 1 4 this 3 1798  
658 0 0 7 1023 1620 0 0 0 0 
659 0 0 15 1025 1802 0 0 10 /**
 *
 */ 1 4 copy 1 1800  
660 0 0 7 1025 1802 0 0 10 /**
 *
 */ 2 7 shape_a 1 1589  7 shape_b 1 1589  
661 0 0 4 1026 1616 0 0 10 /**
 *
 */ 2 4 this 3 1802  2 ts 1 1622  
662 0 0 4 1027 1616 0 0 10 /**
 *
 */ 2 4 this 3 1802  2 ts 1 1622  
663 0 0 7 1028 1622 0 0 10 /**
 *
 */ 1 4 this 3 1800  
664 0 0 7 1029 1622 0 0 10 /**
 *
 */ 1 4 this 3 1800  
665 0 0 7 1030 1589 0 0 10 /**
 *
 */ 1 4 this 3 1800  
666 0 0 7 1031 1589 0 0 10 /**
 *
 */ 1 4 this 3 1800  
667 0 0 7 1038 1620 0 0 0 0 
668 0 0 15 1040 1805 0 0 10 /**
 *
 */ 1 4 copy 1 1803  
669 0 0 7 1040 1805 0 0 10 /**
 *
 */ 2 6 points 1 1757  5 radii 1 1806  
670 0 0 7 1041 1805 0 0 0 2 4 this 3 1805  4 copy 1 1803  
671 0 0 6 1042 1512 0 0 10 /**
 *
 */ 1 4 this 3 1803  
672 0 0 7 1043 1610 0 0 10 /**
 *
 */ 2 4 this 3 1803  5 index 1 1512  
673 0 0 6 1044 1510 0 0 10 /**
 *
 */ 2 4 this 3 1803  5 index 1 1512  
674 0 0 7 1048 1620 0 0 0 0 
675 0 0 15 1050 1813 0 0 10 /**
 *
 */ 1 4 copy 1 1811  
676 0 0 7 1050 1813 0 0 57 /**
 * Creates a plane shape from a plane definition.
 */ 1 5 plane 1 1814  
677 0 0 7 1050 1813 0 0 10 /**
 *
 */ 2 6 normal 1 1651  8 constant 1 1510  
678 0 0 7 1051 1814 0 0 10 /**
 *
 */ 1 4 this 3 1811  
679 0 0 7 1052 1611 0 0 10 /**
 *
 */ 1 4 this 3 1811  
680 0 0 6 1053 1510 0 0 10 /**
 *
 */ 1 4 this 3 1811  
681 0 0 7 1054 1813 0 0 10 /**
 *
 */ 1 5 solid 1 1815  
682 0 0 7 1060 1620 0 0 0 0 
683 0 0 7 1062 1818 0 0 10 /**
 *
 */ 5 6 node_a 1 1668  6 node_b 1 1668  7 frame_a 1 1622  7 frame_b 1 1622  11 use_frame_a 1 1518  
684 0 0 7 1062 1818 0 0 10 /**
 *
 */ 3 6 node_a 1 1668  7 frame_a 1 1622  10 useFrame_a 1 1518  
685 0 0 6 1063 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
686 0 0 6 1064 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
687 0 0 6 1065 1510 0 0 9 // Limits 1 4 this 3 1819  
688 0 0 6 1066 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
689 0 0 6 1067 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
690 0 0 6 1068 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
691 0 0 4 1069 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  5 value 1 1510  
692 0 0 4 1070 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  5 value 1 1510  
693 0 0 4 1071 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  5 value 1 1510  
694 0 0 4 1072 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  5 value 1 1510  
695 0 0 4 1073 1616 0 0 15 // Linear motor 2 4 this 3 1818  2 on 1 1518  
696 0 0 4 1074 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  15 target_velocity 1 1510  
697 0 0 4 1075 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  9 max_force 1 1510  
698 0 0 6 1076 1518 0 0 10 /**
 *
 */ 1 4 this 3 1819  
699 0 0 6 1077 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
700 0 0 6 1078 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
701 0 0 4 1079 1616 0 0 16 // Angular motor 2 4 this 3 1818  2 on 1 1518  
702 0 0 4 1080 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  15 target_velocity 1 1510  
703 0 0 4 1081 1616 0 0 10 /**
 *
 */ 2 4 this 3 1818  9 max_force 1 1510  
704 0 0 6 1082 1518 0 0 10 /**
 *
 */ 1 4 this 3 1819  
705 0 0 6 1083 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
706 0 0 6 1084 1510 0 0 10 /**
 *
 */ 1 4 this 3 1819  
707 0 0 4 1085 1616 0 0 9 // Frames 3 4 this 3 1818  4 ts_a 1 1622  4 ts_b 1 1622  
708 0 0 7 1086 1622 0 0 10 /**
 *
 */ 1 4 this 3 1819  
709 0 0 7 1087 1622 0 0 10 /**
 *
 */ 1 4 this 3 1819  
710 0 0 7 1102 1620 0 0 0 0 
711 0 0 7 1109 1712 1462 0 10 /**
 *
 */ 0 
712 0 0 15 1109 1712 1462 0 0 1 6 param0 0 1821  
713 0 0 4 1111 1616 0 0 10 /**
 *
 */ 2 4 this 3 1712  4 goal 1 1510  
714 0 0 4 1112 1616 0 0 10 /**
 *
 */ 2 4 this 3 1712  9 maxtorque 1 1510  
715 0 0 4 1113 1616 0 0 10 /**
 *
 */ 2 4 this 3 1712  5 angle 1 1510  
716 0 0 4 1114 1616 0 0 10 /**
 *
 */ 2 4 this 3 1712  4 sign 1 1510  
717 0 0 6 1107 1823 0 0 0 1 4 this 3 1712  
718 0 0 7 1116 1572 0 0 10 /**
 *
 */ 1 4 this 3 1824  
719 0 0 7 1118 1620 0 0 0 0 
720 0 0 15 1120 1828 0 0 10 /**
 *
 */ 1 4 copy 1 1826  
721 0 0 7 1120 1828 0 0 10 /**
 *
 */ 1 6 radius 1 1510  
722 0 0 6 1121 1510 0 0 69 /**
 * Returns the radius that was used to construct this sphere.
 */ 1 4 this 3 1826  
723 0 0 7 1122 1828 0 0 10 /**
 *
 */ 1 5 solid 1 1829  
724 0 0 7 1124 1620 0 0 0 0 
725 0 0 7 1126 1832 0 0 10 /**
 *
 */ 4 6 node_a 1 1668  6 node_b 1 1668  7 pivot_a 1 1671  7 pivot_b 1 1671  
726 0 0 7 1126 1832 0 0 10 /**
 *
 */ 2 6 node_a 1 1668  7 pivot_a 1 1671  
727 0 0 4 1127 1616 0 0 9 // Pivots 2 4 this 3 1832  7 pivot_a 1 1671  
728 0 0 4 1128 1616 0 0 10 /**
 *
 */ 2 4 this 3 1832  7 pivot_b 1 1671  
729 0 0 7 1129 1610 0 0 10 /**
 *
 */ 1 4 this 3 1833  
730 0 0 7 1130 1610 0 0 10 /**
 *
 */ 1 4 this 3 1833  
731 0 0 7 1133 1620 0 0 0 0 
732 0 0 23 1135 1837 1482 0 10 /**
 *
 */ 1 8 timestep 1 1835  
733 0 0 6 1136 1510 0 0 10 /**
 *
 */ 1 4 this 3 1838  
734 0 0 7 1138 1620 0 0 0 0 
735 0 0 7 1141 1840 0 0 10 /**
 *
 */ 0 
736 0 0 4 1142 1616 0 0 347 /**
 * Adds a triangle with the indicated coordinates.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */ 5 4 this 3 1840  2 p0 1 1671  2 p1 1 1671  2 p2 1 1671  25 remove_duplicate_vertices 5 1518  
737 0 0 4 1143 1616 0 0 452 /**
 * Adds triangle information from an array of points and indices referring to
 * these points.  This is more efficient than adding triangles one at a time.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */ 4 4 this 3 1840  6 points 1 1757  7 indices 1 1841  25 remove_duplicate_vertices 5 1518  
738 0 0 4 1144 1616 0 0 461 /**
 * Adds the geometry from the indicated Geom from the triangle mesh.  This is
 * a one-time copy operation, and future updates to the Geom will not be
 * reflected.
 *
 * If remove_duplicate_vertices is true, it will make sure that it does not
 * add duplicate vertices if they already exist in the triangle mesh, within
 * the tolerance specified by set_welding_distance().  This comes at a
 * significant performance cost, especially for large meshes.
 */ 4 4 this 3 1840  4 geom 1 1713  25 remove_duplicate_vertices 5 1518  2 ts 5 1622  
739 0 0 4 1145 1616 0 0 254 /**
 * Sets the square of the distance at which vertices will be merged
 * together when adding geometry with remove_duplicate_vertices set to true.
 *
 * The default is 0, meaning vertices will only be merged if they have the
 * exact same position.
 */ 2 4 this 3 1840  8 distance 1 1510  
740 0 0 4 1146 1616 0 0 239 /**
 * Used to reserve memory in anticipation of the given amount of vertices and
 * indices being added to the triangle mesh.  This is useful if you are about
 * to call add_triangle() many times, to prevent unnecessary reallocations.
 */ 3 4 this 3 1840  9 num_verts 1 1512  11 num_indices 1 1512  
741 0 0 6 1147 1845 0 0 65 /**
 * Returns the number of triangles in this triangle mesh.
 */ 1 4 this 3 1843  
742 0 0 6 1148 1510 0 0 110 /**
 * Returns the value previously set with set_welding_distance(), or the
 * value of 0 if none was set.
 */ 1 4 this 3 1843  
743 0 0 4 1149 1616 0 0 10 /**
 *
 */ 2 4 this 3 1843  3 out 1 1847  
744 0 0 4 1150 1616 0 0 10 /**
 *
 */ 3 4 this 3 1843  3 out 1 1847  12 indent_level 1 1512  
745 0 0 7 1158 1620 0 0 0 0 
746 0 0 6 1152 1845 0 0 64 /**
 * Returns the number of vertices in this triangle mesh.
 */ 1 4 this 3 1843  
747 0 0 7 1160 1849 0 0 155 /**
 * The parameters 'compress' and 'bvh' are only used if 'dynamic' is set to
 * FALSE.
 * Assumes the lock(bullet global lock) is held by the caller
 */ 4 4 mesh 1 1840  7 dynamic 1 1518  8 compress 5 1518  3 bvh 5 1518  
748 0 0 15 1160 1849 0 0 10 /**
 *
 */ 1 4 copy 1 1850  
749 0 0 4 1161 1616 0 0 10 /**
 *
 */ 3 4 this 3 1849  8 aabb_min 1 1671  8 aabb_max 1 1671  
750 0 0 6 1162 1518 0 0 10 /**
 *
 */ 1 4 this 3 1850  
751 0 0 6 1163 1518 0 0 10 /**
 *
 */ 1 4 this 3 1850  
752 0 0 7 1166 1620 0 0 0 0 
753 0 0 6 2 1502 0 0 10 /**
 *
 */ 0 
754 0 0 6 3 1512 0 0 60 /**
 * Returns the version of the linked Bullet library.
 */ 0 
350
1502 12 BulletUpAxis 0 532481 12 BulletUpAxis 12 BulletUpAxis 0 0 0 0 0 0 0 0 0 0 3 4 X_up 4 X_up 0
0 4 Y_up 4 Y_up 0
1 4 Z_up 4 Z_up 0
2 0 0

1503 12 BulletRayHit 0 140289 12 BulletRayHit 12 BulletRayHit 0 0 0 1 764 765 6 1852 1853 1854 1855 1856 1857 7 757 758 759 760 761 762 763 0 0 0 0 0 10
/**
 *
 */

1504 11 PandaNode * 0 8576 11 PandaNode * 11 PandaNode * 0 0 1505 0 0 0 0 0 0 0 0 0 0

1505 9 PandaNode 0 2048 9 PandaNode 9 PandaNode 0 0 0 0 0 0 0 0 0 0 0 0 175
/**
 * A basic node of the scene graph or data graph.  This is the base class of
 * all specialized nodes, and also serves as a generic node with no special
 * properties.
 */

1506 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 1507 0 0 0 0 0 0 0 0 0 0

1507 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

1508 8 LVector3 0 2105344 8 LVector3 8 LVector3 0 0 1509 0 0 0 0 0 0 0 0 0 0

1509 9 LVector3f 0 2048 9 LVector3f 9 LVector3f 0 0 0 0 0 0 0 0 0 0 0 0 338
/**
 * This is a three-component vector distance (as opposed to a three-component
 * point, which represents a particular point in space).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

1510 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 1511 0 0 0 0 0 0 0 0 0 0

1511 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

1512 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

1513 22 BulletAllHitsRayResult 0 140289 22 BulletAllHitsRayResult 22 BulletAllHitsRayResult 0 0 0 0 775 4 1858 1859 1860 1861 7 768 769 770 771 772 773 774 1 2151 0 1 3 1514 766 767 0 0 10
/**
 *
 */

1514 24 AllHitsRayResultCallback 0 263168 42 btCollisionWorld::AllHitsRayResultCallback 42 btCollisionWorld::AllHitsRayResultCallback 1515 0 0 0 0 0 0 0 0 0 0 0 0

1515 16 btCollisionWorld 0 2048 16 btCollisionWorld 16 btCollisionWorld 0 0 0 0 0 0 0 0 0 0 0 0 0

1516 11 BulletShape 0 75777 11 BulletShape 11 BulletShape 0 0 0 0 0 10 1862 1863 1864 1865 1866 1867 1868 1869 1870 1871 12 776 777 778 779 780 781 782 783 784 785 786 787 0 0 1 0 1517 0 0 0 0 10
/**
 *
 */

1517 27 TypedWritableReferenceCount 0 2048 27 TypedWritableReferenceCount 27 TypedWritableReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 414
/**
 * A base class for things which need to inherit from both TypedWritable and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedWritables and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1518 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

1519 12 char const * 0 8576 12 char const * 12 char const * 0 0 1520 0 0 0 0 0 0 0 0 0 0

1520 10 char const 0 8832 10 char const 10 char const 0 0 1521 0 0 0 0 0 0 0 0 0 0

1521 4 char 0 8194 4 char 4 char 0 5 0 0 0 0 0 0 0 0 0 0 0

1522 14 BoundingSphere 0 2048 14 BoundingSphere 14 BoundingSphere 0 0 0 0 0 0 0 0 0 0 0 0 147
/**
 * This defines a bounding sphere, consisting of a center and a radius.  It is
 * always a sphere, and never an ellipsoid or other quadric.
 */

1523 33 BulletBaseCharacterControllerNode 0 141313 33 BulletBaseCharacterControllerNode 33 BulletBaseCharacterControllerNode 0 0 0 0 789 0 1 788 0 0 1 0 1505 0 0 0 0 10
/**
 *
 */

1524 14 BulletBodyNode 0 75777 14 BulletBodyNode 14 BulletBodyNode 0 0 0 0 0 20 1872 1873 1874 1875 1876 1877 1878 1879 1880 1881 1882 1883 1884 1885 1886 1887 1888 1889 1890 1891 44 790 791 792 793 794 795 796 797 798 799 800 801 802 803 804 805 806 807 808 809 810 811 812 813 814 815 816 817 818 819 820 821 822 823 824 825 826 827 828 829 830 831 832 833 1 2152 0 1 0 1505 0 0 0 0 10
/**
 *
 */

1525 13 BulletShape * 0 8576 13 BulletShape * 13 BulletShape * 0 0 1516 0 0 0 0 0 0 0 0 0 0

1526 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 1527 0 0 0 0 0 0 0 0 0 0

1527 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

1528 32 ConstPointerTo< TransformState > 0 2048 32 ConstPointerTo< TransformState > 32 ConstPointerTo< TransformState > 0 0 0 0 0 0 0 0 0 0 0 0 0

1529 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 1530 0 0 0 0 0 0 0 0 0 0

1530 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1531 14 BulletBoxShape 0 75777 14 BulletBoxShape 14 BulletBoxShape 0 0 0 1 834 0 2 1892 1893 4 835 836 837 838 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1532 18 BulletCapsuleShape 0 75777 18 BulletCapsuleShape 18 BulletCapsuleShape 0 0 0 1 839 0 2 1894 1895 4 840 841 842 844 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1533 29 BulletCharacterControllerNode 0 75777 29 BulletCharacterControllerNode 29 BulletCharacterControllerNode 0 0 0 1 845 789 4 1896 1897 1898 1899 15 846 847 848 849 850 851 852 853 854 855 856 857 858 859 860 0 0 1 0 1523 0 0 0 0 10
/**
 *
 */

1534 25 BulletClosestHitRayResult 0 140289 25 BulletClosestHitRayResult 25 BulletClosestHitRayResult 0 0 0 1 873 874 8 1900 1901 1902 1903 1904 1905 1906 1907 10 863 864 865 866 867 868 869 870 871 872 0 0 1 3 1535 861 862 0 0 10
/**
 *
 */

1535 24 ClosestRayResultCallback 0 263168 42 btCollisionWorld::ClosestRayResultCallback 42 btCollisionWorld::ClosestRayResultCallback 1515 0 0 0 0 0 0 0 0 0 0 0 0

1536 27 BulletClosestHitSweepResult 0 140289 27 BulletClosestHitSweepResult 27 BulletClosestHitSweepResult 0 0 0 1 885 886 6 1908 1909 1910 1911 1912 1913 8 877 878 879 880 881 882 883 884 0 0 1 3 1537 875 876 0 0 10
/**
 *
 */

1537 27 ClosestConvexResultCallback 0 263168 45 btCollisionWorld::ClosestConvexResultCallback 45 btCollisionWorld::ClosestConvexResultCallback 1515 0 0 0 0 0 0 0 0 0 0 0 0

1538 15 BulletConeShape 0 75777 15 BulletConeShape 15 BulletConeShape 0 0 0 1 887 0 2 1914 1915 3 888 889 890 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1539 16 BulletConstraint 0 75777 16 BulletConstraint 16 BulletConstraint 0 0 0 0 0 6 1916 1933 1934 1935 1936 1937 13 891 892 893 894 895 896 897 898 899 900 901 902 940 0 0 1 0 1540 0 0 0 1 1541 10
/**
 *
 */

1540 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

1541 15 ConstraintParam 0 794624 33 BulletConstraint::ConstraintParam 33 BulletConstraint::ConstraintParam 1539 0 0 0 0 0 0 0 0 0 4 6 CP_erp 24 BulletConstraint::CP_erp 0
1 11 CP_stop_erp 29 BulletConstraint::CP_stop_erp 0
2 6 CP_cfm 24 BulletConstraint::CP_cfm 0
3 11 CP_stop_cfm 29 BulletConstraint::CP_stop_cfm 0
4 0 0

1542 21 BulletRigidBodyNode * 0 8576 21 BulletRigidBodyNode * 21 BulletRigidBodyNode * 0 0 1543 0 0 0 0 0 0 0 0 0 0

1543 19 BulletRigidBodyNode 0 75777 19 BulletRigidBodyNode 19 BulletRigidBodyNode 0 0 0 1 903 0 16 1917 1918 1919 1920 1921 1922 1923 1924 1925 1926 1927 1928 1929 1930 1931 1932 36 904 905 906 907 908 909 910 911 912 913 914 915 916 917 918 919 920 921 922 923 924 925 926 927 928 929 930 931 932 933 934 935 936 937 938 939 0 0 1 0 1524 0 0 0 0 10
/**
 *
 */

1544 8 LMatrix3 0 2105344 8 LMatrix3 8 LMatrix3 0 0 1545 0 0 0 0 0 0 0 0 0 0

1545 9 LMatrix3f 0 2048 9 LMatrix3f 9 LMatrix3f 0 0 0 0 0 0 0 0 0 0 0 0 231
/**
 * This is a 3-by-3 transform matrix.  It typically will represent either a
 * rotation-and-scale (no translation) matrix in 3-d, or a full affine matrix
 * (rotation, scale, translation) in 2-d, e.g.  for a texture matrix.
 */

1546 25 BulletConeTwistConstraint 0 75777 25 BulletConeTwistConstraint 25 BulletConeTwistConstraint 0 0 0 1 941 0 3 1938 1939 1940 13 942 943 944 945 946 947 948 949 950 951 952 953 954 0 0 1 0 1539 0 0 0 0 10
/**
 *
 */

1547 19 BulletManifoldPoint 0 26625 19 BulletManifoldPoint 19 BulletManifoldPoint 0 0 0 0 955 23 1941 1942 1943 1944 1945 1946 1947 1948 1949 1950 1951 1952 1953 1954 1955 1956 1957 1958 1959 1960 1961 1962 1963 35 956 957 958 959 960 961 962 963 964 965 966 967 968 969 970 971 972 973 974 975 976 977 978 979 980 981 982 983 984 985 986 987 988 989 990 0 0 0 0 0 10
/**
 *
 */

1548 25 BulletContactCallbackData 0 10241 25 BulletContactCallbackData 25 BulletContactCallbackData 0 0 0 1 991 0 7 1964 1965 1966 1967 1968 1969 1970 8 992 993 994 995 996 997 998 999 0 0 1 0 1549 0 0 0 0 10
/**
 *
 */

1549 12 CallbackData 0 2048 12 CallbackData 12 CallbackData 0 0 0 0 0 0 0 0 0 0 0 0 348
/**
 * This is a generic data block that is passed along to a CallbackObject when
 * a callback is made.  It contains data specific to the particular callback
 * type in question.
 *
 * This is actually an abstract base class and contains no data.
 * Specializations of this class will contain the actual data relevant to each
 * callback type.
 */

1550 13 BulletContact 0 140289 13 BulletContact 13 BulletContact 0 0 0 0 1007 7 1971 1972 1973 1974 1975 1976 1977 7 1000 1001 1002 1003 1004 1005 1006 0 0 0 0 0 10
/**
 *
 */

1551 19 BulletContactResult 0 140289 19 BulletContactResult 19 BulletContactResult 0 0 0 0 1012 1 1978 2 1010 1011 1 2153 0 1 3 1552 1008 1009 0 0 10
/**
 *
 */

1552 21 ContactResultCallback 0 263168 39 btCollisionWorld::ContactResultCallback 39 btCollisionWorld::ContactResultCallback 1515 0 0 0 0 0 0 0 0 0 0 0 0

1553 15 BulletDebugNode 0 75777 15 BulletDebugNode 15 BulletDebugNode 0 0 0 1 1013 0 4 1979 1980 1981 1982 10 1014 1015 1016 1017 1018 1019 1020 1021 1022 1023 0 0 1 0 1505 0 0 0 0 10
/**
 *
 */

1554 15 BulletGhostNode 0 75777 15 BulletGhostNode 15 BulletGhostNode 0 0 0 1 1024 0 1 1983 3 1025 1026 1027 1 2154 0 1 0 1524 0 0 0 0 10
/**
 *
 */

1555 25 BulletSoftBodyNodeElement 0 26625 25 BulletSoftBodyNodeElement 25 BulletSoftBodyNodeElement 0 0 0 1 1036 1028 6 1984 1985 1986 1987 1988 1989 7 1029 1030 1031 1032 1033 1034 1035 0 0 0 0 0 10
/**
 *
 */

1556 18 BulletSoftBodyNode 0 75777 18 BulletSoftBodyNode 18 BulletSoftBodyNode 0 0 0 0 0 7 1990 2016 2022 2023 2024 2025 2029 45 1037 1038 1039 1040 1041 1042 1043 1044 1045 1046 1047 1048 1049 1050 1051 1052 1053 1054 1055 1056 1057 1058 1059 1060 1061 1062 1063 1064 1065 1066 1067 1068 1069 1070 1071 1072 1073 1074 1075 1076 1077 1078 1079 1080 1158 2 2155 2156 0 1 0 1524 0 0 0 0 10
/**
 *
 */

1557 20 BulletSoftBodyConfig 0 26625 20 BulletSoftBodyConfig 20 BulletSoftBodyConfig 0 0 0 1 1135 1081 25 1991 1992 1993 1994 1995 1996 1997 1998 1999 2000 2001 2002 2003 2004 2005 2006 2007 2008 2009 2010 2011 2012 2013 2014 2015 53 1082 1083 1084 1085 1086 1087 1088 1089 1090 1091 1092 1093 1094 1095 1096 1097 1098 1099 1100 1101 1102 1103 1104 1105 1106 1107 1108 1109 1110 1111 1112 1113 1114 1115 1116 1117 1118 1119 1120 1121 1122 1123 1124 1125 1126 1127 1128 1129 1130 1131 1132 1133 1134 0 0 0 0 2 1558 1559 10
/**
 *
 */

1558 13 CollisionFlag 0 794624 35 BulletSoftBodyConfig::CollisionFlag 35 BulletSoftBodyConfig::CollisionFlag 1557 0 0 0 0 0 0 0 0 0 7 21 CF_rigid_vs_soft_mask 43 BulletSoftBodyConfig::CF_rigid_vs_soft_mask 34
// RVSmask: Rigid versus soft mask
15 17 CF_sdf_rigid_soft 39 BulletSoftBodyConfig::CF_sdf_rigid_soft 35
// SDF_RS:  SDF based rigid vs soft
1 21 CF_cluster_rigid_soft 43 BulletSoftBodyConfig::CF_cluster_rigid_soft 43
// CL_RS:   Cluster vs convex rigid vs soft
2 20 CF_soft_vs_soft_mask 42 BulletSoftBodyConfig::CF_soft_vs_soft_mask 33
// SVSmask: Soft versus soft mask
48 24 CF_vertex_face_soft_soft 46 BulletSoftBodyConfig::CF_vertex_face_soft_soft 48
// VF_SS:   Vertex vs face soft vs soft handling
16 20 CF_cluster_soft_soft 42 BulletSoftBodyConfig::CF_cluster_soft_soft 52
// CL_SS:   Cluster vs cluster soft vs soft handling
32 15 CF_cluster_self 37 BulletSoftBodyConfig::CF_cluster_self 44
// CL_SELF: Cluster soft body self collision
64 0 0

1559 9 AeroModel 0 794624 31 BulletSoftBodyConfig::AeroModel 31 BulletSoftBodyConfig::AeroModel 1557 0 0 0 0 0 0 0 0 0 5 15 AM_vertex_point 37 BulletSoftBodyConfig::AM_vertex_point 58
// V_Point:    Vertex normals are oriented toward velocity
0 19 AM_vertex_two_sided 41 BulletSoftBodyConfig::AM_vertex_two_sided 58
// V_TwoSided: Vertex normals are fliped to match velocity
1 19 AM_vertex_one_sided 41 BulletSoftBodyConfig::AM_vertex_one_sided 48
// V_OneSided: Vertex normals are taken as it is
2 17 AM_face_two_sided 39 BulletSoftBodyConfig::AM_face_two_sided 56
// F_TwoSided: Face normals are fliped to match velocity
3 17 AM_face_one_sided 39 BulletSoftBodyConfig::AM_face_one_sided 46
// F_OneSided: Face normals are taken as it is
4 0 0

1560 23 BulletSoftBodyWorldInfo 0 26625 23 BulletSoftBodyWorldInfo 23 BulletSoftBodyWorldInfo 0 0 0 1 1148 1136 5 2017 2018 2019 2020 2021 11 1137 1138 1139 1140 1141 1142 1143 1144 1145 1146 1147 0 0 0 0 0 10
/**
 *
 */

1561 11 BoundingBox 0 2048 11 BoundingBox 11 BoundingBox 0 0 0 0 0 0 0 0 0 0 0 0 201
/**
 * An axis-aligned bounding box; that is, a minimum and maximum coordinate
 * triple.
 *
 * This box is always axis-aligned.  If you need a more general bounding box,
 * try BoundingHexahedron.
 */

1562 22 BulletSoftBodyMaterial 0 26625 22 BulletSoftBodyMaterial 22 BulletSoftBodyMaterial 0 0 0 1 1157 1149 3 2026 2027 2028 7 1150 1151 1152 1153 1154 1155 1156 0 0 0 0 0 10
/**
 *
 */

1563 19 BulletVehicleTuning 0 10241 19 BulletVehicleTuning 19 BulletVehicleTuning 0 0 0 0 0 6 2030 2031 2032 2033 2034 2035 12 1159 1160 1161 1162 1163 1164 1165 1166 1167 1168 1169 1170 0 0 0 0 0 10
/**
 *
 */

1564 13 BulletVehicle 0 75777 13 BulletVehicle 13 BulletVehicle 0 0 0 1 1171 0 5 2036 2037 2038 2039 2073 15 1172 1173 1174 1175 1176 1177 1178 1179 1180 1181 1182 1183 1184 1185 1246 1 2157 0 1 0 1540 0 0 0 0 257
/**
 * Simulates a raycast vehicle which casts a ray per wheel at the ground as a
 * cheap replacement for complex suspension simulation.  The suspension can be
 * tuned in various ways.  It is possible to add a (probably) arbitrary number
 * of wheels.
 */

1565 11 BulletWheel 0 26625 11 BulletWheel 11 BulletWheel 0 0 0 1 1245 1186 25 2040 2049 2050 2051 2052 2053 2054 2055 2056 2057 2058 2059 2060 2061 2062 2063 2064 2065 2066 2067 2068 2069 2070 2071 2072 48 1187 1188 1189 1190 1191 1192 1193 1194 1195 1196 1197 1198 1199 1200 1201 1202 1203 1204 1205 1206 1207 1208 1209 1210 1211 1212 1213 1214 1215 1216 1217 1218 1219 1220 1221 1222 1223 1224 1225 1226 1227 1228 1229 1230 1231 1232 1233 1234 0 0 0 0 0 145
/**
 * One wheel of a BulletVehicle.  Instances should not be created directly but
 * using the factory method BulletVehicle::create_wheel().
 */

1566 22 BulletWheelRaycastInfo 0 26625 22 BulletWheelRaycastInfo 22 BulletWheelRaycastInfo 0 0 0 1 1244 1235 8 2041 2042 2043 2044 2045 2046 2047 2048 8 1236 1237 1238 1239 1240 1241 1242 1243 0 0 0 0 0 10
/**
 *
 */

1567 11 BulletWorld 0 75777 11 BulletWorld 11 BulletWorld 0 0 0 1 1247 0 11 2074 2075 2076 2077 2078 2079 2080 2081 2082 2083 2089 54 1248 1249 1250 1251 1252 1253 1254 1255 1256 1257 1258 1259 1260 1261 1262 1263 1264 1265 1266 1267 1268 1269 1270 1271 1272 1273 1274 1275 1276 1277 1278 1279 1280 1281 1282 1283 1284 1285 1286 1287 1288 1289 1300 1301 1302 1303 1304 1305 1306 1307 1308 1309 1310 1311 7 2158 2159 2160 2161 2162 2163 2164 0 1 0 1540 0 0 0 2 1568 1569 10
/**
 *
 */

1568 19 BroadphaseAlgorithm 0 794624 32 BulletWorld::BroadphaseAlgorithm 32 BulletWorld::BroadphaseAlgorithm 1567 0 0 0 0 0 0 0 0 0 2 18 BA_sweep_and_prune 31 BulletWorld::BA_sweep_and_prune 0
0 20 BA_dynamic_aabb_tree 33 BulletWorld::BA_dynamic_aabb_tree 0
1 0 16
// Configuration

1569 15 FilterAlgorithm 0 794624 28 BulletWorld::FilterAlgorithm 28 BulletWorld::FilterAlgorithm 1567 0 0 0 0 0 0 0 0 0 3 7 FA_mask 20 BulletWorld::FA_mask 0
0 14 FA_groups_mask 27 BulletWorld::FA_groups_mask 0
1 11 FA_callback 24 BulletWorld::FA_callback 0
2 0 0

1570 17 BulletDebugNode * 0 8576 17 BulletDebugNode * 17 BulletDebugNode * 0 0 1553 0 0 0 0 0 0 0 0 0 0

1571 17 BulletGhostNode * 0 8576 17 BulletGhostNode * 17 BulletGhostNode * 0 0 1554 0 0 0 0 0 0 0 0 0 0

1572 20 BulletSoftBodyNode * 0 8576 20 BulletSoftBodyNode * 20 BulletSoftBodyNode * 0 0 1556 0 0 0 0 0 0 0 0 0 0

1573 35 BulletBaseCharacterControllerNode * 0 8576 35 BulletBaseCharacterControllerNode * 35 BulletBaseCharacterControllerNode * 0 0 1523 0 0 0 0 0 0 0 0 0 0

1574 15 BulletVehicle * 0 8576 15 BulletVehicle * 15 BulletVehicle * 0 0 1564 0 0 0 0 0 0 0 0 0 0

1575 18 BulletConstraint * 0 8576 18 BulletConstraint * 18 BulletConstraint * 0 0 1539 0 0 0 0 0 0 0 0 0 0

1576 24 BulletPersistentManifold 0 26625 24 BulletPersistentManifold 24 BulletPersistentManifold 0 0 0 1 1299 1290 5 2084 2085 2086 2087 2088 8 1291 1292 1293 1294 1295 1296 1297 1298 1 2165 0 0 0 0 10
/**
 *
 */

1577 21 BulletConvexHullShape 0 75777 21 BulletConvexHullShape 21 BulletConvexHullShape 0 0 0 1 1312 0 0 4 1313 1314 1315 1316 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1578 27 BulletConvexPointCloudShape 0 75777 27 BulletConvexPointCloudShape 27 BulletConvexPointCloudShape 0 0 0 1 1317 0 1 2090 2 1318 1319 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1579 19 BulletCylinderShape 0 75777 19 BulletCylinderShape 19 BulletCylinderShape 0 0 0 1 1320 0 3 2091 2092 2093 4 1321 1322 1323 1324 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1580 24 BulletFilterCallbackData 0 141313 24 BulletFilterCallbackData 24 BulletFilterCallbackData 0 0 0 1 1325 1331 3 2094 2095 2096 5 1326 1327 1328 1329 1330 0 0 1 0 1549 0 0 0 0 10
/**
 *
 */

1581 26 BulletRotationalLimitMotor 0 26625 26 BulletRotationalLimitMotor 26 BulletRotationalLimitMotor 0 0 0 1 1332 1333 6 2097 2098 2099 2100 2101 2102 18 1334 1335 1336 1337 1338 1339 1340 1341 1342 1343 1344 1345 1346 1347 1348 1349 1350 1351 0 0 0 0 0 55
/**
 * Rotation Limit structure for generic joints.
 */

1582 29 BulletTranslationalLimitMotor 0 26625 29 BulletTranslationalLimitMotor 29 BulletTranslationalLimitMotor 0 0 0 1 1352 1353 3 2103 2104 2105 17 1354 1355 1356 1357 1358 1359 1360 1361 1362 1363 1364 1365 1366 1367 1368 1369 1370 0 0 0 0 0 55
/**
 * Rotation Limit structure for generic joints.
 */

1583 23 BulletGenericConstraint 0 75777 23 BulletGenericConstraint 23 BulletGenericConstraint 0 0 0 1 1371 0 3 2106 2107 2108 11 1372 1373 1374 1375 1376 1377 1378 1379 1380 1381 1382 0 0 1 0 1539 0 0 0 0 10
/**
 *
 */

1584 22 BulletHeightfieldShape 0 75777 22 BulletHeightfieldShape 22 BulletHeightfieldShape 0 0 0 1 1383 0 0 2 1384 1385 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1585 12 BulletHelper 0 141313 12 BulletHelper 12 BulletHelper 0 0 0 1 1394 1395 2 2109 2110 8 1386 1387 1388 1389 1390 1391 1392 1393 0 0 0 0 0 10
/**
 *
 */

1586 25 PointerTo< InternalName > 0 2048 25 PointerTo< InternalName > 25 PointerTo< InternalName > 0 0 0 0 0 0 0 0 0 0 0 0 0

1587 21 BulletHingeConstraint 0 75777 21 BulletHingeConstraint 21 BulletHingeConstraint 0 0 0 1 1396 0 6 2111 2112 2113 2114 2115 2116 15 1397 1398 1399 1400 1401 1402 1403 1404 1405 1406 1407 1408 1409 1410 1411 0 0 1 0 1539 0 0 0 0 158
/**
 * The hinge constraint lets two bodies rotate around a given axis while
 * adhering to specified limits.  It's motor can apply angular force to them.
 */

1588 23 BulletMinkowskiSumShape 0 75777 23 BulletMinkowskiSumShape 23 BulletMinkowskiSumShape 0 0 0 1 1412 0 4 2117 2118 2119 2120 7 1413 1414 1415 1416 1417 1418 1419 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1589 19 BulletShape const * 0 8576 19 BulletShape const * 19 BulletShape const * 0 0 1590 0 0 0 0 0 0 0 0 0 0

1590 17 BulletShape const 0 8832 17 BulletShape const 17 BulletShape const 0 0 1516 0 0 0 0 0 0 0 0 0 0

1591 22 BulletMultiSphereShape 0 75777 22 BulletMultiSphereShape 22 BulletMultiSphereShape 0 0 0 1 1420 0 3 2121 2122 2123 5 1421 1422 1423 1424 1425 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1592 16 BulletPlaneShape 0 75777 16 BulletPlaneShape 16 BulletPlaneShape 0 0 0 1 1426 0 3 2124 2125 2126 5 1427 1428 1429 1430 1431 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1593 6 LPlane 0 2105344 6 LPlane 6 LPlane 0 0 1594 0 0 0 0 0 0 0 0 0 0

1594 7 LPlanef 0 2048 7 LPlanef 7 LPlanef 0 0 0 0 0 0 0 0 0 0 0 0 120
/**
 * An abstract mathematical description of a plane.  A plane is defined by the
 * equation Ax + By + Cz + D = 0.
 */

1595 22 BulletSliderConstraint 0 75777 22 BulletSliderConstraint 22 BulletSliderConstraint 0 0 0 1 1432 0 14 2127 2128 2129 2130 2131 2132 2133 2134 2135 2136 2137 2138 2139 2140 26 1433 1434 1435 1436 1437 1438 1439 1440 1441 1442 1443 1444 1445 1446 1447 1448 1449 1450 1451 1452 1453 1454 1455 1456 1457 1458 0 0 1 0 1539 0 0 0 0 10
/**
 *
 */

1596 21 BulletSoftBodyControl 0 26625 21 BulletSoftBodyControl 21 BulletSoftBodyControl 0 0 0 1 1461 1462 0 4 1463 1464 1465 1466 0 0 1 3 1597 1459 1460 0 0 10
/**
 *
 */

1597 8 IControl 0 263168 28 btSoftBody::AJoint::IControl 28 btSoftBody::AJoint::IControl 1598 0 0 0 0 0 0 0 0 0 0 0 0

1598 6 AJoint 0 263168 18 btSoftBody::AJoint 18 btSoftBody::AJoint 1599 0 0 0 0 0 0 0 0 0 0 0 0

1599 10 btSoftBody 0 2048 10 btSoftBody 10 btSoftBody 0 0 0 0 0 0 0 0 0 0 0 0 0

1600 19 BulletSoftBodyShape 0 75777 19 BulletSoftBodyShape 19 BulletSoftBodyShape 0 0 0 0 0 1 2141 2 1467 1468 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1601 17 BulletSphereShape 0 75777 17 BulletSphereShape 17 BulletSphereShape 0 0 0 1 1469 0 1 2142 3 1470 1471 1472 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1602 25 BulletSphericalConstraint 0 75777 25 BulletSphericalConstraint 25 BulletSphericalConstraint 0 0 0 1 1473 0 2 2143 2144 5 1474 1475 1476 1477 1478 0 0 1 0 1539 0 0 0 0 328
/**
 * A constraint between two rigid bodies, each with a pivot point.  The pivot
 * points are described in the body's local space.  The constraint limits
 * movement of the two rigid bodies in such a way that the pivot points match
 * in global space.  The spherical constraint can be seen as a "ball and
 * socket" joint.
 */

1603 22 BulletTickCallbackData 0 141313 22 BulletTickCallbackData 22 BulletTickCallbackData 0 0 0 1 1479 1482 1 2145 2 1480 1481 0 0 1 0 1549 0 0 0 0 10
/**
 *
 */

1604 18 BulletTriangleMesh 0 75777 18 BulletTriangleMesh 18 BulletTriangleMesh 0 0 0 1 1483 0 3 2146 2147 2148 10 1484 1485 1486 1487 1488 1489 1490 1491 1492 1496 0 0 1 0 1517 0 0 0 0 10
/**
 *
 */

1605 10 LVecBase3i 0 2048 10 LVecBase3i 10 LVecBase3i 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

1606 23 BulletTriangleMeshShape 0 75777 23 BulletTriangleMeshShape 23 BulletTriangleMeshShape 0 0 0 1 1497 0 2 2149 2150 4 1498 1499 1500 1501 0 0 1 0 1516 0 0 0 0 10
/**
 *
 */

1607 14 BulletRayHit * 0 8576 14 BulletRayHit * 14 BulletRayHit * 0 0 1503 0 0 0 0 0 0 0 0 0 0

1608 20 BulletRayHit const * 0 8576 20 BulletRayHit const * 20 BulletRayHit const * 0 0 1609 0 0 0 0 0 0 0 0 0 0

1609 18 BulletRayHit const 0 8832 18 BulletRayHit const 18 BulletRayHit const 0 0 1503 0 0 0 0 0 0 0 0 0 0

1610 9 LPoint3 * 0 8576 9 LPoint3 * 9 LPoint3 * 0 0 1506 0 0 0 0 0 0 0 0 0 0

1611 10 LVector3 * 0 8576 10 LVector3 * 10 LVector3 * 0 0 1508 0 0 0 0 0 0 0 0 0 0

1612 24 BulletAllHitsRayResult * 0 8576 24 BulletAllHitsRayResult * 24 BulletAllHitsRayResult * 0 0 1513 0 0 0 0 0 0 0 0 0 0

1613 30 BulletAllHitsRayResult const * 0 8576 30 BulletAllHitsRayResult const * 30 BulletAllHitsRayResult const * 0 0 1614 0 0 0 0 0 0 0 0 0 0

1614 28 BulletAllHitsRayResult const 0 8832 28 BulletAllHitsRayResult const 28 BulletAllHitsRayResult const 0 0 1513 0 0 0 0 0 0 0 0 0 0

1615 26 AllHitsRayResultCallback * 0 8576 44 btCollisionWorld::AllHitsRayResultCallback * 44 btCollisionWorld::AllHitsRayResultCallback * 0 0 1514 0 0 0 0 0 0 0 0 0 0

1616 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

1617 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

1618 16 BoundingSphere * 0 8576 16 BoundingSphere * 16 BoundingSphere * 0 0 1522 0 0 0 0 0 0 0 0 0 0

1619 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

1620 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 1619 0 0 0 0 0 0 0 0 0 0

1621 16 BulletBodyNode * 0 8576 16 BulletBodyNode * 16 BulletBodyNode * 0 0 1524 0 0 0 0 0 0 0 0 0 0

1622 22 TransformState const * 0 8576 22 TransformState const * 22 TransformState const * 0 0 1623 0 0 0 0 0 0 0 0 0 0

1623 20 TransformState const 0 8832 20 TransformState const 20 TransformState const 0 0 1624 0 0 0 0 0 0 0 0 0 0

1624 14 TransformState 0 16779264 14 TransformState 14 TransformState 0 0 0 0 0 0 0 0 0 0 0 0 734
/**
 * Indicates a coordinate-system transform on vertices.  TransformStates are
 * the primary means for storing transformations on the scene graph.
 *
 * Transforms may be specified in one of two ways: componentwise, with a pos-
 * hpr-scale, or with an arbitrary transform matrix.  If you specify a
 * transform componentwise, it will remember its original components.
 *
 * TransformState objects are managed very much like RenderState objects.
 * They are immutable and reference-counted automatically.
 *
 * You should not attempt to create or modify a TransformState object
 * directly.  Instead, call one of the make() functions to create one for you.
 * And instead of modifying a TransformState object, create a new one.
 */

1625 22 BulletBodyNode const * 0 8576 22 BulletBodyNode const * 22 BulletBodyNode const * 0 0 1626 0 0 0 0 0 0 0 0 0 0

1626 20 BulletBodyNode const 0 8832 20 BulletBodyNode const 20 BulletBodyNode const 0 0 1524 0 0 0 0 0 0 0 0 0 0

1627 10 LMatrix4 * 0 8576 10 LMatrix4 * 10 LMatrix4 * 0 0 1526 0 0 0 0 0 0 0 0 0 0

1628 15 CollisionNode * 0 8576 15 CollisionNode * 15 CollisionNode * 0 0 1629 0 0 0 0 0 0 0 0 0 0

1629 13 CollisionNode 0 2048 13 CollisionNode 13 CollisionNode 0 0 0 0 0 0 0 0 0 0 0 0 261
/**
 * A node in the scene graph that can hold any number of CollisionSolids.
 * This may either represent a bit of static geometry in the scene that things
 * will collide with, or an animated object twirling around in the world and
 * running into things.
 */

1630 13 CollideMask * 0 8576 13 CollideMask * 13 CollideMask * 0 0 1631 0 0 0 0 0 0 0 0 0 0

1631 11 CollideMask 0 2105344 11 CollideMask 11 CollideMask 0 0 1632 0 0 0 0 0 0 0 0 0 0

1632 9 BitMask32 0 2105344 9 BitMask32 9 BitMask32 0 0 1633 0 0 0 0 0 0 0 0 0 0

1633 23 BitMask< uint32_t, 32 > 0 2048 23 BitMask< uint32_t, 32 > 23 BitMask< uint32_t, 32 > 0 0 0 0 0 0 0 0 0 0 0 0 0

1634 10 bool const 0 8832 10 bool const 10 bool const 0 0 1518 0 0 0 0 0 0 0 0 0 0

1635 17 LVecBase3 const * 0 8576 17 LVecBase3 const * 17 LVecBase3 const * 0 0 1636 0 0 0 0 0 0 0 0 0 0

1636 15 LVecBase3 const 0 8832 15 LVecBase3 const 15 LVecBase3 const 0 0 1529 0 0 0 0 0 0 0 0 0 0

1637 11 LVecBase3 * 0 8576 11 LVecBase3 * 11 LVecBase3 * 0 0 1529 0 0 0 0 0 0 0 0 0 0

1638 22 BulletBoxShape const * 0 8576 22 BulletBoxShape const * 22 BulletBoxShape const * 0 0 1639 0 0 0 0 0 0 0 0 0 0

1639 20 BulletBoxShape const 0 8832 20 BulletBoxShape const 20 BulletBoxShape const 0 0 1531 0 0 0 0 0 0 0 0 0 0

1640 16 BulletBoxShape * 0 8576 16 BulletBoxShape * 16 BulletBoxShape * 0 0 1531 0 0 0 0 0 0 0 0 0 0

1641 20 CollisionBox const * 0 8576 20 CollisionBox const * 20 CollisionBox const * 0 0 1642 0 0 0 0 0 0 0 0 0 0

1642 18 CollisionBox const 0 8832 18 CollisionBox const 18 CollisionBox const 0 0 1643 0 0 0 0 0 0 0 0 0 0

1643 12 CollisionBox 0 2048 12 CollisionBox 12 CollisionBox 0 0 0 0 0 0 0 0 0 0 0 0 47
/**
 * A cuboid collision volume or object.
 */

1644 26 BulletCapsuleShape const * 0 8576 26 BulletCapsuleShape const * 26 BulletCapsuleShape const * 0 0 1645 0 0 0 0 0 0 0 0 0 0

1645 24 BulletCapsuleShape const 0 8832 24 BulletCapsuleShape const 24 BulletCapsuleShape const 0 0 1532 0 0 0 0 0 0 0 0 0 0

1646 20 BulletCapsuleShape * 0 8576 20 BulletCapsuleShape * 20 BulletCapsuleShape * 0 0 1532 0 0 0 0 0 0 0 0 0 0

1647 24 CollisionCapsule const * 0 8576 24 CollisionCapsule const * 24 CollisionCapsule const * 0 0 1648 0 0 0 0 0 0 0 0 0 0

1648 22 CollisionCapsule const 0 8832 22 CollisionCapsule const 22 CollisionCapsule const 0 0 1649 0 0 0 0 0 0 0 0 0 0

1649 16 CollisionCapsule 0 2048 16 CollisionCapsule 16 CollisionCapsule 0 0 0 0 0 0 0 0 0 0 0 0 201
/**
 * This implements a solid consisting of a cylinder with hemispherical endcaps,
 * also known as a capsule or a spherocylinder.
 *
 * This shape was previously erroneously called CollisionTube.
 */

1650 31 BulletCharacterControllerNode * 0 8576 31 BulletCharacterControllerNode * 31 BulletCharacterControllerNode * 0 0 1533 0 0 0 0 0 0 0 0 0 0

1651 16 LVector3 const * 0 8576 16 LVector3 const * 16 LVector3 const * 0 0 1652 0 0 0 0 0 0 0 0 0 0

1652 14 LVector3 const 0 8832 14 LVector3 const 14 LVector3 const 0 0 1508 0 0 0 0 0 0 0 0 0 0

1653 37 BulletCharacterControllerNode const * 0 8576 37 BulletCharacterControllerNode const * 37 BulletCharacterControllerNode const * 0 0 1654 0 0 0 0 0 0 0 0 0 0

1654 35 BulletCharacterControllerNode const 0 8832 35 BulletCharacterControllerNode const 35 BulletCharacterControllerNode const 0 0 1533 0 0 0 0 0 0 0 0 0 0

1655 33 BulletClosestHitRayResult const * 0 8576 33 BulletClosestHitRayResult const * 33 BulletClosestHitRayResult const * 0 0 1656 0 0 0 0 0 0 0 0 0 0

1656 31 BulletClosestHitRayResult const 0 8832 31 BulletClosestHitRayResult const 31 BulletClosestHitRayResult const 0 0 1534 0 0 0 0 0 0 0 0 0 0

1657 27 BulletClosestHitRayResult * 0 8576 27 BulletClosestHitRayResult * 27 BulletClosestHitRayResult * 0 0 1534 0 0 0 0 0 0 0 0 0 0

1658 26 ClosestRayResultCallback * 0 8576 44 btCollisionWorld::ClosestRayResultCallback * 44 btCollisionWorld::ClosestRayResultCallback * 0 0 1535 0 0 0 0 0 0 0 0 0 0

1659 35 BulletClosestHitSweepResult const * 0 8576 35 BulletClosestHitSweepResult const * 35 BulletClosestHitSweepResult const * 0 0 1660 0 0 0 0 0 0 0 0 0 0

1660 33 BulletClosestHitSweepResult const 0 8832 33 BulletClosestHitSweepResult const 33 BulletClosestHitSweepResult const 0 0 1536 0 0 0 0 0 0 0 0 0 0

1661 29 BulletClosestHitSweepResult * 0 8576 29 BulletClosestHitSweepResult * 29 BulletClosestHitSweepResult * 0 0 1536 0 0 0 0 0 0 0 0 0 0

1662 29 ClosestConvexResultCallback * 0 8576 47 btCollisionWorld::ClosestConvexResultCallback * 47 btCollisionWorld::ClosestConvexResultCallback * 0 0 1537 0 0 0 0 0 0 0 0 0 0

1663 23 BulletConeShape const * 0 8576 23 BulletConeShape const * 23 BulletConeShape const * 0 0 1664 0 0 0 0 0 0 0 0 0 0

1664 21 BulletConeShape const 0 8832 21 BulletConeShape const 21 BulletConeShape const 0 0 1538 0 0 0 0 0 0 0 0 0 0

1665 17 BulletConeShape * 0 8576 17 BulletConeShape * 17 BulletConeShape * 0 0 1538 0 0 0 0 0 0 0 0 0 0

1666 24 BulletConstraint const * 0 8576 24 BulletConstraint const * 24 BulletConstraint const * 0 0 1667 0 0 0 0 0 0 0 0 0 0

1667 22 BulletConstraint const 0 8832 22 BulletConstraint const 22 BulletConstraint const 0 0 1539 0 0 0 0 0 0 0 0 0 0

1668 27 BulletRigidBodyNode const * 0 8576 27 BulletRigidBodyNode const * 27 BulletRigidBodyNode const * 0 0 1669 0 0 0 0 0 0 0 0 0 0

1669 25 BulletRigidBodyNode const 0 8832 25 BulletRigidBodyNode const 25 BulletRigidBodyNode const 0 0 1543 0 0 0 0 0 0 0 0 0 0

1670 10 LMatrix3 * 0 8576 10 LMatrix3 * 10 LMatrix3 * 0 0 1544 0 0 0 0 0 0 0 0 0 0

1671 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 1672 0 0 0 0 0 0 0 0 0 0

1672 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 1506 0 0 0 0 0 0 0 0 0 0

1673 27 BulletConeTwistConstraint * 0 8576 27 BulletConeTwistConstraint * 27 BulletConeTwistConstraint * 0 0 1546 0 0 0 0 0 0 0 0 0 0

1674 33 BulletConeTwistConstraint const * 0 8576 33 BulletConeTwistConstraint const * 33 BulletConeTwistConstraint const * 0 0 1675 0 0 0 0 0 0 0 0 0 0

1675 31 BulletConeTwistConstraint const 0 8832 31 BulletConeTwistConstraint const 31 BulletConeTwistConstraint const 0 0 1546 0 0 0 0 0 0 0 0 0 0

1676 19 LQuaternion const * 0 8576 19 LQuaternion const * 19 LQuaternion const * 0 0 1677 0 0 0 0 0 0 0 0 0 0

1677 17 LQuaternion const 0 8832 17 LQuaternion const 17 LQuaternion const 0 0 1678 0 0 0 0 0 0 0 0 0 0

1678 11 LQuaternion 0 2105344 11 LQuaternion 11 LQuaternion 0 0 1679 0 0 0 0 0 0 0 0 0 0

1679 12 LQuaternionf 0 2048 12 LQuaternionf 12 LQuaternionf 0 0 0 0 0 0 0 0 0 0 0 0 44
/**
 * This is the base quaternion class
 */

1680 27 BulletManifoldPoint const * 0 8576 27 BulletManifoldPoint const * 27 BulletManifoldPoint const * 0 0 1681 0 0 0 0 0 0 0 0 0 0

1681 25 BulletManifoldPoint const 0 8832 25 BulletManifoldPoint const 25 BulletManifoldPoint const 0 0 1547 0 0 0 0 0 0 0 0 0 0

1682 21 BulletManifoldPoint * 0 8576 21 BulletManifoldPoint * 21 BulletManifoldPoint * 0 0 1547 0 0 0 0 0 0 0 0 0 0

1683 27 BulletContactCallbackData * 0 8576 27 BulletContactCallbackData * 27 BulletContactCallbackData * 0 0 1548 0 0 0 0 0 0 0 0 0 0

1684 33 BulletContactCallbackData const * 0 8576 33 BulletContactCallbackData const * 33 BulletContactCallbackData const * 0 0 1685 0 0 0 0 0 0 0 0 0 0

1685 31 BulletContactCallbackData const 0 8832 31 BulletContactCallbackData const 31 BulletContactCallbackData const 0 0 1548 0 0 0 0 0 0 0 0 0 0

1686 15 BulletContact * 0 8576 15 BulletContact * 15 BulletContact * 0 0 1550 0 0 0 0 0 0 0 0 0 0

1687 21 BulletContact const * 0 8576 21 BulletContact const * 21 BulletContact const * 0 0 1688 0 0 0 0 0 0 0 0 0 0

1688 19 BulletContact const 0 8832 19 BulletContact const 19 BulletContact const 0 0 1550 0 0 0 0 0 0 0 0 0 0

1689 27 BulletContactResult const * 0 8576 27 BulletContactResult const * 27 BulletContactResult const * 0 0 1690 0 0 0 0 0 0 0 0 0 0

1690 25 BulletContactResult const 0 8832 25 BulletContactResult const 25 BulletContactResult const 0 0 1551 0 0 0 0 0 0 0 0 0 0

1691 21 BulletContactResult * 0 8576 21 BulletContactResult * 21 BulletContactResult * 0 0 1551 0 0 0 0 0 0 0 0 0 0

1692 23 ContactResultCallback * 0 8576 41 btCollisionWorld::ContactResultCallback * 41 btCollisionWorld::ContactResultCallback * 0 0 1552 0 0 0 0 0 0 0 0 0 0

1693 23 BulletDebugNode const * 0 8576 23 BulletDebugNode const * 23 BulletDebugNode const * 0 0 1694 0 0 0 0 0 0 0 0 0 0

1694 21 BulletDebugNode const 0 8832 21 BulletDebugNode const 21 BulletDebugNode const 0 0 1553 0 0 0 0 0 0 0 0 0 0

1695 23 BulletGhostNode const * 0 8576 23 BulletGhostNode const * 23 BulletGhostNode const * 0 0 1696 0 0 0 0 0 0 0 0 0 0

1696 21 BulletGhostNode const 0 8832 21 BulletGhostNode const 21 BulletGhostNode const 0 0 1554 0 0 0 0 0 0 0 0 0 0

1697 33 BulletSoftBodyNodeElement const * 0 8576 33 BulletSoftBodyNodeElement const * 33 BulletSoftBodyNodeElement const * 0 0 1698 0 0 0 0 0 0 0 0 0 0

1698 31 BulletSoftBodyNodeElement const 0 8832 31 BulletSoftBodyNodeElement const 31 BulletSoftBodyNodeElement const 0 0 1555 0 0 0 0 0 0 0 0 0 0

1699 27 BulletSoftBodyNodeElement * 0 8576 27 BulletSoftBodyNodeElement * 27 BulletSoftBodyNodeElement * 0 0 1555 0 0 0 0 0 0 0 0 0 0

1700 22 BulletSoftBodyConfig * 0 8576 22 BulletSoftBodyConfig * 22 BulletSoftBodyConfig * 0 0 1557 0 0 0 0 0 0 0 0 0 0

1701 25 BulletSoftBodyWorldInfo * 0 8576 25 BulletSoftBodyWorldInfo * 25 BulletSoftBodyWorldInfo * 0 0 1560 0 0 0 0 0 0 0 0 0 0

1702 24 BulletSoftBodyMaterial * 0 8576 24 BulletSoftBodyMaterial * 24 BulletSoftBodyMaterial * 0 0 1562 0 0 0 0 0 0 0 0 0 0

1703 26 BulletSoftBodyNode const * 0 8576 26 BulletSoftBodyNode const * 26 BulletSoftBodyNode const * 0 0 1704 0 0 0 0 0 0 0 0 0 0

1704 24 BulletSoftBodyNode const 0 8832 24 BulletSoftBodyNode const 24 BulletSoftBodyNode const 0 0 1556 0 0 0 0 0 0 0 0 0 0

1705 13 BoundingBox * 0 8576 13 BoundingBox * 13 BoundingBox * 0 0 1561 0 0 0 0 0 0 0 0 0 0

1706 6 Geom * 0 8576 6 Geom * 6 Geom * 0 0 1707 0 0 0 0 0 0 0 0 0 0

1707 4 Geom 0 2048 4 Geom 4 Geom 0 0 0 0 0 0 0 0 0 0 0 0 418
/**
 * A container for geometry primitives.  This class associates one or more
 * GeomPrimitive objects with a table of vertices defined by a GeomVertexData
 * object.  All of the primitives stored in a particular Geom are drawn from
 * the same set of vertices (each primitive uses a subset of all of the
 * vertices in the table), and all of them must be rendered at the same time,
 * in the same graphics state.
 */

1708 21 NurbsCurveEvaluator * 0 8576 21 NurbsCurveEvaluator * 21 NurbsCurveEvaluator * 0 0 1709 0 0 0 0 0 0 0 0 0 0

1709 19 NurbsCurveEvaluator 0 2048 19 NurbsCurveEvaluator 19 NurbsCurveEvaluator 0 0 0 0 0 0 0 0 0 0 0 0 492
/**
 * This class is an abstraction for evaluating NURBS curves.  It accepts an
 * array of vertices, each of which may be in a different coordinate space (as
 * defined by a NodePath), as well as an optional knot vector.
 *
 * This is not related to NurbsCurve, CubicCurveseg or any of the
 * ParametricCurve-derived objects in this module.  It is a completely
 * parallel implementation of NURBS curves, and will probably eventually
 * replace the whole ParametricCurve class hierarchy.
 */

1710 23 NurbsSurfaceEvaluator * 0 8576 23 NurbsSurfaceEvaluator * 23 NurbsSurfaceEvaluator * 0 0 1711 0 0 0 0 0 0 0 0 0 0

1711 21 NurbsSurfaceEvaluator 0 2048 21 NurbsSurfaceEvaluator 21 NurbsSurfaceEvaluator 0 0 0 0 0 0 0 0 0 0 0 0 227
/**
 * This class is an abstraction for evaluating NURBS surfaces.  It accepts an
 * array of vertices, each of which may be in a different coordinate space (as
 * defined by a NodePath), as well as an optional knot vector.
 */

1712 23 BulletSoftBodyControl * 0 8576 23 BulletSoftBodyControl * 23 BulletSoftBodyControl * 0 0 1596 0 0 0 0 0 0 0 0 0 0

1713 12 Geom const * 0 8576 12 Geom const * 12 Geom const * 0 0 1714 0 0 0 0 0 0 0 0 0 0

1714 10 Geom const 0 8832 10 Geom const 10 Geom const 0 0 1707 0 0 0 0 0 0 0 0 0 0

1715 15 PTA_LVecBase3 * 0 8576 15 PTA_LVecBase3 * 15 PTA_LVecBase3 * 0 0 1716 0 0 0 0 0 0 0 0 0 0

1716 13 PTA_LVecBase3 0 2105344 13 PTA_LVecBase3 13 PTA_LVecBase3 0 0 1717 0 0 0 0 0 0 0 0 0 0

1717 14 PTA_LVecBase3f 0 2105344 14 PTA_LVecBase3f 14 PTA_LVecBase3f 0 0 1718 0 0 0 0 0 0 0 0 0 0

1718 28 PointerToArray< LVecBase3f > 0 2048 28 PointerToArray< LVecBase3f > 28 PointerToArray< LVecBase3f > 0 0 0 0 0 0 0 0 0 0 0 0 0

1719 9 PTA_int * 0 8576 9 PTA_int * 9 PTA_int * 0 0 1720 0 0 0 0 0 0 0 0 0 0

1720 7 PTA_int 0 2105344 7 PTA_int 7 PTA_int 0 0 1721 0 0 0 0 0 0 0 0 0 0

1721 21 PointerToArray< int > 0 2048 21 PointerToArray< int > 21 PointerToArray< int > 0 0 0 0 0 0 0 0 0 0 0 0 0

1722 28 BulletSoftBodyConfig const * 0 8576 28 BulletSoftBodyConfig const * 28 BulletSoftBodyConfig const * 0 0 1723 0 0 0 0 0 0 0 0 0 0

1723 26 BulletSoftBodyConfig const 0 8832 26 BulletSoftBodyConfig const 26 BulletSoftBodyConfig const 0 0 1557 0 0 0 0 0 0 0 0 0 0

1724 31 BulletSoftBodyWorldInfo const * 0 8576 31 BulletSoftBodyWorldInfo const * 31 BulletSoftBodyWorldInfo const * 0 0 1725 0 0 0 0 0 0 0 0 0 0

1725 29 BulletSoftBodyWorldInfo const 0 8832 29 BulletSoftBodyWorldInfo const 29 BulletSoftBodyWorldInfo const 0 0 1560 0 0 0 0 0 0 0 0 0 0

1726 30 BulletSoftBodyMaterial const * 0 8576 30 BulletSoftBodyMaterial const * 30 BulletSoftBodyMaterial const * 0 0 1727 0 0 0 0 0 0 0 0 0 0

1727 28 BulletSoftBodyMaterial const 0 8832 28 BulletSoftBodyMaterial const 28 BulletSoftBodyMaterial const 0 0 1562 0 0 0 0 0 0 0 0 0 0

1728 21 BulletVehicleTuning * 0 8576 21 BulletVehicleTuning * 21 BulletVehicleTuning * 0 0 1563 0 0 0 0 0 0 0 0 0 0

1729 27 BulletVehicleTuning const * 0 8576 27 BulletVehicleTuning const * 27 BulletVehicleTuning const * 0 0 1730 0 0 0 0 0 0 0 0 0 0

1730 25 BulletVehicleTuning const 0 8832 25 BulletVehicleTuning const 25 BulletVehicleTuning const 0 0 1563 0 0 0 0 0 0 0 0 0 0

1731 13 BulletWorld * 0 8576 13 BulletWorld * 13 BulletWorld * 0 0 1567 0 0 0 0 0 0 0 0 0 0

1732 21 BulletVehicle const * 0 8576 21 BulletVehicle const * 21 BulletVehicle const * 0 0 1733 0 0 0 0 0 0 0 0 0 0

1733 19 BulletVehicle const 0 8832 19 BulletVehicle const 19 BulletVehicle const 0 0 1564 0 0 0 0 0 0 0 0 0 0

1734 13 BulletWheel * 0 8576 13 BulletWheel * 13 BulletWheel * 0 0 1565 0 0 0 0 0 0 0 0 0 0

1735 19 BulletWheel const * 0 8576 19 BulletWheel const * 19 BulletWheel const * 0 0 1736 0 0 0 0 0 0 0 0 0 0

1736 17 BulletWheel const 0 8832 17 BulletWheel const 17 BulletWheel const 0 0 1565 0 0 0 0 0 0 0 0 0 0

1737 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 1738 0 0 0 0 0 0 0 0 0 0

1738 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 1526 0 0 0 0 0 0 0 0 0 0

1739 24 BulletWheelRaycastInfo * 0 8576 24 BulletWheelRaycastInfo * 24 BulletWheelRaycastInfo * 0 0 1566 0 0 0 0 0 0 0 0 0 0

1740 30 BulletWheelRaycastInfo const * 0 8576 30 BulletWheelRaycastInfo const * 30 BulletWheelRaycastInfo const * 0 0 1741 0 0 0 0 0 0 0 0 0 0

1741 28 BulletWheelRaycastInfo const 0 8832 28 BulletWheelRaycastInfo const 28 BulletWheelRaycastInfo const 0 0 1566 0 0 0 0 0 0 0 0 0 0

1742 19 BulletWorld const * 0 8576 19 BulletWorld const * 19 BulletWorld const * 0 0 1743 0 0 0 0 0 0 0 0 0 0

1743 17 BulletWorld const 0 8832 17 BulletWorld const 17 BulletWorld const 0 0 1567 0 0 0 0 0 0 0 0 0 0

1744 13 TypedObject * 0 8576 13 TypedObject * 13 TypedObject * 0 0 1745 0 0 0 0 0 0 0 0 0 0

1745 11 TypedObject 0 2048 11 TypedObject 11 TypedObject 0 0 0 0 0 0 0 0 0 0 0 0 2508
/**
 * This is an abstract class that all classes which use TypeHandle, and also
 * provide virtual functions to support polymorphism, should inherit from.
 * Each derived class should define get_type(), which should return the
 * specific type of the derived class.  Inheriting from this automatically
 * provides support for is_of_type() and is_exact_type().
 *
 * All classes that inherit directly or indirectly from TypedObject should
 * redefine get_type() and force_init_type(), as shown below.  Some classes
 * that do not inherit from TypedObject may still declare TypeHandles for
 * themselves by defining methods called get_class_type() and init_type().
 * Classes such as these may serve as base classes, but the dynamic type
 * identification system will be limited.  Classes that do not inherit from
 * TypedObject need not define the virtual functions get_type() and
 * force_init_type() (or any other virtual functions).
 *
 * There is a specific layout for defining the overrides from this class.
 * Keeping the definitions formatted just like these examples will allow
 * someone in the future to use a sed (or similar) script to make global
 * changes, if necessary.  Avoid rearranging the braces or the order of the
 * functions unless you're ready to change them in every file all at once.
 *
 * What follows are some examples that can be used in new classes that you
 * create.
 *
 * @par In the class definition (.h file):
 * @code
 * public:
 *   static TypeHandle get_class_type() {
 *     return _type_handle;
 *   }
 *   static void init_type() {
 *     <<<BaseClassOne>>>::init_type();
 *     <<<BaseClassTwo>>>::init_type();
 *     <<<BaseClassN>>>::init_type();
 *     register_type(_type_handle, "<<<ThisClassStringName>>>",
 *                   <<<BaseClassOne>>>::get_class_type(),
 *                   <<<BaseClassTwo>>>::get_class_type(),
 *                   <<<BaseClassN>>>::get_class_type());
 *   }
 *   virtual TypeHandle get_type() const {
 *     return get_class_type();
 *   }
 *   virtual TypeHandle force_init_type() {init_type(); return get_class_type();}
 *
 * private:
 *   static TypeHandle _type_handle;
 * @endcode
 *
 * @par In the class .cxx file:
 * @code
 * TypeHandle <<<ThisClassStringName>>>::_type_handle;
 * @endcode
 *
 * @par In the class config_<<<PackageName>>>.cxx file:
 * @code
 * ConfigureFn(config_<<<PackageName>>>) {
 *   <<<ClassOne>>>::init_type();
 *   <<<ClassTwo>>>::init_type();
 *   <<<ClassN>>>::init_type();
 * }
 * @endcode
 */

1746 19 CollideMask const * 0 8576 19 CollideMask const * 19 CollideMask const * 0 0 1747 0 0 0 0 0 0 0 0 0 0

1747 17 CollideMask const 0 8832 17 CollideMask const 17 CollideMask const 0 0 1631 0 0 0 0 0 0 0 0 0 0

1748 26 BulletPersistentManifold * 0 8576 26 BulletPersistentManifold * 26 BulletPersistentManifold * 0 0 1576 0 0 0 0 0 0 0 0 0 0

1749 12 unsigned int 0 8198 12 unsigned int 12 unsigned int 0 1 0 0 0 0 0 0 0 0 0 0 0

1750 16 CallbackObject * 0 8576 16 CallbackObject * 16 CallbackObject * 0 0 1751 0 0 0 0 0 0 0 0 0 0

1751 14 CallbackObject 0 2048 14 CallbackObject 14 CallbackObject 0 0 0 0 0 0 0 0 0 0 0 0 276
/**
 * This is a generic object that can be assigned to a callback at various
 * points in the rendering process.  This is actually a base class for a
 * handful of specialized callback object types.  You can also subclass it
 * yourself to make your own callback handler.
 */

1752 32 BulletPersistentManifold const * 0 8576 32 BulletPersistentManifold const * 32 BulletPersistentManifold const * 0 0 1753 0 0 0 0 0 0 0 0 0 0

1753 30 BulletPersistentManifold const 0 8832 30 BulletPersistentManifold const 30 BulletPersistentManifold const 0 0 1576 0 0 0 0 0 0 0 0 0 0

1754 23 BulletConvexHullShape * 0 8576 23 BulletConvexHullShape * 23 BulletConvexHullShape * 0 0 1577 0 0 0 0 0 0 0 0 0 0

1755 29 BulletConvexHullShape const * 0 8576 29 BulletConvexHullShape const * 29 BulletConvexHullShape const * 0 0 1756 0 0 0 0 0 0 0 0 0 0

1756 27 BulletConvexHullShape const 0 8832 27 BulletConvexHullShape const 27 BulletConvexHullShape const 0 0 1577 0 0 0 0 0 0 0 0 0 0

1757 21 PTA_LVecBase3 const * 0 8576 21 PTA_LVecBase3 const * 21 PTA_LVecBase3 const * 0 0 1758 0 0 0 0 0 0 0 0 0 0

1758 19 PTA_LVecBase3 const 0 8832 19 PTA_LVecBase3 const 19 PTA_LVecBase3 const 0 0 1716 0 0 0 0 0 0 0 0 0 0

1759 35 BulletConvexPointCloudShape const * 0 8576 35 BulletConvexPointCloudShape const * 35 BulletConvexPointCloudShape const * 0 0 1760 0 0 0 0 0 0 0 0 0 0

1760 33 BulletConvexPointCloudShape const 0 8832 33 BulletConvexPointCloudShape const 33 BulletConvexPointCloudShape const 0 0 1578 0 0 0 0 0 0 0 0 0 0

1761 29 BulletConvexPointCloudShape * 0 8576 29 BulletConvexPointCloudShape * 29 BulletConvexPointCloudShape * 0 0 1578 0 0 0 0 0 0 0 0 0 0

1762 27 BulletCylinderShape const * 0 8576 27 BulletCylinderShape const * 27 BulletCylinderShape const * 0 0 1763 0 0 0 0 0 0 0 0 0 0

1763 25 BulletCylinderShape const 0 8832 25 BulletCylinderShape const 25 BulletCylinderShape const 0 0 1579 0 0 0 0 0 0 0 0 0 0

1764 21 BulletCylinderShape * 0 8576 21 BulletCylinderShape * 21 BulletCylinderShape * 0 0 1579 0 0 0 0 0 0 0 0 0 0

1765 26 BulletFilterCallbackData * 0 8576 26 BulletFilterCallbackData * 26 BulletFilterCallbackData * 0 0 1580 0 0 0 0 0 0 0 0 0 0

1766 32 BulletFilterCallbackData const * 0 8576 32 BulletFilterCallbackData const * 32 BulletFilterCallbackData const * 0 0 1767 0 0 0 0 0 0 0 0 0 0

1767 30 BulletFilterCallbackData const 0 8832 30 BulletFilterCallbackData const 30 BulletFilterCallbackData const 0 0 1580 0 0 0 0 0 0 0 0 0 0

1768 34 BulletRotationalLimitMotor const * 0 8576 34 BulletRotationalLimitMotor const * 34 BulletRotationalLimitMotor const * 0 0 1769 0 0 0 0 0 0 0 0 0 0

1769 32 BulletRotationalLimitMotor const 0 8832 32 BulletRotationalLimitMotor const 32 BulletRotationalLimitMotor const 0 0 1581 0 0 0 0 0 0 0 0 0 0

1770 28 BulletRotationalLimitMotor * 0 8576 28 BulletRotationalLimitMotor * 28 BulletRotationalLimitMotor * 0 0 1581 0 0 0 0 0 0 0 0 0 0

1771 37 BulletTranslationalLimitMotor const * 0 8576 37 BulletTranslationalLimitMotor const * 37 BulletTranslationalLimitMotor const * 0 0 1772 0 0 0 0 0 0 0 0 0 0

1772 35 BulletTranslationalLimitMotor const 0 8832 35 BulletTranslationalLimitMotor const 35 BulletTranslationalLimitMotor const 0 0 1582 0 0 0 0 0 0 0 0 0 0

1773 31 BulletTranslationalLimitMotor * 0 8576 31 BulletTranslationalLimitMotor * 31 BulletTranslationalLimitMotor * 0 0 1582 0 0 0 0 0 0 0 0 0 0

1774 25 BulletGenericConstraint * 0 8576 25 BulletGenericConstraint * 25 BulletGenericConstraint * 0 0 1583 0 0 0 0 0 0 0 0 0 0

1775 31 BulletGenericConstraint const * 0 8576 31 BulletGenericConstraint const * 31 BulletGenericConstraint const * 0 0 1776 0 0 0 0 0 0 0 0 0 0

1776 29 BulletGenericConstraint const 0 8832 29 BulletGenericConstraint const 29 BulletGenericConstraint const 0 0 1583 0 0 0 0 0 0 0 0 0 0

1777 30 BulletHeightfieldShape const * 0 8576 30 BulletHeightfieldShape const * 30 BulletHeightfieldShape const * 0 0 1778 0 0 0 0 0 0 0 0 0 0

1778 28 BulletHeightfieldShape const 0 8832 28 BulletHeightfieldShape const 28 BulletHeightfieldShape const 0 0 1584 0 0 0 0 0 0 0 0 0 0

1779 24 BulletHeightfieldShape * 0 8576 24 BulletHeightfieldShape * 24 BulletHeightfieldShape * 0 0 1584 0 0 0 0 0 0 0 0 0 0

1780 16 PNMImage const * 0 8576 16 PNMImage const * 16 PNMImage const * 0 0 1781 0 0 0 0 0 0 0 0 0 0

1781 14 PNMImage const 0 8832 14 PNMImage const 14 PNMImage const 0 0 1782 0 0 0 0 0 0 0 0 0 0

1782 8 PNMImage 0 2048 8 PNMImage 8 PNMImage 0 0 0 0 0 0 0 0 0 0 0 0 1516
/**
 * The name of this class derives from the fact that we originally implemented
 * it as a layer on top of the "pnm library", based on netpbm, which was built
 * to implement pbm, pgm, and pbm files, and is the underlying support of a
 * number of public-domain image file converters.  Nowadays we are no longer
 * derived directly from the pnm library, mainly to allow support of C++
 * iostreams instead of the C stdio FILE interface.
 *
 * Conceptually, a PNMImage is a two-dimensional array of xels, which are the
 * PNM-defined generic pixel type.  Each xel may have a red, green, and blue
 * component, or (if the image is grayscale) a gray component.  The image may
 * be read in, the individual xels manipulated, and written out again, or a
 * black image may be constructed from scratch.
 *
 * A PNMImage has a color space and a maxval, the combination of which defines
 * how a floating-point linear color value is encoded as an integer value in
 * memory.  The functions ending in _val operate on encoded colors, whereas
 * the regular ones work with linear floating-point values.  All operations
 * are color space correct unless otherwise specified.
 *
 * The image is of size XSize() by YSize() xels, numbered from top to bottom,
 * left to right, beginning at zero.
 *
 * Files can be specified by filename, or by an iostream pointer.  The
 * filename "-" refers to stdin or stdout.
 *
 * This class is not inherently thread-safe; use it from a single thread or
 * protect access using a mutex.
 */

1783 9 Texture * 0 8576 9 Texture * 9 Texture * 0 0 1784 0 0 0 0 0 0 0 0 0 0

1784 7 Texture 0 2048 7 Texture 7 Texture 0 0 0 0 0 0 0 0 0 0 0 0 688
/**
 * Represents a texture object, which is typically a single 2-d image but may
 * also represent a 1-d or 3-d texture image, or the six 2-d faces of a cube
 * map texture.
 *
 * A texture's image data might be stored in system RAM (see get_ram_image())
 * or its image may be represented in texture memory on one or more
 * GraphicsStateGuardians (see prepare()), or both.  The typical usage pattern
 * is that a texture is loaded from an image file on disk, which copies its
 * image data into system RAM; then the first time the texture is rendered its
 * image data is copied to texture memory (actually, to the graphics API), and
 * the system RAM image is automatically freed.
 */

1785 14 BulletHelper * 0 8576 14 BulletHelper * 14 BulletHelper * 0 0 1585 0 0 0 0 0 0 0 0 0 0

1786 20 BulletHelper const * 0 8576 20 BulletHelper const * 20 BulletHelper const * 0 0 1787 0 0 0 0 0 0 0 0 0 0

1787 18 BulletHelper const 0 8832 18 BulletHelper const 18 BulletHelper const 0 0 1585 0 0 0 0 0 0 0 0 0 0

1788 18 NodePathCollection 0 2048 18 NodePathCollection 18 NodePathCollection 0 0 0 0 0 0 0 0 0 0 0 0 178
/**
 * This is a set of zero or more NodePaths.  It's handy for returning from
 * functions that need to return multiple NodePaths (for instance,
 * NodePaths::get_children).
 */

1789 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 1790 0 0 0 0 0 0 0 0 0 0

1790 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

1791 20 NodePathCollection * 0 8576 20 NodePathCollection * 20 NodePathCollection * 0 0 1788 0 0 0 0 0 0 0 0 0 0

1792 12 InternalName 0 16779264 12 InternalName 12 InternalName 0 0 0 0 0 0 0 0 0 0 0 0 514
/**
 * Encodes a string name in a hash table, mapping it to a pointer.  This is
 * used to tokenify names so they may be used efficiently in low-level Panda
 * structures, for instance to differentiate the multiple sets of texture
 * coordinates that might be stored on a Geom.
 *
 * InternalNames are hierarchical, with the '.' used by convention as a
 * separator character.  You can construct a single InternalName as a
 * composition of one or more other names, or by giving it a source string
 * directly.
 */

1793 14 InternalName * 0 8576 14 InternalName * 14 InternalName * 0 0 1792 0 0 0 0 0 0 0 0 0 0

1794 22 GeomVertexFormat const 0 8832 22 GeomVertexFormat const 22 GeomVertexFormat const 0 0 1795 0 0 0 0 0 0 0 0 0 0

1795 16 GeomVertexFormat 0 16779264 16 GeomVertexFormat 16 GeomVertexFormat 0 0 0 0 0 0 0 0 0 0 0 0 924
/**
 * This class defines the physical layout of the vertex data stored within a
 * Geom.  The layout consists of a list of named columns, each of which has a
 * numeric type and a size.
 *
 * The columns are typically interleaved within a single array, but they may
 * also be distributed among multiple different arrays; at the extreme, each
 * column may be alone within its own array (which amounts to a parallel-array
 * definition).
 *
 * Thus, a GeomVertexFormat is really a list of GeomVertexArrayFormats, each
 * of which contains a list of columns.  However, a particular column name
 * should not appear more than once in the format, even between different
 * arrays.
 *
 * There are a handful of standard pre-defined GeomVertexFormat objects, or
 * you may define your own as needed.  You may record any combination of
 * standard and/or user-defined columns in your custom GeomVertexFormat
 * constructions.
 */

1796 24 GeomVertexFormat const * 0 8576 24 GeomVertexFormat const * 24 GeomVertexFormat const * 0 0 1794 0 0 0 0 0 0 0 0 0 0

1797 23 BulletHingeConstraint * 0 8576 23 BulletHingeConstraint * 23 BulletHingeConstraint * 0 0 1587 0 0 0 0 0 0 0 0 0 0

1798 29 BulletHingeConstraint const * 0 8576 29 BulletHingeConstraint const * 29 BulletHingeConstraint const * 0 0 1799 0 0 0 0 0 0 0 0 0 0

1799 27 BulletHingeConstraint const 0 8832 27 BulletHingeConstraint const 27 BulletHingeConstraint const 0 0 1587 0 0 0 0 0 0 0 0 0 0

1800 31 BulletMinkowskiSumShape const * 0 8576 31 BulletMinkowskiSumShape const * 31 BulletMinkowskiSumShape const * 0 0 1801 0 0 0 0 0 0 0 0 0 0

1801 29 BulletMinkowskiSumShape const 0 8832 29 BulletMinkowskiSumShape const 29 BulletMinkowskiSumShape const 0 0 1588 0 0 0 0 0 0 0 0 0 0

1802 25 BulletMinkowskiSumShape * 0 8576 25 BulletMinkowskiSumShape * 25 BulletMinkowskiSumShape * 0 0 1588 0 0 0 0 0 0 0 0 0 0

1803 30 BulletMultiSphereShape const * 0 8576 30 BulletMultiSphereShape const * 30 BulletMultiSphereShape const * 0 0 1804 0 0 0 0 0 0 0 0 0 0

1804 28 BulletMultiSphereShape const 0 8832 28 BulletMultiSphereShape const 28 BulletMultiSphereShape const 0 0 1591 0 0 0 0 0 0 0 0 0 0

1805 24 BulletMultiSphereShape * 0 8576 24 BulletMultiSphereShape * 24 BulletMultiSphereShape * 0 0 1591 0 0 0 0 0 0 0 0 0 0

1806 20 PTA_stdfloat const * 0 8576 20 PTA_stdfloat const * 20 PTA_stdfloat const * 0 0 1807 0 0 0 0 0 0 0 0 0 0

1807 18 PTA_stdfloat const 0 8832 18 PTA_stdfloat const 18 PTA_stdfloat const 0 0 1808 0 0 0 0 0 0 0 0 0 0

1808 12 PTA_stdfloat 0 2105344 12 PTA_stdfloat 12 PTA_stdfloat 0 0 1809 0 0 0 0 0 0 0 0 0 0

1809 9 PTA_float 0 2105344 9 PTA_float 9 PTA_float 0 0 1810 0 0 0 0 0 0 0 0 0 0

1810 23 PointerToArray< float > 0 2048 23 PointerToArray< float > 23 PointerToArray< float > 0 0 0 0 0 0 0 0 0 0 0 0 0

1811 24 BulletPlaneShape const * 0 8576 24 BulletPlaneShape const * 24 BulletPlaneShape const * 0 0 1812 0 0 0 0 0 0 0 0 0 0

1812 22 BulletPlaneShape const 0 8832 22 BulletPlaneShape const 22 BulletPlaneShape const 0 0 1592 0 0 0 0 0 0 0 0 0 0

1813 18 BulletPlaneShape * 0 8576 18 BulletPlaneShape * 18 BulletPlaneShape * 0 0 1592 0 0 0 0 0 0 0 0 0 0

1814 8 LPlane * 0 8576 8 LPlane * 8 LPlane * 0 0 1593 0 0 0 0 0 0 0 0 0 0

1815 22 CollisionPlane const * 0 8576 22 CollisionPlane const * 22 CollisionPlane const * 0 0 1816 0 0 0 0 0 0 0 0 0 0

1816 20 CollisionPlane const 0 8832 20 CollisionPlane const 20 CollisionPlane const 0 0 1817 0 0 0 0 0 0 0 0 0 0

1817 14 CollisionPlane 0 2048 14 CollisionPlane 14 CollisionPlane 0 0 0 0 0 0 0 0 0 0 0 0 10
/**
 *
 */

1818 24 BulletSliderConstraint * 0 8576 24 BulletSliderConstraint * 24 BulletSliderConstraint * 0 0 1595 0 0 0 0 0 0 0 0 0 0

1819 30 BulletSliderConstraint const * 0 8576 30 BulletSliderConstraint const * 30 BulletSliderConstraint const * 0 0 1820 0 0 0 0 0 0 0 0 0 0

1820 28 BulletSliderConstraint const 0 8832 28 BulletSliderConstraint const 28 BulletSliderConstraint const 0 0 1595 0 0 0 0 0 0 0 0 0 0

1821 29 BulletSoftBodyControl const * 0 8576 29 BulletSoftBodyControl const * 29 BulletSoftBodyControl const * 0 0 1822 0 0 0 0 0 0 0 0 0 0

1822 27 BulletSoftBodyControl const 0 8832 27 BulletSoftBodyControl const 27 BulletSoftBodyControl const 0 0 1596 0 0 0 0 0 0 0 0 0 0

1823 10 IControl * 0 8576 30 btSoftBody::AJoint::IControl * 30 btSoftBody::AJoint::IControl * 0 0 1597 0 0 0 0 0 0 0 0 0 0

1824 27 BulletSoftBodyShape const * 0 8576 27 BulletSoftBodyShape const * 27 BulletSoftBodyShape const * 0 0 1825 0 0 0 0 0 0 0 0 0 0

1825 25 BulletSoftBodyShape const 0 8832 25 BulletSoftBodyShape const 25 BulletSoftBodyShape const 0 0 1600 0 0 0 0 0 0 0 0 0 0

1826 25 BulletSphereShape const * 0 8576 25 BulletSphereShape const * 25 BulletSphereShape const * 0 0 1827 0 0 0 0 0 0 0 0 0 0

1827 23 BulletSphereShape const 0 8832 23 BulletSphereShape const 23 BulletSphereShape const 0 0 1601 0 0 0 0 0 0 0 0 0 0

1828 19 BulletSphereShape * 0 8576 19 BulletSphereShape * 19 BulletSphereShape * 0 0 1601 0 0 0 0 0 0 0 0 0 0

1829 23 CollisionSphere const * 0 8576 23 CollisionSphere const * 23 CollisionSphere const * 0 0 1830 0 0 0 0 0 0 0 0 0 0

1830 21 CollisionSphere const 0 8832 21 CollisionSphere const 21 CollisionSphere const 0 0 1831 0 0 0 0 0 0 0 0 0 0

1831 15 CollisionSphere 0 2048 15 CollisionSphere 15 CollisionSphere 0 0 0 0 0 0 0 0 0 0 0 0 50
/**
 * A spherical collision volume or object.
 */

1832 27 BulletSphericalConstraint * 0 8576 27 BulletSphericalConstraint * 27 BulletSphericalConstraint * 0 0 1602 0 0 0 0 0 0 0 0 0 0

1833 33 BulletSphericalConstraint const * 0 8576 33 BulletSphericalConstraint const * 33 BulletSphericalConstraint const * 0 0 1834 0 0 0 0 0 0 0 0 0 0

1834 31 BulletSphericalConstraint const 0 8832 31 BulletSphericalConstraint const 31 BulletSphericalConstraint const 0 0 1602 0 0 0 0 0 0 0 0 0 0

1835 8 btScalar 0 2105344 8 btScalar 8 btScalar 0 0 1836 0 0 0 0 0 0 0 0 0 0

1836 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

1837 24 BulletTickCallbackData * 0 8576 24 BulletTickCallbackData * 24 BulletTickCallbackData * 0 0 1603 0 0 0 0 0 0 0 0 0 0

1838 30 BulletTickCallbackData const * 0 8576 30 BulletTickCallbackData const * 30 BulletTickCallbackData const * 0 0 1839 0 0 0 0 0 0 0 0 0 0

1839 28 BulletTickCallbackData const 0 8832 28 BulletTickCallbackData const 28 BulletTickCallbackData const 0 0 1603 0 0 0 0 0 0 0 0 0 0

1840 20 BulletTriangleMesh * 0 8576 20 BulletTriangleMesh * 20 BulletTriangleMesh * 0 0 1604 0 0 0 0 0 0 0 0 0 0

1841 15 PTA_int const * 0 8576 15 PTA_int const * 15 PTA_int const * 0 0 1842 0 0 0 0 0 0 0 0 0 0

1842 13 PTA_int const 0 8832 13 PTA_int const 13 PTA_int const 0 0 1720 0 0 0 0 0 0 0 0 0 0

1843 26 BulletTriangleMesh const * 0 8576 26 BulletTriangleMesh const * 26 BulletTriangleMesh const * 0 0 1844 0 0 0 0 0 0 0 0 0 0

1844 24 BulletTriangleMesh const 0 8832 24 BulletTriangleMesh const 24 BulletTriangleMesh const 0 0 1604 0 0 0 0 0 0 0 0 0 0

1845 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 1846 0 0 0 0 0 0 0 0 0 0

1846 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

1847 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 1848 0 0 0 0 0 0 0 0 0 0

1848 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

1849 25 BulletTriangleMeshShape * 0 8576 25 BulletTriangleMeshShape * 25 BulletTriangleMeshShape * 0 0 1606 0 0 0 0 0 0 0 0 0 0

1850 31 BulletTriangleMeshShape const * 0 8576 31 BulletTriangleMeshShape const * 31 BulletTriangleMeshShape const * 0 0 1851 0 0 0 0 0 0 0 0 0 0

1851 29 BulletTriangleMeshShape const 0 8832 29 BulletTriangleMeshShape const 29 BulletTriangleMeshShape const 0 0 1606 0 0 0 0 0 0 0 0 0 0

0
299
1852 4 node 0 2 1504 758 0 0 0 0 0 0 0 18 BulletRayHit::node 0

1853 7 hit_pos 0 2 1506 759 0 0 0 0 0 0 0 21 BulletRayHit::hit_pos 0

1854 10 hit_normal 0 2 1508 760 0 0 0 0 0 0 0 24 BulletRayHit::hit_normal 0

1855 12 hit_fraction 0 2 1510 761 0 0 0 0 0 0 0 26 BulletRayHit::hit_fraction 0

1856 10 shape_part 0 2 1512 762 0 0 0 0 0 0 0 24 BulletRayHit::shape_part 0

1857 14 triangle_index 0 2 1512 763 0 0 0 0 0 0 0 28 BulletRayHit::triangle_index 0

1858 8 from_pos 0 2 1506 769 0 0 0 0 0 0 0 32 BulletAllHitsRayResult::from_pos 0

1859 6 to_pos 0 2 1506 770 0 0 0 0 0 0 0 30 BulletAllHitsRayResult::to_pos 0

1860 20 closest_hit_fraction 0 2 1510 772 0 0 0 0 0 0 0 44 BulletAllHitsRayResult::closest_hit_fraction 0

1861 4 hits 0 66 1503 774 0 0 0 0 773 0 0 28 BulletAllHitsRayResult::hits 0

1862 10 polyhedral 0 2 1518 776 0 0 0 0 0 0 0 23 BulletShape::polyhedral 0

1863 6 convex 0 2 1518 777 0 0 0 0 0 0 0 19 BulletShape::convex 0

1864 9 convex_2d 0 2 1518 778 0 0 0 0 0 0 0 22 BulletShape::convex_2d 0

1865 7 concave 0 2 1518 779 0 0 0 0 0 0 0 20 BulletShape::concave 0

1866 8 infinite 0 2 1518 780 0 0 0 0 0 0 0 21 BulletShape::infinite 0

1867 10 non_moving 0 2 1518 781 0 0 0 0 0 0 0 23 BulletShape::non_moving 0

1868 9 soft_body 0 2 1518 782 0 0 0 0 0 0 0 22 BulletShape::soft_body 0

1869 6 margin 0 6 1510 785 783 0 0 0 0 0 0 19 BulletShape::margin 0

1870 4 name 0 2 1519 784 0 0 0 0 0 0 0 17 BulletShape::name 0

1871 12 shape_bounds 0 2 1522 786 0 0 0 0 0 0 0 25 BulletShape::shape_bounds 0

1872 6 shapes 0 66 1525 793 0 0 0 0 792 0 0 22 BulletBodyNode::shapes 0

1873 9 shape_pos 0 66 1506 794 0 0 0 0 792 0 0 25 BulletBodyNode::shape_pos 0

1874 9 shape_mat 0 66 1526 795 0 0 0 0 792 0 0 25 BulletBodyNode::shape_mat 0

1875 15 shape_transform 0 66 1528 796 0 0 0 0 792 0 0 31 BulletBodyNode::shape_transform 0

1876 12 shape_bounds 0 2 1522 797 0 0 0 0 0 0 0 28 BulletBodyNode::shape_bounds 0

1877 6 static 0 6 1518 799 801 0 0 0 0 0 0 22 BulletBodyNode::static 23
// Static and kinematic

1878 9 kinematic 0 6 1518 800 802 0 0 0 0 0 0 25 BulletBodyNode::kinematic 0

1879 22 collision_notification 0 6 1518 805 804 0 0 0 0 0 0 38 BulletBodyNode::collision_notification 0

1880 18 collision_response 0 6 1518 807 806 0 0 0 0 0 0 34 BulletBodyNode::collision_response 0

1881 16 contact_response 0 2 1518 809 0 0 0 0 0 0 0 32 BulletBodyNode::contact_response 0

1882 28 contact_processing_threshold 0 6 1510 810 811 0 0 0 0 0 0 44 BulletBodyNode::contact_processing_threshold 0

1883 6 active 0 6 1518 812 814 0 0 0 0 0 0 22 BulletBodyNode::active 15
// Deactivation

1884 17 deactivation_time 0 6 1510 816 815 0 0 0 0 0 0 33 BulletBodyNode::deactivation_time 0

1885 20 deactivation_enabled 0 6 1518 818 817 0 0 0 0 0 0 36 BulletBodyNode::deactivation_enabled 0

1886 13 debug_enabled 0 6 1518 820 819 0 0 0 0 0 0 29 BulletBodyNode::debug_enabled 0

1887 11 restitution 0 6 1510 821 822 0 0 0 0 0 0 27 BulletBodyNode::restitution 27
// Friction and Restitution

1888 8 friction 0 6 1510 823 824 0 0 0 0 0 0 24 BulletBodyNode::friction 0

1889 20 anisotropic_friction 0 6 1529 827 826 0 0 0 0 0 0 36 BulletBodyNode::anisotropic_friction 0

1890 23 ccd_swept_sphere_radius 0 6 1510 828 830 0 0 0 0 0 0 39 BulletBodyNode::ccd_swept_sphere_radius 6
// CCD

1891 20 ccd_motion_threshold 0 6 1510 829 831 0 0 0 0 0 0 36 BulletBodyNode::ccd_motion_threshold 0

1892 24 half_extents_with_margin 0 2 1529 836 0 0 0 0 0 0 0 40 BulletBoxShape::half_extents_with_margin 0

1893 27 half_extents_without_margin 0 2 1529 835 0 0 0 0 0 0 0 43 BulletBoxShape::half_extents_without_margin 0

1894 6 radius 0 2 1510 841 0 0 0 0 0 0 0 26 BulletCapsuleShape::radius 0

1895 6 height 0 2 1510 843 0 0 0 0 0 0 0 26 BulletCapsuleShape::height 0

1896 5 shape 0 2 1525 848 0 0 0 0 0 0 0 36 BulletCharacterControllerNode::shape 0

1897 7 gravity 0 6 1510 850 849 0 0 0 0 0 0 38 BulletCharacterControllerNode::gravity 0

1898 9 max_slope 0 6 1510 855 854 0 0 0 0 0 0 40 BulletCharacterControllerNode::max_slope 0

1899 9 on_ground 0 2 1518 857 0 0 0 0 0 0 0 40 BulletCharacterControllerNode::on_ground 0

1900 8 from_pos 0 2 1506 864 0 0 0 0 0 0 0 35 BulletClosestHitRayResult::from_pos 0

1901 6 to_pos 0 2 1506 865 0 0 0 0 0 0 0 33 BulletClosestHitRayResult::to_pos 0

1902 4 node 0 2 1504 867 0 0 0 0 0 0 0 31 BulletClosestHitRayResult::node 0

1903 7 hit_pos 0 2 1506 868 0 0 0 0 0 0 0 34 BulletClosestHitRayResult::hit_pos 0

1904 10 hit_normal 0 2 1508 869 0 0 0 0 0 0 0 37 BulletClosestHitRayResult::hit_normal 0

1905 12 hit_fraction 0 2 1510 870 0 0 0 0 0 0 0 39 BulletClosestHitRayResult::hit_fraction 0

1906 10 shape_part 0 2 1512 871 0 0 0 0 0 0 0 37 BulletClosestHitRayResult::shape_part 0

1907 14 triangle_index 0 2 1512 872 0 0 0 0 0 0 0 41 BulletClosestHitRayResult::triangle_index 0

1908 8 from_pos 0 2 1506 878 0 0 0 0 0 0 0 37 BulletClosestHitSweepResult::from_pos 0

1909 6 to_pos 0 2 1506 879 0 0 0 0 0 0 0 35 BulletClosestHitSweepResult::to_pos 0

1910 4 node 0 2 1504 881 0 0 0 0 0 0 0 33 BulletClosestHitSweepResult::node 0

1911 7 hit_pos 0 2 1506 882 0 0 0 0 0 0 0 36 BulletClosestHitSweepResult::hit_pos 0

1912 10 hit_normal 0 2 1508 883 0 0 0 0 0 0 0 39 BulletClosestHitSweepResult::hit_normal 0

1913 12 hit_fraction 0 2 1510 884 0 0 0 0 0 0 0 41 BulletClosestHitSweepResult::hit_fraction 0

1914 6 radius 0 2 1510 888 0 0 0 0 0 0 0 23 BulletConeShape::radius 0

1915 6 height 0 2 1510 889 0 0 0 0 0 0 0 23 BulletConeShape::height 0

1916 12 rigid_body_a 0 2 1542 891 0 0 0 0 0 0 0 30 BulletConstraint::rigid_body_a 0

1917 4 mass 0 6 1510 905 904 0 0 0 0 0 0 25 BulletRigidBodyNode::mass 0

1918 8 inv_mass 0 2 1510 906 0 0 0 0 0 0 0 29 BulletRigidBodyNode::inv_mass 0

1919 7 inertia 0 6 1508 908 907 0 0 0 0 0 0 28 BulletRigidBodyNode::inertia 0

1920 22 inv_inertia_diag_local 0 2 1508 909 0 0 0 0 0 0 0 43 BulletRigidBodyNode::inv_inertia_diag_local 0

1921 24 inv_inertia_tensor_world 0 2 1544 910 0 0 0 0 0 0 0 45 BulletRigidBodyNode::inv_inertia_tensor_world 0

1922 15 linear_velocity 0 6 1508 911 913 0 0 0 0 0 0 36 BulletRigidBodyNode::linear_velocity 11
// Velocity

1923 16 angular_velocity 0 6 1508 912 914 0 0 0 0 0 0 37 BulletRigidBodyNode::angular_velocity 0

1924 14 linear_damping 0 6 1510 915 917 0 0 0 0 0 0 35 BulletRigidBodyNode::linear_damping 10
// Damping

1925 15 angular_damping 0 6 1510 916 918 0 0 0 0 0 0 36 BulletRigidBodyNode::angular_damping 0

1926 11 total_force 0 2 1508 926 0 0 0 0 0 0 0 32 BulletRigidBodyNode::total_force 0

1927 12 total_torque 0 2 1508 927 0 0 0 0 0 0 0 33 BulletRigidBodyNode::total_torque 0

1928 22 linear_sleep_threshold 0 6 1510 928 930 0 0 0 0 0 0 43 BulletRigidBodyNode::linear_sleep_threshold 26
// Deactivation thresholds

1929 23 angular_sleep_threshold 0 6 1510 929 931 0 0 0 0 0 0 44 BulletRigidBodyNode::angular_sleep_threshold 0

1930 7 gravity 0 6 1508 933 932 0 0 0 0 0 0 28 BulletRigidBodyNode::gravity 0

1931 13 linear_factor 0 6 1508 934 936 0 0 0 0 0 0 34 BulletRigidBodyNode::linear_factor 20
// Restrict movement

1932 14 angular_factor 0 6 1508 935 937 0 0 0 0 0 0 35 BulletRigidBodyNode::angular_factor 0

1933 12 rigid_body_b 0 2 1542 892 0 0 0 0 0 0 0 30 BulletConstraint::rigid_body_b 0

1934 15 debug_draw_size 0 6 1510 895 894 0 0 0 0 0 0 33 BulletConstraint::debug_draw_size 0

1935 15 applied_impulse 0 2 1510 896 0 0 0 0 0 0 0 33 BulletConstraint::applied_impulse 0

1936 18 breaking_threshold 0 6 1510 898 897 0 0 0 0 0 0 36 BulletConstraint::breaking_threshold 0

1937 7 enabled 0 6 1518 900 899 0 0 0 0 0 0 25 BulletConstraint::enabled 0

1938 13 fix_threshold 0 6 1510 944 945 0 0 0 0 0 0 40 BulletConeTwistConstraint::fix_threshold 0

1939 7 frame_a 0 2 1528 952 0 0 0 0 0 0 0 34 BulletConeTwistConstraint::frame_a 0

1940 7 frame_b 0 2 1528 953 0 0 0 0 0 0 0 34 BulletConeTwistConstraint::frame_b 0

1941 9 life_time 0 2 1512 956 0 0 0 0 0 0 0 30 BulletManifoldPoint::life_time 0

1942 8 distance 0 2 1510 957 0 0 0 0 0 0 0 29 BulletManifoldPoint::distance 0

1943 15 applied_impulse 0 6 1510 958 975 0 0 0 0 0 0 36 BulletManifoldPoint::applied_impulse 0

1944 19 position_world_on_a 0 2 1506 959 0 0 0 0 0 0 0 40 BulletManifoldPoint::position_world_on_a 0

1945 19 position_world_on_b 0 2 1506 960 0 0 0 0 0 0 0 40 BulletManifoldPoint::position_world_on_b 0

1946 17 normal_world_on_b 0 2 1508 961 0 0 0 0 0 0 0 38 BulletManifoldPoint::normal_world_on_b 0

1947 13 local_point_a 0 2 1506 962 0 0 0 0 0 0 0 34 BulletManifoldPoint::local_point_a 0

1948 13 local_point_b 0 2 1506 963 0 0 0 0 0 0 0 34 BulletManifoldPoint::local_point_b 0

1949 8 part_id0 0 2 1512 964 0 0 0 0 0 0 0 29 BulletManifoldPoint::part_id0 0

1950 8 part_id1 0 2 1512 965 0 0 0 0 0 0 0 29 BulletManifoldPoint::part_id1 0

1951 6 index0 0 2 1512 966 0 0 0 0 0 0 0 27 BulletManifoldPoint::index0 0

1952 6 index1 0 2 1512 967 0 0 0 0 0 0 0 27 BulletManifoldPoint::index1 0

1953 28 lateral_friction_initialized 0 6 1518 980 968 0 0 0 0 0 0 49 BulletManifoldPoint::lateral_friction_initialized 0

1954 21 lateral_friction_dir1 0 6 1508 981 969 0 0 0 0 0 0 42 BulletManifoldPoint::lateral_friction_dir1 0

1955 21 lateral_friction_dir2 0 6 1508 982 970 0 0 0 0 0 0 42 BulletManifoldPoint::lateral_friction_dir2 0

1956 15 contact_motion1 0 6 1510 983 971 0 0 0 0 0 0 36 BulletManifoldPoint::contact_motion1 0

1957 15 contact_motion2 0 6 1510 984 972 0 0 0 0 0 0 36 BulletManifoldPoint::contact_motion2 0

1958 17 combined_friction 0 6 1510 985 973 0 0 0 0 0 0 38 BulletManifoldPoint::combined_friction 0

1959 20 combined_restitution 0 6 1510 986 974 0 0 0 0 0 0 41 BulletManifoldPoint::combined_restitution 0

1960 24 applied_impulse_lateral1 0 6 1510 987 976 0 0 0 0 0 0 45 BulletManifoldPoint::applied_impulse_lateral1 0

1961 24 applied_impulse_lateral2 0 6 1510 988 977 0 0 0 0 0 0 45 BulletManifoldPoint::applied_impulse_lateral2 0

1962 12 contact_cfm1 0 6 1510 989 978 0 0 0 0 0 0 33 BulletManifoldPoint::contact_cfm1 0

1963 12 contact_cfm2 0 6 1510 990 979 0 0 0 0 0 0 33 BulletManifoldPoint::contact_cfm2 0

1964 8 manifold 0 2 1547 992 0 0 0 0 0 0 0 35 BulletContactCallbackData::manifold 0

1965 5 node0 0 2 1504 993 0 0 0 0 0 0 0 32 BulletContactCallbackData::node0 0

1966 5 node1 0 2 1504 994 0 0 0 0 0 0 0 32 BulletContactCallbackData::node1 0

1967 8 part_id0 0 2 1512 995 0 0 0 0 0 0 0 35 BulletContactCallbackData::part_id0 0

1968 8 part_id1 0 2 1512 996 0 0 0 0 0 0 0 35 BulletContactCallbackData::part_id1 0

1969 6 index0 0 2 1512 997 0 0 0 0 0 0 0 33 BulletContactCallbackData::index0 0

1970 6 index1 0 2 1512 998 0 0 0 0 0 0 0 33 BulletContactCallbackData::index1 0

1971 14 manifold_point 0 2 1547 1000 0 0 0 0 0 0 0 29 BulletContact::manifold_point 0

1972 5 node0 0 2 1504 1001 0 0 0 0 0 0 0 20 BulletContact::node0 0

1973 5 node1 0 2 1504 1002 0 0 0 0 0 0 0 20 BulletContact::node1 0

1974 4 idx0 0 2 1512 1003 0 0 0 0 0 0 0 19 BulletContact::idx0 0

1975 4 idx1 0 2 1512 1004 0 0 0 0 0 0 0 19 BulletContact::idx1 0

1976 8 part_id0 0 2 1512 1005 0 0 0 0 0 0 0 23 BulletContact::part_id0 0

1977 8 part_id1 0 2 1512 1006 0 0 0 0 0 0 0 23 BulletContact::part_id1 0

1978 8 contacts 0 66 1550 1011 0 0 0 0 1010 0 0 29 BulletContactResult::contacts 0

1979 9 wireframe 0 6 1518 1019 1015 0 0 0 0 0 0 26 BulletDebugNode::wireframe 0

1980 11 constraints 0 6 1518 1020 1016 0 0 0 0 0 0 28 BulletDebugNode::constraints 0

1981 14 bounding_boxes 0 6 1518 1021 1017 0 0 0 0 0 0 31 BulletDebugNode::bounding_boxes 0

1982 7 normals 0 6 1518 1022 1018 0 0 0 0 0 0 24 BulletDebugNode::normals 0

1983 17 overlapping_nodes 0 66 1504 1026 0 0 0 0 1025 0 0 34 BulletGhostNode::overlapping_nodes 0

1984 3 pos 0 2 1506 1030 0 0 0 0 0 0 0 30 BulletSoftBodyNodeElement::pos 0

1985 8 velocity 0 2 1508 1031 0 0 0 0 0 0 0 35 BulletSoftBodyNodeElement::velocity 0

1986 6 normal 0 2 1508 1032 0 0 0 0 0 0 0 33 BulletSoftBodyNodeElement::normal 0

1987 8 inv_mass 0 2 1510 1033 0 0 0 0 0 0 0 35 BulletSoftBodyNodeElement::inv_mass 0

1988 4 area 0 2 1510 1034 0 0 0 0 0 0 0 31 BulletSoftBodyNodeElement::area 0

1989 8 attached 0 2 1512 1035 0 0 0 0 0 0 0 35 BulletSoftBodyNodeElement::attached 0

1990 3 cfg 0 2 1557 1037 0 0 0 0 0 0 0 23 BulletSoftBodyNode::cfg 0

1991 10 aero_model 0 6 1559 1086 1085 0 0 0 0 0 0 32 BulletSoftBodyConfig::aero_model 0

1992 28 velocities_correction_factor 0 6 1510 1111 1087 0 0 0 0 0 0 50 BulletSoftBodyConfig::velocities_correction_factor 0

1993 19 damping_coefficient 0 6 1510 1112 1088 0 0 0 0 0 0 41 BulletSoftBodyConfig::damping_coefficient 0

1994 16 drag_coefficient 0 6 1510 1113 1089 0 0 0 0 0 0 38 BulletSoftBodyConfig::drag_coefficient 0

1995 16 lift_coefficient 0 6 1510 1114 1090 0 0 0 0 0 0 38 BulletSoftBodyConfig::lift_coefficient 0

1996 20 pressure_coefficient 0 6 1510 1115 1091 0 0 0 0 0 0 42 BulletSoftBodyConfig::pressure_coefficient 0

1997 31 volume_conservation_coefficient 0 6 1510 1116 1092 0 0 0 0 0 0 53 BulletSoftBodyConfig::volume_conservation_coefficient 0

1998 28 dynamic_friction_coefficient 0 6 1510 1117 1093 0 0 0 0 0 0 50 BulletSoftBodyConfig::dynamic_friction_coefficient 0

1999 25 pose_matching_coefficient 0 6 1510 1118 1094 0 0 0 0 0 0 47 BulletSoftBodyConfig::pose_matching_coefficient 0

2000 23 rigid_contacts_hardness 0 6 1510 1119 1095 0 0 0 0 0 0 45 BulletSoftBodyConfig::rigid_contacts_hardness 0

2001 25 kinetic_contacts_hardness 0 6 1510 1120 1096 0 0 0 0 0 0 47 BulletSoftBodyConfig::kinetic_contacts_hardness 0

2002 22 soft_contacts_hardness 0 6 1510 1121 1097 0 0 0 0 0 0 44 BulletSoftBodyConfig::soft_contacts_hardness 0

2003 16 anchors_hardness 0 6 1510 1122 1098 0 0 0 0 0 0 38 BulletSoftBodyConfig::anchors_hardness 0

2004 22 soft_vs_rigid_hardness 0 6 1510 1123 1099 0 0 0 0 0 0 44 BulletSoftBodyConfig::soft_vs_rigid_hardness 0

2005 24 soft_vs_kinetic_hardness 0 6 1510 1124 1100 0 0 0 0 0 0 46 BulletSoftBodyConfig::soft_vs_kinetic_hardness 0

2006 21 soft_vs_soft_hardness 0 6 1510 1125 1101 0 0 0 0 0 0 43 BulletSoftBodyConfig::soft_vs_soft_hardness 0

2007 27 soft_vs_rigid_impulse_split 0 6 1510 1126 1102 0 0 0 0 0 0 49 BulletSoftBodyConfig::soft_vs_rigid_impulse_split 0

2008 29 soft_vs_kinetic_impulse_split 0 6 1510 1127 1103 0 0 0 0 0 0 51 BulletSoftBodyConfig::soft_vs_kinetic_impulse_split 0

2009 26 soft_vs_soft_impulse_split 0 6 1510 1128 1104 0 0 0 0 0 0 48 BulletSoftBodyConfig::soft_vs_soft_impulse_split 0

2010 9 maxvolume 0 6 1510 1129 1105 0 0 0 0 0 0 31 BulletSoftBodyConfig::maxvolume 0

2011 9 timescale 0 6 1510 1130 1106 0 0 0 0 0 0 31 BulletSoftBodyConfig::timescale 0

2012 27 positions_solver_iterations 0 6 1512 1131 1107 0 0 0 0 0 0 49 BulletSoftBodyConfig::positions_solver_iterations 0

2013 28 velocities_solver_iterations 0 6 1512 1132 1108 0 0 0 0 0 0 50 BulletSoftBodyConfig::velocities_solver_iterations 0

2014 23 drift_solver_iterations 0 6 1512 1133 1109 0 0 0 0 0 0 45 BulletSoftBodyConfig::drift_solver_iterations 0

2015 25 cluster_solver_iterations 0 6 1512 1134 1110 0 0 0 0 0 0 47 BulletSoftBodyConfig::cluster_solver_iterations 0

2016 10 world_info 0 2 1560 1038 0 0 0 0 0 0 0 30 BulletSoftBodyNode::world_info 0

2017 11 air_density 0 6 1510 1142 1137 0 0 0 0 0 0 36 BulletSoftBodyWorldInfo::air_density 0

2018 13 water_density 0 6 1510 1143 1138 0 0 0 0 0 0 38 BulletSoftBodyWorldInfo::water_density 0

2019 12 water_offset 0 6 1510 1144 1139 0 0 0 0 0 0 37 BulletSoftBodyWorldInfo::water_offset 0

2020 12 water_normal 0 6 1508 1145 1140 0 0 0 0 0 0 37 BulletSoftBodyWorldInfo::water_normal 0

2021 7 gravity 0 6 1508 1146 1141 0 0 0 0 0 0 32 BulletSoftBodyWorldInfo::gravity 0

2022 13 wind_velocity 0 6 1508 1053 1052 0 0 0 0 0 0 33 BulletSoftBodyNode::wind_velocity 0

2023 4 aabb 0 2 1561 1055 0 0 0 0 0 0 0 24 BulletSoftBodyNode::aabb 0

2024 12 num_clusters 0 2 1512 1059 0 0 0 0 0 0 0 32 BulletSoftBodyNode::num_clusters 0

2025 9 materials 0 66 1562 1071 0 0 0 0 1070 0 0 29 BulletSoftBodyNode::materials 0

2026 16 linear_stiffness 0 6 1510 1151 1152 0 0 0 0 0 0 40 BulletSoftBodyMaterial::linear_stiffness 0

2027 17 angular_stiffness 0 6 1510 1153 1154 0 0 0 0 0 0 41 BulletSoftBodyMaterial::angular_stiffness 0

2028 19 volume_preservation 0 6 1510 1155 1156 0 0 0 0 0 0 43 BulletSoftBodyMaterial::volume_preservation 0

2029 5 nodes 0 66 1555 1074 0 0 0 0 1073 0 0 25 BulletSoftBodyNode::nodes 0

2030 20 suspension_stiffness 0 6 1510 1165 1159 0 0 0 0 0 0 41 BulletVehicleTuning::suspension_stiffness 0

2031 22 suspension_compression 0 6 1510 1166 1160 0 0 0 0 0 0 43 BulletVehicleTuning::suspension_compression 0

2032 18 suspension_damping 0 6 1510 1167 1161 0 0 0 0 0 0 39 BulletVehicleTuning::suspension_damping 0

2033 24 max_suspension_travel_cm 0 6 1510 1168 1162 0 0 0 0 0 0 45 BulletVehicleTuning::max_suspension_travel_cm 0

2034 13 friction_slip 0 6 1510 1169 1163 0 0 0 0 0 0 34 BulletVehicleTuning::friction_slip 0

2035 20 max_suspension_force 0 6 1510 1170 1164 0 0 0 0 0 0 41 BulletVehicleTuning::max_suspension_force 0

2036 7 chassis 0 2 1542 1176 0 0 0 0 0 0 0 22 BulletVehicle::chassis 0

2037 21 current_speed_km_hour 0 2 1510 1177 0 0 0 0 0 0 0 36 BulletVehicle::current_speed_km_hour 0

2038 14 forward_vector 0 2 1508 1179 0 0 0 0 0 0 0 29 BulletVehicle::forward_vector 0

2039 6 wheels 0 66 1565 1184 0 0 0 0 1183 0 0 21 BulletVehicle::wheels 0

2040 12 raycast_info 0 2 1566 1234 0 0 0 0 0 0 0 25 BulletWheel::raycast_info 0

2041 10 in_contact 0 2 1518 1236 0 0 0 0 0 0 0 34 BulletWheelRaycastInfo::in_contact 0

2042 17 suspension_length 0 2 1510 1237 0 0 0 0 0 0 0 41 BulletWheelRaycastInfo::suspension_length 0

2043 17 contact_normal_ws 0 2 1508 1238 0 0 0 0 0 0 0 41 BulletWheelRaycastInfo::contact_normal_ws 0

2044 18 wheel_direction_ws 0 2 1508 1239 0 0 0 0 0 0 0 42 BulletWheelRaycastInfo::wheel_direction_ws 0

2045 13 wheel_axle_ws 0 2 1508 1240 0 0 0 0 0 0 0 37 BulletWheelRaycastInfo::wheel_axle_ws 0

2046 16 contact_point_ws 0 2 1506 1241 0 0 0 0 0 0 0 40 BulletWheelRaycastInfo::contact_point_ws 0

2047 13 hard_point_ws 0 2 1506 1242 0 0 0 0 0 0 0 37 BulletWheelRaycastInfo::hard_point_ws 0

2048 13 ground_object 0 2 1504 1243 0 0 0 0 0 0 0 37 BulletWheelRaycastInfo::ground_object 0

2049 22 suspension_rest_length 0 2 1510 1210 0 0 0 0 0 0 0 35 BulletWheel::suspension_rest_length 0

2050 20 suspension_stiffness 0 6 1510 1211 1187 0 0 0 0 0 0 33 BulletWheel::suspension_stiffness 0

2051 24 max_suspension_travel_cm 0 6 1510 1212 1188 0 0 0 0 0 0 37 BulletWheel::max_suspension_travel_cm 0

2052 13 friction_slip 0 6 1510 1213 1189 0 0 0 0 0 0 26 BulletWheel::friction_slip 0

2053 20 max_suspension_force 0 6 1510 1214 1190 0 0 0 0 0 0 33 BulletWheel::max_suspension_force 0

2054 26 wheels_damping_compression 0 6 1510 1215 1191 0 0 0 0 0 0 39 BulletWheel::wheels_damping_compression 0

2055 25 wheels_damping_relaxation 0 6 1510 1216 1192 0 0 0 0 0 0 38 BulletWheel::wheels_damping_relaxation 0

2056 14 roll_influence 0 6 1510 1217 1193 0 0 0 0 0 0 27 BulletWheel::roll_influence 0

2057 12 wheel_radius 0 6 1510 1218 1194 0 0 0 0 0 0 25 BulletWheel::wheel_radius 0

2058 8 steering 0 6 1510 1219 1195 0 0 0 0 0 0 21 BulletWheel::steering 0

2059 8 rotation 0 6 1510 1220 1196 0 0 0 0 0 0 21 BulletWheel::rotation 0

2060 14 delta_rotation 0 6 1510 1221 1197 0 0 0 0 0 0 27 BulletWheel::delta_rotation 0

2061 12 engine_force 0 6 1510 1222 1198 0 0 0 0 0 0 25 BulletWheel::engine_force 0

2062 5 brake 0 6 1510 1223 1199 0 0 0 0 0 0 18 BulletWheel::brake 0

2063 9 skid_info 0 6 1510 1224 1200 0 0 0 0 0 0 22 BulletWheel::skid_info 0

2064 23 wheels_suspension_force 0 6 1510 1225 1201 0 0 0 0 0 0 36 BulletWheel::wheels_suspension_force 0

2065 28 suspension_relative_velocity 0 6 1510 1226 1202 0 0 0 0 0 0 41 BulletWheel::suspension_relative_velocity 0

2066 31 clipped_inv_connection_point_cs 0 6 1510 1227 1203 0 0 0 0 0 0 44 BulletWheel::clipped_inv_connection_point_cs 0

2067 27 chassis_connection_point_cs 0 6 1506 1228 1204 0 0 0 0 0 0 40 BulletWheel::chassis_connection_point_cs 0

2068 18 wheel_direction_cs 0 6 1508 1229 1205 0 0 0 0 0 0 31 BulletWheel::wheel_direction_cs 0

2069 13 wheel_axle_cs 0 6 1508 1230 1206 0 0 0 0 0 0 26 BulletWheel::wheel_axle_cs 0

2070 15 world_transform 0 6 1526 1231 1207 0 0 0 0 0 0 28 BulletWheel::world_transform 0

2071 11 front_wheel 0 6 1518 1232 1208 0 0 0 0 0 0 24 BulletWheel::front_wheel 0

2072 4 node 0 6 1504 1233 1209 0 0 0 0 0 0 17 BulletWheel::node 0

2073 6 tuning 0 2 1563 1185 0 0 0 0 0 0 0 21 BulletVehicle::tuning 9
// Tuning

2074 7 gravity 0 6 1508 1249 1248 0 0 0 0 0 0 20 BulletWorld::gravity 0

2075 10 world_info 0 2 1560 1251 0 0 0 0 0 0 0 23 BulletWorld::world_info 0

2076 10 debug_node 0 30 1570 1254 1252 1255 1253 0 0 0 0 23 BulletWorld::debug_node 0

2077 6 ghosts 0 66 1571 1260 0 0 0 0 1259 0 0 19 BulletWorld::ghosts 0

2078 12 rigid_bodies 0 66 1542 1262 0 0 0 0 1261 0 0 25 BulletWorld::rigid_bodies 0

2079 11 soft_bodies 0 66 1572 1264 0 0 0 0 1263 0 0 24 BulletWorld::soft_bodies 0

2080 10 characters 0 66 1573 1266 0 0 0 0 1265 0 0 23 BulletWorld::characters 0

2081 8 vehicles 0 66 1574 1268 0 0 0 0 1267 0 0 21 BulletWorld::vehicles 0

2082 11 constraints 0 66 1575 1270 0 0 0 0 1269 0 0 24 BulletWorld::constraints 0

2083 9 manifolds 0 66 1576 1279 0 0 0 0 1277 0 0 22 BulletWorld::manifolds 0

2084 5 node0 0 2 1504 1291 0 0 0 0 0 0 0 31 BulletPersistentManifold::node0 0

2085 5 node1 0 2 1504 1292 0 0 0 0 0 0 0 31 BulletPersistentManifold::node1 0

2086 15 manifold_points 0 66 1547 1295 0 0 0 0 1293 0 0 41 BulletPersistentManifold::manifold_points 0

2087 26 contact_breaking_threshold 0 2 1510 1296 0 0 0 0 0 0 0 52 BulletPersistentManifold::contact_breaking_threshold 0

2088 28 contact_processing_threshold 0 2 1510 1297 0 0 0 0 0 0 0 54 BulletPersistentManifold::contact_processing_threshold 0

2089 22 force_update_all_aabbs 0 6 1518 1283 1282 0 0 0 0 0 0 35 BulletWorld::force_update_all_aabbs 0

2090 10 num_points 0 2 1512 1318 0 0 0 0 0 0 0 39 BulletConvexPointCloudShape::num_points 0

2091 6 radius 0 2 1510 1321 0 0 0 0 0 0 0 27 BulletCylinderShape::radius 0

2092 27 half_extents_without_margin 0 2 1529 1322 0 0 0 0 0 0 0 48 BulletCylinderShape::half_extents_without_margin 0

2093 24 half_extents_with_margin 0 2 1529 1323 0 0 0 0 0 0 0 45 BulletCylinderShape::half_extents_with_margin 0

2094 6 node_0 0 2 1504 1326 0 0 0 0 0 0 0 32 BulletFilterCallbackData::node_0 0

2095 6 node_1 0 2 1504 1327 0 0 0 0 0 0 0 32 BulletFilterCallbackData::node_1 0

2096 7 collide 0 6 1518 1329 1328 0 0 0 0 0 0 33 BulletFilterCallbackData::collide 0

2097 7 limited 0 2 1518 1346 0 0 0 0 0 0 0 35 BulletRotationalLimitMotor::limited 0

2098 13 motor_enabled 0 6 1518 1347 1334 0 0 0 0 0 0 41 BulletRotationalLimitMotor::motor_enabled 0

2099 13 current_limit 0 2 1512 1348 0 0 0 0 0 0 0 41 BulletRotationalLimitMotor::current_limit 0

2100 13 current_error 0 2 1510 1349 0 0 0 0 0 0 0 41 BulletRotationalLimitMotor::current_error 0

2101 16 current_position 0 2 1510 1350 0 0 0 0 0 0 0 44 BulletRotationalLimitMotor::current_position 0

2102 19 accumulated_impulse 0 2 1510 1351 0 0 0 0 0 0 0 47 BulletRotationalLimitMotor::accumulated_impulse 0

2103 13 current_error 0 2 1508 1368 0 0 0 0 0 0 0 44 BulletTranslationalLimitMotor::current_error 0

2104 12 current_diff 0 2 1506 1369 0 0 0 0 0 0 0 43 BulletTranslationalLimitMotor::current_diff 0

2105 19 accumulated_impulse 0 2 1508 1370 0 0 0 0 0 0 0 50 BulletTranslationalLimitMotor::accumulated_impulse 0

2106 25 translational_limit_motor 0 2 1582 1378 0 0 0 0 0 0 0 50 BulletGenericConstraint::translational_limit_motor 0

2107 7 frame_a 0 2 1528 1380 0 0 0 0 0 0 0 32 BulletGenericConstraint::frame_a 0

2108 7 frame_b 0 2 1528 1381 0 0 0 0 0 0 0 32 BulletGenericConstraint::frame_b 0

2109 8 sb_index 0 2 1586 1387 0 0 0 0 0 0 0 22 BulletHelper::sb_index 17
// Internal names

2110 7 sb_flip 0 2 1586 1388 0 0 0 0 0 0 0 21 BulletHelper::sb_flip 0

2111 11 hinge_angle 0 2 1510 1397 0 0 0 0 0 0 0 34 BulletHingeConstraint::hinge_angle 0

2112 11 lower_limit 0 2 1510 1398 0 0 0 0 0 0 0 34 BulletHingeConstraint::lower_limit 0

2113 11 upper_limit 0 2 1510 1399 0 0 0 0 0 0 0 34 BulletHingeConstraint::upper_limit 0

2114 12 angular_only 0 6 1518 1400 1401 0 0 0 0 0 0 35 BulletHingeConstraint::angular_only 0

2115 7 frame_a 0 2 1528 1409 0 0 0 0 0 0 0 30 BulletHingeConstraint::frame_a 0

2116 7 frame_b 0 2 1528 1410 0 0 0 0 0 0 0 30 BulletHingeConstraint::frame_b 0

2117 11 transform_a 0 6 1528 1415 1413 0 0 0 0 0 0 36 BulletMinkowskiSumShape::transform_a 0

2118 11 transform_b 0 6 1528 1416 1414 0 0 0 0 0 0 36 BulletMinkowskiSumShape::transform_b 0

2119 7 shape_a 0 2 1589 1417 0 0 0 0 0 0 0 32 BulletMinkowskiSumShape::shape_a 0

2120 7 shape_b 0 2 1589 1418 0 0 0 0 0 0 0 32 BulletMinkowskiSumShape::shape_b 0

2121 12 sphere_count 0 2 1512 1422 0 0 0 0 0 0 0 36 BulletMultiSphereShape::sphere_count 0

2122 10 sphere_pos 0 66 1506 1423 0 0 0 0 1422 0 0 34 BulletMultiSphereShape::sphere_pos 0

2123 13 sphere_radius 0 66 1510 1424 0 0 0 0 1422 0 0 37 BulletMultiSphereShape::sphere_radius 0

2124 5 plane 0 2 1593 1427 0 0 0 0 0 0 0 23 BulletPlaneShape::plane 0

2125 12 plane_normal 0 2 1508 1428 0 0 0 0 0 0 0 30 BulletPlaneShape::plane_normal 0

2126 14 plane_constant 0 2 1510 1429 0 0 0 0 0 0 0 32 BulletPlaneShape::plane_constant 0

2127 10 linear_pos 0 2 1510 1433 0 0 0 0 0 0 0 34 BulletSliderConstraint::linear_pos 0

2128 11 angular_pos 0 2 1510 1434 0 0 0 0 0 0 0 35 BulletSliderConstraint::angular_pos 0

2129 18 lower_linear_limit 0 6 1510 1435 1439 0 0 0 0 0 0 42 BulletSliderConstraint::lower_linear_limit 9
// Limits

2130 18 upper_linear_limit 0 6 1510 1436 1440 0 0 0 0 0 0 42 BulletSliderConstraint::upper_linear_limit 0

2131 19 lower_angular_limit 0 6 1510 1437 1441 0 0 0 0 0 0 43 BulletSliderConstraint::lower_angular_limit 0

2132 19 upper_angular_limit 0 6 1510 1438 1442 0 0 0 0 0 0 43 BulletSliderConstraint::upper_angular_limit 0

2133 20 powered_linear_motor 0 6 1518 1446 1443 0 0 0 0 0 0 44 BulletSliderConstraint::powered_linear_motor 0

2134 28 target_linear_motor_velocity 0 6 1510 1447 1444 0 0 0 0 0 0 52 BulletSliderConstraint::target_linear_motor_velocity 0

2135 22 max_linear_motor_force 0 6 1510 1448 1445 0 0 0 0 0 0 46 BulletSliderConstraint::max_linear_motor_force 0

2136 21 powered_angular_motor 0 6 1518 1452 1449 0 0 0 0 0 0 45 BulletSliderConstraint::powered_angular_motor 0

2137 29 target_angular_motor_velocity 0 6 1510 1453 1450 0 0 0 0 0 0 53 BulletSliderConstraint::target_angular_motor_velocity 0

2138 23 max_angular_motor_force 0 6 1510 1454 1451 0 0 0 0 0 0 47 BulletSliderConstraint::max_angular_motor_force 0

2139 7 frame_a 0 2 1528 1456 0 0 0 0 0 0 0 31 BulletSliderConstraint::frame_a 0

2140 7 frame_b 0 2 1528 1457 0 0 0 0 0 0 0 31 BulletSliderConstraint::frame_b 0

2141 4 body 0 2 1572 1467 0 0 0 0 0 0 0 25 BulletSoftBodyShape::body 0

2142 6 radius 0 2 1510 1470 0 0 0 0 0 0 0 25 BulletSphereShape::radius 0

2143 7 pivot_a 0 6 1506 1476 1474 0 0 0 0 0 0 34 BulletSphericalConstraint::pivot_a 0

2144 7 pivot_b 0 6 1506 1477 1475 0 0 0 0 0 0 34 BulletSphericalConstraint::pivot_b 0

2145 8 timestep 0 2 1510 1480 0 0 0 0 0 0 0 32 BulletTickCallbackData::timestep 0

2146 16 welding_distance 0 6 1510 1490 1487 0 0 0 0 0 0 36 BulletTriangleMesh::welding_distance 0

2147 8 vertices 0 66 1506 1494 0 0 0 0 1493 0 0 28 BulletTriangleMesh::vertices 0

2148 9 triangles 0 66 1605 1495 0 0 0 0 1489 0 0 29 BulletTriangleMesh::triangles 0

2149 6 static 0 2 1518 1499 0 0 0 0 0 0 0 31 BulletTriangleMeshShape::static 0

2150 7 dynamic 0 2 1518 1500 0 0 0 0 0 0 0 32 BulletTriangleMeshShape::dynamic 0

15
2151 8 get_hits 0 773 774 32 BulletAllHitsRayResult::get_hits 0

2152 10 get_shapes 0 792 793 26 BulletBodyNode::get_shapes 0

2153 12 get_contacts 0 1010 1011 33 BulletContactResult::get_contacts 0

2154 21 get_overlapping_nodes 0 1025 1026 38 BulletGhostNode::get_overlapping_nodes 0

2155 13 get_materials 0 1070 1071 33 BulletSoftBodyNode::get_materials 0

2156 9 get_nodes 0 1073 1074 29 BulletSoftBodyNode::get_nodes 0

2157 10 get_wheels 0 1183 1184 25 BulletVehicle::get_wheels 0

2158 10 get_ghosts 0 1259 1260 23 BulletWorld::get_ghosts 0

2159 16 get_rigid_bodies 0 1261 1262 29 BulletWorld::get_rigid_bodies 0

2160 15 get_soft_bodies 0 1263 1264 28 BulletWorld::get_soft_bodies 0

2161 14 get_characters 0 1265 1266 27 BulletWorld::get_characters 0

2162 12 get_vehicles 0 1267 1268 25 BulletWorld::get_vehicles 0

2163 15 get_constraints 0 1269 1270 28 BulletWorld::get_constraints 0

2164 13 get_manifolds 0 1277 1279 26 BulletWorld::get_manifolds 0

2165 19 get_manifold_points 0 1293 1295 45 BulletPersistentManifold::get_manifold_points 0

