1730992849
3 3
12 libp3deadrec 4 FP4V 14 panda3d.direct 
54
59 11 SmoothMover 0 260 113 24 SmoothMover::SmoothMover 0 2 1 2 10
/**
 *
 */
95
SmoothMover::SmoothMover(void);
inline SmoothMover::SmoothMover(SmoothMover const &) = default;

60 12 ~SmoothMover 0 516 113 25 SmoothMover::~SmoothMover 0 0 10
/**
 *
 */
32
SmoothMover::~SmoothMover(void);

61 7 set_pos 0 4 113 20 SmoothMover::set_pos 0 2 3 4 1188
// These methods are used to specify each position update.  Call the
// appropriate set_* function(s), as needed, and then call mark_position().
// The return value of each function is true if the parameter value has
// changed, or false if it remains the same as last time.

/**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with hpr and
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos(), or false if they are the same.
 */

/**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with hpr and
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos(), or false if they are the same.
 */
134
inline bool SmoothMover::set_pos(LVecBase3 const &pos);
inline bool SmoothMover::set_pos(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z);

62 5 set_x 0 4 113 18 SmoothMover::set_x 0 1 5 52
/**
 * Sets the X position only.  See set_pos().
 */
46
inline bool SmoothMover::set_x(PN_stdfloat x);

63 5 set_y 0 4 113 18 SmoothMover::set_y 0 1 6 52
/**
 * Sets the Y position only.  See set_pos().
 */
46
inline bool SmoothMover::set_y(PN_stdfloat y);

64 5 set_z 0 4 113 18 SmoothMover::set_z 0 1 7 52
/**
 * Sets the Z position only.  See set_pos().
 */
46
inline bool SmoothMover::set_z(PN_stdfloat z);

65 7 set_hpr 0 4 113 20 SmoothMover::set_hpr 0 2 8 9 920
/**
 * Specifies the orientation of the SmoothMover at a particular time in the
 * past.  When mark_position() is called, this will be recorded (along with
 * hpr and timestamp) in a position report, which will then be used along with
 * all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_hpr(), or false if they are the same.
 */

/**
 * Specifies the orientation of the SmoothMover at a particular time in the
 * past.  When mark_position() is called, this will be recorded (along with
 * hpr and timestamp) in a position report, which will then be used along with
 * all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_hpr(), or false if they are the same.
 */
134
inline bool SmoothMover::set_hpr(LVecBase3 const &hpr);
inline bool SmoothMover::set_hpr(PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);

66 5 set_h 0 4 113 18 SmoothMover::set_h 0 1 10 49
/**
 * Sets the heading only.  See set_hpr().
 */
46
inline bool SmoothMover::set_h(PN_stdfloat h);

67 5 set_p 0 4 113 18 SmoothMover::set_p 0 1 11 47
/**
 * Sets the pitch only.  See set_hpr().
 */
46
inline bool SmoothMover::set_p(PN_stdfloat p);

68 5 set_r 0 4 113 18 SmoothMover::set_r 0 1 12 46
/**
 * Sets the roll only.  See set_hpr().
 */
46
inline bool SmoothMover::set_r(PN_stdfloat r);

69 11 set_pos_hpr 0 4 113 24 SmoothMover::set_pos_hpr 0 2 13 14 921
/**
 * Specifies the position and orientation of the SmoothMover at a particular
 * time in the past.  When mark_position() is called, this will be recorded
 * (along with timestamp) in a position report, which will then be used along
 * with all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos_hpr(), or false if they are the same.
 */

/**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos_hpr(), or false if they are the same.
 */
209
inline bool SmoothMover::set_pos_hpr(LVecBase3 const &pos, LVecBase3 const &hpr);
inline bool SmoothMover::set_pos_hpr(PN_stdfloat x, PN_stdfloat y, PN_stdfloat z, PN_stdfloat h, PN_stdfloat p, PN_stdfloat r);

70 14 get_sample_pos 0 4 113 27 SmoothMover::get_sample_pos 0 1 15 230
/**
 * Returns the current position of the working sample point.  This position is
 * updated periodically by set_x(), set_y(), etc., and its current value is
 * copied to the sample point table when mark_position() is called.
 */
62
inline LPoint3 const &SmoothMover::get_sample_pos(void) const;

71 14 get_sample_hpr 0 4 113 27 SmoothMover::get_sample_hpr 0 1 16 239
/**
 * Returns the current orientation of the working sample point.  This
 * orientation is updated periodically by set_h(), set_p(), etc., and its
 * current value is copied to the sample point table when mark_position() is
 * called.
 */
64
inline LVecBase3 const &SmoothMover::get_sample_hpr(void) const;

72 19 set_phony_timestamp 0 4 113 32 SmoothMover::set_phony_timestamp 0 1 17 191
/**
 * Lies and specifies that the current position report was received now.  This
 * is usually used for very old position reports for which we're not sure of
 * the actual receipt time.
 */
97
inline void SmoothMover::set_phony_timestamp(double timestamp = 0.0, bool period_adjust = false);

73 13 set_timestamp 0 4 113 26 SmoothMover::set_timestamp 0 1 18 171
/**
 * Specifies the time that the current position report applies.  This should
 * be called, along with set_pos() and set_hpr(), before a call to
 * mark_position().
 */
57
inline void SmoothMover::set_timestamp(double timestamp);

74 25 has_most_recent_timestamp 0 4 113 38 SmoothMover::has_most_recent_timestamp 0 1 19 67
/**
 * Returns true if we have most recently recorded timestamp
 */
63
inline bool SmoothMover::has_most_recent_timestamp(void) const;

75 25 get_most_recent_timestamp 0 4 113 38 SmoothMover::get_most_recent_timestamp 0 1 20 51
/**
 * Returns most recently recorded timestamp
 */
65
inline double SmoothMover::get_most_recent_timestamp(void) const;

76 13 mark_position 0 4 113 26 SmoothMover::mark_position 0 1 21 313
/**
 * Stores the position, orientation, and timestamp (if relevant) indicated by
 * previous calls to set_pos(), set_hpr(), and set_timestamp() in a new
 * position report.
 *
 * When compute_smooth_position() is called, it uses these stored position
 * reports to base its computation of the known position.
 */
38
void SmoothMover::mark_position(void);

77 15 clear_positions 0 4 113 28 SmoothMover::clear_positions 0 1 22 264
/**
 * Erases all the old position reports.  This should be done, for instance,
 * prior to teleporting the avatar to a new position; otherwise, the smoother
 * might try to lerp the avatar there.  If reset_velocity is true, the
 * velocity is also reset to 0.
 */
55
void SmoothMover::clear_positions(bool reset_velocity);

78 23 compute_smooth_position 0 4 113 36 SmoothMover::compute_smooth_position 0 2 23 24 782
/**
 * Computes the smoothed position (and orientation) of the mover at the
 * indicated point in time, based on the previous position reports.  After
 * this call has been made, get_smooth_pos() etc.  may be called to retrieve
 * the smoothed position.
 *
 * With no parameter, the function uses ClockObject::get_frame_time() as the
 * default time.
 */

/**
 * Computes the smoothed position (and orientation) of the mover at the
 * indicated point in time, based on the previous position reports.  After
 * this call has been made, get_smooth_pos() etc.  may be called to retrieve
 * the smoothed position.
 *
 * The return value is true if the value has changed (or might have changed)
 * since the last call to compute_smooth_position(), or false if it remains
 * the same.
 */
116
inline bool SmoothMover::compute_smooth_position(void);
bool SmoothMover::compute_smooth_position(double timestamp);

79 19 get_latest_position 0 4 113 32 SmoothMover::get_latest_position 0 1 25 257
/**
 * Updates the smooth_pos (and smooth_hpr, etc.) members to reflect the
 * absolute latest position known for this avatar.  This may result in a pop
 * to the most recent position.
 *
 * Returns true if the latest position is known, false otherwise.
 */
44
bool SmoothMover::get_latest_position(void);

80 14 get_smooth_pos 0 4 113 27 SmoothMover::get_smooth_pos 0 1 26 104
/**
 * Returns the smoothed position as computed by a previous call to
 * compute_smooth_position().
 */
62
inline LPoint3 const &SmoothMover::get_smooth_pos(void) const;

81 14 get_smooth_hpr 0 4 113 27 SmoothMover::get_smooth_hpr 0 1 27 107
/**
 * Returns the smoothed orientation as computed by a previous call to
 * compute_smooth_position().
 */
64
inline LVecBase3 const &SmoothMover::get_smooth_hpr(void) const;

82 16 apply_smooth_pos 0 4 113 29 SmoothMover::apply_smooth_pos 0 1 28 255
/**
 * Applies the smoothed position to the indicated NodePath.  This is
 * equivalent to calling node.set_pos(smooth_mover->get_smooth_pos()).  It
 * exists as an optimization only, to avoid the overhead of passing the return
 * value through Python.
 */
64
inline void SmoothMover::apply_smooth_pos(NodePath &node) const;

83 20 apply_smooth_pos_hpr 0 4 113 33 SmoothMover::apply_smooth_pos_hpr 0 1 29 309
/**
 * Applies the smoothed position and orientation to the indicated NodePath.
 * This is equivalent to calling
 * node.set_pos_hpr(smooth_mover->get_smooth_pos(),
 * smooth_mover->get_smooth_hpr()).  It exists as an optimization only, to
 * avoid the overhead of passing the return value through Python.
 */
92
inline void SmoothMover::apply_smooth_pos_hpr(NodePath &pos_node, NodePath &hpr_node) const;

84 16 apply_smooth_hpr 0 4 113 29 SmoothMover::apply_smooth_hpr 0 1 30 258
/**
 * Applies the smoothed orientation to the indicated NodePath.  This is
 * equivalent to calling node.set_hpr(smooth_mover->get_smooth_hpr()).  It
 * exists as an optimization only, to avoid the overhead of passing the return
 * value through Python.
 */
64
inline void SmoothMover::apply_smooth_hpr(NodePath &node) const;

85 28 compute_and_apply_smooth_pos 0 4 113 41 SmoothMover::compute_and_apply_smooth_pos 0 1 31 145
/**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node in one call.
 */
70
inline void SmoothMover::compute_and_apply_smooth_pos(NodePath &node);

86 32 compute_and_apply_smooth_pos_hpr 0 4 113 45 SmoothMover::compute_and_apply_smooth_pos_hpr 0 1 32 212
/**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node or nodes in one call.  The
 * pos_node and hpr_node might be the same NodePath.
 */
98
inline void SmoothMover::compute_and_apply_smooth_pos_hpr(NodePath &pos_node, NodePath &hpr_node);

87 28 compute_and_apply_smooth_hpr 0 4 113 41 SmoothMover::compute_and_apply_smooth_hpr 0 1 33 212
/**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node or nodes in one call.  The
 * pos_node and hpr_node might be the same NodePath.
 */
74
inline void SmoothMover::compute_and_apply_smooth_hpr(NodePath &hpr_node);

88 27 get_smooth_forward_velocity 0 4 113 40 SmoothMover::get_smooth_forward_velocity 0 1 34 263
/**
 * Returns the speed at which the avatar is moving, in feet per second, along
 * its own forward axis (after applying the avatar's hpr).  This will be a
 * positive number if the avatar is moving forward, and a negative number if
 * it is moving backward.
 */
72
inline PN_stdfloat SmoothMover::get_smooth_forward_velocity(void) const;

89 27 get_smooth_lateral_velocity 0 4 113 40 SmoothMover::get_smooth_lateral_velocity 0 1 35 257
/**
 * Returns the speed at which the avatar is moving, in feet per second, along
 * its own lateral axis (after applying the avatar's hpr).  This will be a
 * positive number if the avatar is moving right, and a negative number if it
 * is moving left.
 */
72
inline PN_stdfloat SmoothMover::get_smooth_lateral_velocity(void) const;

90 30 get_smooth_rotational_velocity 0 4 113 43 SmoothMover::get_smooth_rotational_velocity 0 1 36 205
/**
 * Returns the speed at which the avatar is rotating in the horizontal plane
 * (i.e.  heading), in degrees per second.  This may be positive or negative,
 * according to the direction of rotation.
 */
75
inline PN_stdfloat SmoothMover::get_smooth_rotational_velocity(void) const;

91 16 get_forward_axis 0 4 113 29 SmoothMover::get_forward_axis 0 1 37 104
/**
 * Returns the smoothed position as computed by a previous call to
 * compute_smooth_position().
 */
66
inline LVecBase3 const &SmoothMover::get_forward_axis(void) const;

92 19 handle_wrt_reparent 0 4 113 32 SmoothMover::handle_wrt_reparent 0 1 38 97
/**
 * Node is being wrtReparented, update recorded sample positions to reflect
 * new parent
 */
82
void SmoothMover::handle_wrt_reparent(NodePath &old_parent, NodePath &new_parent);

93 15 set_smooth_mode 0 4 113 28 SmoothMover::set_smooth_mode 0 1 39 222
/**
 * Sets the smoothing mode of all SmoothMovers in the world.  If this is
 * SM_off, no smoothing or prediction will be performed, and get_smooth_pos()
 * will simply return the position last set by mark_position().
 */
71
inline void SmoothMover::set_smooth_mode(SmoothMover::SmoothMode mode);

94 15 get_smooth_mode 0 4 113 28 SmoothMover::get_smooth_mode 0 1 40 98
/**
 * Returns the smoothing mode of all SmoothMovers in the world.  See
 * set_smooth_mode().
 */
66
inline SmoothMover::SmoothMode SmoothMover::get_smooth_mode(void);

95 19 set_prediction_mode 0 4 113 32 SmoothMover::set_prediction_mode 0 1 41 171
/**
 * Sets the predictioning mode of all SmoothMovers in the world.  If this is
 * PM_off, no prediction will be performed, but smoothing might still be
 * performed.
 */
79
inline void SmoothMover::set_prediction_mode(SmoothMover::PredictionMode mode);

96 19 get_prediction_mode 0 4 113 32 SmoothMover::get_prediction_mode 0 1 42 106
/**
 * Returns the predictioning mode of all SmoothMovers in the world.  See
 * set_prediction_mode().
 */
74
inline SmoothMover::PredictionMode SmoothMover::get_prediction_mode(void);

97 9 set_delay 0 4 113 22 SmoothMover::set_delay 0 1 43 349
/**
 * Sets the amount of time, in seconds, to delay the computed position of a
 * SmoothMover.  This is particularly useful when the prediction mode is off,
 * because it can allow the apparent motion of an avatar to appear smooth
 * without relying on prediction, at the cost of introducing additional lag in
 * the avatar's apparent position.
 */
49
inline void SmoothMover::set_delay(double delay);

98 9 get_delay 0 4 113 22 SmoothMover::get_delay 0 1 44 120
/**
 * Returns the amount of time, in seconds, to delay the computed position of a
 * SmoothMover.  See set_delay().
 */
43
inline double SmoothMover::get_delay(void);

99 21 set_accept_clock_skew 0 4 113 34 SmoothMover::set_accept_clock_skew 0 1 45 585
/**
 * Sets the 'accept clock skew' flag.  When this flag is true, clock skew from
 * the other clients will be tolerated by delaying each smooth mover's
 * position an additional amount, on top of that specified by set_delay(),
 * based on the measured average latency for timestamp messages received by
 * the client.
 *
 * In this way, if the other client has significant clock skew with respect to
 * our clock, it will be evident as a large positive or negative average
 * latency for timestamps.  By subtracting out this average latency, we
 * compensate for poor clock sync.
 */
58
inline void SmoothMover::set_accept_clock_skew(bool flag);

100 21 get_accept_clock_skew 0 4 113 34 SmoothMover::get_accept_clock_skew 0 1 46 102
/**
 * Returns the current state of the 'accept clock skew' flag.  See
 * set_accept_clock_skew().
 */
53
inline bool SmoothMover::get_accept_clock_skew(void);

101 20 set_max_position_age 0 4 113 33 SmoothMover::set_max_position_age 0 1 47 153
/**
 * Sets the maximum amount of time a position is allowed to remain unchanged
 * before assuming it represents the avatar actually standing still.
 */
58
inline void SmoothMover::set_max_position_age(double age);

102 20 get_max_position_age 0 4 113 33 SmoothMover::get_max_position_age 0 1 48 156
/**
 * Returns the maximum amount of time a position is allowed to remain
 * unchanged before assuming it represents the avatar actually standing still.
 */
54
inline double SmoothMover::get_max_position_age(void);

103 29 set_expected_broadcast_period 0 4 113 42 SmoothMover::set_expected_broadcast_period 0 1 49 242
/**
 * Sets the interval at which we expect the SmoothNodes to broadcast their
 * position, in elapsed seconds.  This controls the length of time we assume
 * the object has truly stopped, when we receive a long sequence of no
 * updates.
 */
70
inline void SmoothMover::set_expected_broadcast_period(double period);

104 29 get_expected_broadcast_period 0 4 113 42 SmoothMover::get_expected_broadcast_period 0 1 50 156
/**
 * Returns the interval at which we expect the SmoothNodes to broadcast their
 * position, in elapsed seconds.  See set_expected_broadcast_period().
 */
63
inline double SmoothMover::get_expected_broadcast_period(void);

105 22 set_reset_velocity_age 0 4 113 35 SmoothMover::set_reset_velocity_age 0 1 51 302
/**
 * Sets the amount of time that should elapse after the last position report
 * before the velocity is reset to 0.  This is similar to max_position_age,
 * but it is only used to determine the resetting of the reported velocity.
 * It should always be greater than or equal to max_position_age.
 */
60
inline void SmoothMover::set_reset_velocity_age(double age);

106 22 get_reset_velocity_age 0 4 113 35 SmoothMover::get_reset_velocity_age 0 1 52 156
/**
 * Returns the amount of time that should elapse after the last position
 * report before the velocity is reset to 0.  See set_reset_velocity_age().
 */
56
inline double SmoothMover::get_reset_velocity_age(void);

107 24 set_directional_velocity 0 4 113 37 SmoothMover::set_directional_velocity 0 1 53 357
/**
 * Sets the flag that indicates whether the avatar's direction is considered
 * in computing the velocity.  When this is true, velocity is automatically
 * decomposed into a forward and a lateral velocity (and both may be positive
 * or negative); when it is false, all velocity is always returned as forward
 * velocity (and it is always positive).
 */
61
inline void SmoothMover::set_directional_velocity(bool flag);

108 24 get_directional_velocity 0 4 113 37 SmoothMover::get_directional_velocity 0 1 54 108
/**
 * Returns the current state of the 'directional velocity' flag.  See
 * set_directional_velocity().
 */
56
inline bool SmoothMover::get_directional_velocity(void);

109 29 set_default_to_standing_still 0 4 113 42 SmoothMover::set_default_to_standing_still 0 1 55 295
/**
 * Sets the flag that indicates whether to assume that the node stopped moving
 * during periods when we don't get enough position updates.  If true, the
 * object will stand still momentarily.  If false, the object will
 * continuously lerp between the position updates that we did get.
 */
66
inline void SmoothMover::set_default_to_standing_still(bool flag);

110 29 get_default_to_standing_still 0 4 113 42 SmoothMover::get_default_to_standing_still 0 1 56 118
/**
 * Returns the current state of the 'default to standing still' flag.  See
 * set_default_to_standing_still().
 */
61
inline bool SmoothMover::get_default_to_standing_still(void);

111 6 output 0 4 113 19 SmoothMover::output 0 1 57 10
/**
 *
 */
50
void SmoothMover::output(std::ostream &out) const;

112 5 write 0 4 113 18 SmoothMover::write 0 1 58 10
/**
 *
 */
49
void SmoothMover::write(std::ostream &out) const;

58
1 0 0 7 2 116 60 0 10 /**
 *
 */ 0 
2 0 0 15 2 116 60 0 0 1 6 param0 0 117  
3 0 0 6 4 123 0 0 455 /**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with hpr and
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos(), or false if they are the same.
 */ 2 4 this 3 116  3 pos 1 119  
4 0 0 6 4 123 0 0 455 /**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with hpr and
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos(), or false if they are the same.
 */ 4 4 this 3 116  1 x 1 124  1 y 1 124  1 z 1 124  
5 0 0 6 5 123 0 0 52 /**
 * Sets the X position only.  See set_pos().
 */ 2 4 this 3 116  1 x 1 124  
6 0 0 6 6 123 0 0 52 /**
 * Sets the Y position only.  See set_pos().
 */ 2 4 this 3 116  1 y 1 124  
7 0 0 6 7 123 0 0 52 /**
 * Sets the Z position only.  See set_pos().
 */ 2 4 this 3 116  1 z 1 124  
8 0 0 6 8 123 0 0 459 /**
 * Specifies the orientation of the SmoothMover at a particular time in the
 * past.  When mark_position() is called, this will be recorded (along with
 * hpr and timestamp) in a position report, which will then be used along with
 * all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_hpr(), or false if they are the same.
 */ 2 4 this 3 116  3 hpr 1 119  
9 0 0 6 8 123 0 0 459 /**
 * Specifies the orientation of the SmoothMover at a particular time in the
 * past.  When mark_position() is called, this will be recorded (along with
 * hpr and timestamp) in a position report, which will then be used along with
 * all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_hpr(), or false if they are the same.
 */ 4 4 this 3 116  1 h 1 124  1 p 1 124  1 r 1 124  
10 0 0 6 9 123 0 0 49 /**
 * Sets the heading only.  See set_hpr().
 */ 2 4 this 3 116  1 h 1 124  
11 0 0 6 10 123 0 0 47 /**
 * Sets the pitch only.  See set_hpr().
 */ 2 4 this 3 116  1 p 1 124  
12 0 0 6 11 123 0 0 46 /**
 * Sets the roll only.  See set_hpr().
 */ 2 4 this 3 116  1 r 1 124  
13 0 0 6 12 123 0 0 468 /**
 * Specifies the position and orientation of the SmoothMover at a particular
 * time in the past.  When mark_position() is called, this will be recorded
 * (along with timestamp) in a position report, which will then be used along
 * with all other position reports to determine the smooth position at any
 * particular instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos_hpr(), or false if they are the same.
 */ 3 4 this 3 116  3 pos 1 119  3 hpr 1 119  
14 0 0 6 12 123 0 0 451 /**
 * Specifies the position of the SmoothMover at a particular time in the past.
 * When mark_position() is called, this will be recorded (along with
 * timestamp) in a position report, which will then be used along with all
 * other position reports to determine the smooth position at any particular
 * instant.
 *
 * The return value is true if any parameter has changed since the last call
 * to set_pos_hpr(), or false if they are the same.
 */ 7 4 this 3 116  1 x 1 124  1 y 1 124  1 z 1 124  1 h 1 124  1 p 1 124  1 r 1 124  
15 0 0 6 13 126 0 0 230 /**
 * Returns the current position of the working sample point.  This position is
 * updated periodically by set_x(), set_y(), etc., and its current value is
 * copied to the sample point table when mark_position() is called.
 */ 1 4 this 3 117  
16 0 0 6 14 119 0 0 239 /**
 * Returns the current orientation of the working sample point.  This
 * orientation is updated periodically by set_h(), set_p(), etc., and its
 * current value is copied to the sample point table when mark_position() is
 * called.
 */ 1 4 this 3 117  
17 0 0 4 15 131 0 0 191 /**
 * Lies and specifies that the current position report was received now.  This
 * is usually used for very old position reports for which we're not sure of
 * the actual receipt time.
 */ 3 4 this 3 116  9 timestamp 5 130  13 period_adjust 5 123  
18 0 0 4 16 131 0 0 171 /**
 * Specifies the time that the current position report applies.  This should
 * be called, along with set_pos() and set_hpr(), before a call to
 * mark_position().
 */ 2 4 this 3 116  9 timestamp 1 130  
19 0 0 6 17 123 0 0 67 /**
 * Returns true if we have most recently recorded timestamp
 */ 1 4 this 3 117  
20 0 0 6 18 130 0 0 51 /**
 * Returns most recently recorded timestamp
 */ 1 4 this 3 117  
21 0 0 4 19 131 0 0 313 /**
 * Stores the position, orientation, and timestamp (if relevant) indicated by
 * previous calls to set_pos(), set_hpr(), and set_timestamp() in a new
 * position report.
 *
 * When compute_smooth_position() is called, it uses these stored position
 * reports to base its computation of the known position.
 */ 1 4 this 3 116  
22 0 0 4 20 131 0 0 264 /**
 * Erases all the old position reports.  This should be done, for instance,
 * prior to teleporting the avatar to a new position; otherwise, the smoother
 * might try to lerp the avatar there.  If reset_velocity is true, the
 * velocity is also reset to 0.
 */ 2 4 this 3 116  14 reset_velocity 1 123  
23 0 0 6 21 123 0 0 354 /**
 * Computes the smoothed position (and orientation) of the mover at the
 * indicated point in time, based on the previous position reports.  After
 * this call has been made, get_smooth_pos() etc.  may be called to retrieve
 * the smoothed position.
 *
 * With no parameter, the function uses ClockObject::get_frame_time() as the
 * default time.
 */ 1 4 this 3 116  
24 0 0 6 21 123 0 0 426 /**
 * Computes the smoothed position (and orientation) of the mover at the
 * indicated point in time, based on the previous position reports.  After
 * this call has been made, get_smooth_pos() etc.  may be called to retrieve
 * the smoothed position.
 *
 * The return value is true if the value has changed (or might have changed)
 * since the last call to compute_smooth_position(), or false if it remains
 * the same.
 */ 2 4 this 3 116  9 timestamp 1 130  
25 0 0 6 22 123 0 0 257 /**
 * Updates the smooth_pos (and smooth_hpr, etc.) members to reflect the
 * absolute latest position known for this avatar.  This may result in a pop
 * to the most recent position.
 *
 * Returns true if the latest position is known, false otherwise.
 */ 1 4 this 3 116  
26 0 0 6 23 126 0 0 104 /**
 * Returns the smoothed position as computed by a previous call to
 * compute_smooth_position().
 */ 1 4 this 3 117  
27 0 0 6 24 119 0 0 107 /**
 * Returns the smoothed orientation as computed by a previous call to
 * compute_smooth_position().
 */ 1 4 this 3 117  
28 0 0 4 25 131 0 0 255 /**
 * Applies the smoothed position to the indicated NodePath.  This is
 * equivalent to calling node.set_pos(smooth_mover->get_smooth_pos()).  It
 * exists as an optimization only, to avoid the overhead of passing the return
 * value through Python.
 */ 2 4 this 3 117  4 node 1 132  
29 0 0 4 26 131 0 0 309 /**
 * Applies the smoothed position and orientation to the indicated NodePath.
 * This is equivalent to calling
 * node.set_pos_hpr(smooth_mover->get_smooth_pos(),
 * smooth_mover->get_smooth_hpr()).  It exists as an optimization only, to
 * avoid the overhead of passing the return value through Python.
 */ 3 4 this 3 117  8 pos_node 1 132  8 hpr_node 1 132  
30 0 0 4 27 131 0 0 258 /**
 * Applies the smoothed orientation to the indicated NodePath.  This is
 * equivalent to calling node.set_hpr(smooth_mover->get_smooth_hpr()).  It
 * exists as an optimization only, to avoid the overhead of passing the return
 * value through Python.
 */ 2 4 this 3 117  4 node 1 132  
31 0 0 4 28 131 0 0 145 /**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node in one call.
 */ 2 4 this 3 116  4 node 1 132  
32 0 0 4 29 131 0 0 212 /**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node or nodes in one call.  The
 * pos_node and hpr_node might be the same NodePath.
 */ 3 4 this 3 116  8 pos_node 1 132  8 hpr_node 1 132  
33 0 0 4 30 131 0 0 212 /**
 * A further optimization to reduce Python calls.  This computes the smooth
 * position and applies it to the indicated node or nodes in one call.  The
 * pos_node and hpr_node might be the same NodePath.
 */ 2 4 this 3 116  8 hpr_node 1 132  
34 0 0 6 31 124 0 0 263 /**
 * Returns the speed at which the avatar is moving, in feet per second, along
 * its own forward axis (after applying the avatar's hpr).  This will be a
 * positive number if the avatar is moving forward, and a negative number if
 * it is moving backward.
 */ 1 4 this 3 117  
35 0 0 6 32 124 0 0 257 /**
 * Returns the speed at which the avatar is moving, in feet per second, along
 * its own lateral axis (after applying the avatar's hpr).  This will be a
 * positive number if the avatar is moving right, and a negative number if it
 * is moving left.
 */ 1 4 this 3 117  
36 0 0 6 33 124 0 0 205 /**
 * Returns the speed at which the avatar is rotating in the horizontal plane
 * (i.e.  heading), in degrees per second.  This may be positive or negative,
 * according to the direction of rotation.
 */ 1 4 this 3 117  
37 0 0 6 34 119 0 0 104 /**
 * Returns the smoothed position as computed by a previous call to
 * compute_smooth_position().
 */ 1 4 this 3 117  
38 0 0 4 35 131 0 0 97 /**
 * Node is being wrtReparented, update recorded sample positions to reflect
 * new parent
 */ 3 4 this 3 116  10 old_parent 1 132  10 new_parent 1 132  
39 0 0 4 38 131 0 0 222 /**
 * Sets the smoothing mode of all SmoothMovers in the world.  If this is
 * SM_off, no smoothing or prediction will be performed, and get_smooth_pos()
 * will simply return the position last set by mark_position().
 */ 2 4 this 3 116  4 mode 1 114  
40 0 0 6 39 114 0 0 98 /**
 * Returns the smoothing mode of all SmoothMovers in the world.  See
 * set_smooth_mode().
 */ 1 4 this 3 116  
41 0 0 4 40 131 0 0 171 /**
 * Sets the predictioning mode of all SmoothMovers in the world.  If this is
 * PM_off, no prediction will be performed, but smoothing might still be
 * performed.
 */ 2 4 this 3 116  4 mode 1 115  
42 0 0 6 41 115 0 0 106 /**
 * Returns the predictioning mode of all SmoothMovers in the world.  See
 * set_prediction_mode().
 */ 1 4 this 3 116  
43 0 0 4 42 131 0 0 349 /**
 * Sets the amount of time, in seconds, to delay the computed position of a
 * SmoothMover.  This is particularly useful when the prediction mode is off,
 * because it can allow the apparent motion of an avatar to appear smooth
 * without relying on prediction, at the cost of introducing additional lag in
 * the avatar's apparent position.
 */ 2 4 this 3 116  5 delay 1 130  
44 0 0 6 43 130 0 0 120 /**
 * Returns the amount of time, in seconds, to delay the computed position of a
 * SmoothMover.  See set_delay().
 */ 1 4 this 3 116  
45 0 0 4 44 131 0 0 585 /**
 * Sets the 'accept clock skew' flag.  When this flag is true, clock skew from
 * the other clients will be tolerated by delaying each smooth mover's
 * position an additional amount, on top of that specified by set_delay(),
 * based on the measured average latency for timestamp messages received by
 * the client.
 *
 * In this way, if the other client has significant clock skew with respect to
 * our clock, it will be evident as a large positive or negative average
 * latency for timestamps.  By subtracting out this average latency, we
 * compensate for poor clock sync.
 */ 2 4 this 3 116  4 flag 1 123  
46 0 0 6 45 123 0 0 102 /**
 * Returns the current state of the 'accept clock skew' flag.  See
 * set_accept_clock_skew().
 */ 1 4 this 3 116  
47 0 0 4 46 131 0 0 153 /**
 * Sets the maximum amount of time a position is allowed to remain unchanged
 * before assuming it represents the avatar actually standing still.
 */ 2 4 this 3 116  3 age 1 130  
48 0 0 6 47 130 0 0 156 /**
 * Returns the maximum amount of time a position is allowed to remain
 * unchanged before assuming it represents the avatar actually standing still.
 */ 1 4 this 3 116  
49 0 0 4 48 131 0 0 242 /**
 * Sets the interval at which we expect the SmoothNodes to broadcast their
 * position, in elapsed seconds.  This controls the length of time we assume
 * the object has truly stopped, when we receive a long sequence of no
 * updates.
 */ 2 4 this 3 116  6 period 1 130  
50 0 0 6 49 130 0 0 156 /**
 * Returns the interval at which we expect the SmoothNodes to broadcast their
 * position, in elapsed seconds.  See set_expected_broadcast_period().
 */ 1 4 this 3 116  
51 0 0 4 50 131 0 0 302 /**
 * Sets the amount of time that should elapse after the last position report
 * before the velocity is reset to 0.  This is similar to max_position_age,
 * but it is only used to determine the resetting of the reported velocity.
 * It should always be greater than or equal to max_position_age.
 */ 2 4 this 3 116  3 age 1 130  
52 0 0 6 51 130 0 0 156 /**
 * Returns the amount of time that should elapse after the last position
 * report before the velocity is reset to 0.  See set_reset_velocity_age().
 */ 1 4 this 3 116  
53 0 0 4 52 131 0 0 357 /**
 * Sets the flag that indicates whether the avatar's direction is considered
 * in computing the velocity.  When this is true, velocity is automatically
 * decomposed into a forward and a lateral velocity (and both may be positive
 * or negative); when it is false, all velocity is always returned as forward
 * velocity (and it is always positive).
 */ 2 4 this 3 116  4 flag 1 123  
54 0 0 6 53 123 0 0 108 /**
 * Returns the current state of the 'directional velocity' flag.  See
 * set_directional_velocity().
 */ 1 4 this 3 116  
55 0 0 4 54 131 0 0 295 /**
 * Sets the flag that indicates whether to assume that the node stopped moving
 * during periods when we don't get enough position updates.  If true, the
 * object will stand still momentarily.  If false, the object will
 * continuously lerp between the position updates that we did get.
 */ 2 4 this 3 116  4 flag 1 123  
56 0 0 6 55 123 0 0 118 /**
 * Returns the current state of the 'default to standing still' flag.  See
 * set_default_to_standing_still().
 */ 1 4 this 3 116  
57 0 0 4 56 131 0 0 10 /**
 *
 */ 2 4 this 3 117  3 out 1 134  
58 0 0 4 57 131 0 0 10 /**
 *
 */ 2 4 this 3 117  3 out 1 134  
23
113 11 SmoothMover 0 26625 11 SmoothMover 11 SmoothMover 0 0 0 1 59 60 0 52 61 62 63 64 65 66 67 68 69 70 71 72 73 74 75 76 77 78 79 80 81 82 83 84 85 86 87 88 89 90 91 92 93 94 95 96 97 98 99 100 101 102 103 104 105 106 107 108 109 110 111 112 0 0 0 0 2 114 115 702
/**
 * This class handles smoothing of sampled motion points over time, e.g.  for
 * smoothing the apparent movement of remote avatars, whose positions are sent
 * via occasional telemetry updates.
 *
 * It can operate in any of three modes: off, in which it does not smooth any
 * motion but provides the last position it was told; smoothing only, in which
 * it smooths motion information but never tries to anticipate where the
 * avatar might be going; or full prediction, in which it smooths motion as
 * well as tries to predict the avatar's position in lead of the last position
 * update.  The assumption is that all SmoothMovers in the world will be
 * operating in the same mode together.
 */

114 10 SmoothMode 0 794624 23 SmoothMover::SmoothMode 23 SmoothMover::SmoothMode 113 0 0 0 0 0 0 0 0 0 2 6 SM_off 19 SmoothMover::SM_off 0
0 5 SM_on 18 SmoothMover::SM_on 0
1 0 0

115 14 PredictionMode 0 794624 27 SmoothMover::PredictionMode 27 SmoothMover::PredictionMode 113 0 0 0 0 0 0 0 0 0 2 6 PM_off 19 SmoothMover::PM_off 0
0 5 PM_on 18 SmoothMover::PM_on 0
1 0 0

116 13 SmoothMover * 0 8576 13 SmoothMover * 13 SmoothMover * 0 0 113 0 0 0 0 0 0 0 0 0 0

117 19 SmoothMover const * 0 8576 19 SmoothMover const * 19 SmoothMover const * 0 0 118 0 0 0 0 0 0 0 0 0 0

118 17 SmoothMover const 0 8832 17 SmoothMover const 17 SmoothMover const 0 0 113 0 0 0 0 0 0 0 0 0 0

119 17 LVecBase3 const * 0 8576 17 LVecBase3 const * 17 LVecBase3 const * 0 0 120 0 0 0 0 0 0 0 0 0 0

120 15 LVecBase3 const 0 8832 15 LVecBase3 const 15 LVecBase3 const 0 0 121 0 0 0 0 0 0 0 0 0 0

121 9 LVecBase3 0 2105344 9 LVecBase3 9 LVecBase3 0 0 122 0 0 0 0 0 0 0 0 0 0

122 10 LVecBase3f 0 2048 10 LVecBase3f 10 LVecBase3f 0 0 0 0 0 0 0 0 0 0 0 0 77
/**
 * This is the base class for all three-component vectors and points.
 */

123 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

124 11 PN_stdfloat 0 2105344 11 PN_stdfloat 11 PN_stdfloat 0 0 125 0 0 0 0 0 0 0 0 0 0

125 5 float 0 8194 5 float 5 float 0 2 0 0 0 0 0 0 0 0 0 0 0

126 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 127 0 0 0 0 0 0 0 0 0 0

127 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 128 0 0 0 0 0 0 0 0 0 0

128 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 129 0 0 0 0 0 0 0 0 0 0

129 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

130 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

131 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

132 10 NodePath * 0 8576 10 NodePath * 10 NodePath * 0 0 133 0 0 0 0 0 0 0 0 0 0

133 8 NodePath 0 2048 8 NodePath 8 NodePath 0 0 0 0 0 0 0 0 0 0 0 0 762
/**
 * NodePath is the fundamental system for disambiguating instances, and also
 * provides a higher-level interface for manipulating the scene graph.
 *
 * A NodePath is a list of connected nodes from the root of the graph to any
 * sub-node.  Each NodePath therefore uniquely describes one instance of a
 * node.
 *
 * NodePaths themselves are lightweight objects that may easily be copied and
 * passed by value.  Their data is stored as a series of NodePathComponents
 * that are stored on the nodes.  Holding a NodePath will keep a reference
 * count to all the nodes in the path.  However, if any node in the path is
 * removed or reparented (perhaps through a different NodePath), the NodePath
 * will automatically be updated to reflect the changes.
 */

134 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 135 0 0 0 0 0 0 0 0 0 0

135 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

0
0
0
