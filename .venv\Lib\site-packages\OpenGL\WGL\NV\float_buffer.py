'''OpenGL extension NV.float_buffer

This module customises the behaviour of the 
OpenGL.raw.WGL.NV.float_buffer to provide a more 
Python-friendly API

Overview (from the spec)
	
	This extension builds upon NV_fragment_program to provide a framebuffer
	and texture format that allows fragment programs to read and write
	unconstrained floating point data.
	
	In unextended OpenGL, most computations dealing with color or depth
	buffers are typically constrained to operate on values in the range [0,1].
	Computational results are also typically clamped to the range [0,1].
	Color, texture, and depth buffers themselves also hold values mapped to
	the range [0,1].
	
	The NV_fragment_program extension provides a general computational model
	that supports floating-point numbers constrained only by the precision of
	the underlying data types.  The quantites computed by fragment programs do
	not necessarily correspond in number or in range to conventional
	attributes such as RGBA colors or depth values.  Because of the range and
	precision constraints imposed by conventional fixed-point color buffers,
	it may be difficult (if not impossible) to use them to implement certain
	multi-pass algorithms.
	
	To enhance the extended range and precision available through fragment
	programs, this extension provides floating-point RGBA color buffers that
	can be used instead of conventional fixed-point RGBA color buffers.  A
	floating-point RGBA color buffer consists of one to four floating-point
	components stored in the 16- or 32-bit floating-point formats (fp16 or
	fp32) defined in the NV_half_float and NV_fragment_program extensions.
	
	When a floating-point color buffer is used, the results of fragment
	programs, as written to the "x", "y", "z", and "w" components of the
	o[COLR] or o[COLH] output registers, are written directly to the color
	buffer without any clamping or modification.  Certain per-fragment
	operations are bypassed when rendering to floating-point color buffers.
	
	A floating-point color buffer can also be used as a texture map, either by
	reading back the contents and then using conventional TexImage calls, or
	by using the buffer directly via the ARB_render_texture extension or
	the EXT_framebuffer_object extension.
	
	This extension has many uses.  Some possible uses include:
	
	    (1) Multi-pass algorithms with arbitrary intermediate results that
	        don't have to be artifically forced into the range [0,1].  In
	        addition, intermediate results can be written without having to
	        worry about out-of-range values.
	
	    (2) Deferred shading algorithms where an expensive fragment program is
	        executed only after depth testing is fully complete.  Instead, a
	        simple program is executed, which stores the parameters necessary
	        to produce a final result.  After the entire scene is rendered, a
	        second pass is executed over the entire frame buffer to execute
	        the complex fragment program using the results written to the
	        floating-point color buffer in the first pass.  This will save the
	        cost of applying complex fragment programs to fragments that will
	        not appear in the final image.
	
	    (3) Use floating-point texture maps to evaluate functions with
	        arbitrary ranges.  Arbitrary functions with a finite domain can be
	        approximated using a texture map holding sample results and
	        piecewise linear approximation.
	
	There are several significant limitations on the use of floating-point
	color buffers.  First, floating-point color buffers do not support frame
	buffer blending.  Second, floating-point texture maps do not support
	mipmapping or any texture filtering other than NEAREST.  Third,
	floating-point texture maps must be 2D, and must use the
	NV_texture_rectangle extension.

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/NV/float_buffer.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.NV.float_buffer import *
from OpenGL.raw.WGL.NV.float_buffer import _EXTENSION_NAME

def glInitFloatBufferNV():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION