'''OpenGL extension ARB.extensions_string

This module customises the behaviour of the 
OpenGL.raw.WGL.ARB.extensions_string to provide a more 
Python-friendly API

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/ARB/extensions_string.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.ARB.extensions_string import *
from OpenGL.raw.WGL.ARB.extensions_string import _EXTENSION_NAME

def glInitExtensionsStringARB():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION