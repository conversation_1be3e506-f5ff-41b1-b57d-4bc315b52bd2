'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_delay_before_swap'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_delay_before_swap',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.Bool,ctypes.POINTER(_cs.Display),_cs.GLXDrawable,_cs.GLfloat)
def glXDelayBeforeSwapNV(dpy,drawable,seconds):pass
