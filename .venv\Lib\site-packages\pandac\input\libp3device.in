1730992849
3 3
11 libp3device 4 k0F5 12 panda3d.core 
135
102 8 get_time 0 4 237 21 TrackerData::get_time 0 0 180
/**
 * Returns the time at which the position information (pos and orient) are
 * effective.  It is an error to call this if has_time() does not return true.
 * See set_time().
 */
48
inline double TrackerData::get_time(void) const;

103 8 set_time 0 4 237 21 TrackerData::set_time 0 0 251
/**
 * Indicates the time at which the position information (pos and orient) are
 * effective.  This is a time elapsed in seconds since some undefined epoch;
 * it may or may not correspond to the clock time indicated in the global
 * ClockObject.
 */
47
inline void TrackerData::set_time(double time);

104 7 get_pos 0 4 237 20 TrackerData::get_pos 0 0 168
/**
 * Returns the current position of the tracker.  It is legal to call this if
 * has_pos() returns false; in this case, the position will always be (0, 0,
 * 0).
 */
55
inline LPoint3 const &TrackerData::get_pos(void) const;

105 7 set_pos 0 4 237 20 TrackerData::set_pos 0 0 143
/**
 * Indicates the current position of the tracker sensor in space.  The
 * coordinate system of this position is defined by the tracker.
 */
53
inline void TrackerData::set_pos(LPoint3 const &pos);

106 10 get_orient 0 4 237 23 TrackerData::get_orient 0 0 182
/**
 * Returns the current orientation of the tracker.  It is legal to call this
 * if has_orient() returns false; in this case, the result is always the
 * identity orientation.
 */
63
inline LOrientation const &TrackerData::get_orient(void) const;

107 10 set_orient 0 4 237 23 TrackerData::set_orient 0 0 225
/**
 * Indicates the current orientation of the tracker sensor in space.  The
 * coordinate system of this orientation is defined by the tracker, but should
 * be the same coordinate system as that reflected by set_pos().
 */
64
inline void TrackerData::set_orient(LOrientation const &orient);

108 6 get_dt 0 4 237 19 TrackerData::get_dt 0 0 188
/**
 * Returns the amount of elapsed time over which the information (pos and
 * orient) were computed.  It is an error to call this if has_dt() does not
 * return true.  See set_dt().
 */
46
inline double TrackerData::get_dt(void) const;

109 6 set_dt 0 4 237 19 TrackerData::set_dt 0 0 259
/**
 * Indicates the amount of elapsed time over which which the information (pos
 * and orient) were computed.  This only makes sense if the information
 * represents velocity or acceleration, rather than position.  This is an
 * elapsed time in seconds.
 */
43
inline void TrackerData::set_dt(double dt);

110 12 ~TrackerData 0 516 237 25 TrackerData::~TrackerData 0 0 0
32
TrackerData::~TrackerData(void);

111 22 operator typecast bool 0 132 251 48 InputDevice::ButtonState::operator typecast bool 0 1 21 0
21
operator bool (void);

112 8 is_known 0 4 251 34 InputDevice::ButtonState::is_known 0 0 55
/**
 * True if the button state is currently known.
 */
59
inline bool InputDevice::ButtonState::is_known(void) const;

113 10 is_pressed 0 4 251 36 InputDevice::ButtonState::is_pressed 0 0 63
/**
 * True if the button is currently known to be pressed.
 */
61
inline bool InputDevice::ButtonState::is_pressed(void) const;

114 10 get_handle 0 20 251 36 InputDevice::ButtonState::get_handle 0 0 57
getter for ButtonHandle InputDevice::ButtonState::handle;
62
ButtonHandle InputDevice::ButtonState::get_handle(void) const;

115 11 ButtonState 0 260 251 37 InputDevice::ButtonState::ButtonState 0 1 20 10
/**
 *
 */
89
inline InputDevice::ButtonState::ButtonState(InputDevice::ButtonState const &) = default;

116 12 ~ButtonState 0 516 251 38 InputDevice::ButtonState::~ButtonState 0 0 0
45
InputDevice::ButtonState::~ButtonState(void);

117 22 operator typecast bool 0 132 254 46 InputDevice::AxisState::operator typecast bool 0 1 23 0
21
operator bool (void);

118 8 get_axis 0 20 254 32 InputDevice::AxisState::get_axis 0 0 58
getter for InputDevice::Axis InputDevice::AxisState::axis;
63
InputDevice::Axis InputDevice::AxisState::get_axis(void) const;

119 8 set_axis 0 36 254 32 InputDevice::AxisState::set_axis 0 0 58
setter for InputDevice::Axis InputDevice::AxisState::axis;
63
void InputDevice::AxisState::set_axis(InputDevice::Axis value);

120 9 get_value 0 20 254 33 InputDevice::AxisState::get_value 0 0 48
getter for double InputDevice::AxisState::value;
53
double InputDevice::AxisState::get_value(void) const;

121 9 set_value 0 36 254 33 InputDevice::AxisState::set_value 0 0 48
setter for double InputDevice::AxisState::value;
53
void InputDevice::AxisState::set_value(double value);

122 9 get_known 0 20 254 33 InputDevice::AxisState::get_known 0 0 46
getter for bool InputDevice::AxisState::known;
51
bool InputDevice::AxisState::get_known(void) const;

123 9 set_known 0 36 254 33 InputDevice::AxisState::set_known 0 0 46
setter for bool InputDevice::AxisState::known;
51
void InputDevice::AxisState::set_known(bool value);

124 9 AxisState 0 260 254 33 InputDevice::AxisState::AxisState 0 1 22 0
83
inline InputDevice::AxisState::AxisState(InputDevice::AxisState const &) = default;

125 10 ~AxisState 0 516 254 34 InputDevice::AxisState::~AxisState 0 0 0
41
InputDevice::AxisState::~AxisState(void);

126 9 get_level 0 20 255 35 InputDevice::BatteryData::get_level 0 0 53
getter for short int InputDevice::BatteryData::level;
58
short int InputDevice::BatteryData::get_level(void) const;

127 9 set_level 0 36 255 35 InputDevice::BatteryData::set_level 0 0 53
setter for short int InputDevice::BatteryData::level;
58
void InputDevice::BatteryData::set_level(short int value);

128 13 get_max_level 0 20 255 39 InputDevice::BatteryData::get_max_level 0 0 57
getter for short int InputDevice::BatteryData::max_level;
62
short int InputDevice::BatteryData::get_max_level(void) const;

129 13 set_max_level 0 36 255 39 InputDevice::BatteryData::set_max_level 0 0 57
setter for short int InputDevice::BatteryData::max_level;
62
void InputDevice::BatteryData::set_max_level(short int value);

130 11 BatteryData 0 260 255 37 InputDevice::BatteryData::BatteryData 0 2 24 25 0
151
inline InputDevice::BatteryData::BatteryData(void) = default;
inline InputDevice::BatteryData::BatteryData(InputDevice::BatteryData const &) = default;

131 12 ~BatteryData 0 516 255 38 InputDevice::BatteryData::~BatteryData 0 0 0
45
InputDevice::BatteryData::~BatteryData(void);

132 8 get_name 0 4 245 21 InputDevice::get_name 0 0 81
/**
 * Returns a human-readable name for the device.  Not necessarily unique.
 */
53
inline std::string InputDevice::get_name(void) const;

133 16 get_manufacturer 0 4 245 29 InputDevice::get_manufacturer 0 0 103
/**
 * Returns a string containing the manufacturer of the device, if this
 * information is known.
 */
61
inline std::string InputDevice::get_manufacturer(void) const;

134 17 get_serial_number 0 4 245 30 InputDevice::get_serial_number 0 0 104
/**
 * Returns a string containing the serial number of the device, if this
 * information is known.
 */
62
inline std::string InputDevice::get_serial_number(void) const;

135 13 get_vendor_id 0 4 245 26 InputDevice::get_vendor_id 0 0 104
/**
 * Returns a string containing the USB vendor ID of the device, if this
 * information is known.
 */
65
inline unsigned short int InputDevice::get_vendor_id(void) const;

136 14 get_product_id 0 4 245 27 InputDevice::get_product_id 0 0 105
/**
 * Returns a string containing the USB product ID of the device, if this
 * information is known.
 */
66
inline unsigned short int InputDevice::get_product_id(void) const;

137 12 is_connected 0 4 245 25 InputDevice::is_connected 0 0 131
/**
 * Returns true if the device is still connected and able to receive data,
 * false otherwise.  May return false positives.
 */
50
inline bool InputDevice::is_connected(void) const;

138 16 get_device_class 0 4 245 29 InputDevice::get_device_class 0 0 133
/**
 * Returns an identification of the general type of device.  If this could not
 * be determined, returns DeviceClass.unknown.
 */
74
inline InputDevice::DeviceClass InputDevice::get_device_class(void) const;

139 11 has_feature 0 4 245 24 InputDevice::has_feature 0 1 1 102
// Determine supported features

/**
 * Returns true if the device supports the indicated feature.
 */
73
inline bool InputDevice::has_feature(InputDevice::Feature feature) const;

140 11 get_tracker 0 4 245 24 InputDevice::get_tracker 0 0 140
/**
 * Returns the TrackerData associated with the input device's tracker.  This
 * only makes sense if has_tracker() also returns true.
 */
56
inline TrackerData InputDevice::get_tracker(void) const;

141 11 has_tracker 0 4 245 24 InputDevice::has_tracker 0 1 16 115
/**
 * Returns true if the device features a tracker that can track position and/or
 * orientation in 3D space.
 */
49
inline bool InputDevice::has_tracker(void) const;

142 11 get_battery 0 4 245 24 InputDevice::get_battery 0 0 138
/**
 * Returns a rough indication of the battery level, ranging from 0 (completely
 * empty battery) to the indicated max_level value.
 */
69
inline InputDevice::BatteryData InputDevice::get_battery(void) const;

143 11 has_battery 0 4 245 24 InputDevice::has_battery 0 1 17 99
/**
 * Returns true if the device may be able to provide information about its
 * battery life.
 */
49
inline bool InputDevice::has_battery(void) const;

144 15 get_num_buttons 0 4 245 28 InputDevice::get_num_buttons 0 1 18 279
/**
 * Returns the number of buttons known to the device.  This includes those
 * buttons whose state has been seen, as well as buttons that have been
 * associated with a ButtonHandle even if their state is unknown.  This number
 * may change as more buttons are discovered.
 */
60
inline std::size_t InputDevice::get_num_buttons(void) const;

145 10 get_button 0 4 245 23 InputDevice::get_button 0 0 128
/**
 * Returns the ButtonState that is set at the given index, or throw an assert
 * if the index was not found in the list.
 */
81
inline InputDevice::ButtonState InputDevice::get_button(std::size_t index) const;

146 12 get_num_axes 0 4 245 25 InputDevice::get_num_axes 0 1 19 126
/**
 * Returns the number of analog axes known to the InputDevice.  This number
 * may change as more axes are discovered.
 */
57
inline std::size_t InputDevice::get_num_axes(void) const;

147 8 get_axis 0 4 245 21 InputDevice::get_axis 0 0 127
/**
 * Returns the axis state that is set at the given index, or throw an assert
 * if the index was not found in the list.
 */
77
inline InputDevice::AxisState InputDevice::get_axis(std::size_t index) const;

148 10 map_button 0 4 245 23 InputDevice::map_button 0 1 2 540
// Associate buttons/axes with symbolic handles.

/**
 * Associates the indicated ButtonHandle with the button of the indicated index
 * number.  When the given button index changes state, a corresponding
 * ButtonEvent will be generated with the given ButtonHandle.  Pass
 * ButtonHandle::none() to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various buttons by index number; this is only necessary in order to
 * generate ButtonEvents when the buttons change state.
 */
76
inline void InputDevice::map_button(std::size_t index, ButtonHandle handle);

149 8 map_axis 0 4 245 21 InputDevice::map_axis 0 1 3 251
/**
 * Associates the indicated Axis with the axis of the indicated index
 * number.  Pass Axis::none to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various axes by index number.
 */
77
inline void InputDevice::map_axis(std::size_t index, InputDevice::Axis axis);

150 11 find_button 0 4 245 24 InputDevice::find_button 0 1 4 137
/**
 * Returns the first ButtonState found with the given axis, or throw an assert
 * if the button handle was not found in the list.
 */
84
inline InputDevice::ButtonState InputDevice::find_button(ButtonHandle handle) const;

151 9 find_axis 0 4 245 22 InputDevice::find_axis 0 1 5 127
/**
 * Returns the first AnalogAxis found with the given axis, or throw an assert
 * if the axis was not found in the list.
 */
83
inline InputDevice::AxisState InputDevice::find_axis(InputDevice::Axis axis) const;

152 13 set_vibration 0 4 245 26 InputDevice::set_vibration 0 1 6 267
// Enable rumble force-feedback effects

/**
 * Sets the strength of the vibration effect, if supported.  The values are
 * clamped to 0-1 range. The first value axes the low-frequency rumble
 * motor, whereas the second axes the high-frequency motor, if present.
 */
67
inline void InputDevice::set_vibration(double strong, double weak);

153 21 enable_pointer_events 0 4 245 34 InputDevice::enable_pointer_events 0 1 7 59
/**
 * Enables the generation of mouse-movement events.
 */
53
inline void InputDevice::enable_pointer_events(void);

154 22 disable_pointer_events 0 4 245 35 InputDevice::disable_pointer_events 0 1 8 60
/**
 * Disables the generation of mouse-movement events.
 */
54
inline void InputDevice::disable_pointer_events(void);

155 4 poll 0 4 245 17 InputDevice::poll 0 1 9 233
/**
 * Polls the input device for new activity, to ensure it contains the latest
 * events.  This will only have any effect for some types of input devices;
 * others may be updated automatically, and this method will be a no-op.
 */
29
void InputDevice::poll(void);

156 16 has_button_event 0 4 245 29 InputDevice::has_button_event 0 1 10 218
/**
 * Returns true if this device has a pending button event (a mouse button or
 * keyboard button down/up), false otherwise.  If this returns true, the
 * particular event may be extracted via get_button_event().
 */
47
bool InputDevice::has_button_event(void) const;

157 17 get_button_events 0 4 245 30 InputDevice::get_button_events 0 1 11 92
/**
 * Returns the list of recently-generated ButtonEvents.
 * The list is also cleared.
 */
66
PointerTo< ButtonEventList > InputDevice::get_button_events(void);

158 17 has_pointer_event 0 4 245 30 InputDevice::has_pointer_event 0 1 12 198
/**
 * Returns true if this device has a pending pointer event (a mouse movement),
 * or false otherwise.  If this returns true, the particular event may be
 * extracted via get_pointer_event().
 */
48
bool InputDevice::has_pointer_event(void) const;

159 18 get_pointer_events 0 4 245 31 InputDevice::get_pointer_events 0 1 13 99
/**
 * Returns a PointerEventList containing all the recent pointer events.
 * Clears the list.
 */
68
PointerTo< PointerEventList > InputDevice::get_pointer_events(void);

160 6 output 0 6 245 19 InputDevice::output 0 1 14 58
/**
 * Writes a one-line string describing the device.
 */
58
virtual void InputDevice::output(std::ostream &out) const;

161 11 get_buttons 0 20 245 24 InputDevice::get_buttons 0 0 54
getter for InputDevice::Buttons InputDevice::_buttons;
58
InputDevice::Buttons InputDevice::get_buttons(void) const;

162 8 get_axes 0 20 245 21 InputDevice::get_axes 0 0 48
getter for InputDevice::Axes InputDevice::_axes;
52
InputDevice::Axes InputDevice::get_axes(void) const;

163 16 get_pointer_data 0 20 245 29 InputDevice::get_pointer_data 0 0 50
getter for PointerData InputDevice::_pointer_data;
61
PointerData const &InputDevice::get_pointer_data(void) const;

164 16 get_battery_data 0 20 245 29 InputDevice::get_battery_data 0 0 63
getter for InputDevice::BatteryData InputDevice::_battery_data;
67
InputDevice::BatteryData InputDevice::get_battery_data(void) const;

165 16 get_tracker_data 0 20 245 29 InputDevice::get_tracker_data 0 0 50
getter for TrackerData InputDevice::_tracker_data;
61
TrackerData const &InputDevice::get_tracker_data(void) const;

166 14 get_class_type 0 4 245 27 InputDevice::get_class_type 0 1 15 0
52
static TypeHandle InputDevice::get_class_type(void);

167 24 fork_asynchronous_thread 0 4 265 36 ClientBase::fork_asynchronous_thread 0 1 26 314
/**
 * Forks a separate thread to do all the polling of connected devices.  The
 * forked thread will poll after every poll_time seconds has elapsed.  Returns
 * true if the fork was successful, or false otherwise (for instance, because
 * we were already forked, or because asynchronous threads are disabled).
 */
60
bool ClientBase::fork_asynchronous_thread(double poll_time);

168 9 is_forked 0 4 265 21 ClientBase::is_forked 0 1 27 130
/**
 * Returns true if the ClientBase has been forked (and, therefore, poll() does
 * not need to be called), false otherwise.
 */
46
inline bool ClientBase::is_forked(void) const;

169 4 poll 0 4 265 16 ClientBase::poll 0 1 28 183
/**
 * Initiates a poll of the client devices, if we are not forked and if we have
 * not already polled this frame.  Returns true if the poll occurred, or false
 * if it did not.
 */
35
inline bool ClientBase::poll(void);

170 18 get_last_poll_time 0 4 265 30 ClientBase::get_last_poll_time 0 1 29 118
/**
 * Returns the time (according to the global ClockObject's get_real_time()
 * method) of the last device poll.
 */
57
inline double ClientBase::get_last_poll_time(void) const;

171 21 set_coordinate_system 0 4 265 33 ClientBase::set_coordinate_system 0 1 30 138
/**
 * Specifies the coordinate system that all devices associated with this
 * client will operate in.  Normally, this is CS_default.
 */
67
inline void ClientBase::set_coordinate_system(CoordinateSystem cs);

172 21 get_coordinate_system 0 4 265 33 ClientBase::get_coordinate_system 0 1 31 136
/**
 * Returns the coordinate system that all devices associated with this client
 * will operate in.  Normally, this is CS_default.
 */
70
inline CoordinateSystem ClientBase::get_coordinate_system(void) const;

173 14 get_class_type 0 4 265 26 ClientBase::get_class_type 0 1 32 0
51
static TypeHandle ClientBase::get_class_type(void);

174 10 AnalogNode 0 260 266 22 AnalogNode::AnalogNode 0 3 33 34 35 22
/**
 *
 */

/**
 *
 */
199
explicit AnalogNode::AnalogNode(ClientBase *client, std::string const &device_name);
explicit AnalogNode::AnalogNode(InputDevice *device);
inline AnalogNode::AnalogNode(AnalogNode const &) = default;

175 8 is_valid 0 4 266 20 AnalogNode::is_valid 0 1 36 97
/**
 * Returns true if the AnalogNode is valid and connected to a server, false
 * otherwise.
 */
45
inline bool AnalogNode::is_valid(void) const;

176 16 get_num_controls 0 4 266 28 AnalogNode::get_num_controls 0 1 37 133
/**
 * Returns the number of analog controls known to the AnalogNode.  This number
 * may change as more controls are discovered.
 */
52
inline int AnalogNode::get_num_controls(void) const;

177 17 get_control_state 0 4 266 29 AnalogNode::get_control_state 0 1 38 193
/**
 * Returns the current position of indicated analog control identified by its
 * index number, or 0.0 if the control is unknown.  The normal range of a
 * single control is -1.0 to 1.0.
 */
61
inline double AnalogNode::get_control_state(int index) const;

178 16 is_control_known 0 4 266 28 AnalogNode::is_control_known 0 1 39 153
/**
 * Returns true if the state of the indicated analog control is known, or
 * false if we have never heard anything about this particular control.
 */
58
inline bool AnalogNode::is_control_known(int index) const;

179 10 set_output 0 4 266 22 AnalogNode::set_output 0 1 40 314
/**
 * Causes a particular analog control to be placed in the data graph for the
 * indicated channel.  Normally, a mouse uses channels 0 and 1 for the X and Y
 * information, respectively; channels 0, 1, and 2 are available.  If flip is
 * true, the analog control value will be reversed before outputting it.
 */
70
inline void AnalogNode::set_output(int channel, int index, bool flip);

180 12 clear_output 0 4 266 24 AnalogNode::clear_output 0 1 41 107
/**
 * Removes the output to the data graph associated with the indicated channel.
 * See set_output().
 */
50
inline void AnalogNode::clear_output(int channel);

181 10 get_output 0 4 266 22 AnalogNode::get_output 0 1 42 174
/**
 * Returns the analog control index that is output to the data graph on the
 * indicated channel, or -1 if no control is output on that channel.  See
 * set_output().
 */
53
inline int AnalogNode::get_output(int channel) const;

182 17 is_output_flipped 0 4 266 29 AnalogNode::is_output_flipped 0 1 43 143
/**
 * Returns true if the analog control index that is output to the data graph
 * on the indicated channel is flipped.  See set_output().
 */
61
inline bool AnalogNode::is_output_flipped(int channel) const;

183 14 get_class_type 0 4 266 26 AnalogNode::get_class_type 0 1 44 0
51
static TypeHandle AnalogNode::get_class_type(void);

184 10 ButtonNode 0 260 268 22 ButtonNode::ButtonNode 0 3 45 46 47 22
/**
 *
 */

/**
 *
 */
199
explicit ButtonNode::ButtonNode(ClientBase *client, std::string const &device_name);
explicit ButtonNode::ButtonNode(InputDevice *device);
inline ButtonNode::ButtonNode(ButtonNode const &) = default;

185 8 is_valid 0 4 268 20 ButtonNode::is_valid 0 1 48 97
/**
 * Returns true if the ButtonNode is valid and connected to a server, false
 * otherwise.
 */
45
inline bool ButtonNode::is_valid(void) const;

186 15 get_num_buttons 0 4 268 27 ButtonNode::get_num_buttons 0 1 49 283
/**
 * Returns the number of buttons known to the ButtonNode.  This includes those
 * buttons whose state has been seen, as well as buttons that have been
 * associated with a ButtonHandle even if their state is unknown.  This number
 * may change as more buttons are discovered.
 */
51
inline int ButtonNode::get_num_buttons(void) const;

187 14 set_button_map 0 4 268 26 ButtonNode::set_button_map 0 1 50 490
/**
 * Associates the indicated ButtonHandle with the button of the indicated
 * index number.  When the given button index changes state, a corresponding
 * ButtonEvent will be generated with the given ButtonHandle.  Pass
 * ButtonHandle::none() to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various buttons by index number; this is only necessary in order to
 * generate ButtonEvents when the buttons change state.
 */
71
inline void ButtonNode::set_button_map(int index, ButtonHandle button);

188 14 get_button_map 0 4 268 26 ButtonNode::get_button_map 0 1 51 184
/**
 * Returns the ButtonHandle that was previously associated with the given
 * index number by a call to set_button_map(), or ButtonHandle::none() if no
 * button was associated.
 */
64
inline ButtonHandle ButtonNode::get_button_map(int index) const;

189 16 get_button_state 0 4 268 28 ButtonNode::get_button_state 0 1 52 147
/**
 * Returns true if the indicated button (identified by its index number) is
 * currently known to be down, or false if it is up or unknown.
 */
58
inline bool ButtonNode::get_button_state(int index) const;

190 15 is_button_known 0 4 268 27 ButtonNode::is_button_known 0 1 53 144
/**
 * Returns true if the state of the indicated button is known, or false if we
 * have never heard anything about this particular button.
 */
57
inline bool ButtonNode::is_button_known(int index) const;

191 14 get_class_type 0 4 268 26 ButtonNode::get_class_type 0 1 54 0
51
static TypeHandle ButtonNode::get_class_type(void);

192 8 DialNode 0 260 269 18 DialNode::DialNode 0 2 55 56 10
/**
 *
 */
135
explicit DialNode::DialNode(ClientBase *client, std::string const &device_name);
inline DialNode::DialNode(DialNode const &) = default;

193 8 is_valid 0 4 269 18 DialNode::is_valid 0 1 57 95
/**
 * Returns true if the DialNode is valid and connected to a server, false
 * otherwise.
 */
43
inline bool DialNode::is_valid(void) const;

194 13 get_num_dials 0 4 269 23 DialNode::get_num_dials 0 1 58 123
/**
 * Returns the number of dial dials known to the DialNode.  This number may
 * change as more dials are discovered.
 */
47
inline int DialNode::get_num_dials(void) const;

195 9 read_dial 0 4 269 19 DialNode::read_dial 0 1 59 216
/**
 * Returns the number of complete revolutions of the dial since the last time
 * read_dial() was called.  This is a destructive operation; it is not
 * possible to read the dial without resetting the counter.
 */
45
inline double DialNode::read_dial(int index);

196 13 is_dial_known 0 4 269 23 DialNode::is_dial_known 0 1 60 145
/**
 * Returns true if the state of the indicated dial dial is known, or false if
 * we have never heard anything about this particular dial.
 */
53
inline bool DialNode::is_dial_known(int index) const;

197 14 get_class_type 0 4 269 24 DialNode::get_class_type 0 1 61 0
49
static TypeHandle DialNode::get_class_type(void);

198 14 InputDeviceSet 0 260 270 30 InputDeviceSet::InputDeviceSet 0 2 62 63 22
/**
 *
 */

/**
 *
 */
97
InputDeviceSet::InputDeviceSet(void);
InputDeviceSet::InputDeviceSet(InputDeviceSet const &copy);

199 10 operator = 0 4 270 26 InputDeviceSet::operator = 0 1 64 0
60
void InputDeviceSet::operator =(InputDeviceSet const &copy);

200 15 ~InputDeviceSet 0 516 270 31 InputDeviceSet::~InputDeviceSet 0 0 10
/**
 *
 */
45
inline InputDeviceSet::~InputDeviceSet(void);

201 5 clear 0 4 270 21 InputDeviceSet::clear 0 1 65 56
/**
 * Removes all InputDevices from the collection.
 */
33
void InputDeviceSet::clear(void);

202 7 reserve 0 4 270 23 InputDeviceSet::reserve 0 1 66 160
/**
 * This is a hint to Panda to allocate enough memory to hold the given number
 * of InputDevices, if you know ahead of time how many you will be adding.
 */
46
void InputDeviceSet::reserve(std::size_t num);

203 11 operator [] 0 4 270 27 InputDeviceSet::operator [] 0 1 67 57
/**
 * Returns the nth InputDevice in the collection.
 */
73
inline InputDevice *InputDeviceSet::operator [](std::size_t index) const;

204 4 size 0 4 270 20 InputDeviceSet::size 0 1 68 59
/**
 * Returns the number of devices in the collection.
 */
52
inline std::size_t InputDeviceSet::size(void) const;

205 6 output 0 4 270 22 InputDeviceSet::output 0 1 69 103
/**
 * Writes a brief one-line description of the InputDeviceSet to the indicated
 * output stream.
 */
53
void InputDeviceSet::output(std::ostream &out) const;

206 5 write 0 4 270 21 InputDeviceSet::write 0 1 70 108
/**
 * Writes a complete multi-line description of the InputDeviceSet to the
 * indicated output stream.
 */
74
void InputDeviceSet::write(std::ostream &out, int indent_level = 0) const;

207 11 get_devices 0 4 271 31 InputDeviceManager::get_devices 0 2 71 72 156
/**
 * Description: Returns all currently connected devices.
 */

/**
 * Description: Returns all currently connected devices of the given device class.
 */
152
InputDeviceSet InputDeviceManager::get_devices(void) const;
InputDeviceSet InputDeviceManager::get_devices(InputDevice::DeviceClass device_class) const;

208 10 add_device 0 4 271 30 InputDeviceManager::add_device 0 1 73 166
/**
 * Called when a new device has been discovered.  This may also be used to
 * register virtual devices.
 *
 * This causes a connect-device event to be thrown.
 */
57
void InputDeviceManager::add_device(InputDevice *device);

209 13 remove_device 0 4 271 33 InputDeviceManager::remove_device 0 1 74 166
/**
 * Called when a device has been removed, or when a device should otherwise no
 * longer be tracked.
 *
 * This causes a disconnect-device event to be thrown.
 */
60
void InputDeviceManager::remove_device(InputDevice *device);

210 6 update 0 6 271 26 InputDeviceManager::update 0 1 75 109
/**
 * Polls the system to see if there are any new devices.  In some
 * implementations this is a no-op.
 */
46
virtual void InputDeviceManager::update(void);

211 14 get_global_ptr 0 4 271 34 InputDeviceManager::get_global_ptr 0 1 76 61
/**
 * Returns the singleton InputDeviceManager instance.
 */
75
static inline InputDeviceManager *InputDeviceManager::get_global_ptr(void);

212 15 InputDeviceNode 0 260 272 32 InputDeviceNode::InputDeviceNode 0 2 77 78 0
155
InputDeviceNode::InputDeviceNode(InputDevice *device, std::string const &name);
inline InputDeviceNode::InputDeviceNode(InputDeviceNode const &) = default;

213 10 get_device 0 4 272 27 InputDeviceNode::get_device 0 0 41
/**
 * Returns the associated device.
 */
65
PointerTo< InputDevice > InputDeviceNode::get_device(void) const;

214 10 set_device 0 4 272 27 InputDeviceNode::set_device 0 0 71
/**
 * Redirects the class to get the data from a different device.
 */
54
void InputDeviceNode::set_device(InputDevice *device);

215 14 get_class_type 0 4 272 31 InputDeviceNode::get_class_type 0 1 79 0
56
static TypeHandle InputDeviceNode::get_class_type(void);

216 16 ~InputDeviceNode 0 516 272 33 InputDeviceNode::~InputDeviceNode 0 0 0
40
InputDeviceNode::~InputDeviceNode(void);

217 11 TrackerNode 0 260 274 24 TrackerNode::TrackerNode 0 3 80 81 82 22
/**
 *
 */

/**
 *
 */
206
explicit TrackerNode::TrackerNode(ClientBase *client, std::string const &device_name);
explicit TrackerNode::TrackerNode(InputDevice *device);
inline TrackerNode::TrackerNode(TrackerNode const &) = default;

218 8 is_valid 0 4 274 21 TrackerNode::is_valid 0 1 83 98
/**
 * Returns true if the TrackerNode is valid and connected to a server, false
 * otherwise.
 */
46
inline bool TrackerNode::is_valid(void) const;

219 7 get_pos 0 4 274 20 TrackerNode::get_pos 0 1 84 75
/**
 * Returns the current position of the tracker, if it is available.
 */
55
inline LPoint3 const &TrackerNode::get_pos(void) const;

220 10 get_orient 0 4 274 23 TrackerNode::get_orient 0 1 85 78
/**
 * Returns the current orientation of the tracker, if it is available.
 */
63
inline LOrientation const &TrackerNode::get_orient(void) const;

221 13 get_transform 0 4 274 26 TrackerNode::get_transform 0 1 86 96
/**
 * Returns the current position and orientation of the tracker, as a combined
 * matrix.
 */
62
inline LMatrix4 const &TrackerNode::get_transform(void) const;

222 8 get_time 0 4 274 21 TrackerNode::get_time 0 1 87 57
/**
 * Returns the time of the tracker's last update.
 */
48
inline double TrackerNode::get_time(void) const;

223 8 has_time 0 4 274 21 TrackerNode::has_time 0 1 88 51
/**
 * True if this data comes with timestamps.
 */
46
inline bool TrackerNode::has_time(void) const;

224 29 set_tracker_coordinate_system 0 4 274 42 TrackerNode::set_tracker_coordinate_system 0 1 89 257
/**
 * Specifies the coordinate system that the tracker associated with this node
 * will operate in.  Normally, this is set from the ClientBase that's used to
 * create the TrackerNode, so it should not need to be set on an individual
 * tracker basis.
 */
76
inline void TrackerNode::set_tracker_coordinate_system(CoordinateSystem cs);

225 29 get_tracker_coordinate_system 0 4 274 42 TrackerNode::get_tracker_coordinate_system 0 1 90 103
/**
 * Returns the coordinate system that the tracker associated with this node
 * will operate in.
 */
79
inline CoordinateSystem TrackerNode::get_tracker_coordinate_system(void) const;

226 27 set_graph_coordinate_system 0 4 274 40 TrackerNode::set_graph_coordinate_system 0 1 91 165
/**
 * Specifies the coordinate system that the TrackerNode will convert its
 * transform into for passing down the data graph.  Normally, this is
 * CS_default.
 */
74
inline void TrackerNode::set_graph_coordinate_system(CoordinateSystem cs);

227 27 get_graph_coordinate_system 0 4 274 40 TrackerNode::get_graph_coordinate_system 0 1 92 163
/**
 * Returns the coordinate system that the TrackerNode will convert its
 * transform into for passing down the data graph.  Normally, this is
 * CS_default.
 */
77
inline CoordinateSystem TrackerNode::get_graph_coordinate_system(void) const;

228 14 get_class_type 0 4 274 27 TrackerNode::get_class_type 0 1 93 0
52
static TypeHandle TrackerNode::get_class_type(void);

229 12 VirtualMouse 0 260 275 26 VirtualMouse::VirtualMouse 0 2 94 95 10
/**
 *
 */
128
explicit VirtualMouse::VirtualMouse(std::string const &name);
inline VirtualMouse::VirtualMouse(VirtualMouse const &) = default;

230 13 set_mouse_pos 0 4 275 27 VirtualMouse::set_mouse_pos 0 1 96 155
/**
 * Sets the current mouse pixel location, where (0,0) is the upper left, and
 * (width-1, height-1) is the lower right pixel of the virtual window.
 */
47
void VirtualMouse::set_mouse_pos(int x, int y);

231 15 set_window_size 0 4 275 29 VirtualMouse::set_window_size 0 1 97 136
/**
 * Sets the size of the "window" in which the mouse rolls.  This changes the
 * meaning of the values passed to set_mouse_pos().
 */
58
void VirtualMouse::set_window_size(int width, int height);

232 12 set_mouse_on 0 4 275 26 VirtualMouse::set_mouse_on 0 1 98 217
/**
 * Sets whether the mouse should appear to be within the window or not.  If
 * this is true, the mouse is within the window; if false, the mouse is not
 * within the window (and set_mouse_pos() means nothing).
 */
43
void VirtualMouse::set_mouse_on(bool flag);

233 12 press_button 0 4 275 26 VirtualMouse::press_button 0 1 99 167
/**
 * Simulates a mouse or keyboard button being depressed.  This should be
 * followed up by a call to release_button() sometime later (possibly
 * immediately).
 */
53
void VirtualMouse::press_button(ButtonHandle button);

234 14 release_button 0 4 275 28 VirtualMouse::release_button 0 1 100 105
/**
 * Simulates the button being released.  This should follow a previous call to
 * press_button().
 */
55
void VirtualMouse::release_button(ButtonHandle button);

235 14 get_class_type 0 4 275 28 VirtualMouse::get_class_type 0 1 101 0
53
static TypeHandle VirtualMouse::get_class_type(void);

236 13 ~VirtualMouse 0 516 275 27 VirtualMouse::~VirtualMouse 0 0 0
34
VirtualMouse::~VirtualMouse(void);

101
1 0 0 6 80 252 0 0 69 /**
 * Returns true if the device supports the indicated feature.
 */ 2 4 this 3 276  7 feature 1 248  
2 0 0 4 93 282 0 0 490 /**
 * Associates the indicated ButtonHandle with the button of the indicated index
 * number.  When the given button index changes state, a corresponding
 * ButtonEvent will be generated with the given ButtonHandle.  Pass
 * ButtonHandle::none() to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various buttons by index number; this is only necessary in order to
 * generate ButtonEvents when the buttons change state.
 */ 3 4 this 3 278  5 index 1 279  6 handle 1 281  
3 0 0 4 94 282 0 0 251 /**
 * Associates the indicated Axis with the axis of the indicated index
 * number.  Pass Axis::none to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various axes by index number.
 */ 3 4 this 3 278  5 index 1 279  4 axis 1 249  
4 0 0 7 95 283 116 0 137 /**
 * Returns the first ButtonState found with the given axis, or throw an assert
 * if the button handle was not found in the list.
 */ 2 4 this 3 276  6 handle 1 281  
5 0 0 7 96 284 125 0 127 /**
 * Returns the first AnalogAxis found with the given axis, or throw an assert
 * if the axis was not found in the list.
 */ 2 4 this 3 276  4 axis 1 249  
6 0 0 4 97 282 0 0 226 /**
 * Sets the strength of the vibration effect, if supported.  The values are
 * clamped to 0-1 range. The first value axes the low-frequency rumble
 * motor, whereas the second axes the high-frequency motor, if present.
 */ 3 4 this 3 278  6 strong 1 238  4 weak 1 238  
7 0 0 4 98 282 0 0 59 /**
 * Enables the generation of mouse-movement events.
 */ 1 4 this 3 278  
8 0 0 4 99 282 0 0 60 /**
 * Disables the generation of mouse-movement events.
 */ 1 4 this 3 278  
9 0 0 4 100 282 0 0 233 /**
 * Polls the input device for new activity, to ensure it contains the latest
 * events.  This will only have any effect for some types of input devices;
 * others may be updated automatically, and this method will be a no-op.
 */ 1 4 this 3 278  
10 0 0 6 101 252 0 0 218 /**
 * Returns true if this device has a pending button event (a mouse button or
 * keyboard button down/up), false otherwise.  If this returns true, the
 * particular event may be extracted via get_button_event().
 */ 1 4 this 3 276  
11 0 0 7 102 286 0 0 92 /**
 * Returns the list of recently-generated ButtonEvents.
 * The list is also cleared.
 */ 1 4 this 3 278  
12 0 0 6 103 252 0 0 198 /**
 * Returns true if this device has a pending pointer event (a mouse movement),
 * or false otherwise.  If this returns true, the particular event may be
 * extracted via get_pointer_event().
 */ 1 4 this 3 276  
13 0 0 7 104 288 0 0 99 /**
 * Returns a PointerEventList containing all the recent pointer events.
 * Clears the list.
 */ 1 4 this 3 278  
14 0 0 4 105 282 0 0 58 /**
 * Writes a one-line string describing the device.
 */ 2 4 this 3 276  3 out 1 289  
15 0 0 7 121 292 0 0 0 0 
16 0 0 6 83 252 0 0 115 /**
 * Returns true if the device features a tracker that can track position and/or
 * orientation in 3D space.
 */ 1 4 this 3 276  
17 0 0 6 86 252 0 0 99 /**
 * Returns true if the device may be able to provide information about its
 * battery life.
 */ 1 4 this 3 276  
18 0 0 6 87 279 0 0 279 /**
 * Returns the number of buttons known to the device.  This includes those
 * buttons whose state has been seen, as well as buttons that have been
 * associated with a ButtonHandle even if their state is unknown.  This number
 * may change as more buttons are discovered.
 */ 1 4 this 3 276  
19 0 0 6 90 279 0 0 126 /**
 * Returns the number of analog axes known to the InputDevice.  This number
 * may change as more axes are discovered.
 */ 1 4 this 3 276  
20 0 0 15 38 283 116 0 0 1 6 param0 0 293  
21 0 0 6 29 252 0 0 0 1 4 this 3 283  
22 0 0 15 51 284 125 0 0 1 6 param0 0 295  
23 0 0 6 41 252 0 0 0 1 4 this 3 284  
24 0 0 7 61 297 131 0 0 0 
25 0 0 15 61 297 131 0 0 1 6 param0 0 298  
26 0 0 6 123 252 0 0 314 /**
 * Forks a separate thread to do all the polling of connected devices.  The
 * forked thread will poll after every poll_time seconds has elapsed.  Returns
 * true if the fork was successful, or false otherwise (for instance, because
 * we were already forked, or because asynchronous threads are disabled).
 */ 2 4 this 3 300  9 poll_time 1 238  
27 0 0 6 124 252 0 0 130 /**
 * Returns true if the ClientBase has been forked (and, therefore, poll() does
 * not need to be called), false otherwise.
 */ 1 4 this 3 301  
28 0 0 6 125 252 0 0 183 /**
 * Initiates a poll of the client devices, if we are not forked and if we have
 * not already polled this frame.  Returns true if the poll occurred, or false
 * if it did not.
 */ 1 4 this 3 300  
29 0 0 6 126 238 0 0 118 /**
 * Returns the time (according to the global ClockObject's get_real_time()
 * method) of the last device poll.
 */ 1 4 this 3 301  
30 0 0 4 127 282 0 0 138 /**
 * Specifies the coordinate system that all devices associated with this
 * client will operate in.  Normally, this is CS_default.
 */ 2 4 this 3 300  2 cs 1 303  
31 0 0 6 128 303 0 0 136 /**
 * Returns the coordinate system that all devices associated with this client
 * will operate in.  Normally, this is CS_default.
 */ 1 4 this 3 301  
32 0 0 7 129 292 0 0 0 0 
33 0 0 15 132 306 0 0 0 1 6 param0 0 304  
34 0 0 7 132 306 0 0 10 /**
 *
 */ 2 6 client 1 300  11 device_name 1 307  
35 0 0 7 132 306 0 0 10 /**
 *
 */ 1 6 device 1 278  
36 0 0 6 133 252 0 0 97 /**
 * Returns true if the AnalogNode is valid and connected to a server, false
 * otherwise.
 */ 1 4 this 3 304  
37 0 0 6 134 308 0 0 133 /**
 * Returns the number of analog controls known to the AnalogNode.  This number
 * may change as more controls are discovered.
 */ 1 4 this 3 304  
38 0 0 6 135 238 0 0 193 /**
 * Returns the current position of indicated analog control identified by its
 * index number, or 0.0 if the control is unknown.  The normal range of a
 * single control is -1.0 to 1.0.
 */ 2 4 this 3 304  5 index 1 308  
39 0 0 6 136 252 0 0 153 /**
 * Returns true if the state of the indicated analog control is known, or
 * false if we have never heard anything about this particular control.
 */ 2 4 this 3 304  5 index 1 308  
40 0 0 4 137 282 0 0 314 /**
 * Causes a particular analog control to be placed in the data graph for the
 * indicated channel.  Normally, a mouse uses channels 0 and 1 for the X and Y
 * information, respectively; channels 0, 1, and 2 are available.  If flip is
 * true, the analog control value will be reversed before outputting it.
 */ 4 4 this 3 306  7 channel 1 308  5 index 1 308  4 flip 1 252  
41 0 0 4 138 282 0 0 107 /**
 * Removes the output to the data graph associated with the indicated channel.
 * See set_output().
 */ 2 4 this 3 306  7 channel 1 308  
42 0 0 6 139 308 0 0 174 /**
 * Returns the analog control index that is output to the data graph on the
 * indicated channel, or -1 if no control is output on that channel.  See
 * set_output().
 */ 2 4 this 3 304  7 channel 1 308  
43 0 0 6 140 252 0 0 143 /**
 * Returns true if the analog control index that is output to the data graph
 * on the indicated channel is flipped.  See set_output().
 */ 2 4 this 3 304  7 channel 1 308  
44 0 0 7 141 292 0 0 0 0 
45 0 0 15 143 311 0 0 0 1 6 param0 0 309  
46 0 0 7 143 311 0 0 10 /**
 *
 */ 2 6 client 1 300  11 device_name 1 307  
47 0 0 7 143 311 0 0 10 /**
 *
 */ 1 6 device 1 278  
48 0 0 6 144 252 0 0 97 /**
 * Returns true if the ButtonNode is valid and connected to a server, false
 * otherwise.
 */ 1 4 this 3 309  
49 0 0 6 145 308 0 0 283 /**
 * Returns the number of buttons known to the ButtonNode.  This includes those
 * buttons whose state has been seen, as well as buttons that have been
 * associated with a ButtonHandle even if their state is unknown.  This number
 * may change as more buttons are discovered.
 */ 1 4 this 3 309  
50 0 0 4 146 282 0 0 490 /**
 * Associates the indicated ButtonHandle with the button of the indicated
 * index number.  When the given button index changes state, a corresponding
 * ButtonEvent will be generated with the given ButtonHandle.  Pass
 * ButtonHandle::none() to turn off any association.
 *
 * It is not necessary to call this if you simply want to query the state of
 * the various buttons by index number; this is only necessary in order to
 * generate ButtonEvents when the buttons change state.
 */ 3 4 this 3 311  5 index 1 308  6 button 1 281  
51 0 0 7 147 281 0 0 184 /**
 * Returns the ButtonHandle that was previously associated with the given
 * index number by a call to set_button_map(), or ButtonHandle::none() if no
 * button was associated.
 */ 2 4 this 3 309  5 index 1 308  
52 0 0 6 148 252 0 0 147 /**
 * Returns true if the indicated button (identified by its index number) is
 * currently known to be down, or false if it is up or unknown.
 */ 2 4 this 3 309  5 index 1 308  
53 0 0 6 149 252 0 0 144 /**
 * Returns true if the state of the indicated button is known, or false if we
 * have never heard anything about this particular button.
 */ 2 4 this 3 309  5 index 1 308  
54 0 0 7 150 292 0 0 0 0 
55 0 0 7 152 312 0 0 10 /**
 *
 */ 2 6 client 1 300  11 device_name 1 307  
56 0 0 15 152 312 0 0 0 1 6 param0 0 313  
57 0 0 6 153 252 0 0 95 /**
 * Returns true if the DialNode is valid and connected to a server, false
 * otherwise.
 */ 1 4 this 3 313  
58 0 0 6 154 308 0 0 123 /**
 * Returns the number of dial dials known to the DialNode.  This number may
 * change as more dials are discovered.
 */ 1 4 this 3 313  
59 0 0 6 155 238 0 0 216 /**
 * Returns the number of complete revolutions of the dial since the last time
 * read_dial() was called.  This is a destructive operation; it is not
 * possible to read the dial without resetting the counter.
 */ 2 4 this 3 312  5 index 1 308  
60 0 0 6 156 252 0 0 145 /**
 * Returns true if the state of the indicated dial dial is known, or false if
 * we have never heard anything about this particular dial.
 */ 2 4 this 3 313  5 index 1 308  
61 0 0 7 157 292 0 0 0 0 
62 0 0 7 159 315 200 0 10 /**
 *
 */ 0 
63 0 0 15 159 315 200 0 10 /**
 *
 */ 1 4 copy 1 316  
64 0 0 6 160 315 0 0 0 2 4 this 3 315  4 copy 1 316  
65 0 0 4 162 282 0 0 56 /**
 * Removes all InputDevices from the collection.
 */ 1 4 this 3 315  
66 0 0 4 163 282 0 0 160 /**
 * This is a hint to Panda to allocate enough memory to hold the given number
 * of InputDevices, if you know ahead of time how many you will be adding.
 */ 2 4 this 3 315  3 num 1 279  
67 0 0 7 164 278 0 0 57 /**
 * Returns the nth InputDevice in the collection.
 */ 2 4 this 3 316  5 index 1 279  
68 0 0 6 165 279 0 0 59 /**
 * Returns the number of devices in the collection.
 */ 1 4 this 3 316  
69 0 0 4 166 282 0 0 103 /**
 * Writes a brief one-line description of the InputDeviceSet to the indicated
 * output stream.
 */ 2 4 this 3 316  3 out 1 289  
70 0 0 4 167 282 0 0 108 /**
 * Writes a complete multi-line description of the InputDeviceSet to the
 * indicated output stream.
 */ 3 4 this 3 316  3 out 1 289  12 indent_level 5 308  
71 0 0 7 169 315 200 0 64 /**
 * Description: Returns all currently connected devices.
 */ 1 4 this 3 318  
72 0 0 7 169 315 200 0 90 /**
 * Description: Returns all currently connected devices of the given device class.
 */ 2 4 this 3 318  12 device_class 1 247  
73 0 0 4 170 282 0 0 166 /**
 * Called when a new device has been discovered.  This may also be used to
 * register virtual devices.
 *
 * This causes a connect-device event to be thrown.
 */ 2 4 this 3 320  6 device 1 278  
74 0 0 4 171 282 0 0 166 /**
 * Called when a device has been removed, or when a device should otherwise no
 * longer be tracked.
 *
 * This causes a disconnect-device event to be thrown.
 */ 2 4 this 3 320  6 device 1 278  
75 0 0 4 172 282 0 0 109 /**
 * Polls the system to see if there are any new devices.  In some
 * implementations this is a no-op.
 */ 1 4 this 3 320  
76 0 0 6 173 320 0 0 61 /**
 * Returns the singleton InputDeviceManager instance.
 */ 0 
77 0 0 23 175 321 216 0 0 2 6 device 1 278  4 name 1 307  
78 0 0 15 175 321 216 0 0 1 6 param0 0 322  
79 0 0 7 180 292 0 0 0 0 
80 0 0 7 183 324 0 0 10 /**
 *
 */ 2 6 client 1 300  11 device_name 1 307  
81 0 0 7 183 324 0 0 10 /**
 *
 */ 1 6 device 1 278  
82 0 0 15 183 324 0 0 0 1 6 param0 0 325  
83 0 0 6 184 252 0 0 98 /**
 * Returns true if the TrackerNode is valid and connected to a server, false
 * otherwise.
 */ 1 4 this 3 325  
84 0 0 6 185 327 0 0 75 /**
 * Returns the current position of the tracker, if it is available.
 */ 1 4 this 3 325  
85 0 0 6 186 328 0 0 78 /**
 * Returns the current orientation of the tracker, if it is available.
 */ 1 4 this 3 325  
86 0 0 6 187 329 0 0 96 /**
 * Returns the current position and orientation of the tracker, as a combined
 * matrix.
 */ 1 4 this 3 325  
87 0 0 6 188 238 0 0 57 /**
 * Returns the time of the tracker's last update.
 */ 1 4 this 3 325  
88 0 0 6 189 252 0 0 51 /**
 * True if this data comes with timestamps.
 */ 1 4 this 3 325  
89 0 0 4 190 282 0 0 257 /**
 * Specifies the coordinate system that the tracker associated with this node
 * will operate in.  Normally, this is set from the ClientBase that's used to
 * create the TrackerNode, so it should not need to be set on an individual
 * tracker basis.
 */ 2 4 this 3 324  2 cs 1 303  
90 0 0 6 191 303 0 0 103 /**
 * Returns the coordinate system that the tracker associated with this node
 * will operate in.
 */ 1 4 this 3 325  
91 0 0 4 192 282 0 0 165 /**
 * Specifies the coordinate system that the TrackerNode will convert its
 * transform into for passing down the data graph.  Normally, this is
 * CS_default.
 */ 2 4 this 3 324  2 cs 1 303  
92 0 0 6 193 303 0 0 163 /**
 * Returns the coordinate system that the TrackerNode will convert its
 * transform into for passing down the data graph.  Normally, this is
 * CS_default.
 */ 1 4 this 3 325  
93 0 0 7 194 292 0 0 0 0 
94 0 0 15 196 335 236 0 0 1 6 param0 0 333  
95 0 0 7 196 335 236 0 10 /**
 *
 */ 1 4 name 1 307  
96 0 0 4 197 282 0 0 155 /**
 * Sets the current mouse pixel location, where (0,0) is the upper left, and
 * (width-1, height-1) is the lower right pixel of the virtual window.
 */ 3 4 this 3 335  1 x 1 308  1 y 1 308  
97 0 0 4 198 282 0 0 136 /**
 * Sets the size of the "window" in which the mouse rolls.  This changes the
 * meaning of the values passed to set_mouse_pos().
 */ 3 4 this 3 335  5 width 1 308  6 height 1 308  
98 0 0 4 199 282 0 0 217 /**
 * Sets whether the mouse should appear to be within the window or not.  If
 * this is true, the mouse is within the window; if false, the mouse is not
 * within the window (and set_mouse_pos() means nothing).
 */ 2 4 this 3 335  4 flag 1 252  
99 0 0 4 200 282 0 0 167 /**
 * Simulates a mouse or keyboard button being depressed.  This should be
 * followed up by a call to release_button() sometime later (possibly
 * immediately).
 */ 2 4 this 3 335  6 button 1 281  
100 0 0 4 201 282 0 0 105 /**
 * Simulates the button being released.  This should follow a previous call to
 * press_button().
 */ 2 4 this 3 335  6 button 1 281  
101 0 0 7 202 292 0 0 0 0 
99
237 11 TrackerData 0 141313 11 TrackerData 11 TrackerData 0 0 0 0 110 4 336 337 338 339 0 0 0 0 0 0 64
/**
 * Stores the kinds of data that a tracker might output.
 */

238 6 double 0 8194 6 double 6 double 0 3 0 0 0 0 0 0 0 0 0 0 0

239 13 LPoint3 const 0 8832 13 LPoint3 const 13 LPoint3 const 0 0 240 0 0 0 0 0 0 0 0 0 0

240 7 LPoint3 0 2105344 7 LPoint3 7 LPoint3 0 0 241 0 0 0 0 0 0 0 0 0 0

241 8 LPoint3f 0 2048 8 LPoint3f 8 LPoint3f 0 0 0 0 0 0 0 0 0 0 0 0 337
/**
 * This is a three-component point in space (as opposed to a three-component
 * vector, which represents a direction and a distance).  Some of the methods
 * are slightly different between LPoint3 and LVector3; in particular,
 * subtraction of two points yields a vector, while addition of a vector and a
 * point yields a point.
 */

242 18 LOrientation const 0 8832 18 LOrientation const 18 LOrientation const 0 0 243 0 0 0 0 0 0 0 0 0 0

243 12 LOrientation 0 2105344 12 LOrientation 12 LOrientation 0 0 244 0 0 0 0 0 0 0 0 0 0

244 13 LOrientationf 0 2048 13 LOrientationf 13 LOrientationf 0 0 0 0 0 0 0 0 0 0 0 0 65
/**
 * This is a unit quaternion representing an orientation.
 */

245 11 InputDevice 0 75777 11 InputDevice 11 InputDevice 0 0 0 0 0 16 348 349 350 351 352 353 354 355 356 357 358 359 360 361 362 363 15 139 148 149 150 151 152 153 154 155 156 157 158 159 160 166 0 0 1 0 246 0 0 0 9 247 248 249 250 251 254 255 260 262 959
/**
 * This is a structure representing a single input device.  Input devices may
 * have zero or more buttons, pointers, or axes associated with them, and
 * optionally a motion tracker.
 *
 * These devices are brought under a common interface because there is such a
 * large range of devices out there that may support any number of these types
 * of axes, we couldn't even begin to cover them with type-specific
 * subclasses.
 *
 * Use the various has_() and get_num_() methods to determine information about
 * the device capabilities. For instance, has_keyboard() will give an
 * indication that you can receive keystroke events from this device, and
 * get_num_buttons() will tell you that the device may send button events.
 *
 * There is the DeviceType enumeration, however, which will (if known) contain
 * identification of the general category of devices this fits in, such as
 * keyboard, mouse, gamepad, or flight stick.
 *
 * @since 1.10.0
 */

246 19 TypedReferenceCount 0 2048 19 TypedReferenceCount 19 TypedReferenceCount 0 0 0 0 0 0 0 0 0 0 0 0 410
/**
 * A base class for things which need to inherit from both TypedObject and
 * from ReferenceCount.  It's convenient to define this intermediate base
 * class instead of multiply inheriting from the two classes each time they
 * are needed, so that we can sensibly pass around pointers to things which
 * are both TypedObjects and ReferenceCounters.
 *
 * See also TypedObject for detailed instructions.
 */

247 11 DeviceClass 0 9183232 24 InputDevice::DeviceClass 24 InputDevice::DeviceClass 245 0 0 0 0 0 0 0 0 0 12 7 unknown 20 InputDevice::unknown 47
// It is not known what type of device this is.
0 14 virtual_device 27 InputDevice::virtual_device 121
// This means that the device doesn't correspond to a physical
// device, but rather to a dynamic source of input events.
1 8 keyboard 21 InputDevice::keyboard 37
// A physical, alphabetical keyboard.
2 5 mouse 18 InputDevice::mouse 0
3 5 touch 18 InputDevice::touch 0
4 7 gamepad 20 InputDevice::gamepad 59
// A gamepad with action buttons, a D-pad, and thumbsticks.
5 12 flight_stick 25 InputDevice::flight_stick 0
6 14 steering_wheel 27 InputDevice::steering_wheel 0
7 9 dance_pad 22 InputDevice::dance_pad 0
8 3 hmd 16 InputDevice::hmd 24
// Head-mounted display.
9 13 spatial_mouse 26 InputDevice::spatial_mouse 45
// 3D mouse, such as produced by 3Dconnexion.
10 9 digitizer 22 InputDevice::digitizer 37
// A graphics tablet with stylus/pen.
11 0 91
// This enum contains information that can be used to identify the
// type of input device.

248 7 Feature 0 9183232 20 InputDevice::Feature 20 InputDevice::Feature 245 0 0 0 0 0 0 0 0 0 5 7 pointer 20 InputDevice::pointer 51
// The device provides absolute screen coordinates.
0 8 keyboard 21 InputDevice::keyboard 56
// The device has an interface for providing text input.
1 7 tracker 20 InputDevice::tracker 51
// The device has a motion tracker, such as an HMD.
2 9 vibration 22 InputDevice::vibration 41
// The device can produce force feedback.
3 7 battery 20 InputDevice::battery 54
// The device provides information about battery life.
4 0 0

249 4 Axis 0 9183232 17 InputDevice::Axis 17 InputDevice::Axis 245 0 0 0 0 0 0 0 0 0 19 4 none 17 InputDevice::none 0
0 1 x 14 InputDevice::x 29
// Generic translational axes
1 1 y 14 InputDevice::y 0
2 1 z 14 InputDevice::z 0
3 3 yaw 16 InputDevice::yaw 57
// Generic rotational axes, used by joysticks and 3D mice
4 5 pitch 18 InputDevice::pitch 0
5 4 roll 17 InputDevice::roll 0
6 6 left_x 19 InputDevice::left_x 10
// Gamepad
7 6 left_y 19 InputDevice::left_y 0
8 12 left_trigger 25 InputDevice::left_trigger 0
9 7 right_x 20 InputDevice::right_x 0
10 7 right_y 20 InputDevice::right_y 0
11 13 right_trigger 26 InputDevice::right_trigger 0
12 8 throttle 21 InputDevice::throttle 24
// Flight stick specific
13 6 rudder 19 InputDevice::rudder 37
// When available separately from yaw
14 5 wheel 18 InputDevice::wheel 26
// Steering wheel / pedals
15 11 accelerator 24 InputDevice::accelerator 0
16 5 brake 18 InputDevice::brake 0
17 8 pressure 21 InputDevice::pressure 15
// Pen pressure
18 0 0

250 5 State 0 794624 18 InputDevice::State 18 InputDevice::State 245 0 0 0 0 0 0 0 0 0 3 9 S_unknown 22 InputDevice::S_unknown 0
0 4 S_up 17 InputDevice::S_up 0
1 6 S_down 19 InputDevice::S_down 0
2 0 0

251 11 ButtonState 0 403457 24 InputDevice::ButtonState 24 InputDevice::ButtonState 245 0 0 1 115 116 3 340 341 342 0 0 1 111 0 0 0 0

252 4 bool 0 8194 4 bool 4 bool 0 4 0 0 0 0 0 0 0 0 0 0 0

253 12 ButtonHandle 0 16779264 12 ButtonHandle 12 ButtonHandle 0 0 0 0 0 0 0 0 0 0 0 0 162
/**
 * A ButtonHandle represents a single button from any device, including
 * keyboard buttons and mouse buttons (but see KeyboardButton and
 * MouseButton).
 */

254 9 AxisState 0 403457 22 InputDevice::AxisState 22 InputDevice::AxisState 245 0 0 1 124 125 3 343 344 345 0 0 1 117 0 0 0 0

255 11 BatteryData 0 403457 24 InputDevice::BatteryData 24 InputDevice::BatteryData 245 0 0 1 130 131 2 346 347 0 0 0 0 0 0 0

256 9 short int 0 8258 9 short int 9 short int 0 1 0 0 0 0 0 0 0 0 0 0 0

257 6 string 0 2105344 11 std::string 11 std::string 0 0 258 0 0 0 0 0 0 0 0 0 0

258 20 basic_string< char > 0 2048 25 std::basic_string< char > 25 std::basic_string< char > 0 0 0 0 0 0 0 0 0 0 0 0 0

259 18 unsigned short int 0 8262 18 unsigned short int 18 unsigned short int 0 1 0 0 0 0 0 0 0 0 0 0 0

260 7 Buttons 0 2367488 20 InputDevice::Buttons 20 InputDevice::Buttons 245 0 261 0 0 0 0 0 0 0 0 0 0

261 22 pvector< ButtonState > 0 2048 35 pvector< InputDevice::ButtonState > 35 pvector< InputDevice::ButtonState > 0 0 0 0 0 0 0 0 0 0 0 0 0

262 4 Axes 0 2367488 17 InputDevice::Axes 17 InputDevice::Axes 245 0 263 0 0 0 0 0 0 0 0 0 0

263 20 pvector< AxisState > 0 2048 33 pvector< InputDevice::AxisState > 33 pvector< InputDevice::AxisState > 0 0 0 0 0 0 0 0 0 0 0 0 0

264 11 PointerData 0 2048 11 PointerData 11 PointerData 0 0 0 0 0 0 0 0 0 0 0 0 124
/**
 * Holds the data that might be generated by a 2-d pointer input device, such
 * as the mouse in the GraphicsWindow.
 */

265 10 ClientBase 0 75777 10 ClientBase 10 ClientBase 0 0 0 0 0 0 7 167 168 169 170 171 172 173 0 0 1 0 246 0 0 0 0 307
/**
 * An abstract base class for a family of client device interfaces--including
 * trackers, buttons, dials, and other analog inputs.
 *
 * This provides a common interface to connect to such devices and extract
 * their data; it is used by TrackerNode etc.  to put these devices in the
 * data graph.
 */

266 10 AnalogNode 0 75777 10 AnalogNode 10 AnalogNode 0 0 0 1 174 0 0 9 175 176 177 178 179 180 181 182 183 0 0 1 0 267 0 0 0 0 712
/**
 * This is the primary interface to analog controls like sliders and joysticks
 * associated with a ClientBase.  This creates a node that connects to the
 * named analog device, if it exists, and provides hooks to the user to read
 * the state of any of the sequentially numbered controls associated with that
 * device.
 *
 * Each control can return a value ranging from -1 to 1, reflecting the
 * current position of the control within its total range of motion.
 *
 * The user may choose up to two analog controls to place on the data graph as
 * the two channels of an xy datagram, similarly to the way a mouse places its
 * position data.  In this way, an AnalogNode may be used in place of a mouse.
 */

267 8 DataNode 0 2048 8 DataNode 8 DataNode 0 0 0 0 0 0 0 0 0 0 0 0 422
/**
 * The fundamental type of node for the data graph.  The DataNode class is
 * itself primarily intended as an abstract class; it defines no inputs and no
 * outputs.  Most kinds of data nodes will derive from this to specify the
 * inputs and outputs in the constructor.
 *
 * DataNode does not attempt to cycle its data with a PipelineCycler.  The
 * data graph is intended to be used only within a single thread.
 */

268 10 ButtonNode 0 75777 10 ButtonNode 10 ButtonNode 0 0 0 1 184 0 0 7 185 186 187 188 189 190 191 0 0 1 0 267 0 0 0 0 527
/**
 * This is the primary interface to on/off button devices associated with a
 * ClientBase.  This creates a node that connects to the named button device,
 * if it exists, and provides hooks to the user to read the state of any of
 * the sequentially numbered buttons associated with that device.
 *
 * It also can associate an arbitrary ButtonHandle with each button; when
 * buttons are associated with ButtonHandles, this node will put appropriate
 * up and down events on the data graph for each button state change.
 */

269 8 DialNode 0 75777 8 DialNode 8 DialNode 0 0 0 1 192 0 0 5 193 194 195 196 197 0 0 1 0 267 0 0 0 0 511
/**
 * This is the primary interface to infinite dial type devices associated with
 * a ClientBase.  This creates a node that connects to the named dial device,
 * if it exists, and provides hooks to the user to read the state of any of
 * the sequentially numbered dial controls associated with that device.
 *
 * A dial is a rotating device that does not have stops--it can keep rotating
 * any number of times.  Therefore it does not have a specific position at any
 * given time, unlike an AnalogDevice.
 */

270 14 InputDeviceSet 0 26625 14 InputDeviceSet 14 InputDeviceSet 0 0 0 1 198 200 0 7 199 201 202 203 204 205 206 0 0 0 0 0 192
/**
 * Manages a list of InputDevice objects, as returned by various
 * InputDeviceManager methods.  This is implemented like a set, meaning the
 * same device cannot occur more than once.
 */

271 18 InputDeviceManager 0 43009 18 InputDeviceManager 18 InputDeviceManager 0 0 0 0 0 0 5 207 208 209 210 211 0 0 0 0 0 145
/**
 * This class keeps track of all the devices on a system, and sends out events
 * when a device has been hot-plugged.
 *
 * @since 1.10.0
 */

272 15 InputDeviceNode 0 141313 15 InputDeviceNode 15 InputDeviceNode 0 0 0 1 212 216 1 364 1 215 0 0 1 0 267 0 0 0 0 175
/**
 * Reads the controller data sent from the InputDeviceManager, and transmits
 * it down the data graph.
 *
 * This is intended to only be accessed from the app thread.
 */

273 24 PointerTo< InputDevice > 0 2048 24 PointerTo< InputDevice > 24 PointerTo< InputDevice > 0 0 0 0 0 0 0 0 0 0 0 0 0

274 11 TrackerNode 0 75777 11 TrackerNode 11 TrackerNode 0 0 0 1 217 0 0 11 218 219 220 221 222 223 224 225 226 227 228 0 0 1 0 267 0 0 0 0 244
/**
 * This class reads the position and orientation information from a tracker
 * device and makes it available as a transformation on the data graph.
 * It is also the primary interface to a Tracker object associated with a
 * ClientBase.
 */

275 12 VirtualMouse 0 141313 12 VirtualMouse 12 VirtualMouse 0 0 0 1 229 236 0 6 230 231 232 233 234 235 0 0 1 0 267 0 0 0 0 291
/**
 * Poses as a MouseAndKeyboard object in the datagraph, but accepts input from
 * user calls, rather than reading the actual mouse and keyboard from an input
 * device.  The user can write high-level code to put the mouse wherever
 * he/she wants, and to insert keypresses on demand.
 */

276 19 InputDevice const * 0 8576 19 InputDevice const * 19 InputDevice const * 0 0 277 0 0 0 0 0 0 0 0 0 0

277 17 InputDevice const 0 8832 17 InputDevice const 17 InputDevice const 0 0 245 0 0 0 0 0 0 0 0 0 0

278 13 InputDevice * 0 8576 13 InputDevice * 13 InputDevice * 0 0 245 0 0 0 0 0 0 0 0 0 0

279 6 size_t 0 2105344 11 std::size_t 11 std::size_t 0 0 280 0 0 0 0 0 0 0 0 0 0

280 22 unsigned long long int 0 8230 22 unsigned long long int 22 unsigned long long int 0 8 0 0 0 0 0 0 0 0 0 0 0

281 14 ButtonHandle * 0 8576 14 ButtonHandle * 14 ButtonHandle * 0 0 253 0 0 0 0 0 0 0 0 0 0

282 4 void 0 8194 4 void 4 void 0 6 0 0 0 0 0 0 0 0 0 0 0

283 13 ButtonState * 0 8576 26 InputDevice::ButtonState * 26 InputDevice::ButtonState * 0 0 251 0 0 0 0 0 0 0 0 0 0

284 11 AxisState * 0 8576 24 InputDevice::AxisState * 24 InputDevice::AxisState * 0 0 254 0 0 0 0 0 0 0 0 0 0

285 15 ButtonEventList 0 2048 15 ButtonEventList 15 ButtonEventList 0 0 0 0 0 0 0 0 0 0 0 0 225
/**
 * Records a set of button events that happened recently.  This class is
 * usually used only in the data graph, to transmit the recent button presses,
 * but it may be used anywhere a list of ButtonEvents is desired.
 */

286 17 ButtonEventList * 0 8576 17 ButtonEventList * 17 ButtonEventList * 0 0 285 0 0 0 0 0 0 0 0 0 0

287 16 PointerEventList 0 2048 16 PointerEventList 16 PointerEventList 0 0 0 0 0 0 0 0 0 0 0 0 228
/**
 * Records a set of pointer events that happened recently.  This class is
 * usually used only in the data graph, to transmit the recent pointer
 * presses, but it may be used anywhere a list of PointerEvents is desired.
 */

288 18 PointerEventList * 0 8576 18 PointerEventList * 18 PointerEventList * 0 0 287 0 0 0 0 0 0 0 0 0 0

289 9 ostream * 0 8576 14 std::ostream * 14 std::ostream * 0 0 290 0 0 0 0 0 0 0 0 0 0

290 7 ostream 0 2048 12 std::ostream 12 std::ostream 0 0 0 0 0 0 0 0 0 0 0 0 0

291 10 TypeHandle 0 16779264 10 TypeHandle 10 TypeHandle 0 0 0 0 0 0 0 0 0 0 0 0 732
/**
 * TypeHandle is the identifier used to differentiate C++ class types.  Any
 * C++ classes that inherit from some base class, and must be differentiated
 * at run time, should store a static TypeHandle object that can be queried
 * through a static member function named get_class_type().  Most of the time,
 * it is also desirable to inherit from TypedObject, which provides some
 * virtual functions to return the TypeHandle for a particular instance.
 *
 * At its essence, a TypeHandle is simply a unique identifier that is assigned
 * by the TypeRegistry.  The TypeRegistry stores a tree of TypeHandles, so
 * that ancestry of a particular type may be queried, and the type name may be
 * retrieved for run-time display.
 */

292 12 TypeHandle * 0 8576 12 TypeHandle * 12 TypeHandle * 0 0 291 0 0 0 0 0 0 0 0 0 0

293 19 ButtonState const * 0 8576 32 InputDevice::ButtonState const * 32 InputDevice::ButtonState const * 0 0 294 0 0 0 0 0 0 0 0 0 0

294 17 ButtonState const 0 8832 30 InputDevice::ButtonState const 30 InputDevice::ButtonState const 0 0 251 0 0 0 0 0 0 0 0 0 0

295 17 AxisState const * 0 8576 30 InputDevice::AxisState const * 30 InputDevice::AxisState const * 0 0 296 0 0 0 0 0 0 0 0 0 0

296 15 AxisState const 0 8832 28 InputDevice::AxisState const 28 InputDevice::AxisState const 0 0 254 0 0 0 0 0 0 0 0 0 0

297 13 BatteryData * 0 8576 26 InputDevice::BatteryData * 26 InputDevice::BatteryData * 0 0 255 0 0 0 0 0 0 0 0 0 0

298 19 BatteryData const * 0 8576 32 InputDevice::BatteryData const * 32 InputDevice::BatteryData const * 0 0 299 0 0 0 0 0 0 0 0 0 0

299 17 BatteryData const 0 8832 30 InputDevice::BatteryData const 30 InputDevice::BatteryData const 0 0 255 0 0 0 0 0 0 0 0 0 0

300 12 ClientBase * 0 8576 12 ClientBase * 12 ClientBase * 0 0 265 0 0 0 0 0 0 0 0 0 0

301 18 ClientBase const * 0 8576 18 ClientBase const * 18 ClientBase const * 0 0 302 0 0 0 0 0 0 0 0 0 0

302 16 ClientBase const 0 8832 16 ClientBase const 16 ClientBase const 0 0 265 0 0 0 0 0 0 0 0 0 0

303 16 CoordinateSystem 0 532480 16 CoordinateSystem 16 CoordinateSystem 0 0 0 0 0 0 0 0 0 0 6 10 CS_default 10 CS_default 210
// The CS_default entry does not refer to a particular coordinate system,
// but rather to the value stored in default_coordinate_system, which in
// turn is loaded from the config variable "coordinate-system".
0 12 CS_zup_right 12 CS_zup_right 21
// Z-Up, Right-handed
1 12 CS_yup_right 12 CS_yup_right 21
// Y-Up, Right-handed
2 11 CS_zup_left 11 CS_zup_left 20
// Z-Up, Left-handed
3 11 CS_yup_left 11 CS_yup_left 20
// Y-Up, Left-handed
4 10 CS_invalid 10 CS_invalid 156
// CS_invalid is not a coordinate system at all.  It can be used in user-
// input processing code to indicate a contradictory coordinate system
// request.
5 0 0

304 18 AnalogNode const * 0 8576 18 AnalogNode const * 18 AnalogNode const * 0 0 305 0 0 0 0 0 0 0 0 0 0

305 16 AnalogNode const 0 8832 16 AnalogNode const 16 AnalogNode const 0 0 266 0 0 0 0 0 0 0 0 0 0

306 12 AnalogNode * 0 8576 12 AnalogNode * 12 AnalogNode * 0 0 266 0 0 0 0 0 0 0 0 0 0

307 13 atomic string 0 2 13 atomic string 13 atomic string 0 7 0 0 0 0 0 0 0 0 0 0 0

308 3 int 0 8194 3 int 3 int 0 1 0 0 0 0 0 0 0 0 0 0 0

309 18 ButtonNode const * 0 8576 18 ButtonNode const * 18 ButtonNode const * 0 0 310 0 0 0 0 0 0 0 0 0 0

310 16 ButtonNode const 0 8832 16 ButtonNode const 16 ButtonNode const 0 0 268 0 0 0 0 0 0 0 0 0 0

311 12 ButtonNode * 0 8576 12 ButtonNode * 12 ButtonNode * 0 0 268 0 0 0 0 0 0 0 0 0 0

312 10 DialNode * 0 8576 10 DialNode * 10 DialNode * 0 0 269 0 0 0 0 0 0 0 0 0 0

313 16 DialNode const * 0 8576 16 DialNode const * 16 DialNode const * 0 0 314 0 0 0 0 0 0 0 0 0 0

314 14 DialNode const 0 8832 14 DialNode const 14 DialNode const 0 0 269 0 0 0 0 0 0 0 0 0 0

315 16 InputDeviceSet * 0 8576 16 InputDeviceSet * 16 InputDeviceSet * 0 0 270 0 0 0 0 0 0 0 0 0 0

316 22 InputDeviceSet const * 0 8576 22 InputDeviceSet const * 22 InputDeviceSet const * 0 0 317 0 0 0 0 0 0 0 0 0 0

317 20 InputDeviceSet const 0 8832 20 InputDeviceSet const 20 InputDeviceSet const 0 0 270 0 0 0 0 0 0 0 0 0 0

318 26 InputDeviceManager const * 0 8576 26 InputDeviceManager const * 26 InputDeviceManager const * 0 0 319 0 0 0 0 0 0 0 0 0 0

319 24 InputDeviceManager const 0 8832 24 InputDeviceManager const 24 InputDeviceManager const 0 0 271 0 0 0 0 0 0 0 0 0 0

320 20 InputDeviceManager * 0 8576 20 InputDeviceManager * 20 InputDeviceManager * 0 0 271 0 0 0 0 0 0 0 0 0 0

321 17 InputDeviceNode * 0 8576 17 InputDeviceNode * 17 InputDeviceNode * 0 0 272 0 0 0 0 0 0 0 0 0 0

322 23 InputDeviceNode const * 0 8576 23 InputDeviceNode const * 23 InputDeviceNode const * 0 0 323 0 0 0 0 0 0 0 0 0 0

323 21 InputDeviceNode const 0 8832 21 InputDeviceNode const 21 InputDeviceNode const 0 0 272 0 0 0 0 0 0 0 0 0 0

324 13 TrackerNode * 0 8576 13 TrackerNode * 13 TrackerNode * 0 0 274 0 0 0 0 0 0 0 0 0 0

325 19 TrackerNode const * 0 8576 19 TrackerNode const * 19 TrackerNode const * 0 0 326 0 0 0 0 0 0 0 0 0 0

326 17 TrackerNode const 0 8832 17 TrackerNode const 17 TrackerNode const 0 0 274 0 0 0 0 0 0 0 0 0 0

327 15 LPoint3 const * 0 8576 15 LPoint3 const * 15 LPoint3 const * 0 0 239 0 0 0 0 0 0 0 0 0 0

328 20 LOrientation const * 0 8576 20 LOrientation const * 20 LOrientation const * 0 0 242 0 0 0 0 0 0 0 0 0 0

329 16 LMatrix4 const * 0 8576 16 LMatrix4 const * 16 LMatrix4 const * 0 0 330 0 0 0 0 0 0 0 0 0 0

330 14 LMatrix4 const 0 8832 14 LMatrix4 const 14 LMatrix4 const 0 0 331 0 0 0 0 0 0 0 0 0 0

331 8 LMatrix4 0 2105344 8 LMatrix4 8 LMatrix4 0 0 332 0 0 0 0 0 0 0 0 0 0

332 9 LMatrix4f 0 2048 9 LMatrix4f 9 LMatrix4f 0 0 0 0 0 0 0 0 0 0 0 0 45
/**
 * This is a 4-by-4 transform matrix.
 */

333 20 VirtualMouse const * 0 8576 20 VirtualMouse const * 20 VirtualMouse const * 0 0 334 0 0 0 0 0 0 0 0 0 0

334 18 VirtualMouse const 0 8832 18 VirtualMouse const 18 VirtualMouse const 0 0 275 0 0 0 0 0 0 0 0 0 0

335 14 VirtualMouse * 0 8576 14 VirtualMouse * 14 VirtualMouse * 0 0 275 0 0 0 0 0 0 0 0 0 0

0
29
336 4 time 0 6 238 102 103 0 0 0 0 0 0 17 TrackerData::time 0

337 3 pos 0 6 239 104 105 0 0 0 0 0 0 16 TrackerData::pos 0

338 6 orient 0 6 242 106 107 0 0 0 0 0 0 19 TrackerData::orient 0

339 2 dt 0 6 238 108 109 0 0 0 0 0 0 15 TrackerData::dt 0

340 5 known 0 2 252 112 0 0 0 0 0 0 0 31 InputDevice::ButtonState::known 0

341 7 pressed 0 2 252 113 0 0 0 0 0 0 0 33 InputDevice::ButtonState::pressed 0

342 6 handle 0 2 253 114 0 0 0 0 0 0 0 32 InputDevice::ButtonState::handle 0

343 4 axis 0 6 249 118 119 0 0 0 0 0 0 28 InputDevice::AxisState::axis 0

344 5 value 0 6 238 120 121 0 0 0 0 0 0 29 InputDevice::AxisState::value 0

345 5 known 0 6 252 122 123 0 0 0 0 0 0 29 InputDevice::AxisState::known 0

346 5 level 0 6 256 126 127 0 0 0 0 0 0 31 InputDevice::BatteryData::level 35
// Ranges from 0 through max_level.

347 9 max_level 0 6 256 128 129 0 0 0 0 0 0 35 InputDevice::BatteryData::max_level 34
// Maximum value of 'level' field.

348 4 name 0 2 257 132 0 0 0 0 0 0 0 17 InputDevice::name 48
// The human-readable name of this input device.

349 12 manufacturer 0 2 257 133 0 0 0 0 0 0 0 25 InputDevice::manufacturer 63
// The device's manufacturer, or the empty string if not known.

350 13 serial_number 0 2 257 134 0 0 0 0 0 0 0 26 InputDevice::serial_number 64
// The device's serial number, or the empty string if not known.

351 9 vendor_id 0 2 259 135 0 0 0 0 0 0 0 22 InputDevice::vendor_id 50
// USB vendor ID of the device, or 0 if not known.

352 10 product_id 0 2 259 136 0 0 0 0 0 0 0 23 InputDevice::product_id 51
// USB product ID of the device, or 0 if not known.

353 9 connected 0 2 252 137 0 0 0 0 0 0 0 22 InputDevice::connected 139
// This is false if we know that the device is not currently connected.
// May report false positives if we can't know this with certainty.

354 12 device_class 0 2 247 138 0 0 0 0 0 0 0 25 InputDevice::device_class 127
// This contains an identification of the general type of device.  If
// this could not be determined, it is set to DC_unknown.

355 7 tracker 0 10 237 140 0 141 0 0 0 0 0 20 InputDevice::tracker 48
// Getters for the various types of device data.

356 7 battery 0 10 255 142 0 143 0 0 0 0 0 20 InputDevice::battery 0

357 7 buttons 0 66 251 145 0 0 0 0 144 0 0 20 InputDevice::buttons 40
// Make device buttons and axes iterable

358 4 axes 0 66 254 147 0 0 0 0 146 0 0 17 InputDevice::axes 0

359 8 _buttons 0 2 260 161 0 0 0 0 0 0 0 21 InputDevice::_buttons 0

360 5 _axes 0 2 262 162 0 0 0 0 0 0 0 18 InputDevice::_axes 0

361 13 _pointer_data 0 2 264 163 0 0 0 0 0 0 0 26 InputDevice::_pointer_data 0

362 13 _battery_data 0 2 255 164 0 0 0 0 0 0 0 26 InputDevice::_battery_data 0

363 13 _tracker_data 0 2 237 165 0 0 0 0 0 0 0 26 InputDevice::_tracker_data 0

364 6 device 0 6 273 213 214 0 0 0 0 0 0 23 InputDeviceNode::device 0

0
