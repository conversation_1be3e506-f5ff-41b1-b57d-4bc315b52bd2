'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.GLX import _types as _cs
# End users want this...
from OpenGL.raw.GLX._types import *
from OpenGL.raw.GLX import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'GLX_NV_multigpu_context'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.GLX,'GLX_NV_multigpu_context',error_checker=_errors._error_checker)
GLX_CONTEXT_MULTIGPU_ATTRIB_AFR_NV=_C('GLX_CONTEXT_MULTIGPU_ATTRIB_AFR_NV',0x20AC)
GLX_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV=_C('GLX_CONTEXT_MULTIGPU_ATTRIB_MULTICAST_NV',0x20AD)
GLX_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV=_C('GLX_CONTEXT_MULTIGPU_ATTRIB_MULTI_DISPLAY_MULTICAST_NV',0x20AE)
GLX_CONTEXT_MULTIGPU_ATTRIB_NV=_C('GLX_CONTEXT_MULTIGPU_ATTRIB_NV',0x20AA)
GLX_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV=_C('GLX_CONTEXT_MULTIGPU_ATTRIB_SINGLE_NV',0x20AB)

