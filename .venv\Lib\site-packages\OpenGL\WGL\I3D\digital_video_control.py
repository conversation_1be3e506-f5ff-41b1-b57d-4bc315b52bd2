'''OpenGL extension I3D.digital_video_control

This module customises the behaviour of the 
OpenGL.raw.WGL.I3D.digital_video_control to provide a more 
Python-friendly API

The official definition of this extension is available here:
http://www.opengl.org/registry/specs/I3D/digital_video_control.txt
'''
from OpenGL import platform, constant, arrays
from OpenGL import extensions, wrapper
import ctypes
from OpenGL.raw.WGL import _types, _glgets
from OpenGL.raw.WGL.I3D.digital_video_control import *
from OpenGL.raw.WGL.I3D.digital_video_control import _EXTENSION_NAME

def glInitDigitalVideoControlI3D():
    '''Return boolean indicating whether this extension is available'''
    from OpenGL import extensions
    return extensions.hasGLExtension( _EXTENSION_NAME )


### END AUTOGENERATED SECTION