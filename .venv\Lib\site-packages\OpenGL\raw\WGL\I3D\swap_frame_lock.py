'''Autogenerated by xml_generate script, do not edit!'''
from OpenGL import platform as _p, arrays
# Code generation uses this
from OpenGL.raw.WGL import _types as _cs
# End users want this...
from OpenGL.raw.WGL._types import *
from OpenGL.raw.WGL import _errors
from OpenGL.constant import Constant as _C

import ctypes
_EXTENSION_NAME = 'WGL_I3D_swap_frame_lock'
def _f( function ):
    return _p.createFunction( function,_p.PLATFORM.WGL,'WGL_I3D_swap_frame_lock',error_checker=_errors._error_checker)

@_f
@_p.types(_cs.BOOL,)
def wglDisableFrameLockI3D():pass
@_f
@_p.types(_cs.BOOL,)
def wglEnableFrameLockI3D():pass
@_f
@_p.types(_cs.BOOL,ctypes.POINTER(_cs.BOOL))
def wglIsEnabledFrameLockI3D(pFlag):pass
@_f
@_p.types(_cs.BOOL,ctypes.POINTER(_cs.BOOL))
def wglQueryFrameLockMasterI3D(pFlag):pass
